# 开发环境
APP_ENV=development

# 数据库配置
DATABASE_URL=sqlite:///./quantization.db
DATABASE_TYPE=sqlite
REDIS_URL=redis://localhost:6379/0

# 自定义API配置
DATA_API_TYPE=mairui
MAIRUI_TOKEN=6241685402a0d9118b

# FastAPI配置
API_HOST=0.0.0.0
API_PORT=8000
API_PREFIX=/api
DEBUG=false

# 任务调度配置
# 每个工作日18:00更新数据
DATA_UPDATE_CRON=0 18 * * 1-5
# 每周日12:00更新股票列表
STOCK_LIST_UPDATE_CRON=0 12 * * 0

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=logs

CACHE_WARMUP_ENABLED=false
