# 缓存系统使用指南

## 概述

本项目实现了一个灵活的缓存系统，支持内存缓存和Redis缓存两种模式，可以根据配置自动切换。

## 配置

### Redis缓存配置

在 `.env` 文件中设置：

```bash
# Redis连接URL
REDIS_URL=redis://localhost:6379/0

# 是否启用缓存
CACHE_ENABLED=true

# 默认缓存过期时间（秒）
CACHE_EXPIRATION=3600
```

如果未配置 `REDIS_URL`，系统会自动降级使用内存缓存。

## 使用方法

### 1. 使用缓存装饰器（推荐）

#### 预定义装饰器

```python
from app.core.cache_decorator import (
    indicator_cache,  # 指标缓存（1小时）
    stock_cache,      # 股票数据缓存（30分钟）
    kline_cache,      # K线数据缓存（30分钟）
    short_cache,      # 短期缓存（5分钟）
    long_cache        # 长期缓存（24小时）
)

class StockService:
    @indicator_cache()
    async def calculate_macd(self, stock_code: str, start_date: str):
        # 复杂的MACD计算
        return result
    
    @short_cache()
    async def get_realtime_price(self, stock_code: str):
        # 获取实时价格
        return price
```

#### 自定义缓存配置

```python
from app.core.cache_decorator import cached

@cached(expire=7200, namespace="custom")
async def custom_function(param1: str, param2: int):
    # 自定义2小时缓存
    return result

# 使用自定义缓存键生成器
@cached(
    expire=1800,
    key_builder=lambda func_name, args, kwargs: f"my_key:{args[1]}:{kwargs.get('type', 'default')}"
)
async def complex_function(self, id: str, type: str = "default"):
    return result
```

### 2. 手动缓存操作

```python
from app.core.cache import cache_result, get_cached_result, clear_cache_by_prefix

# 设置缓存
await cache_result("my_key", {"data": "value"}, expire=1800)

# 获取缓存
cached_data = await get_cached_result("my_key")

# 删除特定前缀的缓存
deleted_count = await clear_cache_by_prefix("indicator:")
```

### 3. 直接使用缓存客户端

```python
from app.core.cache import cache_client

# 基本操作
await cache_client.set("key", "value", expire=3600)
value = await cache_client.get("key")
await cache_client.delete("key")

# 批量删除
deleted = await cache_client.clear_by_prefix("prefix:")
```

## 缓存管理API

系统提供了缓存管理的REST API端点：

### 获取缓存统计信息
```
GET /api/v1/cache/stats
```

### 清空所有缓存
```
DELETE /api/v1/cache/clear
```

### 根据前缀清除缓存
```
DELETE /api/v1/cache/prefix/{prefix}
```

### 检查缓存健康状态
```
GET /api/v1/cache/health
```

## 最佳实践

### 1. 缓存键命名规范

使用冒号分隔的层级结构：
```
namespace:function_name:param1:param2
```

例如：
```
indicator:macd:600000:2024-01-01:2024-01-31
stock:price:600000
kline:daily:600000:2024-01
```

### 2. 缓存时间选择

- **实时数据**：5-10分钟
- **技术指标**：30分钟-1小时
- **历史数据**：1-24小时
- **基本面数据**：24小时或更长

### 3. 缓存失效策略

- 使用 `clear_cache_by_prefix` 批量清除相关缓存
- 在数据更新后主动清除相关缓存
- 设置合理的过期时间，避免数据过期

### 4. 性能优化

- 对于频繁访问的数据使用缓存装饰器
- 批量操作时考虑批量缓存
- 避免缓存过大的对象（Redis有序列化开销）

## 注意事项

1. **内存缓存限制**：
   - 仅在单进程内有效
   - 应用重启后缓存丢失
   - 适合开发环境

2. **Redis缓存优势**：
   - 支持分布式部署
   - 数据持久化
   - 更好的内存管理
   - 适合生产环境

3. **序列化支持**：
   - 简单类型使用JSON序列化（字符串、数字、列表、字典）
   - 复杂对象使用pickle序列化（DataFrame、自定义类等）

4. **错误处理**：
   - 缓存操作失败不会影响业务逻辑
   - 自动降级到无缓存模式
   - 所有错误都会记录日志

## 示例代码

完整示例请参考 `app/services/cache_example.py`

## 监控和调试

1. 查看缓存统计：访问 `/api/v1/cache/stats`
2. 检查日志：缓存操作会记录详细日志
3. 使用Redis CLI监控：`redis-cli monitor`
4. 性能分析：使用装饰器自动记录缓存命中率