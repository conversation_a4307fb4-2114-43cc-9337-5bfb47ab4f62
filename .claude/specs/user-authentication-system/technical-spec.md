# 用户认证系统技术规范 (JWT版本)

## 问题陈述
- **业务问题**: 当前系统缺乏完整的用户认证和权限管理机制，所有功能都使用临时的sessionId，无法实现按用户管理的自选股功能和用户权限控制
- **当前状态**: 系统使用简单的sessionId进行扫描功能的临时会话管理，缺乏真正的用户身份认证和持久化会话管理
- **预期结果**: 实现完整的JWT认证系统，支持管理员和普通用户角色，24小时token过期和续期机制，所有功能都需要登录，自选股按用户存储

## 解决方案概述
- **技术方案**: 使用JWT (JSON Web Token) 作为认证机制，替换现有的sessionId系统
- **核心变更**: 
  1. 后端实现JWT token生成、验证和续期
  2. 前端实现token存储、自动续期和登录状态管理
  3. 数据库添加用户角色字段，自选股关联用户ID
  4. 移除扫描sessionId，统一使用用户ID
- **成功标准**: 所有API都需要JWT认证，用户可以多设备登录，token支持24小时过期和自动续期

## 技术实现

### 数据库变更

#### 用户表修改
```sql
-- 已存在的用户表，需要添加token相关字段
ALTER TABLE users ADD COLUMN token_version INTEGER DEFAULT 0 COMMENT 'JWT token版本号，用于强制过期';
```

#### 新增JWT配置表
```sql
-- JWT配置表，用于存储token黑名单和版本管理
CREATE TABLE jwt_tokens (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    token_jti VARCHAR(36) NOT NULL COMMENT 'JWT ID，用于标识token',
    token_type VARCHAR(20) DEFAULT 'access' COMMENT 'token类型：access/refresh',
    expires_at DATETIME NOT NULL COMMENT 'token过期时间',
    is_revoked BOOLEAN DEFAULT FALSE COMMENT '是否已撤销',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_token_jti (token_jti),
    FOREIGN KEY (user_id) REFERENCES users (id)
);
```

### 代码变更

#### 后端文件修改

**新文件: /home/<USER>/workspace/quantization/app/utils/jwt_auth.py**
```python
"""JWT认证工具模块"""
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import jwt
import uuid
from sqlalchemy.orm import Session

from app.core.config import settings
from app.models.user import User

# JWT配置
JWT_SECRET_KEY = "your-secret-key-change-in-production"
JWT_ALGORITHM = "HS256"
JWT_EXPIRE_HOURS = 24

def generate_access_token(user_id: int, username: str, is_admin: bool) -> str:
    """生成访问token"""
    now = datetime.utcnow()
    payload = {
        'user_id': user_id,
        'username': username, 
        'is_admin': is_admin,
        'exp': now + timedelta(hours=JWT_EXPIRE_HOURS),
        'iat': now,
        'jti': str(uuid.uuid4())  # JWT ID用于标识唯一token
    }
    return jwt.encode(payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)

def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """验证并解析token"""
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.JWTError:
        return None

def refresh_token(old_token: str, db: Session) -> Optional[str]:
    """刷新token（24小时内可续期）"""
    payload = verify_token(old_token)
    if not payload:
        return None
        
    user = db.query(User).filter(User.id == payload['user_id']).first()
    if not user or not user.is_active:
        return None
        
    # 生成新token
    return generate_access_token(user.id, user.username, user.is_admin)
```

**修改文件: /home/<USER>/workspace/quantization/app/services/user_service.py**
```python
# 在现有文件基础上添加JWT相关方法
from app.utils.jwt_auth import generate_access_token, verify_token
from sqlalchemy.sql import func

class UserService:
    # ... 保留现有方法 ...
    
    @staticmethod
    def login_user(db: Session, username: str, password: str) -> Optional[Dict[str, Any]]:
        """用户登录，返回token和用户信息"""
        user = UserService.authenticate_user(db, username, password)
        if not user:
            return None
            
        # 更新最后登录时间
        user.last_login = func.now()
        db.commit()
        
        # 生成JWT token
        access_token = generate_access_token(user.id, user.username, user.is_admin)
        
        return {
            'access_token': access_token,
            'token_type': 'bearer',
            'expires_in': 24 * 3600,  # 24小时
            'user': {
                'id': user.id,
                'username': user.username,
                'is_admin': user.is_admin,
                'email': user.email
            }
        }
    
    @staticmethod
    def get_current_user_from_token(token: str, db: Session) -> Optional[User]:
        """从token获取当前用户"""
        payload = verify_token(token)
        if not payload:
            return None
            
        user = db.query(User).filter(User.id == payload['user_id']).first()
        if not user or not user.is_active:
            return None
            
        return user
```

**新文件: /home/<USER>/workspace/quantization/app/utils/dependencies.py**
```python
"""FastAPI依赖项"""
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from app.core.sync_database import get_db
from app.services.user_service import UserService
from app.models.user import User

security = HTTPBearer()

def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """获取当前登录用户"""
    user = UserService.get_current_user_from_token(credentials.credentials, db)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token无效或已过期",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return user

def get_current_admin_user(current_user: User = Depends(get_current_user)) -> User:
    """获取当前管理员用户"""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足，需要管理员权限"
        )
    return current_user
```

**新文件: /home/<USER>/workspace/quantization/app/api/endpoints/auth.py**
```python
"""认证相关API端点"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.sync_database import get_db
from app.schemas.auth import LoginRequest, LoginResponse, TokenRefreshRequest
from app.services.user_service import UserService
from app.utils.jwt_auth import refresh_token
from app.utils.dependencies import get_current_user
from app.models.user import User

router = APIRouter(prefix="/auth", tags=["认证"])

@router.post("/login", response_model=LoginResponse)
def login(login_data: LoginRequest, db: Session = Depends(get_db)):
    """用户登录"""
    result = UserService.login_user(db, login_data.username, login_data.password)
    if not result:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )
    return result

@router.post("/refresh", response_model=dict)
def refresh_access_token(
    request: TokenRefreshRequest, 
    db: Session = Depends(get_db)
):
    """刷新访问token"""
    new_token = refresh_token(request.token, db)
    if not new_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token刷新失败，请重新登录"
        )
    return {
        'access_token': new_token,
        'token_type': 'bearer',
        'expires_in': 24 * 3600
    }

@router.get("/me", response_model=dict)
def get_profile(current_user: User = Depends(get_current_user)):
    """获取当前用户信息"""
    return {
        'id': current_user.id,
        'username': current_user.username,
        'email': current_user.email,
        'is_admin': current_user.is_admin,
        'last_login': current_user.last_login
    }

@router.post("/logout")
def logout(current_user: User = Depends(get_current_user)):
    """用户登出（可以在这里实现token撤销逻辑）"""
    return {'message': '退出登录成功'}
```

**新文件: /home/<USER>/workspace/quantization/app/schemas/auth.py**
```python
"""认证相关的模式定义"""
from pydantic import BaseModel, Field
from typing import Dict, Any

class LoginRequest(BaseModel):
    """登录请求"""
    username: str = Field(..., min_length=3, max_length=50)
    password: str = Field(..., min_length=6)
    remember_me: bool = False

class LoginResponse(BaseModel):
    """登录响应"""
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: Dict[str, Any]

class TokenRefreshRequest(BaseModel):
    """Token刷新请求"""
    token: str
```

**修改文件: /home/<USER>/workspace/quantization/app/api/endpoints/users.py**
```python
# 更新现有用户API，添加权限控制
from app.utils.dependencies import get_current_admin_user, get_current_user

@router.post("/", response_model=dict, status_code=status.HTTP_201_CREATED)
def create_user(
    user: UserCreate, 
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)  # 只有管理员可创建用户
):
    """创建新用户（仅管理员）"""
    # ... 保留原有逻辑 ...

@router.get("/list", response_model=List[UserResponse])
def list_users(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin_user)  # 只有管理员可查看用户列表
):
    """获取用户列表（仅管理员）"""
    users = db.query(User).all()
    return users
```

#### 前端文件修改

**新文件: /home/<USER>/workspace/quantization/frontend-app/src/stores/auth.js**
```javascript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import apiClient from '@/services/apiClient'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref(null)
  const token = ref(localStorage.getItem('access_token'))
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.is_admin || false)

  // 自动刷新token的定时器
  let refreshTimer = null

  // 登录
  async function login(credentials) {
    isLoading.value = true
    try {
      const response = await apiClient.post('/v1/auth/login', credentials)
      
      token.value = response.access_token
      user.value = response.user
      
      // 保存到localStorage
      localStorage.setItem('access_token', response.access_token)
      if (credentials.remember_me) {
        localStorage.setItem('remember_user', 'true')
      }
      
      // 启动token刷新定时器（在过期前1小时刷新）
      startTokenRefresh(response.expires_in)
      
      return { success: true }
    } catch (error) {
      return { 
        success: false, 
        message: error.message || '登录失败' 
      }
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  async function logout() {
    try {
      await apiClient.post('/v1/auth/logout')
    } catch (error) {
      console.error('登出API调用失败:', error)
    }
    
    // 清理本地状态
    token.value = null
    user.value = null
    localStorage.removeItem('access_token')
    localStorage.removeItem('remember_user')
    
    // 清理定时器
    if (refreshTimer) {
      clearTimeout(refreshTimer)
      refreshTimer = null
    }
  }

  // 刷新token
  async function refreshToken() {
    if (!token.value) return false
    
    try {
      const response = await apiClient.post('/v1/auth/refresh', {
        token: token.value
      })
      
      token.value = response.access_token
      localStorage.setItem('access_token', response.access_token)
      
      // 重新启动定时器
      startTokenRefresh(response.expires_in)
      
      return true
    } catch (error) {
      console.error('Token刷新失败:', error)
      await logout() // 刷新失败则登出
      return false
    }
  }

  // 启动token刷新定时器
  function startTokenRefresh(expiresIn) {
    if (refreshTimer) {
      clearTimeout(refreshTimer)
    }
    
    // 在过期前1小时刷新token
    const refreshTime = (expiresIn - 3600) * 1000
    if (refreshTime > 0) {
      refreshTimer = setTimeout(() => {
        refreshToken()
      }, refreshTime)
    }
  }

  // 获取用户信息
  async function fetchUser() {
    if (!token.value) return
    
    try {
      const userData = await apiClient.get('/v1/auth/me')
      user.value = userData
    } catch (error) {
      console.error('获取用户信息失败:', error)
      await logout()
    }
  }

  // 初始化
  async function initialize() {
    if (token.value) {
      await fetchUser()
      // 如果有记住用户的设置，启动token刷新
      if (localStorage.getItem('remember_user')) {
        startTokenRefresh(24 * 3600) // 默认24小时
      }
    }
  }

  return {
    user,
    token,
    isLoading,
    isAuthenticated,
    isAdmin,
    login,
    logout,
    refreshToken,
    fetchUser,
    initialize
  }
})
```

**新文件: /home/<USER>/workspace/quantization/frontend-app/src/views/Login.vue**
```vue
<template>
  <div class="login-container">
    <div class="login-form">
      <h2>用户登录</h2>
      <el-form 
        :model="loginForm" 
        :rules="rules" 
        @submit.prevent="handleLogin"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input 
            v-model="loginForm.username" 
            placeholder="请输入用户名"
            :disabled="loading"
          />
        </el-form-item>
        
        <el-form-item label="密码" prop="password">
          <el-input 
            v-model="loginForm.password" 
            type="password" 
            placeholder="请输入密码"
            :disabled="loading"
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <el-form-item>
          <el-checkbox v-model="loginForm.remember_me">
            记住我
          </el-checkbox>
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            @click="handleLogin" 
            :loading="loading"
            style="width: 100%"
          >
            登录
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const loading = ref(false)
const loginForm = reactive({
  username: '',
  password: '',
  remember_me: false
})

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码至少6位', trigger: 'blur' },
    { 
      pattern: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]/, 
      message: '密码必须包含字母和数字', 
      trigger: 'blur' 
    }
  ]
}

const handleLogin = async () => {
  loading.value = true
  
  const result = await authStore.login(loginForm)
  
  if (result.success) {
    ElMessage.success('登录成功')
    router.push('/')
  } else {
    ElMessage.error(result.message)
  }
  
  loading.value = false
}
</script>

<style scoped>
.login-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f5f5f5;
}

.login-form {
  width: 400px;
  padding: 40px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}
</style>
```

**修改文件: /home/<USER>/workspace/quantization/frontend-app/src/services/apiClient.js**
```javascript
// 更新请求拦截器，使用JWT token
apiClient.interceptors.request.use(
  (config) => {
    // JWT token处理
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 移除原有的sessionId处理逻辑
    // 移除cookie中的session_id相关代码
    
    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 更新响应拦截器，处理JWT token过期
apiClient.interceptors.response.use(
  (response) => {
    // 保持原有的响应处理逻辑
    const res = response.data
    if (res && typeof res === 'object' && 
        'data' in res && 'status' in res && 'message' in res &&
        Object.keys(res).length === 3) {
      return res.data
    }
    return res
  },
  async (error) => {
    const { response, config } = error
    
    if (response?.status === 401) {
      // JWT token过期，尝试刷新
      if (!config._retry) {
        config._retry = true
        
        try {
          // 动态导入auth store并尝试刷新token
          const { useAuthStore } = await import('@/stores/auth')
          const authStore = useAuthStore()
          const success = await authStore.refreshToken()
          
          if (success) {
            // 重试原请求
            const newToken = localStorage.getItem('access_token')
            config.headers.Authorization = `Bearer ${newToken}`
            return apiClient.request(config)
          }
        } catch (refreshError) {
          console.error('Token刷新失败:', refreshError)
        }
      }
      
      // 刷新失败或其他401错误，跳转到登录页
      localStorage.removeItem('access_token')
      window.location.href = '/login'
    }
    
    return Promise.reject(error)
  }
)
```

### API变更

#### 新增认证端点
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/refresh` - 刷新token
- `GET /api/v1/auth/me` - 获取当前用户信息
- `POST /api/v1/auth/logout` - 用户登出

#### 更新现有端点
- 所有API端点添加JWT认证依赖
- 用户管理端点添加管理员权限验证
- 自选股端点自动关联当前用户ID

#### 权限矩阵
```
端点类型          | 普通用户 | 管理员 | 未登录
-----------------|---------|-------|--------
股票查询          | ✓       | ✓     | ✗
技术指标          | ✓       | ✓     | ✗
自选股管理        | ✓       | ✓     | ✗
扫描功能          | ✓       | ✓     | ✗
用户创建          | ✗       | ✓     | ✗
用户管理          | ✗       | ✓     | ✗
```

### 配置变更

#### 环境变量添加
```bash
# JWT配置
JWT_SECRET_KEY=your-super-secret-key-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRE_HOURS=24

# 认证配置
AUTH_REQUIRED=true
ALLOW_MULTI_DEVICE_LOGIN=true
```

#### 依赖包添加
```txt
# 后端新增依赖
PyJWT==2.8.0
python-multipart==0.0.6

# 前端新增依赖
@vueuse/core
pinia
```

## 实现序列

### 第一阶段: JWT基础设施
1. **后端JWT工具** - 创建jwt_auth.py，实现token生成和验证
2. **数据库迁移** - 添加JWT相关字段和表
3. **认证API** - 实现登录、刷新、用户信息获取接口
4. **权限依赖** - 创建FastAPI认证依赖项

### 第二阶段: 前端认证系统
1. **认证Store** - 创建Pinia store管理JWT token
2. **登录页面** - 实现独立的登录界面
3. **API客户端** - 更新axios拦截器支持JWT
4. **路由守卫** - 添加全局路由认证检查

### 第三阶段: 现有功能升级
1. **API端点升级** - 所有API添加JWT认证依赖
2. **用户管理功能** - 实现管理员用户创建和管理
3. **自选股用户关联** - 更新自选股功能关联用户ID
4. **移除sessionId** - 清理原有扫描sessionId机制

## 验证计划

### 单元测试
- JWT token生成和验证功能测试
- 用户认证服务测试
- 权限控制逻辑测试

### 集成测试
- 登录流程端到端测试
- Token刷新机制测试
- 多设备登录场景测试

### 业务逻辑验证
- 管理员用户创建功能验证
- 按用户存储自选股功能验证
- 全局登录要求功能验证

## 关键配置参数

### JWT配置
- **密钥**: JWT_SECRET_KEY (生产环境必须更改)
- **算法**: HS256
- **过期时间**: 24小时
- **自动刷新**: 过期前1小时自动刷新

### 安全配置
- **多设备登录**: 支持
- **密码策略**: 英文+数字，最少6位
- **Token撤销**: 支持通过token_version实现

### 性能配置
- **Token缓存**: 使用内存缓存提升验证性能
- **数据库连接**: 复用现有连接池
- **前端缓存**: localStorage存储token