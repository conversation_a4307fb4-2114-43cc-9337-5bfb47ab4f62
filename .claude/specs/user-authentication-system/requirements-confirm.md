# 用户认证系统需求确认

## 原始需求
"需要给项目增加用户系统与登陆功能以及登陆校验与过期功能（现在已经有sessionId的使用，可以基于这个扩展）"

## 澄清问答记录

### 第1轮澄清 (时间: 2025-08-06)

**Q1: 用户注册方式**
A: 现在不需要用户注册，只需要预先创建用户

**Q2: 认证技术选择**  
A: 不需要jwt，对用户名密码进行简单base64加密之后由后端进行校验即可，过期时间24小时，需要token续期

**Q3: 用户角色定义**
A: 需要有角色，现在就是管理员与普通用户，管理员能够管理用户、创建用户

**Q4: 现有功能整合**
A: 所有功能都需要登陆，自选股肯定要按照用户存储，没有匿名用户

**Q5: 密码安全策略**
A: 密码要求英文加数字

**Q6: 会话管理**
A: 允许多设备登陆，把现有的扫描sessionId去了整体升级为用户id，本来扫描的sessionId就是在没有用户sessionId的降级处理

**Q7: 用户界面**
A: 用户界面是独立的界面

**Q8: 用户体验**
A: 需要记住用户，不需要三方登陆

## 最终确认的需求规格

### 功能需求

#### 1. 用户管理系统
- **用户创建方式**: 仅支持管理员预先创建用户，无用户自注册功能
- **用户角色**: 
  - 管理员(admin): 可管理用户、创建用户、访问所有功能
  - 普通用户(user): 只能访问基本功能，不能管理其他用户
- **用户信息**: 用户名、密码、角色、创建时间、最后登录时间

#### 2. 认证机制
- **认证方式**: Base64编码用户名密码，后端验证
- **Token机制**: 自定义token，非JWT
- **过期时间**: 24小时
- **续期机制**: 支持token自动续期
- **记住用户**: 支持"记住我"功能，延长登录状态

#### 3. 密码策略
- **密码要求**: 必须包含英文字母和数字
- **长度要求**: 最少6位（推断）
- **存储方式**: 后端hash存储，前端Base64传输

#### 4. 会话管理
- **多设备登录**: 允许同一用户在多个设备同时登录
- **Session升级**: 移除现有扫描sessionId，统一使用用户认证session
- **会话标识**: 使用用户ID作为会话关联

#### 5. 功能权限
- **全局登录要求**: 所有功能都需要登录才能访问
- **自选股**: 按用户维度存储和管理
- **扫描功能**: 与用户ID关联，不再使用独立sessionId
- **无匿名访问**: 完全移除匿名用户支持

#### 6. 前端界面
- **登录页面**: 独立的登录界面
- **用户管理**: 管理员用户创建界面
- **导航栏**: 显示当前用户信息和登出功能
- **无注册页面**: 不需要用户注册界面

### 技术需求

#### 后端实现
- 更新用户模型，添加角色字段
- 实现Base64认证逻辑
- 创建用户管理API（仅管理员可用）
- 更新现有API，添加用户认证依赖
- 移除扫描sessionId机制，改用用户ID
- 实现token生成、验证、续期逻辑

#### 前端实现
- 创建登录页面
- 创建用户管理页面（管理员专用）
- 更新导航栏，添加用户信息显示
- 实现记住用户功能
- 更新所有API调用，添加认证头
- 实现自动token续期机制

#### 数据库变更
- 用户表添加角色字段
- 自选股表关联用户ID
- 移除或更新扫描相关表的sessionId字段

## 质量评分（重新评估）

1. **功能清晰度 (30分)**: 28/30分 ✅
   - ✅ 明确的认证方式和用户管理需求
   - ✅ 清晰的角色权限定义
   - ✅ 具体的功能集成要求

2. **技术特异性 (25分)**: 23/25分 ✅
   - ✅ 明确的Base64认证技术选择
   - ✅ 具体的会话管理升级方案
   - ✅ 前后端集成需求清晰

3. **实现完整性 (25分)**: 22/25分 ✅
   - ✅ 用户会话管理详细定义
   - ✅ 密码策略明确
   - ✅ Token续期机制确定

4. **业务背景 (20分)**: 18/20分 ✅
   - ✅ 用户角色和权限明确定义
   - ✅ 现有系统集成方案清晰
   - ✅ 安全性要求适中

**最终质量得分**: 91/100分 ✅ 

**状态**: 需求确认完成，质量评分≥90分，可以进入实现阶段