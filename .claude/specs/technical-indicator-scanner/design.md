# 技术指标扫描功能设计文档

## 概述

技术指标扫描功能是一个基于"成交量压力与动量双驱策略"的股票筛选系统。系统采用前后端分离架构，后端提供异步扫描任务管理和技术指标计算服务，前端提供用户友好的扫描控制和结果展示界面。

### 核心设计原则

1. **轻量级内存管理**：基于内存缓存管理扫描任务，避免数据库复杂度
2. **多用户隔离**：支持基于会话的用户任务隔离
3. **精确指标计算**：严格按照策略文档实现技术指标算法
4. **扩展友好**：预留未来功能扩展的接口
5. **用户体验优先**：提供实时反馈和直观操作界面

## 架构设计

### 整体架构

```mermaid
graph TB
    subgraph "Frontend (Vue.js)"
        UI[扫描控制界面]
        RL[结果列表]
        SM[会话管理器]
        API_CLIENT[API客户端]
    end
    
    subgraph "Backend (FastAPI)"
        subgraph "API Layer"
            SCAN_API[扫描API]
            INDICATOR_API[指标API]
        end
        
        subgraph "Service Layer"
            MSS[内存扫描服务]
            SIC[股票指标计算器]
            SSM[会话管理器]
        end
        
        subgraph "Storage Layer"
            STOCK_STORAGE[股票数据存储]
            MEMORY_CACHE[内存缓存]
        end
    end
    
    subgraph "External Data"
        DATA_PROVIDERS[数据提供商]
    end
    
    UI --> API_CLIENT
    RL --> API_CLIENT
    SM --> API_CLIENT
    API_CLIENT --> SCAN_API
    API_CLIENT --> INDICATOR_API
    
    SCAN_API --> MSS
    INDICATOR_API --> SIC
    MSS --> SIC
    MSS --> SSM
    SIC --> STOCK_STORAGE
    STOCK_STORAGE --> DATA_PROVIDERS
```

### 核心组件关系

- **前端组件**通过统一的API客户端与后端交互
- **扫描服务**管理异步任务生命周期和用户会话隔离
- **指标计算器**负责精确的技术指标计算
- **会话管理器**处理多用户场景的数据隔离

## 组件和接口设计

### 1. 后端组件设计

#### 1.1 内存扫描服务 (MemoryScannerService)

```python
class MemoryScannerService:
    """基于内存的扫描任务管理服务"""
    
    def __init__(self):
        self._tasks: Dict[str, ScanTask] = {}  # 任务存储
        self._user_tasks: Dict[str, str] = {}  # 用户会话映射
        self._scan_locks: Dict[str, asyncio.Lock] = {}  # 并发控制
        
    async def start_scan(self, criteria: dict, user_id: str, session_id: str) -> str
    async def stop_user_scan(self, user_id: str, session_id: str)
    async def get_user_task(self, user_id: str, session_id: str) -> Optional[dict]
    async def cleanup_expired_tasks(self)
```

**核心特性**：
- 支持多用户会话隔离
- 自动过期任务清理（2小时TTL）
- 并发安全的任务管理
- 优雅的任务停止机制

#### 1.2 股票指标计算器 (StockIndicatorCalculator)

```python
class StockIndicatorCalculator:
    """股票技术指标计算器"""
    
    async def calculate_all_indicators(self, stock_code: str, df: pd.DataFrame) -> Dict[str, Any]
    def calculate_volume_pressure_indicator(self, df: pd.DataFrame, lag: int = 6) -> pd.Series
    def calculate_volume_bollinger_middle(self, log_vol: pd.Series, window: int = 20) -> pd.Series
    def calculate_kdj_precise(self, df: pd.DataFrame) -> Dict[str, pd.Series]
    def calculate_price_bollinger_bands(self, df: pd.DataFrame) -> Dict[str, pd.Series]
    def identify_trading_signals(self, df: pd.DataFrame, indicators: Dict) -> Dict[str, List[int]]
```

**计算流程**：
1. 数据验证和预处理
2. 成交量压力指标计算（log_vol）
3. 成交量布林带中轨计算（average_vol）
4. KDJ指标精确计算
5. 价格布林带计算
6. 交易信号识别（买入/止盈/止损）

#### 1.3 会话管理器 (SessionManager)

```python
class SessionManager:
    """用户会话管理器"""
    
    @staticmethod
    def get_user_session_key(user_id: Optional[str], session_id: str) -> str
    @staticmethod 
    def extract_session_info(request: Request, authorization: Optional[str]) -> Tuple[str, str]
    @staticmethod
    def generate_session_id() -> str
```

### 2. API接口设计

#### 2.1 扫描控制API

```python
# POST /api/v1/scan/start
{
    "indicators": ["volume_pressure", "kdj_golden_cross"],
    "volume_threshold": 0.5,
    "kdj_enabled": true,
    "scan_window": 20
}

# Response
{
    "success": true,
    "data": {
        "task_id": "uuid-string",
        "message": "扫描已开始",
        "session_id": "session-uuid"
    }
}

# POST /api/v1/scan/stop
# Response  
{
    "success": true,
    "message": "扫描已停止"
}

# GET /api/v1/scan/status
# Response
{
    "success": true,
    "data": {
        "task_id": "uuid-string",
        "status": "running",
        "progress": 0.65,
        "total_stocks": 1000,
        "scanned_stocks": 650,
        "found_stocks": 15,
        "scan_criteria": {...},
        "created_at": "2025-01-22T10:00:00Z",
        "updated_at": "2025-01-22T10:05:30Z"
    }
}

# GET /api/v1/scan/results
# Response
{
    "success": true,
    "data": [
        {
            "stock_code": "600519",
            "stock_name": "贵州茅台",
            "signal_type": "buy",
            "signal_strength": 0.85,
            "indicators": {
                "log_vol": 1.2,
                "average_vol": 0.8,
                "kdj_k": 65,
                "kdj_d": 58
            },
            "discovered_at": "2025-01-22T10:03:15Z"
        }
    ]
}
```

#### 2.2 技术指标详情API

```python
# GET /api/v1/indicators/analysis/{stock_code}
# Response
{
    "success": true,
    "data": {
        "stock_code": "600519",
        "period": "20d",
        "indicators": {
            "volume_pressure": {
                "log_vol": [0.8, 1.0, 1.2, ...],
                "average_vol": [0.9, 0.9, 0.8, ...]
            },
            "kdj": {
                "K": [45, 52, 65, ...],
                "D": [40, 48, 58, ...],
                "J": [55, 60, 79, ...]
            },
            "price_bollinger": {
                "upper": [1800, 1820, 1850, ...],
                "middle": [1750, 1760, 1780, ...],
                "lower": [1700, 1700, 1710, ...]
            }
        },
        "signals": {
            "buy_signals": [15, 18],
            "take_profit_signals": [12],
            "stop_loss_signals": []
        },
        "dates": ["2025-01-01", "2025-01-02", ...]
    }
}
```

### 3. 前端组件设计

#### 3.1 扫描控制组件 (ScanControl.vue)

```vue
<template>
  <div class="scan-control">
    <!-- 指标配置区 -->
    <div class="config-section">
      <ConfigPanel v-model="scanConfig" />
    </div>
    
    <!-- 控制按钮区 -->
    <div class="control-section">
      <el-button @click="startScan" :loading="isScanning" type="primary">
        {{ isScanning ? '扫描中...' : '开始扫描' }}
      </el-button>
      <el-button @click="stopScan" :disabled="!isScanning">
        停止扫描
      </el-button>
    </div>
  </div>
</template>
```

#### 3.2 扫描结果组件 (ScanResults.vue)

```vue
<template>
  <div class="scan-results">
    <!-- 进度显示 -->
    <ProgressDisplay v-if="currentTask" :task="currentTask" />
    
    <!-- 结果筛选 -->
    <ResultFilter v-model="filters" />
    
    <!-- 结果列表 -->
    <ResultList :results="filteredResults" @view-detail="handleViewDetail" />
  </div>
</template>
```

#### 3.3 会话管理器 (SessionManager.js)

```javascript
export class SessionManager {
    constructor() {
        this.sessionId = this.getOrCreateSessionId()
        this.userId = this.getUserId()
    }
    
    getOrCreateSessionId() {
        let sessionId = localStorage.getItem('scan_session_id')
        if (!sessionId) {
            sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
            localStorage.setItem('scan_session_id', sessionId)
        }
        return sessionId
    }
    
    getHeaders() {
        const headers = {
            'X-Session-ID': this.sessionId
        }
        
        const token = localStorage.getItem('access_token')
        if (token) {
            headers['Authorization'] = `Bearer ${token}`
        }
        
        return headers
    }
}
```

## 数据模型设计

### 1. 扫描任务模型

```python
@dataclass
class ScanTask:
    task_id: str                    # 任务唯一标识
    user_id: Optional[str]          # 用户ID（支持匿名用户）
    session_id: str                 # 会话ID
    status: str                     # 任务状态：running/completed/stopped/failed
    progress: float                 # 进度：0.0-1.0
    total_stocks: int               # 总股票数
    scanned_stocks: int             # 已扫描股票数
    found_stocks: int               # 找到的符合条件股票数
    scan_criteria: dict             # 扫描条件
    results: List[dict]             # 扫描结果
    created_at: datetime            # 创建时间
    updated_at: datetime            # 更新时间
    expires_at: datetime            # 过期时间
```

### 2. 扫描结果模型

```python
@dataclass
class ScanResult:
    stock_code: str                 # 股票代码
    stock_name: str                 # 股票名称
    signal_type: str                # 信号类型：buy/take_profit/stop_loss
    signal_strength: float          # 信号强度：0.0-1.0
    indicators: Dict[str, Any]      # 相关指标值
    discovered_at: datetime         # 发现时间
```

### 3. 扫描配置模型

```python
@dataclass
class ScanConfig:
    indicators: List[str]           # 启用的指标：["volume_pressure", "kdj_golden_cross"]
    volume_threshold: float         # 成交量压力阈值
    kdj_enabled: bool               # 是否启用KDJ确认
    scan_window: int                # 扫描时间窗口（天数）
```

## 错误处理设计

### 1. 错误分类和处理策略

```python
class ScanErrorHandler:
    """扫描错误处理器"""
    
    ERROR_CODES = {
        'INSUFFICIENT_DATA': '数据不足，无法计算技术指标',
        'CALCULATION_ERROR': '指标计算失败',
        'TASK_NOT_FOUND': '扫描任务不存在',
        'SESSION_EXPIRED': '会话已过期',
        'CONCURRENT_SCAN': '已有扫描任务在运行',
        'NETWORK_ERROR': '数据获取失败'
    }
    
    @staticmethod
    def handle_calculation_error(error: Exception, stock_code: str) -> dict:
        return {
            'stock_code': stock_code,
            'error_code': 'CALCULATION_ERROR',
            'error_message': f'计算{stock_code}指标失败: {str(error)}'
        }
```

### 2. 前端错误处理

```javascript
export class ScanErrorHandler {
    static handleError(error, context) {
        const errorMap = {
            'INSUFFICIENT_DATA': '该股票数据不足，无法进行技术分析',
            'CALCULATION_ERROR': '技术指标计算出现错误，请稍后重试',
            'TASK_NOT_FOUND': '扫描任务已过期或不存在',
            'SESSION_EXPIRED': '会话已过期，请刷新页面',
            'CONCURRENT_SCAN': '已有扫描任务在运行，请先停止当前任务',
            'NETWORK_ERROR': '网络连接失败，请检查网络后重试'
        }
        
        const userMessage = errorMap[error.code] || error.message || '未知错误'
        
        // 显示用户友好的错误消息
        ElMessage.error(userMessage)
        
        // 记录详细错误信息用于调试
        console.error(`[${context}] Error:`, error)
    }
}
```

## 测试策略

### 1. 单元测试重点

**后端测试**：
- 技术指标计算精度测试
- 信号识别逻辑测试  
- 扫描任务生命周期测试
- 多用户会话隔离测试
- 错误处理测试

**前端测试**：
- 扫描控制组件交互测试
- 结果展示组件渲染测试
- API集成测试
- 会话管理测试

### 2. 集成测试

- 完整扫描流程测试
- 多用户并发扫描测试
- 长时间运行稳定性测试
- 异常情况恢复测试

### 3. 测试数据准备

```python
class TestDataGenerator:
    """测试数据生成器"""
    
    @staticmethod
    def generate_stock_data(days: int = 30) -> pd.DataFrame:
        """生成模拟股票数据用于测试"""
        
    @staticmethod
    def generate_signal_test_cases() -> List[dict]:
        """生成信号识别测试用例"""
        
    @staticmethod
    def generate_edge_cases() -> List[dict]:
        """生成边界条件测试用例"""
```

## 部署和运维考虑

### 1. 性能优化

- **并发处理**：使用asyncio实现高效的异步扫描
- **内存管理**：定期清理过期任务，避免内存泄漏
- **计算优化**：缓存中间计算结果，避免重复计算
- **前端优化**：使用防抖技术避免频繁API调用

### 2. 监控和日志

- **任务监控**：跟踪扫描任务的执行状态和性能
- **错误监控**：记录和报告计算错误和系统异常
- **用户行为**：跟踪用户的扫描模式和偏好
- **资源使用**：监控内存和CPU使用情况

### 3. 扩展性设计

**未来数据库迁移**：
```python
# 预留数据库接口
class TaskStorage(ABC):
    @abstractmethod
    async def save_task(self, task: ScanTask): pass
    
class MemoryTaskStorage(TaskStorage):
    # 当前内存实现
    pass
    
class DatabaseTaskStorage(TaskStorage):
    # 未来数据库实现
    pass
```

**多策略支持**：
```python
class ScanStrategy(ABC):
    @abstractmethod
    async def scan(self, criteria: dict) -> List[dict]: pass

class VPKMStrategy(ScanStrategy):
    # 当前成交量压力与动量双驱策略
    pass

class CustomStrategy(ScanStrategy):
    # 未来自定义策略支持
    pass
```

这个设计方案确保了系统的可靠性、可扩展性和用户友好性，同时保持了实现的简洁性。