# 技术指标扫描功能需求文档

## 功能概述

技术指标扫描功能是一个股票量化分析工具，用于实现《股票策略文档》中定义的"成交量压力与动量双驱策略"。该功能允许用户配置扫描条件，对股票池中的所有股票进行技术指标分析，识别符合买入、止盈、止损条件的股票，并提供结果展示和进一步分析功能。

核心特色：
- 基于策略文档的精确技术指标计算
- 后端异步扫描任务，不受前端刷新影响
- 支持多用户会话隔离
- 实时进度跟踪和结果展示
- 扩展友好的架构设计

## 功能需求

### 1. 扫描控制管理

**用户故事**: 作为量化分析师，我希望能够配置技术指标扫描条件并控制扫描进程，以便根据特定策略筛选股票。

**验收标准**:
1.1. 当用户访问技术指标页面时，系统应展示扫描控制界面
1.2. 当用户配置扫描条件时，系统应提供以下配置选项：
    - 成交量压力突破阈值设置（数值输入，默认0.5）
    - KDJ金叉确认开关（布尔选择，默认开启）
    - 扫描时间窗口设置（默认20天K线数据）
1.3. 当用户点击"开始扫描"时，系统应启动后端异步扫描任务
1.4. 当扫描任务运行时，用户应无法重新开始扫描，"开始扫描"按钮应显示为禁用状态
1.5. 当用户点击"停止扫描"时，系统应终止当前扫描任务并更新状态
1.6. 当没有扫描任务运行时，"停止扫描"按钮应显示为禁用状态

### 2. 扫描进度跟踪

**用户故事**: 作为量化分析师，我希望能够实时查看扫描进度和统计信息，以便了解扫描任务的执行状态。

**验收标准**:
2.1. 当扫描任务开始后，系统应显示进度条，显示扫描完成的百分比
2.2. 当扫描进行中时，系统应实时更新以下信息：
    - 已扫描股票数量 / 总股票数量
    - 当前扫描进度百分比
    - 已发现符合条件的股票数量
2.3. 当用户刷新页面时，系统应恢复之前的扫描状态和进度信息
2.4. 当扫描完成时，进度条应显示100%并标记为完成状态
2.5. 当扫描被用户手动停止时，进度条应显示当前进度并标记为停止状态

### 3. 扫描结果展示

**用户故事**: 作为量化分析师，我希望能够查看扫描出的符合条件的股票列表，以便进行进一步的分析和决策。

**验收标准**:
3.1. 当扫描找到符合条件的股票时，系统应在结果列表中实时显示该股票
3.2. 每个结果项应包含以下信息：
    - 股票名称和代码
    - 触发的信号类型（买入、止盈、止损）
    - 信号强度或置信度
3.3. 当结果列表为空时，系统应显示相应的空状态提示
3.4. 当用户点击"查看详情"时，系统应跳转到该股票的技术指标详情页面
3.5. 结果列表应支持基本的筛选功能（如按信号类型筛选）

### 4. 后端扫描任务管理

**用户故事**: 作为系统，我需要管理异步扫描任务，确保任务稳定执行且支持多用户场景。

**验收标准**:
4.1. 当接收到扫描请求时，系统应创建新的扫描任务并分配唯一任务ID
4.2. 系统应支持基于用户会话的任务隔离，不同用户的扫描任务互不干扰
4.3. 当扫描任务运行时，系统应能够处理任务停止请求并优雅终止
4.4. 系统应定期清理过期的扫描任务和结果数据（2小时后过期）
4.5. 当服务重启时，正在运行的扫描任务应被清理（基于内存存储的限制）
4.6. 系统应支持同一用户会话中的任务切换（停止旧任务，开始新任务）

### 5. 技术指标计算

**用户故事**: 作为系统，我需要精确计算技术指标，确保策略逻辑的正确执行。

**验收标准**:
5.1. 系统应实现以下核心指标计算：
    - 成交量压力指标（log_vol）：基于内外盘差值的EMA平滑处理
    - 成交量布林带中轨（average_vol）：成交量压力指标的移动平均
    - KDJ指标（K、D、J值）：标准KDJ计算算法
    - 价格布林带（上轨、中轨、下轨）：用于止损判断
5.2. 系统应实现以下交易信号识别：
    - 买入信号：成交量压力突破 AND KDJ金叉（双条件必须同时满足）
    - 止盈信号：成交量压力衰竭 OR KDJ死叉（任一条件满足即可）
    - 止损信号：收盘价跌破价格布林带下轨
5.3. 当输入数据不足时，系统应返回相应错误信息而不是错误的计算结果
5.4. 系统应对计算过程中的边界条件进行适当处理（如NaN值、除零等）
5.5. 计算结果应通过合理性验证（如KDJ值范围检查）

### 6. 多用户支持

**用户故事**: 作为系统，我需要支持多个用户同时使用扫描功能，确保用户间的数据隔离。

**验收标准**:
6.1. 系统应基于用户会话ID隔离不同用户的扫描任务
6.2. 匿名用户应基于浏览器会话进行任务隔离
6.3. 登录用户应基于用户ID进行任务隔离
6.4. 同一用户的多个浏览器标签页应基于会话ID进行独立管理
6.5. 用户切换或登出不应影响其他用户的扫描任务
6.6. 系统应为未来的用户权限管理和配额限制预留扩展接口

### 7. 路由和导航集成

**用户故事**: 作为用户，我希望能够通过系统导航访问技术指标扫描功能。

**验收标准**:
7.1. 技术指标页面应在系统主导航中可见和可访问
7.2. 页面路由应恢复为非隐藏状态（移除hidden: true配置）
7.3. 导航菜单应显示合适的图标和标题
7.4. 页面应支持直接URL访问和浏览器前进后退功能

### 8. 错误处理和用户反馈

**用户故事**: 作为用户，当系统出现错误时，我希望获得清晰的错误信息和合理的解决建议。

**验收标准**:
8.1. 当扫描任务失败时，系统应显示具体的错误原因
8.2. 当网络连接失败时，系统应显示网络错误提示并支持重试
8.3. 当数据不足以计算指标时，系统应显示相应提示信息
8.4. 系统应提供Loading状态指示，避免用户操作困惑
8.5. 所有用户可见的错误信息应使用中文显示

### 9. 性能和可扩展性

**用户故事**: 作为系统，我需要确保扫描功能的性能和未来扩展能力。

**验收标准**:
9.1. 系统应支持对数百只股票的并发扫描而不影响响应性
9.2. 扫描结果应支持增量加载，避免一次性加载大量数据
9.3. 指标计算应进行合理的缓存以避免重复计算
9.4. 前端应实现适当的防抖机制避免频繁的API请求
9.5. 系统架构应为未来数据库持久化存储预留扩展点
9.6. 系统架构应为未来多种扫描策略预留扩展接口