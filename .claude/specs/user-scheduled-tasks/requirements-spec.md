# 用户定时任务功能技术规格

## 问题陈述
- **业务问题**: 用户需要通过前端界面配置定时技术指标扫描任务，并查看任务执行历史和结果
- **当前状态**: 系统已有MemoryScanner组件和手动扫描功能，但缺乏用户自定义定时任务管理
- **预期结果**: 用户可以创建、编辑、删除定时扫描任务，设置cron表达式，查看任务执行记录，统一管理定时任务和手动扫描的历史记录

## 解决方案概述
- **方法**: 基于现有MemoryScanner组件，构建抽象任务类型系统和统一执行记录机制
- **核心变更**: 新增UserScheduledTask和TaskExecution数据模型，扩展API接口，新增前端定时任务管理页面
- **成功标准**: 用户可创建定时任务，查看cron可视化界面，监控任务执行状态，管理任务历史记录

## 技术实施

### 数据库变更

#### 新增表

##### UserScheduledTask (用户定时任务)
```sql
CREATE TABLE user_scheduled_tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_id INTEGER NOT NULL,
    name VARCHAR(100) NOT NULL COMMENT '任务名称',
    task_type VARCHAR(50) NOT NULL DEFAULT 'indicator_scan' COMMENT '任务类型',
    cron_expression VARCHAR(100) NOT NULL COMMENT 'Cron表达式',
    is_active BOOLEAN NOT NULL DEFAULT true COMMENT '是否启用',
    max_executions INTEGER NULL COMMENT '最大执行次数，NULL表示无限制',
    current_executions INTEGER NOT NULL DEFAULT 0 COMMENT '当前已执行次数',
    task_config TEXT NOT NULL COMMENT '任务配置JSON',
    description VARCHAR(500) NULL COMMENT '任务描述',
    last_execution DATETIME NULL COMMENT '最后执行时间',
    next_execution DATETIME NULL COMMENT '下次执行时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_scheduled_tasks_user_id (user_id),
    INDEX idx_user_scheduled_tasks_next_execution (next_execution),
    INDEX idx_user_scheduled_tasks_active (is_active)
);
```

##### TaskExecution (任务执行记录)
```sql
CREATE TABLE task_executions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_id INTEGER NOT NULL,
    scheduled_task_id INTEGER NULL COMMENT '定时任务ID，NULL表示手动触发',
    trigger_type VARCHAR(20) NOT NULL COMMENT '触发类型：scheduled/manual',
    task_type VARCHAR(50) NOT NULL DEFAULT 'indicator_scan' COMMENT '任务类型',
    task_config TEXT NOT NULL COMMENT '任务配置JSON',
    status VARCHAR(20) NOT NULL DEFAULT 'pending' COMMENT '执行状态',
    start_time DATETIME NULL COMMENT '开始时间',
    end_time DATETIME NULL COMMENT '结束时间',
    duration_seconds INTEGER NULL COMMENT '执行时长(秒)',
    results_count INTEGER NOT NULL DEFAULT 0 COMMENT '结果数量',
    error_message TEXT NULL COMMENT '错误信息',
    results_data LONGTEXT NULL COMMENT '执行结果JSON',
    metadata TEXT NULL COMMENT '扩展元数据JSON',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (scheduled_task_id) REFERENCES user_scheduled_tasks(id) ON DELETE CASCADE,
    INDEX idx_task_executions_user_id (user_id),
    INDEX idx_task_executions_scheduled_task_id (scheduled_task_id),
    INDEX idx_task_executions_trigger_type (trigger_type),
    INDEX idx_task_executions_status (status),
    INDEX idx_task_executions_start_time (start_time)
);
```

#### 迁移脚本
```sql
-- Migration: Add user scheduled tasks
-- File: migrations/versions/XXXX_add_user_scheduled_tasks.py
-- 使用alembic命令生成: alembic revision --autogenerate -m "Add user scheduled tasks tables"
```

### 代码变更

#### 新增文件

##### app/models/task.py
```python
"""定时任务相关数据模型"""

from sqlalchemy import Column, String, Integer, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship
from app.models.base import BaseModel

class UserScheduledTask(BaseModel):
    """用户定时任务模型"""
    __tablename__ = "user_scheduled_tasks"
    
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    name = Column(String(100), nullable=False, comment="任务名称")
    task_type = Column(String(50), nullable=False, default="indicator_scan", comment="任务类型")
    cron_expression = Column(String(100), nullable=False, comment="Cron表达式")
    is_active = Column(Boolean, nullable=False, default=True, comment="是否启用", index=True)
    max_executions = Column(Integer, nullable=True, comment="最大执行次数")
    current_executions = Column(Integer, nullable=False, default=0, comment="当前已执行次数")
    task_config = Column(Text, nullable=False, comment="任务配置JSON")
    description = Column(String(500), nullable=True, comment="任务描述")
    last_execution = Column(DateTime, nullable=True, comment="最后执行时间")
    next_execution = Column(DateTime, nullable=True, comment="下次执行时间", index=True)
    
    # 关联关系
    user = relationship("User", back_populates="scheduled_tasks")
    executions = relationship("TaskExecution", back_populates="scheduled_task", cascade="all, delete-orphan")

class TaskExecution(BaseModel):
    """任务执行记录模型"""
    __tablename__ = "task_executions"
    
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    scheduled_task_id = Column(Integer, ForeignKey("user_scheduled_tasks.id"), nullable=True, index=True)
    trigger_type = Column(String(20), nullable=False, comment="触发类型", index=True)  # scheduled/manual
    task_type = Column(String(50), nullable=False, default="indicator_scan", comment="任务类型")
    task_config = Column(Text, nullable=False, comment="任务配置JSON")
    status = Column(String(20), nullable=False, default="pending", comment="执行状态", index=True)
    start_time = Column(DateTime, nullable=True, comment="开始时间", index=True)
    end_time = Column(DateTime, nullable=True, comment="结束时间")
    duration_seconds = Column(Integer, nullable=True, comment="执行时长")
    results_count = Column(Integer, nullable=False, default=0, comment="结果数量")
    error_message = Column(Text, nullable=True, comment="错误信息")
    results_data = Column(Text, nullable=True, comment="执行结果JSON")
    metadata = Column(Text, nullable=True, comment="扩展元数据JSON")
    
    # 关联关系
    user = relationship("User", back_populates="task_executions")
    scheduled_task = relationship("UserScheduledTask", back_populates="executions")
```

##### app/schemas/scheduled_task.py
```python
"""定时任务相关Schema"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class TaskType(str, Enum):
    """任务类型枚举"""
    INDICATOR_SCAN = "indicator_scan"
    AI_ANALYSIS = "ai_analysis"  # 预留

class TriggerType(str, Enum):
    """触发类型枚举"""
    SCHEDULED = "scheduled"
    MANUAL = "manual"

class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running" 
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class IndicatorScanConfig(BaseModel):
    """指标扫描任务配置"""
    indicators: List[str] = Field(..., description="扫描指标列表")
    stock_codes: Optional[List[str]] = Field(None, description="股票代码列表")
    parameters: Optional[Dict[str, Any]] = Field(None, description="指标参数")
    scan_mode: str = Field("traditional", description="扫描模式")
    periods: List[str] = Field(["d"], description="扫描周期")
    adjust: str = Field("n", description="复权方式")

class UserScheduledTaskCreate(BaseModel):
    """创建定时任务请求"""
    name: str = Field(..., max_length=100, description="任务名称")
    task_type: TaskType = Field(TaskType.INDICATOR_SCAN, description="任务类型")
    cron_expression: str = Field(..., max_length=100, description="Cron表达式")
    task_config: IndicatorScanConfig = Field(..., description="任务配置")
    description: Optional[str] = Field(None, max_length=500, description="任务描述")
    max_executions: Optional[int] = Field(None, description="最大执行次数")

class UserScheduledTaskUpdate(BaseModel):
    """更新定时任务请求"""
    name: Optional[str] = Field(None, max_length=100, description="任务名称")
    cron_expression: Optional[str] = Field(None, max_length=100, description="Cron表达式")
    task_config: Optional[IndicatorScanConfig] = Field(None, description="任务配置")
    description: Optional[str] = Field(None, max_length=500, description="任务描述")
    max_executions: Optional[int] = Field(None, description="最大执行次数")
    is_active: Optional[bool] = Field(None, description="是否启用")

class UserScheduledTaskResponse(BaseModel):
    """定时任务响应"""
    id: int
    name: str
    task_type: TaskType
    cron_expression: str
    is_active: bool
    max_executions: Optional[int]
    current_executions: int
    task_config: Dict[str, Any]
    description: Optional[str]
    last_execution: Optional[datetime]
    next_execution: Optional[datetime]
    created_at: datetime
    updated_at: datetime

class TaskExecutionResponse(BaseModel):
    """任务执行记录响应"""
    id: int
    scheduled_task_id: Optional[int]
    trigger_type: TriggerType
    task_type: TaskType
    status: TaskStatus
    start_time: Optional[datetime]
    end_time: Optional[datetime]
    duration_seconds: Optional[int]
    results_count: int
    error_message: Optional[str]
    results_data: Optional[Dict[str, Any]]
    created_at: datetime
    
    # 关联任务信息
    scheduled_task_name: Optional[str] = Field(None, description="定时任务名称")

class TaskExecutionListResponse(BaseModel):
    """任务执行记录列表响应"""
    total: int
    items: List[TaskExecutionResponse]
```

##### app/services/tasks/scheduler.py
```python
"""定时任务调度器"""

import asyncio
import logging
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from croniter import croniter
from sqlalchemy.orm import Session

from app.core.database import db_session
from app.models.task import UserScheduledTask, TaskExecution
from app.services.tasks.executor import TaskExecutor
from app.utils.dependencies import get_current_user

logger = logging.getLogger(__name__)

class TaskScheduler:
    """定时任务调度器"""
    
    def __init__(self):
        self.running = False
        self.executor = TaskExecutor()
        self._scheduler_task: Optional[asyncio.Task] = None
    
    async def start(self):
        """启动调度器"""
        if self.running:
            return
        
        self.running = True
        self._scheduler_task = asyncio.create_task(self._scheduler_loop())
        logger.info("定时任务调度器已启动")
    
    async def stop(self):
        """停止调度器"""
        self.running = False
        if self._scheduler_task:
            self._scheduler_task.cancel()
            try:
                await self._scheduler_task
            except asyncio.CancelledError:
                pass
        logger.info("定时任务调度器已停止")
    
    async def _scheduler_loop(self):
        """调度器主循环"""
        while self.running:
            try:
                await self._check_and_execute_tasks()
                await asyncio.sleep(60)  # 每分钟检查一次
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"调度器执行错误: {e}")
                await asyncio.sleep(60)
    
    async def _check_and_execute_tasks(self):
        """检查并执行到期的任务"""
        now = datetime.now()
        
        async with db_session() as db:
            # 获取需要执行的任务
            tasks = db.query(UserScheduledTask).filter(
                UserScheduledTask.is_active == True,
                UserScheduledTask.next_execution <= now
            ).all()
            
            for task in tasks:
                # 检查是否已达最大执行次数
                if (task.max_executions is not None and 
                    task.current_executions >= task.max_executions):
                    task.is_active = False
                    db.commit()
                    continue
                
                # 创建执行记录
                execution = TaskExecution(
                    user_id=task.user_id,
                    scheduled_task_id=task.id,
                    trigger_type="scheduled",
                    task_type=task.task_type,
                    task_config=task.task_config,
                    status="pending"
                )
                db.add(execution)
                db.commit()
                
                # 异步执行任务
                asyncio.create_task(self.executor.execute_task(execution.id))
                
                # 更新下次执行时间
                cron = croniter(task.cron_expression, now)
                task.next_execution = cron.get_next(datetime)
                task.current_executions += 1
                task.last_execution = now
                
                db.commit()

# 全局调度器实例
_scheduler = TaskScheduler()

async def get_task_scheduler() -> TaskScheduler:
    """获取任务调度器实例"""
    return _scheduler
```

##### app/services/tasks/executor.py
```python
"""任务执行器"""

import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from sqlalchemy.orm import Session

from app.core.database import db_session
from app.models.task import TaskExecution
from app.services.scan.memory_scanner import MemoryScanner
from app.services.scan.session_manager import get_session_manager
from app.schemas.scheduled_task import IndicatorScanConfig, TaskStatus

logger = logging.getLogger(__name__)

class TaskExecutor:
    """任务执行器"""
    
    def __init__(self):
        self.memory_scanner = None
    
    async def execute_task(self, execution_id: int):
        """执行任务"""
        async with db_session() as db:
            execution = db.query(TaskExecution).get(execution_id)
            if not execution:
                logger.error(f"任务执行记录不存在: {execution_id}")
                return
            
            try:
                # 更新状态为运行中
                execution.status = TaskStatus.RUNNING
                execution.start_time = datetime.now()
                db.commit()
                
                # 根据任务类型执行
                if execution.task_type == "indicator_scan":
                    await self._execute_indicator_scan(execution, db)
                else:
                    raise ValueError(f"不支持的任务类型: {execution.task_type}")
                
                # 更新完成状态
                execution.status = TaskStatus.COMPLETED
                execution.end_time = datetime.now()
                execution.duration_seconds = int(
                    (execution.end_time - execution.start_time).total_seconds()
                )
                
            except Exception as e:
                logger.error(f"任务执行失败: {e}")
                execution.status = TaskStatus.FAILED
                execution.error_message = str(e)
                execution.end_time = datetime.now()
                if execution.start_time:
                    execution.duration_seconds = int(
                        (execution.end_time - execution.start_time).total_seconds()
                    )
            finally:
                db.commit()
    
    async def _execute_indicator_scan(self, execution: TaskExecution, db: Session):
        """执行指标扫描任务"""
        # 解析任务配置
        config_data = json.loads(execution.task_config)
        config = IndicatorScanConfig(**config_data)
        
        # 初始化扫描器
        if not self.memory_scanner:
            session_manager = get_session_manager()
            from app.services.scan.manager import get_memory_scanner
            self.memory_scanner = get_memory_scanner()
        
        # 创建临时会话ID
        session_id = f"scheduled_task_{execution.id}"
        
        # 启动扫描
        task_id = await self.memory_scanner.start_scan(
            session_id=session_id,
            indicators=config.indicators,
            stock_codes=config.stock_codes,
            parameters=config.parameters,
            scan_mode=config.scan_mode,
            periods=config.periods,
            adjust=config.adjust
        )
        
        # 等待扫描完成
        session_manager = get_session_manager()
        while True:
            task = session_manager.get_scan_task(session_id, task_id)
            if not task or task.status.value in ["completed", "failed", "cancelled"]:
                break
            await asyncio.sleep(5)
        
        # 获取扫描结果
        if task and task.status.value == "completed":
            results = [result.dict() for result in task.results]
            execution.results_data = json.dumps(results, ensure_ascii=False, default=str)
            execution.results_count = len(results)
        else:
            execution.error_message = task.error_message if task else "未知错误"
            raise Exception(execution.error_message)
```

##### app/api/endpoints/scheduled_tasks.py
```python
"""定时任务API端点"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_

from app.core.database import get_db
from app.utils.dependencies import get_current_user
from app.models.user import User
from app.models.task import UserScheduledTask, TaskExecution
from app.schemas.scheduled_task import (
    UserScheduledTaskCreate,
    UserScheduledTaskUpdate,
    UserScheduledTaskResponse,
    TaskExecutionResponse,
    TaskExecutionListResponse
)
from app.services.tasks.executor import TaskExecutor

router = APIRouter()

@router.post("/", response_model=UserScheduledTaskResponse)
async def create_scheduled_task(
    task_data: UserScheduledTaskCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建定时任务"""
    from croniter import croniter
    from datetime import datetime
    
    # 验证cron表达式
    try:
        cron = croniter(task_data.cron_expression)
        next_run = cron.get_next(datetime)
    except Exception:
        raise HTTPException(status_code=400, detail="无效的Cron表达式")
    
    # 创建任务
    task = UserScheduledTask(
        user_id=current_user.id,
        name=task_data.name,
        task_type=task_data.task_type,
        cron_expression=task_data.cron_expression,
        task_config=task_data.task_config.json(),
        description=task_data.description,
        max_executions=task_data.max_executions,
        next_execution=next_run
    )
    
    db.add(task)
    db.commit()
    db.refresh(task)
    
    return UserScheduledTaskResponse(**task.__dict__)

@router.get("/", response_model=List[UserScheduledTaskResponse])
async def list_scheduled_tasks(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取用户的定时任务列表"""
    tasks = db.query(UserScheduledTask).filter(
        UserScheduledTask.user_id == current_user.id
    ).order_by(desc(UserScheduledTask.created_at)).offset(skip).limit(limit).all()
    
    return [UserScheduledTaskResponse(**task.__dict__) for task in tasks]

@router.get("/{task_id}", response_model=UserScheduledTaskResponse)
async def get_scheduled_task(
    task_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取特定定时任务详情"""
    task = db.query(UserScheduledTask).filter(
        and_(UserScheduledTask.id == task_id, UserScheduledTask.user_id == current_user.id)
    ).first()
    
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return UserScheduledTaskResponse(**task.__dict__)

@router.put("/{task_id}", response_model=UserScheduledTaskResponse)
async def update_scheduled_task(
    task_id: int,
    task_data: UserScheduledTaskUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新定时任务"""
    task = db.query(UserScheduledTask).filter(
        and_(UserScheduledTask.id == task_id, UserScheduledTask.user_id == current_user.id)
    ).first()
    
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    # 更新字段
    update_data = task_data.dict(exclude_unset=True)
    
    # 处理cron表达式更新
    if "cron_expression" in update_data:
        from croniter import croniter
        from datetime import datetime
        try:
            cron = croniter(update_data["cron_expression"])
            task.next_execution = cron.get_next(datetime)
        except Exception:
            raise HTTPException(status_code=400, detail="无效的Cron表达式")
    
    # 处理配置更新
    if "task_config" in update_data:
        update_data["task_config"] = update_data["task_config"].json()
    
    for field, value in update_data.items():
        setattr(task, field, value)
    
    db.commit()
    db.refresh(task)
    
    return UserScheduledTaskResponse(**task.__dict__)

@router.delete("/{task_id}")
async def delete_scheduled_task(
    task_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除定时任务"""
    task = db.query(UserScheduledTask).filter(
        and_(UserScheduledTask.id == task_id, UserScheduledTask.user_id == current_user.id)
    ).first()
    
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    db.delete(task)
    db.commit()
    
    return {"message": "任务已删除"}

@router.post("/{task_id}/execute")
async def execute_task_manually(
    task_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """手动执行定时任务"""
    task = db.query(UserScheduledTask).filter(
        and_(UserScheduledTask.id == task_id, UserScheduledTask.user_id == current_user.id)
    ).first()
    
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    # 创建执行记录
    execution = TaskExecution(
        user_id=current_user.id,
        scheduled_task_id=task.id,
        trigger_type="manual",
        task_type=task.task_type,
        task_config=task.task_config,
        status="pending"
    )
    
    db.add(execution)
    db.commit()
    db.refresh(execution)
    
    # 异步执行任务
    executor = TaskExecutor()
    import asyncio
    asyncio.create_task(executor.execute_task(execution.id))
    
    return {"message": "任务已提交执行", "execution_id": execution.id}

@router.get("/{task_id}/executions", response_model=TaskExecutionListResponse)
async def get_task_executions(
    task_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=500),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取任务执行记录"""
    # 验证任务所有权
    task = db.query(UserScheduledTask).filter(
        and_(UserScheduledTask.id == task_id, UserScheduledTask.user_id == current_user.id)
    ).first()
    
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    # 获取执行记录
    query = db.query(TaskExecution).filter(TaskExecution.scheduled_task_id == task_id)
    total = query.count()
    executions = query.order_by(desc(TaskExecution.created_at)).offset(skip).limit(limit).all()
    
    # 构造响应
    items = []
    for execution in executions:
        item_dict = execution.__dict__.copy()
        item_dict["scheduled_task_name"] = task.name
        items.append(TaskExecutionResponse(**item_dict))
    
    return TaskExecutionListResponse(total=total, items=items)
```

#### 更新文件

##### app/models/user.py (添加关系)
```python
# 在User类中添加：
scheduled_tasks = relationship("UserScheduledTask", back_populates="user", cascade="all, delete-orphan")
task_executions = relationship("TaskExecution", back_populates="user", cascade="all, delete-orphan")
```

##### app/models/__init__.py (导入新模型)
```python
from .task import UserScheduledTask, TaskExecution
```

### API接口设计

#### 定时任务管理接口

##### POST /api/v1/scheduled-tasks/
- **功能**: 创建定时任务
- **请求体**: UserScheduledTaskCreate
- **响应**: UserScheduledTaskResponse
- **权限**: 需要登录

##### GET /api/v1/scheduled-tasks/
- **功能**: 获取用户定时任务列表
- **查询参数**: skip, limit
- **响应**: List[UserScheduledTaskResponse]
- **权限**: 需要登录

##### GET /api/v1/scheduled-tasks/{task_id}
- **功能**: 获取定时任务详情
- **响应**: UserScheduledTaskResponse
- **权限**: 任务所有者

##### PUT /api/v1/scheduled-tasks/{task_id}
- **功能**: 更新定时任务
- **请求体**: UserScheduledTaskUpdate
- **响应**: UserScheduledTaskResponse
- **权限**: 任务所有者

##### DELETE /api/v1/scheduled-tasks/{task_id}
- **功能**: 删除定时任务
- **响应**: 成功消息
- **权限**: 任务所有者

##### POST /api/v1/scheduled-tasks/{task_id}/execute
- **功能**: 手动触发任务执行
- **响应**: 执行记录ID
- **权限**: 任务所有者

#### 任务执行记录接口

##### GET /api/v1/scheduled-tasks/{task_id}/executions
- **功能**: 获取任务执行历史
- **查询参数**: skip, limit
- **响应**: TaskExecutionListResponse
- **权限**: 任务所有者

##### GET /api/v1/task-executions/
- **功能**: 获取用户所有任务执行记录
- **查询参数**: skip, limit, trigger_type, status
- **响应**: TaskExecutionListResponse
- **权限**: 需要登录

##### GET /api/v1/task-executions/{execution_id}
- **功能**: 获取执行记录详情
- **响应**: TaskExecutionResponse
- **权限**: 执行记录所有者

##### DELETE /api/v1/task-executions/{execution_id}
- **功能**: 删除执行记录
- **响应**: 成功消息
- **权限**: 执行记录所有者

### 前端界面设计

#### 页面结构
```
frontend-app/src/pages/ScheduledTasks/
├── index.vue                 # 定时任务列表页面
├── components/
│   ├── TaskList.vue         # 任务列表组件
│   ├── TaskForm.vue         # 任务创建/编辑表单
│   ├── CronBuilder.vue      # Cron表达式可视化构建器
│   ├── TaskExecutions.vue   # 任务执行记录组件
│   └── ExecutionDetails.vue # 执行详情组件
├── composables/
│   ├── useScheduledTasks.js # 定时任务操作逻辑
│   ├── useCronBuilder.js    # Cron构建器逻辑
│   └── useTaskExecutions.js # 执行记录操作逻辑
└── types/
    └── task.ts              # TypeScript类型定义
```

#### 核心组件设计

##### frontend-app/src/pages/ScheduledTasks/index.vue
```vue
<template>
  <div class="scheduled-tasks-page">
    <div class="page-header">
      <h1>定时任务管理</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <i-carbon-add class="mr-1" /> 创建任务
      </el-button>
    </div>
    
    <TaskList 
      :tasks="tasks"
      :loading="loading"
      @edit="handleEdit"
      @delete="handleDelete"
      @execute="handleManualExecute"
      @view-executions="handleViewExecutions"
    />
    
    <TaskForm
      v-model="showCreateDialog"
      :task="editingTask"
      @submit="handleSubmit"
      @cancel="handleCancel"
    />
    
    <TaskExecutions
      v-model="showExecutionsDialog" 
      :task-id="selectedTaskId"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useScheduledTasks } from './composables/useScheduledTasks'
import TaskList from './components/TaskList.vue'
import TaskForm from './components/TaskForm.vue'
import TaskExecutions from './components/TaskExecutions.vue'

const { 
  tasks, 
  loading, 
  loadTasks, 
  createTask, 
  updateTask, 
  deleteTask,
  executeTask 
} = useScheduledTasks()

const showCreateDialog = ref(false)
const showExecutionsDialog = ref(false)
const editingTask = ref(null)
const selectedTaskId = ref(null)

onMounted(() => {
  loadTasks()
})

const handleEdit = (task) => {
  editingTask.value = { ...task }
  showCreateDialog.value = true
}

const handleDelete = async (taskId) => {
  await deleteTask(taskId)
  await loadTasks()
}

const handleManualExecute = async (taskId) => {
  await executeTask(taskId)
  ElMessage.success('任务已提交执行')
}

const handleViewExecutions = (taskId) => {
  selectedTaskId.value = taskId
  showExecutionsDialog.value = true
}

const handleSubmit = async (taskData) => {
  if (editingTask.value) {
    await updateTask(editingTask.value.id, taskData)
  } else {
    await createTask(taskData)
  }
  await loadTasks()
  handleCancel()
}

const handleCancel = () => {
  showCreateDialog.value = false
  editingTask.value = null
}
</script>
```

##### frontend-app/src/pages/ScheduledTasks/components/CronBuilder.vue
```vue
<template>
  <div class="cron-builder">
    <div class="cron-input">
      <el-input 
        v-model="cronExpression"
        placeholder="输入Cron表达式"
        @input="parseCron"
      />
      <el-button @click="showVisualBuilder = !showVisualBuilder">
        可视化编辑器
      </el-button>
    </div>
    
    <div v-if="showVisualBuilder" class="visual-builder">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="简单" name="simple">
          <SimpleBuilder v-model="cronData" @change="buildCron" />
        </el-tab-pane>
        <el-tab-pane label="高级" name="advanced">
          <AdvancedBuilder v-model="cronData" @change="buildCron" />
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <div v-if="nextRuns.length > 0" class="next-runs">
      <h4>下次执行时间预览:</h4>
      <ul>
        <li v-for="time in nextRuns" :key="time">{{ time }}</li>
      </ul>
    </div>
    
    <div v-if="cronError" class="error-message">
      {{ cronError }}
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useCronBuilder } from '../composables/useCronBuilder'

const props = defineProps({
  modelValue: String
})

const emit = defineEmits(['update:modelValue'])

const { cronExpression, cronData, nextRuns, cronError, parseCron, buildCron } = useCronBuilder()

const showVisualBuilder = ref(false)
const activeTab = ref('simple')

// 同步外部值
cronExpression.value = props.modelValue

// 同步到外部
const syncToParent = () => {
  emit('update:modelValue', cronExpression.value)
}

watch(() => cronExpression.value, syncToParent)
</script>
```

##### frontend-app/src/pages/ScheduledTasks/composables/useScheduledTasks.js
```javascript
import { ref, reactive } from 'vue'
import { scheduledTasksApi } from '@/services/api/scheduledTasks'
import { ElMessage, ElMessageBox } from 'element-plus'

export function useScheduledTasks() {
  const tasks = ref([])
  const loading = ref(false)
  const pagination = reactive({
    current: 1,
    pageSize: 20,
    total: 0
  })

  const loadTasks = async (params = {}) => {
    loading.value = true
    try {
      const response = await scheduledTasksApi.getTasks({
        skip: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize,
        ...params
      })
      tasks.value = response.data || response
      // 假设API返回total字段
      if (response.total !== undefined) {
        pagination.total = response.total
      }
    } catch (error) {
      ElMessage.error('加载任务列表失败')
      console.error(error)
    } finally {
      loading.value = false
    }
  }

  const createTask = async (taskData) => {
    try {
      await scheduledTasksApi.createTask(taskData)
      ElMessage.success('任务创建成功')
    } catch (error) {
      ElMessage.error('任务创建失败')
      throw error
    }
  }

  const updateTask = async (taskId, taskData) => {
    try {
      await scheduledTasksApi.updateTask(taskId, taskData)
      ElMessage.success('任务更新成功')
    } catch (error) {
      ElMessage.error('任务更新失败')
      throw error
    }
  }

  const deleteTask = async (taskId) => {
    try {
      await ElMessageBox.confirm('确认删除此任务？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      
      await scheduledTasksApi.deleteTask(taskId)
      ElMessage.success('任务删除成功')
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('任务删除失败')
        throw error
      }
    }
  }

  const executeTask = async (taskId) => {
    try {
      const result = await scheduledTasksApi.executeTask(taskId)
      return result
    } catch (error) {
      ElMessage.error('任务执行失败')
      throw error
    }
  }

  const toggleTaskStatus = async (taskId, isActive) => {
    try {
      await scheduledTasksApi.updateTask(taskId, { is_active: isActive })
      ElMessage.success(isActive ? '任务已启用' : '任务已禁用')
      await loadTasks()
    } catch (error) {
      ElMessage.error('状态更新失败')
      throw error
    }
  }

  return {
    tasks,
    loading,
    pagination,
    loadTasks,
    createTask,
    updateTask,
    deleteTask,
    executeTask,
    toggleTaskStatus
  }
}
```

##### frontend-app/src/services/api/scheduledTasks.js
```javascript
import apiClient from '../apiClient'

export const scheduledTasksApi = {
  // 获取任务列表
  getTasks(params) {
    return apiClient.get('/scheduled-tasks/', { params })
  },

  // 创建任务
  createTask(data) {
    return apiClient.post('/scheduled-tasks/', data)
  },

  // 获取任务详情
  getTask(taskId) {
    return apiClient.get(`/scheduled-tasks/${taskId}`)
  },

  // 更新任务
  updateTask(taskId, data) {
    return apiClient.put(`/scheduled-tasks/${taskId}`, data)
  },

  // 删除任务
  deleteTask(taskId) {
    return apiClient.delete(`/scheduled-tasks/${taskId}`)
  },

  // 手动执行任务
  executeTask(taskId) {
    return apiClient.post(`/scheduled-tasks/${taskId}/execute`)
  },

  // 获取执行记录
  getExecutions(taskId, params) {
    return apiClient.get(`/scheduled-tasks/${taskId}/executions`, { params })
  },

  // 获取所有执行记录
  getAllExecutions(params) {
    return apiClient.get('/task-executions/', { params })
  },

  // 获取执行详情
  getExecutionDetail(executionId) {
    return apiClient.get(`/task-executions/${executionId}`)
  },

  // 删除执行记录
  deleteExecution(executionId) {
    return apiClient.delete(`/task-executions/${executionId}`)
  }
}
```

### 任务执行架构设计

#### 核心组件

##### 1. TaskScheduler (调度器)
- **职责**: 定时检查待执行任务，触发任务执行
- **运行方式**: 后台异步循环，每分钟扫描一次
- **cron解析**: 使用croniter库解析cron表达式
- **并发控制**: 每个任务异步执行，避免阻塞

##### 2. TaskExecutor (执行器)  
- **职责**: 实际执行任务逻辑，复用MemoryScanner组件
- **扩展性**: 支持多种任务类型，当前实现指标扫描
- **错误处理**: 完整的异常捕获和状态更新
- **结果存储**: 将执行结果保存到数据库

##### 3. 抽象任务类型系统
```python
class BaseTask(ABC):
    """抽象任务基类"""
    
    @abstractmethod
    async def execute(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """执行任务"""
        pass
    
    @abstractmethod
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置"""
        pass

class IndicatorScanTask(BaseTask):
    """指标扫描任务实现"""
    
    async def execute(self, config: Dict[str, Any]) -> Dict[str, Any]:
        # 复用MemoryScanner逻辑
        pass

class AIAnalysisTask(BaseTask):
    """AI分析任务实现 (预留)"""
    pass
```

### 集成方案

#### 与现有系统集成点

##### 1. 用户认证系统
- **依赖**: 复用现有User模型和认证中间件
- **权限**: 所有用户可创建和管理自己的定时任务
- **会话**: 定时任务执行时创建临时会话ID

##### 2. MemoryScanner集成
- **复用策略**: 直接调用MemoryScanner.start_scan()方法
- **配置映射**: 将IndicatorScanConfig转换为扫描器参数
- **结果处理**: 获取ScanResult列表并存储到执行记录

##### 3. 数据库集成
- **外键关系**: UserScheduledTask -> User, TaskExecution -> User/UserScheduledTask
- **数据迁移**: 通过Alembic管理数据库schema变更
- **性能优化**: 适当的索引设计支持高效查询

##### 4. 前端路由集成
```javascript
// frontend-app/src/router/index.js
{
  path: '/scheduled-tasks',
  name: 'ScheduledTasks',
  component: () => import('@/pages/ScheduledTasks/index.vue'),
  meta: { requiresAuth: true }
}
```

##### 5. 导航菜单集成
```vue
<!-- frontend-app/src/components/layout/Sidebar.vue -->
<router-link to="/scheduled-tasks" class="nav-item">
  <i-carbon-time class="nav-icon" />
  <span>定时任务</span>
</router-link>
```

## 实施序列

### 阶段1: 数据库和模型 (1-2天)
1. 创建UserScheduledTask和TaskExecution数据模型
2. 编写数据库迁移脚本
3. 更新User模型添加关联关系
4. 编写Schema定义和验证逻辑

### 阶段2: 后端服务和API (2-3天)
1. 实现TaskScheduler调度器服务
2. 实现TaskExecutor执行器，集成MemoryScanner
3. 创建定时任务管理API端点
4. 实现任务执行记录查询API
5. 集成到应用启动流程

### 阶段3: 前端界面 (2-3天)
1. 创建定时任务列表页面和组件
2. 实现Cron表达式可视化构建器
3. 创建任务配置表单组件
4. 实现任务执行记录查看界面
5. 集成到主导航和路由系统

### 阶段4: 测试和优化 (1-2天)
1. 单元测试: 任务调度和执行逻辑
2. 集成测试: API端点和数据库操作
3. 前端测试: 用户界面交互流程
4. 性能测试: 大量定时任务的调度性能
5. 错误场景测试: 任务失败和异常处理

## 验证计划

### 单元测试
- **任务模型验证**: UserScheduledTask和TaskExecution字段验证
- **Cron解析测试**: 各种cron表达式的解析和下次执行时间计算
- **任务执行逻辑**: TaskExecutor各种任务类型的执行测试
- **API Schema验证**: 请求/响应数据结构验证

### 集成测试  
- **定时任务创建流程**: 从前端创建到数据库存储的完整流程
- **任务执行流程**: 调度器触发到执行完成的端到端测试
- **权限控制测试**: 用户只能管理自己的任务
- **并发执行测试**: 多个任务同时执行的稳定性

### 业务逻辑验证
- **Cron表达式准确性**: 验证各种时间配置的准确执行
- **最大执行次数限制**: 验证任务达到限制后自动停用
- **手动执行功能**: 验证用户手动触发任务的功能
- **执行记录管理**: 验证历史记录的查看和删除功能
- **与现有扫描功能的兼容性**: 确保定时任务和手动扫描结果格式一致