# 用户定时任务功能需求确认

## 原始需求
现在要开发定时任务功能，虽然现在已经有定时任务的实现，但是新的需求是用户可以通过前端界面设定定时的技术指标或者其他任务，并查看相应的任务结果。数据库要增加任务列表库，并存储任务结果，同时需要保存每次扫描任务的结果。后期还会加一个新的任务就是ai分析。

## 需求澄清轮次

### 第一轮澄清 (质量分数: 75分)
**问题涵盖**: 功能范围、技术集成、错误处理、业务优先级

### 第二轮确认 (质量分数: 92分)
**用户明确回答**:
1. **界面设计**: 专门的"定时任务"路由页面
2. **任务类型**: 当前只有技术指标扫描，AI分析预留扩展
3. **时间配置**: 基于cron但提供可视化界面，支持执行次数限制
4. **参数配置**: 与现有扫描界面一致但更精简
5. **架构设计**: 抽象任务类，复用MemoryScanner
6. **数据管理**: 结果永久保存，用户可删除
7. **错误处理**: 在结果界面显示失败状态和原因
8. **用户权限**: 所有用户可用，无需通知功能

## 最终确认的需求规格

### 1. 功能模块
- **定时任务管理页面**: 独立路由，任务的增删改查
- **任务配置界面**: cron可视化设置 + 参数配置
- **任务结果查看**: 历史执行记录，支持删除操作
- **任务执行引擎**: 抽象任务类 + MemoryScanner集成

### 2. 数据库设计
- **UserScheduledTask**: 用户定时任务配置表
- **TaskExecution**: 任务执行记录表（统一记录定时任务和手动扫描）
- 支持JSON字段存储灵活参数和结果
- 执行记录包含触发方式标识（定时/手动）

### 3. 技术要求
- 复用现有MemoryScanner逻辑
- 可扩展的任务类型架构
- cron表达式可视化配置
- 精简的参数配置界面
- 永久数据存储机制

### 4. 业务规则
- 所有用户可用
- 支持一次性、多次、无限期执行
- 任务失败显示详细错误信息
- 用户可管理自己的任务历史

**需求质量评分**: 92/100 (达到实施标准)