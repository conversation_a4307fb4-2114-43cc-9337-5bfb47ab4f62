# 技术指标扫描器算法优化实施任务

## 实施任务清单

### 1. 后端算法修正和参数固化
- [ ] 1.1 修正StockIndicatorCalculator类的固定参数常量
  - 将KDJ参数从n=9改为n=20，保持m1=3, m2=3
  - 设置布林带参数为window=20, std_dev=2.0
  - 设置EMA周期为10天，数据周期为20天
  - **需求引用**: 1.6 - 系统应当使用固定参数

- [ ] 1.2 重构calculate_all_indicators方法移除参数输入
  - 移除所有方法参数，使用类常量
  - 更新方法签名为calculate_all_indicators(self, df: pd.DataFrame) -> pd.DataFrame
  - 修改所有技术指标计算调用使用固定参数
  - **需求引用**: 2.5 - 系统应当在后端硬编码所有技术指标计算参数

- [ ] 1.3 实现准确的KDJ金叉死叉检测逻辑
  - 创建check_kdj_golden_cross方法：检查T-1时K<D且T时K>D
  - 创建check_kdj_death_cross方法：检查T-1时K>D且T时K<D
  - 添加边界条件检查（current_index < 1时返回False）
  - **需求引用**: 1.1, 1.2 - KDJ金叉死叉条件检测

- [ ] 1.4 修正成交量压力指标判断逻辑
  - 实现check_volume_pressure_breakout方法：当日值 > 20日平均
  - 实现check_volume_pressure_decline方法：当日值 < 20日平均
  - 移除复杂的突破检测逻辑，改为简单数值比较
  - **需求引用**: 1.3, 1.4 - 成交量压力突破和衰减条件

- [ ] 1.5 实现布林带条件检测
  - 创建check_bollinger_stop_loss方法：收盘价 < 下轨线
  - 创建check_bollinger_opportunity方法：接近下轨但未跌破的买入机会
  - **需求引用**: 1.5 - 布林带止损条件检测

- [ ] 1.6 实现新的条件筛选核心逻辑
  - 创建check_indicator_conditions方法，接收selected_indicators参数
  - 根据选中指标列表应用相应的检测方法
  - 实现AND逻辑：所有选中条件都必须满足
  - **需求引用**: 3.4, 3.5 - 多条件组合和AND逻辑

### 2. 扫描服务层修改
- [ ] 2.1 修改MemoryScanner的_analyze_stock方法
  - 移除thresholds参数，只保留indicators参数
  - 使用新的check_indicator_conditions方法进行股票筛选
  - 不满足条件时返回None，实现有效过滤
  - **需求引用**: 3.6 - 仅返回满足所有选中条件的股票

- [ ] 2.2 扩展StockIndicatorData模型
  - 添加prev_kdj_k和prev_kdj_d字段用于金叉死叉判断
  - 添加volume_pressure_avg字段存储20日平均值
  - 添加close_price字段存储当日收盘价
  - 添加bollinger_distance_pct字段计算距离下轨百分比
  - **需求引用**: 5.1-5.4 - 扩展数据结构支持新显示需求

- [ ] 2.3 更新_build_scan_result方法构建增强结果数据
  - 计算并填充新增的指标数据字段
  - 从历史数据中提取前一日KDJ值
  - 计算布林带距离百分比
  - 确保数据类型转换正确性
  - **需求引用**: 5.5, 5.6 - 保持数据结构兼容性和数值精度

### 3. API接口简化
- [ ] 3.1 修改扫描启动API端点
  - 更新start_scan方法签名，移除thresholds参数
  - 修改请求验证逻辑，只检查indicators数组
  - 更新API文档和响应示例
  - **需求引用**: 6.1, 6.2 - 简化API接口参数

- [ ] 3.2 确保API响应格式兼容性
  - 验证新的StockIndicatorData字段能正确序列化
  - 测试前端数据接收的完整性
  - 保持现有错误处理机制
  - **需求引用**: 6.3, 6.4, 6.5 - API兼容性和错误处理

### 4. 前端界面简化
- [ ] 4.1 移除Scanner/index.vue中的参数配置UI组件
  - 删除参数配置按钮（第45-49行）
  - 删除整个参数配置对话框（第84-194行）
  - 删除快速预设功能相关代码
  - **需求引用**: 2.1, 2.2, 2.3, 2.4 - 移除参数配置UI

- [ ] 4.2 重构指标选择区域为条件筛选器
  - 更新标题和说明文字为"筛选条件"
  - 修改指标选项描述：KDJ金叉、成交量突破、布林带机会
  - 添加条件说明文字："勾选要筛选的技术指标条件"
  - **需求引用**: 3.1, 3.2, 3.3 - 条件筛选选项

- [ ] 4.3 简化扫描表单数据结构
  - 修改scanForm对象，移除thresholds配置
  - 只保留indicators数组用于条件选择
  - 更新表单验证逻辑
  - **需求引用**: 7.1, 7.2 - 界面说明文字更新

- [ ] 4.4 更新扫描请求发送逻辑
  - 修改startScan方法，只传递indicators参数
  - 移除thresholds参数构建逻辑
  - 确保与后端API接口匹配
  - **需求引用**: 6.1, 6.2 - API参数简化

### 5. 结果展示增强
- [ ] 5.1 扩展ScannerResults.vue的表格列定义
  - 添加"满足条件"列显示触发的筛选条件
  - 添加"KDJ详情"列显示K、D值和金叉状态
  - 添加"成交量详情"列显示当前值、平均值和突破状态
  - 添加"布林带详情"列显示收盘价、下轨值和距离
  - **需求引用**: 4.1, 4.2, 4.3, 4.4 - 增强结果表格显示

- [ ] 5.2 实现自定义表格模板
  - 创建triggered_conditions模板显示满足的条件标签
  - 创建kdj_details模板显示KDJ值和金叉死叉状态
  - 创建volume_details模板显示成交量压力对比
  - 创建bollinger_details模板显示布林带距离信息
  - **需求引用**: 4.4, 4.5 - 信号原因和布林带预警显示

- [ ] 5.3 添加条件状态样式和图标
  - 为不同信号类型设计颜色标识（绿色买入、红色止损等）
  - 添加金叉、突破等状态的图标显示
  - 实现跌破布林带的红色高亮显示
  - **需求引用**: 4.5 - 跌破布林带红色显示

### 6. 数据完整性和错误处理
- [ ] 6.1 实现算法边界条件处理
  - 添加数据不足时的降级处理（少于20日数据）
  - 实现除零错误防护（成交量为0时）
  - 添加NaN值处理和默认值设置
  - **需求引用**: 设计文档 - 错误处理章节

- [ ] 6.2 添加用户体验错误处理
  - 实现未选择筛选条件时的友好提示
  - 添加扫描结果为空时的说明信息
  - 保持现有网络错误重试机制
  - **需求引用**: 7.5 - 结果为空时的提示信息

### 7. 测试验证
- [ ] 7.1 编写技术指标算法单元测试
  - 测试KDJ金叉死叉判断逻辑准确性
  - 测试成交量压力计算和比较逻辑
  - 测试布林带计算和条件判断
  - 测试边界条件和异常数据处理
  - **需求引用**: 设计文档 - 测试策略

- [ ] 7.2 编写筛选逻辑集成测试
  - 测试单一条件筛选功能
  - 测试多条件AND逻辑组合
  - 测试空结果和边界场景
  - 验证API请求响应的完整性
  - **需求引用**: 设计文档 - 集成测试

- [ ] 7.3 编写前端组件测试
  - 测试条件选择器交互功能
  - 测试扫描按钮状态和进度显示
  - 测试结果表格渲染和数据展示
  - 测试错误状态和提示信息显示
  - **需求引用**: 设计文档 - 前端组件测试

- [ ] 7.4 执行端到端功能验证
  - 验证完整扫描流程的正确性
  - 验证结果数据的准确性和完整性
  - 验证不同条件组合的筛选效果
  - 验证界面操作的用户体验
  - **需求引用**: 设计文档 - 端到端测试