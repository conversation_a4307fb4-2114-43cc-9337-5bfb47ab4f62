# 技术指标回测功能技术规范

## 问题陈述

### 业务问题
当前扫描系统仅支持基于最新数据的实时扫描，无法支持历史回测分析。用户需要能够选择历史特定日期进行技术指标扫描，以验证投资策略的历史有效性。

### 当前状态
- 扫描系统固定使用最新交易日数据
- 缺少历史日期选择功能
- 页面刷新后扫描配置丢失
- 扫描结果缺少数据截止日期信息

### 预期结果
- 在所有扫描模式下添加历史日期选择功能
- 实现完整参数回显机制（指标+参数+日期+模式）
- 扫描结果显示实际数据截止日期
- 提供用户友好的日期选择体验

## 解决方案概述

### 方案策略
在现有扫描系统基础上扩展历史日期选择能力，通过在API请求中添加end_date参数实现回测功能，同时增强前端状态管理保证配置持久化。

### 核心变更
1. 新增交易日API端点提供最近交易日
2. 扩展扫描API支持end_date参数
3. 前端增加日期选择器组件
4. 实现完整的参数回显机制
5. 优化扫描结果显示包含截止日期

### 成功标准
- 用户可在所有扫描模式下选择历史日期
- 页面刷新后所有配置（包括日期）完整恢复
- 扫描结果明确显示数据截止日期
- 日期选择器默认值为最近交易日

## 技术实施方案

### 数据库变更
**TaskExecution表扩展**
```sql
-- 无需新增表，利用现有task_config JSON字段存储end_date
-- task_config示例结构：
{
  "indicators": ["kdj", "volume_pressure"],
  "end_date": "2024-01-15",
  "scan_mode": "traditional",
  "parameters": {...},
  ...
}
```

### 代码变更

#### 后端API变更

**新增交易日API端点**
- 文件：`/home/<USER>/workspace/quantization/app/api/endpoints/trading.py`
- 功能：提供最近交易日期查询
```python
@router.get("/latest-trading-date")
async def get_latest_trading_date() -> dict:
    """获取最近交易日期"""
    from app.utils.trading_utils import get_last_trading_date, format_trading_date
    trading_date = get_last_trading_date()
    return {
        "date": format_trading_date(trading_date),
        "formatted": trading_date.strftime('%Y年%m月%d日')
    }
```

**扫描API扩展**
- 文件：`/home/<USER>/workspace/quantization/app/api/endpoints/scan.py`
- 修改：`ScanStartRequest`模式支持end_date参数
- 修改：`start_scan`端点存储end_date到task_config

**Schema扩展**
- 文件：`/home/<USER>/workspace/quantization/app/schemas/scan.py`
- 扩展：`ScanStartRequest`添加可选end_date字段
- 扩展：`ScanResultResponse`添加end_date字段用于结果显示

#### 前端组件变更

**主扫描页面**
- 文件：`/home/<USER>/workspace/quantization/frontend-app/src/pages/Scanner/index.vue`
- 新增：历史日期选择器组件
- 修改：开始扫描按钮改为Icon按钮
- 新增：参数回显逻辑包含日期状态

**新增日期选择器组件**
- 文件：`/home/<USER>/workspace/quantization/frontend-app/src/components/scanner/DatePicker.vue`
- 功能：提供交易日选择，tooltip显示，默认最近交易日

**扫描结果页面**
- 文件：`/home/<USER>/workspace/quantization/frontend-app/src/pages/Scanner/ScannerResults.vue`
- 修改：标题显示截止日期信息

**状态管理扩展**
- 文件：`/home/<USER>/workspace/quantization/frontend-app/src/store/modules/scanner.js`
- 扩展：支持end_date参数传递
- 修改：任务配置回显包含日期信息

**API客户端扩展**
- 文件：`/home/<USER>/workspace/quantization/frontend-app/src/services/api/scanner.js`
- 新增：获取最近交易日API调用
- 修改：startScan方法支持end_date参数

### 配置变更
**路由注册**
- 文件：`/home/<USER>/workspace/quantization/app/api/router.py`
- 新增：trading端点路由注册

## 实施序列

### 第一阶段：后端API开发
1. **新增交易日API**
   - 创建`app/api/endpoints/trading.py`
   - 实现`get_latest_trading_date`端点
   - 在路由中注册新端点

2. **扩展扫描API**
   - 修改`app/schemas/scan.py`中的`ScanStartRequest`
   - 更新`app/api/endpoints/scan.py`中的`start_scan`方法
   - 确保end_date存储到task_config

3. **扫描结果API扩展**
   - 修改扫描结果响应包含截止日期信息
   - 更新`get_scan_results`端点返回end_date

### 第二阶段：前端组件开发
1. **日期选择器组件**
   - 创建`DatePicker.vue`组件
   - 集成ElementPlus日期选择器
   - 实现tooltip和默认值逻辑

2. **主扫描页面集成**
   - 在扫描页面添加日期选择器
   - 修改开始按钮为Icon按钮
   - 实现参数回显包含日期

3. **状态管理更新**
   - 扩展scanner store支持end_date
   - 实现配置持久化存储
   - 确保页面刷新后状态恢复

### 第三阶段：结果展示优化
1. **扫描结果页面**
   - 修改结果标题显示截止日期
   - 优化时间显示格式
   - 区分执行时间和数据截止时间

2. **API客户端完善**
   - 添加获取交易日API调用
   - 更新扫描API调用参数
   - 处理日期相关错误场景

## 验证计划

### 单元测试
- **交易日API测试**：验证最近交易日获取逻辑
- **扫描API测试**：验证end_date参数处理和存储
- **组件测试**：验证日期选择器组件功能

### 集成测试
- **端到端扫描流程**：从日期选择到结果展示的完整流程
- **参数回显测试**：页面刷新后配置完整恢复
- **多模式测试**：传统模式和多周期模式下的日期选择

### 业务逻辑验证
- **历史数据扫描**：选择历史日期进行实际扫描验证
- **结果时间显示**：确认执行时间和截止日期正确显示
- **用户体验测试**：日期选择器交互和tooltip显示验证

## 具体文件修改清单

### 新增文件
1. `/home/<USER>/workspace/quantization/app/api/endpoints/trading.py` - 交易日API端点
2. `/home/<USER>/workspace/quantization/frontend-app/src/components/scanner/DatePicker.vue` - 日期选择器组件

### 修改文件
1. `/home/<USER>/workspace/quantization/app/schemas/scan.py`
   - `ScanStartRequest`添加`end_date: Optional[str]`字段
   - `ScanResultResponse`添加`end_date: str`字段

2. `/home/<USER>/workspace/quantization/app/api/endpoints/scan.py`
   - `start_scan`方法处理end_date参数
   - `get_scan_results`方法返回end_date信息

3. `/home/<USER>/workspace/quantization/app/api/router.py`
   - 注册trading端点路由

4. `/home/<USER>/workspace/quantization/frontend-app/src/pages/Scanner/index.vue`
   - 集成日期选择器组件
   - 修改开始按钮为Icon按钮
   - 实现参数回显逻辑

5. `/home/<USER>/workspace/quantization/frontend-app/src/pages/Scanner/ScannerResults.vue`
   - 修改结果标题显示截止日期

6. `/home/<USER>/workspace/quantization/frontend-app/src/store/modules/scanner.js`
   - 扩展startScan方法支持end_date
   - 实现配置回显包含日期

7. `/home/<USER>/workspace/quantization/frontend-app/src/services/api/scanner.js`
   - 添加获取交易日API方法
   - 更新startScan API调用

### 技术约束和注意事项

1. **数据完整性**：确保历史日期的数据可用性验证
2. **性能考虑**：避免频繁的交易日API调用，考虑前端缓存
3. **用户体验**：日期选择器禁用非交易日，提供清晰的日期格式
4. **向后兼容**：确保不影响现有扫描功能，end_date为可选参数
5. **错误处理**：处理历史数据不存在或API失败的情况

通过以上技术规范的实施，将为系统增加完整的历史回测能力，满足用户对不同时间点数据分析的需求。