[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
include = ["app*"]
exclude = ["migrations*", "tests*", "scripts*", "doc*", "frontend-app*"]

[project]
name = "quantization"
version = "0.1.0"
description = "A high-performance stock quantitative analysis system"
readme = "README.md"
requires-python = ">=3.12"
license = "MIT"
dependencies = [
    "fastapi>=0.68.0",
    "uvicorn>=0.15.0",
    "sqlalchemy>=1.4.0",
    "aiosqlite>=0.17.0",
    "pandas>=1.3.0",
    "pandas-ta>=0.3.0",
    "numpy>=1.20.0,<2.0.0",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "email-validator>=2.0.0",
    "python-dotenv>=0.19.0",
    "alembic>=1.7.0",
    "apscheduler>=3.8.0",
    "aiohttp>=3.8.0",
    "tushare>=1.2.0",
    "akshare>=1.0.0",
    "yfinance>=0.1.0",
    "python-dateutil>=2.8.0",
    "passlib>=1.7.0",
    "setuptools>=45.0.0",
    "mypy>=1.17.0",
    "redis[hiredis]>=5.0.0",
]

[project.optional-dependencies]
mysql = [
    "pymysql>=1.0.0",
]
redis = [
    "redis>=4.0.0",
]
dev = [
    "pytest>=6.0.0",
    "pytest-asyncio>=0.16.0",
    "pytest-cov>=2.12.0",
    "black>=21.5b0",
    "isort>=5.9.0",
    "flake8>=3.9.0",
    "mypy>=0.910",
]

[tool.black]
line-length = 88
target-version = ["py38"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
