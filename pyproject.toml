[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
include = ["app*"]
exclude = ["migrations*", "tests*", "scripts*", "doc*", "frontend-app*"]

[project]
name = "quantization"
version = "0.1.0"
description = "A high-performance stock quantitative analysis system"
readme = "README.md"
requires-python = ">=3.12"
license = "MIT"
dependencies = [
    "fastapi>=0.68.0",
    "uvicorn>=0.15.0",
    "sqlalchemy>=1.4.0",
    "aiosqlite>=0.17.0",
    "pandas>=1.3.0",
    "pandas-ta>=0.3.0",
    "numpy>=1.20.0,<2.0.0",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "email-validator>=2.0.0",
    "python-dotenv>=0.19.0",
    "alembic>=1.7.0",
    "apscheduler>=3.8.0",
    "aiohttp>=3.12.15",
    "tushare>=1.2.0",
    "akshare>=1.14.24",
    "yfinance>=0.1.0",
    "python-dateutil>=2.8.0",
    "passlib>=1.7.0",
    "PyJWT>=2.8.0",
    "python-multipart>=0.0.6",
    "setuptools>=45.0.0",
    "mypy>=1.17.0",
    "redis[hiredis]>=5.0.0",
    "cachetools>=5.0.0",
    "croniter>=1.4.0",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
    "httpx>=0.24.0",
    "bcrypt<4.0",
    "greenlet>=3.2.4",
]

[project.optional-dependencies]
mysql = [
    "pymysql>=1.0.0",
]
redis = [
    "redis>=4.0.0",
]
dev = [
    "pytest>=6.0.0",
    "pytest-asyncio>=0.16.0",
    "pytest-cov>=2.12.0",
    "pytest-html>=3.1.0",
    "pytest-json-report>=1.5.0",
    "pytest-xdist>=2.5.0",
    "pytest-timeout>=2.1.0",
    "pytest-mock>=3.6.0",
    "pytest-benchmark>=4.0.0",
    "allure-pytest>=2.9.0",
    "black>=21.5b0",
    "isort>=5.9.0",
    "flake8>=3.9.0",
    "mypy>=0.910",
]

[tool.black]
line-length = 88
target-version = ["py312"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[tool.pytest.ini_options]
minversion = "6.0"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-report=xml:coverage.xml",
    "--html=reports/pytest_report.html",
    "--self-contained-html",
    "--json-report",
    "--json-report-file=reports/pytest_report.json",
    "--tb=short",
    "--maxfail=5",
    "--durations=10"
]
markers = [
    "unit: Unit tests",
    "integration: Integration tests", 
    "performance: Performance tests",
    "security: Security tests",
    "scenario: Scenario tests",
    "chaos: Chaos engineering tests",
    "recovery: Recovery tests",
    "stress: Stress tests",
    "load: Load tests",
    "slow: Slow running tests (deselect with '-m \"not slow\"')",
    "owasp: OWASP security tests",
    "owasp_a01_access_control: OWASP A01 - Broken Access Control",
    "owasp_a02_cryptographic_failures: OWASP A02 - Cryptographic Failures", 
    "owasp_a03_injection: OWASP A03 - Injection",
    "owasp_a07_authentication: OWASP A07 - Identification and Authentication Failures",
    "security_sql_injection: SQL Injection security tests",
    "security_xss_reflected: Reflected XSS security tests",
    "security_command_injection: Command Injection security tests",
    "security_path_traversal: Path Traversal security tests",
    "security_input_validation: Input Validation security tests",
    "security_json_parsing: JSON Parsing security tests",
    "security_data_sanitization: Data Sanitization security tests",
    "security_sql_character_escaping: SQL Character Escaping security tests",
    "security_brute_force: Brute Force security tests",
    "security_account_lockout: Account Lockout security tests", 
    "security_timing_attack: Timing Attack security tests",
    "security_token_entropy: Token Entropy security tests",
    "security_token_expiration: Token Expiration security tests",
    "security_data_at_rest: Data at Rest security tests",
    "security_transit_encryption: Transit Encryption security tests",
    "security_api_key_exposure: API Key Exposure security tests",
    "security_database_credentials: Database Credentials security tests",
    "security_pii_protection: PII Protection security tests",
    "security_data_masking: Data Masking security tests",
    "security_data_retention: Data Retention security tests",
    "security_http_headers: HTTP Headers security tests",
    "security_cors_configuration: CORS Configuration security tests",
    "security_content_type_validation: Content Type Validation security tests",
    "scenario_config_hot_reload: Configuration Hot Reload scenario tests",
    "scenario_routing_config_reload: Routing Config Reload scenario tests",
    "scenario_feature_toggle_reload: Feature Toggle Reload scenario tests",
    "scenario_worker_scaling: Worker Scaling scenario tests",
    "scenario_memory_scaling: Memory Scaling scenario tests",
    "scenario_graceful_startup: Graceful Startup scenario tests",
    "scenario_graceful_shutdown: Graceful Shutdown scenario tests",
    "chaos_random_failures: Random Failures chaos tests",
    "chaos_network_partition: Network Partition chaos tests",
    "recovery_database_connection_loss: Database Connection Loss recovery tests",
    "recovery_database_timeout: Database Timeout recovery tests", 
    "recovery_data_corruption: Data Corruption recovery tests",
    "recovery_redis_failure: Redis Failure recovery tests",
    "recovery_cache_memory_pressure: Cache Memory Pressure recovery tests",
    "recovery_data_provider_failover: Data Provider Failover recovery tests",
    "recovery_api_rate_limit: API Rate Limit recovery tests"
]
asyncio_mode = "auto"
timeout = 300
log_cli = true
log_cli_level = "INFO"
log_cli_format = "%(asctime)s [%(levelname)8s] %(name)s: %(message)s"
log_cli_date_format = "%Y-%m-%d %H:%M:%S"
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning"
]

[tool.coverage.run]
source = ["app"]
omit = [
    "*/migrations/*",
    "*/tests/*",
    "*/venv/*",
    "*/__pycache__/*",
    "*/site-packages/*"
]
branch = true

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod"
]
show_missing = true
skip_covered = false
precision = 2

[tool.coverage.html]
directory = "htmlcov"
