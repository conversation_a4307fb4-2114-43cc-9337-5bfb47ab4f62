"""
技术指标回测功能测试套件配置
"""
import pytest
from typing import Dict, Any


# 测试标记配置
pytest_plugins = ["pytest_asyncio"]

# 测试标记定义
def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line(
        "markers", "unit: 单元测试标记"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试标记"
    )
    config.addinivalue_line(
        "markers", "e2e: 端到端测试标记"
    )
    config.addinivalue_line(
        "markers", "performance: 性能测试标记"
    )
    config.addinivalue_line(
        "markers", "slow: 耗时较长的测试标记"
    )
    config.addinivalue_line(
        "markers", "backtest: 回测功能相关测试标记"
    )


# 测试收集配置
def pytest_collection_modifyitems(config, items):
    """修改测试收集配置"""
    # 为回测相关测试添加标记
    backtest_test_files = [
        "test_backtest_api.py",
        "test_date_picker_component.py", 
        "test_backtest_business_logic.py",
        "test_backtest_e2e.py",
        "test_frontend_integration.py",
        "test_backtest_performance.py"
    ]
    
    for item in items:
        # 添加回测标记
        if any(test_file in str(item.fspath) for test_file in backtest_test_files):
            item.add_marker(pytest.mark.backtest)
        
        # 添加层级标记
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        elif "performance" in str(item.fspath):
            item.add_marker(pytest.mark.performance)
        
        # 添加性能测试标记
        if "performance" in str(item.fspath) or "slow" in item.keywords:
            item.add_marker(pytest.mark.slow)


# 异步测试配置
@pytest.fixture(scope="session")
def event_loop():
    """创建异步事件循环"""
    import asyncio
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


# 数据库测试配置
@pytest.fixture(scope="session", autouse=True)
async def setup_test_database():
    """设置测试数据库"""
    from app.core.database import create_tables, drop_tables
    
    # 创建测试表
    await create_tables()
    
    yield
    
    # 清理测试表（可选）
    # await drop_tables()


# 回测功能特定配置
class BacktestTestConfig:
    """回测测试配置"""
    
    # 测试数据配置
    DEFAULT_TEST_STOCKS = ["000001", "000002", "600000"]
    DEFAULT_TEST_INDICATORS = ["kdj", "volume_pressure", "bollinger"]
    DEFAULT_TEST_END_DATE = "2024-01-15"
    
    # 性能测试阈值
    PERFORMANCE_THRESHOLDS = {
        "single_stock_scan": 5.0,  # 单股票扫描不超过5秒
        "batch_scan_per_stock": 0.5,  # 批量扫描每股票不超过0.5秒
        "api_response_time": 2.0,  # API响应时间不超过2秒
        "database_query_time": 1.0,  # 数据库查询不超过1秒
    }
    
    # 测试用户配置
    TEST_USER_CONFIG = {
        "username": "backtest_test_user",
        "email": "<EMAIL>",
        "password": "test_password_123"
    }


@pytest.fixture(scope="session")
def backtest_config():
    """回测测试配置fixture"""
    return BacktestTestConfig()


# 测试报告配置
def pytest_html_report_title(report):
    """自定义HTML报告标题"""
    report.title = "技术指标回测功能测试报告"


def pytest_html_results_summary(prefix, summary, postfix):
    """自定义HTML报告摘要"""
    prefix.extend([
        "<h2>回测功能测试摘要</h2>",
        "<p>本报告展示技术指标回测功能的完整测试结果</p>"
    ])


# 测试失败时的清理配置
@pytest.fixture(autouse=True)
async def cleanup_after_test():
    """测试后清理"""
    yield
    
    # 清理Redis缓存（如果使用）
    try:
        from app.core.cache import get_redis_client
        redis = get_redis_client()
        if redis:
            await redis.flushdb()
    except:
        pass  # 忽略Redis清理错误
    
    # 清理临时文件
    import tempfile
    import shutil
    temp_dir = tempfile.gettempdir()
    for item in ["test_backtest_*", "temp_scan_*"]:
        try:
            import glob
            for file in glob.glob(f"{temp_dir}/{item}"):
                if os.path.isfile(file):
                    os.remove(file)
                elif os.path.isdir(file):
                    shutil.rmtree(file)
        except:
            pass  # 忽略清理错误


# Mock配置
@pytest.fixture
def mock_external_apis():
    """模拟外部API"""
    from unittest.mock import patch
    
    # 模拟数据获取API
    with patch('app.services.data_fetcher.provider.tushare_provider.TushareProvider') as mock_tushare, \
         patch('app.services.data_fetcher.provider.akshare_provider.AkshareProvider') as mock_akshare, \
         patch('app.services.data_fetcher.provider.mairui_provider.MairuiProvider') as mock_mairui:
        
        # 配置模拟返回数据
        mock_data = [
            {
                'date': '2024-01-15',
                'open': 10.0,
                'high': 11.0,
                'low': 9.0,
                'close': 10.5,
                'volume': 1000000,
                'amount': 10500000.0
            }
        ]
        
        mock_tushare.return_value.get_stock_daily_data.return_value = mock_data
        mock_akshare.return_value.get_stock_daily_data.return_value = mock_data
        mock_mairui.return_value.get_stock_daily_data.return_value = mock_data
        
        yield {
            'tushare': mock_tushare,
            'akshare': mock_akshare,
            'mairui': mock_mairui
        }


# 环境变量配置
@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """设置测试环境变量"""
    import os
    
    # 设置测试环境变量
    os.environ['TESTING'] = 'True'
    os.environ['DATABASE_URL'] = 'sqlite:///:memory:'
    os.environ['REDIS_URL'] = 'redis://localhost:6379/15'  # 使用测试数据库
    os.environ['LOG_LEVEL'] = 'INFO'
    
    yield
    
    # 清理环境变量
    for key in ['TESTING', 'DATABASE_URL', 'REDIS_URL', 'LOG_LEVEL']:
        os.environ.pop(key, None)


# 日志配置
@pytest.fixture(scope="session", autouse=True)
def setup_test_logging():
    """设置测试日志"""
    import logging
    
    # 配置测试日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('tests/backtest_tests.log', mode='w')
        ]
    )
    
    # 设置特定模块日志级别
    logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)
    logging.getLogger('asyncio').setLevel(logging.WARNING)
    
    yield