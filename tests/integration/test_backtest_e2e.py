"""
技术指标回测功能 - 端到端集成测试
测试完整回测流程、参数回显功能、状态管理、扫描结果显示等
"""
import pytest
from httpx import AsyncClient
from fastapi import status
import asyncio
import json
from datetime import datetime, timedelta

from app.main import app
from app.core.database import db_session
from app.models.user import User
from app.models.task import TaskExecution
from app.schemas.scheduled_task import TaskStatus
from tests.utils.test_data_factory import TestDataFactory


class TestBacktestE2EIntegration:
    """回测功能端到端集成测试"""

    @pytest.fixture
    async def test_user(self):
        """创建测试用户"""
        async with db_session() as db:
            factory = TestDataFactory(db)
            user = await factory.create_test_user("e2e_backtest_user")
            yield user

    @pytest.fixture
    async def auth_headers(self, test_user):
        """获取认证头"""
        from app.core.auth.jwt_auth import create_access_token
        token = create_access_token(data={"sub": str(test_user.id)})
        return {"Authorization": f"Bearer {token}"}

    @pytest.mark.asyncio
    async def test_complete_backtest_workflow(self, auth_headers):
        """测试完整回测工作流程"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            # 1. 获取最近交易日
            trading_date_response = await ac.get("/api/v1/trading/latest-trading-date")
            assert trading_date_response.status_code == status.HTTP_200_OK
            trading_date = trading_date_response.json()["date"]
            
            # 2. 启动历史回测扫描
            backtest_date = "2024-01-15"
            scan_request = {
                "indicators": ["kdj", "volume_pressure"],
                "stock_codes": ["000001", "000002"],
                "scan_mode": "traditional",
                "periods": ["d"],
                "adjust": "n",
                "end_date": backtest_date
            }
            
            start_response = await ac.post(
                "/api/v1/scan/start",
                json=scan_request,
                headers=auth_headers
            )
            assert start_response.status_code == status.HTTP_200_OK
            
            start_data = start_response.json()
            task_id = start_data["task_id"]
            assert start_data["status"] in ["pending", "running"]
            
            # 3. 监控任务进度
            max_attempts = 10
            attempts = 0
            task_completed = False
            
            while attempts < max_attempts and not task_completed:
                await asyncio.sleep(1)  # 等待1秒
                
                progress_response = await ac.get(
                    f"/api/v1/scan/{task_id}/progress",
                    headers=auth_headers
                )
                
                if progress_response.status_code == status.HTTP_200_OK:
                    progress_data = progress_response.json()
                    
                    # 验证进度响应结构
                    assert "status" in progress_data
                    assert "total" in progress_data
                    assert "current" in progress_data
                    assert "percentage" in progress_data
                    
                    if progress_data["status"] in ["completed", "failed", "cancelled"]:
                        task_completed = True
                        break
                
                attempts += 1
            
            # 4. 获取任务详情（验证参数回显）
            task_response = await ac.get(
                f"/api/v1/scan/{task_id}",
                headers=auth_headers
            )
            assert task_response.status_code == status.HTTP_200_OK
            
            task_data = task_response.json()
            assert "end_date" in task_data
            assert task_data["end_date"] == backtest_date
            assert "indicators" in task_data
            assert set(task_data["indicators"]) == {"kdj", "volume_pressure"}
            
            # 5. 获取扫描结果
            results_response = await ac.get(
                f"/api/v1/scan/{task_id}/results",
                headers=auth_headers
            )
            
            if results_response.status_code == status.HTTP_200_OK:
                results_data = results_response.json()
                
                # 验证结果结构
                assert "task_id" in results_data
                assert "results" in results_data
                assert "end_date" in results_data
                assert results_data["end_date"] == backtest_date
                
                # 验证每个结果项
                if results_data["results"]:
                    for result in results_data["results"]:
                        assert "stock_code" in result
                        assert "stock_name" in result
                        assert "end_date" in result
                        assert result["end_date"] == backtest_date
                        assert "indicator_data" in result
                        assert "scan_time" in result

    @pytest.mark.asyncio
    async def test_multi_period_backtest_e2e(self, auth_headers):
        """测试多周期回测端到端流程"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            # 启动多周期回测
            scan_request = {
                "indicators": ["kdj", "volume_pressure"],
                "scan_mode": "multi_period",
                "scan_strategy": "parallel",
                "periods": ["d", "w"],
                "adjust": "n",
                "period_indicators": {
                    "d": ["kdj"],
                    "w": ["volume_pressure"]
                },
                "period_parameters": {
                    "d": {
                        "kdj": {"n": 20, "m1": 3, "m2": 3}
                    },
                    "w": {
                        "volume_pressure": {"ema_period": 10}
                    }
                },
                "end_date": "2024-01-15"
            }
            
            start_response = await ac.post(
                "/api/v1/scan/start",
                json=scan_request,
                headers=auth_headers
            )
            assert start_response.status_code == status.HTTP_200_OK
            
            task_id = start_response.json()["task_id"]
            
            # 验证多周期任务配置
            task_response = await ac.get(
                f"/api/v1/scan/{task_id}",
                headers=auth_headers
            )
            
            if task_response.status_code == status.HTTP_200_OK:
                task_data = task_response.json()
                assert task_data["end_date"] == "2024-01-15"

    @pytest.mark.asyncio
    async def test_parameter_echo_functionality(self, auth_headers):
        """测试参数回显功能完整性"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            # 使用复杂参数配置
            scan_request = {
                "indicators": ["kdj", "volume_pressure", "bollinger"],
                "stock_codes": ["000001", "000002", "600000"],
                "scan_mode": "traditional",
                "periods": ["d"],
                "adjust": "f",  # 前复权
                "parameters": {
                    "kdj": {"n": 25, "m1": 5, "m2": 5},
                    "volume_pressure": {"ema_period": 15},
                    "bollinger": {"window": 25, "std_dev": 2.5}
                },
                "end_date": "2024-01-10"
            }
            
            start_response = await ac.post(
                "/api/v1/scan/start",
                json=scan_request,
                headers=auth_headers
            )
            assert start_response.status_code == status.HTTP_200_OK
            
            task_id = start_response.json()["task_id"]
            
            # 验证所有参数被正确回显
            task_response = await ac.get(
                f"/api/v1/scan/{task_id}",
                headers=auth_headers
            )
            assert task_response.status_code == status.HTTP_200_OK
            
            task_data = task_response.json()
            
            # 验证基本参数
            assert task_data["end_date"] == "2024-01-10"
            assert set(task_data["indicators"]) == {"kdj", "volume_pressure", "bollinger"}
            
            # 获取活跃扫描列表也应该包含完整信息
            active_response = await ac.get(
                "/api/v1/scan/active",
                headers=auth_headers
            )
            assert active_response.status_code == status.HTTP_200_OK
            
            active_data = active_response.json()
            if active_data:
                latest_task = active_data[0]
                assert latest_task["end_date"] == "2024-01-10"

    @pytest.mark.asyncio
    async def test_status_management_throughout_lifecycle(self, auth_headers):
        """测试任务生命周期中的状态管理"""
        async with db_session() as db:
            # 监控任务状态变化
            status_history = []
            
            async with AsyncClient(app=app, base_url="http://test") as ac:
                # 启动任务
                scan_request = {
                    "indicators": ["kdj"],
                    "stock_codes": ["000001"],
                    "scan_mode": "traditional",
                    "periods": ["d"],
                    "adjust": "n",
                    "end_date": "2024-01-15"
                }
                
                start_response = await ac.post(
                    "/api/v1/scan/start",
                    json=scan_request,
                    headers=auth_headers
                )
                task_id = start_response.json()["task_id"]
                
                # 监控状态变化
                for _ in range(5):  # 监控5次
                    progress_response = await ac.get(
                        f"/api/v1/scan/{task_id}/progress",
                        headers=auth_headers
                    )
                    
                    if progress_response.status_code == status.HTTP_200_OK:
                        progress_data = progress_response.json()
                        status_history.append({
                            "status": progress_data["status"],
                            "percentage": progress_data.get("percentage", 0),
                            "timestamp": datetime.now()
                        })
                        
                        if progress_data["status"] in ["completed", "failed"]:
                            break
                    
                    await asyncio.sleep(1)
                
                # 验证状态变化合理性
                assert len(status_history) > 0
                
                # 验证状态流转（从pending/running到最终状态）
                initial_status = status_history[0]["status"]
                final_status = status_history[-1]["status"]
                
                assert initial_status in ["pending", "running"]
                assert final_status in ["completed", "failed", "cancelled", "running"]

    @pytest.mark.asyncio
    async def test_scan_results_display_integration(self, auth_headers):
        """测试扫描结果显示集成"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            scan_request = {
                "indicators": ["kdj", "volume_pressure"],
                "stock_codes": ["000001"],
                "scan_mode": "traditional",
                "periods": ["d"],
                "adjust": "n",
                "end_date": "2024-01-15"
            }
            
            start_response = await ac.post(
                "/api/v1/scan/start",
                json=scan_request,
                headers=auth_headers
            )
            task_id = start_response.json()["task_id"]
            
            # 测试分页结果显示
            results_response = await ac.get(
                f"/api/v1/scan/{task_id}/results?page=1&page_size=10",
                headers=auth_headers
            )
            
            if results_response.status_code == status.HTTP_200_OK:
                results_data = results_response.json()
                
                # 验证分页信息
                assert "page" in results_data
                assert "page_size" in results_data
                assert "total_count" in results_data
                assert results_data["page"] == 1
                assert results_data["page_size"] == 10
                
                # 验证结果显示完整性
                if results_data["results"]:
                    sample_result = results_data["results"][0]
                    
                    # 验证必要显示字段
                    required_fields = [
                        "stock_code", "stock_name", "signals", 
                        "indicator_data", "price", "change_percent",
                        "scan_time", "end_date"
                    ]
                    
                    for field in required_fields:
                        assert field in sample_result
                    
                    # 验证指标数据完整性
                    indicator_data = sample_result["indicator_data"]
                    indicator_fields = [
                        "kdj_k", "volume_pressure", "close_price"
                    ]
                    
                    for field in indicator_fields:
                        assert field in indicator_data

    @pytest.mark.asyncio
    async def test_concurrent_user_backtest_isolation(self):
        """测试并发用户回测隔离"""
        # 创建两个测试用户
        async with db_session() as db:
            factory = TestDataFactory(db)
            user1 = await factory.create_test_user("user1_backtest")
            user2 = await factory.create_test_user("user2_backtest")
        
        # 创建认证头
        from app.core.auth.jwt_auth import create_access_token
        headers1 = {"Authorization": f"Bearer {create_access_token(data={'sub': str(user1.id)})}"}
        headers2 = {"Authorization": f"Bearer {create_access_token(data={'sub': str(user2.id)})}"}
        
        async with AsyncClient(app=app, base_url="http://test") as ac:
            # 用户1启动回测
            scan_request1 = {
                "indicators": ["kdj"],
                "stock_codes": ["000001"],
                "scan_mode": "traditional",
                "periods": ["d"],
                "adjust": "n",
                "end_date": "2024-01-10"
            }
            
            # 用户2启动回测
            scan_request2 = {
                "indicators": ["volume_pressure"],
                "stock_codes": ["000002"],
                "scan_mode": "traditional",
                "periods": ["d"],
                "adjust": "n",
                "end_date": "2024-01-15"
            }
            
            # 并发启动
            response1, response2 = await asyncio.gather(
                ac.post("/api/v1/scan/start", json=scan_request1, headers=headers1),
                ac.post("/api/v1/scan/start", json=scan_request2, headers=headers2)
            )
            
            assert response1.status_code == status.HTTP_200_OK
            assert response2.status_code == status.HTTP_200_OK
            
            task_id1 = response1.json()["task_id"]
            task_id2 = response2.json()["task_id"]
            
            # 验证任务隔离
            assert task_id1 != task_id2
            
            # 验证用户只能访问自己的任务
            task_response1 = await ac.get(f"/api/v1/scan/{task_id1}", headers=headers1)
            task_response2 = await ac.get(f"/api/v1/scan/{task_id2}", headers=headers2)
            
            assert task_response1.status_code == status.HTTP_200_OK
            assert task_response2.status_code == status.HTTP_200_OK
            
            # 用户1不能访问用户2的任务
            cross_access = await ac.get(f"/api/v1/scan/{task_id2}", headers=headers1)
            assert cross_access.status_code == status.HTTP_404_NOT_FOUND

    @pytest.mark.asyncio
    async def test_error_recovery_and_handling(self, auth_headers):
        """测试错误恢复和处理"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            # 测试无效股票代码
            scan_request = {
                "indicators": ["kdj"],
                "stock_codes": ["INVALID123"],
                "scan_mode": "traditional",
                "periods": ["d"],
                "adjust": "n",
                "end_date": "2024-01-15"
            }
            
            start_response = await ac.post(
                "/api/v1/scan/start",
                json=scan_request,
                headers=auth_headers
            )
            
            # 任务应该能启动，但可能会失败
            if start_response.status_code == status.HTTP_200_OK:
                task_id = start_response.json()["task_id"]
                
                # 等待任务处理
                await asyncio.sleep(2)
                
                # 检查任务状态
                task_response = await ac.get(
                    f"/api/v1/scan/{task_id}",
                    headers=auth_headers
                )
                
                if task_response.status_code == status.HTTP_200_OK:
                    task_data = task_response.json()
                    
                    # 任务可能失败但应该有明确的错误信息
                    if "error_message" in task_data and task_data["error_message"]:
                        assert isinstance(task_data["error_message"], str)

    @pytest.mark.asyncio
    async def test_task_cancellation_e2e(self, auth_headers):
        """测试任务取消端到端流程"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            # 启动长时间运行的任务
            scan_request = {
                "indicators": ["kdj", "volume_pressure", "bollinger"],
                "stock_codes": ["000001", "000002", "600000", "000858"],  # 多个股票
                "scan_mode": "traditional",
                "periods": ["d"],
                "adjust": "n",
                "end_date": "2024-01-15"
            }
            
            start_response = await ac.post(
                "/api/v1/scan/start",
                json=scan_request,
                headers=auth_headers
            )
            assert start_response.status_code == status.HTTP_200_OK
            
            task_id = start_response.json()["task_id"]
            
            # 等待任务开始运行
            await asyncio.sleep(1)
            
            # 取消任务
            cancel_response = await ac.post(
                f"/api/v1/scan/{task_id}/stop",
                headers=auth_headers
            )
            
            if cancel_response.status_code == status.HTTP_200_OK:
                # 验证取消成功
                cancel_data = cancel_response.json()
                assert "message" in cancel_data
                
                # 验证任务状态更新
                await asyncio.sleep(1)
                task_response = await ac.get(
                    f"/api/v1/scan/{task_id}",
                    headers=auth_headers
                )
                
                if task_response.status_code == status.HTTP_200_OK:
                    task_data = task_response.json()
                    # 任务状态应该是已取消或已停止
                    assert task_data["status"] in ["cancelled", "failed", "completed"]

    @pytest.mark.asyncio 
    async def test_results_consistency_across_apis(self, auth_headers):
        """测试跨API结果一致性"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            scan_request = {
                "indicators": ["kdj"],
                "stock_codes": ["000001"],
                "scan_mode": "traditional", 
                "periods": ["d"],
                "adjust": "n",
                "end_date": "2024-01-15"
            }
            
            start_response = await ac.post(
                "/api/v1/scan/start",
                json=scan_request,
                headers=auth_headers
            )
            task_id = start_response.json()["task_id"]
            
            # 等待任务完成
            await asyncio.sleep(3)
            
            # 从多个API获取任务信息
            task_response = await ac.get(f"/api/v1/scan/{task_id}", headers=auth_headers)
            active_response = await ac.get("/api/v1/scan/active", headers=auth_headers)
            results_response = await ac.get(f"/api/v1/scan/{task_id}/results", headers=auth_headers)
            
            if all(r.status_code == status.HTTP_200_OK for r in [task_response, active_response, results_response]):
                task_data = task_response.json()
                active_data = active_response.json()
                results_data = results_response.json()
                
                # 验证end_date在所有API中一致
                assert task_data["end_date"] == "2024-01-15"
                
                if active_data:
                    assert active_data[0]["end_date"] == "2024-01-15"
                
                assert results_data["end_date"] == "2024-01-15"