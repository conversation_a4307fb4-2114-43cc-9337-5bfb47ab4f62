"""端到端定时任务功能测试"""

import pytest
import asyncio
import json
from datetime import datetime, timedelta
from unittest.mock import patch, Mock, AsyncMock
from sqlalchemy.orm import Session

from app.services.tasks.scheduler import TaskScheduler, get_task_scheduler
from app.services.tasks.executor import TaskExecutor, get_task_executor
from app.models.user import User
from app.models.task import UserScheduledTask, TaskExecution
from app.schemas.scheduled_task import TaskType, TaskStatus, TriggerType, IndicatorScanConfig


class TestEndToEndScheduledTasks:
    """端到端定时任务功能测试"""

    @pytest.mark.asyncio
    async def test_complete_task_lifecycle(self, mock_user, mock_indicator_config, mock_memory_scanner, mock_session_manager, scan_result_samples):
        """测试完整的任务生命周期"""
        # 1. 创建定时任务
        scheduler = get_task_scheduler()
        executor = get_task_executor()
        
        # 模拟创建的定时任务
        scheduled_task = UserScheduledTask(
            id=1,
            user_id=mock_user.id,
            name="端到端测试任务",
            task_type=TaskType.INDICATOR_SCAN,
            cron_expression="*/5 * * * *",  # 每5分钟执行一次
            task_config=mock_indicator_config.json(),
            description="端到端测试任务",
            is_active=True,
            max_executions=None,
            current_executions=0,
            next_execution=datetime.now() - timedelta(minutes=1)  # 设置为过去的时间，应该立即执行
        )
        
        # 2. 模拟调度器检查并执行任务
        with patch('app.services.tasks.scheduler.db_session') as mock_scheduler_db:
            with patch('app.services.tasks.executor.db_session') as mock_executor_db:
                # 调度器的数据库操作模拟
                scheduler_db_session = Mock()
                scheduler_query = Mock()
                scheduler_query.filter.return_value = scheduler_query
                scheduler_query.limit.return_value = scheduler_query
                scheduler_query.all.return_value = [scheduled_task]
                scheduler_query.count.return_value = 0  # 用户无活跃任务
                scheduler_db_session.query.return_value = scheduler_query
                
                mock_scheduler_db.return_value.__aenter__ = AsyncMock(return_value=scheduler_db_session)
                mock_scheduler_db.return_value.__aexit__ = AsyncMock(return_value=None)
                
                # 执行器的数据库操作模拟
                executor_db_session = Mock()
                created_execution = TaskExecution(
                    id=1,
                    user_id=mock_user.id,
                    scheduled_task_id=scheduled_task.id,
                    trigger_type=TriggerType.SCHEDULED,
                    task_type=TaskType.INDICATOR_SCAN,
                    task_config=scheduled_task.task_config,
                    status=TaskStatus.PENDING
                )
                executor_db_session.query.return_value.get.return_value = created_execution
                
                mock_executor_db.return_value.__aenter__ = AsyncMock(return_value=executor_db_session)
                mock_executor_db.return_value.__aexit__ = AsyncMock(return_value=None)
                
                # 3. 模拟扫描器和会话管理器
                with patch('app.services.tasks.scheduler.get_memory_scanner', return_value=mock_memory_scanner):
                    with patch('app.services.tasks.scheduler.get_session_manager', return_value=mock_session_manager):
                        with patch('app.services.tasks.executor.get_memory_scanner', return_value=mock_memory_scanner):
                            with patch('app.services.tasks.executor.get_session_manager', return_value=mock_session_manager):
                                
                                # 模拟扫描成功完成
                                mock_scan_task = Mock()
                                mock_scan_task.status.value = "completed"
                                mock_scan_task.results = [Mock(dict=Mock(return_value=result)) for result in scan_result_samples]
                                mock_session_manager.get_scan_task.return_value = mock_scan_task
                                
                                # 4. 执行调度检查
                                await scheduler._check_and_execute_tasks()
                                
                                # 5. 等待任务完成（模拟）
                                # 在实际测试中，我们需要等待异步任务完成
                                if scheduler._active_executions:
                                    await asyncio.sleep(0.1)  # 给异步任务一些执行时间
                                
                                # 6. 验证结果
                                # 验证任务被调度执行
                                assert len(scheduler._running_task_ids) <= 1  # 任务可能已完成
                                
                                # 验证扫描器被调用
                                mock_memory_scanner.start_scan.assert_called()
                                
                                # 验证会话管理
                                mock_session_manager.get_scan_task.assert_called()

    @pytest.mark.asyncio
    async def test_concurrent_task_execution(self, mock_user, mock_indicator_config, mock_memory_scanner, mock_session_manager):
        """测试并发任务执行"""
        scheduler = get_task_scheduler()
        
        # 创建多个定时任务
        tasks = []
        for i in range(5):
            task = UserScheduledTask(
                id=i + 1,
                user_id=mock_user.id,
                name=f"并发测试任务{i+1}",
                task_type=TaskType.INDICATOR_SCAN,
                cron_expression="*/5 * * * *",
                task_config=mock_indicator_config.json(),
                is_active=True,
                max_executions=None,
                current_executions=0,
                next_execution=datetime.now() - timedelta(minutes=1)
            )
            tasks.append(task)
        
        with patch('app.services.tasks.scheduler.db_session') as mock_db:
            with patch('app.services.tasks.executor.db_session') as mock_executor_db:
                # 模拟数据库返回所有任务
                db_session = Mock()
                query = Mock()
                query.filter.return_value = query
                query.limit.return_value = query
                query.all.return_value = tasks
                query.count.return_value = 0  # 用户无活跃任务
                db_session.query.return_value = query
                
                mock_db.return_value.__aenter__ = AsyncMock(return_value=db_session)
                mock_db.return_value.__aexit__ = AsyncMock(return_value=None)
                
                # 模拟执行器数据库操作
                executor_db_session = Mock()
                executor_db_session.query.return_value.get.side_effect = lambda exec_id: Mock(
                    id=exec_id,
                    task_type=TaskType.INDICATOR_SCAN,
                    task_config=mock_indicator_config.json()
                )
                
                mock_executor_db.return_value.__aenter__ = AsyncMock(return_value=executor_db_session)
                mock_executor_db.return_value.__aexit__ = AsyncMock(return_value=None)
                
                with patch('app.services.tasks.scheduler.get_memory_scanner', return_value=mock_memory_scanner):
                    with patch('app.services.tasks.scheduler.get_session_manager', return_value=mock_session_manager):
                        with patch('app.services.tasks.executor.get_memory_scanner', return_value=mock_memory_scanner):
                            with patch('app.services.tasks.executor.get_session_manager', return_value=mock_session_manager):
                                
                                # 模拟扫描快速完成
                                mock_scan_task = Mock()
                                mock_scan_task.status.value = "completed"
                                mock_scan_task.results = []
                                mock_session_manager.get_scan_task.return_value = mock_scan_task
                                
                                start_time = datetime.now()
                                
                                # 执行调度
                                await scheduler._check_and_execute_tasks()
                                
                                end_time = datetime.now()
                                execution_time = (end_time - start_time).total_seconds()
                                
                                # 验证并发执行性能（应该很快完成，因为是并发的）
                                assert execution_time < 2.0  # 应该在2秒内完成
                                
                                # 验证调度了适当数量的任务（受并发限制）
                                expected_scheduled = min(len(tasks), scheduler._max_concurrent_tasks)
                                assert len(scheduler._running_task_ids) <= expected_scheduled

    @pytest.mark.asyncio
    async def test_task_failure_recovery(self, mock_user, mock_indicator_config, mock_memory_scanner, mock_session_manager):
        """测试任务失败恢复机制"""
        scheduler = get_task_scheduler()
        
        failed_task = UserScheduledTask(
            id=1,
            user_id=mock_user.id,
            name="失败测试任务",
            task_type=TaskType.INDICATOR_SCAN,
            cron_expression="*/5 * * * *",
            task_config=mock_indicator_config.json(),
            is_active=True,
            max_executions=None,
            current_executions=0,
            next_execution=datetime.now() - timedelta(minutes=1)
        )
        
        with patch('app.services.tasks.scheduler.db_session') as mock_db:
            with patch('app.services.tasks.executor.db_session') as mock_executor_db:
                # 调度器数据库模拟
                scheduler_db_session = Mock()
                query = Mock()
                query.filter.return_value = query
                query.limit.return_value = query
                query.all.return_value = [failed_task]
                query.count.return_value = 0
                scheduler_db_session.query.return_value = query
                
                mock_db.return_value.__aenter__ = AsyncMock(return_value=scheduler_db_session)
                mock_db.return_value.__aexit__ = AsyncMock(return_value=None)
                
                # 执行器数据库模拟
                executor_db_session = Mock()
                failed_execution = Mock()
                failed_execution.id = 1
                failed_execution.task_type = TaskType.INDICATOR_SCAN
                failed_execution.task_config = mock_indicator_config.json()
                executor_db_session.query.return_value.get.return_value = failed_execution
                
                mock_executor_db.return_value.__aenter__ = AsyncMock(return_value=executor_db_session)
                mock_executor_db.return_value.__aexit__ = AsyncMock(return_value=None)
                
                with patch('app.services.tasks.scheduler.get_memory_scanner', return_value=mock_memory_scanner):
                    with patch('app.services.tasks.scheduler.get_session_manager', return_value=mock_session_manager):
                        with patch('app.services.tasks.executor.get_memory_scanner', return_value=mock_memory_scanner):
                            with patch('app.services.tasks.executor.get_session_manager', return_value=mock_session_manager):
                                
                                # 模拟扫描失败
                                mock_scan_task = Mock()
                                mock_scan_task.status.value = "failed"
                                mock_scan_task.error_message = "扫描器内部错误"
                                mock_session_manager.get_scan_task.return_value = mock_scan_task
                                
                                # 执行调度
                                await scheduler._check_and_execute_tasks()
                                
                                # 等待异步任务完成
                                await asyncio.sleep(0.1)
                                
                                # 验证失败处理
                                # 任务应该被标记为失败，但调度器应该继续运行
                                assert failed_execution.status == TaskStatus.FAILED
                                assert "扫描失败" in failed_execution.error_message

    @pytest.mark.asyncio
    async def test_max_executions_enforcement(self, mock_user, mock_indicator_config):
        """测试最大执行次数强制执行"""
        scheduler = get_task_scheduler()
        
        # 创建已达最大执行次数的任务
        limited_task = UserScheduledTask(
            id=1,
            user_id=mock_user.id,
            name="限制执行次数任务",
            task_type=TaskType.INDICATOR_SCAN,
            cron_expression="*/5 * * * *",
            task_config=mock_indicator_config.json(),
            is_active=True,
            max_executions=5,
            current_executions=5,  # 已达限制
            next_execution=datetime.now() - timedelta(minutes=1)
        )
        
        with patch('app.services.tasks.scheduler.db_session') as mock_db:
            db_session = Mock()
            query = Mock()
            query.filter.return_value = query
            query.limit.return_value = query
            query.all.return_value = [limited_task]
            db_session.query.return_value = query
            
            mock_db.return_value.__aenter__ = AsyncMock(return_value=db_session)
            mock_db.return_value.__aexit__ = AsyncMock(return_value=None)
            
            # 执行调度
            await scheduler._check_and_execute_tasks()
            
            # 验证任务被停用
            assert limited_task.is_active is False
            db_session.commit.assert_called()

    @pytest.mark.asyncio
    async def test_scheduler_error_resilience(self, mock_user, mock_indicator_config):
        """测试调度器错误恢复能力"""
        scheduler = get_task_scheduler()
        
        # 模拟数据库连接失败
        error_count = 0
        async def mock_db_context():
            nonlocal error_count
            error_count += 1
            if error_count <= 2:  # 前两次失败
                raise Exception("数据库连接失败")
            else:  # 第三次成功
                mock_db = Mock()
                mock_query = Mock()
                mock_query.filter.return_value = mock_query
                mock_query.limit.return_value = mock_query
                mock_query.all.return_value = []  # 无任务
                mock_db.query.return_value = mock_query
                return mock_db
        
        with patch('app.services.tasks.scheduler.db_session') as mock_db_patch:
            mock_db_patch.return_value.__aenter__ = mock_db_context
            mock_db_patch.return_value.__aexit__ = AsyncMock(return_value=None)
            
            # 连续执行调度检查
            for i in range(3):
                try:
                    await scheduler._check_and_execute_tasks()
                except Exception:
                    # 前两次应该失败，第三次应该成功
                    if i >= 2:
                        pytest.fail("调度器在错误恢复后仍然失败")
                
                # 短暂等待
                await asyncio.sleep(0.01)
            
            # 验证调度器最终恢复正常
            assert error_count == 3

    @pytest.mark.asyncio
    async def test_user_task_limits_enforcement(self, mock_user, mock_indicator_config):
        """测试用户任务限制强制执行"""
        scheduler = get_task_scheduler()
        
        # 创建一个正常任务
        task = UserScheduledTask(
            id=1,
            user_id=mock_user.id,
            name="用户限制测试任务",
            task_type=TaskType.INDICATOR_SCAN,
            cron_expression="*/5 * * * *",
            task_config=mock_indicator_config.json(),
            is_active=True,
            max_executions=None,
            current_executions=0,
            next_execution=datetime.now() - timedelta(minutes=1)
        )
        
        with patch('app.services.tasks.scheduler.db_session') as mock_db:
            db_session = Mock()
            
            # 设置查询模拟
            def mock_query_side_effect(model):
                query_mock = Mock()
                
                if model == UserScheduledTask:
                    # 任务查询
                    query_mock.filter.return_value = query_mock
                    query_mock.limit.return_value = query_mock
                    query_mock.all.return_value = [task]
                    return query_mock
                elif model == TaskExecution:
                    # 执行记录查询（模拟用户已有5个活跃任务）
                    query_mock.filter.return_value = query_mock
                    query_mock.count.return_value = 5  # 达到用户限制
                    return query_mock
                
                return query_mock
            
            db_session.query.side_effect = mock_query_side_effect
            
            mock_db.return_value.__aenter__ = AsyncMock(return_value=db_session)
            mock_db.return_value.__aexit__ = AsyncMock(return_value=None)
            
            # 执行调度
            await scheduler._check_and_execute_tasks()
            
            # 验证任务因用户限制而未被执行
            assert task.id not in scheduler._running_task_ids

    @pytest.mark.asyncio
    async def test_manual_execution_integration(self, mock_user, mock_scheduled_task, mock_memory_scanner, mock_session_manager, scan_result_samples):
        """测试手动执行集成"""
        executor = get_task_executor()
        
        # 创建手动执行记录
        manual_execution = TaskExecution(
            id=1,
            user_id=mock_user.id,
            scheduled_task_id=mock_scheduled_task.id,
            trigger_type=TriggerType.MANUAL,
            task_type=TaskType.INDICATOR_SCAN,
            task_config=mock_scheduled_task.task_config,
            status=TaskStatus.PENDING
        )
        
        with patch('app.services.tasks.executor.db_session') as mock_db:
            db_session = Mock()
            db_session.query.return_value.get.return_value = manual_execution
            
            mock_db.return_value.__aenter__ = AsyncMock(return_value=db_session)
            mock_db.return_value.__aexit__ = AsyncMock(return_value=None)
            
            with patch('app.services.tasks.executor.get_memory_scanner', return_value=mock_memory_scanner):
                with patch('app.services.tasks.executor.get_session_manager', return_value=mock_session_manager):
                    
                    # 模拟扫描成功
                    mock_scan_task = Mock()
                    mock_scan_task.status.value = "completed"
                    mock_scan_task.results = [Mock(dict=Mock(return_value=result)) for result in scan_result_samples]
                    mock_session_manager.get_scan_task.return_value = mock_scan_task
                    
                    # 执行手动任务
                    execution_task = await executor.execute_task(manual_execution.id)
                    
                    # 验证任务被添加到活跃执行列表
                    assert manual_execution.id in executor._active_executions
                    
                    # 等待执行完成
                    if execution_task:
                        await execution_task
                    
                    # 验证执行状态
                    assert manual_execution.status == TaskStatus.COMPLETED
                    assert manual_execution.results_count == len(scan_result_samples)
                    assert manual_execution.trigger_type == TriggerType.MANUAL


class TestIntegrationStressTests:
    """集成压力测试"""

    @pytest.mark.asyncio
    async def test_high_frequency_scheduling(self, mock_user, mock_indicator_config, mock_memory_scanner, mock_session_manager):
        """测试高频调度"""
        scheduler = get_task_scheduler()
        
        # 创建多个高频任务
        tasks = []
        for i in range(20):
            task = UserScheduledTask(
                id=i + 1,
                user_id=mock_user.id,
                name=f"高频任务{i+1}",
                task_type=TaskType.INDICATOR_SCAN,
                cron_expression="* * * * *",  # 每分钟执行
                task_config=mock_indicator_config.json(),
                is_active=True,
                next_execution=datetime.now() - timedelta(seconds=i)  # 错开执行时间
            )
            tasks.append(task)
        
        with patch('app.services.tasks.scheduler.db_session') as mock_db:
            with patch('app.services.tasks.executor.db_session') as mock_executor_db:
                # 调度器数据库模拟
                scheduler_db = Mock()
                query = Mock()
                query.filter.return_value = query
                query.limit.return_value = query
                query.all.return_value = tasks
                query.count.return_value = 0
                scheduler_db.query.return_value = query
                
                mock_db.return_value.__aenter__ = AsyncMock(return_value=scheduler_db)
                mock_db.return_value.__aexit__ = AsyncMock(return_value=None)
                
                # 执行器数据库模拟
                executor_db = Mock()
                executor_db.query.return_value.get.side_effect = lambda exec_id: Mock(
                    id=exec_id,
                    task_type=TaskType.INDICATOR_SCAN,
                    task_config=mock_indicator_config.json()
                )
                
                mock_executor_db.return_value.__aenter__ = AsyncMock(return_value=executor_db)
                mock_executor_db.return_value.__aexit__ = AsyncMock(return_value=None)
                
                with patch('app.services.tasks.scheduler.get_memory_scanner', return_value=mock_memory_scanner):
                    with patch('app.services.tasks.scheduler.get_session_manager', return_value=mock_session_manager):
                        with patch('app.services.tasks.executor.get_memory_scanner', return_value=mock_memory_scanner):
                            with patch('app.services.tasks.executor.get_session_manager', return_value=mock_session_manager):
                                
                                # 模拟快速扫描完成
                                mock_scan_task = Mock()
                                mock_scan_task.status.value = "completed"
                                mock_scan_task.results = []
                                mock_session_manager.get_scan_task.return_value = mock_scan_task
                                
                                start_time = datetime.now()
                                
                                # 连续执行多次调度
                                for _ in range(5):
                                    await scheduler._check_and_execute_tasks()
                                    await asyncio.sleep(0.01)  # 短暂等待
                                
                                end_time = datetime.now()
                                total_time = (end_time - start_time).total_seconds()
                                
                                # 验证性能（高频调度应该能在合理时间内完成）
                                assert total_time < 3.0  # 3秒内完成
                                
                                # 验证并发限制生效
                                assert len(scheduler._active_executions) <= scheduler._max_concurrent_tasks

    @pytest.mark.asyncio
    async def test_memory_management_under_load(self, mock_user, mock_indicator_config):
        """测试负载下的内存管理"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        scheduler = get_task_scheduler()
        executor = get_task_executor()
        
        # 创建大量任务执行
        for i in range(100):
            # 模拟执行记录
            execution_id = i + 1
            task_id = i + 1
            
            # 添加到活跃列表然后立即清理（模拟快速完成的任务）
            scheduler._active_executions[execution_id] = Mock()
            scheduler._running_task_ids.add(task_id)
            executor._active_executions[execution_id] = Mock()
            
            # 立即清理（模拟任务完成）
            scheduler._cleanup_task(execution_id, task_id)
            executor._active_executions.pop(execution_id, None)
        
        # 检查内存使用
        current_memory = process.memory_info().rss
        memory_increase = current_memory - initial_memory
        
        # 内存增长应该在合理范围内
        assert memory_increase < 50 * 1024 * 1024  # 50MB限制
        
        # 验证所有任务都被清理
        assert len(scheduler._active_executions) == 0
        assert len(scheduler._running_task_ids) == 0
        assert len(executor._active_executions) == 0