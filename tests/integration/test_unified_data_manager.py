"""
集成测试增强 - 多级缓存完整链路测试

验证统一数据管理系统的完整集成流程，包括多级缓存链路
"""
import pytest
import asyncio
import time
import redis.asyncio as redis
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch, MagicMock

from app.core.data.manager import UnifiedDataManager
from app.core.data.models import (
    DataManagerConfig,
    CacheConfig,
    RoutingConfig,
    DataRequest,
    DataType
)


class TestMultiLevelCacheIntegration:
    """多级缓存集成测试"""
    
    @pytest.mark.asyncio
    async def test_complete_cache_chain_integration(
        self,
        redis_client: redis.Redis
    ):
        """测试完整的多级缓存链路"""
        # 配置多级缓存
        config = DataManagerConfig(
            cache=CacheConfig(
                enabled=True,
                default_ttl=3600,
                max_memory_mb=50,
                l1_cache_enabled=True,  # 内存缓存
                l2_cache_enabled=True,  # Redis缓存
                compression_enabled=True
            )
        )
        
        manager = UnifiedDataManager(config)
        await manager.start()
        
        try:
            stock_code = "000001"
            cache_hit_stats = {
                'l1_hits': 0,
                'l2_hits': 0,  
                'cache_misses': 0
            }
            
            # 阶段1: 冷启动 - 应该从数据源获取
            print("\\n=== 阶段1: 冷启动测试 ===")
            start_time = time.perf_counter()
            result1 = await manager.get_stock_info(code=stock_code)
            cold_start_time = time.perf_counter() - start_time
            
            assert result1 is not None, "冷启动应该成功获取数据"
            cache_hit_stats['cache_misses'] += 1
            
            # 阶段2: L1缓存命中测试
            print("=== 阶段2: L1缓存命中测试 ===")
            start_time = time.perf_counter()
            result2 = await manager.get_stock_info(code=stock_code)
            l1_cache_time = time.perf_counter() - start_time
            
            assert result2 is not None, "L1缓存应该命中"
            assert l1_cache_time < cold_start_time * 0.1, "L1缓存应该显著更快"
            cache_hit_stats['l1_hits'] += 1
            
            # 阶段3: 清除L1缓存，测试L2缓存
            print("=== 阶段3: L2缓存命中测试 ===")
            if hasattr(manager, 'clear_l1_cache'):
                await manager.clear_l1_cache()
            
            start_time = time.perf_counter()
            result3 = await manager.get_stock_info(code=stock_code)
            l2_cache_time = time.perf_counter() - start_time
            
            assert result3 is not None, "L2缓存应该命中"
            assert l2_cache_time < cold_start_time * 0.5, "L2缓存应该比冷启动快"
            assert l2_cache_time > l1_cache_time, "L2缓存应该比L1缓存慢"
            cache_hit_stats['l2_hits'] += 1
            
            # 阶段4: 缓存预热测试
            print("=== 阶段4: 缓存预热测试 ===")
            warmup_codes = [f"{i:06d}" for i in range(2, 12)]  # 10个股票代码
            
            warmup_start = time.perf_counter()
            warmup_results = await manager.warmup_cache(
                stock_codes=warmup_codes,
                data_types=['basic_info', 'daily_data']
            )
            warmup_time = time.perf_counter() - warmup_start
            
            assert len(warmup_results) > 0, "缓存预热应该返回结果"
            
            # 验证预热后的缓存命中
            for code in warmup_codes[:3]:  # 测试前3个
                start_time = time.perf_counter()
                result = await manager.get_stock_info(code=code)
                hit_time = time.perf_counter() - start_time
                
                assert result is not None, f"预热后应该能获取 {code} 的数据"
                assert hit_time < 0.01, f"预热后访问 {code} 应该很快"
            
            # 性能断言
            assert cold_start_time > l2_cache_time > l1_cache_time, "缓存层级性能递增"
            assert l1_cache_time < 0.005, "L1缓存响应时间应该 < 5ms"
            assert l2_cache_time < 0.02, "L2缓存响应时间应该 < 20ms"
            assert cold_start_time < 0.1, "冷启动响应时间应该 < 100ms"
            
            print(f"\\n缓存性能统计:")
            print(f"冷启动时间: {cold_start_time*1000:.2f}ms")
            print(f"L1缓存时间: {l1_cache_time*1000:.2f}ms")
            print(f"L2缓存时间: {l2_cache_time*1000:.2f}ms")
            print(f"缓存预热时间: {warmup_time:.2f}s (10个股票)")
            print(f"缓存统计: {cache_hit_stats}")
            
        finally:
            await manager.shutdown()
    
    @pytest.mark.asyncio
    async def test_cache_consistency_across_levels(
        self,
        redis_client: redis.Redis
    ):
        """测试多级缓存的数据一致性"""
        config = DataManagerConfig(
            cache=CacheConfig(
                enabled=True,
                default_ttl=300,  # 5分钟TTL
                consistency_check_enabled=True
            )
        )
        
        manager = UnifiedDataManager(config)
        await manager.start()
        
        try:
            stock_code = "000002"
            
            # 第一次获取数据，填充所有缓存层
            original_data = await manager.get_stock_info(code=stock_code)
            
            # 模拟数据更新（绕过缓存直接更新数据源）
            updated_data = {
                **original_data.__dict__ if hasattr(original_data, '__dict__') else original_data,
                'last_updated': datetime.now(),
                'version': 2
            }
            
            # 使用强制刷新来更新缓存
            if hasattr(manager, 'refresh_cache'):
                await manager.refresh_cache(
                    cache_key=f"stock_info:{stock_code}",
                    new_data=updated_data
                )
            
            # 验证所有缓存层都已更新
            for _ in range(5):  # 多次检查确保一致性
                result = await manager.get_stock_info(code=stock_code)
                
                # 检查数据是否为最新版本
                if hasattr(result, 'version'):
                    assert result.version == 2, f"缓存数据不一致，版本应该是2，实际是{result.version}"
                
                # 短暂休息后再次检查
                await asyncio.sleep(0.1)
            
            print(f"\\n缓存一致性测试通过:")
            print(f"股票代码: {stock_code}")
            print(f"数据版本: {getattr(result, 'version', 'N/A')}")
            
        finally:
            await manager.shutdown()
    
    @pytest.mark.asyncio
    async def test_cache_eviction_and_replacement(
        self,
        redis_client: redis.Redis
    ):
        """测试缓存淘汰和替换策略"""
        config = DataManagerConfig(
            cache=CacheConfig(
                enabled=True,
                max_memory_mb=10,  # 限制缓存大小触发淘汰
                eviction_policy="lru",
                default_ttl=3600
            )
        )
        
        manager = UnifiedDataManager(config)
        await manager.start()
        
        try:
            # 填充缓存直到触发淘汰
            cached_codes = []
            
            for i in range(100):  # 缓存100个股票信息
                code = f"{i+1:06d}"
                cached_codes.append(code)
                
                result = await manager.get_stock_info(code=code)
                assert result is not None, f"应该能获取股票 {code} 的信息"
                
                # 每10个检查一次缓存状态
                if (i + 1) % 10 == 0:
                    cache_stats = await manager.get_cache_statistics()
                    print(f"缓存了 {i+1} 个股票，缓存统计: {cache_stats}")
            
            # 访问最早缓存的股票，测试LRU是否生效
            early_codes = cached_codes[:10]
            late_codes = cached_codes[-10:]
            
            early_access_times = []
            late_access_times = []
            
            # 测试早期缓存的股票（可能已被淘汰）
            for code in early_codes:
                start_time = time.perf_counter()
                result = await manager.get_stock_info(code=code)
                access_time = time.perf_counter() - start_time
                early_access_times.append(access_time)
            
            # 测试最近缓存的股票（应该仍在缓存中）
            for code in late_codes:
                start_time = time.perf_counter()
                result = await manager.get_stock_info(code=code)
                access_time = time.perf_counter() - start_time
                late_access_times.append(access_time)
            
            # 分析访问时间差异
            avg_early_time = sum(early_access_times) / len(early_access_times)
            avg_late_time = sum(late_access_times) / len(late_access_times)
            
            print(f"\\n缓存淘汰测试结果:")
            print(f"早期股票平均访问时间: {avg_early_time*1000:.2f}ms")
            print(f"最近股票平均访问时间: {avg_late_time*1000:.2f}ms")
            
            # 最近的股票应该访问更快（仍在缓存中）
            if avg_early_time > avg_late_time * 2:
                print("✓ LRU淘汰策略正常工作")
            
        finally:
            await manager.shutdown()


class TestDataSourceIntegration:
    """数据源集成测试"""
    
    @pytest.mark.asyncio
    async def test_multi_data_source_failover(
        self
    ):
        """测试多数据源故障转移"""
        # 配置多个数据源
        config = DataManagerConfig(
            routing=RoutingConfig(
                primary_source="mairui",
                fallback_sources=["tushare", "akshare"],
                failover_enabled=True,
                circuit_breaker_enabled=True
            )
        )
        
        manager = UnifiedDataManager(config)
        
        # 模拟主数据源故障
        with patch.object(manager.router, '_get_data_source') as mock_get_source:
            # 第一个数据源失败
            failing_source = AsyncMock()
            failing_source.get_stock_info.side_effect = Exception("Primary source failed")
            
            # 第二个数据源成功
            working_source = AsyncMock()
            expected_data = {
                'code': '000001',
                'name': '测试股票',
                'market': 'SZ'
            }
            working_source.get_stock_info.return_value = expected_data
            
            # 配置数据源返回
            mock_get_source.side_effect = [failing_source, working_source]
            
            await manager.start()
            
            try:
                # 请求数据，应该自动故障转移
                result = await manager.get_stock_info(code="000001")
                
                assert result is not None, "故障转移应该成功"
                assert result.get('code') == '000001', "应该返回正确的数据"
                
                # 验证故障转移统计
                stats = await manager.get_routing_statistics()
                assert stats.get('failover_count', 0) > 0, "应该记录故障转移次数"
                
                print(f"\\n故障转移测试结果:")
                print(f"成功获取数据: {result}")
                print(f"路由统计: {stats}")
                
            finally:
                await manager.shutdown()
    
    @pytest.mark.asyncio  
    async def test_data_source_load_balancing(
        self
    ):
        """测试数据源负载均衡"""
        config = DataManagerConfig(
            routing=RoutingConfig(
                load_balancing_enabled=True,
                available_sources=["mairui", "tushare", "akshare"],
                load_balancing_strategy="round_robin"
            )
        )
        
        manager = UnifiedDataManager(config)
        
        # 模拟多个数据源
        source_call_counts = {"mairui": 0, "tushare": 0, "akshare": 0}
        
        def mock_source_factory(source_name):
            source = AsyncMock()
            
            async def mock_get_stock_info(*args, **kwargs):
                source_call_counts[source_name] += 1
                return {
                    'code': '000001',
                    'name': f'数据来源{source_name}',
                    'source': source_name
                }
            
            source.get_stock_info = mock_get_stock_info
            return source
        
        with patch.object(manager.router, '_get_data_source', side_effect=mock_source_factory):
            await manager.start()
            
            try:
                # 发送多个请求，测试负载均衡
                results = []
                for i in range(15):  # 15个请求，应该平均分配给3个数据源
                    result = await manager.get_stock_info(code=f"{i+1:06d}")
                    results.append(result)
                
                # 检查负载分布
                print(f"\\n负载均衡测试结果:")
                print(f"数据源调用统计: {source_call_counts}")
                
                # 每个数据源应该被调用大约相同的次数
                call_counts = list(source_call_counts.values())
                min_calls = min(call_counts)
                max_calls = max(call_counts)
                
                # 允许一定的不平衡度
                assert max_calls - min_calls <= 2, f"负载分布不均: {source_call_counts}"
                
                print("✓ 负载均衡正常工作")
                
            finally:
                await manager.shutdown()


class TestEventDrivenIntegration:
    """事件驱动集成测试"""
    
    @pytest.mark.asyncio
    async def test_data_update_event_propagation(
        self
    ):
        """测试数据更新事件传播"""
        config = DataManagerConfig(
            event_driven_enabled=True,
            cache=CacheConfig(
                enabled=True,
                auto_refresh_on_update=True
            )
        )
        
        manager = UnifiedDataManager(config)
        await manager.start()
        
        try:
            event_received = []
            
            # 注册事件监听器
            async def on_data_update(event_data):
                event_received.append(event_data)
                print(f"收到数据更新事件: {event_data}")
            
            if hasattr(manager.event_bus, 'subscribe'):
                await manager.event_bus.subscribe('data_updated', on_data_update)
            
            # 触发数据更新
            stock_code = "000003"
            await manager.get_stock_info(code=stock_code)
            
            # 模拟数据变更
            if hasattr(manager, 'update_stock_info'):
                await manager.update_stock_info(
                    code=stock_code,
                    name="更新后的股票名称",
                    last_price=15.86
                )
            
            # 等待事件传播
            await asyncio.sleep(0.5)
            
            # 验证事件被正确接收
            assert len(event_received) > 0, "应该收到数据更新事件"
            
            latest_event = event_received[-1]
            assert latest_event.get('stock_code') == stock_code, "事件应该包含正确的股票代码"
            
            print(f"\\n事件驱动测试结果:")
            print(f"接收到的事件数量: {len(event_received)}")
            print(f"最新事件: {latest_event}")
            
        finally:
            await manager.shutdown()
    
    @pytest.mark.asyncio
    async def test_real_time_data_streaming(
        self
    ):
        """测试实时数据流集成"""
        config = DataManagerConfig(
            streaming_enabled=True,
            stream_buffer_size=1000,
            batch_processing_enabled=True
        )
        
        manager = UnifiedDataManager(config)
        await manager.start()
        
        try:
            streaming_data = []
            
            # 模拟实时数据流
            async def mock_data_stream():
                for i in range(20):
                    yield {
                        'code': f"{i%5+1:06d}",  # 5个股票轮换
                        'price': 10.0 + i * 0.1,
                        'volume': 1000 * (i + 1),
                        'timestamp': datetime.now()
                    }
                    await asyncio.sleep(0.05)  # 50ms间隔
            
            # 启动数据流处理
            if hasattr(manager, 'start_data_stream'):
                stream_task = asyncio.create_task(
                    manager.start_data_stream(mock_data_stream())
                )
                
                # 同时进行数据查询
                query_results = []
                for i in range(10):
                    result = await manager.get_real_time_data(code=f"{i%5+1:06d}")
                    if result:
                        query_results.append(result)
                    await asyncio.sleep(0.1)
                
                # 停止数据流
                stream_task.cancel()
                
                try:
                    await stream_task
                except asyncio.CancelledError:
                    pass
                
                # 验证实时数据更新
                assert len(query_results) > 0, "应该能获取实时数据"
                
                # 检查数据是否在更新
                if len(query_results) >= 2:
                    first_result = query_results[0]
                    last_result = query_results[-1]
                    
                    # 价格应该有变化（模拟数据在递增）
                    if hasattr(first_result, 'price') and hasattr(last_result, 'price'):
                        assert last_result.price != first_result.price, "实时数据应该在更新"
                
                print(f"\\n实时数据流测试结果:")
                print(f"查询结果数量: {len(query_results)}")
                print(f"第一个结果: {query_results[0] if query_results else 'None'}")
                print(f"最后一个结果: {query_results[-1] if query_results else 'None'}")
                
        finally:
            await manager.shutdown()


class TestPluginSystemIntegration:
    """插件系统集成测试"""
    
    @pytest.mark.asyncio
    async def test_data_transformation_plugins(
        self
    ):
        """测试数据转换插件集成"""
        config = DataManagerConfig(
            plugins={
                'data_transformers': [
                    'price_normalizer',
                    'currency_converter',
                    'data_validator'
                ]
            }
        )
        
        manager = UnifiedDataManager(config)
        
        # 模拟插件
        mock_plugins = {
            'price_normalizer': AsyncMock(),
            'currency_converter': AsyncMock(),
            'data_validator': AsyncMock()
        }
        
        # 配置插件行为
        mock_plugins['price_normalizer'].transform.return_value = {
            'code': '000001',
            'price': 10.50,  # 规范化后的价格
            'normalized': True
        }
        
        mock_plugins['currency_converter'].transform.return_value = {
            'code': '000001',
            'price': 10.50,
            'price_usd': 1.45,  # 转换后的美元价格
            'currency': 'USD'
        }
        
        mock_plugins['data_validator'].transform.return_value = {
            'code': '000001',
            'price': 10.50,
            'price_usd': 1.45,
            'validated': True,
            'validation_passed': True
        }
        
        with patch.object(manager.plugin_manager, 'get_plugin', side_effect=lambda name: mock_plugins.get(name)):
            await manager.start()
            
            try:
                # 请求数据，应该经过所有插件处理
                result = await manager.get_stock_info(code="000001")
                
                # 验证插件是否被调用
                for plugin_name, plugin in mock_plugins.items():
                    assert plugin.transform.called, f"插件 {plugin_name} 应该被调用"
                
                # 验证最终结果包含所有插件的处理结果
                assert result.get('normalized') is True, "应该包含价格规范化标记"
                assert result.get('price_usd') is not None, "应该包含美元价格"
                assert result.get('validated') is True, "应该包含验证标记"
                
                print(f"\\n插件系统测试结果:")
                print(f"最终结果: {result}")
                print("✓ 所有数据转换插件正常工作")
                
            finally:
                await manager.shutdown()
    
    @pytest.mark.asyncio
    async def test_custom_indicator_plugins(
        self
    ):
        """测试自定义指标插件集成"""
        config = DataManagerConfig(
            plugins={
                'indicators': [
                    'custom_rsi',
                    'sentiment_score',
                    'volatility_index'
                ]
            }
        )
        
        manager = UnifiedDataManager(config)
        
        # 模拟指标计算插件
        mock_indicator_plugins = {
            'custom_rsi': AsyncMock(),
            'sentiment_score': AsyncMock(),
            'volatility_index': AsyncMock()
        }
        
        # 配置指标计算结果
        mock_indicator_plugins['custom_rsi'].calculate.return_value = {
            'rsi': 65.4,
            'signal': 'neutral'
        }
        
        mock_indicator_plugins['sentiment_score'].calculate.return_value = {
            'sentiment': 0.75,
            'confidence': 0.8
        }
        
        mock_indicator_plugins['volatility_index'].calculate.return_value = {
            'volatility': 0.25,
            'risk_level': 'medium'
        }
        
        with patch.object(manager.plugin_manager, 'get_plugin', side_effect=lambda name: mock_indicator_plugins.get(name)):
            await manager.start()
            
            try:
                # 请求指标计算
                if hasattr(manager, 'calculate_indicators'):
                    indicators = await manager.calculate_indicators(
                        code="000001",
                        indicator_types=['custom_rsi', 'sentiment_score', 'volatility_index']
                    )
                    
                    # 验证所有指标都被计算
                    assert 'custom_rsi' in indicators, "应该包含自定义RSI"
                    assert 'sentiment_score' in indicators, "应该包含情绪分数"
                    assert 'volatility_index' in indicators, "应该包含波动率指数"
                    
                    # 验证指标值
                    assert indicators['custom_rsi']['rsi'] == 65.4
                    assert indicators['sentiment_score']['sentiment'] == 0.75
                    assert indicators['volatility_index']['volatility'] == 0.25
                    
                    print(f"\\n自定义指标插件测试结果:")
                    print(f"计算的指标: {list(indicators.keys())}")
                    print(f"指标详情: {indicators}")
                    print("✓ 所有自定义指标插件正常工作")
                
            finally:
                await manager.shutdown()


class TestSystemIntegrationEndToEnd:
    """端到端系统集成测试"""
    
    @pytest.mark.asyncio
    async def test_complete_trading_workflow(
        self,
        redis_client: redis.Redis
    ):
        """测试完整的交易工作流程集成"""
        config = DataManagerConfig(
            cache=CacheConfig(enabled=True, default_ttl=1800),
            routing=RoutingConfig(failover_enabled=True),
            event_driven_enabled=True,
            streaming_enabled=True
        )
        
        manager = UnifiedDataManager(config)
        await manager.start()
        
        try:
            workflow_steps = []
            
            # 步骤1: 获取股票列表
            stock_list = await manager.get_stock_list(market="SZ")
            workflow_steps.append(("股票列表获取", len(stock_list) if stock_list else 0))
            
            # 步骤2: 获取热门股票的基本信息
            hot_stocks = stock_list[:5] if stock_list else ["000001", "000002"]
            stock_info_results = []
            
            for code in hot_stocks:
                info = await manager.get_stock_info(code=code)
                if info:
                    stock_info_results.append(info)
            
            workflow_steps.append(("股票信息获取", len(stock_info_results)))
            
            # 步骤3: 获取历史数据
            daily_data_results = []
            for code in hot_stocks[:3]:
                daily_data = await manager.get_stock_daily_data(
                    code=code,
                    start_date=datetime.now() - timedelta(days=30),
                    end_date=datetime.now()
                )
                if daily_data:
                    daily_data_results.append((code, len(daily_data)))
            
            workflow_steps.append(("历史数据获取", len(daily_data_results)))
            
            # 步骤4: 计算技术指标
            if hasattr(manager, 'calculate_indicators'):
                indicator_results = []
                for code in hot_stocks[:2]:
                    indicators = await manager.calculate_indicators(
                        code=code,
                        indicator_types=['MACD', 'RSI', 'KDJ']
                    )
                    if indicators:
                        indicator_results.append((code, list(indicators.keys())))
                
                workflow_steps.append(("技术指标计算", len(indicator_results)))
            
            # 步骤5: 获取实时数据
            realtime_results = []
            for code in hot_stocks[:3]:
                realtime = await manager.get_real_time_data(code=code)
                if realtime:
                    realtime_results.append(realtime)
            
            workflow_steps.append(("实时数据获取", len(realtime_results)))
            
            # 验证完整工作流程
            print(f"\\n完整交易工作流程测试结果:")
            for step_name, result_count in workflow_steps:
                print(f"{step_name}: {result_count} 个结果")
                assert result_count > 0, f"{step_name} 应该返回结果"
            
            # 性能验证：整个工作流程应该在合理时间内完成
            total_time = time.perf_counter()
            # 重新执行一遍测试缓存性能
            for code in hot_stocks[:2]:
                await manager.get_stock_info(code=code)
                await manager.get_real_time_data(code=code)
            
            cached_time = time.perf_counter() - total_time
            assert cached_time < 0.1, f"缓存后的工作流程应该很快: {cached_time:.3f}s"
            
            print(f"\\n性能统计:")
            print(f"缓存后工作流程时间: {cached_time*1000:.1f}ms")
            print("✓ 完整交易工作流程集成测试通过")
            
        finally:
            await manager.shutdown()
    
    @pytest.mark.asyncio
    async def test_system_resilience_under_stress(
        self,
        redis_client: redis.Redis
    ):
        """测试系统在压力下的弹性"""
        config = DataManagerConfig(
            cache=CacheConfig(enabled=True, max_memory_mb=20),
            routing=RoutingConfig(
                circuit_breaker_enabled=True,
                max_retries=3,
                timeout_seconds=10
            ),
            worker_count=5,
            request_queue_size=1000
        )
        
        manager = UnifiedDataManager(config)
        await manager.start()
        
        try:
            stress_results = {
                'successful_requests': 0,
                'failed_requests': 0,
                'timeout_requests': 0,
                'total_time': 0
            }
            
            # 生成大量并发请求
            async def stress_request(request_id: int):
                try:
                    start_time = time.perf_counter()
                    
                    # 随机选择不同类型的请求
                    request_type = request_id % 4
                    
                    if request_type == 0:
                        result = await manager.get_stock_list()
                    elif request_type == 1:
                        code = f"{(request_id % 100) + 1:06d}"
                        result = await manager.get_stock_info(code=code)
                    elif request_type == 2:
                        code = f"{(request_id % 50) + 1:06d}"
                        result = await manager.get_real_time_data(code=code)
                    else:
                        code = f"{(request_id % 20) + 1:06d}"
                        result = await manager.get_stock_daily_data(
                            code=code,
                            start_date=datetime.now() - timedelta(days=7),
                            end_date=datetime.now()
                        )
                    
                    request_time = time.perf_counter() - start_time
                    
                    if result is not None:
                        stress_results['successful_requests'] += 1
                    else:
                        stress_results['failed_requests'] += 1
                    
                    stress_results['total_time'] += request_time
                    
                except asyncio.TimeoutError:
                    stress_results['timeout_requests'] += 1
                except Exception:
                    stress_results['failed_requests'] += 1
            
            # 启动压力测试
            stress_start = time.perf_counter()
            concurrent_requests = 200
            
            tasks = [stress_request(i) for i in range(concurrent_requests)]
            await asyncio.gather(*tasks, return_exceptions=True)
            
            total_stress_time = time.perf_counter() - stress_start
            
            # 分析压力测试结果
            total_requests = (
                stress_results['successful_requests'] + 
                stress_results['failed_requests'] + 
                stress_results['timeout_requests']
            )
            
            success_rate = stress_results['successful_requests'] / total_requests if total_requests > 0 else 0
            avg_response_time = stress_results['total_time'] / stress_results['successful_requests'] if stress_results['successful_requests'] > 0 else 0
            throughput = total_requests / total_stress_time
            
            # 弹性测试断言
            assert success_rate >= 0.95, f"成功率应该 >= 95%, 实际: {success_rate:.2%}"
            assert avg_response_time < 0.1, f"平均响应时间应该 < 100ms, 实际: {avg_response_time*1000:.1f}ms"
            assert throughput >= 50, f"吞吐量应该 >= 50 req/s, 实际: {throughput:.1f} req/s"
            
            print(f"\\n系统弹性压力测试结果:")
            print(f"总请求数: {total_requests}")
            print(f"成功请求: {stress_results['successful_requests']}")
            print(f"失败请求: {stress_results['failed_requests']}")
            print(f"超时请求: {stress_results['timeout_requests']}")
            print(f"成功率: {success_rate:.2%}")
            print(f"平均响应时间: {avg_response_time*1000:.1f}ms")
            print(f"吞吐量: {throughput:.1f} req/s")
            print(f"总耗时: {total_stress_time:.2f}s")
            print("✓ 系统弹性测试通过")
            
        finally:
            await manager.shutdown()