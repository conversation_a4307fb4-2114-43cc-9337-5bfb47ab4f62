"""
技术指标回测功能 - API集成测试
测试交易日API端点、扫描API的end_date参数、Schema验证等
"""
import pytest
from httpx import AsyncClient
from fastapi import status
from datetime import datetime, timedelta
import json

from app.main import app
from app.core.database import db_session
from app.models.user import User
from app.schemas.scheduled_task import TaskStatus
from tests.utils.test_data_factory import TestDataFactory


class TestBacktestAPI:
    """回测功能API测试"""

    @pytest.fixture
    async def test_user(self):
        """创建测试用户"""
        async with db_session() as db:
            factory = TestDataFactory(db)
            user = await factory.create_test_user("backtest_user")
            yield user

    @pytest.fixture
    async def auth_headers(self, test_user):
        """获取认证头"""
        from app.core.auth.jwt_auth import create_access_token
        token = create_access_token(data={"sub": str(test_user.id)})
        return {"Authorization": f"Bearer {token}"}

    @pytest.mark.asyncio
    async def test_get_latest_trading_date_success(self):
        """测试获取最近交易日期 - 成功场景"""
        async with AsyncClient(app=app, base_url="http://test") as ac:
            response = await ac.get("/api/v1/trading/latest-trading-date")
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # 验证响应结构
        assert "date" in data
        assert "formatted" in data
        
        # 验证日期格式
        assert isinstance(data["date"], str)
        assert len(data["date"]) == 10  # YYYY-MM-DD格式
        
        # 验证日期可以被解析
        try:
            datetime.strptime(data["date"], "%Y-%m-%d")
        except ValueError:
            pytest.fail("返回的日期格式不正确")
        
        # 验证友好格式
        assert isinstance(data["formatted"], str)
        assert "年" in data["formatted"] and "月" in data["formatted"] and "日" in data["formatted"]

    @pytest.mark.asyncio 
    async def test_scan_start_with_end_date_valid(self, auth_headers):
        """测试扫描启动 - 有效end_date参数"""
        scan_request = {
            "indicators": ["kdj", "volume_pressure"],
            "stock_codes": ["000001", "000002"],
            "scan_mode": "traditional",
            "periods": ["d"],
            "adjust": "n",
            "end_date": "2024-01-15"  # 有效的历史日期
        }
        
        async with AsyncClient(app=app, base_url="http://test") as ac:
            response = await ac.post(
                "/api/v1/scan/start",
                json=scan_request,
                headers=auth_headers
            )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # 验证返回结构
        assert "task_id" in data
        assert "status" in data
        assert "created_at" in data
        
        # 任务应该被成功创建
        assert data["status"] in ["pending", "running"]
        assert data["task_id"] is not None

    @pytest.mark.asyncio
    async def test_scan_start_with_end_date_invalid_format(self, auth_headers):
        """测试扫描启动 - 无效end_date格式"""
        scan_request = {
            "indicators": ["kdj"],
            "stock_codes": ["000001"],
            "scan_mode": "traditional",
            "periods": ["d"],
            "adjust": "n",
            "end_date": "2024/01/15"  # 错误格式
        }
        
        async with AsyncClient(app=app, base_url="http://test") as ac:
            response = await ac.post(
                "/api/v1/scan/start",
                json=scan_request,
                headers=auth_headers
            )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        error_data = response.json()
        
        # 验证错误信息包含end_date相关内容
        assert "detail" in error_data
        error_detail = str(error_data["detail"])
        assert "end_date" in error_detail.lower() or "yyyy-mm-dd" in error_detail.lower()

    @pytest.mark.asyncio
    async def test_scan_start_with_future_end_date(self, auth_headers):
        """测试扫描启动 - 未来日期的end_date"""
        future_date = (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")
        
        scan_request = {
            "indicators": ["kdj"],
            "stock_codes": ["000001"],
            "scan_mode": "traditional", 
            "periods": ["d"],
            "adjust": "n",
            "end_date": future_date
        }
        
        async with AsyncClient(app=app, base_url="http://test") as ac:
            response = await ac.post(
                "/api/v1/scan/start",
                json=scan_request,
                headers=auth_headers
            )
        
        # Schema验证应该允许未来日期（业务逻辑层再处理）
        assert response.status_code == status.HTTP_200_OK

    @pytest.mark.asyncio
    async def test_scan_start_without_end_date(self, auth_headers):
        """测试扫描启动 - 不提供end_date参数（使用默认值）"""
        scan_request = {
            "indicators": ["kdj"],
            "stock_codes": ["000001"],
            "scan_mode": "traditional",
            "periods": ["d"],
            "adjust": "n"
            # 不包含end_date
        }
        
        async with AsyncClient(app=app, base_url="http://test") as ac:
            response = await ac.post(
                "/api/v1/scan/start",
                json=scan_request,
                headers=auth_headers
            )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "task_id" in data

    @pytest.mark.asyncio
    async def test_scan_start_null_end_date(self, auth_headers):
        """测试扫描启动 - end_date为null"""
        scan_request = {
            "indicators": ["kdj"],
            "stock_codes": ["000001"],
            "scan_mode": "traditional",
            "periods": ["d"],
            "adjust": "n",
            "end_date": None
        }
        
        async with AsyncClient(app=app, base_url="http://test") as ac:
            response = await ac.post(
                "/api/v1/scan/start",
                json=scan_request,
                headers=auth_headers
            )
        
        assert response.status_code == status.HTTP_200_OK

    @pytest.mark.asyncio
    async def test_scan_results_include_end_date(self, auth_headers):
        """测试扫描结果包含end_date信息"""
        # 首先创建一个扫描任务
        scan_request = {
            "indicators": ["kdj"],
            "stock_codes": ["000001"],
            "scan_mode": "traditional",
            "periods": ["d"],
            "adjust": "n",
            "end_date": "2024-01-15"
        }
        
        async with AsyncClient(app=app, base_url="http://test") as ac:
            # 启动扫描
            start_response = await ac.post(
                "/api/v1/scan/start",
                json=scan_request,
                headers=auth_headers
            )
            
            assert start_response.status_code == status.HTTP_200_OK
            task_id = start_response.json()["task_id"]
            
            # 等待任务完成并获取结果
            # 注意：在实际测试中可能需要等待或模拟任务完成
            results_response = await ac.get(
                f"/api/v1/scan/{task_id}/results",
                headers=auth_headers
            )
            
            if results_response.status_code == status.HTTP_200_OK:
                results_data = results_response.json()
                
                # 验证结果中包含end_date
                assert "end_date" in results_data
                assert results_data["end_date"] == "2024-01-15"
                
                # 验证结果中每个项目也包含end_date
                if "results" in results_data and results_data["results"]:
                    for result_item in results_data["results"]:
                        assert "end_date" in result_item
                        assert result_item["end_date"] == "2024-01-15"

    @pytest.mark.asyncio
    async def test_scan_task_details_include_end_date(self, auth_headers):
        """测试扫描任务详情包含end_date"""
        scan_request = {
            "indicators": ["kdj"],
            "stock_codes": ["000001"],
            "scan_mode": "traditional",
            "periods": ["d"],
            "adjust": "n",
            "end_date": "2024-01-15"
        }
        
        async with AsyncClient(app=app, base_url="http://test") as ac:
            # 启动扫描
            start_response = await ac.post(
                "/api/v1/scan/start",
                json=scan_request,
                headers=auth_headers
            )
            
            task_id = start_response.json()["task_id"]
            
            # 获取任务详情
            task_response = await ac.get(
                f"/api/v1/scan/{task_id}",
                headers=auth_headers
            )
            
            assert task_response.status_code == status.HTTP_200_OK
            task_data = task_response.json()
            
            # 验证任务详情包含end_date
            assert "end_date" in task_data
            assert task_data["end_date"] == "2024-01-15"

    @pytest.mark.asyncio
    async def test_active_scans_include_end_date(self, auth_headers):
        """测试活跃扫描列表包含end_date"""
        scan_request = {
            "indicators": ["kdj"],
            "stock_codes": ["000001"],
            "scan_mode": "traditional", 
            "periods": ["d"],
            "adjust": "n",
            "end_date": "2024-01-15"
        }
        
        async with AsyncClient(app=app, base_url="http://test") as ac:
            # 启动扫描
            await ac.post(
                "/api/v1/scan/start",
                json=scan_request,
                headers=auth_headers
            )
            
            # 获取活跃扫描
            active_response = await ac.get(
                "/api/v1/scan/active",
                headers=auth_headers
            )
            
            assert active_response.status_code == status.HTTP_200_OK
            active_data = active_response.json()
            
            # 验证活跃扫描列表包含end_date
            assert isinstance(active_data, list)
            if active_data:
                latest_scan = active_data[0]
                assert "end_date" in latest_scan
                assert latest_scan["end_date"] == "2024-01-15"

    @pytest.mark.asyncio
    async def test_schema_validation_edge_cases(self, auth_headers):
        """测试Schema验证边界情况"""
        
        # 测试空字符串end_date
        scan_request = {
            "indicators": ["kdj"],
            "stock_codes": ["000001"],
            "scan_mode": "traditional",
            "periods": ["d"],
            "adjust": "n",
            "end_date": ""
        }
        
        async with AsyncClient(app=app, base_url="http://test") as ac:
            response = await ac.post(
                "/api/v1/scan/start",
                json=scan_request,
                headers=auth_headers
            )
        
        # 空字符串应该被拒绝
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    @pytest.mark.asyncio
    async def test_multi_period_with_end_date(self, auth_headers):
        """测试多周期模式下的end_date参数"""
        scan_request = {
            "indicators": ["kdj", "volume_pressure"],
            "scan_mode": "multi_period",
            "scan_strategy": "parallel",
            "periods": ["d", "w"],
            "adjust": "n",
            "period_indicators": {
                "d": ["kdj"],
                "w": ["volume_pressure"]
            },
            "end_date": "2024-01-15"
        }
        
        async with AsyncClient(app=app, base_url="http://test") as ac:
            response = await ac.post(
                "/api/v1/scan/start",
                json=scan_request,
                headers=auth_headers
            )
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert "task_id" in data

    @pytest.mark.asyncio
    async def test_authentication_required(self):
        """测试需要认证的端点"""
        scan_request = {
            "indicators": ["kdj"],
            "stock_codes": ["000001"],
            "scan_mode": "traditional",
            "periods": ["d"],
            "adjust": "n",
            "end_date": "2024-01-15"
        }
        
        async with AsyncClient(app=app, base_url="http://test") as ac:
            response = await ac.post(
                "/api/v1/scan/start",
                json=scan_request
                # 不包含认证头
            )
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.parametrize("invalid_date", [
        "2024-13-01",  # 无效月份
        "2024-02-30",  # 无效日期
        "24-01-15",    # 年份格式错误
        "2024-1-1",    # 月日格式错误
        "invalid-date", # 完全无效
    ])
    @pytest.mark.asyncio
    async def test_invalid_date_formats(self, auth_headers, invalid_date):
        """测试各种无效日期格式"""
        scan_request = {
            "indicators": ["kdj"],
            "stock_codes": ["000001"],
            "scan_mode": "traditional",
            "periods": ["d"],
            "adjust": "n",
            "end_date": invalid_date
        }
        
        async with AsyncClient(app=app, base_url="http://test") as ac:
            response = await ac.post(
                "/api/v1/scan/start",
                json=scan_request,
                headers=auth_headers
            )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY