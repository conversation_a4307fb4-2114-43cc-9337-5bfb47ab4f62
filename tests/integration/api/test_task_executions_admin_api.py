"""任务执行记录管理员API集成测试"""

import pytest
import json
from datetime import datetime, timedelta
from unittest.mock import patch, Mock, AsyncMock
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import User
from app.models.task import TaskExecution
from app.schemas.scheduled_task import TaskType, TaskStatus, TriggerType


class TestTaskExecutionAdminAPI:
    """任务执行记录管理员API集成测试"""

    @pytest.fixture
    def mock_admin_user(self):
        """创建模拟管理员用户"""
        admin_user = Mock(spec=User)
        admin_user.id = 1
        admin_user.username = "admin"
        admin_user.email = "<EMAIL>"
        admin_user.is_admin = True
        return admin_user

    @pytest.fixture
    def mock_regular_user(self):
        """创建模拟普通用户"""
        user = Mock(spec=User)
        user.id = 2
        user.username = "user1"
        user.email = "<EMAIL>" 
        user.is_admin = False
        return user

    @pytest.fixture
    def admin_auth_headers(self, mock_admin_user):
        """管理员认证头"""
        return {"Authorization": f"Bearer admin_token_{mock_admin_user.id}"}

    @pytest.fixture
    def user_auth_headers(self, mock_regular_user):
        """普通用户认证头"""
        return {"Authorization": f"Bearer user_token_{mock_regular_user.id}"}

    @pytest.mark.asyncio
    async def test_admin_get_all_task_executions(self, client, admin_auth_headers, mock_admin_user):
        """测试管理员获取所有用户的任务执行记录"""
        with patch('app.api.endpoints.task_executions.get_current_admin_user', return_value=mock_admin_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock(spec=AsyncSession)
                mock_get_db.return_value.__aenter__.return_value = mock_db
                
                # 创建模拟执行记录（多个用户）
                mock_executions = []
                users = [
                    {"id": 1, "username": "admin", "email": "<EMAIL>"},
                    {"id": 2, "username": "user1", "email": "<EMAIL>"},
                    {"id": 3, "username": "user2", "email": "<EMAIL>"}
                ]
                
                for i, user_info in enumerate(users):
                    for j in range(2):  # 每个用户2条记录
                        execution_id = i * 2 + j + 1
                        mock_execution = Mock()
                        mock_execution.id = execution_id
                        mock_execution.user_id = user_info["id"]
                        mock_execution.scheduled_task_id = execution_id
                        mock_execution.trigger_type = TriggerType.SCHEDULED if j == 0 else TriggerType.MANUAL
                        mock_execution.task_type = TaskType.INDICATOR_SCAN
                        mock_execution.status = TaskStatus.COMPLETED
                        mock_execution.start_time = datetime.now()
                        mock_execution.end_time = datetime.now() + timedelta(minutes=5)
                        mock_execution.duration_seconds = 300
                        mock_execution.results_count = 10 + i * 5
                        mock_execution.error_message = None
                        mock_execution.results_data = json.dumps([{"symbol": "000001.SZ", "signal": "buy"}])
                        mock_execution.created_at = datetime.now()
                        mock_execution.task_config = json.dumps({"indicators": ["MACD", "KDJ"]})
                        
                        # 模拟关联用户
                        mock_user = Mock()
                        mock_user.username = user_info["username"]
                        mock_user.email = user_info["email"]
                        mock_execution.user = mock_user
                        
                        # 模拟关联任务
                        mock_scheduled_task = Mock()
                        mock_scheduled_task.name = f"{user_info['username']}的任务{j+1}"
                        mock_execution.scheduled_task = mock_scheduled_task
                        
                        mock_execution.__dict__ = {
                            'id': execution_id,
                            'user_id': user_info["id"],
                            'scheduled_task_id': execution_id,
                            'trigger_type': mock_execution.trigger_type,
                            'task_type': mock_execution.task_type,
                            'status': mock_execution.status,
                            'start_time': mock_execution.start_time,
                            'end_time': mock_execution.end_time,
                            'duration_seconds': mock_execution.duration_seconds,
                            'results_count': mock_execution.results_count,
                            'error_message': mock_execution.error_message,
                            'results_data': mock_execution.results_data,
                            'created_at': mock_execution.created_at,
                            'task_config': mock_execution.task_config
                        }
                        
                        mock_executions.append(mock_execution)
                
                # 模拟异步数据库查询
                mock_result = Mock()
                mock_result.scalars.return_value.all.return_value = mock_executions
                mock_db.execute.return_value = mock_result
                
                # 模拟count查询
                mock_count_result = Mock()
                mock_count_result.scalar.return_value = len(mock_executions)
                mock_db.execute.side_effect = [mock_count_result, mock_result]
                
                response = await client.get("/api/v1/task-executions/admin/all", headers=admin_auth_headers)
                
                assert response.status_code == 200
                response_data = response.json()
                assert response_data["total"] == len(mock_executions)
                assert len(response_data["items"]) == len(mock_executions)
                
                # 验证包含所有用户的记录
                usernames = {item["user_username"] for item in response_data["items"] if item["user_username"]}
                assert "admin" in usernames
                assert "user1" in usernames
                assert "user2" in usernames
                
                # 验证管理员视图包含用户信息
                first_item = response_data["items"][0]
                assert "user_username" in first_item
                assert "user_email" in first_item

    @pytest.mark.asyncio
    async def test_admin_filter_by_user_id(self, client, admin_auth_headers, mock_admin_user):
        """测试管理员按用户ID过滤执行记录"""
        target_user_id = 2
        
        with patch('app.api.endpoints.task_executions.get_current_admin_user', return_value=mock_admin_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock(spec=AsyncSession)
                mock_get_db.return_value.__aenter__.return_value = mock_db
                
                # 模拟过滤后的结果（只有特定用户的记录）
                mock_execution = Mock()
                mock_execution.id = 1
                mock_execution.user_id = target_user_id
                mock_execution.trigger_type = TriggerType.MANUAL
                mock_execution.status = TaskStatus.COMPLETED
                
                mock_user = Mock()
                mock_user.username = "user2"
                mock_user.email = "<EMAIL>"
                mock_execution.user = mock_user
                
                mock_executions = [mock_execution]
                
                # 模拟数据库查询
                mock_result = Mock()
                mock_result.scalars.return_value.all.return_value = mock_executions
                mock_count_result = Mock()
                mock_count_result.scalar.return_value = len(mock_executions)
                mock_db.execute.side_effect = [mock_count_result, mock_result]
                
                response = await client.get(
                    f"/api/v1/task-executions/admin/all?user_id={target_user_id}",
                    headers=admin_auth_headers
                )
                
                assert response.status_code == 200
                response_data = response.json()
                assert response_data["total"] == 1
                
                # 验证过滤条件生效
                for item in response_data["items"]:
                    assert item["user_username"] == "user2"

    @pytest.mark.asyncio 
    async def test_admin_pagination_and_sorting(self, client, admin_auth_headers, mock_admin_user):
        """测试管理员视图的分页和排序"""
        with patch('app.api.endpoints.task_executions.get_current_admin_user', return_value=mock_admin_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock(spec=AsyncSession)
                mock_get_db.return_value.__aenter__.return_value = mock_db
                
                # 模拟大量数据的第二页
                total_count = 100
                page_size = 20
                skip = 20
                
                mock_executions = []
                for i in range(page_size):
                    mock_execution = Mock()
                    mock_execution.id = skip + i + 1
                    mock_execution.created_at = datetime.now() - timedelta(days=i)
                    mock_executions.append(mock_execution)
                
                # 模拟查询结果
                mock_result = Mock()
                mock_result.scalars.return_value.all.return_value = mock_executions
                mock_count_result = Mock()
                mock_count_result.scalar.return_value = total_count
                mock_db.execute.side_effect = [mock_count_result, mock_result]
                
                response = await client.get(
                    f"/api/v1/task-executions/admin/all?skip={skip}&limit={page_size}",
                    headers=admin_auth_headers
                )
                
                assert response.status_code == 200
                response_data = response.json()
                assert response_data["total"] == total_count
                assert len(response_data["items"]) == page_size

    @pytest.mark.asyncio
    async def test_regular_user_cannot_access_admin_endpoint(self, client, user_auth_headers, mock_regular_user):
        """测试普通用户不能访问管理员端点"""
        with patch('app.api.endpoints.task_executions.get_current_admin_user') as mock_admin_check:
            # 模拟管理员权限检查失败
            from fastapi import HTTPException
            mock_admin_check.side_effect = HTTPException(status_code=403, detail="需要管理员权限")
            
            response = await client.get("/api/v1/task-executions/admin/all", headers=user_auth_headers)
            assert response.status_code == 403

    @pytest.mark.asyncio
    async def test_admin_view_mixed_trigger_types(self, client, admin_auth_headers, mock_admin_user):
        """测试管理员查看混合触发类型的记录"""
        with patch('app.api.endpoints.task_executions.get_current_admin_user', return_value=mock_admin_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock(spec=AsyncSession)
                mock_get_db.return_value.__aenter__.return_value = mock_db
                
                # 创建不同触发类型的执行记录
                mock_executions = []
                
                # 定时任务记录
                scheduled_execution = Mock()
                scheduled_execution.id = 1
                scheduled_execution.trigger_type = TriggerType.SCHEDULED
                scheduled_execution.scheduled_task_id = 1
                scheduled_task = Mock()
                scheduled_task.name = "定时指标扫描"
                scheduled_execution.scheduled_task = scheduled_task
                scheduled_execution.__dict__ = {
                    'id': 1,
                    'trigger_type': TriggerType.SCHEDULED,
                    'scheduled_task_id': 1
                }
                mock_executions.append(scheduled_execution)
                
                # 手动任务记录
                manual_execution = Mock()
                manual_execution.id = 2
                manual_execution.trigger_type = TriggerType.MANUAL
                manual_execution.scheduled_task_id = None
                manual_execution.scheduled_task = None
                manual_execution.__dict__ = {
                    'id': 2,
                    'trigger_type': TriggerType.MANUAL,
                    'scheduled_task_id': None
                }
                mock_executions.append(manual_execution)
                
                # 模拟数据库查询
                mock_result = Mock()
                mock_result.scalars.return_value.all.return_value = mock_executions
                mock_count_result = Mock()
                mock_count_result.scalar.return_value = len(mock_executions)
                mock_db.execute.side_effect = [mock_count_result, mock_result]
                
                response = await client.get("/api/v1/task-executions/admin/all", headers=admin_auth_headers)
                
                assert response.status_code == 200
                response_data = response.json()
                
                # 验证包含定时和手动两种类型
                trigger_types = {item["trigger_type"] for item in response_data["items"]}
                assert "scheduled" in trigger_types
                assert "manual" in trigger_types
                
                # 验证手动任务没有关联定时任务名称
                manual_item = next(item for item in response_data["items"] if item["trigger_type"] == "manual")
                assert manual_item["scheduled_task_name"] is None

    @pytest.mark.asyncio
    async def test_admin_view_performance_with_large_dataset(self, client, admin_auth_headers, mock_admin_user):
        """测试管理员视图在大数据量下的性能"""
        with patch('app.api.endpoints.task_executions.get_current_admin_user', return_value=mock_admin_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock(spec=AsyncSession)
                mock_get_db.return_value.__aenter__.return_value = mock_db
                
                # 模拟大量数据（但只返回请求的分页数据）
                total_records = 10000
                page_size = 500  # 最大限制
                
                # 模拟分页查询结果
                mock_executions = []
                for i in range(page_size):
                    mock_execution = Mock()
                    mock_execution.id = i + 1
                    mock_execution.created_at = datetime.now() - timedelta(minutes=i)
                    mock_executions.append(mock_execution)
                
                # 模拟查询执行时间监控
                import time
                start_time = time.time()
                
                mock_result = Mock()
                mock_result.scalars.return_value.all.return_value = mock_executions
                mock_count_result = Mock()
                mock_count_result.scalar.return_value = total_records
                mock_db.execute.side_effect = [mock_count_result, mock_result]
                
                response = await client.get(
                    f"/api/v1/task-executions/admin/all?limit={page_size}",
                    headers=admin_auth_headers
                )
                
                end_time = time.time()
                query_duration = end_time - start_time
                
                assert response.status_code == 200
                response_data = response.json()
                assert response_data["total"] == total_records
                assert len(response_data["items"]) == page_size
                
                # 验证响应时间合理（应该在1秒内）
                assert query_duration < 1.0

    @pytest.mark.asyncio
    async def test_admin_view_error_handling(self, client, admin_auth_headers, mock_admin_user):
        """测试管理员视图的错误处理"""
        with patch('app.api.endpoints.task_executions.get_current_admin_user', return_value=mock_admin_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock(spec=AsyncSession)
                mock_get_db.return_value.__aenter__.return_value = mock_db
                
                # 模拟数据库连接错误
                mock_db.execute.side_effect = Exception("数据库连接失败")
                
                response = await client.get("/api/v1/task-executions/admin/all", headers=admin_auth_headers)
                
                # 应该返回500错误或者被全局异常处理器处理
                assert response.status_code in [500, 503]

    @pytest.mark.asyncio
    async def test_admin_complex_filtering(self, client, admin_auth_headers, mock_admin_user):
        """测试管理员的复合过滤条件"""
        with patch('app.api.endpoints.task_executions.get_current_admin_user', return_value=mock_admin_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock(spec=AsyncSession)
                mock_get_db.return_value.__aenter__.return_value = mock_db
                
                # 模拟符合多个过滤条件的记录
                mock_execution = Mock()
                mock_execution.id = 1
                mock_execution.trigger_type = TriggerType.MANUAL
                mock_execution.status = TaskStatus.FAILED
                mock_execution.user_id = 2
                
                mock_user = Mock()
                mock_user.username = "test_user"
                mock_user.email = "<EMAIL>"
                mock_execution.user = mock_user
                
                mock_executions = [mock_execution]
                
                mock_result = Mock()
                mock_result.scalars.return_value.all.return_value = mock_executions
                mock_count_result = Mock()
                mock_count_result.scalar.return_value = 1
                mock_db.execute.side_effect = [mock_count_result, mock_result]
                
                # 使用多个过滤条件
                response = await client.get(
                    "/api/v1/task-executions/admin/all?trigger_type=manual&status=failed&user_id=2",
                    headers=admin_auth_headers
                )
                
                assert response.status_code == 200
                response_data = response.json()
                assert response_data["total"] == 1
                
                item = response_data["items"][0]
                assert item["trigger_type"] == "manual"
                assert item["status"] == "failed"
                assert item["user_username"] == "test_user"