"""任务执行记录API集成测试"""

import pytest
import json
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from unittest.mock import patch, Mock
from sqlalchemy.orm import Session

from app.main import app
from app.models.user import User
from app.models.task import UserScheduledTask, TaskExecution
from app.schemas.scheduled_task import TaskType, TaskStatus, TriggerType


class TestTaskExecutionAPI:
    """任务执行记录API集成测试"""

    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)

    @pytest.fixture
    def auth_headers(self, mock_user):
        """创建认证头"""
        return {"Authorization": f"Bearer test_token_for_user_{mock_user.id}"}

    def test_get_all_task_executions(self, client, auth_headers, mock_user):
        """测试获取所有任务执行记录"""
        with patch('app.api.endpoints.task_executions.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                # 创建模拟执行记录
                mock_executions = []
                for i in range(3):
                    mock_execution = Mock()
                    mock_execution.id = i + 1
                    mock_execution.scheduled_task_id = i + 1
                    mock_execution.trigger_type = TriggerType.SCHEDULED
                    mock_execution.task_type = TaskType.INDICATOR_SCAN
                    mock_execution.status = TaskStatus.COMPLETED
                    mock_execution.start_time = datetime.now()
                    mock_execution.end_time = datetime.now() + timedelta(minutes=5)
                    mock_execution.duration_seconds = 300
                    mock_execution.results_count = 10
                    mock_execution.error_message = None
                    mock_execution.results_data = json.dumps([{"symbol": "000001.SZ", "signal": "buy"}])
                    mock_execution.created_at = datetime.now()
                    
                    # 模拟关联的定时任务
                    mock_scheduled_task = Mock()
                    mock_scheduled_task.name = f"测试任务{i+1}"
                    mock_execution.scheduled_task = mock_scheduled_task
                    
                    mock_execution.__dict__ = {
                        'id': mock_execution.id,
                        'scheduled_task_id': mock_execution.scheduled_task_id,
                        'trigger_type': mock_execution.trigger_type,
                        'task_type': mock_execution.task_type,
                        'status': mock_execution.status,
                        'start_time': mock_execution.start_time,
                        'end_time': mock_execution.end_time,
                        'duration_seconds': mock_execution.duration_seconds,
                        'results_count': mock_execution.results_count,
                        'error_message': mock_execution.error_message,
                        'results_data': mock_execution.results_data,
                        'created_at': mock_execution.created_at
                    }
                    
                    mock_executions.append(mock_execution)
                
                # 模拟数据库查询
                mock_query = Mock()
                mock_query.filter.return_value = mock_query
                mock_query.count.return_value = len(mock_executions)
                mock_query.order_by.return_value.offset.return_value.limit.return_value.all.return_value = mock_executions
                mock_db.query.return_value = mock_query
                
                response = client.get("/api/v1/task-executions/", headers=auth_headers)
                
                assert response.status_code == 200
                response_data = response.json()
                assert response_data["total"] == len(mock_executions)
                assert len(response_data["items"]) == len(mock_executions)
                
                # 验证第一条记录
                first_item = response_data["items"][0]
                assert first_item["id"] == 1
                assert first_item["trigger_type"] == "scheduled"
                assert first_item["status"] == "completed"
                assert first_item["scheduled_task_name"] == "测试任务1"

    def test_get_executions_with_filters(self, client, auth_headers, mock_user):
        """测试使用过滤条件获取执行记录"""
        with patch('app.api.endpoints.task_executions.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                # 模拟过滤后的结果
                mock_query = Mock()
                mock_query.filter.return_value = mock_query
                mock_query.count.return_value = 1
                mock_query.order_by.return_value.offset.return_value.limit.return_value.all.return_value = []
                mock_db.query.return_value = mock_query
                
                # 测试按触发类型过滤
                response = client.get(
                    "/api/v1/task-executions/?trigger_type=manual&status=completed",
                    headers=auth_headers
                )
                
                assert response.status_code == 200
                
                # 验证过滤条件被应用
                assert mock_query.filter.call_count >= 2  # 至少调用了2次filter（用户ID + 过滤条件）

    def test_get_task_execution_detail(self, client, auth_headers, mock_user):
        """测试获取任务执行记录详情"""
        execution_id = 1
        
        with patch('app.api.endpoints.task_executions.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                # 创建模拟执行记录
                mock_execution = Mock()
                mock_execution.id = execution_id
                mock_execution.scheduled_task_id = 1
                mock_execution.trigger_type = TriggerType.MANUAL
                mock_execution.task_type = TaskType.INDICATOR_SCAN
                mock_execution.status = TaskStatus.COMPLETED
                mock_execution.start_time = datetime.now()
                mock_execution.end_time = datetime.now() + timedelta(minutes=5)
                mock_execution.duration_seconds = 300
                mock_execution.results_count = 15
                mock_execution.error_message = None
                mock_execution.results_data = json.dumps([
                    {"symbol": "000001.SZ", "signal": "buy", "strength": 0.85}
                ])
                mock_execution.created_at = datetime.now()
                
                # 模拟关联任务
                mock_scheduled_task = Mock()
                mock_scheduled_task.name = "详情测试任务"
                mock_execution.scheduled_task = mock_scheduled_task
                
                mock_execution.__dict__ = {
                    'id': mock_execution.id,
                    'scheduled_task_id': mock_execution.scheduled_task_id,
                    'trigger_type': mock_execution.trigger_type,
                    'task_type': mock_execution.task_type,
                    'status': mock_execution.status,
                    'start_time': mock_execution.start_time,
                    'end_time': mock_execution.end_time,
                    'duration_seconds': mock_execution.duration_seconds,
                    'results_count': mock_execution.results_count,
                    'error_message': mock_execution.error_message,
                    'results_data': mock_execution.results_data,
                    'created_at': mock_execution.created_at
                }
                
                mock_db.query.return_value.filter.return_value.first.return_value = mock_execution
                
                response = client.get(f"/api/v1/task-executions/{execution_id}", headers=auth_headers)
                
                assert response.status_code == 200
                response_data = response.json()
                assert response_data["id"] == execution_id
                assert response_data["trigger_type"] == "manual"
                assert response_data["status"] == "completed"
                assert response_data["results_count"] == 15
                assert response_data["scheduled_task_name"] == "详情测试任务"
                
                # 验证results_data被正确解析
                assert isinstance(response_data["results_data"], list)
                assert response_data["results_data"][0]["symbol"] == "000001.SZ"

    def test_get_nonexistent_execution(self, client, auth_headers, mock_user):
        """测试获取不存在的执行记录"""
        with patch('app.api.endpoints.task_executions.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                mock_db.query.return_value.filter.return_value.first.return_value = None
                
                response = client.get("/api/v1/task-executions/999", headers=auth_headers)
                
                assert response.status_code == 404
                assert "执行记录不存在" in response.json()["detail"]

    def test_delete_task_execution(self, client, auth_headers, mock_user):
        """测试删除任务执行记录"""
        execution_id = 1
        
        with patch('app.api.endpoints.task_executions.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                # 创建已完成的执行记录
                mock_execution = Mock()
                mock_execution.id = execution_id
                mock_execution.status = TaskStatus.COMPLETED
                
                mock_db.query.return_value.filter.return_value.first.return_value = mock_execution
                
                response = client.delete(f"/api/v1/task-executions/{execution_id}", headers=auth_headers)
                
                assert response.status_code == 200
                assert "执行记录已删除" in response.json()["message"]
                
                # 验证删除操作
                mock_db.delete.assert_called_once_with(mock_execution)
                mock_db.commit.assert_called()

    def test_delete_running_execution(self, client, auth_headers, mock_user):
        """测试删除正在运行的执行记录（应该失败）"""
        execution_id = 1
        
        with patch('app.api.endpoints.task_executions.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                # 创建正在运行的执行记录
                mock_execution = Mock()
                mock_execution.id = execution_id
                mock_execution.status = TaskStatus.RUNNING
                
                mock_db.query.return_value.filter.return_value.first.return_value = mock_execution
                
                response = client.delete(f"/api/v1/task-executions/{execution_id}", headers=auth_headers)
                
                assert response.status_code == 400
                assert "不能删除正在执行的任务记录" in response.json()["detail"]

    def test_cancel_task_execution(self, client, auth_headers, mock_user):
        """测试取消任务执行"""
        execution_id = 1
        
        with patch('app.api.endpoints.task_executions.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                with patch('app.api.endpoints.task_executions.get_task_executor') as mock_get_executor:
                    mock_db = Mock(spec=Session)
                    mock_get_db.return_value = mock_db
                    
                    # 创建正在运行的执行记录
                    mock_execution = Mock()
                    mock_execution.id = execution_id
                    mock_execution.status = TaskStatus.RUNNING
                    mock_execution.start_time = datetime.now()
                    mock_execution.end_time = None
                    
                    mock_db.query.return_value.filter.return_value.first.return_value = mock_execution
                    
                    # 模拟执行器
                    mock_executor = Mock()
                    mock_executor.cancel_execution.return_value = True  # 成功取消
                    mock_get_executor.return_value = mock_executor
                    
                    response = client.post(f"/api/v1/task-executions/{execution_id}/cancel", headers=auth_headers)
                    
                    assert response.status_code == 200
                    response_data = response.json()
                    assert "任务取消成功" in response_data["message"]
                    assert response_data["cancelled"] is True
                    
                    # 验证状态更新
                    assert mock_execution.status == TaskStatus.CANCELLED
                    assert mock_execution.end_time is not None
                    mock_db.commit.assert_called()

    def test_cancel_completed_execution(self, client, auth_headers, mock_user):
        """测试取消已完成的执行记录（应该失败）"""
        execution_id = 1
        
        with patch('app.api.endpoints.task_executions.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                # 创建已完成的执行记录
                mock_execution = Mock()
                mock_execution.id = execution_id
                mock_execution.status = TaskStatus.COMPLETED
                
                mock_db.query.return_value.filter.return_value.first.return_value = mock_execution
                
                response = client.post(f"/api/v1/task-executions/{execution_id}/cancel", headers=auth_headers)
                
                assert response.status_code == 400
                assert "只能取消待执行或正在执行的任务" in response.json()["detail"]

    def test_cancel_execution_partial_success(self, client, auth_headers, mock_user):
        """测试取消任务执行（执行器中取消失败）"""
        execution_id = 1
        
        with patch('app.api.endpoints.task_executions.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                with patch('app.api.endpoints.task_executions.get_task_executor') as mock_get_executor:
                    mock_db = Mock(spec=Session)
                    mock_get_db.return_value = mock_db
                    
                    # 创建待执行的记录
                    mock_execution = Mock()
                    mock_execution.id = execution_id
                    mock_execution.status = TaskStatus.PENDING
                    mock_execution.start_time = None
                    mock_execution.end_time = None
                    
                    mock_db.query.return_value.filter.return_value.first.return_value = mock_execution
                    
                    # 模拟执行器取消失败（任务不在执行器中）
                    mock_executor = Mock()
                    mock_executor.cancel_execution.return_value = False
                    mock_get_executor.return_value = mock_executor
                    
                    response = client.post(f"/api/v1/task-executions/{execution_id}/cancel", headers=auth_headers)
                    
                    assert response.status_code == 200
                    response_data = response.json()
                    assert "任务取消请求已提交" in response_data["message"]
                    assert response_data["cancelled"] is False
                    
                    # 验证数据库状态仍然更新
                    assert mock_execution.status == TaskStatus.CANCELLED

    def test_pagination_parameters(self, client, auth_headers, mock_user):
        """测试分页参数"""
        with patch('app.api.endpoints.task_executions.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                mock_query = Mock()
                mock_query.filter.return_value = mock_query
                mock_query.count.return_value = 0
                mock_query.order_by.return_value.offset.return_value.limit.return_value.all.return_value = []
                mock_db.query.return_value = mock_query
                
                # 测试有效分页参数
                response = client.get("/api/v1/task-executions/?skip=20&limit=100", headers=auth_headers)
                assert response.status_code == 200
                
                # 验证offset和limit被正确调用
                mock_query.order_by.return_value.offset.assert_called_with(20)
                mock_query.order_by.return_value.offset.return_value.limit.assert_called_with(100)

    def test_invalid_pagination_parameters(self, client, auth_headers, mock_user):
        """测试无效分页参数"""
        with patch('app.api.endpoints.task_executions.get_current_user', return_value=mock_user):
            # 测试负数skip
            response = client.get("/api/v1/task-executions/?skip=-1", headers=auth_headers)
            assert response.status_code == 422
            
            # 测试超出限制的limit
            response = client.get("/api/v1/task-executions/?limit=1000", headers=auth_headers)
            assert response.status_code == 422

    def test_manual_execution_without_scheduled_task(self, client, auth_headers, mock_user):
        """测试手动执行记录（无关联定时任务）"""
        execution_id = 1
        
        with patch('app.api.endpoints.task_executions.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                # 创建手动执行记录（无关联任务）
                mock_execution = Mock()
                mock_execution.id = execution_id
                mock_execution.scheduled_task_id = None
                mock_execution.scheduled_task = None
                mock_execution.trigger_type = TriggerType.MANUAL
                mock_execution.task_type = TaskType.INDICATOR_SCAN
                mock_execution.status = TaskStatus.COMPLETED
                mock_execution.start_time = datetime.now()
                mock_execution.end_time = datetime.now() + timedelta(minutes=3)
                mock_execution.duration_seconds = 180
                mock_execution.results_count = 5
                mock_execution.error_message = None
                mock_execution.results_data = None
                mock_execution.created_at = datetime.now()
                
                mock_execution.__dict__ = {
                    'id': mock_execution.id,
                    'scheduled_task_id': mock_execution.scheduled_task_id,
                    'trigger_type': mock_execution.trigger_type,
                    'task_type': mock_execution.task_type,
                    'status': mock_execution.status,
                    'start_time': mock_execution.start_time,
                    'end_time': mock_execution.end_time,
                    'duration_seconds': mock_execution.duration_seconds,
                    'results_count': mock_execution.results_count,
                    'error_message': mock_execution.error_message,
                    'results_data': mock_execution.results_data,
                    'created_at': mock_execution.created_at
                }
                
                mock_db.query.return_value.filter.return_value.first.return_value = mock_execution
                
                response = client.get(f"/api/v1/task-executions/{execution_id}", headers=auth_headers)
                
                assert response.status_code == 200
                response_data = response.json()
                assert response_data["scheduled_task_id"] is None
                assert response_data["scheduled_task_name"] is None
                assert response_data["trigger_type"] == "manual"

    def test_execution_with_error_message(self, client, auth_headers, mock_user):
        """测试包含错误信息的执行记录"""
        execution_id = 1
        
        with patch('app.api.endpoints.task_executions.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                # 创建失败的执行记录
                mock_execution = Mock()
                mock_execution.id = execution_id
                mock_execution.scheduled_task_id = 1
                mock_execution.trigger_type = TriggerType.SCHEDULED
                mock_execution.task_type = TaskType.INDICATOR_SCAN
                mock_execution.status = TaskStatus.FAILED
                mock_execution.start_time = datetime.now()
                mock_execution.end_time = datetime.now() + timedelta(minutes=1)
                mock_execution.duration_seconds = 60
                mock_execution.results_count = 0
                mock_execution.error_message = "扫描器连接超时"
                mock_execution.results_data = None
                mock_execution.created_at = datetime.now()
                
                # 模拟关联任务
                mock_scheduled_task = Mock()
                mock_scheduled_task.name = "失败测试任务"
                mock_execution.scheduled_task = mock_scheduled_task
                
                mock_execution.__dict__ = {
                    'id': mock_execution.id,
                    'scheduled_task_id': mock_execution.scheduled_task_id,
                    'trigger_type': mock_execution.trigger_type,
                    'task_type': mock_execution.task_type,
                    'status': mock_execution.status,
                    'start_time': mock_execution.start_time,
                    'end_time': mock_execution.end_time,
                    'duration_seconds': mock_execution.duration_seconds,
                    'results_count': mock_execution.results_count,
                    'error_message': mock_execution.error_message,
                    'results_data': mock_execution.results_data,
                    'created_at': mock_execution.created_at
                }
                
                mock_db.query.return_value.filter.return_value.first.return_value = mock_execution
                
                response = client.get(f"/api/v1/task-executions/{execution_id}", headers=auth_headers)
                
                assert response.status_code == 200
                response_data = response.json()
                assert response_data["status"] == "failed"
                assert response_data["error_message"] == "扫描器连接超时"
                assert response_data["results_count"] == 0

    def test_unauthorized_access(self, client):
        """测试未授权访问"""
        # 不提供认证头
        response = client.get("/api/v1/task-executions/")
        assert response.status_code in [401, 403]
        
        response = client.get("/api/v1/task-executions/1")
        assert response.status_code in [401, 403]
        
        response = client.delete("/api/v1/task-executions/1")
        assert response.status_code in [401, 403]

    def test_access_other_user_execution(self, client, auth_headers):
        """测试访问其他用户的执行记录"""
        execution_id = 999
        
        with patch('app.api.endpoints.task_executions.get_current_user') as mock_get_user:
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_user = Mock()
                mock_user.id = 1
                mock_get_user.return_value = mock_user
                
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                # 模拟查询不到属于当前用户的执行记录
                mock_db.query.return_value.filter.return_value.first.return_value = None
                
                response = client.get(f"/api/v1/task-executions/{execution_id}", headers=auth_headers)
                
                assert response.status_code == 404
                assert "执行记录不存在" in response.json()["detail"]

    def test_invalid_results_data_handling(self, client, auth_headers, mock_user):
        """测试无效结果数据的处理"""
        execution_id = 1
        
        with patch('app.api.endpoints.task_executions.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                # 创建包含无效JSON的执行记录
                mock_execution = Mock()
                mock_execution.id = execution_id
                mock_execution.scheduled_task_id = None
                mock_execution.scheduled_task = None
                mock_execution.trigger_type = TriggerType.MANUAL
                mock_execution.task_type = TaskType.INDICATOR_SCAN
                mock_execution.status = TaskStatus.COMPLETED
                mock_execution.start_time = datetime.now()
                mock_execution.end_time = datetime.now() + timedelta(minutes=1)
                mock_execution.duration_seconds = 60
                mock_execution.results_count = 0
                mock_execution.error_message = None
                mock_execution.results_data = "invalid_json_data"  # 无效JSON
                mock_execution.created_at = datetime.now()
                
                mock_execution.__dict__ = {
                    'id': mock_execution.id,
                    'scheduled_task_id': mock_execution.scheduled_task_id,
                    'trigger_type': mock_execution.trigger_type,
                    'task_type': mock_execution.task_type,
                    'status': mock_execution.status,
                    'start_time': mock_execution.start_time,
                    'end_time': mock_execution.end_time,
                    'duration_seconds': mock_execution.duration_seconds,
                    'results_count': mock_execution.results_count,
                    'error_message': mock_execution.error_message,
                    'results_data': mock_execution.results_data,
                    'created_at': mock_execution.created_at
                }
                
                mock_db.query.return_value.filter.return_value.first.return_value = mock_execution
                
                response = client.get(f"/api/v1/task-executions/{execution_id}", headers=auth_headers)
                
                assert response.status_code == 200
                response_data = response.json()
                # 无效JSON应该被处理为None
                assert response_data["results_data"] is None