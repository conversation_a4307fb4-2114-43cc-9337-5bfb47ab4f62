"""定时任务API集成测试"""

import pytest
import json
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from unittest.mock import patch, Mock, AsyncMock
from sqlalchemy.orm import Session

from app.main import app
from app.models.user import User
from app.models.task import UserScheduledTask, TaskExecution
from app.schemas.scheduled_task import TaskType, TaskStatus, TriggerType, IndicatorScanConfig


class TestScheduledTasksAPI:
    """定时任务API集成测试"""

    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)

    @pytest.fixture
    def auth_headers(self, mock_user):
        """创建认证头"""
        # 这里应该根据实际的认证机制创建有效的认证头
        return {"Authorization": f"Bearer test_token_for_user_{mock_user.id}"}

    def test_create_scheduled_task(self, client, auth_headers, mock_user, mock_indicator_config):
        """测试创建定时任务"""
        task_data = {
            "name": "测试定时任务",
            "task_type": "indicator_scan",
            "cron_expression": "0 9 * * 1-5",
            "task_config": mock_indicator_config.dict(),
            "description": "测试任务描述",
            "max_executions": 10
        }
        
        with patch('app.api.endpoints.scheduled_tasks.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.scheduled_tasks.get_db') as mock_get_db:
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                # 模拟任务创建
                created_task = Mock()
                created_task.id = 1
                created_task.name = task_data["name"]
                created_task.task_type = TaskType.INDICATOR_SCAN
                created_task.cron_expression = task_data["cron_expression"]
                created_task.is_active = True
                created_task.max_executions = task_data["max_executions"]
                created_task.current_executions = 0
                created_task.task_config = json.dumps(task_data["task_config"])
                created_task.description = task_data["description"]
                created_task.last_execution = None
                created_task.next_execution = datetime.now() + timedelta(hours=1)
                created_task.created_at = datetime.now()
                created_task.updated_at = datetime.now()
                created_task.__dict__ = {
                    'id': created_task.id,
                    'name': created_task.name,
                    'task_type': created_task.task_type,
                    'cron_expression': created_task.cron_expression,
                    'is_active': created_task.is_active,
                    'max_executions': created_task.max_executions,
                    'current_executions': created_task.current_executions,
                    'task_config': created_task.task_config,
                    'description': created_task.description,
                    'last_execution': created_task.last_execution,
                    'next_execution': created_task.next_execution,
                    'created_at': created_task.created_at,
                    'updated_at': created_task.updated_at
                }
                
                mock_db.add.return_value = None
                mock_db.commit.return_value = None
                mock_db.refresh.return_value = None
                mock_db.query.return_value.filter.return_value.count.return_value = 0  # 用户任务数量
                
                response = client.post("/api/v1/scheduled-tasks/", json=task_data, headers=auth_headers)
                
                assert response.status_code == 200
                response_data = response.json()
                assert response_data["name"] == task_data["name"]
                assert response_data["task_type"] == task_data["task_type"]
                assert response_data["cron_expression"] == task_data["cron_expression"]

    def test_create_task_invalid_cron(self, client, auth_headers, mock_user, mock_indicator_config):
        """测试创建任务时使用无效Cron表达式"""
        task_data = {
            "name": "测试任务",
            "task_type": "indicator_scan",
            "cron_expression": "invalid_cron",  # 无效表达式
            "task_config": mock_indicator_config.dict(),
        }
        
        with patch('app.api.endpoints.scheduled_tasks.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.scheduled_tasks.get_db'):
                response = client.post("/api/v1/scheduled-tasks/", json=task_data, headers=auth_headers)
                
                assert response.status_code == 400
                assert "无效的Cron表达式" in response.json()["detail"]

    def test_create_task_user_limit_exceeded(self, client, auth_headers, mock_user, mock_indicator_config):
        """测试用户任务数量限制"""
        task_data = {
            "name": "测试任务",
            "task_type": "indicator_scan", 
            "cron_expression": "0 9 * * *",
            "task_config": mock_indicator_config.dict(),
        }
        
        with patch('app.api.endpoints.scheduled_tasks.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.scheduled_tasks.get_db') as mock_get_db:
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                # 模拟用户已有20个任务
                mock_db.query.return_value.filter.return_value.count.return_value = 20
                
                response = client.post("/api/v1/scheduled-tasks/", json=task_data, headers=auth_headers)
                
                assert response.status_code == 400
                assert "数量已达上限" in response.json()["detail"]

    def test_list_scheduled_tasks(self, client, auth_headers, mock_user):
        """测试获取定时任务列表"""
        with patch('app.api.endpoints.scheduled_tasks.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.scheduled_tasks.get_db') as mock_get_db:
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                # 模拟任务列表
                mock_tasks = []
                for i in range(3):
                    mock_task = Mock()
                    mock_task.id = i + 1
                    mock_task.name = f"任务{i+1}"
                    mock_task.task_type = TaskType.INDICATOR_SCAN
                    mock_task.cron_expression = "0 9 * * *"
                    mock_task.is_active = True
                    mock_task.max_executions = None
                    mock_task.current_executions = 0
                    mock_task.task_config = '{"indicators": ["MACD"]}'
                    mock_task.description = f"任务{i+1}描述"
                    mock_task.last_execution = None
                    mock_task.next_execution = datetime.now() + timedelta(hours=1)
                    mock_task.created_at = datetime.now()
                    mock_task.updated_at = datetime.now()
                    mock_task.__dict__ = {
                        'id': mock_task.id,
                        'name': mock_task.name,
                        'task_type': mock_task.task_type,
                        'cron_expression': mock_task.cron_expression,
                        'is_active': mock_task.is_active,
                        'max_executions': mock_task.max_executions,
                        'current_executions': mock_task.current_executions,
                        'task_config': mock_task.task_config,
                        'description': mock_task.description,
                        'last_execution': mock_task.last_execution,
                        'next_execution': mock_task.next_execution,
                        'created_at': mock_task.created_at,
                        'updated_at': mock_task.updated_at
                    }
                    mock_tasks.append(mock_task)
                
                mock_db.query.return_value.filter.return_value.order_by.return_value.offset.return_value.limit.return_value.all.return_value = mock_tasks
                
                response = client.get("/api/v1/scheduled-tasks/", headers=auth_headers)
                
                assert response.status_code == 200
                response_data = response.json()
                assert len(response_data) == 3
                assert response_data[0]["name"] == "任务1"

    def test_get_scheduled_task_detail(self, client, auth_headers, mock_user, mock_scheduled_task):
        """测试获取定时任务详情"""
        task_id = mock_scheduled_task.id
        
        with patch('app.api.endpoints.scheduled_tasks.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.scheduled_tasks.get_db') as mock_get_db:
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                mock_scheduled_task.__dict__ = {
                    'id': mock_scheduled_task.id,
                    'name': mock_scheduled_task.name,
                    'task_type': mock_scheduled_task.task_type,
                    'cron_expression': mock_scheduled_task.cron_expression,
                    'is_active': mock_scheduled_task.is_active,
                    'max_executions': mock_scheduled_task.max_executions,
                    'current_executions': mock_scheduled_task.current_executions,
                    'task_config': mock_scheduled_task.task_config,
                    'description': mock_scheduled_task.description,
                    'last_execution': mock_scheduled_task.last_execution,
                    'next_execution': mock_scheduled_task.next_execution,
                    'created_at': mock_scheduled_task.created_at,
                    'updated_at': mock_scheduled_task.updated_at
                }
                
                mock_db.query.return_value.filter.return_value.first.return_value = mock_scheduled_task
                
                response = client.get(f"/api/v1/scheduled-tasks/{task_id}", headers=auth_headers)
                
                assert response.status_code == 200
                response_data = response.json()
                assert response_data["id"] == task_id
                assert response_data["name"] == mock_scheduled_task.name

    def test_get_nonexistent_task(self, client, auth_headers, mock_user):
        """测试获取不存在的任务"""
        with patch('app.api.endpoints.scheduled_tasks.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.scheduled_tasks.get_db') as mock_get_db:
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                mock_db.query.return_value.filter.return_value.first.return_value = None
                
                response = client.get("/api/v1/scheduled-tasks/999", headers=auth_headers)
                
                assert response.status_code == 404
                assert "任务不存在" in response.json()["detail"]

    def test_update_scheduled_task(self, client, auth_headers, mock_user, mock_scheduled_task):
        """测试更新定时任务"""
        task_id = mock_scheduled_task.id
        update_data = {
            "name": "更新后的任务名称",
            "cron_expression": "0 10 * * 1-5",
            "is_active": False
        }
        
        with patch('app.api.endpoints.scheduled_tasks.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.scheduled_tasks.get_db') as mock_get_db:
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                # 更新任务属性
                for key, value in update_data.items():
                    setattr(mock_scheduled_task, key, value)
                    
                mock_scheduled_task.__dict__.update(update_data)
                
                mock_db.query.return_value.filter.return_value.first.return_value = mock_scheduled_task
                
                response = client.put(f"/api/v1/scheduled-tasks/{task_id}", json=update_data, headers=auth_headers)
                
                assert response.status_code == 200
                response_data = response.json()
                assert response_data["name"] == update_data["name"]
                assert response_data["cron_expression"] == update_data["cron_expression"]
                assert response_data["is_active"] == update_data["is_active"]

    def test_update_task_with_invalid_cron(self, client, auth_headers, mock_user, mock_scheduled_task):
        """测试使用无效Cron表达式更新任务"""
        task_id = mock_scheduled_task.id
        update_data = {
            "cron_expression": "invalid_cron_expression"
        }
        
        with patch('app.api.endpoints.scheduled_tasks.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.scheduled_tasks.get_db') as mock_get_db:
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                mock_db.query.return_value.filter.return_value.first.return_value = mock_scheduled_task
                
                response = client.put(f"/api/v1/scheduled-tasks/{task_id}", json=update_data, headers=auth_headers)
                
                assert response.status_code == 400
                assert "无效的Cron表达式" in response.json()["detail"]

    def test_delete_scheduled_task(self, client, auth_headers, mock_user, mock_scheduled_task):
        """测试删除定时任务"""
        task_id = mock_scheduled_task.id
        
        with patch('app.api.endpoints.scheduled_tasks.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.scheduled_tasks.get_db') as mock_get_db:
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                mock_db.query.return_value.filter.return_value.first.return_value = mock_scheduled_task
                
                response = client.delete(f"/api/v1/scheduled-tasks/{task_id}", headers=auth_headers)
                
                assert response.status_code == 200
                assert "任务已删除" in response.json()["message"]
                
                # 验证删除操作被调用
                mock_db.delete.assert_called_once_with(mock_scheduled_task)
                mock_db.commit.assert_called()

    def test_manual_execute_task(self, client, auth_headers, mock_user, mock_scheduled_task):
        """测试手动执行任务"""
        task_id = mock_scheduled_task.id
        
        with patch('app.api.endpoints.scheduled_tasks.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.scheduled_tasks.get_db') as mock_get_db:
                with patch('app.api.endpoints.scheduled_tasks.get_task_executor') as mock_get_executor:
                    mock_db = Mock(spec=Session)
                    mock_get_db.return_value = mock_db
                    
                    mock_executor = Mock()
                    mock_executor.execute_task = AsyncMock()
                    mock_get_executor.return_value = mock_executor
                    
                    mock_db.query.return_value.filter.return_value.first.return_value = mock_scheduled_task
                    mock_db.query.return_value.filter.return_value.count.return_value = 0  # 无正在执行的任务
                    
                    # 模拟创建的执行记录
                    mock_execution = Mock()
                    mock_execution.id = 123
                    
                    response = client.post(f"/api/v1/scheduled-tasks/{task_id}/execute", headers=auth_headers)
                    
                    assert response.status_code == 200
                    response_data = response.json()
                    assert "任务已提交执行" in response_data["message"]
                    assert "execution_id" in response_data

    def test_manual_execute_task_concurrency_limit(self, client, auth_headers, mock_user, mock_scheduled_task):
        """测试手动执行任务的并发限制"""
        task_id = mock_scheduled_task.id
        
        with patch('app.api.endpoints.scheduled_tasks.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.scheduled_tasks.get_db') as mock_get_db:
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                mock_db.query.return_value.filter.return_value.first.return_value = mock_scheduled_task
                mock_db.query.return_value.filter.return_value.count.return_value = 3  # 已有3个并发任务
                
                response = client.post(f"/api/v1/scheduled-tasks/{task_id}/execute", headers=auth_headers)
                
                assert response.status_code == 400
                assert "太多正在执行的任务" in response.json()["detail"]

    def test_get_task_executions(self, client, auth_headers, mock_user, mock_scheduled_task):
        """测试获取任务执行记录"""
        task_id = mock_scheduled_task.id
        
        with patch('app.api.endpoints.scheduled_tasks.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.scheduled_tasks.get_db') as mock_get_db:
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                mock_db.query.return_value.filter.return_value.first.return_value = mock_scheduled_task
                
                # 模拟执行记录
                mock_executions = []
                for i in range(3):
                    mock_execution = Mock()
                    mock_execution.id = i + 1
                    mock_execution.scheduled_task_id = task_id
                    mock_execution.trigger_type = TriggerType.SCHEDULED
                    mock_execution.task_type = TaskType.INDICATOR_SCAN
                    mock_execution.status = TaskStatus.COMPLETED
                    mock_execution.start_time = datetime.now()
                    mock_execution.end_time = datetime.now() + timedelta(minutes=5)
                    mock_execution.duration_seconds = 300
                    mock_execution.results_count = 10
                    mock_execution.error_message = None
                    mock_execution.results_data = json.dumps([{"symbol": "000001.SZ", "signal": "buy"}])
                    mock_execution.created_at = datetime.now()
                    mock_execution.__dict__ = {
                        'id': mock_execution.id,
                        'scheduled_task_id': mock_execution.scheduled_task_id,
                        'trigger_type': mock_execution.trigger_type,
                        'task_type': mock_execution.task_type,
                        'status': mock_execution.status,
                        'start_time': mock_execution.start_time,
                        'end_time': mock_execution.end_time,
                        'duration_seconds': mock_execution.duration_seconds,
                        'results_count': mock_execution.results_count,
                        'error_message': mock_execution.error_message,
                        'results_data': mock_execution.results_data,
                        'created_at': mock_execution.created_at
                    }
                    mock_executions.append(mock_execution)
                
                mock_query = Mock()
                mock_query.filter.return_value = mock_query
                mock_query.count.return_value = len(mock_executions)
                mock_query.order_by.return_value.offset.return_value.limit.return_value.all.return_value = mock_executions
                mock_db.query.return_value = mock_query
                
                response = client.get(f"/api/v1/scheduled-tasks/{task_id}/executions", headers=auth_headers)
                
                assert response.status_code == 200
                response_data = response.json()
                assert response_data["total"] == len(mock_executions)
                assert len(response_data["items"]) == len(mock_executions)
                
                # 验证执行记录数据
                first_execution = response_data["items"][0]
                assert first_execution["scheduled_task_name"] == mock_scheduled_task.name

    def test_toggle_task_status(self, client, auth_headers, mock_user, mock_scheduled_task):
        """测试切换任务状态"""
        task_id = mock_scheduled_task.id
        
        with patch('app.api.endpoints.scheduled_tasks.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.scheduled_tasks.get_db') as mock_get_db:
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                mock_db.query.return_value.filter.return_value.first.return_value = mock_scheduled_task
                
                # 测试禁用任务
                response = client.put(f"/api/v1/scheduled-tasks/{task_id}/toggle?is_active=false", headers=auth_headers)
                
                assert response.status_code == 200
                assert "任务已禁用" in response.json()["message"]
                assert mock_scheduled_task.is_active is False
                assert mock_scheduled_task.next_execution is None
                
                # 测试启用任务
                mock_scheduled_task.is_active = True
                response = client.put(f"/api/v1/scheduled-tasks/{task_id}/toggle?is_active=true", headers=auth_headers)
                
                assert response.status_code == 200
                assert "任务已启用" in response.json()["message"]

    def test_unauthorized_access(self, client):
        """测试未授权访问"""
        # 不提供认证头
        response = client.get("/api/v1/scheduled-tasks/")
        assert response.status_code in [401, 403]  # 根据认证机制可能返回401或403

    def test_access_other_user_task(self, client, auth_headers):
        """测试访问其他用户的任务"""
        other_user_task_id = 999
        
        with patch('app.api.endpoints.scheduled_tasks.get_current_user') as mock_get_user:
            with patch('app.api.endpoints.scheduled_tasks.get_db') as mock_get_db:
                mock_user = Mock()
                mock_user.id = 1
                mock_get_user.return_value = mock_user
                
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                # 模拟查询不到属于当前用户的任务
                mock_db.query.return_value.filter.return_value.first.return_value = None
                
                response = client.get(f"/api/v1/scheduled-tasks/{other_user_task_id}", headers=auth_headers)
                
                assert response.status_code == 404
                assert "任务不存在" in response.json()["detail"]


class TestTaskExecutionAPI:
    """任务执行记录API测试"""

    @pytest.fixture
    def client(self):
        return TestClient(app)

    @pytest.fixture
    def auth_headers(self, mock_user):
        return {"Authorization": f"Bearer test_token_for_user_{mock_user.id}"}

    def test_get_all_task_executions(self, client, auth_headers, mock_user):
        """测试获取所有任务执行记录"""
        # 这个测试需要根据实际的task_executions端点实现
        # 当前代码中似乎没有这个端点，可能需要添加
        pass

    def test_get_task_execution_detail(self, client, auth_headers, mock_user):
        """测试获取任务执行记录详情"""
        # 这个测试需要根据实际的task_executions详情端点实现
        pass

    def test_delete_task_execution(self, client, auth_headers, mock_user):
        """测试删除任务执行记录"""
        # 这个测试需要根据实际的删除执行记录端点实现
        pass


class TestAPIValidationAndErrorHandling:
    """API验证和错误处理测试"""

    @pytest.fixture
    def client(self):
        return TestClient(app)

    def test_invalid_json_request(self, client, auth_headers, mock_user):
        """测试无效JSON请求"""
        with patch('app.api.endpoints.scheduled_tasks.get_current_user', return_value=mock_user):
            response = client.post(
                "/api/v1/scheduled-tasks/",
                data="invalid_json",  # 不是有效的JSON
                headers=auth_headers
            )
            
            assert response.status_code == 422  # Unprocessable Entity

    def test_missing_required_fields(self, client, auth_headers, mock_user):
        """测试缺少必需字段"""
        with patch('app.api.endpoints.scheduled_tasks.get_current_user', return_value=mock_user):
            # 缺少必需的task_config字段
            incomplete_data = {
                "name": "测试任务",
                "cron_expression": "0 9 * * *"
                # 缺少task_config
            }
            
            response = client.post("/api/v1/scheduled-tasks/", json=incomplete_data, headers=auth_headers)
            
            assert response.status_code == 422

    def test_field_validation_errors(self, client, auth_headers, mock_user):
        """测试字段验证错误"""
        with patch('app.api.endpoints.scheduled_tasks.get_current_user', return_value=mock_user):
            # 名称过长
            invalid_data = {
                "name": "x" * 101,  # 超过100字符限制
                "task_type": "indicator_scan",
                "cron_expression": "0 9 * * *",
                "task_config": {
                    "indicators": ["MACD"],
                    "stock_codes": None,
                    "parameters": None,
                    "scan_mode": "traditional",
                    "periods": ["d"],
                    "adjust": "n"
                }
            }
            
            response = client.post("/api/v1/scheduled-tasks/", json=invalid_data, headers=auth_headers)
            
            assert response.status_code == 422

    def test_invalid_enum_values(self, client, auth_headers, mock_user):
        """测试无效枚举值"""
        with patch('app.api.endpoints.scheduled_tasks.get_current_user', return_value=mock_user):
            invalid_data = {
                "name": "测试任务",
                "task_type": "invalid_task_type",  # 无效的任务类型
                "cron_expression": "0 9 * * *",
                "task_config": {
                    "indicators": ["MACD"],
                    "scan_mode": "traditional",
                    "periods": ["d"],
                    "adjust": "n"
                }
            }
            
            response = client.post("/api/v1/scheduled-tasks/", json=invalid_data, headers=auth_headers)
            
            assert response.status_code == 422

    def test_pagination_parameters(self, client, auth_headers, mock_user):
        """测试分页参数"""
        with patch('app.api.endpoints.scheduled_tasks.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.scheduled_tasks.get_db') as mock_get_db:
                mock_db = Mock(spec=Session)
                mock_get_db.return_value = mock_db
                
                mock_db.query.return_value.filter.return_value.order_by.return_value.offset.return_value.limit.return_value.all.return_value = []
                
                # 测试有效分页参数
                response = client.get("/api/v1/scheduled-tasks/?skip=10&limit=50", headers=auth_headers)
                assert response.status_code == 200
                
                # 测试无效分页参数
                response = client.get("/api/v1/scheduled-tasks/?skip=-1&limit=0", headers=auth_headers)
                assert response.status_code == 422