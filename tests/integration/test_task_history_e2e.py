"""任务历史页面端到端测试"""

import pytest
import json
import asyncio
from datetime import datetime, timedelta
from unittest.mock import patch, Mock, AsyncMock
from fastapi.testclient import TestClient

from app.main import app
from app.models.user import User
from app.models.task import TaskExecution, UserScheduledTask
from app.schemas.scheduled_task import TaskType, TaskStatus, TriggerType


class TestTaskHistoryE2E:
    """任务历史页面端到端测试"""

    @pytest.fixture
    def client(self):
        return TestClient(app)

    @pytest.fixture
    def mock_user(self):
        """普通用户"""
        user = Mock(spec=User)
        user.id = 1
        user.username = "testuser"
        user.email = "<EMAIL>"
        user.is_admin = False
        return user

    @pytest.fixture
    def mock_admin(self):
        """管理员用户"""
        admin = Mock(spec=User)
        admin.id = 2
        admin.username = "admin"
        admin.email = "<EMAIL>"
        admin.is_admin = True
        return admin

    @pytest.fixture
    def user_headers(self, mock_user):
        return {"Authorization": f"Bearer token_user_{mock_user.id}"}

    @pytest.fixture
    def admin_headers(self, mock_admin):
        return {"Authorization": f"Bearer token_admin_{mock_admin.id}"}

    def create_mock_execution(self, exec_id, user_id, trigger_type, status, 
                            task_name=None, error_msg=None, results_count=0):
        """创建模拟执行记录的辅助方法"""
        mock_execution = Mock()
        mock_execution.id = exec_id
        mock_execution.user_id = user_id
        mock_execution.trigger_type = trigger_type
        mock_execution.task_type = TaskType.INDICATOR_SCAN
        mock_execution.status = status
        mock_execution.start_time = datetime.now() - timedelta(minutes=10)
        mock_execution.end_time = datetime.now() - timedelta(minutes=5) if status in [TaskStatus.COMPLETED, TaskStatus.FAILED] else None
        mock_execution.duration_seconds = 300 if mock_execution.end_time else None
        mock_execution.results_count = results_count
        mock_execution.error_message = error_msg
        mock_execution.results_data = json.dumps([{"symbol": "000001.SZ"}]) if results_count > 0 else None
        mock_execution.task_config = json.dumps({"indicators": ["MACD", "KDJ"]})
        mock_execution.created_at = datetime.now() - timedelta(minutes=15)
        
        if trigger_type == TriggerType.SCHEDULED and task_name:
            mock_execution.scheduled_task_id = exec_id
            mock_task = Mock()
            mock_task.name = task_name
            mock_execution.scheduled_task = mock_task
        else:
            mock_execution.scheduled_task_id = None
            mock_execution.scheduled_task = None

        mock_execution.__dict__ = {
            'id': mock_execution.id,
            'user_id': mock_execution.user_id,
            'scheduled_task_id': mock_execution.scheduled_task_id,
            'trigger_type': mock_execution.trigger_type,
            'task_type': mock_execution.task_type,
            'status': mock_execution.status,
            'start_time': mock_execution.start_time,
            'end_time': mock_execution.end_time,
            'duration_seconds': mock_execution.duration_seconds,
            'results_count': mock_execution.results_count,
            'error_message': mock_execution.error_message,
            'results_data': mock_execution.results_data,
            'task_config': mock_execution.task_config,
            'created_at': mock_execution.created_at
        }
        
        return mock_execution

    def test_user_view_own_task_history(self, client, user_headers, mock_user):
        """测试用户查看自己的任务历史"""
        with patch('app.api.endpoints.task_executions.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock()
                mock_get_db.return_value.__aenter__.return_value = mock_db
                
                # 创建用户的执行记录
                executions = [
                    self.create_mock_execution(1, mock_user.id, TriggerType.SCHEDULED, TaskStatus.COMPLETED, "每日MACD扫描", None, 15),
                    self.create_mock_execution(2, mock_user.id, TriggerType.MANUAL, TaskStatus.COMPLETED, None, None, 8),
                    self.create_mock_execution(3, mock_user.id, TriggerType.SCHEDULED, TaskStatus.FAILED, "KDJ金叉扫描", "数据源连接失败", 0),
                    self.create_mock_execution(4, mock_user.id, TriggerType.MANUAL, TaskStatus.RUNNING, None, None, 0)
                ]
                
                # 模拟数据库查询
                mock_result = Mock()
                mock_result.scalars.return_value.all.return_value = executions
                mock_count_result = Mock()
                mock_count_result.scalar.return_value = len(executions)
                mock_db.execute.side_effect = [mock_count_result, mock_result]
                
                response = client.get("/api/v1/task-executions/", headers=user_headers)
                
                assert response.status_code == 200
                data = response.json()
                assert data["total"] == 4
                assert len(data["items"]) == 4
                
                # 验证不同类型的任务记录
                scheduled_tasks = [item for item in data["items"] if item["trigger_type"] == "scheduled"]
                manual_tasks = [item for item in data["items"] if item["trigger_type"] == "manual"]
                
                assert len(scheduled_tasks) == 2
                assert len(manual_tasks) == 2
                
                # 验证定时任务有任务名称
                for task in scheduled_tasks:
                    assert task["scheduled_task_name"] is not None
                
                # 验证手动任务没有任务名称
                for task in manual_tasks:
                    assert task["scheduled_task_name"] is None

    def test_admin_view_all_users_task_history(self, client, admin_headers, mock_admin):
        """测试管理员查看所有用户的任务历史"""
        with patch('app.api.endpoints.task_executions.get_current_admin_user', return_value=mock_admin):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock()
                mock_get_db.return_value.__aenter__.return_value = mock_db
                
                # 创建多个用户的执行记录
                executions = []
                
                # 用户1的记录
                user1_execution = self.create_mock_execution(1, 1, TriggerType.SCHEDULED, TaskStatus.COMPLETED, "用户1任务", None, 10)
                mock_user1 = Mock()
                mock_user1.username = "user1"
                mock_user1.email = "<EMAIL>"
                user1_execution.user = mock_user1
                executions.append(user1_execution)
                
                # 用户2的记录
                user2_execution = self.create_mock_execution(2, 2, TriggerType.MANUAL, TaskStatus.FAILED, None, "执行超时", 0)
                mock_user2 = Mock()
                mock_user2.username = "user2"  
                mock_user2.email = "<EMAIL>"
                user2_execution.user = mock_user2
                executions.append(user2_execution)
                
                # 管理员自己的记录
                admin_execution = self.create_mock_execution(3, mock_admin.id, TriggerType.SCHEDULED, TaskStatus.RUNNING, "管理员监控任务", None, 0)
                admin_execution.user = mock_admin
                executions.append(admin_execution)
                
                # 模拟数据库查询
                mock_result = Mock()
                mock_result.scalars.return_value.all.return_value = executions
                mock_count_result = Mock()
                mock_count_result.scalar.return_value = len(executions)
                mock_db.execute.side_effect = [mock_count_result, mock_result]
                
                response = client.get("/api/v1/task-executions/admin/all", headers=admin_headers)
                
                assert response.status_code == 200
                data = response.json()
                assert data["total"] == 3
                assert len(data["items"]) == 3
                
                # 验证包含用户信息
                usernames = {item["user_username"] for item in data["items"]}
                assert "user1" in usernames
                assert "user2" in usernames
                assert "admin" in usernames
                
                # 验证每条记录都有用户信息
                for item in data["items"]:
                    assert "user_username" in item
                    assert "user_email" in item
                    assert item["user_username"] is not None
                    assert item["user_email"] is not None

    def test_task_execution_filtering_workflow(self, client, user_headers, mock_user):
        """测试任务执行记录过滤工作流"""
        with patch('app.api.endpoints.task_executions.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock()
                mock_get_db.return_value.__aenter__.return_value = mock_db
                
                # 设置不同的模拟数据来测试过滤
                all_executions = [
                    self.create_mock_execution(1, mock_user.id, TriggerType.MANUAL, TaskStatus.COMPLETED, None, None, 5),
                    self.create_mock_execution(2, mock_user.id, TriggerType.SCHEDULED, TaskStatus.COMPLETED, "定时任务", None, 10),
                    self.create_mock_execution(3, mock_user.id, TriggerType.MANUAL, TaskStatus.FAILED, None, "连接失败", 0),
                    self.create_mock_execution(4, mock_user.id, TriggerType.SCHEDULED, TaskStatus.RUNNING, "运行中任务", None, 0)
                ]
                
                def setup_mock_query(filter_executions, total_count):
                    mock_result = Mock()
                    mock_result.scalars.return_value.all.return_value = filter_executions
                    mock_count_result = Mock()
                    mock_count_result.scalar.return_value = total_count
                    mock_db.execute.side_effect = [mock_count_result, mock_result]
                
                # 测试1: 过滤手动触发的任务
                manual_executions = [ex for ex in all_executions if ex.trigger_type == TriggerType.MANUAL]
                setup_mock_query(manual_executions, len(manual_executions))
                
                response = client.get("/api/v1/task-executions/?trigger_type=manual", headers=user_headers)
                assert response.status_code == 200
                data = response.json()
                assert data["total"] == 2
                for item in data["items"]:
                    assert item["trigger_type"] == "manual"
                
                # 测试2: 过滤失败状态的任务
                failed_executions = [ex for ex in all_executions if ex.status == TaskStatus.FAILED]
                setup_mock_query(failed_executions, len(failed_executions))
                
                response = client.get("/api/v1/task-executions/?status=failed", headers=user_headers)
                assert response.status_code == 200
                data = response.json()
                assert data["total"] == 1
                for item in data["items"]:
                    assert item["status"] == "failed"
                    assert item["error_message"] is not None
                
                # 测试3: 复合过滤条件
                manual_completed = [ex for ex in all_executions 
                                 if ex.trigger_type == TriggerType.MANUAL and ex.status == TaskStatus.COMPLETED]
                setup_mock_query(manual_completed, len(manual_completed))
                
                response = client.get("/api/v1/task-executions/?trigger_type=manual&status=completed", headers=user_headers)
                assert response.status_code == 200
                data = response.json()
                assert data["total"] == 1
                item = data["items"][0]
                assert item["trigger_type"] == "manual"
                assert item["status"] == "completed"

    def test_task_execution_details_workflow(self, client, user_headers, mock_user):
        """测试任务执行详情查看工作流"""
        execution_id = 1
        
        with patch('app.api.endpoints.task_executions.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock()
                mock_get_db.return_value.__aenter__.return_value = mock_db
                
                # 创建详细的执行记录
                mock_execution = self.create_mock_execution(
                    execution_id, mock_user.id, TriggerType.SCHEDULED, TaskStatus.COMPLETED, 
                    "MACD金叉策略扫描", None, 25
                )
                
                # 添加详细的results_data
                detailed_results = [
                    {"symbol": "000001.SZ", "name": "平安银行", "signal": "buy", "strength": 0.85, "price": 12.34},
                    {"symbol": "000002.SZ", "name": "万科A", "signal": "buy", "strength": 0.78, "price": 23.45},
                    {"symbol": "600000.SH", "name": "浦发银行", "signal": "sell", "strength": 0.92, "price": 8.76}
                ]
                mock_execution.results_data = json.dumps(detailed_results, ensure_ascii=False)
                mock_execution.__dict__['results_data'] = mock_execution.results_data
                
                # 模拟数据库查询
                mock_result = Mock()
                mock_result.scalar_one_or_none.return_value = mock_execution
                mock_db.execute.return_value = mock_result
                
                response = client.get(f"/api/v1/task-executions/{execution_id}", headers=user_headers)
                
                assert response.status_code == 200
                data = response.json()
                
                # 验证基本信息
                assert data["id"] == execution_id
                assert data["trigger_type"] == "scheduled"
                assert data["status"] == "completed"
                assert data["scheduled_task_name"] == "MACD金叉策略扫描"
                assert data["results_count"] == 25
                
                # 验证结果数据被正确解析
                assert data["results_data"] is not None
                assert isinstance(data["results_data"], list)
                assert len(data["results_data"]) == 3
                
                # 验证结果数据内容
                first_result = data["results_data"][0]
                assert first_result["symbol"] == "000001.SZ"
                assert first_result["signal"] == "buy"
                assert first_result["strength"] == 0.85

    def test_task_cancellation_workflow(self, client, user_headers, mock_user):
        """测试任务取消工作流"""
        execution_id = 1
        
        with patch('app.api.endpoints.task_executions.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                with patch('app.api.endpoints.task_executions.get_task_executor') as mock_get_executor:
                    mock_db = Mock()
                    mock_get_db.return_value.__aenter__.return_value = mock_db
                    
                    # 创建正在运行的执行记录
                    mock_execution = self.create_mock_execution(
                        execution_id, mock_user.id, TriggerType.MANUAL, TaskStatus.RUNNING, None, None, 0
                    )
                    
                    # 模拟查询执行记录
                    mock_result = Mock()
                    mock_result.scalar_one_or_none.return_value = mock_execution
                    mock_db.execute.return_value = mock_result
                    
                    # 模拟任务执行器
                    mock_executor = Mock()
                    mock_executor.cancel_execution.return_value = True
                    mock_get_executor.return_value = mock_executor
                    
                    # 模拟数据库提交
                    mock_db.commit = Mock()
                    
                    # 发起取消请求
                    response = client.post(f"/api/v1/task-executions/{execution_id}/cancel", headers=user_headers)
                    
                    assert response.status_code == 200
                    data = response.json()
                    assert "任务取消成功" in data["message"]
                    assert data["cancelled"] is True
                    
                    # 验证状态被更新
                    assert mock_execution.status == TaskStatus.CANCELLED
                    assert mock_execution.end_time is not None
                    mock_db.commit.assert_called()

    def test_task_deletion_workflow(self, client, user_headers, mock_user):
        """测试任务删除工作流"""
        execution_id = 1
        
        with patch('app.api.endpoints.task_executions.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock()
                mock_get_db.return_value.__aenter__.return_value = mock_db
                
                # 创建已完成的执行记录
                mock_execution = self.create_mock_execution(
                    execution_id, mock_user.id, TriggerType.SCHEDULED, TaskStatus.COMPLETED, "测试任务", None, 10
                )
                
                # 模拟查询执行记录
                mock_result = Mock()
                mock_result.scalar_one_or_none.return_value = mock_execution
                mock_db.execute.return_value = mock_result
                
                # 模拟删除操作
                mock_db.delete = Mock()
                mock_db.commit = Mock()
                
                response = client.delete(f"/api/v1/task-executions/{execution_id}", headers=user_headers)
                
                assert response.status_code == 200
                data = response.json()
                assert "执行记录已删除" in data["message"]
                
                # 验证删除操作
                mock_db.delete.assert_called_once_with(mock_execution)
                mock_db.commit.assert_called()

    def test_error_recovery_scenarios(self, client, user_headers, mock_user):
        """测试错误恢复场景"""
        # 测试场景1: 查看不存在的执行记录
        with patch('app.api.endpoints.task_executions.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock()
                mock_get_db.return_value.__aenter__.return_value = mock_db
                
                mock_result = Mock()
                mock_result.scalar_one_or_none.return_value = None
                mock_db.execute.return_value = mock_result
                
                response = client.get("/api/v1/task-executions/999", headers=user_headers)
                assert response.status_code == 404
                assert "执行记录不存在" in response.json()["detail"]

        # 测试场景2: 尝试删除正在运行的任务
        with patch('app.api.endpoints.task_executions.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock()
                mock_get_db.return_value.__aenter__.return_value = mock_db
                
                # 创建正在运行的执行记录
                running_execution = self.create_mock_execution(
                    1, mock_user.id, TriggerType.MANUAL, TaskStatus.RUNNING, None, None, 0
                )
                
                mock_result = Mock()
                mock_result.scalar_one_or_none.return_value = running_execution
                mock_db.execute.return_value = mock_result
                
                response = client.delete("/api/v1/task-executions/1", headers=user_headers)
                assert response.status_code == 400
                assert "不能删除正在执行的任务记录" in response.json()["detail"]

        # 测试场景3: 尝试取消已完成的任务
        with patch('app.api.endpoints.task_executions.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock()
                mock_get_db.return_value.__aenter__.return_value = mock_db
                
                # 创建已完成的执行记录
                completed_execution = self.create_mock_execution(
                    1, mock_user.id, TriggerType.SCHEDULED, TaskStatus.COMPLETED, "已完成任务", None, 5
                )
                
                mock_result = Mock()
                mock_result.scalar_one_or_none.return_value = completed_execution
                mock_db.execute.return_value = mock_result
                
                response = client.post("/api/v1/task-executions/1/cancel", headers=user_headers)
                assert response.status_code == 400
                assert "只能取消待执行或正在执行的任务" in response.json()["detail"]

    def test_pagination_and_performance(self, client, user_headers, mock_user):
        """测试分页和性能场景"""
        with patch('app.api.endpoints.task_executions.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock()
                mock_get_db.return_value.__aenter__.return_value = mock_db
                
                # 模拟大量数据
                total_records = 1000
                page_size = 50
                current_page_records = []
                
                for i in range(page_size):
                    execution = self.create_mock_execution(
                        i + 1, mock_user.id, 
                        TriggerType.SCHEDULED if i % 2 == 0 else TriggerType.MANUAL,
                        TaskStatus.COMPLETED,
                        f"批量任务{i+1}" if i % 2 == 0 else None,
                        None, i + 1
                    )
                    current_page_records.append(execution)
                
                # 模拟分页查询
                mock_result = Mock()
                mock_result.scalars.return_value.all.return_value = current_page_records
                mock_count_result = Mock()
                mock_count_result.scalar.return_value = total_records
                mock_db.execute.side_effect = [mock_count_result, mock_result]
                
                # 测试第一页
                response = client.get(f"/api/v1/task-executions/?skip=0&limit={page_size}", headers=user_headers)
                assert response.status_code == 200
                data = response.json()
                assert data["total"] == total_records
                assert len(data["items"]) == page_size
                
                # 重置mock副作用用于下一个请求
                mock_db.execute.side_effect = [mock_count_result, mock_result]
                
                # 测试第二页
                response = client.get(f"/api/v1/task-executions/?skip={page_size}&limit={page_size}", headers=user_headers)
                assert response.status_code == 200
                data = response.json()
                assert data["total"] == total_records
                assert len(data["items"]) == page_size

    def test_mixed_execution_types_integration(self, client, user_headers, mock_user):
        """测试定时任务和手动扫描的统一显示"""
        with patch('app.api.endpoints.task_executions.get_current_user', return_value=mock_user):
            with patch('app.api.endpoints.task_executions.get_db') as mock_get_db:
                mock_db = Mock()
                mock_get_db.return_value.__aenter__.return_value = mock_db
                
                # 创建混合类型的执行记录
                mixed_executions = [
                    # 定时任务执行记录
                    self.create_mock_execution(1, mock_user.id, TriggerType.SCHEDULED, TaskStatus.COMPLETED, "每日MACD扫描", None, 20),
                    self.create_mock_execution(2, mock_user.id, TriggerType.SCHEDULED, TaskStatus.FAILED, "RSI超买扫描", "网络超时", 0),
                    
                    # 手动扫描执行记录  
                    self.create_mock_execution(3, mock_user.id, TriggerType.MANUAL, TaskStatus.COMPLETED, None, None, 15),
                    self.create_mock_execution(4, mock_user.id, TriggerType.MANUAL, TaskStatus.RUNNING, None, None, 0),
                    
                    # 定时任务手动触发记录
                    self.create_mock_execution(5, mock_user.id, TriggerType.MANUAL, TaskStatus.COMPLETED, "KDJ策略", None, 8)
                ]
                
                # 设置最后一个记录有关联的定时任务（手动触发定时任务）
                mixed_executions[4].scheduled_task_id = 1
                mock_task = Mock()
                mock_task.name = "KDJ策略"
                mixed_executions[4].scheduled_task = mock_task
                
                mock_result = Mock()
                mock_result.scalars.return_value.all.return_value = mixed_executions
                mock_count_result = Mock()
                mock_count_result.scalar.return_value = len(mixed_executions)
                mock_db.execute.side_effect = [mock_count_result, mock_result]
                
                response = client.get("/api/v1/task-executions/", headers=user_headers)
                
                assert response.status_code == 200
                data = response.json()
                assert data["total"] == 5
                
                # 分析不同类型的记录
                scheduled_records = [item for item in data["items"] if item["trigger_type"] == "scheduled"]
                manual_records = [item for item in data["items"] if item["trigger_type"] == "manual"]
                
                assert len(scheduled_records) == 2  # 定时触发
                assert len(manual_records) == 3     # 手动触发（包括手动触发的定时任务）
                
                # 验证定时触发的记录都有任务名称
                for record in scheduled_records:
                    assert record["scheduled_task_name"] is not None
                
                # 验证手动记录中，有些有任务名称（手动触发的定时任务），有些没有（纯手动扫描）
                manual_with_task = [r for r in manual_records if r["scheduled_task_name"] is not None]
                manual_without_task = [r for r in manual_records if r["scheduled_task_name"] is None]
                
                assert len(manual_with_task) == 1   # 手动触发的定时任务
                assert len(manual_without_task) == 2 # 纯手动扫描
                
                # 验证状态分布
                completed_count = len([r for r in data["items"] if r["status"] == "completed"])
                failed_count = len([r for r in data["items"] if r["status"] == "failed"])
                running_count = len([r for r in data["items"] if r["status"] == "running"])
                
                assert completed_count == 3
                assert failed_count == 1
                assert running_count == 1