"""
数据保护和敏感信息处理测试

测试系统对敏感数据的保护和隐私安全
"""
import pytest
import asyncio
import re
import json
import hashlib
from typing import Dict, List, Any, Optional
import aiohttp
from unittest.mock import patch, AsyncMock

from tests.security.conftest import (
    security_test,
    owasp_test,
    SecurityTestHelper
)


class TestDataEncryption:
    """数据加密测试"""
    
    @security_test("Data_At_Rest")
    async def test_sensitive_data_encryption(
        self,
        performance_data_manager
    ):
        """测试静态数据加密"""
        sensitive_data = {
            "api_key": "sk-1234567890abcdef",
            "secret_token": "secret_token_12345",
            "password": "UserPassword123!",
            "private_key": "-----BEGIN PRIVATE KEY-----\\nMIIEvQIBADANBg...",
            "credit_card": "4111-1111-1111-1111",
        }
        
        # 测试敏感数据是否被加密存储
        for field_name, sensitive_value in sensitive_data.items():
            try:
                # 假设有配置更新功能
                if hasattr(performance_data_manager, 'update_config'):
                    await performance_data_manager.update_config(
                        key=field_name,
                        value=sensitive_value
                    )
                    
                    # 获取存储的值
                    stored_value = await performance_data_manager.get_config(key=field_name)
                    
                    # 敏感数据不应该以明文存储
                    assert stored_value != sensitive_value, (
                        f"敏感数据 {field_name} 以明文存储"
                    )
                    
                    # 存储的值应该看起来像加密或哈希后的数据
                    # 检查是否包含明显的加密特征
                    encryption_indicators = [
                        len(stored_value) > len(sensitive_value),  # 加密后通常更长
                        not stored_value.isalnum(),  # 包含特殊字符
                        stored_value != stored_value.lower(),  # 大小写混合
                    ]
                    
                    assert sum(encryption_indicators) >= 2, (
                        f"存储的 {field_name} 不像加密数据: {stored_value[:20]}..."
                    )
            
            except Exception as e:
                # 如果不支持该功能，跳过
                continue
        
        print(f"\\n静态数据加密测试:")
        print(f"测试敏感字段数量: {len(sensitive_data)}")
        print("所有敏感数据都已适当保护")
    
    @security_test("Transit_Encryption")
    async def test_data_in_transit_encryption(
        self,
        security_test_client: aiohttp.ClientSession
    ):
        """测试传输中数据加密（HTTPS）"""
        endpoints_to_test = [
            "/api/v1/auth/login",
            "/api/v1/user/profile",
            "/api/v1/stocks/",
            "/api/v1/admin/config",
        ]
        
        for endpoint in endpoints_to_test:
            # 测试HTTP连接是否被重定向到HTTPS
            try:
                async with security_test_client.get(
                    f"http://localhost:8000{endpoint}",
                    allow_redirects=False
                ) as response:
                    
                    # 应该重定向到HTTPS或返回安全错误
                    if response.status in [301, 302, 307, 308]:
                        location = response.headers.get('Location', '')
                        assert location.startswith('https://'), (
                            f"HTTP不安全重定向: {location}"
                        )
                    elif response.status == 400:
                        # 拒绝HTTP连接也是可以接受的
                        pass
                    else:
                        # 如果接受HTTP连接，至少应该有安全警告
                        content = await response.text()
                        security_warnings = ['upgrade', 'https', 'secure', 'ssl', 'tls']
                        has_warning = any(warning in content.lower() for warning in security_warnings)
                        
                        if not has_warning:
                            print(f"警告: {endpoint} 接受不安全的HTTP连接")
            
            except aiohttp.ClientError:
                # 连接错误是可以接受的
                continue
        
        print(f"\\n传输加密测试:")
        print(f"测试端点数量: {len(endpoints_to_test)}")
        print("传输安全检查完成")


class TestSensitiveDataExposure:
    """敏感数据暴露测试"""
    
    @owasp_test("A02_Cryptographic_Failures")
    async def test_password_exposure_in_responses(
        self,
        security_test_client: aiohttp.ClientSession
    ):
        """测试API响应中的密码暴露"""
        # 尝试创建用户并检查响应
        try:
            response = await security_test_client.post(
                "http://localhost:8000/api/v1/auth/register",
                json={
                    "username": "testuser123",
                    "password": "SecretPassword123!",
                    "email": "<EMAIL>"
                }
            )
            
            if response.status in [200, 201]:
                response_text = await response.text()
                
                # 响应中不应该包含密码
                sensitive_patterns = [
                    "SecretPassword123!",
                    "password.*SecretPassword",
                    '"password"\\s*:\\s*"[^"]*"',
                ]
                
                for pattern in sensitive_patterns:
                    assert not re.search(pattern, response_text, re.IGNORECASE), (
                        f"API响应中暴露了密码: {pattern}"
                    )
        
        except aiohttp.ClientError:
            # 如果接口不存在，跳过测试
            pass
        
        # 测试用户信息接口
        try:
            response = await security_test_client.get(
                "http://localhost:8000/api/v1/users/profile",
                headers={"Authorization": "Bearer test_token"}
            )
            
            if response.status == 200:
                response_text = await response.text()
                
                # 检查是否暴露了密码相关信息
                password_fields = ['password', 'passwd', 'pwd', 'secret', 'hash']
                for field in password_fields:
                    pattern = f'"{field}"\\s*:\\s*"[^"]*"'
                    if re.search(pattern, response_text, re.IGNORECASE):
                        field_value = re.search(pattern, response_text, re.IGNORECASE).group()
                        # 如果包含密码字段，值应该被屏蔽或为空
                        assert '***' in field_value or 'null' in field_value, (
                            f"用户信息中暴露了敏感字段: {field}"
                        )
        
        except aiohttp.ClientError:
            pass
        
        print(f"\\n密码暴露测试:")
        print("✓ API响应中未发现密码暴露")
    
    @security_test("API_Key_Exposure")
    async def test_api_key_exposure(
        self,
        security_test_client: aiohttp.ClientSession
    ):
        """测试API密钥暴露"""
        # 检查配置接口是否暴露API密钥
        sensitive_endpoints = [
            "/api/v1/config",
            "/api/v1/system/info",
            "/api/v1/admin/settings",
            "/api/v1/.env",
            "/api/v1/debug/config",
        ]
        
        api_key_patterns = [
            r'["\']?api[_-]?key["\']?\\s*[:=]\\s*["\']([^"\'\\s]+)["\']',
            r'["\']?token["\']?\\s*[:=]\\s*["\']([^"\'\\s]+)["\']',
            r'["\']?secret["\']?\\s*[:=]\\s*["\']([^"\'\\s]+)["\']',
            r'sk-[a-zA-Z0-9]{32,}',  # OpenAI风格的密钥
            r'AIza[0-9A-Za-z-_]{35}',  # Google API密钥
        ]
        
        for endpoint in sensitive_endpoints:
            try:
                response = await security_test_client.get(
                    f"http://localhost:8000{endpoint}"
                )
                
                if response.status == 200:
                    response_text = await response.text()
                    
                    # 检查是否暴露了API密钥
                    for pattern in api_key_patterns:
                        matches = re.findall(pattern, response_text, re.IGNORECASE)
                        for match in matches:
                            # 检查匹配的内容是否像真实的API密钥
                            if isinstance(match, tuple):
                                key_value = match[0] if match else ""
                            else:
                                key_value = match
                            
                            # 真实的API密钥通常长度较长且包含随机字符
                            if len(key_value) > 10 and not key_value.lower() in ['placeholder', 'your_key_here', 'test']:
                                assert False, f"API密钥暴露在 {endpoint}: {key_value[:10]}..."
            
            except aiohttp.ClientError:
                continue
        
        print(f"\\nAPI密钥暴露测试:")
        print(f"检查端点数量: {len(sensitive_endpoints)}")
        print("✓ 未发现API密钥暴露")
    
    @security_test("Database_Credentials")
    async def test_database_credentials_exposure(
        self,
        security_test_client: aiohttp.ClientSession
    ):
        """测试数据库凭据暴露"""
        # 检查错误页面和调试信息
        error_triggering_requests = [
            ("/api/v1/stocks/INVALID_CODE_12345", "GET"),
            ("/api/v1/nonexistent", "GET"),
            ("/api/v1/stocks/", "POST"),  # 无数据的POST请求
        ]
        
        db_credential_patterns = [
            r'(?i)(database|db|mysql|postgres|sqlite).*[:=].*["\']([^"\'\\s]+)["\']',
            r'(?i)(user|username|uid).*[:=].*["\']([^"\'\\s]+)["\']',
            r'(?i)(pass|password|pwd).*[:=].*["\']([^"\'\\s]+)["\']',
            r'(?i)(host|server|hostname).*[:=].*["\']([^"\'\\s]+)["\']',
            r'(?i)connection.*string.*["\']([^"\']+)["\']',
        ]
        
        for endpoint, method in error_triggering_requests:
            try:
                if method == "GET":
                    response = await security_test_client.get(f"http://localhost:8000{endpoint}")
                elif method == "POST":
                    response = await security_test_client.post(f"http://localhost:8000{endpoint}", json={})
                
                response_text = await response.text()
                
                # 检查错误响应中是否暴露了数据库凭据
                for pattern in db_credential_patterns:
                    matches = re.findall(pattern, response_text)
                    for match in matches:
                        credential_value = match[1] if isinstance(match, tuple) and len(match) > 1 else match
                        
                        # 排除明显的占位符
                        placeholders = ['localhost', 'example.com', 'user', 'admin', 'test', 'placeholder']
                        if credential_value.lower() not in placeholders:
                            assert False, f"数据库凭据暴露在 {endpoint}: {credential_value}"
            
            except aiohttp.ClientError:
                continue
        
        print(f"\\n数据库凭据暴露测试:")
        print(f"测试错误场景数量: {len(error_triggering_requests)}")
        print("✓ 未发现数据库凭据暴露")


class TestPrivacyProtection:
    """隐私保护测试"""
    
    @security_test("PII_Protection")
    async def test_personal_information_protection(
        self,
        performance_data_manager
    ):
        """测试个人身份信息（PII）保护"""
        pii_data = {
            "phone": "***********",
            "email": "<EMAIL>",
            "id_card": "110101199001011234",
            "address": "北京市朝阳区某某街道123号",
            "bank_account": "6222123456789012345",
        }
        
        # 测试PII数据的处理
        for field_name, pii_value in pii_data.items():
            try:
                # 假设有用户信息更新功能
                if hasattr(performance_data_manager, 'update_user_info'):
                    await performance_data_manager.update_user_info(
                        user_id="test_user",
                        **{field_name: pii_value}
                    )
                    
                    # 获取用户信息
                    user_info = await performance_data_manager.get_user_info("test_user")
                    
                    if hasattr(user_info, field_name):
                        stored_value = getattr(user_info, field_name)
                        
                        # PII应该被脱敏或加密
                        if stored_value == pii_value:
                            # 如果值相同，检查是否在安全的上下文中
                            print(f"警告: {field_name} 可能未被脱敏")
                        else:
                            # 检查脱敏处理是否合理
                            if '*' in stored_value or 'X' in stored_value:
                                print(f"✓ {field_name} 已被脱敏: {stored_value}")
                            else:
                                print(f"✓ {field_name} 已被处理: {len(stored_value)} 字符")
            
            except Exception:
                # 如果功能不存在，跳过
                continue
        
        print(f"\\nPII保护测试:")
        print(f"测试PII字段数量: {len(pii_data)}")
        print("PII保护检查完成")
    
    @security_test("Data_Masking")
    async def test_data_masking_in_logs(
        self,
        performance_data_manager
    ):
        """测试日志中的数据脱敏"""
        # 模拟包含敏感信息的操作
        sensitive_operations = [
            ("登录", {"username": "testuser", "password": "secret123"}),
            ("API调用", {"api_key": "sk-1234567890abcdef", "data": "test"}),
            ("支付", {"card_number": "****************", "cvv": "123"}),
            ("更新配置", {"database_password": "db_secret_pass", "config": "test"}),
        ]
        
        # 这个测试需要根据实际的日志系统来实现
        # 这里提供一个框架示例
        
        for operation_name, operation_data in sensitive_operations:
            try:
                # 执行可能产生日志的操作
                if hasattr(performance_data_manager, 'log_operation'):
                    await performance_data_manager.log_operation(
                        operation=operation_name,
                        data=operation_data
                    )
                
                # 检查日志文件或日志接口
                if hasattr(performance_data_manager, 'get_recent_logs'):
                    recent_logs = await performance_data_manager.get_recent_logs(limit=10)
                    
                    for log_entry in recent_logs:
                        log_content = str(log_entry)
                        
                        # 检查敏感信息是否被脱敏
                        sensitive_values = [
                            "secret123", "sk-1234567890abcdef", 
                            "****************", "123", "db_secret_pass"
                        ]
                        
                        for sensitive_value in sensitive_values:
                            assert sensitive_value not in log_content, (
                                f"日志中暴露了敏感信息: {sensitive_value}"
                            )
            
            except Exception:
                continue
        
        print(f"\\n日志脱敏测试:")
        print(f"测试敏感操作数量: {len(sensitive_operations)}")
        print("✓ 日志脱敏检查完成")
    
    @security_test("Data_Retention")
    async def test_data_retention_policy(
        self,
        performance_data_manager
    ):
        """测试数据保留策略"""
        # 检查是否有适当的数据清理机制
        data_types_to_check = [
            "user_sessions",
            "access_logs", 
            "temporary_files",
            "cache_data",
            "error_logs",
        ]
        
        for data_type in data_types_to_check:
            try:
                # 检查是否有清理功能
                cleanup_method = f"cleanup_{data_type}"
                if hasattr(performance_data_manager, cleanup_method):
                    # 测试清理功能
                    result = await getattr(performance_data_manager, cleanup_method)(
                        older_than_days=30
                    )
                    
                    if result:
                        print(f"✓ {data_type} 支持数据清理")
                
                # 检查是否有保留策略配置
                policy_method = f"get_{data_type}_retention_policy"
                if hasattr(performance_data_manager, policy_method):
                    policy = await getattr(performance_data_manager, policy_method)()
                    
                    if policy:
                        print(f"✓ {data_type} 有保留策略: {policy}")
            
            except Exception:
                continue
        
        print(f"\\n数据保留策略测试:")
        print(f"检查数据类型数量: {len(data_types_to_check)}")
        print("数据保留策略检查完成")


class TestSecureCommunication:
    """安全通信测试"""
    
    @security_test("HTTP_Headers")
    async def test_security_headers(
        self,
        security_test_client: aiohttp.ClientSession,
        security_headers: Dict[str, str]
    ):
        """测试安全HTTP头部"""
        endpoints_to_test = [
            "/api/v1/stocks/",
            "/api/v1/auth/login",
            "/api/v1/user/profile",
        ]
        
        missing_headers = {}
        
        for endpoint in endpoints_to_test:
            try:
                response = await security_test_client.get(f"http://localhost:8000{endpoint}")
                
                endpoint_missing = []
                for header_name, expected_value in security_headers.items():
                    actual_value = response.headers.get(header_name)
                    
                    if not actual_value:
                        endpoint_missing.append(header_name)
                    elif expected_value not in actual_value:
                        print(f"警告: {endpoint} 的 {header_name} 头部值可能不安全: {actual_value}")
                
                if endpoint_missing:
                    missing_headers[endpoint] = endpoint_missing
            
            except aiohttp.ClientError:
                continue
        
        # 至少应该有基本的安全头部
        critical_headers = ["X-Content-Type-Options", "X-Frame-Options"]
        
        for endpoint, missing in missing_headers.items():
            critical_missing = [h for h in missing if h in critical_headers]
            assert len(critical_missing) == 0, (
                f"{endpoint} 缺少关键安全头部: {critical_missing}"
            )
        
        print(f"\\n安全头部测试:")
        print(f"测试端点数量: {len(endpoints_to_test)}")
        print(f"检查安全头部数量: {len(security_headers)}")
        
        if not missing_headers:
            print("✓ 所有端点都有适当的安全头部")
        else:
            for endpoint, missing in missing_headers.items():
                print(f"- {endpoint} 缺少: {missing}")
    
    @security_test("CORS_Configuration")
    async def test_cors_configuration(
        self,
        security_test_client: aiohttp.ClientSession
    ):
        """测试CORS配置安全性"""
        test_origins = [
            "http://evil.com",
            "https://malicious.com",
            "null",
            "*",
            "http://localhost:3000",  # 可能的合法前端
        ]
        
        for origin in test_origins:
            try:
                headers = {"Origin": origin}
                response = await security_test_client.options(
                    "http://localhost:8000/api/v1/stocks/",
                    headers=headers
                )
                
                cors_origin = response.headers.get("Access-Control-Allow-Origin")
                
                if cors_origin:
                    # 检查CORS配置是否过于宽松
                    if cors_origin == "*" and origin in ["http://evil.com", "https://malicious.com"]:
                        print(f"警告: CORS配置过于宽松，允许任意来源")
                    elif cors_origin == origin and "evil" in origin:
                        assert False, f"CORS允许恶意来源: {origin}"
                    else:
                        print(f"✓ CORS正确处理来源: {origin} -> {cors_origin}")
            
            except aiohttp.ClientError:
                continue
        
        print(f"\\nCORS配置测试:")
        print(f"测试来源数量: {len(test_origins)}")
        print("CORS安全检查完成")
    
    @security_test("Content_Type_Validation")
    async def test_content_type_validation(
        self,
        security_test_client: aiohttp.ClientSession
    ):
        """测试Content-Type验证"""
        # 测试恶意Content-Type
        malicious_content_types = [
            "application/json; charset=utf-8",  # 正常
            "text/html",  # 可能导致XSS
            "application/javascript",  # 危险类型
            "text/xml",  # XML外部实体攻击
            "application/x-www-form-urlencoded; charset=utf-8; boundary=something",  # 恶意边界
        ]
        
        test_data = '{"test": "data"}'
        
        for content_type in malicious_content_types:
            try:
                response = await security_test_client.post(
                    "http://localhost:8000/api/v1/stocks/",
                    data=test_data,
                    headers={"Content-Type": content_type}
                )
                
                # 检查服务器如何处理不同的Content-Type
                if content_type in ["text/html", "application/javascript"]:
                    # 这些类型不应该被API接受
                    assert response.status >= 400, (
                        f"接受了危险的Content-Type: {content_type}"
                    )
            
            except aiohttp.ClientError:
                continue
        
        print(f"\\nContent-Type验证测试:")
        print(f"测试Content-Type数量: {len(malicious_content_types)}")
        print("Content-Type安全检查完成")