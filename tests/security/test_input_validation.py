"""
输入验证和注入攻击防护测试

测试系统对各种输入验证漏洞和注入攻击的防护能力
"""
import pytest
import asyncio
import json
from typing import List, Dict, Any
import aiohttp
from unittest.mock import patch, AsyncMock

from tests.security.conftest import (
    security_test,
    owasp_test,
    SecurityTestHelper
)


class TestSQLInjectionProtection:
    """SQL注入防护测试"""
    
    @owasp_test("A03_Injection")
    async def test_sql_injection_in_stock_query(
        self,
        performance_data_manager,
        malicious_sql_payloads: List[str],
        vulnerability_scanner
    ):
        """测试股票查询接口的SQL注入防护"""
        for payload in malicious_sql_payloads:
            try:
                # 尝试在股票代码中注入SQL
                result = await performance_data_manager.get_stock_info(code=payload)
                
                # 如果没有抛出异常且返回了意外数据，可能存在SQL注入
                if result and hasattr(result, 'code') and result.code == payload:
                    vulnerability_scanner.report_vulnerability(
                        'sql_injection',
                        {
                            'payload': payload,
                            'endpoint': 'get_stock_info',
                            'severity': 'critical',
                            'description': 'SQL注入可能成功执行'
                        }
                    )
                
            except Exception as e:
                # 预期的安全异常
                expected_errors = [
                    'Invalid stock code format',
                    'Validation error',
                    'Invalid input',
                    'Security violation'
                ]
                
                error_msg = str(e).lower()
                if not any(expected in error_msg for expected in expected_errors):
                    vulnerability_scanner.report_vulnerability(
                        'unexpected_error',
                        {
                            'payload': payload,
                            'error': str(e),
                            'severity': 'medium'
                        }
                    )
        
        # 验证没有发现严重的SQL注入漏洞
        report = vulnerability_scanner.get_vulnerability_report()
        critical_sql_injection = [
            v for v in report['critical_vulnerabilities'] 
            if v['type'] == 'sql_injection'
        ]
        
        assert len(critical_sql_injection) == 0, (
            f"发现 {len(critical_sql_injection)} 个严重SQL注入漏洞"
        )
        
        print(f"\\nSQL注入测试结果:")
        print(f"测试载荷数量: {len(malicious_sql_payloads)}")
        print(f"发现漏洞数量: {report['total_vulnerabilities']}")
        print(f"严重漏洞数量: {len(report['critical_vulnerabilities'])}")
    
    @security_test("SQL_Injection")
    async def test_sql_injection_in_date_range_query(
        self,
        performance_data_manager,
        malicious_sql_payloads: List[str]
    ):
        """测试日期范围查询的SQL注入防护"""
        from datetime import datetime, timedelta
        
        base_start_date = datetime.now() - timedelta(days=30)
        base_end_date = datetime.now()
        
        for payload in malicious_sql_payloads:
            # 测试日期参数注入
            try:
                await performance_data_manager.get_stock_daily_data(
                    code="000001",
                    start_date=payload,  # 注入载荷作为日期
                    end_date=base_end_date
                )
                # 如果没有异常，检查是否正确处理了恶意输入
                assert False, f"日期参数SQL注入防护失效: {payload}"
                
            except (ValueError, TypeError, Exception) as e:
                # 预期的输入验证异常
                assert "date" in str(e).lower() or "invalid" in str(e).lower()
    
    @security_test("NoSQL_Injection")
    async def test_nosql_injection_protection(
        self,
        performance_data_manager,
        security_test_client: aiohttp.ClientSession
    ):
        """测试NoSQL注入防护（如果使用MongoDB等）"""
        nosql_payloads = [
            {"$gt": ""},
            {"$ne": None},
            {"$where": "function() { return true; }"},
            {"$regex": ".*"},
            {"$exists": True},
            {"$or": [{"code": "000001"}, {"code": "000002"}]},
        ]
        
        for payload in nosql_payloads:
            try:
                # 假设有个支持复杂查询的接口
                response = await security_test_client.post(
                    "http://localhost:8000/api/v1/stocks/search",
                    json={"query": payload}
                )
                
                # 检查是否正确处理了NoSQL注入尝试
                if response.status == 200:
                    data = await response.json()
                    # 不应该返回所有数据或执行任意查询
                    assert not (isinstance(data, list) and len(data) > 1000), (
                        f"可能的NoSQL注入: {payload}"
                    )
                
            except aiohttp.ClientError:
                # 连接错误是正常的（如果接口不存在）
                continue


class TestXSSProtection:
    """XSS防护测试"""
    
    @owasp_test("A03_Cross_Site_Scripting")
    async def test_xss_in_stock_name_field(
        self,
        performance_data_manager,
        xss_payloads: List[str],
        vulnerability_scanner
    ):
        """测试股票名称字段的XSS防护"""
        for payload in xss_payloads:
            try:
                # 模拟更新股票信息
                if hasattr(performance_data_manager, 'update_stock_info'):
                    result = await performance_data_manager.update_stock_info(
                        code="TEST01",
                        name=payload,  # XSS载荷
                        industry="测试行业"
                    )
                    
                    # 检查返回数据是否已转义
                    if hasattr(result, 'name'):
                        if any(dangerous in result.name for dangerous in ['<script', 'javascript:', 'onerror']):
                            vulnerability_scanner.report_vulnerability(
                                'xss_stored',
                                {
                                    'payload': payload,
                                    'field': 'stock_name',
                                    'severity': 'high',
                                    'output': result.name
                                }
                            )
                
            except Exception as e:
                # 应该有适当的输入验证
                assert "validation" in str(e).lower() or "invalid" in str(e).lower()
        
        report = vulnerability_scanner.get_vulnerability_report()
        xss_vulnerabilities = report['vulnerabilities_by_type'].get('xss_stored', 0)
        
        assert xss_vulnerabilities == 0, f"发现 {xss_vulnerabilities} 个XSS漏洞"
        
        print(f"\\nXSS防护测试结果:")
        print(f"测试载荷数量: {len(xss_payloads)}")
        print(f"发现XSS漏洞: {xss_vulnerabilities}")
    
    @security_test("XSS_Reflected")
    async def test_reflected_xss_in_api_responses(
        self,
        security_test_client: aiohttp.ClientSession,
        xss_payloads: List[str]
    ):
        """测试API响应中的反射型XSS"""
        endpoints = [
            "/api/v1/stocks/{payload}",
            "/api/v1/stocks/search?q={payload}",
            "/api/v1/indicators/{payload}",
        ]
        
        for endpoint_template in endpoints:
            for payload in xss_payloads[:5]:  # 限制测试数量
                try:
                    endpoint = endpoint_template.format(payload=payload)
                    
                    async with security_test_client.get(f"http://localhost:8000{endpoint}") as response:
                        if response.status == 200:
                            content = await response.text()
                            
                            # 检查响应是否包含未转义的XSS载荷
                            dangerous_patterns = ['<script', 'javascript:', 'onerror=', 'onload=']
                            for pattern in dangerous_patterns:
                                assert pattern not in content, (
                                    f"反射型XSS漏洞在 {endpoint}: 包含 {pattern}"
                                )
                
                except aiohttp.ClientError:
                    # 连接错误是正常的
                    continue


class TestCommandInjectionProtection:
    """命令注入防护测试"""
    
    @security_test("Command_Injection")
    async def test_command_injection_in_export_functionality(
        self,
        performance_data_manager,
        command_injection_payloads: List[str]
    ):
        """测试导出功能的命令注入防护"""
        for payload in command_injection_payloads:
            try:
                # 假设有文件导出功能
                if hasattr(performance_data_manager, 'export_data'):
                    result = await performance_data_manager.export_data(
                        format="csv",
                        filename=payload,  # 命令注入载荷
                        data_type="stock_list"
                    )
                    
                    # 如果成功执行且没有适当验证，可能存在命令注入
                    if result and hasattr(result, 'filename'):
                        # 文件名不应包含shell元字符
                        dangerous_chars = [';', '|', '&', '`', '$', '(', ')']
                        for char in dangerous_chars:
                            assert char not in result.filename, (
                                f"可能的命令注入: 文件名包含危险字符 {char}"
                            )
                
            except Exception as e:
                # 应该有适当的输入验证
                expected_errors = ['invalid filename', 'illegal character', 'validation error']
                error_msg = str(e).lower()
                assert any(expected in error_msg for expected in expected_errors), (
                    f"意外的错误消息: {str(e)}"
                )


class TestPathTraversalProtection:
    """路径遍历防护测试"""
    
    @security_test("Path_Traversal")
    async def test_path_traversal_in_file_access(
        self,
        security_test_client: aiohttp.ClientSession,
        path_traversal_payloads: List[str]
    ):
        """测试文件访问的路径遍历防护"""
        # 假设存在文件下载或静态文件访问接口
        file_endpoints = [
            "/api/v1/files/{payload}",
            "/static/{payload}",
            "/exports/{payload}",
        ]
        
        for endpoint_template in file_endpoints:
            for payload in path_traversal_payloads:
                try:
                    endpoint = endpoint_template.format(payload=payload)
                    
                    async with security_test_client.get(f"http://localhost:8000{endpoint}") as response:
                        # 成功访问敏感文件是严重问题
                        if response.status == 200:
                            content = await response.text()
                            
                            # 检查是否返回了系统敏感文件内容
                            sensitive_patterns = [
                                'root:x:0:0:root',  # /etc/passwd
                                '[boot loader]',    # Windows boot.ini
                                'SAM Accounts',     # Windows SAM
                                'HKEY_LOCAL_MACHINE', # Windows Registry
                            ]
                            
                            for pattern in sensitive_patterns:
                                assert pattern not in content, (
                                    f"路径遍历漏洞: 访问了敏感文件 {endpoint}"
                                )
                
                except aiohttp.ClientError:
                    # 连接错误或403/404是预期的
                    continue


class TestInputValidationBypass:
    """输入验证绕过测试"""
    
    @security_test("Input_Validation")
    async def test_unicode_normalization_bypass(
        self,
        performance_data_manager
    ):
        """测试Unicode规范化绕过攻击"""
        # Unicode规范化可能导致验证绕过
        bypass_payloads = [
            "admin\\u0020",  # 带有Unicode空格
            "ａｄｍｉｎ",      # 全角字符
            "Admin\\u200B",  # 零宽字符
            "\\u0061dmin",   # Unicode编码的'a'
            "\\uff41dmin",   # 全角'a'
        ]
        
        for payload in bypass_payloads:
            try:
                # 测试用户名验证绕过
                if hasattr(performance_data_manager, 'validate_username'):
                    result = await performance_data_manager.validate_username(payload)
                    
                    # 应该正确识别并拒绝这些绕过尝试
                    assert not result, f"Unicode规范化绕过成功: {payload}"
                
            except Exception:
                # 验证失败是预期的
                pass
    
    @security_test("Input_Length")
    async def test_input_length_limits(
        self,
        performance_data_manager,
        oversized_payloads: Dict[str, str]
    ):
        """测试输入长度限制"""
        for payload_name, payload in oversized_payloads.items():
            try:
                # 测试各种输入字段的长度限制
                if hasattr(performance_data_manager, 'get_stock_info'):
                    await performance_data_manager.get_stock_info(code=payload[:20])
                
                # 不应该允许过长的输入
                if len(payload) > 1000:
                    assert False, f"接受了过长的输入: {payload_name}"
                
            except Exception as e:
                # 应该有长度验证错误
                error_msg = str(e).lower()
                expected_errors = ['too long', 'length', 'size limit', 'max length']
                assert any(expected in error_msg for expected in expected_errors), (
                    f"意外的长度验证错误: {str(e)}"
                )
    
    @security_test("JSON_Parsing")
    async def test_json_parsing_vulnerabilities(
        self,
        security_test_client: aiohttp.ClientSession,
        oversized_payloads: Dict[str, str]
    ):
        """测试JSON解析漏洞"""
        json_attack_payloads = [
            oversized_payloads['deep_json'],  # 深度嵌套
            oversized_payloads['large_json'], # 大JSON
            '{"a":' + '"b",' * 10000 + '"c":"d"}',  # 大量字段
            '["' + 'x' * 100000 + '"]',  # 长字符串
        ]
        
        for payload in json_attack_payloads:
            try:
                async with security_test_client.post(
                    "http://localhost:8000/api/v1/stocks/",
                    data=payload,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    # 应该拒绝恶意JSON或返回适当错误
                    assert response.status != 200 or response.status == 413, (
                        f"接受了恶意JSON载荷"
                    )
            
            except (aiohttp.ClientError, asyncio.TimeoutError):
                # 这是预期的防护反应
                pass


class TestDataSanitization:
    """数据清理测试"""
    
    @security_test("Data_Sanitization")
    async def test_html_sanitization(
        self,
        performance_data_manager
    ):
        """测试HTML内容清理"""
        html_payloads = [
            "<b>Bold Text</b>",
            "<script>alert('xss')</script>正常文本",
            "正常文本<img src=x onerror=alert(1)>",
            "<iframe src='javascript:alert(1)'></iframe>",
            "<svg><script>alert(1)</script></svg>",
        ]
        
        for payload in html_payloads:
            try:
                # 假设有支持HTML内容的字段
                if hasattr(performance_data_manager, 'update_stock_description'):
                    result = await performance_data_manager.update_stock_description(
                        code="TEST01",
                        description=payload
                    )
                    
                    if hasattr(result, 'description'):
                        # 检查是否正确清理了危险HTML
                        dangerous_tags = ['<script', '<iframe', '<object', '<embed']
                        for tag in dangerous_tags:
                            assert tag not in result.description.lower(), (
                                f"HTML清理失效: 保留了危险标签 {tag}"
                            )
            
            except Exception:
                # 验证失败是可以接受的
                pass
    
    @security_test("SQL_Character_Escaping")
    async def test_sql_character_escaping(
        self,
        performance_data_manager
    ):
        """测试SQL特殊字符转义"""
        sql_special_chars = [
            "O'Reilly Stock",  # 单引号
            'Stock "Premium" Class',  # 双引号  
            "Stock\\tTab\\nNewline",  # 控制字符
            "Stock\\x00Null",  # 空字节
            "Stock%Wildcard_Test",  # SQL通配符
        ]
        
        for test_name in sql_special_chars:
            try:
                # 测试包含特殊字符的股票名称
                if hasattr(performance_data_manager, 'create_stock_info'):
                    result = await performance_data_manager.create_stock_info(
                        code="TEST02",
                        name=test_name,
                        industry="测试"
                    )
                    
                    # 应该能够正确处理特殊字符
                    if hasattr(result, 'name'):
                        # 名称应该被正确存储和返回
                        assert result.name is not None
                        print(f"成功处理特殊字符: {test_name} -> {result.name}")
            
            except Exception as e:
                # 如果不支持特殊字符，至少应该有明确的错误消息
                assert "character" in str(e).lower() or "encoding" in str(e).lower()