"""
权限控制和认证安全测试

测试系统的认证机制、权限控制和访问安全
"""
import pytest
import asyncio
import time
import jwt
import hashlib
import secrets
from typing import Dict, List, Any, Optional
import aiohttp
from unittest.mock import patch, AsyncMock

from tests.security.conftest import (
    security_test,
    owasp_test,
    SecurityTestHelper
)


class TestAuthenticationSecurity:
    """认证安全测试"""
    
    @owasp_test("A07_Authentication")
    async def test_weak_password_policy(
        self,
        security_test_client: aiohttp.ClientSession,
        test_user_credentials: Dict[str, Any]
    ):
        """测试弱密码策略"""
        weak_passwords = [
            "123456",
            "password",
            "admin",
            "123",
            "abc",
            "111111",
            "qwerty",
            "12345678",
            "password123",
            "admin123"
        ]
        
        for weak_password in weak_passwords:
            try:
                # 尝试注册弱密码用户
                response = await security_test_client.post(
                    "http://localhost:8000/api/v1/auth/register",
                    json={
                        "username": f"testuser_{secrets.token_hex(4)}",
                        "password": weak_password,
                        "email": f"test_{secrets.token_hex(4)}@example.com"
                    }
                )
                
                # 应该拒绝弱密码
                assert response.status != 201, f"接受了弱密码: {weak_password}"
                
                if response.status == 400:
                    error_data = await response.json()
                    assert "password" in str(error_data).lower(), "应该明确指出密码问题"
                
            except aiohttp.ClientError:
                # 如果接口不存在，跳过测试
                continue
        
        print(f"\\n弱密码策略测试:")
        print(f"测试弱密码数量: {len(weak_passwords)}")
        print("所有弱密码都被正确拒绝")
    
    @security_test("Password_Hashing")
    async def test_password_hashing_security(
        self,
        performance_data_manager
    ):
        """测试密码哈希安全性"""
        # 假设有用户管理功能
        if not hasattr(performance_data_manager, 'create_user'):
            pytest.skip("系统不支持用户管理功能")
        
        test_password = "TestPassword123!@#"
        username = f"testuser_{secrets.token_hex(8)}"
        
        try:
            # 创建用户
            user = await performance_data_manager.create_user(
                username=username,
                password=test_password,
                email=f"{username}@test.com"
            )
            
            # 检查密码是否被正确哈希
            if hasattr(user, 'password_hash'):
                password_hash = user.password_hash
                
                # 密码不应该以明文存储
                assert password_hash != test_password, "密码以明文存储"
                
                # 应该使用强哈希算法
                # bcrypt哈希通常以$2a$, $2b$, $2x$, $2y$开头
                # argon2哈希通常以$argon2开头
                # pbkdf2哈希通常以pbkdf2开头
                strong_hash_patterns = ['$2a$', '$2b$', '$2x$', '$2y$', '$argon2', 'pbkdf2']
                
                is_strong_hash = any(password_hash.startswith(pattern) for pattern in strong_hash_patterns)
                assert is_strong_hash, f"使用了弱哈希算法: {password_hash[:20]}..."
                
                # 哈希长度应该足够
                assert len(password_hash) >= 32, f"哈希长度过短: {len(password_hash)}"
                
                print(f"\\n密码哈希安全测试:")
                print(f"原始密码长度: {len(test_password)}")
                print(f"哈希长度: {len(password_hash)}")
                print(f"哈希前缀: {password_hash[:10]}...")
        
        except Exception as e:
            pytest.skip(f"用户创建功能不可用: {str(e)}")
    
    @security_test("JWT_Security")
    async def test_jwt_token_security(
        self,
        security_test_client: aiohttp.ClientSession,
        security_helper: SecurityTestHelper
    ):
        """测试JWT令牌安全性"""
        # 测试恶意JWT令牌
        malicious_tokens = security_helper.generate_malformed_jwt()
        
        for token in malicious_tokens:
            try:
                headers = {"Authorization": f"Bearer {token}"}
                
                response = await security_test_client.get(
                    "http://localhost:8000/api/v1/protected-endpoint",
                    headers=headers
                )
                
                # 恶意令牌应该被拒绝
                assert response.status in [401, 403], f"接受了恶意JWT令牌: {token[:20]}..."
                
            except aiohttp.ClientError:
                continue
        
        # 测试过期令牌
        expired_payload = {
            "sub": "testuser",
            "exp": int(time.time()) - 3600,  # 1小时前过期
            "iat": int(time.time()) - 7200   # 2小时前签发
        }
        
        expired_token = security_helper.generate_jwt_token(expired_payload)
        
        try:
            headers = {"Authorization": f"Bearer {expired_token}"}
            response = await security_test_client.get(
                "http://localhost:8000/api/v1/protected-endpoint",
                headers=headers
            )
            
            assert response.status == 401, "接受了过期的JWT令牌"
            
        except aiohttp.ClientError:
            # 如果接口不存在，跳过
            pass
        
        print(f"\\nJWT安全测试:")
        print(f"测试恶意令牌数量: {len(malicious_tokens)}")
        print("所有恶意令牌都被正确拒绝")
    
    @security_test("Session_Security")
    async def test_session_security(
        self,
        security_test_client: aiohttp.ClientSession
    ):
        """测试会话安全性"""
        # 测试会话固定攻击防护
        session_cookies = []
        
        for i in range(3):
            try:
                # 多次登录，检查会话ID是否变化
                response = await security_test_client.post(
                    "http://localhost:8000/api/v1/auth/login",
                    json={
                        "username": "testuser",
                        "password": "testpassword"
                    }
                )
                
                if response.status == 200:
                    # 提取会话cookie
                    cookies = response.cookies
                    for cookie in cookies:
                        if 'session' in cookie.key.lower():
                            session_cookies.append(cookie.value)
                
            except aiohttp.ClientError:
                continue
        
        # 每次登录应该生成不同的会话ID
        if len(session_cookies) > 1:
            unique_sessions = set(session_cookies)
            assert len(unique_sessions) == len(session_cookies), (
                "会话固定漏洞: 重复使用相同的会话ID"
            )
        
        print(f"\\n会话安全测试:")
        print(f"收集到的会话数量: {len(session_cookies)}")
        print(f"不重复会话数量: {len(set(session_cookies))}")


class TestAuthorizationSecurity:
    """授权安全测试"""
    
    @owasp_test("A01_Access_Control")
    async def test_horizontal_privilege_escalation(
        self,
        security_test_client: aiohttp.ClientSession,
        test_user_credentials: Dict[str, Any]
    ):
        """测试水平权限提升（访问其他用户数据）"""
        user1_token = None
        user2_token = None
        
        # 获取两个不同用户的令牌
        for username in ['user1', 'user2']:
            try:
                response = await security_test_client.post(
                    "http://localhost:8000/api/v1/auth/login",
                    json={
                        "username": username,
                        "password": "testpassword"
                    }
                )
                
                if response.status == 200:
                    data = await response.json()
                    token = data.get('token')
                    if username == 'user1':
                        user1_token = token
                    else:
                        user2_token = token
            
            except aiohttp.ClientError:
                continue
        
        if not (user1_token and user2_token):
            pytest.skip("无法获取测试用户令牌")
        
        # 用户1尝试访问用户2的数据
        user_specific_endpoints = [
            "/api/v1/users/user2/profile",
            "/api/v1/users/user2/watchlist",
            "/api/v1/users/user2/settings",
        ]
        
        for endpoint in user_specific_endpoints:
            try:
                headers = {"Authorization": f"Bearer {user1_token}"}
                response = await security_test_client.get(
                    f"http://localhost:8000{endpoint}",
                    headers=headers
                )
                
                # 应该拒绝跨用户访问
                assert response.status in [403, 404], (
                    f"水平权限提升漏洞: 用户1可以访问用户2的 {endpoint}"
                )
                
            except aiohttp.ClientError:
                continue
        
        print(f"\\n水平权限提升测试:")
        print(f"测试端点数量: {len(user_specific_endpoints)}")
        print("所有跨用户访问都被正确拒绝")
    
    @security_test("Vertical_Privilege_Escalation")
    async def test_vertical_privilege_escalation(
        self,
        security_test_client: aiohttp.ClientSession,
        test_user_credentials: Dict[str, Any]
    ):
        """测试垂直权限提升（普通用户访问管理员功能）"""
        # 获取普通用户令牌
        try:
            response = await security_test_client.post(
                "http://localhost:8000/api/v1/auth/login",
                json=test_user_credentials['user']
            )
            
            if response.status != 200:
                pytest.skip("无法获取普通用户令牌")
            
            data = await response.json()
            user_token = data.get('token')
            
        except aiohttp.ClientError:
            pytest.skip("登录接口不可用")
        
        # 管理员专用端点
        admin_endpoints = [
            ("/api/v1/admin/users", "GET"),
            ("/api/v1/admin/system/config", "GET"),
            ("/api/v1/admin/logs", "GET"),
            ("/api/v1/admin/users/123", "DELETE"),
            ("/api/v1/admin/system/shutdown", "POST"),
        ]
        
        for endpoint, method in admin_endpoints:
            try:
                headers = {"Authorization": f"Bearer {user_token}"}
                
                if method == "GET":
                    response = await security_test_client.get(
                        f"http://localhost:8000{endpoint}",
                        headers=headers
                    )
                elif method == "POST":
                    response = await security_test_client.post(
                        f"http://localhost:8000{endpoint}",
                        headers=headers,
                        json={}
                    )
                elif method == "DELETE":
                    response = await security_test_client.delete(
                        f"http://localhost:8000{endpoint}",
                        headers=headers
                    )
                
                # 普通用户不应该能访问管理员功能
                assert response.status == 403, (
                    f"垂直权限提升漏洞: 普通用户可以访问 {method} {endpoint}"
                )
                
            except aiohttp.ClientError:
                continue
        
        print(f"\\n垂直权限提升测试:")
        print(f"测试管理员端点数量: {len(admin_endpoints)}")
        print("所有权限提升尝试都被正确拒绝")
    
    @security_test("IDOR")
    async def test_insecure_direct_object_references(
        self,
        security_test_client: aiohttp.ClientSession
    ):
        """测试不安全的直接对象引用（IDOR）"""
        # 假设获取了有效的用户令牌
        user_token = "valid_user_token"  # 在实际测试中需要获取真实令牌
        
        # 测试不同的对象ID
        test_object_ids = [
            1, 2, 3, 999, 9999,  # 数字ID
            "admin", "root", "system",  # 系统用户ID
            "../admin", "../../root",  # 路径遍历尝试
            "user1", "user2", "testuser"  # 其他用户ID
        ]
        
        id_based_endpoints = [
            "/api/v1/users/{id}/profile",
            "/api/v1/watchlists/{id}",
            "/api/v1/portfolios/{id}",
            "/api/v1/reports/{id}",
        ]
        
        for endpoint_template in id_based_endpoints:
            for obj_id in test_object_ids:
                endpoint = endpoint_template.format(id=obj_id)
                
                try:
                    headers = {"Authorization": f"Bearer {user_token}"}
                    response = await security_test_client.get(
                        f"http://localhost:8000{endpoint}",
                        headers=headers
                    )
                    
                    # 检查是否正确验证了对象访问权限
                    if response.status == 200:
                        data = await response.json()
                        
                        # 确保返回的对象属于当前用户
                        if 'user_id' in data or 'owner' in data:
                            # 这里需要根据实际数据结构验证
                            # 示例：assert data.get('user_id') == current_user_id
                            pass
                
                except aiohttp.ClientError:
                    continue
        
        print(f"\\nIDOR测试:")
        print(f"测试对象ID数量: {len(test_object_ids)}")
        print(f"测试端点模板数量: {len(id_based_endpoints)}")


class TestBruteForceProtection:
    """暴力破解防护测试"""
    
    @security_test("Brute_Force")
    async def test_login_brute_force_protection(
        self,
        security_test_client: aiohttp.ClientSession,
        security_helper: SecurityTestHelper,
        rate_limit_tester
    ):
        """测试登录暴力破解防护"""
        username = "testuser"
        passwords = security_helper.generate_brute_force_passwords()
        
        failed_attempts = 0
        blocked_attempts = 0
        
        for password in passwords[:20]:  # 限制测试数量
            try:
                response = await security_test_client.post(
                    "http://localhost:8000/api/v1/auth/login",
                    json={
                        "username": username,
                        "password": password
                    }
                )
                
                if response.status == 401:
                    failed_attempts += 1
                elif response.status == 429:  # Too Many Requests
                    blocked_attempts += 1
                    break  # 触发了速率限制
                elif response.status == 423:  # Locked
                    blocked_attempts += 1
                    break  # 账户被锁定
                
                # 短暂延迟避免过快请求
                await asyncio.sleep(0.1)
                
            except aiohttp.ClientError:
                continue
        
        # 应该在一定次数后触发防护机制
        assert blocked_attempts > 0 or failed_attempts >= 5, (
            "暴力破解防护机制未触发"
        )
        
        print(f"\\n暴力破解防护测试:")
        print(f"尝试密码数量: {min(len(passwords), 20)}")
        print(f"失败尝试: {failed_attempts}")
        print(f"被阻止尝试: {blocked_attempts}")
        
        if blocked_attempts > 0:
            print("✓ 暴力破解防护机制正常工作")
    
    @security_test("Account_Lockout")
    async def test_account_lockout_policy(
        self,
        security_test_client: aiohttp.ClientSession
    ):
        """测试账户锁定策略"""
        username = f"lockout_test_{secrets.token_hex(4)}"
        wrong_password = "wrongpassword"
        
        lockout_triggered = False
        attempt_count = 0
        
        # 连续错误登录尝试
        for i in range(10):
            try:
                response = await security_test_client.post(
                    "http://localhost:8000/api/v1/auth/login",
                    json={
                        "username": username,
                        "password": wrong_password
                    }
                )
                
                attempt_count += 1
                
                if response.status == 423:  # Account Locked
                    lockout_triggered = True
                    break
                elif response.status == 429:  # Rate Limited
                    # 速率限制也是有效的防护
                    lockout_triggered = True
                    break
                
                await asyncio.sleep(0.1)
                
            except aiohttp.ClientError:
                continue
        
        # 账户锁定或速率限制应该在合理次数内触发
        if attempt_count >= 5:
            assert lockout_triggered, f"在{attempt_count}次尝试后未触发账户保护"
        
        print(f"\\n账户锁定策略测试:")
        print(f"尝试次数: {attempt_count}")
        print(f"锁定触发: {'是' if lockout_triggered else '否'}")
    
    @security_test("Timing_Attack")
    async def test_timing_attack_resistance(
        self,
        security_test_client: aiohttp.ClientSession,
        security_helper: SecurityTestHelper
    ):
        """测试时序攻击防护"""
        # 测试用户名枚举的时序攻击
        valid_username = "admin"
        invalid_usernames = security_helper.generate_timing_attack_payloads()
        
        timing_results = []
        
        # 测试有效用户名的响应时间
        for _ in range(5):
            response_time = await security_helper.measure_response_time(
                security_test_client,
                "http://localhost:8000/api/v1/auth/login",
                {"username": valid_username, "password": "wrongpassword"}
            )
            timing_results.append(('valid', response_time))
        
        # 测试无效用户名的响应时间
        for username in invalid_usernames[:5]:
            response_time = await security_helper.measure_response_time(
                security_test_client,
                "http://localhost:8000/api/v1/auth/login",
                {"username": username, "password": "wrongpassword"}
            )
            timing_results.append(('invalid', response_time))
        
        # 分析时序差异
        valid_times = [t for category, t in timing_results if category == 'valid']
        invalid_times = [t for category, t in timing_results if category == 'invalid']
        
        if valid_times and invalid_times:
            avg_valid_time = sum(valid_times) / len(valid_times)
            avg_invalid_time = sum(invalid_times) / len(invalid_times)
            
            # 时序差异不应该太大，避免用户名枚举
            time_difference = abs(avg_valid_time - avg_invalid_time)
            max_allowed_difference = 0.1  # 100ms
            
            assert time_difference < max_allowed_difference, (
                f"时序攻击漏洞: 有效/无效用户名响应时间差异 {time_difference:.3f}s"
            )
            
            print(f"\\n时序攻击防护测试:")
            print(f"有效用户名平均响应时间: {avg_valid_time:.3f}s")
            print(f"无效用户名平均响应时间: {avg_invalid_time:.3f}s")
            print(f"时序差异: {time_difference:.3f}s")


class TestTokenSecurity:
    """令牌安全测试"""
    
    @security_test("Token_Entropy")
    async def test_token_randomness(
        self,
        performance_data_manager
    ):
        """测试令牌随机性和熵"""
        tokens = []
        
        # 生成多个令牌
        for i in range(100):
            if hasattr(performance_data_manager, 'generate_api_token'):
                token = await performance_data_manager.generate_api_token(
                    user_id=f"user_{i}"
                )
                tokens.append(token)
        
        if not tokens:
            pytest.skip("系统不支持API令牌生成")
        
        # 检查令牌唯一性
        unique_tokens = set(tokens)
        assert len(unique_tokens) == len(tokens), "生成了重复的令牌"
        
        # 检查令牌长度
        for token in tokens:
            assert len(token) >= 32, f"令牌长度过短: {len(token)} < 32"
        
        # 简单的熵检查
        for token in tokens[:10]:
            # 检查字符分布
            char_counts = {}
            for char in token:
                char_counts[char] = char_counts.get(char, 0) + 1
            
            # 不应该有过度重复的字符
            max_char_count = max(char_counts.values())
            assert max_char_count <= len(token) * 0.3, f"令牌熵过低: {token[:20]}..."
        
        print(f"\\n令牌安全测试:")
        print(f"生成令牌数量: {len(tokens)}")
        print(f"唯一令牌数量: {len(unique_tokens)}")
        print(f"平均长度: {sum(len(t) for t in tokens) / len(tokens):.1f}")
    
    @security_test("Token_Expiration")
    async def test_token_expiration(
        self,
        security_test_client: aiohttp.ClientSession
    ):
        """测试令牌过期机制"""
        # 这个测试需要根据实际的令牌实现来调整
        # 假设可以创建短期令牌进行测试
        
        try:
            # 创建短期令牌
            response = await security_test_client.post(
                "http://localhost:8000/api/v1/auth/token",
                json={
                    "username": "testuser",
                    "password": "testpassword",
                    "expires_in": 1  # 1秒过期
                }
            )
            
            if response.status == 200:
                data = await response.json()
                short_lived_token = data.get('token')
                
                # 立即使用令牌
                headers = {"Authorization": f"Bearer {short_lived_token}"}
                response1 = await security_test_client.get(
                    "http://localhost:8000/api/v1/user/profile",
                    headers=headers
                )
                
                # 等待令牌过期
                await asyncio.sleep(2)
                
                # 再次使用过期令牌
                response2 = await security_test_client.get(
                    "http://localhost:8000/api/v1/user/profile",
                    headers=headers
                )
                
                # 过期令牌应该被拒绝
                assert response1.status == 200, "有效令牌应该被接受"
                assert response2.status == 401, "过期令牌应该被拒绝"
                
                print(f"\\n令牌过期测试:")
                print("✓ 令牌过期机制正常工作")
        
        except aiohttp.ClientError:
            pytest.skip("令牌接口不可用")