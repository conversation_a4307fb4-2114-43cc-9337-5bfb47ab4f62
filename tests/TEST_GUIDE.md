# 测试目录结构说明

## 目录结构

```
tests/
├── __init__.py                    # 测试包初始化
├── conftest.py                    # 全局测试配置和fixtures
├── unit/                          # 单元测试
│   ├── __init__.py
│   ├── api/                       # API层单元测试
│   ├── core/                      # 核心模块单元测试
│   ├── models/                    # 数据模型单元测试
│   └── services/                  # 服务层单元测试
├── integration/                   # 集成测试
│   ├── __init__.py
│   ├── api/                       # API集成测试
│   ├── services/                  # 服务集成测试
│   └── test_unified_data_manager.py  # 统一数据管理器集成测试
├── performance/                   # 性能测试
│   ├── __init__.py
│   ├── conftest.py               # 性能测试配置
│   ├── test_response_time.py     # 响应时间测试
│   ├── test_concurrency.py      # 并发性能测试
│   └── test_cache_performance.py # 缓存性能测试
├── security/                      # 安全测试
│   ├── __init__.py
│   ├── conftest.py               # 安全测试配置
│   ├── test_input_validation.py  # 输入验证测试
│   ├── test_authentication.py    # 认证安全测试
│   └── test_data_protection.py   # 数据保护测试
├── scenarios/                     # 场景测试
│   ├── __init__.py
│   ├── conftest.py               # 场景测试配置
│   ├── test_failure_recovery.py  # 故障恢复测试
│   └── test_config_hot_reload.py # 配置热加载测试
└── utils/                         # 测试工具类
    └── test_data_fetcher.py      # 数据获取工具测试
```

## 测试分类

### 1. 单元测试 (Unit Tests)
- **路径**: `tests/unit/`
- **标记**: `@pytest.mark.unit`
- **用途**: 测试单个函数、类或模块的功能
- **特点**: 快速执行，高度隔离，使用mock对象

### 2. 集成测试 (Integration Tests)
- **路径**: `tests/integration/`
- **标记**: `@pytest.mark.integration`
- **用途**: 测试多个组件间的协作
- **特点**: 使用真实的外部依赖，测试完整的数据流

### 3. 性能测试 (Performance Tests)
- **路径**: `tests/performance/`
- **标记**: `@pytest.mark.performance`、`@pytest.mark.stress`、`@pytest.mark.load`
- **用途**: 验证系统性能指标
- **目标**:
  - 响应时间 < 50ms
  - 并发支持 1000+
  - 缓存命中率 > 80%

### 4. 安全测试 (Security Tests)
- **路径**: `tests/security/`
- **标记**: `@pytest.mark.security`、`@pytest.mark.owasp`
- **用途**: 验证系统安全性
- **覆盖**: OWASP Top 10、输入验证、权限控制

### 5. 场景测试 (Scenario Tests)
- **路径**: `tests/scenarios/`
- **标记**: `@pytest.mark.scenario`、`@pytest.mark.chaos`、`@pytest.mark.recovery`
- **用途**: 测试复杂的业务场景和故障情况
- **包含**: 故障恢复、配置热加载、混沌工程

## 测试标记 (Markers)

### 基础标记
- `unit`: 单元测试
- `integration`: 集成测试
- `performance`: 性能测试
- `security`: 安全测试
- `scenario`: 场景测试
- `slow`: 慢速测试（可通过 `-m "not slow"` 排除）

### 性能测试标记
- `stress`: 压力测试
- `load`: 负载测试

### 安全测试标记
- `owasp`: OWASP安全测试
- `owasp_a01_access_control`: 访问控制测试
- `owasp_a02_cryptographic_failures`: 加密失败测试
- `owasp_a03_injection`: 注入攻击测试
- `security_sql_injection`: SQL注入测试
- `security_xss_reflected`: 反射型XSS测试

### 场景测试标记
- `chaos`: 混沌工程测试
- `recovery`: 故障恢复测试
- `scenario_config_hot_reload`: 配置热加载测试
- `recovery_database_connection_loss`: 数据库连接丢失恢复测试

## 运行测试

### 使用Python脚本
```bash
# 基本用法
python run_tests.py [测试类型] [选项]

# 示例
python run_tests.py unit --coverage --html
python run_tests.py performance --parallel 4
python run_tests.py security -k "test_sql"
```

### 使用Shell脚本
```bash
# 基本用法  
./test_runner.sh [测试类型] [选项]

# 快捷命令
./test_runner.sh quick          # 快速测试
./test_runner.sh ci            # CI/CD测试
./test_runner.sh security-scan # 安全扫描
./test_runner.sh stress-test   # 压力测试
./test_runner.sh full-suite    # 完整测试套件
```

### 直接使用pytest
```bash
# 运行特定标记的测试
pytest -m "performance and not slow"
pytest -m "security"
pytest -m "owasp"

# 运行特定目录的测试
pytest tests/unit/
pytest tests/performance/

# 生成覆盖率报告
pytest --cov=app --cov-report=html

# 并行运行
pytest -n 4

# 详细输出
pytest -v --tb=short
```

## 测试配置

测试配置主要在以下文件中：

1. **pyproject.toml**: pytest配置、依赖、标记定义
2. **tests/conftest.py**: 全局fixtures和配置
3. **tests/*/conftest.py**: 各测试套件的特定配置

## 报告生成

测试执行后会生成以下报告：

1. **HTML报告**: `reports/test_report.html`
2. **JSON报告**: `reports/test_report.json`  
3. **覆盖率报告**: `htmlcov/index.html`
4. **XML覆盖率**: `coverage.xml` (用于CI/CD)

## 性能指标

### 目标指标
- **响应时间**: 平均 < 50ms, P95 < 100ms
- **并发处理**: 支持 1000+ 并发请求
- **缓存命中率**: > 80%
- **吞吐量**: > 500 req/s
- **内存使用**: 增长受控 < 10倍
- **错误率**: < 1%

### 安全测试覆盖
- SQL注入防护
- XSS攻击防护  
- 命令注入防护
- 路径遍历防护
- 认证和授权安全
- 数据加密和保护
- 速率限制和防暴力破解

## 最佳实践

1. **测试隔离**: 每个测试独立，不依赖其他测试
2. **数据清理**: 使用fixtures管理测试数据
3. **Mock使用**: 单元测试中mock外部依赖
4. **断言明确**: 使用清晰的断言消息
5. **性能基准**: 建立性能基准线，定期验证
6. **安全第一**: 定期运行安全测试套件
7. **持续集成**: 在CI/CD流水线中运行测试