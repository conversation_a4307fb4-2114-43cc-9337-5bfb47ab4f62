"""
场景测试配置和公共fixtures
"""
import pytest
import asyncio
import tempfile
import shutil
import json
import os
from typing import Dict, List, Any, Optional, AsyncGenerator
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
import redis.asyncio as redis

from app.core.config import settings, SettingsManager
from app.core.data.manager import UnifiedDataManager
from app.core.data.models import DataManagerConfig


@pytest.fixture
async def temp_config_dir():
    """创建临时配置目录"""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)


@pytest.fixture
async def scenario_data_manager():
    """场景测试专用数据管理器"""
    config = DataManagerConfig(
        cache={"enabled": True, "default_ttl": 300},
        routing={"failover_enabled": True, "circuit_breaker_enabled": True},
        worker_count=3
    )
    
    manager = UnifiedDataManager(config)
    await manager.start()
    
    yield manager
    
    await manager.shutdown()


@pytest.fixture
def mock_external_services():
    """模拟外部服务"""
    services = {
        'database': AsyncMock(),
        'redis': AsyncMock(),
        'data_provider': AsyncMock(),
        'notification_service': AsyncMock()
    }
    
    # 配置默认行为
    services['database'].is_connected.return_value = True
    services['redis'].ping.return_value = True
    services['data_provider'].get_stock_info.return_value = {
        'code': '000001',
        'name': '测试股票'
    }
    
    return services


@pytest.fixture
def failure_injection():
    """故障注入工具"""
    class FailureInjector:
        def __init__(self):
            self.active_failures = {}
        
        async def inject_network_failure(self, service_name: str, duration: float = 5.0):
            """注入网络故障"""
            self.active_failures[service_name] = {
                'type': 'network',
                'start_time': asyncio.get_event_loop().time(),
                'duration': duration
            }
        
        async def inject_timeout_failure(self, service_name: str, timeout: float = 0.1):
            """注入超时故障"""
            self.active_failures[service_name] = {
                'type': 'timeout',
                'timeout': timeout
            }
        
        async def inject_data_corruption(self, service_name: str):
            """注入数据损坏"""
            self.active_failures[service_name] = {
                'type': 'data_corruption'
            }
        
        def is_service_failed(self, service_name: str) -> bool:
            """检查服务是否处于故障状态"""
            if service_name not in self.active_failures:
                return False
            
            failure = self.active_failures[service_name]
            
            if failure['type'] == 'network':
                current_time = asyncio.get_event_loop().time()
                if current_time - failure['start_time'] > failure['duration']:
                    del self.active_failures[service_name]
                    return False
                return True
            
            return True
        
        def clear_failures(self):
            """清除所有故障"""
            self.active_failures.clear()
    
    return FailureInjector()


@pytest.fixture
def config_hot_reload():
    """配置热加载测试工具"""
    class ConfigHotReloader:
        def __init__(self, temp_dir: str):
            self.temp_dir = temp_dir
            self.config_file = os.path.join(temp_dir, "test_config.json")
            self.original_config = {}
        
        def create_config_file(self, config: Dict[str, Any]):
            """创建配置文件"""
            self.original_config = config.copy()
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
        
        def update_config_file(self, updates: Dict[str, Any]):
            """更新配置文件"""
            current_config = self.original_config.copy()
            current_config.update(updates)
            
            with open(self.config_file, 'w') as f:
                json.dump(current_config, f, indent=2)
        
        def get_config_file_path(self) -> str:
            """获取配置文件路径"""
            return self.config_file
    
    return ConfigHotReloader


@pytest.fixture
def chaos_monkey():
    """混沌工程测试工具"""
    class ChaosMonkey:
        def __init__(self):
            self.chaos_scenarios = []
            self.active = False
        
        async def random_service_failure(self, services: List[str], failure_rate: float = 0.1):
            """随机服务故障"""
            import random
            
            scenario = {
                'type': 'random_failure',
                'services': services,
                'failure_rate': failure_rate,
                'start_time': datetime.now()
            }
            
            self.chaos_scenarios.append(scenario)
            
            # 模拟随机故障
            for service in services:
                if random.random() < failure_rate:
                    print(f"ChaosMonkey: 随机故障注入到 {service}")
                    yield service, 'failed'
                else:
                    yield service, 'healthy'
        
        async def network_partition(self, duration: float = 10.0):
            """网络分区"""
            scenario = {
                'type': 'network_partition',
                'duration': duration,
                'start_time': datetime.now()
            }
            
            self.chaos_scenarios.append(scenario)
            print(f"ChaosMonkey: 网络分区 {duration} 秒")
        
        async def resource_exhaustion(self, resource_type: str = 'memory'):
            """资源耗尽"""
            scenario = {
                'type': 'resource_exhaustion',
                'resource': resource_type,
                'start_time': datetime.now()
            }
            
            self.chaos_scenarios.append(scenario)
            print(f"ChaosMonkey: {resource_type} 资源耗尽")
        
        def get_chaos_report(self) -> Dict[str, Any]:
            """获取混沌测试报告"""
            return {
                'total_scenarios': len(self.chaos_scenarios),
                'scenarios': self.chaos_scenarios,
                'active': self.active
            }
    
    return ChaosMonkey()


@pytest.fixture
def performance_monitor():
    """性能监控工具"""
    class PerformanceMonitor:
        def __init__(self):
            self.metrics = {
                'response_times': [],
                'memory_usage': [],
                'cpu_usage': [],
                'error_counts': {},
                'throughput_samples': []
            }
            self.monitoring = False
        
        async def start_monitoring(self, interval: float = 1.0):
            """开始性能监控"""
            self.monitoring = True
            
            while self.monitoring:
                # 模拟性能指标收集
                import psutil
                import time
                
                # CPU使用率
                cpu_percent = psutil.cpu_percent()
                self.metrics['cpu_usage'].append({
                    'timestamp': time.time(),
                    'value': cpu_percent
                })
                
                # 内存使用
                memory_info = psutil.virtual_memory()
                self.metrics['memory_usage'].append({
                    'timestamp': time.time(),
                    'value': memory_info.percent
                })
                
                await asyncio.sleep(interval)
        
        def stop_monitoring(self):
            """停止性能监控"""
            self.monitoring = False
        
        def record_response_time(self, operation: str, time_taken: float):
            """记录响应时间"""
            self.metrics['response_times'].append({
                'operation': operation,
                'time': time_taken,
                'timestamp': asyncio.get_event_loop().time()
            })
        
        def record_error(self, error_type: str):
            """记录错误"""
            if error_type not in self.metrics['error_counts']:
                self.metrics['error_counts'][error_type] = 0
            self.metrics['error_counts'][error_type] += 1
        
        def get_performance_report(self) -> Dict[str, Any]:
            """获取性能报告"""
            response_times = [m['time'] for m in self.metrics['response_times']]
            
            report = {
                'avg_response_time': sum(response_times) / len(response_times) if response_times else 0,
                'max_response_time': max(response_times) if response_times else 0,
                'min_response_time': min(response_times) if response_times else 0,
                'total_requests': len(response_times),
                'error_counts': self.metrics['error_counts'],
                'avg_cpu_usage': self._avg_metric('cpu_usage'),
                'avg_memory_usage': self._avg_metric('memory_usage')
            }
            
            return report
        
        def _avg_metric(self, metric_name: str) -> float:
            """计算指标平均值"""
            values = [m['value'] for m in self.metrics[metric_name]]
            return sum(values) / len(values) if values else 0
    
    return PerformanceMonitor()


class ScenarioTestHelper:
    """场景测试辅助类"""
    
    @staticmethod
    async def wait_for_condition(
        condition_func,
        timeout: float = 10.0,
        check_interval: float = 0.1,
        condition_name: str = "condition"
    ) -> bool:
        """等待条件满足"""
        start_time = asyncio.get_event_loop().time()
        
        while asyncio.get_event_loop().time() - start_time < timeout:
            try:
                if await condition_func() if asyncio.iscoroutinefunction(condition_func) else condition_func():
                    return True
            except Exception:
                pass
            
            await asyncio.sleep(check_interval)
        
        raise TimeoutError(f"等待 {condition_name} 超时 ({timeout}s)")
    
    @staticmethod
    async def simulate_gradual_load(
        load_func,
        max_load: int = 100,
        ramp_time: float = 10.0,
        sustain_time: float = 30.0
    ):
        """模拟渐进式负载"""
        load_results = []
        
        # 阶段1: 渐进增加负载
        step_count = 10
        load_per_step = max_load // step_count
        step_duration = ramp_time / step_count
        
        current_load = 0
        for step in range(step_count):
            current_load += load_per_step
            
            # 执行负载
            step_results = await load_func(current_load)
            load_results.extend(step_results)
            
            await asyncio.sleep(step_duration)
        
        # 阶段2: 维持最大负载
        sustain_results = await load_func(max_load)
        load_results.extend(sustain_results)
        
        await asyncio.sleep(sustain_time)
        
        return load_results
    
    @staticmethod
    def create_test_data_set(
        size: int = 1000,
        data_type: str = "stock_info"
    ) -> List[Dict[str, Any]]:
        """创建测试数据集"""
        test_data = []
        
        for i in range(size):
            if data_type == "stock_info":
                data = {
                    'code': f"{i+1:06d}",
                    'name': f"测试股票{i+1}",
                    'market': 'SZ' if i % 2 == 0 else 'SH',
                    'industry': f"行业{i % 10}",
                    'last_price': 10.0 + (i % 100) * 0.1
                }
            elif data_type == "daily_data":
                data = {
                    'code': f"{i+1:06d}",
                    'date': datetime.now() - timedelta(days=i % 365),
                    'open': 10.0 + (i % 50) * 0.1,
                    'high': 11.0 + (i % 50) * 0.1,
                    'low': 9.0 + (i % 50) * 0.1,
                    'close': 10.5 + (i % 50) * 0.1,
                    'volume': 1000000 + i * 1000
                }
            else:
                data = {'id': i, 'value': f"test_data_{i}"}
            
            test_data.append(data)
        
        return test_data


@pytest.fixture
def scenario_helper():
    """场景测试辅助器fixture"""
    return ScenarioTestHelper()


# 场景测试标记
def scenario_test(scenario_type: str):
    """场景测试装饰器"""
    def decorator(func):
        func = pytest.mark.scenario(func)
        func = pytest.mark.__dict__[f"scenario_{scenario_type.lower()}"](func)
        func._scenario_type = scenario_type
        return func
    return decorator


def chaos_test(chaos_type: str):
    """混沌测试装饰器"""
    def decorator(func):
        func = pytest.mark.chaos(func)
        func = pytest.mark.__dict__[f"chaos_{chaos_type.lower()}"](func)
        func._chaos_type = chaos_type
        return func
    return decorator


def recovery_test(recovery_scenario: str):
    """恢复测试装饰器"""
    def decorator(func):
        func = pytest.mark.recovery(func)
        func = pytest.mark.__dict__[f"recovery_{recovery_scenario.lower()}"](func)
        func._recovery_scenario = recovery_scenario
        return func
    return decorator