"""
故障恢复和容错能力测试

测试系统在各种故障情况下的恢复能力和容错机制
"""
import pytest
import asyncio
import time
import random
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch, MagicMock
import redis.asyncio as redis

from tests.scenarios.conftest import (
    scenario_test,
    chaos_test,
    recovery_test,
    ScenarioTestHelper
)


class TestDatabaseFailureRecovery:
    """数据库故障恢复测试"""
    
    @recovery_test("database_connection_loss")
    async def test_database_connection_loss_recovery(
        self,
        scenario_data_manager,
        failure_injection,
        scenario_helper: ScenarioTestHelper
    ):
        """测试数据库连接丢失后的恢复"""
        print("\\n=== 数据库连接丢失恢复测试 ===")
        
        # 阶段1: 正常操作
        result1 = await scenario_data_manager.get_stock_info(code="000001")
        assert result1 is not None, "正常情况下应该能获取数据"
        
        # 阶段2: 注入数据库故障
        await failure_injection.inject_network_failure("database", duration=5.0)
        
        # 模拟数据库连接失败
        with patch.object(
            scenario_data_manager, 
            '_database_client',
            side_effect=ConnectionError("Database connection lost")
        ):
            # 在故障期间，系统应该：
            # 1. 检测到连接失败
            # 2. 尝试从缓存获取数据
            # 3. 启动重连机制
            
            error_count = 0
            cache_hit_count = 0
            
            for i in range(10):
                try:
                    result = await scenario_data_manager.get_stock_info(code="000001")
                    if result is not None:
                        cache_hit_count += 1
                        print(f"第{i+1}次请求: 缓存命中")
                    else:
                        error_count += 1
                        print(f"第{i+1}次请求: 获取失败")
                except Exception as e:
                    error_count += 1
                    print(f"第{i+1}次请求: 异常 {str(e)}")
                
                await asyncio.sleep(0.5)
            
            # 在数据库故障期间，至少应该有部分请求通过缓存成功
            assert cache_hit_count > 0, "数据库故障期间应该有缓存命中"
            print(f"故障期间统计: 缓存命中{cache_hit_count}次, 失败{error_count}次")
        
        # 阶段3: 等待故障恢复
        await asyncio.sleep(6)  # 等待故障持续时间结束
        failure_injection.clear_failures()
        
        # 阶段4: 验证恢复
        async def check_recovery():
            try:
                result = await scenario_data_manager.get_stock_info(code="000002")
                return result is not None
            except:
                return False
        
        # 等待系统恢复
        recovered = await scenario_helper.wait_for_condition(
            check_recovery,
            timeout=10.0,
            condition_name="数据库连接恢复"
        )
        
        assert recovered, "数据库连接应该能够恢复"
        
        # 验证恢复后的正常功能
        recovery_result = await scenario_data_manager.get_stock_info(code="000003")
        assert recovery_result is not None, "恢复后应该能正常获取数据"
        
        print("✓ 数据库连接丢失恢复测试通过")
    
    @recovery_test("database_timeout")
    async def test_database_timeout_handling(
        self,
        scenario_data_manager,
        failure_injection
    ):
        """测试数据库超时处理"""
        print("\\n=== 数据库超时处理测试 ===")
        
        # 注入超时故障
        await failure_injection.inject_timeout_failure("database", timeout=0.1)
        
        timeout_count = 0
        success_count = 0
        
        # 模拟数据库查询超时
        async def slow_query(*args, **kwargs):
            await asyncio.sleep(2.0)  # 2秒延迟，模拟慢查询
            return {"code": "000001", "name": "测试股票"}
        
        with patch.object(
            scenario_data_manager,
            '_execute_database_query',
            side_effect=slow_query
        ):
            for i in range(5):
                try:
                    start_time = time.perf_counter()
                    result = await asyncio.wait_for(
                        scenario_data_manager.get_stock_info(code=f"{i+1:06d}"),
                        timeout=1.0  # 1秒超时
                    )
                    
                    if result:
                        success_count += 1
                        query_time = time.perf_counter() - start_time
                        print(f"查询{i+1}: 成功 ({query_time:.2f}s)")
                    
                except asyncio.TimeoutError:
                    timeout_count += 1
                    query_time = time.perf_counter() - start_time
                    print(f"查询{i+1}: 超时 ({query_time:.2f}s)")
                except Exception as e:
                    print(f"查询{i+1}: 异常 {str(e)}")
        
        # 系统应该正确处理超时
        assert timeout_count > 0, "应该检测到超时"
        print(f"超时处理统计: 超时{timeout_count}次, 成功{success_count}次")
        print("✓ 数据库超时处理测试通过")
    
    @recovery_test("data_corruption")
    async def test_data_corruption_detection(
        self,
        scenario_data_manager,
        failure_injection
    ):
        """测试数据损坏检测和处理"""
        print("\\n=== 数据损坏检测测试 ===")
        
        # 注入数据损坏
        await failure_injection.inject_data_corruption("database")
        
        corrupted_responses = [
            None,  # 空响应
            {"invalid": "data"},  # 无效格式
            {"code": "", "name": None},  # 字段为空
            {"code": "000001"},  # 缺少必需字段
            {"code": "INVALID@#$", "name": "测试"},  # 无效数据
        ]
        
        corruption_detected_count = 0
        
        for i, corrupted_data in enumerate(corrupted_responses):
            with patch.object(
                scenario_data_manager,
                '_fetch_from_database',
                return_value=corrupted_data
            ):
                try:
                    result = await scenario_data_manager.get_stock_info(code=f"{i+1:06d}")
                    
                    # 检查是否正确处理了损坏数据
                    if result is None or not self._is_valid_stock_data(result):
                        corruption_detected_count += 1
                        print(f"损坏数据{i+1}: 正确检测并处理")
                    else:
                        print(f"损坏数据{i+1}: 未检测到损坏，返回: {result}")
                
                except Exception as e:
                    corruption_detected_count += 1
                    print(f"损坏数据{i+1}: 抛出异常处理: {str(e)}")
        
        # 至少应该检测到大部分数据损坏
        detection_rate = corruption_detected_count / len(corrupted_responses)
        assert detection_rate >= 0.8, f"数据损坏检测率应该 >= 80%, 实际: {detection_rate:.1%}"
        
        print(f"数据损坏检测率: {detection_rate:.1%}")
        print("✓ 数据损坏检测测试通过")
    
    def _is_valid_stock_data(self, data: Any) -> bool:
        """验证股票数据是否有效"""
        if not isinstance(data, dict):
            return False
        
        required_fields = ['code', 'name']
        for field in required_fields:
            if field not in data or not data[field]:
                return False
        
        # 验证股票代码格式
        code = data['code']
        if not isinstance(code, str) or len(code) != 6 or not code.isdigit():
            return False
        
        return True


class TestCacheFailureRecovery:
    """缓存故障恢复测试"""
    
    @recovery_test("redis_failure")
    async def test_redis_failure_fallback(
        self,
        scenario_data_manager,
        failure_injection,
        redis_client: redis.Redis
    ):
        """测试Redis故障时的降级处理"""
        print("\\n=== Redis故障降级测试 ===")
        
        # 阶段1: 正常缓存操作
        stock_code = "000001"
        result1 = await scenario_data_manager.get_stock_info(code=stock_code)
        assert result1 is not None, "正常情况下应该能获取数据"
        
        # 再次获取，应该命中缓存
        start_time = time.perf_counter()
        result2 = await scenario_data_manager.get_stock_info(code=stock_code)
        cache_time = time.perf_counter() - start_time
        
        assert result2 is not None, "缓存命中应该返回数据"
        assert cache_time < 0.01, f"缓存命中应该很快: {cache_time:.3f}s"
        
        # 阶段2: 注入Redis故障
        await failure_injection.inject_network_failure("redis", duration=10.0)
        
        # 模拟Redis连接失败
        with patch.object(redis_client, 'get', side_effect=redis.ConnectionError("Redis connection failed")):
            with patch.object(redis_client, 'set', side_effect=redis.ConnectionError("Redis connection failed")):
                
                fallback_success_count = 0
                fallback_error_count = 0
                
                # 测试降级到其他缓存或直接查询数据源
                for i in range(10):
                    try:
                        start_time = time.perf_counter()
                        result = await scenario_data_manager.get_stock_info(code=f"{i+1:06d}")
                        query_time = time.perf_counter() - start_time
                        
                        if result is not None:
                            fallback_success_count += 1
                            print(f"降级查询{i+1}: 成功 ({query_time:.3f}s)")
                        else:
                            fallback_error_count += 1
                            print(f"降级查询{i+1}: 失败")
                    
                    except Exception as e:
                        fallback_error_count += 1
                        print(f"降级查询{i+1}: 异常 {str(e)}")
                
                # 即使Redis故障，系统也应该能降级处理
                fallback_success_rate = fallback_success_count / (fallback_success_count + fallback_error_count)
                assert fallback_success_rate >= 0.8, f"Redis故障降级成功率应该 >= 80%, 实际: {fallback_success_rate:.1%}"
                
                print(f"Redis故障降级统计: 成功{fallback_success_count}次, 失败{fallback_error_count}次")
        
        print("✓ Redis故障降级测试通过")
    
    @recovery_test("cache_memory_pressure")
    async def test_cache_memory_pressure_handling(
        self,
        scenario_data_manager,
        scenario_helper: ScenarioTestHelper
    ):
        """测试缓存内存压力处理"""
        print("\\n=== 缓存内存压力测试 ===")
        
        # 生成大量数据填充缓存
        large_dataset = scenario_helper.create_test_data_set(size=1000, data_type="stock_info")
        
        memory_pressure_detected = False
        eviction_count = 0
        
        # 快速填充缓存
        for i, stock_data in enumerate(large_dataset):
            try:
                # 模拟缓存存储
                await scenario_data_manager.get_stock_info(code=stock_data['code'])
                
                # 每100个检查一次内存状态
                if (i + 1) % 100 == 0:
                    if hasattr(scenario_data_manager, 'get_cache_memory_usage'):
                        memory_usage = await scenario_data_manager.get_cache_memory_usage()
                        print(f"缓存了{i+1}个股票，内存使用: {memory_usage}MB")
                        
                        # 检查是否触发了内存压力处理
                        if memory_usage > 50:  # 假设50MB为压力阈值
                            memory_pressure_detected = True
                    
                    # 检查是否有缓存淘汰
                    if hasattr(scenario_data_manager, 'get_cache_eviction_count'):
                        current_evictions = await scenario_data_manager.get_cache_eviction_count()
                        if current_evictions > eviction_count:
                            eviction_count = current_evictions
                            print(f"检测到缓存淘汰: {eviction_count}次")
            
            except Exception as e:
                print(f"缓存填充异常: {str(e)}")
                break
        
        # 验证内存压力处理
        if memory_pressure_detected:
            print("✓ 检测到内存压力")
        
        if eviction_count > 0:
            print(f"✓ 缓存淘汰机制工作正常: {eviction_count}次淘汰")
        
        # 验证系统在内存压力下仍能正常工作
        test_result = await scenario_data_manager.get_stock_info(code="999999")
        assert test_result is not None, "内存压力下系统应该仍能正常工作"
        
        print("✓ 缓存内存压力处理测试通过")


class TestExternalServiceFailure:
    """外部服务故障测试"""
    
    @recovery_test("data_provider_failover")
    async def test_data_provider_failover(
        self,
        scenario_data_manager,
        failure_injection
    ):
        """测试数据提供商故障转移"""
        print("\\n=== 数据提供商故障转移测试 ===")
        
        # 模拟多个数据提供商
        providers = ["mairui", "tushare", "akshare"]
        failover_sequence = []
        
        async def mock_provider_call(provider_name, *args, **kwargs):
            if failure_injection.is_service_failed(provider_name):
                failover_sequence.append(f"{provider_name}_failed")
                raise ConnectionError(f"{provider_name} service unavailable")
            else:
                failover_sequence.append(f"{provider_name}_success")
                return {
                    'code': '000001',
                    'name': f'来自{provider_name}的数据',
                    'provider': provider_name
                }
        
        # 注入第一个提供商故障
        await failure_injection.inject_network_failure("mairui", duration=5.0)
        
        with patch.object(scenario_data_manager, '_call_data_provider', side_effect=mock_provider_call):
            # 配置故障转移顺序
            scenario_data_manager._provider_fallback_order = providers
            
            # 请求数据，应该自动故障转移
            result = await scenario_data_manager.get_stock_info(code="000001")
            
            assert result is not None, "故障转移应该成功"
            assert result.get('provider') in providers, "应该从某个提供商获取数据"
            
            # 验证故障转移逻辑
            assert 'mairui_failed' in failover_sequence, "应该检测到主提供商故障"
            success_providers = [seq for seq in failover_sequence if '_success' in seq]
            assert len(success_providers) > 0, "应该有提供商成功响应"
            
            print(f"故障转移序列: {failover_sequence}")
            print(f"最终数据来源: {result.get('provider')}")
        
        print("✓ 数据提供商故障转移测试通过")
    
    @recovery_test("api_rate_limit")
    async def test_api_rate_limit_handling(
        self,
        scenario_data_manager,
        failure_injection
    ):
        """测试API速率限制处理"""
        print("\\n=== API速率限制处理测试 ===")
        
        rate_limit_responses = 0
        retry_attempts = 0
        backoff_delays = []
        
        async def mock_rate_limited_api(*args, **kwargs):
            nonlocal rate_limit_responses, retry_attempts
            
            # 前几次调用返回速率限制错误
            if rate_limit_responses < 3:
                rate_limit_responses += 1
                raise Exception("Rate limit exceeded, retry after 60 seconds")
            else:
                # 后续调用成功
                return {'code': '000001', 'name': '测试股票'}
        
        # 模拟带重试的API调用
        with patch.object(scenario_data_manager, '_api_call', side_effect=mock_rate_limited_api):
            
            # 配置重试策略
            max_retries = 5
            base_delay = 1.0
            
            for attempt in range(max_retries):
                try:
                    result = await scenario_data_manager.get_stock_info(code="000001")
                    if result:
                        print(f"第{attempt+1}次尝试成功")
                        break
                except Exception as e:
                    retry_attempts += 1
                    delay = base_delay * (2 ** attempt)  # 指数退避
                    backoff_delays.append(delay)
                    
                    print(f"第{attempt+1}次尝试失败: {str(e)}, 等待{delay}s后重试")
                    await asyncio.sleep(delay)
        
        # 验证速率限制处理
        assert rate_limit_responses > 0, "应该遇到速率限制"
        assert retry_attempts > 0, "应该有重试尝试"
        assert len(backoff_delays) > 0, "应该有退避延迟"
        
        # 验证退避策略是递增的
        for i in range(1, len(backoff_delays)):
            assert backoff_delays[i] >= backoff_delays[i-1], "退避延迟应该递增"
        
        print(f"速率限制处理统计: 限制{rate_limit_responses}次, 重试{retry_attempts}次")
        print(f"退避延迟序列: {backoff_delays}")
        print("✓ API速率限制处理测试通过")


class TestChaosEngineering:
    """混沌工程测试"""
    
    @chaos_test("random_failures")
    async def test_random_service_failures(
        self,
        scenario_data_manager,
        chaos_monkey,
        performance_monitor
    ):
        """测试随机服务故障的系统弹性"""
        print("\\n=== 随机服务故障混沌测试 ===")
        
        services = ["database", "redis", "data_provider", "notification"]
        failure_rate = 0.2  # 20%故障率
        
        # 启动性能监控
        monitor_task = asyncio.create_task(performance_monitor.start_monitoring(interval=0.5))
        
        try:
            # 启动混沌测试
            chaos_results = []
            
            async for service, status in chaos_monkey.random_service_failure(services, failure_rate):
                chaos_results.append((service, status))
                print(f"ChaosMonkey: {service} -> {status}")
            
            # 在故障注入期间执行正常业务操作
            business_operations = []
            
            for i in range(50):
                start_time = time.perf_counter()
                
                try:
                    # 执行各种业务操作
                    if i % 4 == 0:
                        result = await scenario_data_manager.get_stock_list()
                        operation = "get_stock_list"
                    elif i % 4 == 1:
                        result = await scenario_data_manager.get_stock_info(code=f"{i%100+1:06d}")
                        operation = "get_stock_info"
                    elif i % 4 == 2:
                        result = await scenario_data_manager.get_real_time_data(code=f"{i%50+1:06d}")
                        operation = "get_real_time_data"
                    else:
                        if hasattr(scenario_data_manager, 'get_stock_daily_data'):
                            result = await scenario_data_manager.get_stock_daily_data(
                                code=f"{i%20+1:06d}",
                                start_date=datetime.now() - timedelta(days=7),
                                end_date=datetime.now()
                            )
                            operation = "get_daily_data"
                        else:
                            result = await scenario_data_manager.get_stock_info(code=f"{i%20+1:06d}")
                            operation = "get_stock_info_fallback"
                    
                    response_time = time.perf_counter() - start_time
                    
                    business_operations.append({
                        'operation': operation,
                        'success': result is not None,
                        'response_time': response_time
                    })
                    
                    performance_monitor.record_response_time(operation, response_time)
                    
                    if result is None:
                        performance_monitor.record_error(f"{operation}_null_result")
                
                except Exception as e:
                    response_time = time.perf_counter() - start_time
                    business_operations.append({
                        'operation': operation,
                        'success': False,
                        'response_time': response_time,
                        'error': str(e)
                    })
                    
                    performance_monitor.record_error(f"{operation}_exception")
                
                # 短暂休息
                await asyncio.sleep(0.1)
            
            # 分析混沌测试结果
            successful_ops = [op for op in business_operations if op['success']]
            failed_ops = [op for op in business_operations if not op['success']]
            
            success_rate = len(successful_ops) / len(business_operations)
            avg_response_time = sum(op['response_time'] for op in successful_ops) / len(successful_ops) if successful_ops else 0
            
            # 验证系统弹性
            assert success_rate >= 0.8, f"混沌测试成功率应该 >= 80%, 实际: {success_rate:.1%}"
            assert avg_response_time < 0.5, f"混沌测试平均响应时间应该 < 500ms, 实际: {avg_response_time*1000:.1f}ms"
            
            print(f"\\n混沌测试结果:")
            print(f"总操作数: {len(business_operations)}")
            print(f"成功操作: {len(successful_ops)}")
            print(f"失败操作: {len(failed_ops)}")
            print(f"成功率: {success_rate:.1%}")
            print(f"平均响应时间: {avg_response_time*1000:.1f}ms")
            
            # 获取混沌报告
            chaos_report = chaos_monkey.get_chaos_report()
            print(f"混沌场景数: {chaos_report['total_scenarios']}")
            
        finally:
            # 停止性能监控
            performance_monitor.stop_monitoring()
            monitor_task.cancel()
            
            try:
                await monitor_task
            except asyncio.CancelledError:
                pass
        
        print("✓ 随机服务故障混沌测试通过")
    
    @chaos_test("network_partition")
    async def test_network_partition_resilience(
        self,
        scenario_data_manager,
        chaos_monkey
    ):
        """测试网络分区下的系统弹性"""
        print("\\n=== 网络分区弹性测试 ===")
        
        # 阶段1: 正常操作基线
        baseline_results = []
        for i in range(10):
            start_time = time.perf_counter()
            result = await scenario_data_manager.get_stock_info(code=f"{i+1:06d}")
            response_time = time.perf_counter() - start_time
            
            baseline_results.append({
                'success': result is not None,
                'response_time': response_time
            })
        
        baseline_success_rate = sum(1 for r in baseline_results if r['success']) / len(baseline_results)
        baseline_avg_time = sum(r['response_time'] for r in baseline_results if r['success']) / len([r for r in baseline_results if r['success']])
        
        print(f"基线测试: 成功率{baseline_success_rate:.1%}, 平均响应时间{baseline_avg_time*1000:.1f}ms")
        
        # 阶段2: 注入网络分区
        await chaos_monkey.network_partition(duration=10.0)
        
        # 模拟网络分区（部分服务不可达）
        partition_results = []
        
        # 在网络分区期间测试系统行为
        for i in range(20):
            start_time = time.perf_counter()
            
            try:
                # 模拟网络延迟和间歇性连接失败
                if random.random() < 0.3:  # 30%概率网络超时
                    await asyncio.sleep(random.uniform(2.0, 5.0))
                    raise ConnectionError("Network partition - connection timeout")
                
                result = await scenario_data_manager.get_stock_info(code=f"{i+1:06d}")
                response_time = time.perf_counter() - start_time
                
                partition_results.append({
                    'success': result is not None,
                    'response_time': response_time,
                    'from_cache': hasattr(result, 'cached') if result else False
                })
                
            except Exception as e:
                response_time = time.perf_counter() - start_time
                partition_results.append({
                    'success': False,
                    'response_time': response_time,
                    'error': str(e)
                })
            
            await asyncio.sleep(0.2)
        
        # 分析网络分区期间的表现
        partition_successful = [r for r in partition_results if r['success']]
        partition_success_rate = len(partition_successful) / len(partition_results)
        
        cache_hits = sum(1 for r in partition_successful if r.get('from_cache', False))
        cache_hit_rate = cache_hits / len(partition_successful) if partition_successful else 0
        
        print(f"\\n网络分区期间测试:")
        print(f"成功率: {partition_success_rate:.1%}")
        print(f"缓存命中率: {cache_hit_rate:.1%}")
        
        # 网络分区期间系统应该通过缓存等机制保持基本可用性
        assert partition_success_rate >= 0.5, f"网络分区期间成功率应该 >= 50%, 实际: {partition_success_rate:.1%}"
        
        # 如果有缓存命中，说明容错机制工作正常
        if cache_hits > 0:
            print(f"✓ 缓存容错机制工作正常: {cache_hits}次缓存命中")
        
        print("✓ 网络分区弹性测试通过")