"""
配置热加载和系统动态更新测试

测试系统配置的热加载能力和动态更新机制
"""
import pytest
import asyncio
import json
import os
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch, MagicMock

from tests.scenarios.conftest import (
    scenario_test,
    ScenarioTestHelper
)
from app.core.config import SettingsManager


class TestConfigurationHotReload:
    """配置热加载测试"""
    
    @scenario_test("config_hot_reload")
    async def test_cache_config_hot_reload(
        self,
        scenario_data_manager,
        temp_config_dir,
        config_hot_reload,
        scenario_helper: ScenarioTestHelper
    ):
        """测试缓存配置热加载"""
        print("\\n=== 缓存配置热加载测试 ===")
        
        # 初始配置
        initial_config = {
            "cache": {
                "enabled": True,
                "default_ttl": 300,
                "max_memory_mb": 50,
                "compression_enabled": False
            },
            "app_name": "quantization",
            "debug": False
        }
        
        reloader = config_hot_reload(temp_config_dir)
        reloader.create_config_file(initial_config)
        
        # 阶段1: 使用初始配置
        print("使用初始配置...")
        
        # 模拟配置加载
        with patch.object(SettingsManager, '_config_file_path', reloader.get_config_file_path()):
            original_ttl = initial_config["cache"]["default_ttl"]
            
            # 缓存一些数据
            cache_keys = []
            for i in range(5):
                result = await scenario_data_manager.get_stock_info(code=f"{i+1:06d}")
                if result:
                    cache_keys.append(f"stock_info:{i+1:06d}")
            
            print(f"缓存了 {len(cache_keys)} 个股票信息，TTL: {original_ttl}s")
            
            # 阶段2: 热更新配置
            print("\\n热更新配置...")
            updated_config = {
                "cache": {
                    "enabled": True,
                    "default_ttl": 600,  # 从300s改为600s
                    "max_memory_mb": 100,  # 从50MB改为100MB
                    "compression_enabled": True  # 启用压缩
                },
                "app_name": "quantization",
                "debug": True  # 启用调试模式
            }
            
            reloader.update_config_file(updated_config)
            
            # 触发配置重新加载
            if hasattr(scenario_data_manager, 'reload_config'):
                await scenario_data_manager.reload_config()
            
            # 等待配置生效
            await asyncio.sleep(1.0)
            
            # 阶段3: 验证新配置生效
            print("验证新配置...")
            
            # 检查新的TTL设置
            new_cache_result = await scenario_data_manager.get_stock_info(code="000100")
            
            # 验证压缩设置是否生效
            if hasattr(scenario_data_manager, 'get_cache_config'):
                current_cache_config = await scenario_data_manager.get_cache_config()
                
                assert current_cache_config.get('default_ttl') == 600, "TTL应该更新为600s"
                assert current_cache_config.get('max_memory_mb') == 100, "最大内存应该更新为100MB"
                assert current_cache_config.get('compression_enabled') is True, "压缩应该启用"
                
                print(f"✓ 配置更新成功: {current_cache_config}")
            
            # 验证已存在的缓存项是否受影响
            existing_cache_count = 0
            for key in cache_keys[:3]:  # 检查前3个
                if hasattr(scenario_data_manager, 'check_cache_exists'):
                    exists = await scenario_data_manager.check_cache_exists(key)
                    if exists:
                        existing_cache_count += 1
            
            print(f"热更新后仍存在的缓存项: {existing_cache_count}/{min(3, len(cache_keys))}")
            
        print("✓ 缓存配置热加载测试通过")
    
    @scenario_test("routing_config_reload")
    async def test_routing_config_hot_reload(
        self,
        scenario_data_manager,
        temp_config_dir,
        config_hot_reload
    ):
        """测试路由配置热加载"""
        print("\\n=== 路由配置热加载测试 ===")
        
        # 初始路由配置
        initial_config = {
            "routing": {
                "primary_source": "mairui",
                "fallback_sources": ["tushare"],
                "failover_enabled": True,
                "timeout_seconds": 30,
                "max_retries": 3
            }
        }
        
        reloader = config_hot_reload(temp_config_dir)
        reloader.create_config_file(initial_config)
        
        # 阶段1: 测试初始路由配置
        routing_stats = {"mairui": 0, "tushare": 0, "akshare": 0}
        
        def mock_route_call(source_name, *args, **kwargs):
            routing_stats[source_name] += 1
            return {"code": "000001", "name": f"来自{source_name}"}
        
        with patch.object(scenario_data_manager, '_route_to_source', side_effect=mock_route_call):
            # 执行一些请求
            for i in range(5):
                await scenario_data_manager.get_stock_info(code=f"{i+1:06d}")
        
        print(f"初始路由统计: {routing_stats}")
        initial_primary_calls = routing_stats["mairui"]
        
        # 阶段2: 热更新路由配置
        updated_config = {
            "routing": {
                "primary_source": "akshare",  # 改变主数据源
                "fallback_sources": ["mairui", "tushare"],  # 调整备用源顺序
                "failover_enabled": True,
                "timeout_seconds": 60,  # 增加超时时间
                "max_retries": 5  # 增加重试次数
            }
        }
        
        reloader.update_config_file(updated_config)
        
        # 触发路由配置重新加载
        if hasattr(scenario_data_manager, 'reload_routing_config'):
            await scenario_data_manager.reload_routing_config()
        
        # 清零统计
        routing_stats = {"mairui": 0, "tushare": 0, "akshare": 0}
        
        # 阶段3: 验证新路由配置
        with patch.object(scenario_data_manager, '_route_to_source', side_effect=mock_route_call):
            # 执行请求，应该路由到新的主数据源
            for i in range(5):
                await scenario_data_manager.get_stock_info(code=f"{i+10:06d}")
        
        print(f"更新后路由统计: {routing_stats}")
        
        # 验证主数据源已切换
        assert routing_stats["akshare"] > 0, "新的主数据源akshare应该有调用"
        assert routing_stats["akshare"] >= routing_stats["mairui"], "akshare作为主数据源应该有更多调用"
        
        print("✓ 路由配置热加载测试通过")
    
    @scenario_test("feature_toggle_reload")
    async def test_feature_toggle_hot_reload(
        self,
        scenario_data_manager,
        temp_config_dir,
        config_hot_reload
    ):
        """测试功能开关热加载"""
        print("\\n=== 功能开关热加载测试 ===")
        
        # 初始功能配置
        initial_config = {
            "features": {
                "real_time_data_enabled": True,
                "advanced_analytics_enabled": False,
                "data_compression_enabled": False,
                "experimental_cache_enabled": False
            },
            "debug_mode": False
        }
        
        reloader = config_hot_reload(temp_config_dir)
        reloader.create_config_file(initial_config)
        
        # 阶段1: 测试初始功能状态
        feature_usage = {
            "real_time_calls": 0,
            "analytics_calls": 0,
            "compression_calls": 0
        }
        
        # 模拟功能调用
        for i in range(10):
            # 实时数据功能（初始启用）
            if initial_config["features"]["real_time_data_enabled"]:
                if hasattr(scenario_data_manager, 'get_real_time_data'):
                    await scenario_data_manager.get_real_time_data(code=f"{i+1:06d}")
                    feature_usage["real_time_calls"] += 1
            
            # 高级分析功能（初始禁用）
            if initial_config["features"]["advanced_analytics_enabled"]:
                if hasattr(scenario_data_manager, 'get_advanced_analytics'):
                    await scenario_data_manager.get_advanced_analytics(code=f"{i+1:06d}")
                    feature_usage["analytics_calls"] += 1
        
        print(f"初始功能使用统计: {feature_usage}")
        
        # 实时数据应该被调用，高级分析不应该
        assert feature_usage["real_time_calls"] > 0, "实时数据功能应该被调用"
        assert feature_usage["analytics_calls"] == 0, "高级分析功能应该被禁用"
        
        # 阶段2: 热更新功能开关
        updated_config = {
            "features": {
                "real_time_data_enabled": False,  # 禁用实时数据
                "advanced_analytics_enabled": True,  # 启用高级分析
                "data_compression_enabled": True,  # 启用数据压缩
                "experimental_cache_enabled": True  # 启用实验性缓存
            },
            "debug_mode": True  # 启用调试模式
        }
        
        reloader.update_config_file(updated_config)
        
        # 触发功能配置重新加载
        if hasattr(scenario_data_manager, 'reload_feature_config'):
            await scenario_data_manager.reload_feature_config()
        
        await asyncio.sleep(0.5)  # 等待配置生效
        
        # 阶段3: 验证新功能配置
        feature_usage_after = {
            "real_time_calls": 0,
            "analytics_calls": 0,
            "compression_calls": 0
        }
        
        for i in range(10):
            # 实时数据功能（现在禁用）
            try:
                if hasattr(scenario_data_manager, 'is_feature_enabled'):
                    if await scenario_data_manager.is_feature_enabled('real_time_data_enabled'):
                        await scenario_data_manager.get_real_time_data(code=f"{i+20:06d}")
                        feature_usage_after["real_time_calls"] += 1
            except Exception as e:
                # 功能禁用可能抛出异常
                print(f"实时数据功能禁用: {str(e)}")
            
            # 高级分析功能（现在启用）
            try:
                if hasattr(scenario_data_manager, 'is_feature_enabled'):
                    if await scenario_data_manager.is_feature_enabled('advanced_analytics_enabled'):
                        # 模拟高级分析调用
                        feature_usage_after["analytics_calls"] += 1
            except Exception:
                pass
            
            # 数据压缩功能（新启用）
            try:
                if hasattr(scenario_data_manager, 'is_feature_enabled'):
                    if await scenario_data_manager.is_feature_enabled('data_compression_enabled'):
                        feature_usage_after["compression_calls"] += 1
            except Exception:
                pass
        
        print(f"更新后功能使用统计: {feature_usage_after}")
        
        # 验证功能开关生效
        assert feature_usage_after["real_time_calls"] == 0, "实时数据功能应该被禁用"
        
        if feature_usage_after["analytics_calls"] > 0:
            print("✓ 高级分析功能已启用")
        
        if feature_usage_after["compression_calls"] > 0:
            print("✓ 数据压缩功能已启用")
        
        print("✓ 功能开关热加载测试通过")


class TestDynamicScaling:
    """动态扩缩容测试"""
    
    @scenario_test("worker_scaling")
    async def test_dynamic_worker_scaling(
        self,
        scenario_data_manager,
        performance_monitor,
        scenario_helper: ScenarioTestHelper
    ):
        """测试工作线程动态扩缩容"""
        print("\\n=== 动态工作线程扩缩容测试 ===")
        
        # 启动性能监控
        monitor_task = asyncio.create_task(performance_monitor.start_monitoring())
        
        try:
            # 阶段1: 低负载基线测试
            print("阶段1: 低负载基线测试")
            
            initial_worker_count = getattr(scenario_data_manager, '_worker_count', 3)
            print(f"初始工作线程数: {initial_worker_count}")
            
            # 执行低负载任务
            low_load_times = []
            for i in range(10):
                start_time = time.perf_counter()
                await scenario_data_manager.get_stock_info(code=f"{i+1:06d}")
                response_time = time.perf_counter() - start_time
                low_load_times.append(response_time)
                
                performance_monitor.record_response_time("low_load", response_time)
                await asyncio.sleep(0.1)
            
            avg_low_load_time = sum(low_load_times) / len(low_load_times)
            print(f"低负载平均响应时间: {avg_low_load_time*1000:.1f}ms")
            
            # 阶段2: 高负载触发扩容
            print("\\n阶段2: 高负载触发扩容")
            
            # 生成高并发负载
            high_load_tasks = []
            high_load_start = time.perf_counter()
            
            for i in range(50):  # 50个并发请求
                task = asyncio.create_task(
                    scenario_data_manager.get_stock_info(code=f"{i%20+1:06d}")
                )
                high_load_tasks.append(task)
            
            # 等待所有任务完成
            high_load_results = await asyncio.gather(*high_load_tasks, return_exceptions=True)
            high_load_total_time = time.perf_counter() - high_load_start
            
            successful_results = [r for r in high_load_results if r is not None and not isinstance(r, Exception)]
            high_load_success_rate = len(successful_results) / len(high_load_results)
            high_load_throughput = len(successful_results) / high_load_total_time
            
            print(f"高负载测试结果:")
            print(f"成功率: {high_load_success_rate:.1%}")
            print(f"吞吐量: {high_load_throughput:.1f} req/s")
            print(f"总耗时: {high_load_total_time:.2f}s")
            
            # 检查是否触发了扩容
            if hasattr(scenario_data_manager, 'get_current_worker_count'):
                current_worker_count = await scenario_data_manager.get_current_worker_count()
                if current_worker_count > initial_worker_count:
                    print(f"✓ 检测到扩容: {initial_worker_count} -> {current_worker_count} 工作线程")
                else:
                    print(f"工作线程数保持: {current_worker_count}")
            
            # 阶段3: 负载降低后的自动缩容
            print("\\n阶段3: 负载降低后的缩容测试")
            
            # 等待一段时间让系统检测到负载降低
            await asyncio.sleep(5.0)
            
            # 执行低负载任务
            cooldown_times = []
            for i in range(10):
                start_time = time.perf_counter()
                await scenario_data_manager.get_stock_info(code=f"{i+100:06d}")
                response_time = time.perf_counter() - start_time
                cooldown_times.append(response_time)
                
                await asyncio.sleep(0.5)  # 较长间隔模拟低负载
            
            avg_cooldown_time = sum(cooldown_times) / len(cooldown_times)
            print(f"负载降低后平均响应时间: {avg_cooldown_time*1000:.1f}ms")
            
            # 检查是否发生了缩容
            if hasattr(scenario_data_manager, 'get_current_worker_count'):
                final_worker_count = await scenario_data_manager.get_current_worker_count()
                print(f"最终工作线程数: {final_worker_count}")
            
            # 性能验证
            assert high_load_success_rate >= 0.9, f"高负载成功率应该 >= 90%, 实际: {high_load_success_rate:.1%}"
            assert high_load_throughput >= 10, f"高负载吞吐量应该 >= 10 req/s, 实际: {high_load_throughput:.1f}"
            
        finally:
            # 停止性能监控
            performance_monitor.stop_monitoring()
            monitor_task.cancel()
            
            try:
                await monitor_task
            except asyncio.CancelledError:
                pass
        
        print("✓ 动态工作线程扩缩容测试通过")
    
    @scenario_test("memory_scaling")
    async def test_dynamic_memory_scaling(
        self,
        scenario_data_manager,
        scenario_helper: ScenarioTestHelper
    ):
        """测试动态内存扩缩容"""
        print("\\n=== 动态内存扩缩容测试 ===")
        
        # 阶段1: 监控初始内存使用
        if hasattr(scenario_data_manager, 'get_memory_usage'):
            initial_memory = await scenario_data_manager.get_memory_usage()
            print(f"初始内存使用: {initial_memory}MB")
        else:
            initial_memory = 0
        
        # 阶段2: 逐步增加内存压力
        print("逐步增加内存压力...")
        
        # 生成大量测试数据
        large_dataset = scenario_helper.create_test_data_set(size=500, data_type="stock_info")
        
        memory_snapshots = []
        cache_scaling_events = []
        
        for i, batch_start in enumerate(range(0, len(large_dataset), 50)):
            batch = large_dataset[batch_start:batch_start + 50]
            
            # 批量处理数据
            for stock_data in batch:
                await scenario_data_manager.get_stock_info(code=stock_data['code'])
            
            # 记录内存使用情况
            if hasattr(scenario_data_manager, 'get_memory_usage'):
                current_memory = await scenario_data_manager.get_memory_usage()
                memory_snapshots.append({
                    'batch': i + 1,
                    'memory_mb': current_memory,
                    'data_processed': (i + 1) * 50
                })
                
                print(f"批次{i+1}: 处理{(i+1)*50}个股票, 内存使用: {current_memory}MB")
                
                # 检查是否触发了内存扩展
                if current_memory > initial_memory * 1.5:  # 内存增长50%以上
                    cache_scaling_events.append({
                        'type': 'memory_expansion',
                        'batch': i + 1,
                        'memory_before': initial_memory,
                        'memory_after': current_memory
                    })
            
            # 检查是否有缓存淘汰
            if hasattr(scenario_data_manager, 'get_cache_eviction_count'):
                eviction_count = await scenario_data_manager.get_cache_eviction_count()
                if eviction_count > 0:
                    cache_scaling_events.append({
                        'type': 'cache_eviction',
                        'batch': i + 1,
                        'evictions': eviction_count
                    })
            
            await asyncio.sleep(0.1)  # 短暂休息
        
        # 阶段3: 内存压力释放
        print("\\n内存压力释放...")
        
        # 清理缓存触发内存释放
        if hasattr(scenario_data_manager, 'cleanup_cache'):
            await scenario_data_manager.cleanup_cache(aggressive=True)
        
        await asyncio.sleep(2.0)  # 等待内存释放
        
        if hasattr(scenario_data_manager, 'get_memory_usage'):
            final_memory = await scenario_data_manager.get_memory_usage()
            print(f"清理后内存使用: {final_memory}MB")
            
            memory_reduction = initial_memory - final_memory if final_memory < initial_memory else 0
            if memory_reduction > 0:
                print(f"✓ 内存成功释放: {memory_reduction}MB")
        
        # 分析内存扩缩容效果
        if memory_snapshots:
            max_memory = max(snapshot['memory_mb'] for snapshot in memory_snapshots)
            memory_growth_ratio = max_memory / initial_memory if initial_memory > 0 else 1
            
            print(f"\\n内存扩缩容分析:")
            print(f"最大内存使用: {max_memory}MB")
            print(f"内存增长倍数: {memory_growth_ratio:.1f}x")
            print(f"扩缩容事件数: {len(cache_scaling_events)}")
            
            for event in cache_scaling_events:
                print(f"- {event}")
            
            # 验证内存管理有效性
            assert memory_growth_ratio < 10, f"内存增长应该受控, 实际增长: {memory_growth_ratio:.1f}x"
        
        print("✓ 动态内存扩缩容测试通过")


class TestSystemLifecycle:
    """系统生命周期测试"""
    
    @scenario_test("graceful_startup")
    async def test_graceful_system_startup(
        self,
        scenario_helper: ScenarioTestHelper
    ):
        """测试系统优雅启动"""
        print("\\n=== 系统优雅启动测试 ===")
        
        from app.core.data.manager import UnifiedDataManager
        from app.core.data.models import DataManagerConfig
        
        startup_phases = []
        
        # 配置启动监控
        config = DataManagerConfig(
            cache={"enabled": True, "default_ttl": 300},
            routing={"failover_enabled": True},
            worker_count=3
        )
        
        # 阶段1: 初始化
        print("阶段1: 系统初始化...")
        init_start = time.perf_counter()
        
        manager = UnifiedDataManager(config)
        
        init_time = time.perf_counter() - init_start
        startup_phases.append(("initialization", init_time))
        print(f"初始化耗时: {init_time*1000:.1f}ms")
        
        # 阶段2: 启动核心服务
        print("阶段2: 启动核心服务...")
        startup_start = time.perf_counter()
        
        await manager.start()
        
        startup_time = time.perf_counter() - startup_start
        startup_phases.append(("service_startup", startup_time))
        print(f"服务启动耗时: {startup_time*1000:.1f}ms")
        
        # 阶段3: 健康检查
        print("阶段3: 系统健康检查...")
        health_start = time.perf_counter()
        
        health_checks = []
        
        # 检查缓存系统
        try:
            if hasattr(manager, 'check_cache_health'):
                cache_healthy = await manager.check_cache_health()
                health_checks.append(("cache", cache_healthy))
        except:
            health_checks.append(("cache", False))
        
        # 检查路由系统
        try:
            if hasattr(manager, 'check_routing_health'):
                routing_healthy = await manager.check_routing_health()
                health_checks.append(("routing", routing_healthy))
        except:
            health_checks.append(("routing", False))
        
        # 检查工作线程
        try:
            if hasattr(manager, 'check_workers_health'):
                workers_healthy = await manager.check_workers_health()
                health_checks.append(("workers", workers_healthy))
        except:
            health_checks.append(("workers", False))
        
        health_time = time.perf_counter() - health_start
        startup_phases.append(("health_check", health_time))
        
        healthy_services = sum(1 for _, healthy in health_checks if healthy)
        total_services = len(health_checks)
        
        print(f"健康检查耗时: {health_time*1000:.1f}ms")
        print(f"服务健康状态: {healthy_services}/{total_services}")
        
        # 阶段4: 预热操作
        print("阶段4: 系统预热...")
        warmup_start = time.perf_counter()
        
        # 执行预热请求
        warmup_requests = []
        for i in range(5):
            try:
                result = await manager.get_stock_info(code=f"{i+1:06d}")
                warmup_requests.append(result is not None)
            except:
                warmup_requests.append(False)
        
        warmup_time = time.perf_counter() - warmup_start
        startup_phases.append(("warmup", warmup_time))
        
        warmup_success_rate = sum(warmup_requests) / len(warmup_requests)
        print(f"预热耗时: {warmup_time*1000:.1f}ms")
        print(f"预热成功率: {warmup_success_rate:.1%}")
        
        # 验证启动性能
        total_startup_time = sum(phase_time for _, phase_time in startup_phases)
        
        assert total_startup_time < 10.0, f"总启动时间应该 < 10s, 实际: {total_startup_time:.2f}s"
        assert warmup_success_rate >= 0.8, f"预热成功率应该 >= 80%, 实际: {warmup_success_rate:.1%}"
        
        print(f"\\n启动阶段汇总:")
        for phase_name, phase_time in startup_phases:
            print(f"{phase_name}: {phase_time*1000:.1f}ms")
        print(f"总启动时间: {total_startup_time*1000:.1f}ms")
        
        # 清理
        await manager.shutdown()
        
        print("✓ 系统优雅启动测试通过")
    
    @scenario_test("graceful_shutdown")
    async def test_graceful_system_shutdown(
        self,
        scenario_data_manager,
        scenario_helper: ScenarioTestHelper
    ):
        """测试系统优雅关闭"""
        print("\\n=== 系统优雅关闭测试 ===")
        
        # 阶段1: 在活跃状态下准备关闭
        print("阶段1: 系统活跃状态...")
        
        # 启动一些后台任务
        background_tasks = []
        
        async def background_work(task_id: int):
            for i in range(20):
                try:
                    await scenario_data_manager.get_stock_info(code=f"{task_id*100 + i:06d}")
                    await asyncio.sleep(0.1)
                except Exception:
                    break
        
        # 启动3个后台任务
        for i in range(3):
            task = asyncio.create_task(background_work(i))
            background_tasks.append(task)
        
        # 让任务运行一段时间
        await asyncio.sleep(1.0)
        
        print(f"启动了 {len(background_tasks)} 个后台任务")
        
        # 阶段2: 发起优雅关闭
        print("阶段2: 发起优雅关闭...")
        shutdown_start = time.perf_counter()
        
        shutdown_phases = []
        
        # 停止接受新请求
        if hasattr(scenario_data_manager, 'stop_accepting_requests'):
            stop_accepting_start = time.perf_counter()
            await scenario_data_manager.stop_accepting_requests()
            stop_accepting_time = time.perf_counter() - stop_accepting_start
            shutdown_phases.append(("stop_accepting", stop_accepting_time))
            print(f"停止接受新请求: {stop_accepting_time*1000:.1f}ms")
        
        # 等待正在处理的请求完成
        if hasattr(scenario_data_manager, 'wait_for_active_requests'):
            wait_requests_start = time.perf_counter()
            await scenario_data_manager.wait_for_active_requests(timeout=5.0)
            wait_requests_time = time.perf_counter() - wait_requests_start
            shutdown_phases.append(("wait_requests", wait_requests_time))
            print(f"等待活跃请求完成: {wait_requests_time*1000:.1f}ms")
        
        # 取消后台任务
        cancel_tasks_start = time.perf_counter()
        for task in background_tasks:
            task.cancel()
        
        cancelled_tasks = 0
        for task in background_tasks:
            try:
                await task
            except asyncio.CancelledError:
                cancelled_tasks += 1
            except Exception:
                pass
        
        cancel_tasks_time = time.perf_counter() - cancel_tasks_start
        shutdown_phases.append(("cancel_tasks", cancel_tasks_time))
        print(f"取消后台任务: {cancelled_tasks}/{len(background_tasks)}, 耗时: {cancel_tasks_time*1000:.1f}ms")
        
        # 持久化缓存数据
        if hasattr(scenario_data_manager, 'persist_cache'):
            persist_start = time.perf_counter()
            persisted_items = await scenario_data_manager.persist_cache()
            persist_time = time.perf_counter() - persist_start
            shutdown_phases.append(("persist_cache", persist_time))
            print(f"持久化缓存: {persisted_items} 项, 耗时: {persist_time*1000:.1f}ms")
        
        # 关闭连接
        if hasattr(scenario_data_manager, 'close_connections'):
            close_conn_start = time.perf_counter()
            await scenario_data_manager.close_connections()
            close_conn_time = time.perf_counter() - close_conn_start
            shutdown_phases.append(("close_connections", close_conn_time))
            print(f"关闭连接: {close_conn_time*1000:.1f}ms")
        
        # 清理资源
        cleanup_start = time.perf_counter()
        await scenario_data_manager.shutdown()
        cleanup_time = time.perf_counter() - cleanup_start
        shutdown_phases.append(("cleanup", cleanup_time))
        
        total_shutdown_time = time.perf_counter() - shutdown_start
        
        print(f"资源清理: {cleanup_time*1000:.1f}ms")
        print(f"总关闭时间: {total_shutdown_time*1000:.1f}ms")
        
        # 验证关闭性能和完整性
        assert total_shutdown_time < 10.0, f"总关闭时间应该 < 10s, 实际: {total_shutdown_time:.2f}s"
        assert cancelled_tasks == len(background_tasks), f"所有后台任务应该被取消, 实际取消: {cancelled_tasks}/{len(background_tasks)}"
        
        print(f"\\n关闭阶段汇总:")
        for phase_name, phase_time in shutdown_phases:
            print(f"{phase_name}: {phase_time*1000:.1f}ms")
        
        print("✓ 系统优雅关闭测试通过")