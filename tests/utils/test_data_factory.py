"""任务历史测试数据工厂

用于生成各种测试场景所需的模拟数据，包括：
- 不同状态的任务执行记录
- 多种触发类型的任务
- 权限测试用户数据
- 性能测试大数据集
- 边界条件测试数据
"""

import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from unittest.mock import Mock
from enum import Enum

from app.schemas.scheduled_task import TaskType, TaskStatus, TriggerType


class TestDataFactory:
    """测试数据工厂类"""
    
    def __init__(self):
        self.base_time = datetime.now()
        self.user_counter = 1
        self.task_counter = 1
        self.execution_counter = 1
    
    def create_user(self, username: str = None, email: str = None, is_admin: bool = False) -> Mock:
        """创建模拟用户对象"""
        if username is None:
            username = f"user{self.user_counter}"
        if email is None:
            email = f"user{self.user_counter}@test.com"
        
        user = Mock()
        user.id = self.user_counter
        user.username = username
        user.email = email
        user.is_admin = is_admin
        user.created_at = self.base_time - timedelta(days=30)
        user.updated_at = self.base_time - timedelta(days=1)
        
        self.user_counter += 1
        return user
    
    def create_admin_user(self, username: str = "admin", email: str = "<EMAIL>") -> Mock:
        """创建模拟管理员用户"""
        return self.create_user(username, email, is_admin=True)
    
    def create_scheduled_task(self, user_id: int, name: str = None, 
                            is_active: bool = True) -> Mock:
        """创建模拟定时任务对象"""
        if name is None:
            name = f"测试任务{self.task_counter}"
        
        task = Mock()
        task.id = self.task_counter
        task.user_id = user_id
        task.name = name
        task.task_type = TaskType.INDICATOR_SCAN
        task.cron_expression = "0 9 * * *"  # 每天9点
        task.is_active = is_active
        task.max_executions = None
        task.current_executions = 0
        task.task_config = json.dumps({
            "indicators": ["MACD", "KDJ"],
            "stock_codes": ["000001.SZ", "600000.SH"],
            "parameters": {"period": 20}
        })
        task.description = f"这是{name}的描述"
        task.last_execution = self.base_time - timedelta(hours=24)
        task.next_execution = self.base_time + timedelta(hours=24)
        task.created_at = self.base_time - timedelta(days=7)
        task.updated_at = self.base_time - timedelta(days=1)
        
        self.task_counter += 1
        return task
    
    def create_task_execution(
        self,
        user_id: int,
        trigger_type: TriggerType = TriggerType.SCHEDULED,
        status: TaskStatus = TaskStatus.COMPLETED,
        scheduled_task_id: Optional[int] = None,
        scheduled_task_name: Optional[str] = None,
        results_count: int = 10,
        error_message: Optional[str] = None,
        duration_minutes: int = 5
    ) -> Mock:
        """创建模拟任务执行记录对象"""
        
        execution = Mock()
        execution.id = self.execution_counter
        execution.user_id = user_id
        execution.scheduled_task_id = scheduled_task_id
        execution.trigger_type = trigger_type
        execution.task_type = TaskType.INDICATOR_SCAN
        execution.status = status
        
        # 时间设置
        if status == TaskStatus.PENDING:
            execution.start_time = None
            execution.end_time = None
            execution.duration_seconds = None
        elif status == TaskStatus.RUNNING:
            execution.start_time = self.base_time - timedelta(minutes=2)
            execution.end_time = None
            execution.duration_seconds = None
        else:  # COMPLETED, FAILED, CANCELLED
            execution.start_time = self.base_time - timedelta(minutes=duration_minutes + 2)
            execution.end_time = self.base_time - timedelta(minutes=2)
            execution.duration_seconds = duration_minutes * 60
        
        execution.results_count = results_count if status == TaskStatus.COMPLETED else 0
        execution.error_message = error_message
        
        # 结果数据
        if status == TaskStatus.COMPLETED and results_count > 0:
            results = []
            for i in range(min(results_count, 5)):  # 最多生成5条示例结果
                results.append({
                    "symbol": f"{(i+1)*1000:06d}.SZ",
                    "name": f"股票{i+1}",
                    "signal": "buy" if i % 2 == 0 else "sell",
                    "strength": round(0.5 + (i * 0.1), 2),
                    "price": round(10 + i * 2.5, 2)
                })
            execution.results_data = json.dumps(results, ensure_ascii=False)
        else:
            execution.results_data = None
        
        # 任务配置
        execution.task_config = json.dumps({
            "indicators": ["MACD", "RSI"],
            "periods": ["d"],
            "adjust": "n"
        })
        
        execution.metadata = json.dumps({
            "version": "1.0",
            "source": "test_factory"
        })
        
        execution.created_at = self.base_time - timedelta(minutes=duration_minutes + 5)
        execution.updated_at = execution.end_time or execution.start_time or execution.created_at
        
        # 关联的定时任务（如果有）
        if scheduled_task_id and scheduled_task_name:
            mock_task = Mock()
            mock_task.id = scheduled_task_id
            mock_task.name = scheduled_task_name
            execution.scheduled_task = mock_task
        else:
            execution.scheduled_task = None
        
        # 创建__dict__用于响应构建
        execution.__dict__ = {
            'id': execution.id,
            'user_id': execution.user_id,
            'scheduled_task_id': execution.scheduled_task_id,
            'trigger_type': execution.trigger_type,
            'task_type': execution.task_type,
            'status': execution.status,
            'start_time': execution.start_time,
            'end_time': execution.end_time,
            'duration_seconds': execution.duration_seconds,
            'results_count': execution.results_count,
            'error_message': execution.error_message,
            'results_data': execution.results_data,
            'task_config': execution.task_config,
            'metadata': execution.metadata,
            'created_at': execution.created_at,
            'updated_at': execution.updated_at
        }
        
        self.execution_counter += 1
        return execution
    
    def create_multiple_executions(
        self,
        user_id: int,
        count: int,
        status_distribution: Dict[TaskStatus, float] = None,
        trigger_distribution: Dict[TriggerType, float] = None
    ) -> List[Mock]:
        """创建多个任务执行记录"""
        
        if status_distribution is None:
            status_distribution = {
                TaskStatus.COMPLETED: 0.7,
                TaskStatus.FAILED: 0.2,
                TaskStatus.RUNNING: 0.05,
                TaskStatus.CANCELLED: 0.05
            }
        
        if trigger_distribution is None:
            trigger_distribution = {
                TriggerType.SCHEDULED: 0.6,
                TriggerType.MANUAL: 0.4
            }
        
        executions = []
        
        for i in range(count):
            # 根据分布选择状态和触发类型
            status = self._weighted_choice(status_distribution, i)
            trigger_type = self._weighted_choice(trigger_distribution, i)
            
            # 生成其他参数
            scheduled_task_id = i + 1 if trigger_type == TriggerType.SCHEDULED else None
            scheduled_task_name = f"定时任务{i+1}" if scheduled_task_id else None
            results_count = (i % 20) + 1 if status == TaskStatus.COMPLETED else 0
            error_message = f"执行错误{i+1}" if status == TaskStatus.FAILED else None
            duration = (i % 10) + 1
            
            execution = self.create_task_execution(
                user_id=user_id,
                trigger_type=trigger_type,
                status=status,
                scheduled_task_id=scheduled_task_id,
                scheduled_task_name=scheduled_task_name,
                results_count=results_count,
                error_message=error_message,
                duration_minutes=duration
            )
            
            # 调整创建时间，使其有时间分布
            new_created_at = self.base_time - timedelta(hours=i)
            execution.__dict__['created_at'] = new_created_at
            
            executions.append(execution)
        
        return executions
    
    def _weighted_choice(self, choices: Dict, seed: int):
        """根据权重选择选项"""
        import random
        random.seed(seed)
        
        total_weight = sum(choices.values())
        r = random.uniform(0, total_weight)
        
        cumulative = 0
        for choice, weight in choices.items():
            cumulative += weight
            if r <= cumulative:
                return choice
        
        return list(choices.keys())[0]  # 默认返回第一个
    
    def create_mixed_user_scenario(self) -> Dict[str, Any]:
        """创建混合用户场景的测试数据"""
        
        # 创建用户
        regular_user1 = self.create_user("user1", "<EMAIL>", False)
        regular_user2 = self.create_user("user2", "<EMAIL>", False)
        admin_user = self.create_admin_user("admin", "<EMAIL>")
        
        users = [regular_user1, regular_user2, admin_user]
        
        # 为每个用户创建执行记录
        all_executions = []
        
        # 用户1的记录（主要是定时任务）
        user1_executions = self.create_multiple_executions(
            user_id=regular_user1.id,
            count=15,
            status_distribution={
                TaskStatus.COMPLETED: 0.8,
                TaskStatus.FAILED: 0.15,
                TaskStatus.RUNNING: 0.05
            },
            trigger_distribution={
                TriggerType.SCHEDULED: 0.8,
                TriggerType.MANUAL: 0.2
            }
        )
        
        # 为执行记录添加用户信息
        for execution in user1_executions:
            execution.user = regular_user1
        
        all_executions.extend(user1_executions)
        
        # 用户2的记录（主要是手动任务）
        user2_executions = self.create_multiple_executions(
            user_id=regular_user2.id,
            count=10,
            status_distribution={
                TaskStatus.COMPLETED: 0.6,
                TaskStatus.FAILED: 0.3,
                TaskStatus.CANCELLED: 0.1
            },
            trigger_distribution={
                TriggerType.MANUAL: 0.9,
                TriggerType.SCHEDULED: 0.1
            }
        )
        
        for execution in user2_executions:
            execution.user = regular_user2
        
        all_executions.extend(user2_executions)
        
        # 管理员的记录（少量）
        admin_executions = self.create_multiple_executions(
            user_id=admin_user.id,
            count=5,
            status_distribution={
                TaskStatus.COMPLETED: 1.0
            },
            trigger_distribution={
                TriggerType.SCHEDULED: 0.6,
                TriggerType.MANUAL: 0.4
            }
        )
        
        for execution in admin_executions:
            execution.user = admin_user
        
        all_executions.extend(admin_executions)
        
        return {
            'users': users,
            'regular_user1': regular_user1,
            'regular_user2': regular_user2,
            'admin_user': admin_user,
            'all_executions': all_executions,
            'user1_executions': user1_executions,
            'user2_executions': user2_executions,
            'admin_executions': admin_executions,
            'total_executions': len(all_executions)
        }
    
    def create_performance_test_data(self, total_records: int = 10000) -> List[Mock]:
        """创建性能测试用的大数据集"""
        print(f"生成 {total_records} 条性能测试记录...")
        
        executions = []
        users_count = min(100, total_records // 50)  # 最多100个用户
        
        for i in range(total_records):
            user_id = (i % users_count) + 1
            
            # 生成多样化的数据
            trigger_type = TriggerType.SCHEDULED if i % 3 == 0 else TriggerType.MANUAL
            
            # 状态分布
            if i % 20 == 0:
                status = TaskStatus.FAILED
                results_count = 0
                error_message = f"错误类型{i % 5 + 1}"
            elif i % 100 == 0:
                status = TaskStatus.RUNNING
                results_count = 0
                error_message = None
            else:
                status = TaskStatus.COMPLETED
                results_count = (i % 50) + 1
                error_message = None
            
            execution = self.create_task_execution(
                user_id=user_id,
                trigger_type=trigger_type,
                status=status,
                scheduled_task_id=i + 1 if trigger_type == TriggerType.SCHEDULED else None,
                scheduled_task_name=f"性能测试任务{i+1}" if trigger_type == TriggerType.SCHEDULED else None,
                results_count=results_count,
                error_message=error_message,
                duration_minutes=(i % 30) + 1
            )
            
            # 分散创建时间
            new_created_at = self.base_time - timedelta(
                days=(i % 90),  # 最近90天
                hours=(i % 24),
                minutes=(i % 60)
            )
            execution.__dict__['created_at'] = new_created_at
            
            # 添加用户信息（用于管理员视图测试）
            mock_user = Mock()
            mock_user.id = user_id
            mock_user.username = f"perfuser{user_id}"
            mock_user.email = f"perfuser{user_id}@test.com"
            execution.user = mock_user
            
            executions.append(execution)
            
            # 每1000条记录打印一次进度
            if (i + 1) % 1000 == 0:
                print(f"已生成 {i + 1}/{total_records} 条记录")
        
        print("性能测试数据生成完成")
        return executions
    
    def create_edge_case_data(self) -> List[Mock]:
        """创建边界条件测试数据"""
        user_id = 1
        edge_cases = []
        
        # 1. 空结果数据的完成任务
        execution1 = self.create_task_execution(
            user_id=user_id,
            status=TaskStatus.COMPLETED,
            results_count=0,
            duration_minutes=1
        )
        execution1.results_data = None
        execution1.__dict__['results_data'] = None
        edge_cases.append(execution1)
        
        # 2. 超长执行时间的任务
        execution2 = self.create_task_execution(
            user_id=user_id,
            status=TaskStatus.COMPLETED,
            duration_minutes=600  # 10小时
        )
        edge_cases.append(execution2)
        
        # 3. 包含特殊字符的错误消息
        execution3 = self.create_task_execution(
            user_id=user_id,
            status=TaskStatus.FAILED,
            error_message="数据库连接失败: 'NoneType' object has no attribute 'execute' 连接字符串包含中文"
        )
        edge_cases.append(execution3)
        
        # 4. 非常大的结果集
        execution4 = self.create_task_execution(
            user_id=user_id,
            status=TaskStatus.COMPLETED,
            results_count=9999
        )
        # 生成大量结果数据
        large_results = []
        for i in range(100):  # 生成100条示例结果
            large_results.append({
                "symbol": f"{(i+1)*1000:06d}.SZ",
                "name": f"股票名称很长的测试股票{i+1}",
                "signal": "buy" if i % 2 == 0 else "sell",
                "strength": round(0.1 + (i * 0.009), 3),
                "price": round(1 + i * 0.1, 3),
                "volume": (i + 1) * 10000,
                "metadata": {"extra_field": f"数据{i+1}"}
            })
        execution4.results_data = json.dumps(large_results, ensure_ascii=False)
        execution4.__dict__['results_data'] = execution4.results_data
        edge_cases.append(execution4)
        
        # 5. 无效的JSON数据
        execution5 = self.create_task_execution(
            user_id=user_id,
            status=TaskStatus.COMPLETED,
            results_count=1
        )
        execution5.results_data = "invalid_json_data{["
        execution5.__dict__['results_data'] = execution5.results_data
        edge_cases.append(execution5)
        
        # 6. 超长任务名称
        execution6 = self.create_task_execution(
            user_id=user_id,
            trigger_type=TriggerType.SCHEDULED,
            scheduled_task_id=1,
            scheduled_task_name="这是一个非常非常非常长的任务名称用于测试前端显示是否正常处理超长文本内容的情况"
        )
        edge_cases.append(execution6)
        
        # 7. 时间字段为None的记录
        execution7 = self.create_task_execution(
            user_id=user_id,
            status=TaskStatus.CANCELLED
        )
        execution7.start_time = None
        execution7.end_time = None
        execution7.duration_seconds = None
        execution7.__dict__.update({
            'start_time': None,
            'end_time': None,
            'duration_seconds': None
        })
        edge_cases.append(execution7)
        
        return edge_cases
    
    def reset_counters(self):
        """重置计数器"""
        self.user_counter = 1
        self.task_counter = 1
        self.execution_counter = 1


# 全局工厂实例
test_factory = TestDataFactory()


def get_test_factory() -> TestDataFactory:
    """获取测试数据工厂实例"""
    return test_factory