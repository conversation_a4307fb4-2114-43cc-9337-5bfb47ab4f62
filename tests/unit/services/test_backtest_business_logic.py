"""
技术指标回测功能 - 业务逻辑服务层测试
测试历史数据查询、指标计算准确性、多周期模式支持、边界日期处理等
"""
import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
import json

from app.core.database import db_session
from app.services.scan.memory_scanner import MemoryScanner
from app.services.tasks.executor import get_task_executor
from app.utils.trading_utils import get_last_trading_date, format_trading_date
from app.models.task import TaskExecution
from app.schemas.scheduled_task import TaskStatus
from tests.utils.test_data_factory import TestDataFactory


class TestBacktestBusinessLogic:
    """回测功能业务逻辑测试"""

    @pytest.fixture
    async def test_user(self):
        """创建测试用户"""
        async with db_session() as db:
            factory = TestDataFactory(db)
            user = await factory.create_test_user("backtest_logic_user")
            yield user

    @pytest.fixture
    def sample_stock_codes(self):
        """样本股票代码"""
        return ["000001", "000002", "600000"]

    @pytest.fixture
    def sample_indicators(self):
        """样本技术指标"""
        return ["kdj", "volume_pressure", "bollinger"]

    @pytest.fixture
    def mock_stock_data(self):
        """模拟股票数据"""
        base_date = datetime(2024, 1, 1)
        data = []
        for i in range(30):  # 30天数据
            date = base_date + timedelta(days=i)
            data.append({
                'date': date,
                'open': 10.0 + (i * 0.1),
                'high': 11.0 + (i * 0.1),
                'low': 9.0 + (i * 0.1),
                'close': 10.5 + (i * 0.1),
                'volume': 1000000 + (i * 10000),
                'amount': 10500000.0 + (i * 100000)
            })
        return data

    @pytest.mark.asyncio
    async def test_trading_date_calculation(self):
        """测试交易日计算逻辑"""
        # 测试获取最近交易日
        with patch('app.utils.trading_utils.datetime') as mock_datetime:
            # 模拟当前时间为工作日
            mock_datetime.now.return_value = datetime(2024, 1, 15, 10, 0, 0)  # 周一
            mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)
            
            trading_date = get_last_trading_date()
            assert isinstance(trading_date, datetime)
            
            # 验证格式化
            formatted = format_trading_date(trading_date)
            assert isinstance(formatted, str)
            assert len(formatted) == 10  # YYYY-MM-DD

    @pytest.mark.asyncio
    async def test_scanner_with_end_date_historical(self, sample_stock_codes, sample_indicators):
        """测试扫描器历史数据查询"""
        scanner = MemoryScanner()
        
        end_date = "2024-01-15"
        
        with patch.object(scanner, '_fetch_stock_data') as mock_fetch:
            mock_fetch.return_value = self.mock_stock_data
            
            # 测试历史扫描
            results = await scanner.scan_stocks(
                stock_codes=sample_stock_codes,
                indicators=sample_indicators,
                end_date=end_date
            )
            
            # 验证数据查询参数
            mock_fetch.assert_called()
            call_args = mock_fetch.call_args[1] if mock_fetch.call_args else {}
            
            # 验证扫描结果
            assert isinstance(results, list)

    @pytest.mark.asyncio
    async def test_scanner_without_end_date_current(self, sample_stock_codes, sample_indicators):
        """测试扫描器当前数据查询（无end_date）"""
        scanner = MemoryScanner()
        
        with patch.object(scanner, '_fetch_stock_data') as mock_fetch:
            mock_fetch.return_value = self.mock_stock_data
            
            # 测试当前扫描（不指定end_date）
            results = await scanner.scan_stocks(
                stock_codes=sample_stock_codes,
                indicators=sample_indicators,
                end_date=None
            )
            
            # 验证数据查询
            mock_fetch.assert_called()
            assert isinstance(results, list)

    @pytest.mark.asyncio
    async def test_task_executor_with_end_date(self, test_user):
        """测试任务执行器处理end_date参数"""
        async with db_session() as db:
            # 创建任务执行记录
            task_config = {
                "indicators": ["kdj"],
                "stock_codes": ["000001"],
                "end_date": "2024-01-15"
            }
            
            execution = TaskExecution(
                user_id=test_user.id,
                scheduled_task_id=None,
                trigger_type="manual",
                task_type="indicator_scan",
                task_config=json.dumps(task_config),
                status=TaskStatus.PENDING
            )
            db.add(execution)
            await db.commit()
            await db.refresh(execution)
            
            # 测试执行器处理
            executor = get_task_executor()
            
            with patch.object(executor, '_execute_scan_task') as mock_execute:
                mock_execute.return_value = AsyncMock()
                
                # 测试任务执行
                await executor.execute_task(execution.id)
                
                # 验证任务配置被正确解析
                mock_execute.assert_called_once()

    def test_indicator_calculation_with_historical_data(self, mock_stock_data):
        """测试指标计算历史数据准确性"""
        from app.services.indicators.indicator_service import IndicatorService
        
        service = IndicatorService()
        
        # 截取特定日期前的数据
        end_date = datetime(2024, 1, 15)
        historical_data = [d for d in mock_stock_data if d['date'] <= end_date]
        
        # 测试KDJ指标计算
        with patch.object(service.storage, 'get_stock_daily_data') as mock_get_data:
            mock_get_data.return_value = historical_data
            
            # 计算指标
            indicators = service.calculate_kdj("000001", period="d")
            
            # 验证计算结果
            if indicators:
                assert all(key in indicators for key in ['k', 'd', 'j'])
                assert all(isinstance(indicators[key], (int, float)) for key in ['k', 'd', 'j'])

    @pytest.mark.asyncio
    async def test_multi_period_backtest_support(self, sample_stock_codes):
        """测试多周期回测支持"""
        scanner = MemoryScanner()
        
        config = {
            "scan_mode": "multi_period",
            "periods": ["d", "w"],
            "period_indicators": {
                "d": ["kdj"],
                "w": ["volume_pressure"]
            },
            "end_date": "2024-01-15"
        }
        
        with patch.object(scanner, '_fetch_multi_period_data') as mock_fetch:
            mock_fetch.return_value = {
                "d": self.mock_stock_data,
                "w": self.mock_stock_data[:5]  # 5个周数据
            }
            
            # 测试多周期扫描
            results = await scanner.scan_multi_period(
                stock_codes=sample_stock_codes,
                config=config
            )
            
            # 验证多周期数据处理
            mock_fetch.assert_called()
            assert isinstance(results, list)

    def test_boundary_date_handling(self):
        """测试边界日期处理"""
        from app.utils.trading_utils import is_trading_day, get_previous_trading_day
        
        # 测试周末日期处理
        saturday = datetime(2024, 1, 13)  # 周六
        sunday = datetime(2024, 1, 14)    # 周日
        monday = datetime(2024, 1, 15)    # 周一
        
        # 如果实现了交易日判断逻辑
        with patch('app.utils.trading_utils.is_trading_day') as mock_is_trading:
            mock_is_trading.side_effect = lambda d: d.weekday() < 5  # 工作日
            
            assert not is_trading_day(saturday)
            assert not is_trading_day(sunday)
            assert is_trading_day(monday)

    @pytest.mark.asyncio
    async def test_data_consistency_across_periods(self):
        """测试不同周期数据一致性"""
        from app.services.indicators.advanced_indicator_service import AdvancedIndicatorService
        
        service = AdvancedIndicatorService()
        
        with patch.object(service, '_get_stock_data') as mock_get_data:
            # 模拟不同周期的数据
            daily_data = self.mock_stock_data
            weekly_data = daily_data[::7]  # 每7天取一个点
            
            mock_get_data.side_effect = lambda code, period, end_date: {
                "d": daily_data,
                "w": weekly_data
            }.get(period, [])
            
            # 测试数据一致性
            daily_result = await service.get_multi_period_indicators(
                "000001", ["d"], "2024-01-15"
            )
            weekly_result = await service.get_multi_period_indicators(
                "000001", ["w"], "2024-01-15"
            )
            
            # 验证数据结构一致性
            if daily_result and weekly_result:
                assert isinstance(daily_result, dict)
                assert isinstance(weekly_result, dict)

    @pytest.mark.asyncio
    async def test_error_handling_invalid_end_date(self, sample_stock_codes, sample_indicators):
        """测试无效end_date的错误处理"""
        scanner = MemoryScanner()
        
        invalid_dates = [
            "2024-13-01",  # 无效月份
            "2024-02-30",  # 无效日期
            "invalid",     # 完全无效
            "2025-12-31"   # 太遥远的未来
        ]
        
        for invalid_date in invalid_dates:
            with pytest.raises((ValueError, Exception)):
                await scanner.scan_stocks(
                    stock_codes=sample_stock_codes,
                    indicators=sample_indicators,
                    end_date=invalid_date
                )

    def test_indicator_calculation_accuracy(self):
        """测试指标计算准确性"""
        # 使用已知数据验证计算结果
        test_data = [
            {'close': 10.0, 'high': 11.0, 'low': 9.0, 'volume': 1000000},
            {'close': 10.5, 'high': 11.5, 'low': 9.5, 'volume': 1100000},
            {'close': 11.0, 'high': 12.0, 'low': 10.0, 'volume': 1200000},
        ]
        
        from app.services.indicators.indicator_service import IndicatorService
        service = IndicatorService()
        
        # 测试KDJ计算
        with patch.object(service.storage, 'get_stock_daily_data') as mock_get_data:
            mock_get_data.return_value = test_data
            
            result = service._calculate_kdj_values(test_data)
            
            if result:
                # 验证计算结果在合理范围内
                assert 0 <= result.get('k', 0) <= 100
                assert 0 <= result.get('d', 0) <= 100

    @pytest.mark.asyncio
    async def test_performance_with_large_historical_range(self):
        """测试大历史时间范围的性能"""
        scanner = MemoryScanner()
        
        # 模拟大量历史数据
        large_dataset = []
        base_date = datetime(2020, 1, 1)
        for i in range(1000):  # 1000天数据
            date = base_date + timedelta(days=i)
            large_dataset.append({
                'date': date,
                'open': 10.0 + (i * 0.01),
                'high': 11.0 + (i * 0.01),
                'low': 9.0 + (i * 0.01),
                'close': 10.5 + (i * 0.01),
                'volume': 1000000,
                'amount': 10500000.0
            })
        
        with patch.object(scanner, '_fetch_stock_data') as mock_fetch:
            mock_fetch.return_value = large_dataset
            
            import time
            start_time = time.time()
            
            # 测试大数据集处理
            results = await scanner.scan_stocks(
                stock_codes=["000001"],
                indicators=["kdj"],
                end_date="2023-12-31"
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # 验证性能（应在合理时间内完成）
            assert processing_time < 10.0  # 10秒内完成
            assert isinstance(results, list)

    @pytest.mark.asyncio
    async def test_data_integrity_with_missing_dates(self):
        """测试缺失日期数据的完整性处理"""
        from app.services.indicators.indicator_service import IndicatorService
        
        service = IndicatorService()
        
        # 模拟有缺失日期的数据
        incomplete_data = [
            {'date': datetime(2024, 1, 1), 'close': 10.0, 'volume': 1000000},
            # 缺失 1月2日
            {'date': datetime(2024, 1, 3), 'close': 10.5, 'volume': 1100000},
            {'date': datetime(2024, 1, 4), 'close': 11.0, 'volume': 1200000},
        ]
        
        with patch.object(service.storage, 'get_stock_daily_data') as mock_get_data:
            mock_get_data.return_value = incomplete_data
            
            # 测试缺失数据处理
            result = service.calculate_kdj("000001", period="d", end_date="2024-01-04")
            
            # 验证处理结果
            assert result is not None or result is None  # 应该有明确的处理结果

    @pytest.mark.asyncio
    async def test_concurrent_backtest_requests(self, test_user):
        """测试并发回测请求处理"""
        import asyncio
        
        async def create_scan_task(task_id):
            async with db_session() as db:
                task_config = {
                    "indicators": ["kdj"],
                    "stock_codes": ["000001"],
                    "end_date": f"2024-01-{15 + task_id:02d}"
                }
                
                execution = TaskExecution(
                    user_id=test_user.id,
                    scheduled_task_id=None,
                    trigger_type="manual",
                    task_type="indicator_scan",
                    task_config=json.dumps(task_config),
                    status=TaskStatus.PENDING
                )
                db.add(execution)
                await db.commit()
                await db.refresh(execution)
                return execution.id
        
        # 创建多个并发任务
        task_ids = await asyncio.gather(*[
            create_scan_task(i) for i in range(3)
        ])
        
        assert len(task_ids) == 3
        assert all(isinstance(tid, int) for tid in task_ids)

    def test_result_serialization_with_end_date(self):
        """测试结果序列化包含end_date"""
        from app.schemas.scan import ScanResultResponse, StockIndicatorDataResponse
        
        # 测试序列化包含end_date字段
        indicator_data = StockIndicatorDataResponse(
            kdj_k=50.0,
            volume_pressure=1.2,
            bollinger_upper=12.0,
            bollinger_middle=11.0,
            bollinger_lower=10.0,
            dif=0.1,
            dea=0.05,
            macd=0.05,
            rsi=55.0,
            arbr_ar=110.0,
            arbr_br=95.0,
            close_price=11.0,
            volume_pressure_avg=1.1
        )
        
        result = ScanResultResponse(
            stock_code="000001",
            stock_name="测试股票",
            signals=[],
            indicator_data=indicator_data,
            price=11.0,
            change_percent=1.5,
            scan_time=datetime.now(),
            end_date="2024-01-15"
        )
        
        # 验证序列化
        result_dict = result.model_dump()
        assert "end_date" in result_dict
        assert result_dict["end_date"] == "2024-01-15"