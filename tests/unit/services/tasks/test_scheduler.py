"""定时任务调度器单元测试"""

import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from sqlalchemy.orm import Session

from app.services.tasks.scheduler import TaskScheduler, get_task_scheduler
from app.models.task import UserScheduledTask, TaskExecution
from app.schemas.scheduled_task import TaskStatus, TaskType
from app.models.user import User


class TestTaskScheduler:
    """定时任务调度器测试类"""

    @pytest.fixture
    def scheduler(self):
        """创建调度器实例"""
        return TaskScheduler()

    @pytest.mark.asyncio
    async def test_scheduler_initialization(self, scheduler):
        """测试调度器初始化"""
        assert scheduler.running is False
        assert scheduler._scheduler_task is None
        assert scheduler._max_concurrent_tasks == 10
        assert scheduler._retry_attempts == 3
        assert scheduler._scheduler_interval == 60

    @pytest.mark.asyncio
    async def test_scheduler_start_stop(self, scheduler):
        """测试调度器启动和停止"""
        # 测试启动
        await scheduler.start()
        assert scheduler.running is True
        assert scheduler._scheduler_task is not None
        
        # 测试重复启动不会创建新任务
        old_task = scheduler._scheduler_task
        await scheduler.start()
        assert scheduler._scheduler_task is old_task
        
        # 测试停止
        await scheduler.stop()
        assert scheduler.running is False

    @pytest.mark.asyncio
    async def test_cron_expression_parsing(self, scheduler, valid_cron_expressions):
        """测试Cron表达式解析"""
        from croniter import croniter
        
        for expression in valid_cron_expressions:
            try:
                cron = croniter(expression)
                next_time = cron.get_next(datetime)
                assert isinstance(next_time, datetime)
                assert next_time > datetime.now()
            except Exception as e:
                pytest.fail(f"有效的Cron表达式解析失败: {expression}, 错误: {e}")

    @pytest.mark.asyncio
    async def test_invalid_cron_expressions(self, scheduler, invalid_cron_expressions):
        """测试无效Cron表达式处理"""
        from croniter import croniter
        
        for expression in invalid_cron_expressions:
            with pytest.raises(Exception):
                cron = croniter(expression)
                cron.get_next(datetime)

    @pytest.mark.asyncio
    async def test_task_execution_scheduling(self, scheduler, mock_db_session, mock_scheduled_task, mock_croniter):
        """测试任务执行调度"""
        # 模拟数据库查询
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [mock_scheduled_task]
        mock_db_session.query.return_value = mock_query
        
        # 模拟数据库会话上下文管理器
        with patch('app.services.tasks.scheduler.db_session') as mock_db_context:
            mock_db_context.return_value.__aenter__ = AsyncMock(return_value=mock_db_session)
            mock_db_context.return_value.__aexit__ = AsyncMock(return_value=None)
            
            # 模拟任务执行
            with patch.object(scheduler, '_execute_task_with_retry', new_callable=AsyncMock) as mock_execute:
                await scheduler._check_and_execute_tasks()
                
                # 验证调用了任务执行
                mock_execute.assert_called()

    @pytest.mark.asyncio
    async def test_max_executions_limit(self, scheduler, mock_db_session):
        """测试最大执行次数限制"""
        # 创建已达执行次数限制的任务
        completed_task = Mock()
        completed_task.id = 1
        completed_task.max_executions = 5
        completed_task.current_executions = 5
        completed_task.is_active = True
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [completed_task]
        mock_db_session.query.return_value = mock_query
        
        with patch('app.services.tasks.scheduler.db_session') as mock_db_context:
            mock_db_context.return_value.__aenter__ = AsyncMock(return_value=mock_db_session)
            mock_db_context.return_value.__aexit__ = AsyncMock(return_value=None)
            
            await scheduler._check_and_execute_tasks()
            
            # 验证任务被停用
            assert completed_task.is_active is False
            mock_db_session.commit.assert_called()

    @pytest.mark.asyncio
    async def test_concurrent_task_limit(self, scheduler):
        """测试并发任务数量限制"""
        # 设置较低的并发限制用于测试
        scheduler._max_concurrent_tasks = 2
        
        # 模拟已有活跃任务
        scheduler._active_executions = {1: Mock(), 2: Mock()}
        
        with patch('app.services.tasks.scheduler.db_session') as mock_db_context:
            mock_db_context.return_value.__aenter__ = AsyncMock(return_value=Mock())
            mock_db_context.return_value.__aexit__ = AsyncMock(return_value=None)
            
            await scheduler._check_and_execute_tasks()
            
            # 当并发任务达到上限时，不应该执行新任务
            # 这里主要验证不会崩溃，具体逻辑需要更复杂的mock

    @pytest.mark.asyncio
    async def test_task_execution_retry_mechanism(self, scheduler):
        """测试任务执行重试机制"""
        execution_id = 123
        task_id = 456
        
        # 模拟前两次执行失败，第三次成功
        call_count = 0
        async def mock_execute_side_effect(exec_id):
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise Exception(f"执行失败第{call_count}次")
            return "success"
        
        with patch.object(scheduler, '_execute_task', side_effect=mock_execute_side_effect) as mock_execute:
            await scheduler._execute_task_with_retry(execution_id, task_id)
            
            # 验证重试了3次
            assert mock_execute.call_count == 3

    @pytest.mark.asyncio
    async def test_task_execution_max_retries_exceeded(self, scheduler):
        """测试超过最大重试次数"""
        execution_id = 123
        task_id = 456
        scheduler._retry_attempts = 2
        
        # 模拟总是失败
        with patch.object(scheduler, '_execute_task', side_effect=Exception("总是失败")) as mock_execute:
            with patch('app.services.tasks.scheduler.db_session') as mock_db_context:
                mock_db_session = Mock()
                mock_execution = Mock()
                mock_execution.start_time = datetime.now()
                mock_db_session.query.return_value.get.return_value = mock_execution
                
                mock_db_context.return_value.__aenter__ = AsyncMock(return_value=mock_db_session)
                mock_db_context.return_value.__aexit__ = AsyncMock(return_value=None)
                
                await scheduler._execute_task_with_retry(execution_id, task_id)
                
                # 验证尝试了指定次数
                assert mock_execute.call_count == scheduler._retry_attempts
                
                # 验证最终标记为失败
                assert mock_execution.status == TaskStatus.FAILED
                assert "重试" in mock_execution.error_message

    @pytest.mark.asyncio
    async def test_scheduler_loop_error_handling(self, scheduler):
        """测试调度器主循环错误处理"""
        scheduler.running = True
        
        # 模拟检查任务时发生错误
        with patch.object(scheduler, '_check_and_execute_tasks', side_effect=Exception("调度错误")) as mock_check:
            with patch('asyncio.sleep', new_callable=AsyncMock) as mock_sleep:
                # 运行一次循环后停止
                async def stop_after_one():
                    await asyncio.sleep(0.1)
                    scheduler.running = False
                
                asyncio.create_task(stop_after_one())
                await scheduler._scheduler_loop()
                
                # 验证错误被处理且会重试
                mock_check.assert_called()
                mock_sleep.assert_called()

    @pytest.mark.asyncio
    async def test_intelligent_scheduler_interval(self, scheduler):
        """测试智能调度间隔计算"""
        # 无活跃任务
        scheduler._active_executions = {}
        assert scheduler._calculate_scheduler_interval() == 60
        
        # 少量活跃任务
        scheduler._active_executions = {1: Mock(), 2: Mock()}
        assert scheduler._calculate_scheduler_interval() == 30
        
        # 高负载
        scheduler._active_executions = {i: Mock() for i in range(5)}
        assert scheduler._calculate_scheduler_interval() == 15

    @pytest.mark.asyncio
    async def test_task_cleanup(self, scheduler):
        """测试任务清理"""
        execution_id = 123
        task_id = 456
        
        # 添加活跃任务
        scheduler._active_executions[execution_id] = Mock()
        scheduler._running_task_ids.add(task_id)
        
        # 执行清理
        scheduler._cleanup_task(execution_id, task_id)
        
        # 验证清理结果
        assert execution_id not in scheduler._active_executions
        assert task_id not in scheduler._running_task_ids

    @pytest.mark.asyncio
    async def test_user_task_limit_enforcement(self, scheduler, mock_db_session):
        """测试用户任务数量限制"""
        mock_task = Mock()
        mock_task.id = 1
        mock_task.user_id = 123
        mock_task.max_executions = None
        mock_task.current_executions = 0
        
        # 模拟用户已有5个活跃任务
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.count.return_value = 5  # 达到用户限制
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [mock_task]
        
        mock_db_session.query.return_value = mock_query
        
        with patch('app.services.tasks.scheduler.db_session') as mock_db_context:
            mock_db_context.return_value.__aenter__ = AsyncMock(return_value=mock_db_session)
            mock_db_context.return_value.__aexit__ = AsyncMock(return_value=None)
            
            await scheduler._check_and_execute_tasks()
            
            # 验证任务不会被执行（被限制）
            assert mock_task.id not in scheduler._running_task_ids


class TestSchedulerIntegration:
    """调度器集成测试"""

    @pytest.mark.asyncio
    async def test_full_scheduling_cycle(self, scheduler, mock_memory_scanner, mock_session_manager):
        """测试完整的调度周期"""
        with patch('app.services.tasks.scheduler.get_memory_scanner', return_value=mock_memory_scanner):
            with patch('app.services.tasks.scheduler.get_session_manager', return_value=mock_session_manager):
                with patch('app.services.tasks.scheduler.db_session') as mock_db_context:
                    # 模拟数据库操作
                    mock_db_session = Mock()
                    mock_execution = Mock()
                    mock_execution.id = 1
                    mock_execution.task_type = "indicator_scan"
                    mock_execution.task_config = '{"indicators": ["MACD"], "stock_codes": ["000001.SZ"]}'
                    
                    mock_db_session.query.return_value.get.return_value = mock_execution
                    mock_db_context.return_value.__aenter__ = AsyncMock(return_value=mock_db_session)
                    mock_db_context.return_value.__aexit__ = AsyncMock(return_value=None)
                    
                    # 执行任务
                    await scheduler._execute_task(mock_execution.id)
                    
                    # 验证扫描器被调用
                    mock_memory_scanner.start_scan.assert_called_once()
                    
                    # 验证会话管理
                    mock_session_manager.get_scan_task.assert_called()
                    mock_session_manager.remove_session.assert_called()
                    
                    # 验证执行状态更新
                    assert mock_execution.status == TaskStatus.COMPLETED

    @pytest.mark.asyncio
    async def test_scheduler_global_instance(self):
        """测试全局调度器实例"""
        scheduler1 = get_task_scheduler()
        scheduler2 = get_task_scheduler()
        
        # 验证单例模式
        assert scheduler1 is scheduler2
        assert isinstance(scheduler1, TaskScheduler)


class TestCronExpressionParsing:
    """Cron表达式解析测试"""

    def test_valid_cron_expressions_parsing(self, valid_cron_expressions):
        """测试有效Cron表达式解析"""
        from croniter import croniter
        
        for expression in valid_cron_expressions:
            cron = croniter(expression)
            next_time = cron.get_next(datetime)
            
            # 验证下次执行时间在未来
            assert next_time > datetime.now()
            
            # 验证可以获取多个下次执行时间
            next_times = [cron.get_next(datetime) for _ in range(3)]
            assert len(next_times) == 3
            assert all(t1 < t2 for t1, t2 in zip(next_times[:-1], next_times[1:]))

    def test_cron_edge_cases(self):
        """测试Cron表达式边界情况"""
        from croniter import croniter
        
        test_cases = [
            ("0 0 29 2 *", "闰年2月29日"),  # 闰年边界
            ("0 0 31 4 *", "4月31日不存在"),  # 不存在的日期
            ("59 23 * * *", "一天的最后时刻"),  # 边界时间
        ]
        
        for expression, description in test_cases:
            cron = croniter(expression)
            try:
                # 应该能生成时间，即使某些年份不存在
                next_time = cron.get_next(datetime)
                assert isinstance(next_time, datetime)
            except Exception as e:
                # 某些边界情况可能会抛出异常，这是正常的
                pass

    def test_cron_frequency_calculation(self):
        """测试Cron表达式频率计算"""
        from croniter import croniter
        
        test_cases = [
            ("* * * * *", 60),      # 每分钟 (实际测试中会很快)
            ("0 * * * *", 3600),    # 每小时  
            ("0 0 * * *", 86400),   # 每天
        ]
        
        for expression, expected_seconds in test_cases:
            cron = croniter(expression)
            start_time = datetime.now()
            first_next = cron.get_next(datetime)
            second_next = cron.get_next(datetime)
            
            # 计算间隔
            interval_seconds = (second_next - first_next).total_seconds()
            assert abs(interval_seconds - expected_seconds) < 60  # 允许1分钟误差


class TestPerformanceAndScalability:
    """性能和可扩展性测试"""

    @pytest.mark.asyncio
    async def test_concurrent_task_scheduling(self, scheduler, performance_test_tasks):
        """测试并发任务调度性能"""
        # 创建多个模拟任务
        mock_tasks = []
        for i, task_data in enumerate(performance_test_tasks[:10]):  # 测试10个任务
            mock_task = Mock()
            mock_task.id = i
            mock_task.user_id = 1
            mock_task.max_executions = None
            mock_task.current_executions = 0
            mock_task.task_type = "indicator_scan"
            mock_task.task_config = task_data["task_config"].json()
            mock_task.cron_expression = task_data["cron_expression"]
            mock_tasks.append(mock_task)
        
        # 模拟数据库查询返回所有任务
        with patch('app.services.tasks.scheduler.db_session') as mock_db_context:
            mock_db_session = Mock()
            mock_query = Mock()
            mock_query.filter.return_value = mock_query
            mock_query.limit.return_value = mock_query
            mock_query.all.return_value = mock_tasks
            mock_query.count.return_value = 0  # 用户无活跃任务
            mock_db_session.query.return_value = mock_query
            
            mock_db_context.return_value.__aenter__ = AsyncMock(return_value=mock_db_session)
            mock_db_context.return_value.__aexit__ = AsyncMock(return_value=None)
            
            # 模拟任务执行
            with patch.object(scheduler, '_execute_task_with_retry', new_callable=AsyncMock) as mock_execute:
                start_time = datetime.now()
                await scheduler._check_and_execute_tasks()
                end_time = datetime.now()
                
                # 验证执行时间在合理范围内 (应该很快，因为是并发的)
                execution_time = (end_time - start_time).total_seconds()
                assert execution_time < 5.0  # 应该在5秒内完成
                
                # 验证所有任务都被调度 (受并发限制)
                expected_calls = min(len(mock_tasks), scheduler._max_concurrent_tasks)
                assert mock_execute.call_count <= expected_calls

    @pytest.mark.asyncio
    async def test_memory_usage_with_many_tasks(self, scheduler):
        """测试大量任务时的内存使用"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # 模拟大量任务执行
        large_task_count = 100
        for i in range(large_task_count):
            execution_id = i
            task_id = i
            scheduler._active_executions[execution_id] = Mock()
            scheduler._running_task_ids.add(task_id)
        
        current_memory = process.memory_info().rss
        memory_increase = current_memory - initial_memory
        
        # 验证内存增长在合理范围内 (小于50MB)
        assert memory_increase < 50 * 1024 * 1024  # 50MB
        
        # 清理
        scheduler._active_executions.clear()
        scheduler._running_task_ids.clear()

    @pytest.mark.asyncio 
    async def test_scheduler_stability_under_load(self, scheduler):
        """测试调度器在高负载下的稳定性"""
        # 模拟连续调度多次
        with patch.object(scheduler, '_check_and_execute_tasks', new_callable=AsyncMock) as mock_check:
            # 模拟快速连续调用
            tasks = [scheduler._check_and_execute_tasks() for _ in range(20)]
            
            # 等待所有任务完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 验证没有异常
            exceptions = [r for r in results if isinstance(r, Exception)]
            assert len(exceptions) == 0
            
            # 验证所有调用都成功完成
            assert mock_check.call_count == 20


@pytest.mark.asyncio
async def test_scheduler_resource_cleanup():
    """测试调度器资源清理"""
    scheduler = TaskScheduler()
    
    # 启动调度器
    await scheduler.start()
    
    # 添加一些模拟任务
    scheduler._active_executions = {1: Mock(), 2: Mock(), 3: Mock()}
    scheduler._running_task_ids = {1, 2, 3}
    
    # 停止调度器
    await scheduler.stop()
    
    # 验证资源被清理
    assert scheduler.running is False
    assert len(scheduler._active_executions) == 0  # 应该被清理


@pytest.mark.asyncio
async def test_scheduler_exception_isolation():
    """测试调度器异常隔离"""
    scheduler = TaskScheduler()
    
    # 模拟一个任务执行时抛出异常
    def failing_task():
        raise Exception("任务执行失败")
    
    with patch.object(scheduler, '_execute_task_with_retry', side_effect=failing_task):
        # 异常不应该影响调度器的其他功能
        try:
            await scheduler._check_and_execute_tasks()
        except Exception:
            pytest.fail("调度器异常未被正确隔离")