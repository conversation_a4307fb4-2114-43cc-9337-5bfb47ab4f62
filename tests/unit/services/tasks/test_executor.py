"""任务执行器单元测试"""

import pytest
import asyncio
import json
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch, MagicMock

from app.services.tasks.executor import TaskExecutor, get_task_executor
from app.models.task import TaskExecution
from app.schemas.scheduled_task import TaskStatus, TaskType, IndicatorScanConfig


class TestTaskExecutor:
    """任务执行器测试类"""

    @pytest.fixture
    def executor(self):
        """创建执行器实例"""
        return TaskExecutor()

    @pytest.mark.asyncio
    async def test_executor_initialization(self, executor):
        """测试执行器初始化"""
        assert isinstance(executor._active_executions, dict)
        assert len(executor._active_executions) == 0

    @pytest.mark.asyncio
    async def test_simple_task_execution(self, executor, mock_task_execution, mock_memory_scanner, mock_session_manager):
        """测试简单任务执行"""
        execution_id = mock_task_execution.id
        
        with patch('app.services.tasks.executor.get_memory_scanner', return_value=mock_memory_scanner):
            with patch('app.services.tasks.executor.get_session_manager', return_value=mock_session_manager):
                with patch('app.services.tasks.executor.db_session') as mock_db_context:
                    mock_db_session = Mock()
                    mock_db_session.query.return_value.get.return_value = mock_task_execution
                    mock_db_context.return_value.__aenter__ = AsyncMock(return_value=mock_db_session)
                    mock_db_context.return_value.__aexit__ = AsyncMock(return_value=None)
                    
                    # 执行任务
                    task = await executor.execute_task(execution_id)
                    
                    # 验证任务被添加到活跃执行列表
                    assert execution_id in executor._active_executions
                    
                    # 等待任务完成
                    await task
                    
                    # 验证任务被清理
                    assert execution_id not in executor._active_executions

    @pytest.mark.asyncio
    async def test_duplicate_task_execution_prevention(self, executor):
        """测试防止重复执行同一任务"""
        execution_id = 123
        
        # 模拟第一个任务正在执行
        mock_task = AsyncMock()
        executor._active_executions[execution_id] = mock_task
        
        # 尝试执行重复任务
        result = await executor.execute_task(execution_id)
        
        # 验证返回None且没有创建新任务
        assert result is None
        assert len(executor._active_executions) == 1

    @pytest.mark.asyncio
    async def test_indicator_scan_execution(self, executor, mock_indicator_config, mock_memory_scanner, mock_session_manager):
        """测试指标扫描任务执行"""
        # 创建模拟执行记录
        mock_execution = Mock()
        mock_execution.id = 1
        mock_execution.task_type = TaskType.INDICATOR_SCAN
        mock_execution.task_config = mock_indicator_config.json()
        mock_execution.user_id = 123
        
        with patch('app.services.tasks.executor.db_session') as mock_db_context:
            mock_db_session = Mock()
            mock_db_context.return_value.__aenter__ = AsyncMock(return_value=mock_db_session)
            mock_db_context.return_value.__aexit__ = AsyncMock(return_value=None)
            
            await executor._execute_indicator_scan(mock_execution, mock_db_session)
            
            # 验证扫描器调用
            mock_memory_scanner.start_scan.assert_called_once()
            call_args = mock_memory_scanner.start_scan.call_args
            
            # 验证传递的参数
            assert call_args.kwargs['indicators'] == mock_indicator_config.indicators
            assert call_args.kwargs['stock_codes'] == mock_indicator_config.stock_codes
            assert call_args.kwargs['scan_mode'] == mock_indicator_config.scan_mode

    @pytest.mark.asyncio
    async def test_scan_results_processing(self, executor, scan_result_samples, mock_memory_scanner, mock_session_manager):
        """测试扫描结果处理"""
        mock_execution = Mock()
        mock_execution.id = 1
        mock_execution.user_id = 123
        mock_execution.task_config = json.dumps({
            "indicators": ["MACD", "RSI"],
            "stock_codes": ["000001.SZ"],
            "parameters": {},
            "scan_mode": "traditional",
            "periods": ["d"],
            "adjust": "n"
        })
        
        # 模拟扫描任务完成并返回结果
        mock_scan_task = Mock()
        mock_scan_task.status.value = "completed"
        mock_scan_task.results = [
            Mock(dict=Mock(return_value=result)) for result in scan_result_samples
        ]
        mock_session_manager.get_scan_task.return_value = mock_scan_task
        
        with patch('app.services.tasks.executor.db_session') as mock_db_context:
            mock_db_session = Mock()
            mock_db_context.return_value.__aenter__ = AsyncMock(return_value=mock_db_session)
            mock_db_context.return_value.__aexit__ = AsyncMock(return_value=None)
            
            await executor._execute_indicator_scan(mock_execution, mock_db_session)
            
            # 验证结果数据被正确保存
            assert mock_execution.results_count == len(scan_result_samples)
            
            # 验证结果数据格式
            saved_results = json.loads(mock_execution.results_data)
            assert len(saved_results) == len(scan_result_samples)
            assert saved_results[0]['symbol'] == scan_result_samples[0]['symbol']

    @pytest.mark.asyncio
    async def test_scan_failure_handling(self, executor, mock_memory_scanner, mock_session_manager):
        """测试扫描失败处理"""
        mock_execution = Mock()
        mock_execution.id = 1
        mock_execution.user_id = 123
        mock_execution.task_config = json.dumps({
            "indicators": ["MACD"],
            "stock_codes": ["000001.SZ"],
            "parameters": {},
            "scan_mode": "traditional", 
            "periods": ["d"],
            "adjust": "n"
        })
        
        # 模拟扫描失败
        mock_scan_task = Mock()
        mock_scan_task.status.value = "failed"
        mock_scan_task.error_message = "扫描器内部错误"
        mock_session_manager.get_scan_task.return_value = mock_scan_task
        
        with patch('app.services.tasks.executor.db_session') as mock_db_context:
            mock_db_session = Mock()
            mock_db_context.return_value.__aenter__ = AsyncMock(return_value=mock_db_session)
            mock_db_context.return_value.__aexit__ = AsyncMock(return_value=None)
            
            # 扫描失败应该抛出异常
            with pytest.raises(Exception, match="扫描失败"):
                await executor._execute_indicator_scan(mock_execution, mock_db_session)

    @pytest.mark.asyncio
    async def test_scan_timeout_handling(self, executor, mock_memory_scanner, mock_session_manager):
        """测试扫描超时处理"""
        mock_execution = Mock()
        mock_execution.id = 1
        mock_execution.user_id = 123
        mock_execution.task_config = json.dumps({
            "indicators": ["MACD"],
            "stock_codes": ["000001.SZ"],
            "parameters": {},
            "scan_mode": "traditional",
            "periods": ["d"], 
            "adjust": "n"
        })
        
        # 模拟扫描一直运行不完成
        mock_scan_task = Mock()
        mock_scan_task.status.value = "running"  # 一直运行
        mock_session_manager.get_scan_task.return_value = mock_scan_task
        
        # 缩短超时时间用于测试
        original_timeout = 3600
        with patch.object(executor, '_execute_indicator_scan') as mock_method:
            # 模拟超时逻辑
            async def mock_scan_with_timeout(execution, db):
                start_time = datetime.now()
                timeout = 2  # 2秒超时用于测试
                
                while True:
                    if (datetime.now() - start_time).total_seconds() > timeout:
                        await mock_memory_scanner.stop_scan("session_id", "task_id")
                        raise Exception("扫描任务超时")
                    await asyncio.sleep(0.1)
            
            mock_method.side_effect = mock_scan_with_timeout
            
            with pytest.raises(Exception, match="扫描任务超时"):
                await executor._execute_indicator_scan(mock_execution, Mock())
                
        # 验证停止扫描被调用
        mock_memory_scanner.stop_scan.assert_called()

    @pytest.mark.asyncio
    async def test_unsupported_task_type(self, executor):
        """测试不支持的任务类型"""
        mock_execution = Mock()
        mock_execution.id = 1
        mock_execution.task_type = "unsupported_type"
        
        with patch('app.services.tasks.executor.db_session') as mock_db_context:
            mock_db_session = Mock()
            mock_db_session.query.return_value.get.return_value = mock_execution
            mock_db_context.return_value.__aenter__ = AsyncMock(return_value=mock_db_session)
            mock_db_context.return_value.__aexit__ = AsyncMock(return_value=None)
            
            await executor._run_task_execution(mock_execution.id)
            
            # 验证任务状态被设置为失败
            assert mock_execution.status == TaskStatus.FAILED
            assert "不支持的任务类型" in mock_execution.error_message

    @pytest.mark.asyncio
    async def test_execution_status_updates(self, executor, mock_task_execution):
        """测试执行状态更新"""
        execution_id = mock_task_execution.id
        
        with patch('app.services.tasks.executor.db_session') as mock_db_context:
            mock_db_session = Mock()
            mock_db_session.query.return_value.get.return_value = mock_task_execution
            mock_db_context.return_value.__aenter__ = AsyncMock(return_value=mock_db_session)
            mock_db_context.return_value.__aexit__ = AsyncMock(return_value=None)
            
            with patch.object(executor, '_execute_indicator_scan', new_callable=AsyncMock) as mock_scan:
                await executor._run_task_execution(execution_id)
                
                # 验证状态变更序列
                # 1. 开始时设置为RUNNING
                # 2. 完成时设置为COMPLETED
                assert mock_task_execution.status == TaskStatus.COMPLETED
                assert mock_task_execution.start_time is not None
                assert mock_task_execution.end_time is not None
                assert mock_task_execution.duration_seconds is not None

    @pytest.mark.asyncio
    async def test_execution_error_handling(self, executor, mock_task_execution):
        """测试执行错误处理"""
        execution_id = mock_task_execution.id
        error_message = "模拟执行错误"
        
        with patch('app.services.tasks.executor.db_session') as mock_db_context:
            mock_db_session = Mock()
            mock_db_session.query.return_value.get.return_value = mock_task_execution
            mock_db_context.return_value.__aenter__ = AsyncMock(return_value=mock_db_session)
            mock_db_context.return_value.__aexit__ = AsyncMock(return_value=None)
            
            with patch.object(executor, '_execute_indicator_scan', side_effect=Exception(error_message)):
                await executor._run_task_execution(execution_id)
                
                # 验证错误状态
                assert mock_task_execution.status == TaskStatus.FAILED
                assert mock_task_execution.error_message == error_message
                assert mock_task_execution.end_time is not None

    @pytest.mark.asyncio
    async def test_session_cleanup(self, executor, mock_session_manager):
        """测试会话清理"""
        mock_execution = Mock()
        mock_execution.id = 1
        mock_execution.user_id = 123
        mock_execution.task_config = json.dumps({
            "indicators": ["MACD"],
            "stock_codes": ["000001.SZ"],
            "parameters": {},
            "scan_mode": "traditional",
            "periods": ["d"],
            "adjust": "n"
        })
        
        # 模拟扫描过程中发生异常
        mock_scan_task = Mock()
        mock_scan_task.status.value = "completed"
        mock_scan_task.results = []
        mock_session_manager.get_scan_task.return_value = mock_scan_task
        mock_session_manager.remove_session.side_effect = Exception("清理失败")
        
        with patch('app.services.tasks.executor.get_memory_scanner') as mock_get_scanner:
            mock_memory_scanner = Mock()
            mock_memory_scanner.start_scan = AsyncMock(return_value="task_123")
            mock_get_scanner.return_value = mock_memory_scanner
            
            with patch('app.services.tasks.executor.db_session') as mock_db_context:
                mock_db_session = Mock()
                mock_db_context.return_value.__aenter__ = AsyncMock(return_value=mock_db_session)
                mock_db_context.return_value.__aexit__ = AsyncMock(return_value=None)
                
                # 即使会话清理失败，也不应该影响主要执行流程
                await executor._execute_indicator_scan(mock_execution, mock_db_session)
                
                # 验证清理被尝试
                mock_session_manager.remove_session.assert_called()

    def test_cancel_execution(self, executor):
        """测试取消任务执行"""
        execution_id = 123
        
        # 添加一个模拟的活跃任务
        mock_task = Mock()
        executor._active_executions[execution_id] = mock_task
        
        # 取消任务
        result = executor.cancel_execution(execution_id)
        
        # 验证取消成功
        assert result is True
        mock_task.cancel.assert_called_once()
        
        # 测试取消不存在的任务
        result = executor.cancel_execution(999)
        assert result is False

    def test_get_active_executions(self, executor):
        """测试获取活跃执行任务列表"""
        # 添加一些模拟任务
        execution_ids = [1, 2, 3]
        for exec_id in execution_ids:
            executor._active_executions[exec_id] = Mock()
        
        active_list = executor.get_active_executions()
        
        # 验证返回正确的执行ID列表
        assert sorted(active_list) == sorted(execution_ids)


class TestTaskExecutorIntegration:
    """任务执行器集成测试"""

    @pytest.mark.asyncio
    async def test_full_execution_workflow(self, executor, mock_indicator_config, mock_memory_scanner, mock_session_manager, scan_result_samples):
        """测试完整的执行工作流"""
        # 创建完整的执行记录
        mock_execution = Mock()
        mock_execution.id = 1
        mock_execution.user_id = 123
        mock_execution.task_type = TaskType.INDICATOR_SCAN
        mock_execution.task_config = mock_indicator_config.json()
        mock_execution.status = TaskStatus.PENDING
        mock_execution.start_time = None
        mock_execution.end_time = None
        
        # 模拟扫描成功完成
        mock_scan_task = Mock()
        mock_scan_task.status.value = "completed"
        mock_scan_task.results = [Mock(dict=Mock(return_value=result)) for result in scan_result_samples]
        mock_session_manager.get_scan_task.return_value = mock_scan_task
        
        with patch('app.services.tasks.executor.get_memory_scanner', return_value=mock_memory_scanner):
            with patch('app.services.tasks.executor.get_session_manager', return_value=mock_session_manager):
                with patch('app.services.tasks.executor.db_session') as mock_db_context:
                    mock_db_session = Mock()
                    mock_db_session.query.return_value.get.return_value = mock_execution
                    mock_db_context.return_value.__aenter__ = AsyncMock(return_value=mock_db_session)
                    mock_db_context.return_value.__aexit__ = AsyncMock(return_value=None)
                    
                    # 执行完整流程
                    task = await executor.execute_task(mock_execution.id)
                    await task
                    
                    # 验证完整流程
                    assert mock_execution.status == TaskStatus.COMPLETED
                    assert mock_execution.start_time is not None
                    assert mock_execution.end_time is not None
                    assert mock_execution.duration_seconds is not None
                    assert mock_execution.results_count == len(scan_result_samples)
                    
                    # 验证扫描器调用
                    mock_memory_scanner.start_scan.assert_called_once()
                    mock_session_manager.create_session.assert_called()
                    mock_session_manager.remove_session.assert_called()

    @pytest.mark.asyncio
    async def test_concurrent_executions(self, executor):
        """测试并发执行"""
        execution_count = 5
        mock_executions = []
        
        for i in range(execution_count):
            mock_execution = Mock()
            mock_execution.id = i + 1
            mock_execution.user_id = 123
            mock_execution.task_type = TaskType.INDICATOR_SCAN
            mock_execution.task_config = json.dumps({
                "indicators": ["MACD"],
                "stock_codes": [f"00000{i+1}.SZ"],
                "parameters": {},
                "scan_mode": "traditional",
                "periods": ["d"],
                "adjust": "n"
            })
            mock_executions.append(mock_execution)
        
        with patch('app.services.tasks.executor.get_memory_scanner') as mock_get_scanner:
            with patch('app.services.tasks.executor.get_session_manager') as mock_get_manager:
                with patch('app.services.tasks.executor.db_session') as mock_db_context:
                    # 设置模拟
                    mock_memory_scanner = Mock()
                    mock_memory_scanner.start_scan = AsyncMock(return_value="task_123")
                    mock_get_scanner.return_value = mock_memory_scanner
                    
                    mock_session_manager = Mock()
                    mock_session_manager.create_session = Mock()
                    mock_session_manager.remove_session = Mock()
                    
                    mock_scan_task = Mock()
                    mock_scan_task.status.value = "completed"
                    mock_scan_task.results = []
                    mock_session_manager.get_scan_task.return_value = mock_scan_task
                    mock_get_manager.return_value = mock_session_manager
                    
                    mock_db_session = Mock()
                    mock_db_session.query.return_value.get.side_effect = lambda exec_id: mock_executions[exec_id - 1]
                    mock_db_context.return_value.__aenter__ = AsyncMock(return_value=mock_db_session)
                    mock_db_context.return_value.__aexit__ = AsyncMock(return_value=None)
                    
                    # 并发启动多个任务
                    tasks = []
                    for execution in mock_executions:
                        task = await executor.execute_task(execution.id)
                        tasks.append(task)
                    
                    # 等待所有任务完成
                    await asyncio.gather(*tasks)
                    
                    # 验证所有任务都完成
                    for execution in mock_executions:
                        assert execution.status == TaskStatus.COMPLETED

    def test_executor_global_instance(self):
        """测试全局执行器实例"""
        executor1 = get_task_executor()
        executor2 = get_task_executor()
        
        # 验证单例模式
        assert executor1 is executor2
        assert isinstance(executor1, TaskExecutor)


class TestTaskExecutorErrorScenarios:
    """任务执行器错误场景测试"""

    @pytest.mark.asyncio
    async def test_database_connection_failure(self, executor):
        """测试数据库连接失败"""
        execution_id = 123
        
        with patch('app.services.tasks.executor.db_session') as mock_db_context:
            # 模拟数据库连接失败
            mock_db_context.side_effect = Exception("数据库连接失败")
            
            # 执行任务不应该崩溃
            try:
                task = await executor.execute_task(execution_id)
                if task:
                    await task
            except Exception as e:
                # 验证异常被正确处理
                assert "数据库连接失败" in str(e)

    @pytest.mark.asyncio
    async def test_memory_scanner_unavailable(self, executor):
        """测试内存扫描器不可用"""
        mock_execution = Mock()
        mock_execution.id = 1
        mock_execution.task_type = TaskType.INDICATOR_SCAN
        mock_execution.task_config = json.dumps({
            "indicators": ["MACD"],
            "stock_codes": ["000001.SZ"],
            "parameters": {},
            "scan_mode": "traditional",
            "periods": ["d"],
            "adjust": "n"
        })
        
        # 模拟扫描器不可用
        with patch('app.services.tasks.executor.get_memory_scanner', side_effect=Exception("扫描器不可用")):
            with patch('app.services.tasks.executor.db_session') as mock_db_context:
                mock_db_session = Mock()
                mock_db_context.return_value.__aenter__ = AsyncMock(return_value=mock_db_session)
                mock_db_context.return_value.__aexit__ = AsyncMock(return_value=None)
                
                with pytest.raises(Exception, match="扫描器不可用"):
                    await executor._execute_indicator_scan(mock_execution, mock_db_session)

    @pytest.mark.asyncio
    async def test_invalid_task_configuration(self, executor):
        """测试无效的任务配置"""
        mock_execution = Mock()
        mock_execution.id = 1
        mock_execution.task_type = TaskType.INDICATOR_SCAN
        mock_execution.task_config = "invalid_json"  # 无效JSON
        
        with patch('app.services.tasks.executor.db_session') as mock_db_context:
            mock_db_session = Mock()
            mock_db_context.return_value.__aenter__ = AsyncMock(return_value=mock_db_session)
            mock_db_context.return_value.__aexit__ = AsyncMock(return_value=None)
            
            with pytest.raises(Exception):  # 应该抛出JSON解析异常
                await executor._execute_indicator_scan(mock_execution, mock_db_session)

    @pytest.mark.asyncio
    async def test_scan_task_missing(self, executor, mock_memory_scanner, mock_session_manager):
        """测试扫描任务丢失"""
        mock_execution = Mock()
        mock_execution.id = 1
        mock_execution.user_id = 123
        mock_execution.task_config = json.dumps({
            "indicators": ["MACD"],
            "stock_codes": ["000001.SZ"],
            "parameters": {},
            "scan_mode": "traditional",
            "periods": ["d"],
            "adjust": "n"
        })
        
        # 模拟扫描任务丢失
        mock_session_manager.get_scan_task.return_value = None
        
        with patch('app.services.tasks.executor.get_memory_scanner', return_value=mock_memory_scanner):
            with patch('app.services.tasks.executor.get_session_manager', return_value=mock_session_manager):
                with patch('app.services.tasks.executor.db_session') as mock_db_context:
                    mock_db_session = Mock()
                    mock_db_context.return_value.__aenter__ = AsyncMock(return_value=mock_db_session)
                    mock_db_context.return_value.__aexit__ = AsyncMock(return_value=None)
                    
                    with pytest.raises(Exception, match="扫描任务丢失"):
                        await executor._execute_indicator_scan(mock_execution, mock_db_session)


class TestPerformanceAndMemoryManagement:
    """性能和内存管理测试"""

    @pytest.mark.asyncio
    async def test_memory_cleanup_after_execution(self, executor):
        """测试执行后内存清理"""
        import gc
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # 创建并执行多个任务
        execution_count = 20
        tasks = []
        
        for i in range(execution_count):
            mock_execution = Mock()
            mock_execution.id = i + 1
            mock_execution.task_type = TaskType.INDICATOR_SCAN
            mock_execution.task_config = json.dumps({
                "indicators": ["MACD"],
                "stock_codes": ["000001.SZ"],
                "parameters": {},
                "scan_mode": "traditional",
                "periods": ["d"],
                "adjust": "n"
            })
            
            with patch('app.services.tasks.executor.db_session') as mock_db_context:
                with patch('app.services.tasks.executor.get_memory_scanner'):
                    with patch('app.services.tasks.executor.get_session_manager'):
                        mock_db_session = Mock()
                        mock_db_session.query.return_value.get.return_value = mock_execution
                        mock_db_context.return_value.__aenter__ = AsyncMock(return_value=mock_db_session)
                        mock_db_context.return_value.__aexit__ = AsyncMock(return_value=None)
                        
                        task = await executor.execute_task(mock_execution.id)
                        if task:
                            tasks.append(task)
        
        # 等待所有任务完成
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
        
        # 强制垃圾回收
        gc.collect()
        
        # 检查内存增长
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # 内存增长应该在合理范围内
        assert memory_increase < 100 * 1024 * 1024  # 100MB限制

    @pytest.mark.asyncio
    async def test_execution_performance(self, executor):
        """测试执行性能"""
        execution_count = 10
        
        with patch('app.services.tasks.executor.get_memory_scanner') as mock_get_scanner:
            with patch('app.services.tasks.executor.get_session_manager') as mock_get_manager:
                with patch('app.services.tasks.executor.db_session') as mock_db_context:
                    # 快速响应的模拟
                    mock_memory_scanner = Mock()
                    mock_memory_scanner.start_scan = AsyncMock(return_value="task_123")
                    mock_get_scanner.return_value = mock_memory_scanner
                    
                    mock_session_manager = Mock()
                    mock_session_manager.create_session = Mock()
                    mock_session_manager.remove_session = Mock()
                    
                    mock_scan_task = Mock()
                    mock_scan_task.status.value = "completed"
                    mock_scan_task.results = []
                    mock_session_manager.get_scan_task.return_value = mock_scan_task
                    mock_get_manager.return_value = mock_session_manager
                    
                    mock_db_session = Mock()
                    mock_db_context.return_value.__aenter__ = AsyncMock(return_value=mock_db_session)
                    mock_db_context.return_value.__aexit__ = AsyncMock(return_value=None)
                    
                    # 测量执行时间
                    start_time = datetime.now()
                    
                    tasks = []
                    for i in range(execution_count):
                        mock_execution = Mock()
                        mock_execution.id = i + 1
                        mock_execution.task_type = TaskType.INDICATOR_SCAN
                        mock_execution.task_config = json.dumps({
                            "indicators": ["MACD"],
                            "stock_codes": ["000001.SZ"],
                            "parameters": {},
                            "scan_mode": "traditional",
                            "periods": ["d"],
                            "adjust": "n"
                        })
                        
                        mock_db_session.query.return_value.get.return_value = mock_execution
                        
                        task = await executor.execute_task(mock_execution.id)
                        if task:
                            tasks.append(task)
                    
                    # 等待所有任务完成
                    await asyncio.gather(*tasks, return_exceptions=True)
                    
                    end_time = datetime.now()
                    total_time = (end_time - start_time).total_seconds()
                    
                    # 验证平均每个任务执行时间合理
                    avg_time_per_task = total_time / execution_count
                    assert avg_time_per_task < 1.0  # 每个任务应该在1秒内完成(由于使用了mock)