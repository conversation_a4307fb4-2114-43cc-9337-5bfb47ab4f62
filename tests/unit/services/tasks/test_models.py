"""数据模型验证测试"""

import pytest
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app.models.user import User
from app.models.task import UserScheduledTask, TaskExecution
from app.schemas.scheduled_task import (
    IndicatorScanConfig,
    UserScheduledTaskCreate,
    UserScheduledTaskUpdate,
    UserScheduledTaskResponse,
    TaskExecutionResponse,
    TaskType,
    TaskStatus,
    TriggerType
)


class TestTaskModels:
    """任务数据模型测试"""

    def test_user_scheduled_task_creation(self, db_session: Session, mock_user: User):
        """测试用户定时任务创建"""
        task = UserScheduledTask(
            user_id=mock_user.id,
            name="测试定时任务",
            task_type=TaskType.INDICATOR_SCAN,
            cron_expression="0 9 * * 1-5",
            task_config='{"indicators": ["MACD", "RSI"]}',
            description="测试任务描述",
            is_active=True,
            max_executions=10,
            next_execution=datetime.now() + timedelta(hours=1)
        )
        
        db_session.add(task)
        db_session.commit()
        db_session.refresh(task)
        
        # 验证任务创建成功
        assert task.id is not None
        assert task.name == "测试定时任务"
        assert task.task_type == TaskType.INDICATOR_SCAN
        assert task.cron_expression == "0 9 * * 1-5"
        assert task.is_active is True
        assert task.max_executions == 10
        assert task.current_executions == 0

    def test_user_scheduled_task_relationships(self, db_session: Session, mock_user: User):
        """测试用户定时任务关联关系"""
        task = UserScheduledTask(
            user_id=mock_user.id,
            name="关系测试任务",
            task_type=TaskType.INDICATOR_SCAN,
            cron_expression="0 * * * *",
            task_config='{"indicators": ["MACD"]}',
            is_active=True
        )
        
        db_session.add(task)
        db_session.commit()
        db_session.refresh(task)
        
        # 验证用户关联
        assert task.user == mock_user
        assert task in mock_user.scheduled_tasks

    def test_task_execution_creation(self, db_session: Session, mock_user: User, mock_scheduled_task: UserScheduledTask):
        """测试任务执行记录创建"""
        execution = TaskExecution(
            user_id=mock_user.id,
            scheduled_task_id=mock_scheduled_task.id,
            trigger_type=TriggerType.SCHEDULED,
            task_type=TaskType.INDICATOR_SCAN,
            task_config=mock_scheduled_task.task_config,
            status=TaskStatus.PENDING,
            start_time=datetime.now(),
            results_count=0
        )
        
        db_session.add(execution)
        db_session.commit()
        db_session.refresh(execution)
        
        # 验证执行记录创建成功
        assert execution.id is not None
        assert execution.user_id == mock_user.id
        assert execution.scheduled_task_id == mock_scheduled_task.id
        assert execution.trigger_type == TriggerType.SCHEDULED
        assert execution.status == TaskStatus.PENDING

    def test_task_execution_relationships(self, db_session: Session, mock_user: User, mock_scheduled_task: UserScheduledTask):
        """测试任务执行记录关联关系"""
        execution = TaskExecution(
            user_id=mock_user.id,
            scheduled_task_id=mock_scheduled_task.id,
            trigger_type=TriggerType.MANUAL,
            task_type=TaskType.INDICATOR_SCAN,
            task_config=mock_scheduled_task.task_config,
            status=TaskStatus.COMPLETED,
            results_count=5
        )
        
        db_session.add(execution)
        db_session.commit()
        db_session.refresh(execution)
        
        # 验证关联关系
        assert execution.user == mock_user
        assert execution.scheduled_task == mock_scheduled_task
        assert execution in mock_user.task_executions
        assert execution in mock_scheduled_task.executions

    def test_manual_execution_without_scheduled_task(self, db_session: Session, mock_user: User):
        """测试手动执行（无关联定时任务）"""
        execution = TaskExecution(
            user_id=mock_user.id,
            scheduled_task_id=None,  # 手动执行，无关联任务
            trigger_type=TriggerType.MANUAL,
            task_type=TaskType.INDICATOR_SCAN,
            task_config='{"indicators": ["RSI"]}',
            status=TaskStatus.COMPLETED,
            results_count=3
        )
        
        db_session.add(execution)
        db_session.commit()
        db_session.refresh(execution)
        
        # 验证手动执行记录
        assert execution.scheduled_task_id is None
        assert execution.scheduled_task is None
        assert execution.trigger_type == TriggerType.MANUAL

    def test_task_cascade_delete(self, db_session: Session, mock_user: User, mock_scheduled_task: UserScheduledTask):
        """测试级联删除"""
        # 创建执行记录
        execution1 = TaskExecution(
            user_id=mock_user.id,
            scheduled_task_id=mock_scheduled_task.id,
            trigger_type=TriggerType.SCHEDULED,
            task_type=TaskType.INDICATOR_SCAN,
            task_config=mock_scheduled_task.task_config,
            status=TaskStatus.COMPLETED
        )
        
        execution2 = TaskExecution(
            user_id=mock_user.id,
            scheduled_task_id=mock_scheduled_task.id,
            trigger_type=TriggerType.MANUAL,
            task_type=TaskType.INDICATOR_SCAN,
            task_config=mock_scheduled_task.task_config,
            status=TaskStatus.FAILED
        )
        
        db_session.add_all([execution1, execution2])
        db_session.commit()
        
        execution_ids = [execution1.id, execution2.id]
        
        # 删除定时任务
        db_session.delete(mock_scheduled_task)
        db_session.commit()
        
        # 验证执行记录被级联删除
        remaining_executions = db_session.query(TaskExecution).filter(
            TaskExecution.id.in_(execution_ids)
        ).all()
        
        assert len(remaining_executions) == 0

    def test_user_cascade_delete(self, db_session: Session, mock_user: User):
        """测试用户删除时的级联删除"""
        # 创建任务
        task = UserScheduledTask(
            user_id=mock_user.id,
            name="测试级联删除",
            task_type=TaskType.INDICATOR_SCAN,
            cron_expression="0 * * * *",
            task_config='{"indicators": ["MACD"]}',
            is_active=True
        )
        
        db_session.add(task)
        db_session.commit()
        db_session.refresh(task)
        
        # 创建执行记录
        execution = TaskExecution(
            user_id=mock_user.id,
            scheduled_task_id=task.id,
            trigger_type=TriggerType.SCHEDULED,
            task_type=TaskType.INDICATOR_SCAN,
            task_config=task.task_config,
            status=TaskStatus.COMPLETED
        )
        
        db_session.add(execution)
        db_session.commit()
        
        task_id = task.id
        execution_id = execution.id
        
        # 删除用户
        db_session.delete(mock_user)
        db_session.commit()
        
        # 验证相关记录被级联删除
        remaining_task = db_session.query(UserScheduledTask).filter(
            UserScheduledTask.id == task_id
        ).first()
        
        remaining_execution = db_session.query(TaskExecution).filter(
            TaskExecution.id == execution_id
        ).first()
        
        assert remaining_task is None
        assert remaining_execution is None

    def test_model_constraints(self, db_session: Session, mock_user: User):
        """测试模型约束"""
        # 测试必需字段约束
        with pytest.raises(IntegrityError):
            task = UserScheduledTask(
                # user_id=mock_user.id,  # 故意不设置必需字段
                name="测试约束",
                task_type=TaskType.INDICATOR_SCAN,
                cron_expression="0 * * * *",
                task_config='{"indicators": ["MACD"]}'
            )
            db_session.add(task)
            db_session.commit()

    def test_model_defaults(self, db_session: Session, mock_user: User):
        """测试模型默认值"""
        task = UserScheduledTask(
            user_id=mock_user.id,
            name="测试默认值",
            cron_expression="0 * * * *",
            task_config='{"indicators": ["MACD"]}'
        )
        
        db_session.add(task)
        db_session.commit()
        db_session.refresh(task)
        
        # 验证默认值
        assert task.task_type == TaskType.INDICATOR_SCAN
        assert task.is_active is True
        assert task.current_executions == 0
        assert task.max_executions is None


class TestTaskSchemas:
    """任务Schema验证测试"""

    def test_indicator_scan_config_validation(self):
        """测试指标扫描配置验证"""
        # 有效配置
        config = IndicatorScanConfig(
            indicators=["MACD", "RSI", "KDJ"],
            stock_codes=["000001.SZ", "000002.SZ"],
            parameters={"MACD": {"fast_period": 12}},
            scan_mode="traditional",
            periods=["d"],
            adjust="n"
        )
        
        assert config.indicators == ["MACD", "RSI", "KDJ"]
        assert config.stock_codes == ["000001.SZ", "000002.SZ"]
        assert config.scan_mode == "traditional"

    def test_indicator_scan_config_defaults(self):
        """测试指标扫描配置默认值"""
        config = IndicatorScanConfig(
            indicators=["MACD"]
        )
        
        # 验证默认值
        assert config.stock_codes is None
        assert config.parameters is None
        assert config.scan_mode == "traditional"
        assert config.periods == ["d"]
        assert config.adjust == "n"

    def test_user_scheduled_task_create_validation(self, mock_indicator_config):
        """测试创建定时任务请求验证"""
        # 有效请求
        request = UserScheduledTaskCreate(
            name="测试任务",
            task_type=TaskType.INDICATOR_SCAN,
            cron_expression="0 9 * * 1-5",
            task_config=mock_indicator_config,
            description="测试描述",
            max_executions=10
        )
        
        assert request.name == "测试任务"
        assert request.task_type == TaskType.INDICATOR_SCAN
        assert request.cron_expression == "0 9 * * 1-5"
        assert request.description == "测试描述"
        assert request.max_executions == 10

    def test_user_scheduled_task_create_validation_errors(self, mock_indicator_config):
        """测试创建定时任务请求验证错误"""
        # 名称过长
        with pytest.raises(ValueError):
            UserScheduledTaskCreate(
                name="x" * 101,  # 超过100字符限制
                task_type=TaskType.INDICATOR_SCAN,
                cron_expression="0 9 * * 1-5",
                task_config=mock_indicator_config
            )
        
        # 描述过长
        with pytest.raises(ValueError):
            UserScheduledTaskCreate(
                name="测试任务",
                task_type=TaskType.INDICATOR_SCAN,
                cron_expression="0 9 * * 1-5",
                task_config=mock_indicator_config,
                description="x" * 501  # 超过500字符限制
            )

    def test_user_scheduled_task_update_validation(self, mock_indicator_config):
        """测试更新定时任务请求验证"""
        # 部分字段更新
        request = UserScheduledTaskUpdate(
            name="新任务名称",
            cron_expression="0 10 * * 1-5",
            is_active=False
        )
        
        assert request.name == "新任务名称"
        assert request.cron_expression == "0 10 * * 1-5"
        assert request.is_active is False
        assert request.task_config is None  # 未设置的字段应为None
        assert request.description is None
        assert request.max_executions is None

    def test_user_scheduled_task_response_serialization(self, mock_scheduled_task):
        """测试定时任务响应序列化"""
        # 模拟从数据库获取的任务
        task_dict = {
            'id': mock_scheduled_task.id,
            'name': mock_scheduled_task.name,
            'task_type': mock_scheduled_task.task_type,
            'cron_expression': mock_scheduled_task.cron_expression,
            'is_active': mock_scheduled_task.is_active,
            'max_executions': mock_scheduled_task.max_executions,
            'current_executions': mock_scheduled_task.current_executions,
            'task_config': {"indicators": ["MACD", "RSI"]},  # 已解析的配置
            'description': mock_scheduled_task.description,
            'last_execution': mock_scheduled_task.last_execution,
            'next_execution': mock_scheduled_task.next_execution,
            'created_at': mock_scheduled_task.created_at,
            'updated_at': mock_scheduled_task.updated_at
        }
        
        response = UserScheduledTaskResponse(**task_dict)
        
        assert response.id == mock_scheduled_task.id
        assert response.name == mock_scheduled_task.name
        assert response.task_type == mock_scheduled_task.task_type
        assert isinstance(response.task_config, dict)

    def test_task_execution_response_serialization(self, mock_task_execution):
        """测试任务执行记录响应序列化"""
        execution_dict = {
            'id': mock_task_execution.id,
            'scheduled_task_id': mock_task_execution.scheduled_task_id,
            'trigger_type': mock_task_execution.trigger_type,
            'task_type': mock_task_execution.task_type,
            'status': mock_task_execution.status,
            'start_time': datetime.now(),
            'end_time': datetime.now() + timedelta(minutes=5),
            'duration_seconds': 300,
            'results_count': 10,
            'error_message': None,
            'results_data': {"results": [{"symbol": "000001.SZ", "signal": "buy"}]},
            'created_at': mock_task_execution.created_at,
            'scheduled_task_name': "测试任务"
        }
        
        response = TaskExecutionResponse(**execution_dict)
        
        assert response.id == mock_task_execution.id
        assert response.scheduled_task_id == mock_task_execution.scheduled_task_id
        assert response.trigger_type == mock_task_execution.trigger_type
        assert response.duration_seconds == 300
        assert response.results_count == 10
        assert response.scheduled_task_name == "测试任务"

    def test_enum_validation(self):
        """测试枚举值验证"""
        # 有效枚举值
        task_type = TaskType.INDICATOR_SCAN
        trigger_type = TriggerType.SCHEDULED
        task_status = TaskStatus.COMPLETED
        
        assert task_type == "indicator_scan"
        assert trigger_type == "scheduled"
        assert task_status == "completed"
        
        # 无效枚举值会在Pydantic验证时抛出异常
        with pytest.raises(ValueError):
            UserScheduledTaskCreate(
                name="测试任务",
                task_type="invalid_type",  # 无效任务类型
                cron_expression="0 9 * * 1-5",
                task_config=IndicatorScanConfig(indicators=["MACD"])
            )

    def test_datetime_handling(self):
        """测试日期时间处理"""
        now = datetime.now()
        
        # 创建任务响应时的日期时间处理
        response = UserScheduledTaskResponse(
            id=1,
            name="测试任务",
            task_type=TaskType.INDICATOR_SCAN,
            cron_expression="0 9 * * *",
            is_active=True,
            max_executions=None,
            current_executions=0,
            task_config={"indicators": ["MACD"]},
            description=None,
            last_execution=now,
            next_execution=now + timedelta(hours=1),
            created_at=now,
            updated_at=now
        )
        
        assert isinstance(response.last_execution, datetime)
        assert isinstance(response.next_execution, datetime)
        assert isinstance(response.created_at, datetime)
        assert isinstance(response.updated_at, datetime)

    def test_optional_fields(self, mock_indicator_config):
        """测试可选字段"""
        # 只设置必需字段
        minimal_request = UserScheduledTaskCreate(
            name="最小任务",
            cron_expression="0 * * * *",
            task_config=mock_indicator_config
        )
        
        # 验证可选字段默认值
        assert minimal_request.task_type == TaskType.INDICATOR_SCAN
        assert minimal_request.description is None
        assert minimal_request.max_executions is None
        
        # 更新请求中所有字段都是可选的
        empty_update = UserScheduledTaskUpdate()
        
        assert empty_update.name is None
        assert empty_update.cron_expression is None
        assert empty_update.task_config is None
        assert empty_update.description is None
        assert empty_update.max_executions is None
        assert empty_update.is_active is None