"""定时任务测试配置"""

import pytest
import asyncio
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from unittest.mock import Mock, AsyncMock, patch

from app.models.user import User
from app.models.task import UserScheduledTask, TaskExecution
from app.schemas.scheduled_task import (
    IndicatorScanConfig, 
    TaskStatus, 
    TaskType, 
    TriggerType
)

@pytest.fixture
async def mock_user(db_session: Session) -> User:
    """创建测试用户"""
    user = User(
        username="testuser",
        email="<EMAIL>",
        hashed_password="hashed_password_here",
        is_active=True
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user

@pytest.fixture
def mock_indicator_config() -> IndicatorScanConfig:
    """模拟指标扫描配置"""
    return IndicatorScanConfig(
        indicators=["MACD", "RSI", "KDJ"],
        stock_codes=["000001.SZ", "000002.SZ"],
        parameters={
            "MACD": {"fast_period": 12, "slow_period": 26, "signal_period": 9},
            "RSI": {"period": 14},
            "KDJ": {"k_period": 9, "d_period": 3}
        },
        scan_mode="traditional",
        periods=["d"],
        adjust="n"
    )

@pytest.fixture
async def mock_scheduled_task(db_session: Session, mock_user: User, mock_indicator_config: IndicatorScanConfig) -> UserScheduledTask:
    """创建测试定时任务"""
    task = UserScheduledTask(
        user_id=mock_user.id,
        name="测试定时任务",
        task_type=TaskType.INDICATOR_SCAN,
        cron_expression="0 9 * * 1-5",  # 工作日早上9点执行
        task_config=mock_indicator_config.json(),
        description="测试用定时任务",
        is_active=True,
        max_executions=None,
        current_executions=0,
        next_execution=datetime.now() + timedelta(hours=1)
    )
    db_session.add(task)
    db_session.commit()
    db_session.refresh(task)
    return task

@pytest.fixture
async def mock_task_execution(db_session: Session, mock_user: User, mock_scheduled_task: UserScheduledTask) -> TaskExecution:
    """创建测试任务执行记录"""
    execution = TaskExecution(
        user_id=mock_user.id,
        scheduled_task_id=mock_scheduled_task.id,
        trigger_type=TriggerType.SCHEDULED,
        task_type=TaskType.INDICATOR_SCAN,
        task_config=mock_scheduled_task.task_config,
        status=TaskStatus.PENDING
    )
    db_session.add(execution)
    db_session.commit()
    db_session.refresh(execution)
    return execution

@pytest.fixture
def mock_memory_scanner():
    """模拟内存扫描器"""
    scanner = Mock()
    scanner.start_scan = AsyncMock(return_value="task_123")
    scanner.stop_scan = AsyncMock()
    return scanner

@pytest.fixture
def mock_session_manager():
    """模拟会话管理器"""
    manager = Mock()
    manager.create_session = Mock()
    manager.remove_session = Mock()
    
    # 创建一个模拟的扫描任务
    mock_task = Mock()
    mock_task.status.value = "completed"
    mock_task.results = [
        Mock(dict=Mock(return_value={"symbol": "000001.SZ", "indicator": "MACD", "signal": "buy"})),
        Mock(dict=Mock(return_value={"symbol": "000002.SZ", "indicator": "RSI", "signal": "sell"}))
    ]
    mock_task.error_message = None
    
    manager.get_scan_task = Mock(return_value=mock_task)
    return manager

@pytest.fixture
def mock_db_session():
    """模拟数据库会话"""
    session = Mock(spec=Session)
    session.add = Mock()
    session.commit = Mock()
    session.refresh = Mock()
    session.rollback = Mock()
    session.query = Mock()
    return session

@pytest.fixture
def valid_cron_expressions():
    """有效的Cron表达式测试用例"""
    return [
        "0 9 * * 1-5",      # 工作日早上9点
        "30 14 * * *",      # 每天下午2:30
        "0 0 1 * *",        # 每月1号午夜
        "*/15 * * * *",     # 每15分钟
        "0 8-17 * * 1-5",   # 工作日8-17点每小时
    ]

@pytest.fixture
def invalid_cron_expressions():
    """无效的Cron表达式测试用例"""
    return [
        "invalid",          # 完全无效
        "60 * * * *",       # 分钟超出范围
        "* 25 * * *",       # 小时超出范围
        "* * 32 * *",       # 日期超出范围
        "* * * 13 *",       # 月份超出范围
        "* * * * 8",        # 星期超出范围
        "",                 # 空字符串
        "* * * *",          # 缺少字段
        "* * * * * *",      # 太多字段
    ]

@pytest.fixture
def scan_result_samples():
    """扫描结果样本"""
    return [
        {
            "symbol": "000001.SZ",
            "name": "平安银行",
            "indicator": "MACD",
            "signal": "buy",
            "strength": 0.85,
            "timestamp": "2024-01-01T09:30:00Z"
        },
        {
            "symbol": "000002.SZ", 
            "name": "万科A",
            "indicator": "RSI",
            "signal": "sell",
            "strength": 0.72,
            "timestamp": "2024-01-01T09:30:00Z"
        },
        {
            "symbol": "600000.SH",
            "name": "浦发银行", 
            "indicator": "KDJ",
            "signal": "hold",
            "strength": 0.45,
            "timestamp": "2024-01-01T09:30:00Z"
        }
    ]

@pytest.fixture
def performance_test_tasks():
    """性能测试任务数据"""
    return [
        {
            "name": f"性能测试任务_{i}",
            "cron_expression": "0 * * * *",
            "task_config": IndicatorScanConfig(
                indicators=["MACD", "RSI"],
                stock_codes=[f"00000{i}.SZ"],
                parameters={},
                scan_mode="traditional",
                periods=["d"],
                adjust="n"
            )
        }
        for i in range(1, 51)  # 50个测试任务
    ]

@pytest.fixture
async def async_test_timeout():
    """异步测试超时设置"""
    return 30  # 30秒超时

@pytest.fixture
def task_execution_scenarios():
    """任务执行场景测试数据"""
    return {
        "success_scenario": {
            "scan_status": "completed",
            "scan_results": [
                {"symbol": "000001.SZ", "signal": "buy"},
                {"symbol": "000002.SZ", "signal": "sell"}
            ],
            "expected_status": TaskStatus.COMPLETED,
            "expected_results_count": 2
        },
        "failure_scenario": {
            "scan_status": "failed", 
            "error_message": "扫描器内部错误",
            "expected_status": TaskStatus.FAILED,
            "expected_results_count": 0
        },
        "timeout_scenario": {
            "scan_status": "running",  # 永远不完成
            "timeout_seconds": 5,
            "expected_status": TaskStatus.FAILED,
            "expected_error_pattern": ".*超时.*"
        },
        "cancelled_scenario": {
            "scan_status": "cancelled",
            "expected_status": TaskStatus.FAILED,
            "expected_results_count": 0
        }
    }

@pytest.fixture
def concurrent_execution_config():
    """并发执行配置"""
    return {
        "max_concurrent_tasks": 5,
        "user_task_limit": 3,
        "scheduler_interval": 1,  # 1秒用于测试
        "retry_attempts": 2
    }

@pytest.fixture
def mock_croniter():
    """模拟croniter"""
    with patch('app.services.tasks.scheduler.croniter') as mock:
        mock_instance = Mock()
        mock_instance.get_next.return_value = datetime.now() + timedelta(hours=1)
        mock.return_value = mock_instance
        yield mock

@pytest.fixture
def mock_asyncio_sleep():
    """模拟asyncio.sleep以加速测试"""
    with patch('asyncio.sleep', new_callable=AsyncMock) as mock_sleep:
        yield mock_sleep