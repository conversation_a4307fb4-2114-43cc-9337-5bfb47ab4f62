"""
智能缓存装饰器测试
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta

from app.utils.decorators import (
    SmartCache,
    smart_data_cache,
    cache_stock_info,
    cache_stock_daily,
    cache_stock_realtime,
    get_cache_manager
)
from app.core.data.models import DataType, CacheStrategy


class TestSmartCache:
    """智能缓存装饰器测试"""
    
    @pytest.fixture
    def mock_data_manager(self):
        """模拟数据管理器"""
        manager = Mock()
        cache_manager = Mock()
        cache_manager.get = AsyncMock(return_value=None)
        cache_manager.set = AsyncMock()
        cache_manager.delete = AsyncMock()
        manager.router.cache_manager = cache_manager
        return manager
    
    def test_smart_cache_init(self):
        """测试智能缓存初始化"""
        cache = SmartCache(
            data_type=DataType.STOCK_INFO,
            cache_strategy=CacheStrategy.CACHE_FIRST,
            cache_ttl=1800
        )
        
        assert cache.data_type == DataType.STOCK_INFO
        assert cache.cache_strategy == CacheStrategy.CACHE_FIRST
        assert cache.cache_ttl == 1800
        assert cache.enabled is True
    
    def test_disabled_cache(self):
        """测试禁用缓存"""
        def test_func(x):
            return x * 2
        
        cache = SmartCache(enabled=False)
        decorated_func = cache(test_func)
        
        # 禁用缓存时应该直接返回原函数
        assert decorated_func == test_func
    
    @pytest.mark.asyncio
    async def test_async_function_caching(self, mock_data_manager):
        """测试异步函数缓存"""
        with patch('app.utils.decorators.get_data_manager', return_value=mock_data_manager):
            call_count = 0
            
            @smart_data_cache(data_type=DataType.STOCK_INFO, cache_ttl=300)
            async def async_test_func(stock_code: str):
                nonlocal call_count
                call_count += 1
                return {"code": stock_code, "name": f"股票{stock_code}"}
            
            # 第一次调用 - 缓存未命中
            result1 = await async_test_func("000001")
            assert result1 == {"code": "000001", "name": "股票000001"}
            assert call_count == 1
            
            # 模拟缓存命中
            cached_data = {
                'result': {"code": "000001", "name": "股票000001"},
                'timestamp': datetime.now().isoformat(),
                'ttl': 300
            }
            mock_data_manager.router.cache_manager.get.return_value = cached_data
            
            # 第二次调用 - 应该命中缓存
            result2 = await async_test_func("000001")
            assert result2 == {"code": "000001", "name": "股票000001"}
            assert call_count == 1  # 函数不应该被再次调用
    
    @pytest.mark.asyncio
    async def test_cache_key_generation(self, mock_data_manager):
        """测试缓存键生成"""
        with patch('app.utils.decorators.get_data_manager', return_value=mock_data_manager):
            
            @smart_data_cache(data_type=DataType.STOCK_DAILY)
            async def get_stock_data(stock_code: str, start_date: str, end_date: str):
                return [{"date": start_date, "close": 10.0}]
            
            await get_stock_data("000001", "2024-01-01", "2024-01-31")
            
            # 验证缓存键包含函数名和参数
            set_call = mock_data_manager.router.cache_manager.set.call_args
            cache_key = set_call[0][0]
            
            assert "smart_cache:get_stock_data:" in cache_key
    
    @pytest.mark.asyncio
    async def test_force_refresh(self, mock_data_manager):
        """测试强制刷新"""
        with patch('app.utils.decorators.get_data_manager', return_value=mock_data_manager):
            call_count = 0
            
            @smart_data_cache(data_type=DataType.STOCK_INFO)
            async def test_func(stock_code: str, force_refresh: bool = False):
                nonlocal call_count
                call_count += 1
                return {"code": stock_code}
            
            # 第一次调用
            await test_func("000001")
            assert call_count == 1
            
            # 设置缓存命中
            cached_data = {
                'result': {"code": "000001"},
                'timestamp': datetime.now().isoformat(),
                'ttl': 3600
            }
            mock_data_manager.router.cache_manager.get.return_value = cached_data
            
            # 强制刷新调用
            await test_func("000001", force_refresh=True)
            assert call_count == 2  # 应该绕过缓存重新调用
    
    @pytest.mark.asyncio
    async def test_cache_expiration(self, mock_data_manager):
        """测试缓存过期"""
        with patch('app.utils.decorators.get_data_manager', return_value=mock_data_manager):
            call_count = 0
            
            @smart_data_cache(data_type=DataType.STOCK_INFO, cache_ttl=60)
            async def test_func(stock_code: str):
                nonlocal call_count
                call_count += 1
                return {"code": stock_code}
            
            # 第一次调用
            await test_func("000001")
            assert call_count == 1
            
            # 设置过期的缓存数据
            expired_time = datetime.now() - timedelta(seconds=120)  # 2分钟前
            expired_cached_data = {
                'result': {"code": "000001"},
                'timestamp': expired_time.isoformat(),
                'ttl': 60
            }
            mock_data_manager.router.cache_manager.get.return_value = expired_cached_data
            
            # 应该重新调用函数因为缓存过期
            await test_func("000001")
            assert call_count == 2
    
    @pytest.mark.asyncio
    async def test_sync_function_caching(self, mock_data_manager):
        """测试同步函数缓存"""
        with patch('app.utils.decorators.get_data_manager', return_value=mock_data_manager):
            call_count = 0
            
            @smart_data_cache(data_type=DataType.STOCK_INFO)
            def sync_test_func(stock_code: str):
                nonlocal call_count
                call_count += 1
                return {"code": stock_code}
            
            # 模拟事件循环存在的情况
            with patch('asyncio.get_event_loop') as mock_get_loop:
                mock_loop = Mock()
                mock_loop.run_until_complete = AsyncMock(
                    return_value={"code": "000001"}
                )
                mock_get_loop.return_value = mock_loop
                
                result = sync_test_func("000001")
                assert result == {"code": "000001"}
    
    def test_cache_stats(self):
        """测试缓存统计"""
        cache = SmartCache(data_type=DataType.STOCK_INFO)
        
        # 初始统计
        stats = cache.get_stats()
        assert stats['total_calls'] == 0
        assert stats['cache_hits'] == 0
        assert stats['cache_misses'] == 0
        
        # 模拟一些调用
        cache.stats['total_calls'] = 10
        cache.stats['cache_hits'] = 7
        cache.stats['cache_misses'] = 3
        cache.stats['total_time'] = 5.0
        cache.stats['cache_time'] = 1.0
        
        stats = cache.get_stats()
        assert "70.00%" in stats['hit_rate']
        assert "0.5000s" in stats['avg_total_time']


class TestPredefinedCacheDecorators:
    """预定义缓存装饰器测试"""
    
    def test_cache_stock_info_decorator(self):
        """测试股票信息缓存装饰器"""
        @cache_stock_info(cache_ttl=1800)
        def get_stock_info(stock_code: str):
            return {"code": stock_code}
        
        # 检查装饰器是否正确应用
        assert hasattr(get_stock_info, '__wrapped__')
    
    def test_cache_stock_daily_decorator(self):
        """测试股票日线数据缓存装饰器"""
        @cache_stock_daily(cache_ttl=900)
        def get_daily_data(stock_code: str, start_date: str):
            return [{"date": start_date, "close": 10.0}]
        
        assert hasattr(get_daily_data, '__wrapped__')
    
    def test_cache_stock_realtime_decorator(self):
        """测试股票实时数据缓存装饰器"""
        @cache_stock_realtime(cache_ttl=15)
        def get_realtime_data(stock_codes: list):
            return [{"code": code, "price": 10.0} for code in stock_codes]
        
        assert hasattr(get_realtime_data, '__wrapped__')


class TestCacheManager:
    """缓存管理器测试"""
    
    def test_cache_manager_singleton(self):
        """测试缓存管理器单例"""
        manager1 = get_cache_manager()
        manager2 = get_cache_manager()
        
        assert manager1 is manager2
    
    def test_register_cache(self):
        """测试注册缓存实例"""
        manager = get_cache_manager()
        cache_instance = SmartCache(data_type=DataType.STOCK_INFO)
        
        manager.register_cache("test_cache", cache_instance)
        
        assert "test_cache" in manager.cache_instances
        assert manager.cache_instances["test_cache"] is cache_instance
    
    def test_get_cache_stats(self):
        """测试获取缓存统计"""
        manager = get_cache_manager()
        cache_instance = SmartCache(data_type=DataType.STOCK_INFO)
        
        # 模拟一些统计数据
        cache_instance.stats['total_calls'] = 5
        cache_instance.stats['cache_hits'] = 3
        
        manager.register_cache("test_cache", cache_instance)
        
        # 获取特定缓存的统计
        stats = manager.get_cache_stats("test_cache")
        assert stats['total_calls'] == 5
        
        # 获取所有缓存的统计
        all_stats = manager.get_cache_stats()
        assert "test_cache" in all_stats
    
    @pytest.mark.asyncio
    async def test_invalidate_cache(self):
        """测试缓存失效"""
        manager = get_cache_manager()
        cache_instance = SmartCache(data_type=DataType.STOCK_INFO)
        cache_instance.invalidate = AsyncMock()
        
        manager.register_cache("test_cache", cache_instance)
        
        # 失效特定缓存
        await manager.invalidate_cache("test_cache", "test_arg")
        cache_instance.invalidate.assert_called_once_with("test_arg")
        
        # 失效所有缓存
        cache_instance.invalidate.reset_mock()
        await manager.invalidate_cache(None, "test_arg")
        cache_instance.invalidate.assert_called_once_with("test_arg")