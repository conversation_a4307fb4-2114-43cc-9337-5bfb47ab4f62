"""
统一数据管理器的增强单元测试

针对 UnifiedDataManager 的全面测试，包括核心功能、边界条件、异常处理和性能验证。
测试覆盖率目标: 98%+
"""

import pytest
import asyncio
import uuid
from unittest.mock import AsyncMock, MagicMock, patch, call
from datetime import datetime, date, timedelta
import json
from typing import Dict, Any, List

from app.core.data.manager import UnifiedDataManager, get_data_manager, init_data_manager, cleanup_data_manager
from app.core.data.models import (
    DataType, DataRequest, DataResult, CacheStrategy, RoutingStrategy,
    DataManagerConfig, CacheConfig, RoutingConfig, PluginConfig
)


@pytest.mark.unit
@pytest.mark.fast
class TestUnifiedDataManagerCore:
    """统一数据管理器核心功能测试"""
    
    @pytest.fixture
    def manager_config(self):
        """测试用管理器配置"""
        return DataManagerConfig(
            enabled=True,
            debug=True,
            cache=CacheConfig(
                enabled=True,
                ttl=300,
                max_size=100
            ),
            routing=RoutingConfig(
                strategy=RoutingStrategy.FASTEST,
                timeout=10.0,
                max_retries=2
            ),
            plugins=PluginConfig(
                enabled=True,
                auto_discovery=False
            ),
            max_concurrent_requests=10,
            request_queue_size=100
        )
    
    @pytest.fixture
    async def manager(self, manager_config):
        """创建测试用数据管理器"""
        with patch('app.core.data.manager.settings') as mock_settings:
            mock_settings.REDIS_URL = None  # 避免Redis依赖
            mock_settings.DATA_API_TYPE = "test"
            mock_settings.TUSHARE_TOKEN = None
            mock_settings.MAIRUI_TOKEN = None
            
            manager = UnifiedDataManager(manager_config)
            
            # Mock 核心组件
            manager.router = AsyncMock()
            manager.event_bus = AsyncMock()
            manager.plugin_manager = AsyncMock()
            
            # Mock plugin_manager方法
            manager.plugin_manager.initialize = AsyncMock()
            manager.plugin_manager.cleanup = AsyncMock()
            manager.plugin_manager.apply_request_middleware = AsyncMock(side_effect=lambda x: x)
            manager.plugin_manager.apply_response_middleware = AsyncMock(side_effect=lambda x, y: x)
            manager.plugin_manager.get_stats = MagicMock(return_value={})
            
            yield manager
            
            if manager._running:
                await manager.stop()
    
    @pytest.mark.asyncio
    async def test_manager_initialization(self, manager_config):
        """测试管理器初始化"""
        with patch('app.core.data.manager.settings') as mock_settings:
            mock_settings.REDIS_URL = None
            mock_settings.DATA_API_TYPE = "test"
            
            manager = UnifiedDataManager(manager_config)
            
            assert manager.config == manager_config
            assert not manager._running
            assert manager.stats['total_requests'] == 0
            assert manager.stats['successful_requests'] == 0
            assert manager.stats['failed_requests'] == 0
            assert isinstance(manager.stats['start_time'], datetime)
    
    @pytest.mark.asyncio
    async def test_manager_start_stop_lifecycle(self, manager):
        """测试管理器启动-停止生命周期"""
        # 测试启动
        assert not manager._running
        
        await manager.start()
        
        assert manager._running
        manager.plugin_manager.initialize.assert_called_once()
        manager.event_bus.start.assert_called_once()
        manager.event_bus.emit.assert_called()
        
        # 测试重复启动
        manager.event_bus.reset_mock()
        await manager.start()  # 应该不会重复启动
        manager.event_bus.start.assert_not_called()
        
        # 测试停止
        await manager.stop()
        
        assert not manager._running
        manager.event_bus.stop.assert_called_once()
        manager.plugin_manager.cleanup.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_data_basic(self, manager):
        """测试基本数据获取功能"""
        # 准备测试数据
        expected_result = DataResult(
            request_id="test-123",
            success=True,
            data=[{"code": "000001", "name": "测试股票"}],
            count=1,
            response_time=0.1
        )
        
        manager.router.route_request = AsyncMock(return_value=expected_result)
        
        # 执行测试
        result = await manager.get_data(
            data_type=DataType.STOCK_INFO,
            params={"stock_code": "000001"},
            cache_strategy=CacheStrategy.CACHE_FIRST,
            routing_strategy=RoutingStrategy.FASTEST
        )
        
        # 验证结果
        assert result.success
        assert result.data == expected_result.data
        assert result.count == 1
        
        # 验证调用
        manager.router.route_request.assert_called_once()
        call_args = manager.router.route_request.call_args[0][0]
        assert call_args.data_type == DataType.STOCK_INFO
        assert call_args.params == {"stock_code": "000001"}
        assert call_args.cache_strategy == CacheStrategy.CACHE_FIRST
        assert call_args.routing_strategy == RoutingStrategy.FASTEST
    
    @pytest.mark.asyncio
    async def test_get_data_with_error(self, manager):
        """测试数据获取错误处理"""
        # 模拟路由器抛出异常
        manager.router.route_request = AsyncMock(side_effect=Exception("路由错误"))
        
        result = await manager.get_data(
            data_type=DataType.STOCK_INFO,
            params={"stock_code": "000001"}
        )
        
        # 验证错误结果
        assert not result.success
        assert result.error_code == "PROCESS_ERROR"
        assert "路由错误" in result.error_message
        assert result.response_time > 0
        
        # 验证统计更新
        assert manager.stats['total_requests'] == 1
        assert manager.stats['failed_requests'] == 1
        assert manager.stats['successful_requests'] == 0
    
    @pytest.mark.asyncio
    async def test_batch_get_data(self, manager):
        """测试批量数据获取"""
        # 准备测试数据
        requests = [
            {"data_type": DataType.STOCK_INFO, "params": {"stock_code": "000001"}},
            {"data_type": DataType.STOCK_INFO, "params": {"stock_code": "000002"}},
            {"data_type": DataType.STOCK_INFO, "params": {"stock_code": "000003"}}
        ]
        
        # Mock单个请求结果
        def mock_get_data(**kwargs):
            stock_code = kwargs['params']['stock_code']
            return DataResult(
                request_id=f"batch-{stock_code}",
                success=True,
                data=[{"code": stock_code, "name": f"股票{stock_code}"}],
                count=1
            )
        
        manager.get_data = AsyncMock(side_effect=mock_get_data)
        
        # 执行批量请求
        results = await manager.batch_get_data(requests, max_concurrency=2)
        
        # 验证结果
        assert len(results) == 3
        assert all(result.success for result in results)
        assert manager.get_data.call_count == 3
        
        # 验证并发控制
        call_args_list = [call.kwargs for call in manager.get_data.call_args_list]
        expected_stock_codes = {"000001", "000002", "000003"}
        actual_stock_codes = {args['params']['stock_code'] for args in call_args_list}
        assert actual_stock_codes == expected_stock_codes
    
    @pytest.mark.asyncio
    async def test_batch_get_data_with_exceptions(self, manager):
        """测试批量数据获取异常处理"""
        requests = [
            {"data_type": DataType.STOCK_INFO, "params": {"stock_code": "000001"}},
            {"data_type": DataType.STOCK_INFO, "params": {"stock_code": "ERROR"}}
        ]
        
        def mock_get_data(**kwargs):
            if kwargs['params']['stock_code'] == "ERROR":
                raise ValueError("测试异常")
            return DataResult(
                request_id="success",
                success=True,
                data=[{"code": "000001"}],
                count=1
            )
        
        manager.get_data = AsyncMock(side_effect=mock_get_data)
        
        results = await manager.batch_get_data(requests)
        
        assert len(results) == 2
        assert results[0].success
        assert not results[1].success
        assert results[1].error_code == "BATCH_PROCESS_ERROR"
        assert "测试异常" in results[1].error_message
    
    @pytest.mark.asyncio
    async def test_empty_batch_request(self, manager):
        """测试空批量请求"""
        results = await manager.batch_get_data([])
        assert results == []


@pytest.mark.unit
@pytest.mark.fast
class TestUnifiedDataManagerConvenienceMethods:
    """测试便民方法"""
    
    @pytest.fixture
    async def manager(self):
        """创建测试用数据管理器"""
        with patch('app.core.data.manager.settings') as mock_settings:
            mock_settings.REDIS_URL = None
            
            manager = UnifiedDataManager()
            manager.get_data = AsyncMock()
            yield manager
    
    @pytest.mark.asyncio
    async def test_get_stock_info(self, manager):
        """测试获取股票信息便民方法"""
        expected_result = DataResult(
            request_id="test",
            success=True,
            data=[{"code": "000001", "name": "测试股票"}],
            count=1
        )
        manager.get_data.return_value = expected_result
        
        result = await manager.get_stock_info("000001")
        
        manager.get_data.assert_called_once_with(
            data_type=DataType.STOCK_INFO,
            params={"stock_code": "000001"}
        )
        assert result == expected_result
    
    @pytest.mark.asyncio
    async def test_get_stock_info_all(self, manager):
        """测试获取所有股票信息"""
        expected_result = DataResult(
            request_id="test",
            success=True,
            data=[{"code": "000001"}, {"code": "000002"}],
            count=2
        )
        manager.get_data.return_value = expected_result
        
        result = await manager.get_stock_info()
        
        manager.get_data.assert_called_once_with(
            data_type=DataType.STOCK_INFO,
            params={}
        )
        assert result == expected_result
    
    @pytest.mark.asyncio
    async def test_get_stock_daily(self, manager):
        """测试获取股票日线数据"""
        start_date = date(2024, 1, 1)
        end_date = date(2024, 1, 31)
        
        expected_result = DataResult(
            request_id="test",
            success=True,
            data=[{"date": "2024-01-01", "close": 10.0}],
            count=1
        )
        manager.get_data.return_value = expected_result
        
        result = await manager.get_stock_daily(
            "000001", 
            start_date=start_date, 
            end_date=end_date,
            cache_strategy=CacheStrategy.API_FIRST
        )
        
        manager.get_data.assert_called_once_with(
            data_type=DataType.STOCK_DAILY,
            params={
                "stock_code": "000001",
                "start_date": start_date,
                "end_date": end_date
            },
            cache_strategy=CacheStrategy.API_FIRST
        )
        assert result == expected_result
    
    @pytest.mark.asyncio
    async def test_get_stock_realtime(self, manager):
        """测试获取实时行情"""
        stock_codes = ["000001", "000002", "000003"]
        
        expected_result = DataResult(
            request_id="test",
            success=True,
            data=[
                {"code": "000001", "price": 10.0},
                {"code": "000002", "price": 20.0}
            ],
            count=2
        )
        manager.get_data.return_value = expected_result
        
        result = await manager.get_stock_realtime(stock_codes)
        
        manager.get_data.assert_called_once_with(
            data_type=DataType.STOCK_REALTIME,
            params={"stock_codes": stock_codes}
        )
        assert result == expected_result
    
    @pytest.mark.asyncio
    async def test_get_stock_indicators(self, manager):
        """测试获取技术指标"""
        indicator_types = ["MACD", "RSI", "KDJ"]
        start_date = "2024-01-01"
        end_date = "2024-01-31"
        
        expected_result = DataResult(
            request_id="test",
            success=True,
            data=[{"indicator": "MACD", "value": 0.5}],
            count=1
        )
        manager.get_data.return_value = expected_result
        
        result = await manager.get_stock_indicators(
            "000001",
            indicator_types,
            start_date=start_date,
            end_date=end_date
        )
        
        manager.get_data.assert_called_once_with(
            data_type=DataType.STOCK_INDICATORS,
            params={
                "stock_code": "000001",
                "indicator_types": indicator_types,
                "start_date": start_date,
                "end_date": end_date
            }
        )
        assert result == expected_result


@pytest.mark.unit
class TestUnifiedDataManagerRegistration:
    """测试数据源和中间件注册"""
    
    @pytest.fixture
    async def manager(self):
        """创建测试用数据管理器"""
        with patch('app.core.data.manager.settings') as mock_settings:
            mock_settings.REDIS_URL = None
            
            manager = UnifiedDataManager()
            manager.router = MagicMock()
            manager.plugin_manager = MagicMock()
            yield manager
    
    def test_register_data_source(self, manager):
        """测试数据源注册"""
        def mock_handler(request: DataRequest) -> DataResult:
            return DataResult(request_id=request.request_id, success=True)
        
        def mock_health_check():
            return True
        
        data_types = [DataType.STOCK_INFO, DataType.STOCK_DAILY]
        
        manager.register_data_source(
            name="test_source",
            handler=mock_handler,
            priority=10,
            health_check=mock_health_check,
            data_types=data_types
        )
        
        manager.router.register_source.assert_called_once_with(
            "test_source", mock_handler, 10, mock_health_check
        )
    
    def test_register_middleware(self, manager):
        """测试中间件注册"""
        def mock_middleware(request):
            return request
        
        manager.register_middleware(mock_middleware, "request")
        
        manager.plugin_manager.register_middleware.assert_called_once_with(
            mock_middleware, "request"
        )


@pytest.mark.unit
class TestUnifiedDataManagerCacheOperations:
    """测试缓存操作"""
    
    @pytest.fixture
    async def manager(self):
        """创建测试用数据管理器"""
        with patch('app.core.data.manager.settings') as mock_settings:
            mock_settings.REDIS_URL = None
            
            manager = UnifiedDataManager()
            manager.router = MagicMock()
            manager.router.cache_manager = AsyncMock()
            yield manager
    
    @pytest.mark.asyncio
    async def test_invalidate_cache_by_key(self, manager):
        """测试通过缓存键使缓存失效"""
        cache_key = "test:cache:key"
        
        await manager.invalidate_cache(cache_key=cache_key)
        
        manager.router.cache_manager.delete.assert_called_once_with(cache_key)
    
    @pytest.mark.asyncio
    async def test_invalidate_cache_by_params(self, manager):
        """测试通过参数使缓存失效"""
        data_type = DataType.STOCK_INFO
        params = {"stock_code": "000001"}
        
        await manager.invalidate_cache(data_type=data_type, params=params)
        
        # 验证调用了删除方法
        manager.router.cache_manager.delete.assert_called_once()
        
        # 验证生成的缓存键包含预期内容
        call_args = manager.router.cache_manager.delete.call_args[0][0]
        assert "stock_info" in call_args
        assert "000001" in call_args


@pytest.mark.unit
class TestUnifiedDataManagerStatsAndHealth:
    """测试统计和健康检查"""
    
    @pytest.fixture
    async def manager(self):
        """创建测试用数据管理器"""
        with patch('app.core.data.manager.settings') as mock_settings:
            mock_settings.REDIS_URL = None
            
            manager = UnifiedDataManager()
            manager.router = MagicMock()
            manager.plugin_manager = MagicMock()
            manager._request_queue = MagicMock()
            
            # Mock各组件的统计方法
            manager.router.get_stats.return_value = {"cache_hits": 100}
            manager.plugin_manager.get_stats.return_value = {"plugins_loaded": 5}
            manager._request_queue.qsize.return_value = 3
            
            # 设置一些统计数据
            manager.stats.update({
                'total_requests': 100,
                'successful_requests': 95,
                'failed_requests': 5
            })
            
            yield manager
    
    def test_get_stats(self, manager):
        """测试获取统计信息"""
        stats = manager.get_stats()
        
        assert 'manager_stats' in stats
        assert 'router_stats' in stats
        assert 'plugin_stats' in stats
        assert 'queue_size' in stats
        
        manager_stats = stats['manager_stats']
        assert manager_stats['total_requests'] == 100
        assert manager_stats['successful_requests'] == 95
        assert manager_stats['failed_requests'] == 5
        assert manager_stats['success_rate'] == 95.0
        assert 'uptime_seconds' in manager_stats
        assert 'requests_per_second' in manager_stats
        
        assert stats['router_stats']['cache_hits'] == 100
        assert stats['plugin_stats']['plugins_loaded'] == 5
        assert stats['queue_size'] == 3
    
    @pytest.mark.asyncio
    async def test_health_check_without_redis(self, manager):
        """测试不含Redis的健康检查"""
        manager._running = True
        manager.plugin_manager.loaded_plugins = {"plugin1": {}, "plugin2": {}}
        manager.router.source_registry = MagicMock()
        manager.router.source_registry.sources = {"source1": {}, "source2": {}}
        manager.router.source_registry.health_check = AsyncMock(return_value=True)
        
        health = await manager.health_check()
        
        assert health['manager_running'] is True
        assert health['redis_connected'] is False
        assert health['plugins_loaded'] == 2
        assert health['data_sources'] == 2
        assert 'source_health' in health
        assert health['source_health']['source1'] is True
        assert health['source_health']['source2'] is True
    
    @pytest.mark.asyncio
    async def test_health_check_with_redis(self, manager):
        """测试含Redis的健康检查"""
        # 创建Redis mock
        manager.redis_client = AsyncMock()
        manager.redis_client.ping = AsyncMock(return_value=True)
        
        manager._running = True
        manager.plugin_manager.loaded_plugins = {}
        manager.router.source_registry = MagicMock()
        manager.router.source_registry.sources = {}
        
        health = await manager.health_check()
        
        assert health['redis_connected'] is True
        manager.redis_client.ping.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_health_check_redis_failure(self, manager):
        """测试Redis连接失败的健康检查"""
        manager.redis_client = AsyncMock()
        manager.redis_client.ping = AsyncMock(side_effect=Exception("连接失败"))
        
        manager._running = True
        manager.plugin_manager.loaded_plugins = {}
        manager.router.source_registry = MagicMock()
        manager.router.source_registry.sources = {}
        
        health = await manager.health_check()
        
        assert health['redis_connected'] is False


@pytest.mark.unit
class TestUnifiedDataManagerSingleton:
    """测试单例模式"""
    
    def test_get_data_manager_singleton(self):
        """测试获取单例数据管理器"""
        with patch('app.core.data.manager.settings') as mock_settings:
            mock_settings.REDIS_URL = None
            
            # 重置全局实例
            import app.core.data.manager
            app.core.data.manager._manager_instance = None
            
            manager1 = get_data_manager()
            manager2 = get_data_manager()
            
            assert manager1 is manager2
            assert isinstance(manager1, UnifiedDataManager)
    
    @pytest.mark.asyncio
    async def test_init_data_manager(self):
        """测试初始化数据管理器"""
        config = DataManagerConfig(debug=True)
        
        with patch('app.core.data.manager.settings') as mock_settings:
            mock_settings.REDIS_URL = None
            
            # 重置全局实例
            import app.core.data.manager
            app.core.data.manager._manager_instance = None
            
            manager = await init_data_manager(config)
            
            assert isinstance(manager, UnifiedDataManager)
            assert manager.config.debug is True
            
            # 清理
            await cleanup_data_manager()
    
    @pytest.mark.asyncio
    async def test_cleanup_data_manager(self):
        """测试清理数据管理器"""
        with patch('app.core.data.manager.settings') as mock_settings:
            mock_settings.REDIS_URL = None
            
            # 重置并创建实例
            import app.core.data.manager
            app.core.data.manager._manager_instance = None
            
            manager = get_data_manager()
            manager.stop = AsyncMock()
            
            await cleanup_data_manager()
            
            manager.stop.assert_called_once()
            assert app.core.data.manager._manager_instance is None


@pytest.mark.unit
class TestUnifiedDataManagerContextManager:
    """测试上下文管理器"""
    
    @pytest.mark.asyncio
    async def test_session_context_manager(self):
        """测试会话上下文管理器"""
        with patch('app.core.data.manager.settings') as mock_settings:
            mock_settings.REDIS_URL = None
            
            manager = UnifiedDataManager()
            manager.start = AsyncMock()
            manager.stop = AsyncMock()
            
            async with manager.session() as session_manager:
                assert session_manager is manager
                manager.start.assert_called_once()
            
            manager.stop.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_session_context_manager_with_exception(self):
        """测试上下文管理器异常处理"""
        with patch('app.core.data.manager.settings') as mock_settings:
            mock_settings.REDIS_URL = None
            
            manager = UnifiedDataManager()
            manager.start = AsyncMock()
            manager.stop = AsyncMock()
            
            with pytest.raises(ValueError):
                async with manager.session():
                    raise ValueError("测试异常")
            
            # 即使有异常，也应该调用stop
            manager.stop.assert_called_once()


@pytest.mark.unit
@pytest.mark.slow
class TestUnifiedDataManagerEdgeCases:
    """边界条件和异常场景测试"""
    
    @pytest.fixture
    async def manager(self):
        """创建测试用数据管理器"""
        with patch('app.core.data.manager.settings') as mock_settings:
            mock_settings.REDIS_URL = None
            
            manager = UnifiedDataManager()
            manager.router = AsyncMock()
            manager.event_bus = AsyncMock()
            manager.plugin_manager = AsyncMock()
            manager.plugin_manager.apply_request_middleware = AsyncMock(side_effect=lambda x: x)
            manager.plugin_manager.apply_response_middleware = AsyncMock(side_effect=lambda x, y: x)
            yield manager
    
    @pytest.mark.asyncio
    async def test_process_request_middleware_exception(self, manager):
        """测试请求中间件异常"""
        manager.plugin_manager.apply_request_middleware = AsyncMock(
            side_effect=Exception("中间件异常")
        )
        
        request = DataRequest(
            request_id="test",
            data_type=DataType.STOCK_INFO,
            params={}
        )
        
        result = await manager.process_request(request)
        
        assert not result.success
        assert result.error_code == "PROCESS_ERROR"
        assert "中间件异常" in result.error_message
    
    @pytest.mark.asyncio
    async def test_batch_get_data_max_concurrency(self, manager):
        """测试批量请求最大并发限制"""
        # 创建大量请求
        requests = [
            {"data_type": DataType.STOCK_INFO, "params": {"stock_code": f"00000{i}"}}
            for i in range(50)
        ]
        
        # Mock配置最大并发为5
        manager.config.max_concurrent_requests = 5
        
        call_count = 0
        async def mock_get_data(**kwargs):
            nonlocal call_count
            call_count += 1
            await asyncio.sleep(0.01)  # 模拟处理时间
            return DataResult(
                request_id=f"test-{call_count}",
                success=True,
                data=[],
                count=0
            )
        
        manager.get_data = AsyncMock(side_effect=mock_get_data)
        
        results = await manager.batch_get_data(requests, max_concurrency=5)
        
        assert len(results) == 50
        assert all(result.success for result in results)
        assert manager.get_data.call_count == 50
    
    @pytest.mark.asyncio
    async def test_string_data_type_conversion(self, manager):
        """测试字符串数据类型自动转换"""
        manager.router.route_request = AsyncMock(return_value=DataResult(
            request_id="test",
            success=True,
            data=[],
            count=0
        ))
        
        # 使用字符串类型
        result = await manager.get_data(
            data_type="stock_info",  # 字符串而非枚举
            params={}
        )
        
        assert result.success
        
        # 验证转换成功
        call_args = manager.router.route_request.call_args[0][0]
        assert call_args.data_type == DataType.STOCK_INFO
    
    @pytest.mark.asyncio
    async def test_invalid_data_type_string(self, manager):
        """测试无效数据类型字符串"""
        with pytest.raises(ValueError):
            await manager.get_data(
                data_type="invalid_type",
                params={}
            )
    
    @pytest.mark.asyncio
    async def test_event_emission_failure(self, manager):
        """测试事件发送失败不影响主流程"""
        manager.event_bus.emit = AsyncMock(side_effect=Exception("事件发送失败"))
        manager.router.route_request = AsyncMock(return_value=DataResult(
            request_id="test",
            success=True,
            data=[],
            count=0
        ))
        
        # 即使事件发送失败，主流程也应该成功
        result = await manager.get_data(
            data_type=DataType.STOCK_INFO,
            params={}
        )
        
        assert result.success


@pytest.mark.unit
@pytest.mark.performance
class TestUnifiedDataManagerPerformance:
    """性能相关测试"""
    
    @pytest.fixture
    async def manager(self):
        """创建性能测试用数据管理器"""
        config = DataManagerConfig(max_concurrent_requests=100)
        
        with patch('app.core.data.manager.settings') as mock_settings:
            mock_settings.REDIS_URL = None
            
            manager = UnifiedDataManager(config)
            manager.router = AsyncMock()
            manager.event_bus = AsyncMock()
            manager.plugin_manager = AsyncMock()
            manager.plugin_manager.apply_request_middleware = AsyncMock(side_effect=lambda x: x)
            manager.plugin_manager.apply_response_middleware = AsyncMock(side_effect=lambda x, y: x)
            yield manager
    
    @pytest.mark.asyncio
    async def test_concurrent_requests_performance(self, manager, benchmark):
        """测试并发请求性能"""
        manager.router.route_request = AsyncMock(return_value=DataResult(
            request_id="perf-test",
            success=True,
            data=[{"test": "data"}],
            count=1,
            response_time=0.001
        ))
        
        async def concurrent_requests():
            tasks = []
            for i in range(10):
                task = manager.get_data(
                    data_type=DataType.STOCK_INFO,
                    params={"stock_code": f"00000{i}"}
                )
                tasks.append(task)
            
            results = await asyncio.gather(*tasks)
            return results
        
        # 使用benchmark测试
        results = await benchmark(concurrent_requests)
        
        assert len(results) == 10
        assert all(result.success for result in results)
    
    @pytest.mark.asyncio
    async def test_memory_usage_stability(self, manager):
        """测试内存使用稳定性"""
        import gc
        import sys
        
        manager.router.route_request = AsyncMock(return_value=DataResult(
            request_id="memory-test",
            success=True,
            data=[{"test": f"data-{i}"} for i in range(100)],
            count=100
        ))
        
        # 记录初始内存使用
        gc.collect()
        initial_objects = len(gc.get_objects())
        
        # 执行多次请求
        for i in range(100):
            await manager.get_data(
                data_type=DataType.STOCK_INFO,
                params={"iteration": i}
            )
        
        # 检查内存是否稳定
        gc.collect()
        final_objects = len(gc.get_objects())
        
        # 允许适度的内存增长（小于20%）
        memory_growth_ratio = (final_objects - initial_objects) / initial_objects
        assert memory_growth_ratio < 0.2, f"内存增长过多: {memory_growth_ratio:.2%}"


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])