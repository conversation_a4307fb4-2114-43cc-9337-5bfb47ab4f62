"""
智能数据路由器测试
"""

import pytest
import asyncio
from unittest.mock import Mo<PERSON>, AsyncMock, patch
from datetime import datetime, timedelta
import json

from app.core.data.router import SmartDataRouter, CacheManager, DataSourceRegistry
from app.core.data.models import (
    DataRequest,
    DataResult,
    DataType,
    CacheStrategy,
    RoutingStrategy,
    CacheConfig,
    RoutingConfig,
    CacheLevel
)


class TestCacheManager:
    """缓存管理器测试"""
    
    @pytest.fixture
    def cache_config(self):
        """缓存配置fixture"""
        return CacheConfig(
            l1_enabled=True,
            l1_max_size=100,
            l1_ttl=300,
            l2_enabled=True,
            l2_ttl=3600
        )
    
    @pytest.fixture
    def mock_redis(self):
        """模拟Redis客户端"""
        redis_mock = AsyncMock()
        redis_mock.get.return_value = None
        redis_mock.setex.return_value = True
        redis_mock.delete.return_value = True
        return redis_mock
    
    def test_cache_manager_init(self, cache_config, mock_redis):
        """测试缓存管理器初始化"""
        manager = CacheManager(cache_config, mock_redis)
        
        assert manager.config == cache_config
        assert manager.redis_client == mock_redis
        assert manager.l1_cache is not None
    
    @pytest.mark.asyncio
    async def test_l1_cache_operations(self, cache_config):
        """测试L1内存缓存操作"""
        manager = CacheManager(cache_config, None)
        
        # 测试缓存未命中
        result = await manager.get("test_key")
        assert result is None
        
        # 测试缓存写入和命中
        test_data = {"test": "value"}
        await manager.set("test_key", test_data)
        
        result = await manager.get("test_key")
        assert result == test_data
        
        # 检查统计信息
        stats = manager.get_stats()
        assert stats['l1_hits'] > 0
        assert stats['l1_sets'] > 0
    
    @pytest.mark.asyncio
    async def test_l2_redis_cache_operations(self, cache_config, mock_redis):
        """测试L2 Redis缓存操作"""
        manager = CacheManager(cache_config, mock_redis)
        
        # 模拟Redis返回缓存数据
        test_data = {"test": "value"}
        mock_redis.get.return_value = json.dumps(test_data)
        
        result = await manager.get("test_key")
        assert result == test_data
        
        # 验证Redis调用
        expected_key = f"{cache_config.l2_key_prefix}:test_key"
        mock_redis.get.assert_called_with(expected_key)
    
    @pytest.mark.asyncio
    async def test_cache_deletion(self, cache_config, mock_redis):
        """测试缓存删除"""
        manager = CacheManager(cache_config, mock_redis)
        
        # 先设置缓存
        await manager.set("test_key", {"test": "value"})
        
        # 删除缓存
        await manager.delete("test_key")
        
        # 验证L1缓存已删除
        result = await manager.get("test_key")
        # L1应该miss，但L2可能还有数据（根据mock设置）
        
        # 验证Redis删除调用
        expected_key = f"{cache_config.l2_key_prefix}:test_key"
        mock_redis.delete.assert_called_with(expected_key)


class TestDataSourceRegistry:
    """数据源注册表测试"""
    
    @pytest.fixture
    def registry(self):
        """注册表fixture"""
        return DataSourceRegistry()
    
    def test_register_source(self, registry):
        """测试注册数据源"""
        handler = AsyncMock()
        health_check = AsyncMock(return_value=True)
        
        registry.register_source(
            name="test_source",
            handler=handler,
            priority=100,
            health_check=health_check
        )
        
        assert "test_source" in registry.sources
        assert registry.sources["test_source"]["priority"] == 100
        assert registry.health_status["test_source"] is True
    
    def test_get_available_sources(self, registry):
        """测试获取可用数据源"""
        # 注册多个数据源
        registry.register_source("source1", AsyncMock(), priority=100)
        registry.register_source("source2", AsyncMock(), priority=50)
        registry.register_source("source3", AsyncMock(), priority=75)
        
        # 设置一个数据源为不健康
        registry.health_status["source2"] = False
        
        available = registry.get_available_sources()
        
        # 应该只返回健康的数据源，并按优先级排序
        assert len(available) == 2
        assert available[0][0] == "source1"  # 最高优先级
        assert available[1][0] == "source3"
    
    @pytest.mark.asyncio
    async def test_health_check(self, registry):
        """测试健康检查"""
        health_check_mock = AsyncMock(return_value=True)
        
        registry.register_source(
            "test_source",
            AsyncMock(),
            health_check=health_check_mock
        )
        
        result = await registry.health_check("test_source")
        assert result is True
        health_check_mock.assert_called_once()
    
    def test_response_time_tracking(self, registry):
        """测试响应时间追踪"""
        registry.register_source("test_source", AsyncMock())
        
        # 记录响应时间
        registry.record_response_time("test_source", 0.5)
        registry.record_response_time("test_source", 1.0)
        registry.record_response_time("test_source", 0.75)
        
        avg_time = registry.get_avg_response_time("test_source")
        assert avg_time == 0.75  # (0.5 + 1.0 + 0.75) / 3


class TestSmartDataRouter:
    """智能数据路由器测试"""
    
    @pytest.fixture
    def cache_config(self):
        return CacheConfig()
    
    @pytest.fixture
    def routing_config(self):
        return RoutingConfig()
    
    @pytest.fixture
    def router(self, cache_config, routing_config):
        return SmartDataRouter(cache_config, routing_config)
    
    @pytest.fixture
    def sample_request(self):
        return DataRequest(
            request_id="test-request",
            data_type=DataType.STOCK_INFO,
            params={"stock_code": "000001"}
        )
    
    def test_router_initialization(self, router):
        """测试路由器初始化"""
        assert router.cache_manager is not None
        assert router.source_registry is not None
        assert isinstance(router.stats, dict)
    
    @pytest.mark.asyncio
    async def test_route_request_no_sources(self, router, sample_request):
        """测试没有数据源时的路由请求"""
        result = await router.route_request(sample_request)
        
        assert not result.is_success
        assert result.error_code == "NO_AVAILABLE_SOURCE"
    
    @pytest.mark.asyncio
    async def test_route_request_with_source(self, router, sample_request):
        """测试有数据源时的路由请求"""
        # 创建模拟处理器
        async def mock_handler(request):
            return DataResult(
                request_id=request.request_id,
                success=True,
                data={"code": "000001", "name": "平安银行"},
                count=1
            )
        
        # 注册数据源
        router.register_source("test_source", mock_handler, priority=100)
        
        result = await router.route_request(sample_request)
        
        assert result.is_success
        assert result.data == {"code": "000001", "name": "平安银行"}
        assert result.data_source == "test_source"
    
    @pytest.mark.asyncio
    async def test_cache_hit_scenario(self, router, sample_request):
        """测试缓存命中场景"""
        # 先设置缓存数据
        cache_key = sample_request.get_cache_key()
        cache_data = {
            'data': {"code": "000001", "name": "平安银行"},
            'count': 1,
            'timestamp': datetime.now().isoformat(),
            'ttl': 3600
        }
        
        await router.cache_manager.set(cache_key, cache_data)
        
        # 路由请求应该命中缓存
        result = await router.route_request(sample_request)
        
        assert result.is_success
        assert result.cache_hit is True
        assert result.data_source == "cache"
    
    @pytest.mark.asyncio
    async def test_force_refresh(self, router, sample_request):
        """测试强制刷新"""
        # 设置强制刷新请求
        sample_request.force_refresh = True
        
        # 创建模拟处理器
        async def mock_handler(request):
            return DataResult(
                request_id=request.request_id,
                success=True,
                data={"code": "000001", "name": "平安银行"},
                count=1
            )
        
        router.register_source("test_source", mock_handler)
        
        result = await router.route_request(sample_request)
        
        assert result.is_success
        assert result.cache_hit is False  # 应该绕过缓存
    
    @pytest.mark.asyncio
    async def test_routing_strategy_fastest(self, router, sample_request):
        """测试最快路由策略"""
        sample_request.routing_strategy = RoutingStrategy.FASTEST
        
        # 创建多个模拟处理器，模拟不同响应时间
        async def slow_handler(request):
            await asyncio.sleep(0.1)
            return DataResult(request_id=request.request_id, success=True, data="slow")
        
        async def fast_handler(request):
            return DataResult(request_id=request.request_id, success=True, data="fast")
        
        router.register_source("slow_source", slow_handler, priority=100)
        router.register_source("fast_source", fast_handler, priority=50)
        
        # 先让slow_source有一些响应时间记录
        router.source_registry.record_response_time("slow_source", 0.2)
        router.source_registry.record_response_time("fast_source", 0.01)
        
        result = await router.route_request(sample_request)
        
        # 应该选择最快的数据源
        assert result.data_source == "fast_source"
    
    def test_get_stats(self, router):
        """测试获取统计信息"""
        stats = router.get_stats()
        
        assert 'routing_stats' in stats
        assert 'cache_stats' in stats
        assert 'source_stats' in stats
        
        assert 'total_requests' in stats['routing_stats']
        assert 'cache_hit_rate' in stats['routing_stats']