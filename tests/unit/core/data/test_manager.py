"""
统一数据管理器测试
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from app.core.data.manager import UnifiedDataManager, get_data_manager
from app.core.data.models import (
    DataRequest,
    DataResult,
    DataType,
    CacheStrategy,
    RoutingStrategy,
    DataManagerConfig
)


@pytest.fixture
def manager_config():
    """管理器配置fixture"""
    return DataManagerConfig(
        enabled=True,
        debug=True,
        max_concurrent_requests=10
    )


@pytest.fixture
def mock_redis():
    """模拟Redis客户端"""
    return None  # 测试时不使用Redis


class TestUnifiedDataManager:
    """统一数据管理器测试"""
    
    def test_manager_initialization(self, manager_config, mock_redis):
        """测试管理器初始化"""
        with patch('app.core.data.manager.redis.from_url', return_value=mock_redis):
            manager = UnifiedDataManager(manager_config)
            
            assert manager.config == manager_config
            assert manager.router is not None
            assert manager.event_bus is not None
            assert manager.plugin_manager is not None
            assert not manager._running
    
    @pytest.mark.asyncio
    async def test_manager_start_stop(self, manager_config):
        """测试管理器启动和停止"""
        with patch('app.core.data.manager.redis.from_url', return_value=None):
            manager = UnifiedDataManager(manager_config)
            
            # 模拟插件管理器和事件总线
            manager.plugin_manager.initialize = AsyncMock()
            manager.event_bus.start = AsyncMock()
            manager.event_bus.stop = AsyncMock()
            manager.plugin_manager.cleanup = AsyncMock()
            
            # 启动管理器
            await manager.start()
            assert manager._running is True
            
            # 停止管理器
            await manager.stop()
            assert manager._running is False
    
    @pytest.mark.asyncio
    async def test_get_data_basic(self, manager_config):
        """测试基本数据获取"""
        with patch('app.core.data.manager.redis.from_url', return_value=None):
            manager = UnifiedDataManager(manager_config)
            
            # 模拟路由器返回结果
            expected_result = DataResult(
                request_id="test",
                success=True,
                data={"code": "000001", "name": "平安银行"},
                count=1
            )
            
            manager.router.route_request = AsyncMock(return_value=expected_result)
            manager.plugin_manager.apply_request_middleware = AsyncMock(
                side_effect=lambda x: x
            )
            manager.plugin_manager.apply_response_middleware = AsyncMock(
                side_effect=lambda x, y: x
            )
            manager.event_bus.emit = AsyncMock()
            
            result = await manager.get_data(
                data_type=DataType.STOCK_INFO,
                params={"stock_code": "000001"}
            )
            
            assert result.is_success
            assert result.data == {"code": "000001", "name": "平安银行"}
    
    @pytest.mark.asyncio
    async def test_get_stock_info(self, manager_config):
        """测试获取股票信息"""
        with patch('app.core.data.manager.redis.from_url', return_value=None):
            manager = UnifiedDataManager(manager_config)
            
            expected_result = DataResult(
                request_id="test",
                success=True,
                data={"code": "000001", "name": "平安银行"},
                count=1
            )
            
            manager.get_data = AsyncMock(return_value=expected_result)
            
            result = await manager.get_stock_info("000001")
            
            assert result.is_success
            manager.get_data.assert_called_once_with(
                data_type=DataType.STOCK_INFO,
                params={'stock_code': '000001'}
            )
    
    @pytest.mark.asyncio
    async def test_get_stock_daily(self, manager_config):
        """测试获取股票日线数据"""
        with patch('app.core.data.manager.redis.from_url', return_value=None):
            manager = UnifiedDataManager(manager_config)
            
            expected_result = DataResult(
                request_id="test",
                success=True,
                data=[{"date": "2024-01-01", "close": 10.0}],
                count=1
            )
            
            manager.get_data = AsyncMock(return_value=expected_result)
            
            result = await manager.get_stock_daily(
                stock_code="000001",
                start_date="2024-01-01",
                end_date="2024-01-31"
            )
            
            assert result.is_success
            manager.get_data.assert_called_once_with(
                data_type=DataType.STOCK_DAILY,
                params={
                    'stock_code': '000001',
                    'start_date': '2024-01-01',
                    'end_date': '2024-01-31'
                }
            )
    
    @pytest.mark.asyncio
    async def test_get_stock_realtime(self, manager_config):
        """测试获取股票实时数据"""
        with patch('app.core.data.manager.redis.from_url', return_value=None):
            manager = UnifiedDataManager(manager_config)
            
            expected_result = DataResult(
                request_id="test",
                success=True,
                data=[{"code": "000001", "price": 10.0}],
                count=1
            )
            
            manager.get_data = AsyncMock(return_value=expected_result)
            
            result = await manager.get_stock_realtime(["000001", "000002"])
            
            assert result.is_success
            manager.get_data.assert_called_once_with(
                data_type=DataType.STOCK_REALTIME,
                params={'stock_codes': ['000001', '000002']}
            )
    
    @pytest.mark.asyncio
    async def test_batch_get_data(self, manager_config):
        """测试批量获取数据"""
        with patch('app.core.data.manager.redis.from_url', return_value=None):
            manager = UnifiedDataManager(manager_config)
            
            # 模拟get_data方法
            async def mock_get_data(**kwargs):
                stock_code = kwargs.get('params', {}).get('stock_code', 'unknown')
                return DataResult(
                    request_id="test",
                    success=True,
                    data={"code": stock_code, "name": f"股票{stock_code}"},
                    count=1
                )
            
            manager.get_data = mock_get_data
            
            requests = [
                {
                    "data_type": DataType.STOCK_INFO,
                    "params": {"stock_code": "000001"}
                },
                {
                    "data_type": DataType.STOCK_INFO,
                    "params": {"stock_code": "000002"}
                }
            ]
            
            results = await manager.batch_get_data(requests)
            
            assert len(results) == 2
            assert all(result.is_success for result in results)
    
    @pytest.mark.asyncio
    async def test_process_request_with_middleware(self, manager_config):
        """测试带中间件的请求处理"""
        with patch('app.core.data.manager.redis.from_url', return_value=None):
            manager = UnifiedDataManager(manager_config)
            
            request = DataRequest(
                request_id="test-request",
                data_type=DataType.STOCK_INFO,
                params={"stock_code": "000001"}
            )
            
            # 模拟中间件
            def request_middleware(req):
                req.params["middleware"] = "applied"
                return req
            
            def response_middleware(res, req):
                res.metadata["middleware"] = "applied"
                return res
            
            manager.plugin_manager.apply_request_middleware = AsyncMock(
                side_effect=request_middleware
            )
            manager.plugin_manager.apply_response_middleware = AsyncMock(
                side_effect=response_middleware
            )
            
            # 模拟路由器
            expected_result = DataResult(
                request_id="test-request",
                success=True,
                data={"code": "000001"},
                count=1
            )
            manager.router.route_request = AsyncMock(return_value=expected_result)
            manager.event_bus.emit = AsyncMock()
            
            result = await manager.process_request(request)
            
            assert result.is_success
            # 验证中间件被调用
            manager.plugin_manager.apply_request_middleware.assert_called_once()
            manager.plugin_manager.apply_response_middleware.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_invalidate_cache(self, manager_config):
        """测试缓存失效"""
        with patch('app.core.data.manager.redis.from_url', return_value=None):
            manager = UnifiedDataManager(manager_config)
            
            # 模拟缓存管理器
            manager.router.cache_manager.delete = AsyncMock()
            
            await manager.invalidate_cache(cache_key="test_key")
            
            manager.router.cache_manager.delete.assert_called_once_with("test_key")
    
    @pytest.mark.asyncio
    async def test_health_check(self, manager_config):
        """测试健康检查"""
        with patch('app.core.data.manager.redis.from_url', return_value=None):
            manager = UnifiedDataManager(manager_config)
            manager._running = True
            
            # 模拟组件
            manager.plugin_manager.loaded_plugins = []
            manager.router.source_registry.sources = {"test_source": {}}
            manager.router.source_registry.health_check = AsyncMock(return_value=True)
            
            health = await manager.health_check()
            
            assert health['manager_running'] is True
            assert 'redis_connected' in health
            assert 'plugins_loaded' in health
            assert 'data_sources' in health
            assert 'source_health' in health
    
    def test_get_stats(self, manager_config):
        """测试获取统计信息"""
        with patch('app.core.data.manager.redis.from_url', return_value=None):
            manager = UnifiedDataManager(manager_config)
            
            # 模拟组件统计
            manager.router.get_stats = Mock(return_value={
                'routing_stats': {'total_requests': 100}
            })
            manager.plugin_manager.get_stats = Mock(return_value={
                'loaded_plugins': 5
            })
            
            stats = manager.get_stats()
            
            assert 'manager_stats' in stats
            assert 'router_stats' in stats
            assert 'plugin_stats' in stats
            assert stats['manager_stats']['total_requests'] >= 0


class TestGlobalManagerInstance:
    """全局管理器实例测试"""
    
    def test_get_data_manager_singleton(self):
        """测试数据管理器单例"""
        with patch('app.core.data.manager.UnifiedDataManager') as MockManager:
            # 清除可能存在的实例
            import app.core.data.manager
            app.core.data.manager._manager_instance = None
            
            manager1 = get_data_manager()
            manager2 = get_data_manager()
            
            # 应该返回同一个实例
            assert manager1 is manager2
            # 只应该创建一次
            MockManager.assert_called_once()