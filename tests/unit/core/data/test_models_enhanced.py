"""
统一数据管理系统数据模型的增强单元测试

针对 models.py 中所有数据模型的全面测试，包括：
- 数据验证
- 序列化/反序列化
- 边界条件
- 异常处理
测试覆盖率目标: 100%
"""

import pytest
from datetime import datetime, date, timezone
from typing import Dict, Any, List
from pydantic import ValidationError
import json

from app.core.data.models import (
    DataType, CacheLevel, CacheStrategy, RoutingStrategy,
    DataRequest, DataResult, CacheConfig, RoutingConfig, 
    PluginConfig, DataManagerConfig
)


@pytest.mark.unit
@pytest.mark.fast
class TestEnums:
    """测试枚举类型"""
    
    def test_data_type_enum(self):
        """测试数据类型枚举"""
        assert DataType.STOCK_INFO == "stock_info"
        assert DataType.STOCK_DAILY == "stock_daily"
        assert DataType.STOCK_REALTIME == "stock_realtime"
        assert DataType.STOCK_INDICATORS == "stock_indicators"
        assert DataType.INDEX_COMPONENTS == "index_components"
        assert DataType.FINANCIAL_DATA == "financial_data"
        assert DataType.CUSTOM == "custom"
        
        # 测试枚举完整性
        expected_values = {
            "stock_info", "stock_daily", "stock_realtime", 
            "stock_indicators", "index_components", "financial_data", "custom"
        }
        actual_values = {item.value for item in DataType}
        assert actual_values == expected_values
    
    def test_cache_level_enum(self):
        """测试缓存级别枚举"""
        assert CacheLevel.L1_MEMORY == "l1_memory"
        assert CacheLevel.L2_REDIS == "l2_redis"
        assert CacheLevel.L3_DATABASE == "l3_database"
        assert CacheLevel.L4_API == "l4_api"
        
        # 测试枚举顺序（代表优先级）
        levels = list(CacheLevel)
        assert levels[0] == CacheLevel.L1_MEMORY
        assert levels[-1] == CacheLevel.L4_API
    
    def test_cache_strategy_enum(self):
        """测试缓存策略枚举"""
        strategies = {
            CacheStrategy.CACHE_FIRST: "cache_first",
            CacheStrategy.API_FIRST: "api_first",
            CacheStrategy.CACHE_THROUGH: "cache_through",
            CacheStrategy.CACHE_ASIDE: "cache_aside",
            CacheStrategy.WRITE_BEHIND: "write_behind",
            CacheStrategy.WRITE_THROUGH: "write_through"
        }
        
        for strategy, expected_value in strategies.items():
            assert strategy.value == expected_value
    
    def test_routing_strategy_enum(self):
        """测试路由策略枚举"""
        strategies = {
            RoutingStrategy.FASTEST: "fastest",
            RoutingStrategy.MOST_RELIABLE: "reliable",
            RoutingStrategy.COST_EFFECTIVE: "cost",
            RoutingStrategy.LOAD_BALANCED: "balanced"
        }
        
        for strategy, expected_value in strategies.items():
            assert strategy.value == expected_value


@pytest.mark.unit
@pytest.mark.fast
class TestDataRequest:
    """测试数据请求模型"""
    
    def test_minimal_data_request(self):
        """测试最小数据请求"""
        request = DataRequest(
            request_id="test-123",
            data_type=DataType.STOCK_INFO
        )
        
        assert request.request_id == "test-123"
        assert request.data_type == DataType.STOCK_INFO
        assert request.params == {}
        assert request.cache_strategy == CacheStrategy.CACHE_FIRST
        assert request.routing_strategy == RoutingStrategy.FASTEST
        assert request.cache_ttl == 3600
        assert request.force_refresh is False
        assert request.timeout == 30.0
        assert request.max_retries == 3
        assert request.priority == 0
        assert request.tags == []
        assert isinstance(request.created_at, datetime)
    
    def test_complete_data_request(self):
        """测试完整数据请求"""
        now = datetime.now()
        start_date = date(2024, 1, 1)
        end_date = date(2024, 1, 31)
        
        request = DataRequest(
            request_id="complete-test",
            data_type=DataType.STOCK_DAILY,
            params={"stock_code": "000001", "period": "daily"},
            cache_strategy=CacheStrategy.API_FIRST,
            cache_ttl=1800,
            force_refresh=True,
            routing_strategy=RoutingStrategy.MOST_RELIABLE,
            start_date=start_date,
            end_date=end_date,
            timeout=60.0,
            max_retries=5,
            created_at=now,
            priority=10,
            tags=["urgent", "batch"]
        )
        
        assert request.request_id == "complete-test"
        assert request.data_type == DataType.STOCK_DAILY
        assert request.params == {"stock_code": "000001", "period": "daily"}
        assert request.cache_strategy == CacheStrategy.API_FIRST
        assert request.cache_ttl == 1800
        assert request.force_refresh is True
        assert request.routing_strategy == RoutingStrategy.MOST_RELIABLE
        assert request.start_date == start_date
        assert request.end_date == end_date
        assert request.timeout == 60.0
        assert request.max_retries == 5
        assert request.created_at == now
        assert request.priority == 10
        assert request.tags == ["urgent", "batch"]
    
    def test_date_string_parsing(self):
        """测试日期字符串解析"""
        # ISO格式
        request1 = DataRequest(
            request_id="date-test-1",
            data_type=DataType.STOCK_DAILY,
            start_date="2024-01-01T00:00:00",
            end_date="2024-01-31T23:59:59"
        )
        
        assert isinstance(request1.start_date, datetime)
        assert request1.start_date.year == 2024
        assert request1.start_date.month == 1
        assert request1.start_date.day == 1
        
        # 简单日期格式
        request2 = DataRequest(
            request_id="date-test-2",
            data_type=DataType.STOCK_DAILY,
            start_date="2024-01-01",
            end_date="2024-01-31"
        )
        
        assert isinstance(request2.start_date, datetime)
        assert request2.start_date.year == 2024
        
        # 带时区的ISO格式
        request3 = DataRequest(
            request_id="date-test-3",
            data_type=DataType.STOCK_DAILY,
            start_date="2024-01-01T00:00:00Z"
        )
        
        assert isinstance(request3.start_date, datetime)
    
    def test_invalid_date_string(self):
        """测试无效日期字符串"""
        # 无效格式的日期字符串应该保持原样
        request = DataRequest(
            request_id="invalid-date-test",
            data_type=DataType.STOCK_DAILY,
            start_date="invalid-date"
        )
        
        assert request.start_date == "invalid-date"
    
    def test_cache_key_generation(self):
        """测试缓存键生成"""
        request = DataRequest(
            request_id="cache-key-test",
            data_type=DataType.STOCK_INFO,
            params={"stock_code": "000001", "market": "SZ"},
            start_date=date(2024, 1, 1),
            end_date=date(2024, 1, 31)
        )
        
        cache_key = request.get_cache_key()
        
        assert cache_key.startswith("unified_data:")
        assert "stock_info" in cache_key
        assert "000001" in cache_key
        assert "2024-01-01" in cache_key
        assert "2024-01-31" in cache_key
    
    def test_cache_key_consistency(self):
        """测试缓存键一致性"""
        # 相同参数应生成相同缓存键
        request1 = DataRequest(
            request_id="key-test-1",
            data_type=DataType.STOCK_INFO,
            params={"a": 1, "b": 2}
        )
        
        request2 = DataRequest(
            request_id="key-test-2",  # 不同的request_id
            data_type=DataType.STOCK_INFO,
            params={"b": 2, "a": 1}  # 不同的参数顺序
        )
        
        assert request1.get_cache_key() == request2.get_cache_key()
    
    def test_empty_cache_key(self):
        """测试空参数的缓存键"""
        request = DataRequest(
            request_id="empty-test",
            data_type=DataType.STOCK_INFO
        )
        
        cache_key = request.get_cache_key()
        assert cache_key.startswith("unified_data:")
        assert "stock_info" in cache_key


@pytest.mark.unit
@pytest.mark.fast
class TestDataResult:
    """测试数据结果模型"""
    
    def test_minimal_data_result(self):
        """测试最小数据结果"""
        result = DataResult(
            request_id="test-result",
            success=True
        )
        
        assert result.request_id == "test-result"
        assert result.success is True
        assert result.data is None
        assert result.count == 0
        assert result.cache_hit is False
        assert result.cache_level is None
        assert result.response_time == 0.0
        assert result.data_source == ""
        assert result.error_code is None
        assert result.error_message is None
        assert isinstance(result.timestamp, datetime)
        assert result.metadata == {}
    
    def test_complete_data_result(self):
        """测试完整数据结果"""
        test_data = [{"code": "000001", "name": "测试股票"}]
        metadata = {"source": "test", "version": "1.0"}
        timestamp = datetime.now()
        
        result = DataResult(
            request_id="complete-result",
            success=True,
            data=test_data,
            count=1,
            cache_hit=True,
            cache_level=CacheLevel.L1_MEMORY,
            response_time=0.15,
            data_source="test_provider",
            timestamp=timestamp,
            metadata=metadata
        )
        
        assert result.request_id == "complete-result"
        assert result.success is True
        assert result.data == test_data
        assert result.count == 1
        assert result.cache_hit is True
        assert result.cache_level == CacheLevel.L1_MEMORY
        assert result.response_time == 0.15
        assert result.data_source == "test_provider"
        assert result.timestamp == timestamp
        assert result.metadata == metadata
    
    def test_error_data_result(self):
        """测试错误数据结果"""
        result = DataResult(
            request_id="error-result",
            success=False,
            error_code="VALIDATION_ERROR",
            error_message="参数验证失败",
            response_time=0.05
        )
        
        assert result.request_id == "error-result"
        assert result.success is False
        assert result.error_code == "VALIDATION_ERROR"
        assert result.error_message == "参数验证失败"
        assert result.response_time == 0.05
    
    def test_is_success_property(self):
        """测试成功状态属性"""
        # 成功结果
        success_result = DataResult(
            request_id="success-test",
            success=True
        )
        assert success_result.is_success is True
        
        # 失败结果
        failed_result = DataResult(
            request_id="failed-test",
            success=False,
            error_code="ERROR"
        )
        assert failed_result.is_success is False
        
        # 成功但有错误代码（矛盾状态）
        contradictory_result = DataResult(
            request_id="contradictory-test",
            success=True,
            error_code="WARNING"
        )
        assert contradictory_result.is_success is False
    
    def test_has_data_property(self):
        """测试数据存在属性"""
        # 有数据
        with_data = DataResult(
            request_id="with-data",
            success=True,
            data=[{"test": "data"}],
            count=1
        )
        assert with_data.has_data is True
        
        # 无数据（None）
        no_data = DataResult(
            request_id="no-data",
            success=True,
            data=None,
            count=0
        )
        assert no_data.has_data is False
        
        # 无数据（空列表）
        empty_data = DataResult(
            request_id="empty-data",
            success=True,
            data=[],
            count=0
        )
        assert empty_data.has_data is False
        
        # 有数据但count为0
        inconsistent_data = DataResult(
            request_id="inconsistent",
            success=True,
            data=[{"test": "data"}],
            count=0
        )
        assert inconsistent_data.has_data is False


@pytest.mark.unit
@pytest.mark.fast
class TestConfigModels:
    """测试配置模型"""
    
    def test_cache_config_defaults(self):
        """测试缓存配置默认值"""
        config = CacheConfig()
        
        assert config.enabled is True
        assert config.ttl == 3600
        assert config.max_size == 1000
        assert config.strategy == CacheStrategy.CACHE_FIRST
        assert config.l1_enabled is True
        assert config.l1_max_size == 500
        assert config.l1_ttl == 300
        assert config.l2_enabled is True
        assert config.l2_ttl == 3600
        assert config.l2_key_prefix == "unified_data"
        assert config.l3_enabled is True
        assert config.l3_ttl == 86400
    
    def test_cache_config_custom(self):
        """测试自定义缓存配置"""
        config = CacheConfig(
            enabled=False,
            ttl=1800,
            max_size=500,
            strategy=CacheStrategy.API_FIRST,
            l1_enabled=False,
            l2_ttl=7200,
            l2_key_prefix="custom_prefix",
            l3_ttl=172800
        )
        
        assert config.enabled is False
        assert config.ttl == 1800
        assert config.max_size == 500
        assert config.strategy == CacheStrategy.API_FIRST
        assert config.l1_enabled is False
        assert config.l2_ttl == 7200
        assert config.l2_key_prefix == "custom_prefix"
        assert config.l3_ttl == 172800
    
    def test_routing_config_defaults(self):
        """测试路由配置默认值"""
        config = RoutingConfig()
        
        assert config.strategy == RoutingStrategy.FASTEST
        assert config.timeout == 30.0
        assert config.max_retries == 3
        assert config.retry_delay == 1.0
        assert config.source_priorities == {}
        assert config.load_balance_enabled is False
        assert config.health_check_enabled is True
        assert config.health_check_interval == 60
    
    def test_routing_config_custom(self):
        """测试自定义路由配置"""
        source_priorities = {"provider_a": 10, "provider_b": 5}
        
        config = RoutingConfig(
            strategy=RoutingStrategy.LOAD_BALANCED,
            timeout=60.0,
            max_retries=5,
            retry_delay=2.0,
            source_priorities=source_priorities,
            load_balance_enabled=True,
            health_check_enabled=False,
            health_check_interval=120
        )
        
        assert config.strategy == RoutingStrategy.LOAD_BALANCED
        assert config.timeout == 60.0
        assert config.max_retries == 5
        assert config.retry_delay == 2.0
        assert config.source_priorities == source_priorities
        assert config.load_balance_enabled is True
        assert config.health_check_enabled is False
        assert config.health_check_interval == 120
    
    def test_plugin_config_defaults(self):
        """测试插件配置默认值"""
        config = PluginConfig()
        
        assert config.enabled is True
        assert config.plugin_dirs == []
        assert config.auto_discovery is True
        assert config.plugin_priorities == {}
        assert config.middleware_enabled is True
        assert config.middleware_chain == []
    
    def test_plugin_config_custom(self):
        """测试自定义插件配置"""
        plugin_dirs = ["/path/to/plugins", "/another/path"]
        plugin_priorities = {"plugin_a": 10, "plugin_b": 5}
        middleware_chain = ["auth", "logging", "metrics"]
        
        config = PluginConfig(
            enabled=False,
            plugin_dirs=plugin_dirs,
            auto_discovery=False,
            plugin_priorities=plugin_priorities,
            middleware_enabled=False,
            middleware_chain=middleware_chain
        )
        
        assert config.enabled is False
        assert config.plugin_dirs == plugin_dirs
        assert config.auto_discovery is False
        assert config.plugin_priorities == plugin_priorities
        assert config.middleware_enabled is False
        assert config.middleware_chain == middleware_chain


@pytest.mark.unit
@pytest.mark.fast
class TestDataManagerConfig:
    """测试数据管理器配置"""
    
    def test_data_manager_config_defaults(self):
        """测试数据管理器配置默认值"""
        config = DataManagerConfig()
        
        assert config.enabled is True
        assert config.debug is False
        assert isinstance(config.cache, CacheConfig)
        assert isinstance(config.routing, RoutingConfig)
        assert isinstance(config.plugins, PluginConfig)
        assert config.max_concurrent_requests == 100
        assert config.request_queue_size == 1000
        assert config.metrics_enabled is True
        assert config.logging_enabled is True
    
    def test_data_manager_config_custom(self):
        """测试自定义数据管理器配置"""
        cache_config = CacheConfig(enabled=False)
        routing_config = RoutingConfig(strategy=RoutingStrategy.COST_EFFECTIVE)
        plugin_config = PluginConfig(enabled=False)
        
        config = DataManagerConfig(
            enabled=False,
            debug=True,
            cache=cache_config,
            routing=routing_config,
            plugins=plugin_config,
            max_concurrent_requests=50,
            request_queue_size=500,
            metrics_enabled=False,
            logging_enabled=False
        )
        
        assert config.enabled is False
        assert config.debug is True
        assert config.cache is cache_config
        assert config.routing is routing_config
        assert config.plugins is plugin_config
        assert config.max_concurrent_requests == 50
        assert config.request_queue_size == 500
        assert config.metrics_enabled is False
        assert config.logging_enabled is False
    
    def test_data_manager_config_nested_defaults(self):
        """测试嵌套配置的默认值"""
        config = DataManagerConfig()
        
        # 验证嵌套的缓存配置
        assert config.cache.enabled is True
        assert config.cache.ttl == 3600
        
        # 验证嵌套的路由配置
        assert config.routing.strategy == RoutingStrategy.FASTEST
        assert config.routing.timeout == 30.0
        
        # 验证嵌套的插件配置
        assert config.plugins.enabled is True
        assert config.plugins.auto_discovery is True


@pytest.mark.unit
@pytest.mark.fast
class TestModelSerialization:
    """测试模型序列化"""
    
    def test_data_request_json_serialization(self):
        """测试数据请求JSON序列化"""
        request = DataRequest(
            request_id="serialize-test",
            data_type=DataType.STOCK_INFO,
            params={"stock_code": "000001"},
            cache_strategy=CacheStrategy.CACHE_FIRST,
            routing_strategy=RoutingStrategy.FASTEST
        )
        
        # 序列化为字典
        request_dict = request.dict()
        assert request_dict["request_id"] == "serialize-test"
        assert request_dict["data_type"] == "stock_info"
        assert request_dict["params"] == {"stock_code": "000001"}
        assert request_dict["cache_strategy"] == "cache_first"
        assert request_dict["routing_strategy"] == "fastest"
        
        # 序列化为JSON
        request_json = request.json()
        assert isinstance(request_json, str)
        
        # 反序列化
        request_data = json.loads(request_json)
        reconstructed = DataRequest(**request_data)
        assert reconstructed.request_id == request.request_id
        assert reconstructed.data_type == request.data_type
        assert reconstructed.params == request.params
    
    def test_data_result_json_serialization(self):
        """测试数据结果JSON序列化"""
        result = DataResult(
            request_id="result-serialize-test",
            success=True,
            data=[{"code": "000001", "name": "测试"}],
            count=1,
            cache_hit=True,
            cache_level=CacheLevel.L1_MEMORY,
            response_time=0.123
        )
        
        # 序列化为字典
        result_dict = result.dict()
        assert result_dict["request_id"] == "result-serialize-test"
        assert result_dict["success"] is True
        assert result_dict["data"] == [{"code": "000001", "name": "测试"}]
        assert result_dict["cache_level"] == "l1_memory"
        
        # 序列化为JSON
        result_json = result.json()
        assert isinstance(result_json, str)
        
        # 反序列化
        result_data = json.loads(result_json)
        reconstructed = DataResult(**result_data)
        assert reconstructed.request_id == result.request_id
        assert reconstructed.success == result.success
        assert reconstructed.data == result.data
    
    def test_config_model_serialization(self):
        """测试配置模型序列化"""
        config = DataManagerConfig(
            enabled=True,
            debug=False,
            max_concurrent_requests=50
        )
        
        # 序列化为字典
        config_dict = config.dict()
        assert config_dict["enabled"] is True
        assert config_dict["debug"] is False
        assert config_dict["max_concurrent_requests"] == 50
        assert "cache" in config_dict
        assert "routing" in config_dict
        assert "plugins" in config_dict
        
        # 验证嵌套配置序列化
        cache_dict = config_dict["cache"]
        assert cache_dict["enabled"] is True
        assert cache_dict["ttl"] == 3600


@pytest.mark.unit
@pytest.mark.fast
class TestModelValidation:
    """测试模型验证"""
    
    def test_data_request_validation_errors(self):
        """测试数据请求验证错误"""
        # 缺少必需字段
        with pytest.raises(ValidationError) as exc_info:
            DataRequest()
        
        error = exc_info.value
        assert "request_id" in str(error)
        assert "data_type" in str(error)
        
        # 无效的数据类型
        with pytest.raises(ValidationError):
            DataRequest(
                request_id="test",
                data_type="invalid_type"
            )
        
        # 无效的缓存策略
        with pytest.raises(ValidationError):
            DataRequest(
                request_id="test",
                data_type=DataType.STOCK_INFO,
                cache_strategy="invalid_strategy"
            )
    
    def test_data_result_validation_errors(self):
        """测试数据结果验证错误"""
        # 缺少必需字段
        with pytest.raises(ValidationError) as exc_info:
            DataResult()
        
        error = exc_info.value
        assert "request_id" in str(error)
        assert "success" in str(error)
        
        # 无效的缓存级别
        with pytest.raises(ValidationError):
            DataResult(
                request_id="test",
                success=True,
                cache_level="invalid_level"
            )
    
    def test_config_validation_errors(self):
        """测试配置验证错误"""
        # 无效的策略类型
        with pytest.raises(ValidationError):
            RoutingConfig(strategy="invalid_strategy")
        
        with pytest.raises(ValidationError):
            CacheConfig(strategy="invalid_strategy")
        
        # 负数值验证
        with pytest.raises(ValidationError):
            DataManagerConfig(max_concurrent_requests=-1)
        
        with pytest.raises(ValidationError):
            DataManagerConfig(request_queue_size=0)


@pytest.mark.unit
@pytest.mark.fast
class TestModelEdgeCases:
    """测试模型边界条件"""
    
    def test_extremely_large_values(self):
        """测试极大值"""
        large_value = 2**31 - 1
        
        config = DataManagerConfig(
            max_concurrent_requests=large_value,
            request_queue_size=large_value
        )
        
        assert config.max_concurrent_requests == large_value
        assert config.request_queue_size == large_value
    
    def test_empty_and_none_values(self):
        """测试空值和None值"""
        request = DataRequest(
            request_id="",  # 空字符串
            data_type=DataType.STOCK_INFO,
            params={},
            tags=[]
        )
        
        assert request.request_id == ""
        assert request.params == {}
        assert request.tags == []
    
    def test_unicode_and_special_characters(self):
        """测试Unicode和特殊字符"""
        request = DataRequest(
            request_id="测试-123-🚀",
            data_type=DataType.STOCK_INFO,
            params={"name": "测试股票", "symbol": "特殊符号@#$%"},
            tags=["中文标签", "emoji🎯", "special!@#"]
        )
        
        assert request.request_id == "测试-123-🚀"
        assert request.params["name"] == "测试股票"
        assert "emoji🎯" in request.tags
    
    def test_deep_nested_data(self):
        """测试深层嵌套数据"""
        nested_data = {
            "level1": {
                "level2": {
                    "level3": {
                        "level4": {
                            "value": "deep_value",
                            "list": [1, 2, {"nested": True}]
                        }
                    }
                }
            }
        }
        
        result = DataResult(
            request_id="nested-test",
            success=True,
            data=nested_data,
            count=1
        )
        
        assert result.data["level1"]["level2"]["level3"]["level4"]["value"] == "deep_value"
        assert result.has_data is True


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])