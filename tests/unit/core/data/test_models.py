"""
统一数据管理系统核心模型测试
"""

import pytest
from datetime import datetime, date
from unittest.mock import Mock, patch
from typing import Dict, Any

from app.core.data.models import (
    DataType,
    DataRequest,
    DataResult,
    CacheLevel,
    CacheStrategy,
    RoutingStrategy,
    DataManagerConfig,
    CacheConfig,
    RoutingConfig
)


class TestDataRequest:
    """DataRequest模型测试"""
    
    def test_create_basic_request(self):
        """测试创建基本数据请求"""
        request = DataRequest(
            request_id="test-123",
            data_type=DataType.STOCK_INFO,
            params={"stock_code": "000001"}
        )
        
        assert request.request_id == "test-123"
        assert request.data_type == DataType.STOCK_INFO
        assert request.params == {"stock_code": "000001"}
        assert request.cache_strategy == CacheStrategy.CACHE_FIRST
        assert request.routing_strategy == RoutingStrategy.FASTEST
    
    def test_request_with_dates(self):
        """测试包含日期的请求"""
        request = DataRequest(
            request_id="test-124",
            data_type=DataType.STOCK_DAILY,
            params={"stock_code": "000001"},
            start_date="2024-01-01",
            end_date="2024-12-31"
        )
        
        assert isinstance(request.start_date, datetime)
        assert isinstance(request.end_date, datetime)
    
    def test_generate_cache_key(self):
        """测试缓存键生成"""
        request = DataRequest(
            request_id="test-125",
            data_type=DataType.STOCK_INFO,
            params={"stock_code": "000001"}
        )
        
        cache_key = request.get_cache_key()
        assert cache_key.startswith("unified_data:")
        assert "stock_info" in cache_key
        
        # 相同参数应该生成相同的缓存键
        request2 = DataRequest(
            request_id="test-126",  # 不同的request_id
            data_type=DataType.STOCK_INFO,
            params={"stock_code": "000001"}
        )
        assert request.get_cache_key() == request2.get_cache_key()
    
    def test_date_parsing(self):
        """测试日期解析"""
        # 测试ISO格式
        request = DataRequest(
            request_id="test-127",
            data_type=DataType.STOCK_DAILY,
            start_date="2024-01-01T00:00:00Z"
        )
        assert isinstance(request.start_date, datetime)
        
        # 测试简单日期格式
        request2 = DataRequest(
            request_id="test-128",
            data_type=DataType.STOCK_DAILY,
            start_date="2024-01-01"
        )
        assert isinstance(request2.start_date, datetime)


class TestDataResult:
    """DataResult模型测试"""
    
    def test_successful_result(self):
        """测试成功结果"""
        result = DataResult(
            request_id="test-200",
            success=True,
            data=[{"code": "000001", "name": "平安银行"}],
            count=1,
            cache_hit=True
        )
        
        assert result.is_success
        assert result.has_data
        assert result.cache_hit
        assert result.count > 0
    
    def test_failed_result(self):
        """测试失败结果"""
        result = DataResult(
            request_id="test-201",
            success=False,
            error_code="DATA_NOT_FOUND",
            error_message="股票代码不存在"
        )
        
        assert not result.is_success
        assert not result.has_data
        assert result.error_code == "DATA_NOT_FOUND"
    
    def test_empty_result(self):
        """测试空结果"""
        result = DataResult(
            request_id="test-202",
            success=True,
            data=[],
            count=0
        )
        
        assert result.is_success
        assert not result.has_data


class TestDataManagerConfig:
    """DataManagerConfig测试"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = DataManagerConfig()
        
        assert config.enabled is True
        assert config.debug is False
        assert isinstance(config.cache, CacheConfig)
        assert isinstance(config.routing, RoutingConfig)
    
    def test_custom_config(self):
        """测试自定义配置"""
        cache_config = CacheConfig(
            enabled=True,
            ttl=7200,
            l1_enabled=False
        )
        
        config = DataManagerConfig(
            enabled=True,
            debug=True,
            cache=cache_config
        )
        
        assert config.debug is True
        assert config.cache.ttl == 7200
        assert config.cache.l1_enabled is False


class TestCacheConfig:
    """CacheConfig测试"""
    
    def test_default_cache_config(self):
        """测试默认缓存配置"""
        config = CacheConfig()
        
        assert config.enabled is True
        assert config.ttl == 3600
        assert config.l1_enabled is True
        assert config.l2_enabled is True
        assert config.l3_enabled is True
    
    def test_cache_level_configuration(self):
        """测试缓存级别配置"""
        config = CacheConfig(
            l1_enabled=False,
            l1_max_size=100,
            l2_ttl=7200
        )
        
        assert config.l1_enabled is False
        assert config.l1_max_size == 100
        assert config.l2_ttl == 7200


class TestEnums:
    """枚举类型测试"""
    
    def test_data_type_enum(self):
        """测试数据类型枚举"""
        assert DataType.STOCK_INFO == "stock_info"
        assert DataType.STOCK_DAILY == "stock_daily"
        assert DataType.STOCK_REALTIME == "stock_realtime"
        
        # 测试枚举转换
        assert DataType("stock_info") == DataType.STOCK_INFO
    
    def test_cache_level_enum(self):
        """测试缓存级别枚举"""
        assert CacheLevel.L1_MEMORY == "l1_memory"
        assert CacheLevel.L2_REDIS == "l2_redis"
        assert CacheLevel.L3_DATABASE == "l3_database"
        assert CacheLevel.L4_API == "l4_api"
    
    def test_cache_strategy_enum(self):
        """测试缓存策略枚举"""
        assert CacheStrategy.CACHE_FIRST == "cache_first"
        assert CacheStrategy.API_FIRST == "api_first"
        assert CacheStrategy.CACHE_THROUGH == "cache_through"
    
    def test_routing_strategy_enum(self):
        """测试路由策略枚举"""
        assert RoutingStrategy.FASTEST == "fastest"
        assert RoutingStrategy.MOST_RELIABLE == "reliable"
        assert RoutingStrategy.COST_EFFECTIVE == "cost"