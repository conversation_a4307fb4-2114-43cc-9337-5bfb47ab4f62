"""
技术指标回测功能 - 性能和边界测试
测试系统在极端条件下的表现，包括大数据量、高并发、边界条件等
"""
import pytest
import asyncio
import time
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, AsyncMock
import concurrent.futures
from typing import List, Dict, Any

from app.core.database import db_session
from app.services.scan.memory_scanner import MemoryScanner
from app.services.tasks.executor import get_task_executor
from tests.utils.test_data_factory import TestDataFactory


class TestBacktestPerformance:
    """回测功能性能测试"""

    @pytest.fixture
    async def test_user(self):
        """创建测试用户"""
        async with db_session() as db:
            factory = TestDataFactory(db)
            user = await factory.create_test_user("perf_test_user")
            yield user

    @pytest.fixture
    def large_stock_dataset(self):
        """生成大量股票数据"""
        stocks = []
        for i in range(1000):  # 1000只股票
            stock_code = f"{i:06d}"
            stocks.append(stock_code)
        return stocks

    @pytest.fixture
    def historical_data_range(self):
        """生成长期历史数据"""
        data = []
        base_date = datetime(2020, 1, 1)
        
        for i in range(1000):  # 1000天历史数据
            date = base_date + timedelta(days=i)
            data.append({
                'date': date,
                'open': 10.0 + (i * 0.01),
                'high': 11.0 + (i * 0.01),
                'low': 9.0 + (i * 0.01),
                'close': 10.5 + (i * 0.01),
                'volume': 1000000 + (i * 1000),
                'amount': 10500000.0 + (i * 10000),
                'turnover': 2.5,
                'change_pct': 0.5
            })
        
        return data

    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_large_stock_list_performance(self, large_stock_dataset):
        """测试大股票列表性能"""
        scanner = MemoryScanner()
        
        # 模拟大量股票数据
        mock_data = []
        for _ in range(20):  # 20天数据，足够计算指标
            mock_data.append({
                'date': datetime.now() - timedelta(days=_),
                'open': 10.0,
                'high': 11.0,
                'low': 9.0,
                'close': 10.5,
                'volume': 1000000,
                'amount': 10500000.0
            })
        
        with patch.object(scanner, '_fetch_stock_data', return_value=mock_data):
            start_time = time.time()
            
            # 测试100只股票的扫描性能
            test_stocks = large_stock_dataset[:100]
            results = await scanner.scan_stocks(
                stock_codes=test_stocks,
                indicators=["kdj"],
                end_date="2024-01-15"
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # 性能验证
            assert processing_time < 30.0  # 30秒内完成100只股票扫描
            assert isinstance(results, list)
            
            # 吞吐量验证
            throughput = len(test_stocks) / processing_time
            assert throughput > 3.0  # 每秒至少处理3只股票

    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_long_historical_range_performance(self, historical_data_range):
        """测试长历史时间范围性能"""
        scanner = MemoryScanner()
        
        with patch.object(scanner, '_fetch_stock_data', return_value=historical_data_range):
            start_time = time.time()
            
            # 测试3年历史数据回测
            results = await scanner.scan_stocks(
                stock_codes=["000001"],
                indicators=["kdj", "volume_pressure", "bollinger"],
                end_date="2023-12-31"
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # 性能验证
            assert processing_time < 15.0  # 15秒内完成长时间范围扫描
            assert isinstance(results, list)

    @pytest.mark.slow
    @pytest.mark.asyncio
    async def test_concurrent_scan_performance(self, test_user):
        """测试并发扫描性能"""
        async def create_and_execute_task(task_index):
            """创建并执行单个扫描任务"""
            async with db_session() as db:
                from app.models.task import TaskExecution
                from app.schemas.scheduled_task import TaskStatus
                import json
                
                task_config = {
                    "indicators": ["kdj"],
                    "stock_codes": [f"00000{task_index}"],
                    "end_date": "2024-01-15"
                }
                
                execution = TaskExecution(
                    user_id=test_user.id,
                    scheduled_task_id=None,
                    trigger_type="manual",
                    task_type="indicator_scan",
                    task_config=json.dumps(task_config),
                    status=TaskStatus.PENDING
                )
                db.add(execution)
                await db.commit()
                await db.refresh(execution)
                
                return execution.id
        
        start_time = time.time()
        
        # 创建10个并发任务
        concurrent_tasks = 10
        task_ids = await asyncio.gather(*[
            create_and_execute_task(i) for i in range(concurrent_tasks)
        ])
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # 性能验证
        assert processing_time < 5.0  # 5秒内创建10个任务
        assert len(task_ids) == concurrent_tasks
        assert all(isinstance(tid, int) for tid in task_ids)

    @pytest.mark.asyncio
    async def test_memory_usage_with_large_results(self):
        """测试大结果集的内存使用"""
        scanner = MemoryScanner()
        
        # 模拟大量结果数据
        large_results = []
        for i in range(1000):  # 1000条结果
            large_results.append({
                'stock_code': f'{i:06d}',
                'stock_name': f'测试股票{i}',
                'signals': ['KDJ金叉', '成交量突破'],
                'indicator_data': {
                    'kdj_k': 50.0 + i * 0.1,
                    'kdj_d': 45.0 + i * 0.1,
                    'volume_pressure': 1.0 + i * 0.001,
                    'close_price': 10.0 + i * 0.01
                },
                'price': 10.0 + i * 0.01,
                'change_percent': 0.5,
                'scan_time': datetime.now(),
                'end_date': '2024-01-15'
            })
        
        # 测试结果处理性能
        start_time = time.time()
        
        # 模拟结果序列化和处理
        import json
        serialized = json.dumps(large_results, default=str)
        deserialized = json.loads(serialized)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # 性能验证
        assert processing_time < 2.0  # 2秒内完成1000条结果处理
        assert len(deserialized) == 1000

    def test_edge_case_empty_data(self):
        """测试边界条件 - 空数据集"""
        scanner = MemoryScanner()
        
        with patch.object(scanner, '_fetch_stock_data', return_value=[]):
            # 测试空数据处理
            try:
                result = scanner._calculate_indicators([], ["kdj"])
                # 应该优雅处理空数据
                assert result is None or isinstance(result, dict)
            except Exception as e:
                # 如果抛出异常，应该是明确的错误类型
                assert isinstance(e, (ValueError, IndexError))

    def test_edge_case_single_data_point(self):
        """测试边界条件 - 单个数据点"""
        scanner = MemoryScanner()
        
        single_data = [{
            'date': datetime(2024, 1, 15),
            'open': 10.0,
            'high': 11.0,
            'low': 9.0,
            'close': 10.5,
            'volume': 1000000,
            'amount': 10500000.0
        }]
        
        with patch.object(scanner, '_fetch_stock_data', return_value=single_data):
            # 测试单点数据处理
            try:
                result = scanner._calculate_indicators(single_data, ["kdj"])
                # 单点数据可能无法计算某些指标
                assert result is None or isinstance(result, dict)
            except Exception as e:
                assert isinstance(e, (ValueError, IndexError))

    def test_edge_case_invalid_dates(self):
        """测试边界条件 - 无效日期"""
        from app.utils.trading_utils import format_trading_date
        
        invalid_dates = [
            None,
            "",
            "invalid-date",
            "2024-13-01",  # 无效月份
            "2024-02-30",  # 无效日期
        ]
        
        for invalid_date in invalid_dates:
            with pytest.raises((ValueError, TypeError, AttributeError)):
                if invalid_date:
                    datetime.strptime(invalid_date, "%Y-%m-%d")
                else:
                    format_trading_date(invalid_date)

    @pytest.mark.asyncio
    async def test_database_connection_limits(self, test_user):
        """测试数据库连接限制"""
        async def create_db_session():
            """创建数据库会话并执行简单查询"""
            async with db_session() as db:
                from app.models.user import User
                from sqlalchemy import select
                
                stmt = select(User).where(User.id == test_user.id)
                result = await db.execute(stmt)
                user = result.scalar_one_or_none()
                return user is not None
        
        # 测试多个并发数据库连接
        concurrent_connections = 20
        
        start_time = time.time()
        
        results = await asyncio.gather(*[
            create_db_session() for _ in range(concurrent_connections)
        ], return_exceptions=True)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # 验证连接池处理
        successful_connections = sum(1 for r in results if r is True)
        errors = [r for r in results if isinstance(r, Exception)]
        
        # 应该有大部分连接成功
        assert successful_connections >= concurrent_connections * 0.8
        
        # 连接处理时间应该合理
        assert processing_time < 10.0

    def test_extreme_parameter_values(self):
        """测试极端参数值"""
        from app.schemas.scan import ScanStartRequest
        
        # 测试极小参数值
        with pytest.raises(ValueError):
            ScanStartRequest(
                indicators=["kdj"],
                stock_codes=["000001"],
                scan_mode="traditional",
                periods=["d"],
                adjust="n",
                parameters={
                    "kdj": {"n": 1, "m1": 0, "m2": 0}  # 极小值
                }
            )
        
        # 测试极大参数值
        with pytest.raises(ValueError):
            ScanStartRequest(
                indicators=["kdj"],
                stock_codes=["000001"],
                scan_mode="traditional",
                periods=["d"],
                adjust="n",
                parameters={
                    "kdj": {"n": 1000, "m1": 1000, "m2": 1000}  # 极大值
                }
            )

    @pytest.mark.slow
    def test_cpu_intensive_calculations(self):
        """测试CPU密集型计算性能"""
        from app.services.indicators.indicator_service import IndicatorService
        
        service = IndicatorService()
        
        # 生成大量测试数据
        large_dataset = []
        for i in range(1000):
            large_dataset.append({
                'date': datetime(2024, 1, 1) + timedelta(days=i),
                'open': 10.0 + (i * 0.01),
                'high': 11.0 + (i * 0.01),
                'low': 9.0 + (i * 0.01),
                'close': 10.5 + (i * 0.01),
                'volume': 1000000 + (i * 1000)
            })
        
        start_time = time.time()
        
        with patch.object(service.storage, 'get_stock_daily_data', return_value=large_dataset):
            # 计算多个指标
            indicators = ['kdj', 'volume_pressure', 'bollinger', 'macd', 'rsi']
            for indicator in indicators:
                if hasattr(service, f'calculate_{indicator}'):
                    getattr(service, f'calculate_{indicator}')("000001", period="d")
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # CPU密集型计算应该在合理时间内完成
        assert processing_time < 20.0

    @pytest.mark.asyncio
    async def test_network_timeout_simulation(self, test_user):
        """测试网络超时模拟"""
        from app.services.data_fetcher.adapter import DataFetcherAdapter
        
        adapter = DataFetcherAdapter()
        
        # 模拟网络超时
        async def slow_fetch(*args, **kwargs):
            await asyncio.sleep(10)  # 模拟10秒延迟
            return []
        
        with patch.object(adapter, 'get_stock_daily_data', side_effect=slow_fetch):
            start_time = time.time()
            
            try:
                # 设置5秒超时
                result = await asyncio.wait_for(
                    adapter.get_stock_daily_data("000001"),
                    timeout=5.0
                )
            except asyncio.TimeoutError:
                # 预期的超时错误
                pass
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # 应该在超时时间内结束
            assert processing_time < 6.0

    def test_memory_leak_prevention(self):
        """测试内存泄漏防护"""
        import gc
        import sys
        
        initial_objects = len(gc.get_objects())
        
        # 执行大量操作
        for i in range(100):
            scanner = MemoryScanner()
            mock_data = [{
                'date': datetime.now(),
                'open': 10.0,
                'high': 11.0,
                'low': 9.0,
                'close': 10.5,
                'volume': 1000000
            }]
            
            with patch.object(scanner, '_fetch_stock_data', return_value=mock_data):
                # 模拟计算操作
                scanner._calculate_indicators(mock_data, ["kdj"])
            
            # 显式删除对象
            del scanner
        
        # 强制垃圾回收
        gc.collect()
        
        final_objects = len(gc.get_objects())
        
        # 对象数量不应该显著增加（允许一定容差）
        object_increase = final_objects - initial_objects
        assert object_increase < 1000  # 允许增加不超过1000个对象

    @pytest.mark.asyncio
    async def test_api_rate_limiting(self):
        """测试API限流"""
        from httpx import AsyncClient
        from app.main import app
        
        async with AsyncClient(app=app, base_url="http://test") as ac:
            # 快速发送多个请求
            requests = []
            for _ in range(20):
                requests.append(ac.get("/api/v1/trading/latest-trading-date"))
            
            start_time = time.time()
            responses = await asyncio.gather(*requests, return_exceptions=True)
            end_time = time.time()
            
            processing_time = end_time - start_time
            
            # 验证响应
            successful_responses = sum(
                1 for r in responses 
                if hasattr(r, 'status_code') and r.status_code == 200
            )
            
            # 大部分请求应该成功
            assert successful_responses >= 15
            
            # 处理时间应该合理
            assert processing_time < 10.0

    def test_data_consistency_under_load(self):
        """测试负载下的数据一致性"""
        scanner = MemoryScanner()
        
        # 准备一致的测试数据
        consistent_data = []
        for i in range(50):
            consistent_data.append({
                'date': datetime(2024, 1, 1) + timedelta(days=i),
                'open': 10.0,
                'high': 10.0,
                'low': 10.0,
                'close': 10.0,
                'volume': 1000000
            })
        
        with patch.object(scanner, '_fetch_stock_data', return_value=consistent_data):
            # 多次计算相同数据
            results = []
            for _ in range(10):
                result = scanner._calculate_indicators(consistent_data, ["kdj"])
                if result:
                    results.append(result)
            
            # 验证结果一致性
            if results:
                first_result = results[0]
                for result in results[1:]:
                    # 所有结果应该相同
                    assert result == first_result