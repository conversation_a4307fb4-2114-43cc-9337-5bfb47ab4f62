"""
性能测试配置和公共fixtures
"""
import pytest
import asyncio
import time
import aiohttp
import redis.asyncio as redis
from typing import AsyncGenerator, Dict, Any, List
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch

from app.core.config import settings
from app.core.data.manager import UnifiedDataManager
from app.core.data.models import DataManagerConfig, CacheConfig, RoutingConfig


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环用于整个测试会话"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
async def redis_client():
    """创建Redis测试客户端"""
    client = redis.from_url("redis://localhost:6379/15")  # 使用测试数据库
    try:
        await client.ping()
        yield client
    except redis.ConnectionError:
        # 如果Redis不可用，使用模拟客户端
        yield AsyncMock()
    finally:
        if hasattr(client, 'close'):
            await client.close()


@pytest.fixture
async def performance_data_manager(redis_client):
    """创建用于性能测试的数据管理器"""
    config = DataManagerConfig(
        cache=CacheConfig(
            enabled=True,
            default_ttl=3600,
            max_memory_mb=100,
            compression_enabled=True
        ),
        routing=RoutingConfig(
            max_retries=3,
            timeout_seconds=30,
            circuit_breaker_enabled=True
        ),
        request_queue_size=2000,
        worker_count=10
    )
    
    manager = UnifiedDataManager(config)
    await manager.start()
    
    yield manager
    
    await manager.shutdown()


@pytest.fixture
def stock_codes_large() -> List[str]:
    """生成大量股票代码用于压力测试"""
    return [f"{i:06d}" for i in range(1, 1001)]  # 生成1000个股票代码


@pytest.fixture
def performance_metrics():
    """性能指标收集器"""
    return {
        'response_times': [],
        'cache_hits': 0,
        'cache_misses': 0,
        'errors': 0,
        'concurrent_requests': 0,
        'start_time': None,
        'end_time': None
    }


class PerformanceTimer:
    """性能计时器"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
    
    def start(self):
        self.start_time = time.perf_counter()
    
    def stop(self):
        self.end_time = time.perf_counter()
        return self.elapsed
    
    @property
    def elapsed(self) -> float:
        if self.start_time is None:
            return 0.0
        end = self.end_time or time.perf_counter()
        return end - self.start_time


@pytest.fixture
def perf_timer():
    """性能计时器fixture"""
    return PerformanceTimer()


@pytest.fixture
async def mock_fast_data_source():
    """模拟快速数据源"""
    async def fast_fetch(request):
        await asyncio.sleep(0.001)  # 1ms延迟
        return {
            'code': request.get('code', '000001'),
            'price': 10.5,
            'volume': 1000000,
            'timestamp': datetime.now().isoformat()
        }
    return fast_fetch


@pytest.fixture
async def mock_slow_data_source():
    """模拟慢速数据源"""
    async def slow_fetch(request):
        await asyncio.sleep(0.1)  # 100ms延迟
        return {
            'code': request.get('code', '000001'),
            'price': 10.5,
            'volume': 1000000,
            'timestamp': datetime.now().isoformat()
        }
    return slow_fetch


class CacheHitRateMonitor:
    """缓存命中率监控器"""
    
    def __init__(self):
        self.hits = 0
        self.misses = 0
    
    def record_hit(self):
        self.hits += 1
    
    def record_miss(self):
        self.misses += 1
    
    @property
    def hit_rate(self) -> float:
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0
    
    @property
    def total_requests(self) -> int:
        return self.hits + self.misses


@pytest.fixture
def cache_monitor():
    """缓存监控器fixture"""
    return CacheHitRateMonitor()


class ConcurrencyTestHelper:
    """并发测试辅助类"""
    
    def __init__(self, max_concurrent: int = 1000):
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.active_requests = 0
        self.max_active = 0
        self._lock = asyncio.Lock()
    
    async def execute_concurrent(self, coro_func, *args, **kwargs):
        """执行并发任务"""
        async with self.semaphore:
            async with self._lock:
                self.active_requests += 1
                self.max_active = max(self.max_active, self.active_requests)
            
            try:
                result = await coro_func(*args, **kwargs)
                return result
            finally:
                async with self._lock:
                    self.active_requests -= 1


@pytest.fixture
def concurrency_helper():
    """并发测试辅助器fixture"""
    return ConcurrencyTestHelper()


# 性能测试标记
def performance_test(min_throughput=None, max_response_time=None, min_cache_hit_rate=None):
    """性能测试装饰器"""
    def decorator(func):
        func = pytest.mark.performance(func)
        func._min_throughput = min_throughput
        func._max_response_time = max_response_time
        func._min_cache_hit_rate = min_cache_hit_rate
        return func
    return decorator


# 压力测试标记
def stress_test(duration_seconds=60, concurrent_users=100):
    """压力测试装饰器"""
    def decorator(func):
        func = pytest.mark.stress(func)
        func._duration_seconds = duration_seconds
        func._concurrent_users = concurrent_users
        return func
    return decorator


# 负载测试标记
def load_test(ramp_up_time=30, steady_time=60, ramp_down_time=30):
    """负载测试装饰器"""
    def decorator(func):
        func = pytest.mark.load(func)
        func._ramp_up_time = ramp_up_time
        func._steady_time = steady_time
        func._ramp_down_time = ramp_down_time
        return func
    return decorator