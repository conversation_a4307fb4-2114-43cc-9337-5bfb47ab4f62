"""
并发性能测试

验证系统在高并发情况下的性能表现，目标支持1000+并发
"""
import pytest
import asyncio
import statistics
import time
from typing import List, Dict, Any, Coroutine
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor

from tests.performance.conftest import (
    stress_test,
    load_test,
    ConcurrencyTestHelper,
    PerformanceTimer
)


class TestConcurrentPerformance:
    """并发性能测试"""
    
    @stress_test(concurrent_users=1000, duration_seconds=60)
    async def test_high_concurrency_stock_list(
        self,
        performance_data_manager,
        concurrency_helper: ConcurrencyTestHelper
    ):
        """测试1000并发获取股票列表"""
        concurrent_requests = 1000
        results = []
        errors = []
        response_times = []
        
        async def single_request():
            start_time = time.perf_counter()
            try:
                result = await concurrency_helper.execute_concurrent(
                    performance_data_manager.get_stock_list
                )
                end_time = time.perf_counter()
                response_times.append(end_time - start_time)
                return result
            except Exception as e:
                errors.append(str(e))
                return None
        
        # 执行并发请求
        start_time = time.perf_counter()
        tasks = [single_request() for _ in range(concurrent_requests)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.perf_counter() - start_time
        
        # 性能分析
        success_count = len([r for r in results if r is not None])
        error_rate = len(errors) / concurrent_requests
        throughput = success_count / total_time
        
        # 性能断言
        assert error_rate < 0.01, f"错误率 {error_rate*100:.1f}% 超过1%"
        assert throughput >= 500, f"吞吐量 {throughput:.1f} req/s 低于500 req/s"
        assert concurrency_helper.max_active >= 800, f"最大并发数 {concurrency_helper.max_active} 低于预期"
        
        if response_times:
            avg_response_time = statistics.mean(response_times)
            p95_response_time = statistics.quantiles(response_times, n=20)[18]
            
            assert avg_response_time < 0.1, f"高并发下平均响应时间 {avg_response_time:.3f}s 过高"
            assert p95_response_time < 0.2, f"高并发下P95响应时间 {p95_response_time:.3f}s 过高"
        
        print(f"\\n1000并发股票列表性能指标:")
        print(f"总耗时: {total_time:.2f}s")
        print(f"成功请求: {success_count}/{concurrent_requests}")
        print(f"错误率: {error_rate*100:.2f}%")
        print(f"吞吐量: {throughput:.1f} req/s")
        print(f"最大并发: {concurrency_helper.max_active}")
        if response_times:
            print(f"平均响应时间: {statistics.mean(response_times)*1000:.1f}ms")
            print(f"P95响应时间: {statistics.quantiles(response_times, n=20)[18]*1000:.1f}ms")
    
    @stress_test(concurrent_users=500, duration_seconds=30)
    async def test_concurrent_real_time_data(
        self,
        performance_data_manager,
        concurrency_helper: ConcurrencyTestHelper
    ):
        """测试并发实时数据获取"""
        stock_codes = [f"{i:06d}" for i in range(1, 51)]  # 50个股票
        concurrent_requests = 500
        results = []
        response_times = []
        
        async def get_real_time_data(code: str):
            start_time = time.perf_counter()
            try:
                result = await concurrency_helper.execute_concurrent(
                    performance_data_manager.get_real_time_data,
                    code=code
                )
                end_time = time.perf_counter()
                response_times.append(end_time - start_time)
                return result
            except Exception as e:
                return f"Error: {str(e)}"
        
        # 构建请求任务
        tasks = []
        for _ in range(concurrent_requests):
            code = stock_codes[len(tasks) % len(stock_codes)]
            tasks.append(get_real_time_data(code))
        
        # 执行并发请求
        start_time = time.perf_counter()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.perf_counter() - start_time
        
        # 分析结果
        success_count = len([r for r in results if not str(r).startswith("Error")])
        error_rate = (concurrent_requests - success_count) / concurrent_requests
        throughput = success_count / total_time
        
        # 性能断言
        assert error_rate < 0.02, f"实时数据并发错误率 {error_rate*100:.1f}% 超过2%"
        assert throughput >= 200, f"实时数据吞吐量 {throughput:.1f} req/s 低于200 req/s"
        
        if response_times:
            avg_response_time = statistics.mean(response_times)
            assert avg_response_time < 0.05, f"实时数据并发平均响应时间 {avg_response_time:.3f}s 超过50ms"
        
        print(f"\\n500并发实时数据性能指标:")
        print(f"成功率: {success_count/concurrent_requests*100:.1f}%")
        print(f"吞吐量: {throughput:.1f} req/s")
        if response_times:
            print(f"平均响应时间: {statistics.mean(response_times)*1000:.1f}ms")
    
    @load_test(ramp_up_time=30, steady_time=60, ramp_down_time=30)
    async def test_load_testing_gradual_increase(
        self,
        performance_data_manager,
        concurrency_helper: ConcurrencyTestHelper
    ):
        """负载测试：逐步增加并发用户数"""
        max_concurrent = 800
        ramp_up_time = 30  # 30秒逐步增加到最大并发
        steady_time = 60   # 60秒稳定负载
        
        response_times = []
        throughput_samples = []
        active_tasks = []
        
        async def continuous_requests():
            """持续发送请求"""
            local_response_times = []
            request_count = 0
            
            start_time = time.perf_counter()
            while time.perf_counter() - start_time < steady_time:
                try:
                    req_start = time.perf_counter()
                    await concurrency_helper.execute_concurrent(
                        performance_data_manager.get_stock_list
                    )
                    req_end = time.perf_counter()
                    
                    local_response_times.append(req_end - req_start)
                    request_count += 1
                    
                    # 短暂休息避免过度消耗资源
                    await asyncio.sleep(0.01)
                    
                except Exception:
                    pass
            
            return local_response_times, request_count
        
        # 阶段1：逐步增加负载
        current_concurrent = 0
        ramp_step = max_concurrent / (ramp_up_time * 10)  # 每100ms增加的并发数
        
        print(f"\\n开始负载测试 - 目标并发: {max_concurrent}")
        
        for step in range(ramp_up_time * 10):  # 每100ms一步
            if current_concurrent < max_concurrent:
                new_tasks_count = int(ramp_step)
                for _ in range(new_tasks_count):
                    if len(active_tasks) < max_concurrent:
                        task = asyncio.create_task(continuous_requests())
                        active_tasks.append(task)
                        current_concurrent += 1
            
            await asyncio.sleep(0.1)
        
        print(f"逐步增加阶段完成，当前并发: {len(active_tasks)}")
        
        # 阶段2：稳定负载测试
        steady_start = time.perf_counter()
        await asyncio.sleep(steady_time)
        steady_end = time.perf_counter()
        
        # 收集结果
        results = await asyncio.gather(*active_tasks, return_exceptions=True)
        
        # 分析性能数据
        total_requests = 0
        all_response_times = []
        
        for result in results:
            if isinstance(result, tuple):
                times, count = result
                all_response_times.extend(times)
                total_requests += count
        
        if all_response_times:
            avg_response_time = statistics.mean(all_response_times)
            p95_response_time = statistics.quantiles(all_response_times, n=20)[18]
            throughput = total_requests / steady_time
            
            # 性能断言
            assert avg_response_time < 0.1, f"负载测试平均响应时间 {avg_response_time:.3f}s 过高"
            assert p95_response_time < 0.2, f"负载测试P95响应时间 {p95_response_time:.3f}s 过高"
            assert throughput >= 100, f"负载测试吞吐量 {throughput:.1f} req/s 过低"
            
            print(f"\\n负载测试结果:")
            print(f"稳定阶段并发数: {len(active_tasks)}")
            print(f"总请求数: {total_requests}")
            print(f"稳定阶段时长: {steady_time}s")
            print(f"平均吞吐量: {throughput:.1f} req/s")
            print(f"平均响应时间: {avg_response_time*1000:.1f}ms")
            print(f"P95响应时间: {p95_response_time*1000:.1f}ms")


class TestMemoryAndResourceUsage:
    """内存和资源使用测试"""
    
    @stress_test(concurrent_users=1000, duration_seconds=120)
    async def test_memory_usage_under_load(
        self,
        performance_data_manager
    ):
        """测试高负载下的内存使用情况"""
        import psutil
        import gc
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        async def memory_intensive_task():
            """内存密集型任务"""
            for _ in range(100):
                # 获取数据
                await performance_data_manager.get_stock_list()
                await performance_data_manager.get_stock_daily_data(
                    code="000001",
                    start_date=datetime.now() - timedelta(days=30),
                    end_date=datetime.now()
                )
                
                # 短暂休息
                await asyncio.sleep(0.001)
        
        # 执行高并发内存密集型任务
        tasks = [memory_intensive_task() for _ in range(100)]
        
        start_time = time.perf_counter()
        await asyncio.gather(*tasks)
        end_time = time.perf_counter()
        
        # 强制垃圾收集
        gc.collect()
        await asyncio.sleep(1)  # 等待异步清理完成
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_growth = final_memory - initial_memory
        
        # 内存使用断言
        assert memory_growth < 200, f"内存增长 {memory_growth:.1f}MB 超过200MB限制"
        
        print(f"\\n内存使用测试结果:")
        print(f"初始内存: {initial_memory:.1f}MB")
        print(f"最终内存: {final_memory:.1f}MB")
        print(f"内存增长: {memory_growth:.1f}MB")
        print(f"执行时间: {end_time - start_time:.2f}s")
    
    @pytest.mark.asyncio
    async def test_connection_pool_efficiency(
        self,
        performance_data_manager
    ):
        """测试连接池效率"""
        connection_count_samples = []
        
        async def monitor_connections():
            """监控连接数"""
            for _ in range(60):  # 监控60秒
                # 这里需要根据实际的连接池实现来获取连接数
                # 示例中假设可以从数据管理器获取连接统计
                if hasattr(performance_data_manager, 'get_connection_stats'):
                    stats = await performance_data_manager.get_connection_stats()
                    connection_count_samples.append(stats.get('active_connections', 0))
                
                await asyncio.sleep(1)
        
        async def generate_load():
            """生成负载"""
            tasks = []
            for _ in range(500):  # 500个并发任务
                task = asyncio.create_task(
                    performance_data_manager.get_real_time_data("000001")
                )
                tasks.append(task)
                
                if len(tasks) >= 50:  # 每50个任务批次执行
                    await asyncio.gather(*tasks, return_exceptions=True)
                    tasks = []
                    await asyncio.sleep(0.1)
        
        # 同时运行监控和负载生成
        await asyncio.gather(
            monitor_connections(),
            generate_load(),
            return_exceptions=True
        )
        
        if connection_count_samples:
            max_connections = max(connection_count_samples)
            avg_connections = statistics.mean(connection_count_samples)
            
            assert max_connections <= 100, f"最大连接数 {max_connections} 超过100"
            
            print(f"\\n连接池效率测试:")
            print(f"最大连接数: {max_connections}")
            print(f"平均连接数: {avg_connections:.1f}")


class TestThroughputBenchmark:
    """吞吐量基准测试"""
    
    @pytest.mark.asyncio
    async def test_throughput_benchmark_suite(
        self,
        performance_data_manager
    ):
        """综合吞吐量基准测试"""
        benchmarks = {}
        
        # 测试不同类型API的吞吐量
        test_scenarios = [
            ("stock_list", lambda: performance_data_manager.get_stock_list(), 1000),
            ("real_time", lambda: performance_data_manager.get_real_time_data("000001"), 800),
            ("daily_data", lambda: performance_data_manager.get_stock_daily_data(
                "000001", datetime.now() - timedelta(days=10), datetime.now()
            ), 500),
        ]
        
        for scenario_name, task_func, min_throughput in test_scenarios:
            response_times = []
            error_count = 0
            
            start_time = time.perf_counter()
            
            # 执行固定数量的请求
            request_count = 200
            for _ in range(request_count):
                try:
                    req_start = time.perf_counter()
                    await task_func()
                    req_end = time.perf_counter()
                    response_times.append(req_end - req_start)
                except Exception:
                    error_count += 1
            
            total_time = time.perf_counter() - start_time
            success_count = request_count - error_count
            throughput = success_count / total_time
            
            benchmarks[scenario_name] = {
                'throughput': throughput,
                'avg_response_time': statistics.mean(response_times) if response_times else 0,
                'error_rate': error_count / request_count,
                'success_count': success_count
            }
            
            # 吞吐量断言
            assert throughput >= min_throughput * 0.8, (
                f"{scenario_name} 吞吐量 {throughput:.1f} req/s "
                f"低于最低要求 {min_throughput * 0.8:.1f} req/s"
            )
        
        # 输出基准测试结果
        print(f"\\n吞吐量基准测试结果:")
        for scenario, metrics in benchmarks.items():
            print(f"{scenario}:")
            print(f"  吞吐量: {metrics['throughput']:.1f} req/s")
            print(f"  平均响应时间: {metrics['avg_response_time']*1000:.1f}ms")
            print(f"  错误率: {metrics['error_rate']*100:.2f}%")
            print(f"  成功请求数: {metrics['success_count']}")