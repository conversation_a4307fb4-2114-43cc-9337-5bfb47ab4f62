"""
缓存性能测试

验证多级缓存系统的性能，目标缓存命中率 > 80%
"""
import pytest
import asyncio
import statistics
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import redis.asyncio as redis

from tests.performance.conftest import (
    performance_test,
    CacheHitRateMonitor,
    PerformanceTimer
)


class TestCachePerformance:
    """缓存性能测试"""
    
    @performance_test(min_cache_hit_rate=0.8)
    async def test_cache_hit_rate_stock_list(
        self,
        performance_data_manager,
        cache_monitor: CacheHitRateMonitor
    ):
        """测试股票列表缓存命中率 > 80%"""
        # 预热缓存
        await performance_data_manager.get_stock_list()
        
        # 执行大量重复请求
        request_count = 1000
        for _ in range(request_count):
            result = await performance_data_manager.get_stock_list()
            
            # 检查是否来自缓存
            if hasattr(result, 'metadata') and result.metadata.get('from_cache'):
                cache_monitor.record_hit()
            else:
                cache_monitor.record_miss()
        
        hit_rate = cache_monitor.hit_rate
        assert hit_rate >= 0.8, f"股票列表缓存命中率 {hit_rate:.2%} 低于80%"
        
        print(f"\\n股票列表缓存性能:")
        print(f"总请求数: {cache_monitor.total_requests}")
        print(f"命中次数: {cache_monitor.hits}")
        print(f"未命中次数: {cache_monitor.misses}")
        print(f"命中率: {hit_rate:.2%}")
    
    @performance_test(min_cache_hit_rate=0.75)
    async def test_cache_hit_rate_daily_data(
        self,
        performance_data_manager,
        cache_monitor: CacheHitRateMonitor
    ):
        """测试日线数据缓存命中率 > 75%"""
        stock_codes = ["000001", "000002", "600000", "600036"]
        date_ranges = [
            (datetime.now() - timedelta(days=30), datetime.now()),
            (datetime.now() - timedelta(days=60), datetime.now() - timedelta(days=30)),
            (datetime.now() - timedelta(days=90), datetime.now() - timedelta(days=60)),
        ]
        
        # 预热缓存
        for code in stock_codes:
            for start_date, end_date in date_ranges:
                await performance_data_manager.get_stock_daily_data(
                    code=code,
                    start_date=start_date,
                    end_date=end_date
                )
        
        # 测试缓存命中
        for _ in range(200):
            code = stock_codes[_ % len(stock_codes)]
            start_date, end_date = date_ranges[_ % len(date_ranges)]
            
            result = await performance_data_manager.get_stock_daily_data(
                code=code,
                start_date=start_date,
                end_date=end_date
            )
            
            if hasattr(result, 'metadata') and result.metadata.get('from_cache'):
                cache_monitor.record_hit()
            else:
                cache_monitor.record_miss()
        
        hit_rate = cache_monitor.hit_rate
        assert hit_rate >= 0.75, f"日线数据缓存命中率 {hit_rate:.2%} 低于75%"
        
        print(f"\\n日线数据缓存性能:")
        print(f"命中率: {hit_rate:.2%}")
        print(f"命中次数: {cache_monitor.hits}")
        print(f"未命中次数: {cache_monitor.misses}")
    
    @pytest.mark.asyncio
    async def test_cache_response_time_improvement(
        self,
        performance_data_manager,
        redis_client: redis.Redis,
        perf_timer: PerformanceTimer
    ):
        """测试缓存对响应时间的改善效果"""
        stock_code = "000001"
        
        # 清理缓存，测试冷请求
        await redis_client.flushdb()
        
        cold_response_times = []
        for _ in range(10):
            perf_timer.start()
            await performance_data_manager.get_stock_daily_data(
                code=stock_code,
                start_date=datetime.now() - timedelta(days=30),
                end_date=datetime.now()
            )
            cold_time = perf_timer.stop()
            cold_response_times.append(cold_time)
        
        # 测试热请求（缓存命中）
        hot_response_times = []
        for _ in range(100):
            perf_timer.start()
            await performance_data_manager.get_stock_daily_data(
                code=stock_code,
                start_date=datetime.now() - timedelta(days=30),
                end_date=datetime.now()
            )
            hot_time = perf_timer.stop()
            hot_response_times.append(hot_time)
        
        avg_cold_time = statistics.mean(cold_response_times)
        avg_hot_time = statistics.mean(hot_response_times)
        improvement_ratio = avg_cold_time / avg_hot_time if avg_hot_time > 0 else 1
        
        # 缓存应该显著提升性能
        assert improvement_ratio >= 3, f"缓存性能提升比例 {improvement_ratio:.1f}x 低于3x"
        assert avg_hot_time < 0.01, f"缓存命中响应时间 {avg_hot_time:.3f}s 超过10ms"
        
        print(f"\\n缓存性能提升分析:")
        print(f"冷请求平均时间: {avg_cold_time*1000:.1f}ms")
        print(f"热请求平均时间: {avg_hot_time*1000:.1f}ms")
        print(f"性能提升比例: {improvement_ratio:.1f}x")
    
    @pytest.mark.asyncio
    async def test_multi_level_cache_efficiency(
        self,
        performance_data_manager,
        redis_client: redis.Redis
    ):
        """测试多级缓存效率"""
        test_data = {
            'l1_cache': [],  # 内存缓存
            'l2_cache': [],  # Redis缓存
            'database': []   # 数据库查询
        }
        
        stock_codes = [f"{i:06d}" for i in range(1, 21)]  # 20个股票
        
        # 第一轮：数据库查询（冷启动）
        await redis_client.flushdb()
        for code in stock_codes:
            start_time = time.perf_counter()
            await performance_data_manager.get_real_time_data(code=code)
            response_time = time.perf_counter() - start_time
            test_data['database'].append(response_time)
        
        # 第二轮：Redis缓存命中
        # 清理内存缓存但保留Redis缓存
        if hasattr(performance_data_manager, 'clear_memory_cache'):
            await performance_data_manager.clear_memory_cache()
        
        for code in stock_codes:
            start_time = time.perf_counter()
            await performance_data_manager.get_real_time_data(code=code)
            response_time = time.perf_counter() - start_time
            test_data['l2_cache'].append(response_time)
        
        # 第三轮：内存缓存命中
        for code in stock_codes:
            start_time = time.perf_counter()
            await performance_data_manager.get_real_time_data(code=code)
            response_time = time.perf_counter() - start_time
            test_data['l1_cache'].append(response_time)
        
        # 分析多级缓存性能
        avg_db_time = statistics.mean(test_data['database'])
        avg_l2_time = statistics.mean(test_data['l2_cache'])
        avg_l1_time = statistics.mean(test_data['l1_cache'])
        
        # 性能递增断言
        assert avg_l1_time < avg_l2_time, "内存缓存应该比Redis缓存更快"
        assert avg_l2_time < avg_db_time, "Redis缓存应该比数据库查询更快"
        
        l1_improvement = avg_db_time / avg_l1_time if avg_l1_time > 0 else 1
        l2_improvement = avg_db_time / avg_l2_time if avg_l2_time > 0 else 1
        
        assert l1_improvement >= 10, f"L1缓存性能提升 {l1_improvement:.1f}x 低于10x"
        assert l2_improvement >= 5, f"L2缓存性能提升 {l2_improvement:.1f}x 低于5x"
        
        print(f"\\n多级缓存性能分析:")
        print(f"数据库查询平均时间: {avg_db_time*1000:.1f}ms")
        print(f"Redis缓存平均时间: {avg_l2_time*1000:.1f}ms")
        print(f"内存缓存平均时间: {avg_l1_time*1000:.1f}ms")
        print(f"L1缓存提升: {l1_improvement:.1f}x")
        print(f"L2缓存提升: {l2_improvement:.1f}x")


class TestCacheCapacityAndEviction:
    """缓存容量和淘汰策略测试"""
    
    @pytest.mark.asyncio
    async def test_cache_capacity_limits(
        self,
        performance_data_manager,
        redis_client: redis.Redis
    ):
        """测试缓存容量限制和LRU淘汰"""
        # 生成大量不同的请求以填满缓存
        stock_codes = [f"{i:06d}" for i in range(1, 501)]  # 500个股票
        
        cache_misses = 0
        cache_hits = 0
        
        # 第一轮：填充缓存
        for i, code in enumerate(stock_codes[:200]):  # 前200个
            result = await performance_data_manager.get_real_time_data(code=code)
            
            # 记录缓存统计
            if hasattr(result, 'metadata'):
                if result.metadata.get('from_cache'):
                    cache_hits += 1
                else:
                    cache_misses += 1
        
        initial_cache_usage = await self._get_cache_memory_usage(redis_client)
        
        # 第二轮：超出缓存容量，触发淘汰
        for code in stock_codes[200:400]:  # 后200个
            await performance_data_manager.get_real_time_data(code=code)
        
        final_cache_usage = await self._get_cache_memory_usage(redis_client)
        
        # 验证缓存大小得到控制
        memory_growth = final_cache_usage - initial_cache_usage
        
        # 内存使用应该受到控制，不会无限增长
        assert memory_growth < initial_cache_usage * 2, (
            f"缓存内存增长过多: {memory_growth} bytes"
        )
        
        print(f"\\n缓存容量测试:")
        print(f"初始缓存使用: {initial_cache_usage} bytes")
        print(f"最终缓存使用: {final_cache_usage} bytes")
        print(f"内存增长: {memory_growth} bytes")
    
    @pytest.mark.asyncio
    async def test_cache_ttl_expiration(
        self,
        performance_data_manager,
        redis_client: redis.Redis
    ):
        """测试缓存TTL过期机制"""
        stock_code = "000001"
        
        # 设置短TTL进行测试
        original_ttl = getattr(performance_data_manager.config.cache, 'default_ttl', 3600)
        if hasattr(performance_data_manager.config.cache, 'default_ttl'):
            performance_data_manager.config.cache.default_ttl = 2  # 2秒TTL
        
        try:
            # 第一次请求，建立缓存
            perf_timer = PerformanceTimer()
            perf_timer.start()
            result1 = await performance_data_manager.get_real_time_data(code=stock_code)
            first_request_time = perf_timer.stop()
            
            # 立即再次请求，应该命中缓存
            perf_timer.start()
            result2 = await performance_data_manager.get_real_time_data(code=stock_code)
            second_request_time = perf_timer.stop()
            
            # 等待TTL过期
            await asyncio.sleep(3)
            
            # TTL过期后请求，应该重新获取数据
            perf_timer.start()
            result3 = await performance_data_manager.get_real_time_data(code=stock_code)
            third_request_time = perf_timer.stop()
            
            # 验证TTL机制
            assert second_request_time < first_request_time * 0.5, "第二次请求应该更快（缓存命中）"
            assert third_request_time > second_request_time * 2, "第三次请求应该更慢（缓存过期）"
            
            print(f"\\n缓存TTL测试:")
            print(f"首次请求时间: {first_request_time*1000:.1f}ms")
            print(f"缓存命中时间: {second_request_time*1000:.1f}ms")
            print(f"TTL过期后时间: {third_request_time*1000:.1f}ms")
            
        finally:
            # 恢复原始TTL设置
            if hasattr(performance_data_manager.config.cache, 'default_ttl'):
                performance_data_manager.config.cache.default_ttl = original_ttl
    
    async def _get_cache_memory_usage(self, redis_client: redis.Redis) -> int:
        """获取Redis内存使用量"""
        try:
            info = await redis_client.info('memory')
            return info.get('used_memory', 0)
        except:
            return 0


class TestCacheConcurrency:
    """缓存并发测试"""
    
    @pytest.mark.asyncio
    async def test_concurrent_cache_access(
        self,
        performance_data_manager,
        cache_monitor: CacheHitRateMonitor
    ):
        """测试并发访问缓存的正确性和性能"""
        stock_code = "000001"
        concurrent_requests = 100
        
        # 预热缓存
        await performance_data_manager.get_real_time_data(code=stock_code)
        
        async def concurrent_request():
            result = await performance_data_manager.get_real_time_data(code=stock_code)
            
            if hasattr(result, 'metadata') and result.metadata.get('from_cache'):
                cache_monitor.record_hit()
            else:
                cache_monitor.record_miss()
            
            return result
        
        # 执行并发请求
        start_time = time.perf_counter()
        results = await asyncio.gather(*[
            concurrent_request() for _ in range(concurrent_requests)
        ])
        total_time = time.perf_counter() - start_time
        
        # 验证并发缓存访问
        hit_rate = cache_monitor.hit_rate
        throughput = concurrent_requests / total_time
        
        assert hit_rate >= 0.95, f"并发缓存命中率 {hit_rate:.2%} 低于95%"
        assert throughput >= 1000, f"并发缓存吞吐量 {throughput:.1f} req/s 低于1000 req/s"
        
        # 验证数据一致性
        assert len(set([str(r) for r in results])) == 1, "并发缓存访问返回的数据不一致"
        
        print(f"\\n并发缓存访问测试:")
        print(f"并发请求数: {concurrent_requests}")
        print(f"命中率: {hit_rate:.2%}")
        print(f"吞吐量: {throughput:.1f} req/s")
        print(f"总耗时: {total_time*1000:.1f}ms")
    
    @pytest.mark.asyncio
    async def test_cache_stampede_protection(
        self,
        performance_data_manager,
        redis_client: redis.Redis
    ):
        """测试缓存雪崩保护机制"""
        stock_code = "000001"
        concurrent_requests = 50
        
        # 清理缓存，模拟缓存失效场景
        await redis_client.flushdb()
        
        # 记录实际的数据源访问次数
        data_source_calls = 0
        original_fetch = None
        
        # Mock数据源以计算调用次数
        if hasattr(performance_data_manager, '_data_fetcher'):
            original_fetch = performance_data_manager._data_fetcher.get_real_time_data
            
            async def counting_fetch(*args, **kwargs):
                nonlocal data_source_calls
                data_source_calls += 1
                await asyncio.sleep(0.1)  # 模拟数据源延迟
                return await original_fetch(*args, **kwargs)
            
            performance_data_manager._data_fetcher.get_real_time_data = counting_fetch
        
        try:
            # 并发请求同一数据
            start_time = time.perf_counter()
            results = await asyncio.gather(*[
                performance_data_manager.get_real_time_data(code=stock_code)
                for _ in range(concurrent_requests)
            ])
            total_time = time.perf_counter() - start_time
            
            # 验证缓存雪崩保护
            # 在有效的缓存雪崩保护机制下，数据源调用次数应该远少于并发请求数
            protection_ratio = data_source_calls / concurrent_requests
            
            assert protection_ratio <= 0.1, (
                f"缓存雪崩保护效果不佳: 数据源调用比例 {protection_ratio:.2%} 超过10%"
            )
            
            assert len(results) == concurrent_requests, "返回结果数量不正确"
            
            print(f"\\n缓存雪崩保护测试:")
            print(f"并发请求数: {concurrent_requests}")
            print(f"数据源调用次数: {data_source_calls}")
            print(f"保护效果: {100-protection_ratio*100:.1f}%")
            print(f"总耗时: {total_time*1000:.1f}ms")
            
        finally:
            # 恢复原始方法
            if original_fetch and hasattr(performance_data_manager, '_data_fetcher'):
                performance_data_manager._data_fetcher.get_real_time_data = original_fetch