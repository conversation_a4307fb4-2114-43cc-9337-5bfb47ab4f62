"""任务执行性能测试"""

import pytest
import asyncio
import time
from concurrent.futures import ThreadPoolExecutor
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta
import json

from app.models.task import TaskExecution
from app.schemas.scheduled_task import TaskStatus, TriggerType, TaskType


class TestTaskExecutionPerformance:
    """任务执行性能测试"""

    @pytest.mark.asyncio
    async def test_large_dataset_query_performance(self):
        """测试大数据集查询性能"""
        # 模拟大量执行记录的查询性能
        total_records = 100000
        page_size = 100
        
        async def simulate_database_query(skip, limit):
            """模拟数据库查询延迟"""
            # 模拟查询延迟（根据数据量调整）
            query_time = max(0.01, min(0.5, total_records / 1000000))  # 10ms to 500ms
            await asyncio.sleep(query_time)
            
            # 返回模拟数据
            records = []
            for i in range(limit):
                record_id = skip + i + 1
                if record_id > total_records:
                    break
                records.append({
                    'id': record_id,
                    'status': 'completed',
                    'created_at': datetime.now() - timedelta(days=record_id),
                    'results_count': record_id % 100
                })
            return records, total_records

        # 测试不同页面的查询性能
        performance_results = []
        
        for page in [1, 50, 500, 1000]:  # 测试不同页面
            skip = (page - 1) * page_size
            
            start_time = time.time()
            records, total = await simulate_database_query(skip, page_size)
            end_time = time.time()
            
            query_duration = end_time - start_time
            performance_results.append({
                'page': page,
                'skip': skip,
                'duration': query_duration,
                'records_returned': len(records)
            })
            
            # 验证查询性能满足要求（< 1秒）
            assert query_duration < 1.0, f"第{page}页查询耗时{query_duration:.3f}秒，超过性能要求"
            assert len(records) <= page_size

        # 验证性能一致性（不同页面的查询时间应该相近）
        durations = [result['duration'] for result in performance_results]
        max_duration = max(durations)
        min_duration = min(durations)
        performance_variance = (max_duration - min_duration) / min_duration
        
        # 性能波动不应超过100%
        assert performance_variance < 1.0, f"查询性能波动过大: {performance_variance:.2%}"

    @pytest.mark.asyncio
    async def test_concurrent_api_access_performance(self):
        """测试并发API访问性能"""
        concurrent_users = 10
        requests_per_user = 5
        
        async def simulate_user_requests(user_id):
            """模拟单个用户的多个请求"""
            requests = []
            
            # 模拟不同类型的API请求
            api_calls = [
                ('list_executions', 0.1),      # 列表查询
                ('get_execution_detail', 0.05), # 详情查询  
                ('filter_executions', 0.12),    # 过滤查询
                ('delete_execution', 0.08),     # 删除操作
                ('cancel_execution', 0.06)      # 取消操作
            ]
            
            for i in range(requests_per_user):
                api_name, base_duration = api_calls[i % len(api_calls)]
                
                start_time = time.time()
                # 模拟API调用延迟
                await asyncio.sleep(base_duration + (user_id * 0.001))  # 略微错开请求
                end_time = time.time()
                
                requests.append({
                    'user_id': user_id,
                    'api': api_name,
                    'duration': end_time - start_time,
                    'timestamp': end_time
                })
            
            return requests

        # 并发执行所有用户请求
        start_time = time.time()
        
        tasks = [simulate_user_requests(user_id) for user_id in range(concurrent_users)]
        all_results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # 展平结果
        all_requests = []
        for user_requests in all_results:
            all_requests.extend(user_requests)
        
        # 性能分析
        total_requests = len(all_requests)
        average_response_time = sum(req['duration'] for req in all_requests) / total_requests
        max_response_time = max(req['duration'] for req in all_requests)
        
        # 验证并发性能
        assert total_duration < 3.0, f"并发测试总耗时{total_duration:.3f}秒，超过预期"
        assert average_response_time < 0.2, f"平均响应时间{average_response_time:.3f}秒，超过预期"
        assert max_response_time < 0.5, f"最大响应时间{max_response_time:.3f}秒，超过预期"
        
        # 计算吞吐量
        throughput = total_requests / total_duration
        assert throughput > 10, f"吞吐量{throughput:.2f} requests/sec低于预期"

    @pytest.mark.asyncio
    async def test_virtual_scrolling_performance(self):
        """测试虚拟滚动性能"""
        total_items = 50000
        container_height = 600
        item_height = 50
        
        def calculate_visible_range(scroll_top, container_height, item_height, total_items):
            """计算可见范围（模拟前端虚拟滚动逻辑）"""
            start_time = time.time()
            
            # 计算可见项目
            visible_start = max(0, scroll_top // item_height - 5)  # 5个缓冲项
            visible_count = (container_height // item_height) + 10  # 额外10个缓冲项
            visible_end = min(total_items, visible_start + visible_count)
            
            calculation_time = time.time() - start_time
            
            return {
                'start': visible_start,
                'end': visible_end,
                'count': visible_end - visible_start,
                'calculation_time': calculation_time
            }
        
        # 测试不同滚动位置的计算性能
        scroll_positions = [0, 10000, 50000, 100000, 500000, 1000000]  # 不同的滚动位置
        
        calculation_times = []
        
        for scroll_top in scroll_positions:
            result = calculate_visible_range(scroll_top, container_height, item_height, total_items)
            calculation_times.append(result['calculation_time'])
            
            # 验证计算结果合理性
            assert result['start'] >= 0
            assert result['end'] <= total_items
            assert result['start'] < result['end']
            assert result['count'] > 0
            
            # 验证计算速度（应该在1毫秒内完成）
            assert result['calculation_time'] < 0.001, f"滚动位置{scroll_top}计算耗时{result['calculation_time']:.6f}秒，超过预期"
        
        # 验证计算时间的一致性
        avg_calculation_time = sum(calculation_times) / len(calculation_times)
        max_calculation_time = max(calculation_times)
        
        assert avg_calculation_time < 0.0005, f"平均计算时间{avg_calculation_time:.6f}秒，超过预期"
        assert max_calculation_time < 0.001, f"最大计算时间{max_calculation_time:.6f}秒，超过预期"

    @pytest.mark.asyncio
    async def test_real_time_polling_performance(self):
        """测试实时轮询性能"""
        polling_intervals = [1, 5, 10, 30]  # 不同轮询间隔（秒）
        test_duration = 10  # 测试持续时间（秒）
        
        for interval in polling_intervals:
            poll_count = 0
            total_poll_time = 0
            start_time = time.time()
            
            async def mock_poll_api():
                """模拟轮询API调用"""
                nonlocal poll_count, total_poll_time
                poll_start = time.time()
                
                # 模拟API响应时间
                await asyncio.sleep(0.05)  # 50ms 响应时间
                
                poll_end = time.time()
                poll_count += 1
                total_poll_time += (poll_end - poll_start)
                
                return {'status': 'success', 'timestamp': poll_end}
            
            # 执行轮询测试
            end_time = start_time + test_duration
            
            while time.time() < end_time:
                await mock_poll_api()
                await asyncio.sleep(interval)
            
            actual_duration = time.time() - start_time
            average_poll_time = total_poll_time / poll_count if poll_count > 0 else 0
            expected_polls = int(test_duration / interval)
            poll_accuracy = abs(poll_count - expected_polls) / expected_polls
            
            # 验证轮询性能
            assert average_poll_time < 0.1, f"间隔{interval}s轮询平均耗时{average_poll_time:.3f}s超过预期"
            assert poll_accuracy < 0.2, f"间隔{interval}s轮询次数偏差{poll_accuracy:.1%}超过预期"
            assert poll_count > 0, f"间隔{interval}s未执行任何轮询"

    @pytest.mark.asyncio
    async def test_memory_usage_with_large_dataset(self):
        """测试大数据集的内存使用"""
        import sys
        
        def get_memory_usage():
            """获取当前内存使用（简化版）"""
            import psutil
            import os
            process = psutil.Process(os.getpid())
            return process.memory_info().rss / 1024 / 1024  # MB
        
        initial_memory = get_memory_usage()
        
        # 创建大量模拟数据
        large_dataset = []
        for i in range(10000):
            execution_data = {
                'id': i + 1,
                'user_id': (i % 100) + 1,
                'scheduled_task_id': (i % 500) + 1 if i % 2 == 0 else None,
                'scheduled_task_name': f'任务{i+1}' if i % 2 == 0 else None,
                'trigger_type': 'scheduled' if i % 2 == 0 else 'manual',
                'task_type': 'indicator_scan',
                'status': ['completed', 'failed', 'running', 'cancelled'][i % 4],
                'start_time': datetime.now() - timedelta(hours=i),
                'end_time': datetime.now() - timedelta(hours=i-1) if i % 4 != 2 else None,
                'duration_seconds': (i % 600) + 60,
                'results_count': i % 50,
                'error_message': f'错误{i}' if i % 4 == 1 else None,
                'results_data': [{'symbol': f'{j:06d}.SZ', 'signal': 'buy'} for j in range(i % 10)] if i % 4 == 0 else None,
                'created_at': datetime.now() - timedelta(hours=i+1),
                'task_config': {'indicators': ['MACD', 'KDJ'], 'params': {'period': i % 20 + 5}}
            }
            large_dataset.append(execution_data)
        
        after_creation_memory = get_memory_usage()
        memory_increase = after_creation_memory - initial_memory
        
        # 模拟数据处理操作
        processed_data = []
        for execution in large_dataset:
            # 模拟前端数据处理逻辑
            processed_execution = {
                'id': execution['id'],
                'display_name': execution.get('scheduled_task_name', '手动扫描'),
                'status_text': {'completed': '已完成', 'failed': '已失败', 'running': '执行中', 'cancelled': '已取消'}.get(execution['status']),
                'duration_text': f"{execution['duration_seconds']}秒" if execution['duration_seconds'] else '',
                'has_results': bool(execution.get('results_data'))
            }
            processed_data.append(processed_execution)
        
        after_processing_memory = get_memory_usage()
        total_memory_increase = after_processing_memory - initial_memory
        
        # 内存使用验证
        assert memory_increase < 100, f"创建10000条记录内存增长{memory_increase:.1f}MB过多"
        assert total_memory_increase < 150, f"处理后总内存增长{total_memory_increase:.1f}MB过多"
        
        # 清理数据，验证内存释放
        del large_dataset
        del processed_data
        
        # 强制垃圾回收
        import gc
        gc.collect()
        
        await asyncio.sleep(0.1)  # 等待清理完成
        final_memory = get_memory_usage()
        memory_released = after_processing_memory - final_memory
        
        # 验证内存释放（至少释放50%）
        assert memory_released > total_memory_increase * 0.3, f"内存释放{memory_released:.1f}MB不足"

    @pytest.mark.asyncio
    async def test_database_connection_pool_performance(self):
        """测试数据库连接池性能"""
        connection_pool_size = 5
        concurrent_requests = 20
        
        class MockConnectionPool:
            def __init__(self, pool_size):
                self.pool_size = pool_size
                self.active_connections = 0
                self.waiting_requests = []
                self.total_wait_time = 0
                self.connection_acquisitions = 0
            
            async def acquire_connection(self):
                start_wait = time.time()
                
                while self.active_connections >= self.pool_size:
                    await asyncio.sleep(0.001)  # 等待连接释放
                
                wait_time = time.time() - start_wait
                self.total_wait_time += wait_time
                self.connection_acquisitions += 1
                self.active_connections += 1
                
                return {'id': self.active_connections, 'acquired_at': time.time()}
            
            async def release_connection(self, connection):
                self.active_connections -= 1
                await asyncio.sleep(0.01)  # 模拟连接清理时间
            
            def get_average_wait_time(self):
                return self.total_wait_time / self.connection_acquisitions if self.connection_acquisitions > 0 else 0

        pool = MockConnectionPool(connection_pool_size)
        
        async def simulate_database_operation(operation_id):
            """模拟数据库操作"""
            connection = await pool.acquire_connection()
            
            # 模拟查询执行时间
            query_duration = 0.05 + (operation_id % 10) * 0.001  # 50-60ms
            await asyncio.sleep(query_duration)
            
            await pool.release_connection(connection)
            return {'operation_id': operation_id, 'duration': query_duration}
        
        # 并发执行数据库操作
        start_time = time.time()
        tasks = [simulate_database_operation(i) for i in range(concurrent_requests)]
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        # 性能分析
        average_wait_time = pool.get_average_wait_time()
        average_operation_time = sum(result['duration'] for result in results) / len(results)
        
        # 验证连接池性能
        assert total_time < 5.0, f"总执行时间{total_time:.3f}秒过长"
        assert average_wait_time < 0.1, f"平均等待连接时间{average_wait_time:.3f}秒过长"
        assert len(results) == concurrent_requests, "部分操作未完成"
        
        # 验证连接池没有泄漏
        assert pool.active_connections == 0, f"连接池泄漏，剩余{pool.active_connections}个活跃连接"

    @pytest.mark.asyncio
    async def test_cache_performance_impact(self):
        """测试缓存对性能的影响"""
        cache_size = 1000
        total_queries = 5000
        cache_hit_ratio_target = 0.8  # 目标缓存命中率80%
        
        class MockCache:
            def __init__(self, max_size):
                self.max_size = max_size
                self.cache = {}
                self.access_times = {}
                self.hits = 0
                self.misses = 0
            
            def get(self, key):
                if key in self.cache:
                    self.hits += 1
                    self.access_times[key] = time.time()
                    return self.cache[key]
                else:
                    self.misses += 1
                    return None
            
            def set(self, key, value):
                if len(self.cache) >= self.max_size:
                    # LRU 清理
                    oldest_key = min(self.access_times.keys(), 
                                   key=lambda k: self.access_times[k])
                    del self.cache[oldest_key]
                    del self.access_times[oldest_key]
                
                self.cache[key] = value
                self.access_times[key] = time.time()
            
            def get_hit_ratio(self):
                total = self.hits + self.misses
                return self.hits / total if total > 0 else 0
        
        cache = MockCache(cache_size)
        
        async def simulate_cached_query(query_id):
            """模拟带缓存的查询"""
            # 生成查询键（模拟热点数据）
            if query_id < 100:
                cache_key = f"hot_query_{query_id % 20}"  # 20个热点查询
            else:
                cache_key = f"query_{query_id}"
            
            start_time = time.time()
            
            # 尝试从缓存获取
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                # 缓存命中，快速返回
                query_time = time.time() - start_time + 0.001  # 1ms缓存访问时间
                return {'query_id': query_id, 'duration': query_time, 'from_cache': True}
            
            # 缓存未命中，执行"数据库"查询
            await asyncio.sleep(0.05)  # 50ms数据库查询时间
            
            # 存储到缓存
            result_data = {'data': f'result_for_query_{query_id}', 'timestamp': time.time()}
            cache.set(cache_key, result_data)
            
            query_time = time.time() - start_time
            return {'query_id': query_id, 'duration': query_time, 'from_cache': False}
        
        # 执行查询测试
        start_time = time.time()
        
        # 分批执行以模拟真实场景
        batch_size = 100
        all_results = []
        
        for batch_start in range(0, total_queries, batch_size):
            batch_end = min(batch_start + batch_size, total_queries)
            batch_tasks = [simulate_cached_query(i) for i in range(batch_start, batch_end)]
            batch_results = await asyncio.gather(*batch_tasks)
            all_results.extend(batch_results)
            
            # 小间隔模拟真实负载
            await asyncio.sleep(0.01)
        
        total_time = time.time() - start_time
        
        # 性能分析
        cache_hit_ratio = cache.get_hit_ratio()
        cached_queries = [r for r in all_results if r['from_cache']]
        uncached_queries = [r for r in all_results if not r['from_cache']]
        
        avg_cached_time = sum(r['duration'] for r in cached_queries) / len(cached_queries) if cached_queries else 0
        avg_uncached_time = sum(r['duration'] for r in uncached_queries) / len(uncached_queries) if uncached_queries else 0
        
        # 验证缓存性能
        assert cache_hit_ratio >= cache_hit_ratio_target * 0.8, f"缓存命中率{cache_hit_ratio:.2%}低于预期"
        assert avg_cached_time < 0.005, f"缓存查询平均时间{avg_cached_time:.3f}秒过长"
        assert avg_uncached_time > avg_cached_time * 10, f"缓存性能提升不明显"
        
        # 验证总体性能提升
        expected_time_without_cache = total_queries * 0.05  # 所有查询都走数据库
        performance_improvement = (expected_time_without_cache - total_time) / expected_time_without_cache
        
        assert performance_improvement > 0.5, f"缓存性能提升{performance_improvement:.1%}不足"