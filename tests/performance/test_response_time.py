"""
API响应时间性能测试

验证所有API端点的响应时间满足 < 50ms 的要求
"""
import pytest
import asyncio
import statistics
from typing import List, Dict, Any
import aiohttp
from datetime import datetime, timedelta

from tests.performance.conftest import (
    performance_test, 
    PerformanceTimer,
    CacheHitRateMonitor
)


class TestAPIResponseTime:
    """API响应时间测试"""
    
    @performance_test(max_response_time=0.05)  # 50ms
    async def test_stock_list_response_time(
        self, 
        performance_data_manager,
        perf_timer: PerformanceTimer,
        cache_monitor: CacheHitRateMonitor
    ):
        """测试股票列表API响应时间"""
        response_times = []
        
        # 预热缓存
        await performance_data_manager.get_stock_list()
        
        # 执行多次请求测试
        for _ in range(100):
            perf_timer.start()
            
            result = await performance_data_manager.get_stock_list()
            
            response_time = perf_timer.stop()
            response_times.append(response_time)
            
            # 记录缓存命中情况
            if hasattr(result, 'from_cache') and result.from_cache:
                cache_monitor.record_hit()
            else:
                cache_monitor.record_miss()
        
        # 性能断言
        avg_response_time = statistics.mean(response_times)
        p95_response_time = statistics.quantiles(response_times, n=20)[18]  # 95th percentile
        
        assert avg_response_time < 0.05, f"平均响应时间 {avg_response_time:.3f}s 超过50ms"
        assert p95_response_time < 0.1, f"P95响应时间 {p95_response_time:.3f}s 过高"
        assert max(response_times) < 0.2, f"最大响应时间 {max(response_times):.3f}s 过高"
        
        print(f"\\n股票列表API性能指标:")
        print(f"平均响应时间: {avg_response_time*1000:.1f}ms")
        print(f"P95响应时间: {p95_response_time*1000:.1f}ms")
        print(f"最大响应时间: {max(response_times)*1000:.1f}ms")
        print(f"缓存命中率: {cache_monitor.hit_rate*100:.1f}%")
    
    @performance_test(max_response_time=0.05)
    async def test_stock_daily_data_response_time(
        self,
        performance_data_manager,
        perf_timer: PerformanceTimer
    ):
        """测试股票日线数据API响应时间"""
        stock_code = "000001"
        response_times = []
        
        for _ in range(50):
            perf_timer.start()
            
            result = await performance_data_manager.get_stock_daily_data(
                code=stock_code,
                start_date=datetime.now() - timedelta(days=30),
                end_date=datetime.now()
            )
            
            response_time = perf_timer.stop()
            response_times.append(response_time)
        
        avg_response_time = statistics.mean(response_times)
        p95_response_time = statistics.quantiles(response_times, n=20)[18]
        
        assert avg_response_time < 0.05, f"日线数据平均响应时间 {avg_response_time:.3f}s 超过50ms"
        assert p95_response_time < 0.1, f"日线数据P95响应时间 {p95_response_time:.3f}s 过高"
        
        print(f"\\n日线数据API性能指标:")
        print(f"平均响应时间: {avg_response_time*1000:.1f}ms")
        print(f"P95响应时间: {p95_response_time*1000:.1f}ms")
    
    @performance_test(max_response_time=0.03)
    async def test_real_time_data_response_time(
        self,
        performance_data_manager,
        perf_timer: PerformanceTimer
    ):
        """测试实时行情数据响应时间 - 更严格的要求"""
        stock_codes = ["000001", "000002", "600000", "600036", "000858"]
        response_times = []
        
        for code in stock_codes:
            for _ in range(20):
                perf_timer.start()
                
                result = await performance_data_manager.get_real_time_data(code=code)
                
                response_time = perf_timer.stop()
                response_times.append(response_time)
        
        avg_response_time = statistics.mean(response_times)
        p95_response_time = statistics.quantiles(response_times, n=20)[18]
        
        assert avg_response_time < 0.03, f"实时数据平均响应时间 {avg_response_time:.3f}s 超过30ms"
        assert p95_response_time < 0.05, f"实时数据P95响应时间 {p95_response_time:.3f}s 超过50ms"
        
        print(f"\\n实时数据API性能指标:")
        print(f"平均响应时间: {avg_response_time*1000:.1f}ms")
        print(f"P95响应时间: {p95_response_time*1000:.1f}ms")
    
    @performance_test(max_response_time=0.08)
    async def test_indicator_calculation_response_time(
        self,
        performance_data_manager,
        perf_timer: PerformanceTimer
    ):
        """测试技术指标计算响应时间"""
        stock_code = "000001"
        indicators = ["MACD", "KDJ", "RSI", "BOLL"]
        response_times = []
        
        for indicator in indicators:
            for _ in range(10):
                perf_timer.start()
                
                result = await performance_data_manager.calculate_indicator(
                    code=stock_code,
                    indicator=indicator,
                    period=20,
                    start_date=datetime.now() - timedelta(days=60)
                )
                
                response_time = perf_timer.stop()
                response_times.append(response_time)
        
        avg_response_time = statistics.mean(response_times)
        p95_response_time = statistics.quantiles(response_times, n=20)[18]
        
        assert avg_response_time < 0.08, f"指标计算平均响应时间 {avg_response_time:.3f}s 超过80ms"
        assert p95_response_time < 0.15, f"指标计算P95响应时间 {p95_response_time:.3f}s 过高"
        
        print(f"\\n技术指标计算性能指标:")
        print(f"平均响应时间: {avg_response_time*1000:.1f}ms")
        print(f"P95响应时间: {p95_response_time*1000:.1f}ms")


class TestBatchAPIResponseTime:
    """批量API响应时间测试"""
    
    @performance_test(max_response_time=0.1)
    async def test_batch_stock_data_response_time(
        self,
        performance_data_manager,
        stock_codes_large: List[str],
        perf_timer: PerformanceTimer
    ):
        """测试批量股票数据响应时间"""
        batch_sizes = [10, 50, 100, 200]
        
        for batch_size in batch_sizes:
            codes = stock_codes_large[:batch_size]
            response_times = []
            
            for _ in range(5):
                perf_timer.start()
                
                results = await performance_data_manager.get_batch_stock_data(
                    codes=codes,
                    data_type="basic_info"
                )
                
                response_time = perf_timer.stop()
                response_times.append(response_time)
            
            avg_response_time = statistics.mean(response_times)
            
            # 批量请求的响应时间应该随批量大小线性增长，但有合理上限
            max_allowed_time = min(0.1 + (batch_size - 10) * 0.001, 0.5)
            
            assert avg_response_time < max_allowed_time, (
                f"批量{batch_size}个股票数据平均响应时间 {avg_response_time:.3f}s "
                f"超过预期 {max_allowed_time:.3f}s"
            )
            
            print(f"\\n批量{batch_size}股票数据性能:")
            print(f"平均响应时间: {avg_response_time*1000:.1f}ms")
            print(f"单个股票平均时间: {avg_response_time/batch_size*1000:.2f}ms")


class TestColdStartPerformance:
    """冷启动性能测试"""
    
    @performance_test(max_response_time=0.2)
    async def test_cold_start_response_time(
        self,
        redis_client,
        perf_timer: PerformanceTimer
    ):
        """测试系统冷启动时的响应时间"""
        # 清理所有缓存
        await redis_client.flushdb()
        
        # 创建新的数据管理器实例
        from app.core.data.manager import UnifiedDataManager
        from app.core.data.models import DataManagerConfig
        
        config = DataManagerConfig()
        manager = UnifiedDataManager(config)
        
        try:
            perf_timer.start()
            await manager.start()
            startup_time = perf_timer.stop()
            
            # 首次数据请求
            perf_timer.start()
            result = await manager.get_stock_list()
            first_request_time = perf_timer.stop()
            
            assert startup_time < 2.0, f"系统启动时间 {startup_time:.3f}s 过长"
            assert first_request_time < 0.2, f"首次请求响应时间 {first_request_time:.3f}s 超过200ms"
            
            print(f"\\n冷启动性能指标:")
            print(f"系统启动时间: {startup_time*1000:.1f}ms")
            print(f"首次请求时间: {first_request_time*1000:.1f}ms")
            
        finally:
            await manager.shutdown()


@pytest.mark.parametrize("endpoint,expected_max_time", [
    ("/api/v1/stocks/", 0.05),
    ("/api/v1/stocks/000001/daily", 0.05),
    ("/api/v1/stocks/000001/realtime", 0.03),
    ("/api/v1/indicators/MACD", 0.08),
    ("/api/v1/analytics/kline", 0.1),
])
@performance_test()
async def test_http_endpoint_response_times(
    endpoint: str,
    expected_max_time: float
):
    """直接测试HTTP端点响应时间"""
    async with aiohttp.ClientSession() as session:
        response_times = []
        
        for _ in range(20):
            start_time = asyncio.get_event_loop().time()
            
            async with session.get(f"http://localhost:8000{endpoint}") as response:
                await response.read()
            
            end_time = asyncio.get_event_loop().time()
            response_times.append(end_time - start_time)
        
        avg_response_time = statistics.mean(response_times)
        p95_response_time = statistics.quantiles(response_times, n=20)[18]
        
        assert avg_response_time < expected_max_time, (
            f"端点 {endpoint} 平均响应时间 {avg_response_time:.3f}s "
            f"超过预期 {expected_max_time:.3f}s"
        )
        
        print(f"\\n端点 {endpoint} 性能:")
        print(f"平均响应时间: {avg_response_time*1000:.1f}ms")
        print(f"P95响应时间: {p95_response_time*1000:.1f}ms")