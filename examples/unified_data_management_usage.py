"""
统一数据管理系统使用示例

本文件展示如何使用新的统一数据管理系统来替代现有的数据获取方式。
"""

import asyncio
from typing import List, Dict, Any
from datetime import datetime, date

# 导入统一数据管理系统
from app.core.data import (
    UnifiedDataManager,
    DataType,
    DataRequest,
    CacheStrategy,
    RoutingStrategy,
    get_data_manager,
    init_data_manager
)

# 导入智能缓存装饰器
from app.utils.decorators import (
    smart_data_cache,
    cache_stock_info,
    cache_stock_daily,
    cache_stock_realtime
)

# 导入向后兼容层
from app.core.data.compatibility import (
    CompatibilityDataFetcher,
    enable_unified_data_management
)


async def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 初始化数据管理器
    data_manager = await init_data_manager()
    
    try:
        # 1. 获取股票基本信息
        print("1. 获取股票基本信息...")
        result = await data_manager.get_stock_info("000001")
        if result.is_success:
            print(f"股票信息: {result.data}")
            print(f"缓存命中: {result.cache_hit}")
            print(f"数据源: {result.data_source}")
            print(f"响应时间: {result.response_time:.3f}s")
        
        # 2. 获取股票日线数据
        print("\n2. 获取股票日线数据...")
        result = await data_manager.get_stock_daily(
            stock_code="000001",
            start_date="2024-01-01",
            end_date="2024-01-31"
        )
        if result.is_success:
            print(f"日线数据条数: {result.count}")
            print(f"缓存命中: {result.cache_hit}")
        
        # 3. 获取实时行情
        print("\n3. 获取实时行情...")
        result = await data_manager.get_stock_realtime(["000001", "000002"])
        if result.is_success:
            print(f"实时行情: {result.data}")
        
        # 4. 使用不同的缓存策略
        print("\n4. 使用不同缓存策略...")
        result = await data_manager.get_data(
            data_type=DataType.STOCK_INFO,
            params={"stock_code": "000001"},
            cache_strategy=CacheStrategy.API_FIRST,  # 优先从API获取
            force_refresh=True  # 强制刷新缓存
        )
        if result.is_success:
            print(f"API优先获取结果: {result.data}")
        
    finally:
        await data_manager.stop()


async def example_batch_operations():
    """批量操作示例"""
    print("\n=== 批量操作示例 ===")
    
    data_manager = await init_data_manager()
    
    try:
        # 批量获取多只股票信息
        batch_requests = [
            {
                "data_type": DataType.STOCK_INFO,
                "params": {"stock_code": "000001"}
            },
            {
                "data_type": DataType.STOCK_INFO,
                "params": {"stock_code": "000002"}
            },
            {
                "data_type": DataType.STOCK_INFO,
                "params": {"stock_code": "600000"}
            }
        ]
        
        results = await data_manager.batch_get_data(
            batch_requests,
            max_concurrency=5
        )
        
        successful_results = [r for r in results if r.is_success]
        print(f"批量获取成功: {len(successful_results)}/{len(results)}")
        
        for result in successful_results:
            if result.data:
                print(f"- {result.data.get('code', 'N/A')}: {result.data.get('name', 'N/A')}")
    
    finally:
        await data_manager.stop()


def example_decorator_usage():
    """装饰器使用示例"""
    print("\n=== 装饰器使用示例 ===")
    
    # 使用预定义的缓存装饰器
    @cache_stock_info(cache_ttl=1800)  # 30分钟缓存
    async def get_stock_basic_info(stock_code: str) -> Dict[str, Any]:
        """获取股票基本信息（带缓存）"""
        # 这里是实际的数据获取逻辑
        # 在实际使用中，这里会调用数据API
        return {
            "code": stock_code,
            "name": f"股票{stock_code}",
            "market": "深交所"
        }
    
    @cache_stock_daily(cache_ttl=900)  # 15分钟缓存
    async def get_stock_daily_data(
        stock_code: str, 
        start_date: str, 
        end_date: str
    ) -> List[Dict[str, Any]]:
        """获取股票日线数据（带缓存）"""
        return [
            {
                "date": start_date,
                "open": 10.0,
                "close": 10.5,
                "high": 11.0,
                "low": 9.8,
                "volume": 1000000
            }
        ]
    
    @smart_data_cache(
        data_type=DataType.STOCK_INDICATORS,
        cache_strategy=CacheStrategy.CACHE_THROUGH,
        cache_ttl=3600
    )
    async def calculate_indicators(
        stock_code: str,
        indicator_types: List[str]
    ) -> Dict[str, List[float]]:
        """计算技术指标（带智能缓存）"""
        # 模拟指标计算
        return {
            indicator: [1.0, 2.0, 3.0] for indicator in indicator_types
        }
    
    # 示例函数已定义，在实际使用时会被调用
    print("装饰器函数已定义，可以在异步环境中调用")


async def example_compatibility_layer():
    """向后兼容层示例"""
    print("\n=== 向后兼容层示例 ===")
    
    # 启用统一数据管理系统
    enable_unified_data_management()
    
    # 使用兼容性数据获取器
    fetcher = CompatibilityDataFetcher(enable_unified_management=True)
    
    try:
        # 这些方法与现有的DataFetcher接口完全兼容
        stock_list = await fetcher.get_stock_list()
        print(f"股票列表数量: {len(stock_list)}")
        
        if stock_list:
            # 获取第一只股票的日线数据
            first_stock = stock_list[0]
            stock_code = first_stock.get('code', '000001')
            
            daily_data = await fetcher.get_daily_data(
                stock_code,
                start_date=date(2024, 1, 1),
                end_date=date(2024, 1, 31)
            )
            print(f"{stock_code} 日线数据条数: {len(daily_data)}")
        
        # 获取实时行情
        realtime_data = await fetcher.get_realtime_quotes(["000001", "000002"])
        print(f"实时行情数据: {len(realtime_data)} 条")
        
    finally:
        await fetcher.close()


async def example_monitoring_and_stats():
    """监控和统计示例"""
    print("\n=== 监控和统计示例 ===")
    
    data_manager = get_data_manager()
    
    try:
        # 执行一些数据请求
        await data_manager.get_stock_info("000001")
        await data_manager.get_stock_info("000002")
        await data_manager.get_stock_daily("000001", "2024-01-01", "2024-01-31")
        
        # 获取统计信息
        stats = data_manager.get_stats()
        print("数据管理器统计信息:")
        print(f"- 总请求数: {stats['manager_stats']['total_requests']}")
        print(f"- 成功请求数: {stats['manager_stats']['successful_requests']}")
        print(f"- 成功率: {stats['manager_stats']['success_rate']:.2f}%")
        
        # 获取健康检查信息
        health = await data_manager.health_check()
        print(f"\n健康检查:")
        print(f"- 管理器运行状态: {health['manager_running']}")
        print(f"- Redis连接状态: {health['redis_connected']}")
        print(f"- 已加载插件数: {health['plugins_loaded']}")
        print(f"- 数据源数量: {health['data_sources']}")
        
    except Exception as e:
        print(f"监控示例执行出错: {e}")


async def example_cache_management():
    """缓存管理示例"""
    print("\n=== 缓存管理示例 ===")
    
    data_manager = get_data_manager()
    
    try:
        # 获取数据（会被缓存）
        result1 = await data_manager.get_stock_info("000001")
        print(f"第一次获取 - 缓存命中: {result1.cache_hit}")
        
        # 再次获取（应该命中缓存）
        result2 = await data_manager.get_stock_info("000001")
        print(f"第二次获取 - 缓存命中: {result2.cache_hit}")
        
        # 使缓存失效
        await data_manager.invalidate_cache(
            data_type=DataType.STOCK_INFO,
            params={"stock_code": "000001"}
        )
        print("缓存已失效")
        
        # 再次获取（缓存已失效，需要重新获取）
        result3 = await data_manager.get_stock_info("000001")
        print(f"失效后获取 - 缓存命中: {result3.cache_hit}")
        
    except Exception as e:
        print(f"缓存管理示例执行出错: {e}")


async def main():
    """主函数"""
    print("统一数据管理系统使用示例")
    print("=" * 50)
    
    try:
        # 运行各种示例
        await example_basic_usage()
        await example_batch_operations()
        example_decorator_usage()
        await example_compatibility_layer()
        await example_monitoring_and_stats()
        await example_cache_management()
        
    except Exception as e:
        print(f"示例执行出错: {e}")
    
    print("\n所有示例执行完成!")


if __name__ == "__main__":
    asyncio.run(main())