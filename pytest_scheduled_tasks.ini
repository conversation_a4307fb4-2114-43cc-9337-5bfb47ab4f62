# 定时任务测试配置

[pytest]
# 基本配置
minversion = 6.0
testpaths = 
    tests/unit/services/tasks
    tests/integration/api/test_scheduled_tasks_api.py
    tests/integration/api/test_task_executions_api.py
    tests/integration/test_scheduled_tasks_e2e.py

# 测试标记
markers =
    unit: 单元测试
    integration: 集成测试
    e2e: 端到端测试
    slow: 运行时间较长的测试
    asyncio: 异步测试

# 异步测试支持
asyncio_mode = auto

# 测试发现模式
python_files = test_*.py *_test.py
python_classes = Test* *Test *Tests
python_functions = test_*

# 输出配置
addopts = 
    --strict-markers
    --strict-config
    --tb=short
    --disable-warnings
    -ra

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:pydantic.*