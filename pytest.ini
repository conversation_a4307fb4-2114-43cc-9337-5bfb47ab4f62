[pytest]
# 测试文件匹配模式
python_files = test_*.py *_test.py
python_classes = Test* *Tests *TestCase
python_functions = test_*

# 测试标记定义
markers =
    unit: 单元测试，不依赖外部系统
    integration: 集成测试，需要外部依赖
    performance: 性能测试
    security: 安全测试
    scenario: 业务场景测试
    slow: 执行时间较长的测试 (>5秒)
    fast: 快速测试 (<1秒)
    smoke: 冒烟测试，核心功能验证
    regression: 回归测试
    critical: 关键功能测试
    database: 需要数据库的测试
    redis: 需要Redis的测试
    api: API相关测试
    cache: 缓存相关测试
    mock: 使用Mock的测试
    backtest: 技术指标回测功能相关测试
    e2e: 端到端测试
    frontend: 前端相关测试

# 异步测试配置
asyncio_mode = strict

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)s] %(name)s: %(message)s (%(filename)s:%(lineno)s)
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 测试路径
testpaths = tests

# 忽略的目录
norecursedirs = .git .venv venv build dist *.egg-info node_modules frontend-app

# 警告处理
filterwarnings =
    ignore::DeprecationWarning
    ignore::UserWarning
    ignore::PendingDeprecationWarning
    ignore::ResourceWarning
    error::pytest.PytestUnraisableExceptionWarning

# 主要测试配置
addopts = 
    --strict-markers
    --strict-config
    --cov=app
    --cov-report=term-missing:skip-covered
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-report=json:coverage.json
    --cov-branch
    --cov-fail-under=95
    --durations=10
    --tb=short
    --maxfail=3
    --timeout=300
    --html=reports/pytest_report.html
    --self-contained-html
    --json-report
    --json-report-file=reports/pytest_report.json

# 并行测试配置 (使用 -n auto 启用)
# -n auto 会自动检测CPU核心数

# 性能测试最小阈值
timeout = 300

# JUnit XML报告 (CI/CD集成)
junit_family = xunit2
