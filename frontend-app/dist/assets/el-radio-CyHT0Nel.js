import{au as $,ar as z,b7 as w,P as B,a3 as S,a2 as V,U as F,a as E,a7 as X,x as m,bM as P,a9 as Z,bb as J,b1 as Q,W as k,X as _,Z as I,d as C,i as G,e as y,A,bN as U,H as e,h,q as v,bO as x,a0 as N,k as D,t as K,y as L,p as Y,R as ee,by as ae,a6 as oe,ba as le,o as se,am as ne,r as te,bz as re,s as ie,ak as de,aR as M,aH as ue}from"./index-XMYcRHxF.js";const T=B({modelValue:{type:[String,Number,Boolean],default:void 0},size:F,disabled:Boolean,label:{type:[String,Number,Boolean],default:void 0},value:{type:[String,Number,Boolean],default:void 0},name:{type:String,default:void 0}}),ce=B({...T,border:Boolean}),H={[V]:o=>$(o)||z(o)||w(o),[S]:o=>$(o)||z(o)||w(o)},q=Symbol("radioGroupKey"),O=(o,u)=>{const s=E(),a=X(q,void 0),i=m(()=>!!a),c=m(()=>P(o.value)?o.label:o.value),r=m({get(){return i.value?a.modelValue:o.modelValue},set(n){i.value?a.changeEvent(n):u&&u(V,n),s.value.checked=o.modelValue===c.value}}),d=Z(m(()=>a==null?void 0:a.size)),l=J(m(()=>a==null?void 0:a.disabled)),t=E(!1),p=m(()=>l.value||i.value&&r.value!==c.value?-1:0);return Q({from:"label act as value",replacement:"value",version:"3.0.0",scope:"el-radio",ref:"https://element-plus.org/en-US/component/radio.html"},m(()=>i.value&&P(o.value))),{radioRef:s,isGroup:i,radioGroup:a,focus:t,size:d,disabled:l,tabIndex:p,modelValue:r,actualValue:c}},pe=_({name:"ElRadio"}),be=_({...pe,props:ce,emits:H,setup(o,{emit:u}){const s=o,a=I("radio"),{radioRef:i,radioGroup:c,focus:r,size:d,disabled:l,modelValue:t,actualValue:p}=O(s,u);function n(){L(()=>u(S,t.value))}return(b,g)=>{var f;return G(),C("label",{class:v([e(a).b(),e(a).is("disabled",e(l)),e(a).is("focus",e(r)),e(a).is("bordered",b.border),e(a).is("checked",e(t)===e(p)),e(a).m(e(d))])},[y("span",{class:v([e(a).e("input"),e(a).is("disabled",e(l)),e(a).is("checked",e(t)===e(p))])},[A(y("input",{ref_key:"radioRef",ref:i,"onUpdate:modelValue":R=>x(t)?t.value=R:null,class:v(e(a).e("original")),value:e(p),name:b.name||((f=e(c))==null?void 0:f.name),disabled:e(l),checked:e(t)===e(p),type:"radio",onFocus:R=>r.value=!0,onBlur:R=>r.value=!1,onChange:n,onClick:h(()=>{},["stop"])},null,42,["onUpdate:modelValue","value","name","disabled","checked","onFocus","onBlur","onClick"]),[[U,e(t)]]),y("span",{class:v(e(a).e("inner"))},null,2)],2),y("span",{class:v(e(a).e("label")),onKeydown:h(()=>{},["stop"])},[N(b.$slots,"default",{},()=>[D(K(b.label),1)])],42,["onKeydown"])],2)}}});var me=k(be,[["__file","radio.vue"]]);const ve=B({...T}),fe=_({name:"ElRadioButton"}),ye=_({...fe,props:ve,setup(o){const u=o,s=I("radio"),{radioRef:a,focus:i,size:c,disabled:r,modelValue:d,radioGroup:l,actualValue:t}=O(u),p=m(()=>({backgroundColor:(l==null?void 0:l.fill)||"",borderColor:(l==null?void 0:l.fill)||"",boxShadow:l!=null&&l.fill?`-1px 0 0 0 ${l.fill}`:"",color:(l==null?void 0:l.textColor)||""}));return(n,b)=>{var g;return G(),C("label",{class:v([e(s).b("button"),e(s).is("active",e(d)===e(t)),e(s).is("disabled",e(r)),e(s).is("focus",e(i)),e(s).bm("button",e(c))])},[A(y("input",{ref_key:"radioRef",ref:a,"onUpdate:modelValue":f=>x(d)?d.value=f:null,class:v(e(s).be("button","original-radio")),value:e(t),type:"radio",name:n.name||((g=e(l))==null?void 0:g.name),disabled:e(r),onFocus:f=>i.value=!0,onBlur:f=>i.value=!1,onClick:h(()=>{},["stop"])},null,42,["onUpdate:modelValue","value","name","disabled","onFocus","onBlur","onClick"]),[[U,e(d)]]),y("span",{class:v(e(s).be("button","inner")),style:Y(e(d)===e(t)?e(p):{}),onKeydown:h(()=>{},["stop"])},[N(n.$slots,"default",{},()=>[D(K(n.label),1)])],46,["onKeydown"])],2)}}});var W=k(ye,[["__file","radio-button.vue"]]);const _e=B({id:{type:String,default:void 0},size:F,disabled:Boolean,modelValue:{type:[String,Number,Boolean],default:void 0},fill:{type:String,default:""},textColor:{type:String,default:""},name:{type:String,default:void 0},validateEvent:{type:Boolean,default:!0},...ee(["ariaLabel"])}),ge=H,he=_({name:"ElRadioGroup"}),Be=_({...he,props:_e,emits:ge,setup(o,{emit:u}){const s=o,a=I("radio"),i=ae(),c=E(),{formItem:r}=oe(),{inputId:d,isLabeledByFormItem:l}=le(s,{formItemContext:r}),t=n=>{u(V,n),L(()=>u(S,n))};se(()=>{const n=c.value.querySelectorAll("[type=radio]"),b=n[0];!Array.from(n).some(g=>g.checked)&&b&&(b.tabIndex=0)});const p=m(()=>s.name||i.value);return ne(q,te({...re(s),changeEvent:t,name:p})),ie(()=>s.modelValue,()=>{s.validateEvent&&(r==null||r.validate("change").catch(n=>de()))}),(n,b)=>(G(),C("div",{id:e(d),ref_key:"radioGroupRef",ref:c,class:v(e(a).b("group")),role:"radiogroup","aria-label":e(l)?void 0:n.ariaLabel||"radio-group","aria-labelledby":e(l)?e(r).labelId:void 0},[N(n.$slots,"default")],10,["id","aria-label","aria-labelledby"]))}});var j=k(Be,[["__file","radio-group.vue"]]);const Ee=ue(me,{RadioButton:W,RadioGroup:j}),Se=M(j);M(W);export{Se as E,Ee as a};
