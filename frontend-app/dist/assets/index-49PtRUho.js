import{_ as le,u as te,a as d,x as I,r as h,o as se,d as oe,e as f,f as a,k as n,g as U,H as N,bL as re,E as ie,w as t,bt as y,m as p,i as ne,ai as de,l as ue,q as me,t as E,bi as M}from"./index-XMYcRHxF.js";import{E as pe}from"./el-overlay-r0bpWscF.js";import{E as ce,a as fe}from"./el-form-item-B8tg80IO.js";import{E as _e,a as ge}from"./el-radio-CyHT0Nel.js";import{a as ve,E as be}from"./CommonTable-3T2O5_iP.js";/* empty css                 */import{I as we}from"./IconButton-CV2L1HCo.js";import"./el-checkbox-CK4bBt59.js";import"./CommonPagination-B9js4cyy.js";const ye={class:"user-management-container p-6 bg-bg-primary text-text-primary"},Ve={class:"user-list-card bg-bg-secondary rounded-lg shadow-lg"},xe={class:"card-header flex items-center mb-6 gap-4 p-6"},Ce={class:"text-2xl font-semibold text-text-primary"},Ue={class:"search-container"},Ee={class:"flex space-x-3"},ke={class:"card-content px-6 pb-6"},Pe={class:"table-actions"},$e={__name:"index",setup(ze){const j=te(),A=[{prop:"id",label:"ID",width:80},{prop:"username",label:"用户名",minWidth:120},{prop:"email",label:"邮箱",minWidth:160},{prop:"role",label:"角色",width:100,slot:"role"},{prop:"status",label:"状态",width:100,slot:"status"},{prop:"last_login",label:"最后登录",width:180,slot:"last_login"},{prop:"created_at",label:"创建时间",width:180,slot:"created_at"}],k=d(!1),P=d([]),S=d(0),$=d(1),z=d(20),V=d(""),W=I(()=>({total:S.value,currentPage:$.value,pageSize:z.value,pageSizes:[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper"})),v=d(!1),u=d(!1),b=d(null),r=h({username:"",email:"",password:"",confirmPassword:"",is_admin:!1}),w=d(!1),c=d(!1),B=d(null),o=h({id:null,username:"",email:"",is_admin:!1,newPassword:""}),G={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:50,message:"用户名长度在 3 到 50 个字符",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入有效的邮箱地址",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码至少6位",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:(s,e,i)=>{e!==r.password?i(new Error("两次输入的密码不一致")):i()},trigger:"blur"}]},H={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:50,message:"用户名长度在 3 到 50 个字符",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入有效的邮箱地址",trigger:"blur"}]},O=I(()=>{if(!V.value)return P.value;const s=V.value.toLowerCase();return P.value.filter(e=>e.username.toLowerCase().includes(s)||e.email.toLowerCase().includes(s))}),q=s=>s?new Date(s).toLocaleString("zh-CN"):"-",g=async()=>{k.value=!0;try{const s=await y.getUserList($.value,z.value);P.value=s.data||[],S.value=s.total||0}catch(s){console.error("加载用户列表失败:",s),p.error("加载用户列表失败")}finally{k.value=!1}},Q=()=>{},J=({page:s,size:e})=>{$.value=s,z.value=e,g()},K=async()=>{if(!(!b.value||!await b.value.validate().catch(()=>!1))){u.value=!0;try{await y.createUser({username:r.username,email:r.email,password:r.password,is_admin:r.is_admin}),p.success("用户创建成功"),v.value=!1,ae(),await g()}catch(e){console.error("创建用户失败:",e),p.error(e.message||"创建用户失败")}finally{u.value=!1}}},X=s=>{o.id=s.id,o.username=s.username,o.email=s.email,o.is_admin=s.is_admin,o.newPassword="",w.value=!0},Y=async()=>{if(!(!B.value||!await B.value.validate().catch(()=>!1))){c.value=!0;try{const e={username:o.username,email:o.email,is_admin:o.is_admin};o.newPassword&&(e.password=o.newPassword),await y.updateUser(o.id,e),p.success("用户信息更新成功"),w.value=!1,await g()}catch(e){console.error("更新用户失败:",e),p.error(e.message||"更新用户失败")}finally{c.value=!1}}},Z=async s=>{const e=s.is_active?"禁用":"启用";try{await M.confirm(`确定要${e}用户 ${s.username} 吗？`,`${e}用户`,{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await y.toggleUserStatus(s.id,!s.is_active),p.success(`${e}用户成功`),await g()}catch(i){i!=="cancel"&&(console.error(`${e}用户失败:`,i),p.error(`${e}用户失败`))}},ee=async s=>{try{await M.confirm(`确定要删除用户 ${s.username} 吗？此操作不可恢复！`,"删除用户",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"error"}),await y.deleteUser(s.id),p.success("删除用户成功"),await g()}catch(e){e!=="cancel"&&(console.error("删除用户失败:",e),p.error("删除用户失败"))}},ae=()=>{Object.assign(r,{username:"",email:"",password:"",confirmPassword:"",is_admin:!1}),b.value&&b.value.resetFields()};return se(()=>{g()}),(s,e)=>{const i=ie,D=be,_=ue,F=de,m=fe,x=ge,L=_e,R=ce,T=pe;return ne(),oe("div",ye,[f("div",Ve,[f("div",xe,[f("h2",Ce,[a(U,{name:"user-admin",class:"mr-3"}),e[15]||(e[15]=n(" 用户管理 "))]),f("div",Ue,[a(i,{modelValue:V.value,"onUpdate:modelValue":e[0]||(e[0]=l=>V.value=l),placeholder:"搜索用户名或邮箱","prefix-icon":N(re),clearable:"",onInput:Q},null,8,["modelValue","prefix-icon"])]),e[17]||(e[17]=f("div",{id:"gap",class:"flex-1"},null,-1)),f("div",Ee,[a(we,{icon:"add",variant:"primary",size:"sm",onClick:e[1]||(e[1]=l=>v.value=!0)},{default:t(()=>e[16]||(e[16]=[n(" 新建用户 ")])),_:1,__:[16]})])]),f("div",ke,[a(ve,{data:O.value,columns:A,loading:k.value,pagination:W.value,stripe:"",onPageChange:J},{role:t(({row:l})=>[a(D,{type:l.is_admin?"danger":"success"},{default:t(()=>[n(E(l.is_admin?"管理员":"用户"),1)]),_:2},1032,["type"])]),status:t(({row:l})=>[a(D,{type:l.is_active?"success":"danger"},{default:t(()=>[n(E(l.is_active?"激活":"禁用"),1)]),_:2},1032,["type"])]),last_login:t(({row:l})=>[n(E(q(l.last_login)),1)]),created_at:t(({row:l})=>[n(E(q(l.created_at)),1)]),actions:t(({row:l})=>[f("div",Pe,[a(F,{content:"编辑",placement:"top"},{default:t(()=>[a(_,{text:"",size:"small",circle:"",onClick:C=>X(l),class:"action-btn-edit"},{default:t(()=>[a(U,{name:"edit"})]),_:2},1032,["onClick"])]),_:2},1024),a(F,{content:l.is_active?"禁用":"启用",placement:"top"},{default:t(()=>[a(_,{text:"",size:"small",circle:"",onClick:C=>Z(l),class:me(l.is_active?"action-btn-warning":"action-btn-success")},{default:t(()=>[a(U,{name:l.is_active?"locked":"unlocked"},null,8,["name"])]),_:2},1032,["onClick","class"])]),_:2},1032,["content"]),a(F,{content:"删除",placement:"top"},{default:t(()=>{var C;return[a(_,{text:"",size:"small",circle:"",onClick:Be=>ee(l),disabled:l.id===((C=N(j).user)==null?void 0:C.id),class:"action-btn-delete"},{default:t(()=>[a(U,{name:"delete"})]),_:2},1032,["onClick","disabled"])]}),_:2},1024)])]),_:1},8,["data","loading","pagination"])])]),a(T,{modelValue:v.value,"onUpdate:modelValue":e[8]||(e[8]=l=>v.value=l),title:"创建新用户",width:"500px","close-on-click-modal":!1},{footer:t(()=>[a(_,{onClick:e[7]||(e[7]=l=>v.value=!1),disabled:u.value},{default:t(()=>e[20]||(e[20]=[n(" 取消 ")])),_:1,__:[20]},8,["disabled"]),a(_,{type:"primary",onClick:K,loading:u.value},{default:t(()=>e[21]||(e[21]=[n(" 创建用户 ")])),_:1,__:[21]},8,["loading"])]),default:t(()=>[a(R,{ref_key:"createFormRef",ref:b,model:r,rules:G,"label-width":"100px"},{default:t(()=>[a(m,{label:"用户名",prop:"username"},{default:t(()=>[a(i,{modelValue:r.username,"onUpdate:modelValue":e[2]||(e[2]=l=>r.username=l),placeholder:"请输入用户名",disabled:u.value},null,8,["modelValue","disabled"])]),_:1}),a(m,{label:"邮箱",prop:"email"},{default:t(()=>[a(i,{modelValue:r.email,"onUpdate:modelValue":e[3]||(e[3]=l=>r.email=l),placeholder:"请输入邮箱",disabled:u.value},null,8,["modelValue","disabled"])]),_:1}),a(m,{label:"密码",prop:"password"},{default:t(()=>[a(i,{modelValue:r.password,"onUpdate:modelValue":e[4]||(e[4]=l=>r.password=l),type:"password",placeholder:"请输入密码",disabled:u.value,"show-password":""},null,8,["modelValue","disabled"])]),_:1}),a(m,{label:"确认密码",prop:"confirmPassword"},{default:t(()=>[a(i,{modelValue:r.confirmPassword,"onUpdate:modelValue":e[5]||(e[5]=l=>r.confirmPassword=l),type:"password",placeholder:"请再次输入密码",disabled:u.value,"show-password":""},null,8,["modelValue","disabled"])]),_:1}),a(m,{label:"用户角色",prop:"is_admin"},{default:t(()=>[a(L,{modelValue:r.is_admin,"onUpdate:modelValue":e[6]||(e[6]=l=>r.is_admin=l),disabled:u.value},{default:t(()=>[a(x,{label:!1},{default:t(()=>e[18]||(e[18]=[n("普通用户")])),_:1,__:[18]}),a(x,{label:!0},{default:t(()=>e[19]||(e[19]=[n("管理员")])),_:1,__:[19]})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),a(T,{modelValue:w.value,"onUpdate:modelValue":e[14]||(e[14]=l=>w.value=l),title:"编辑用户",width:"500px","close-on-click-modal":!1},{footer:t(()=>[a(_,{onClick:e[13]||(e[13]=l=>w.value=!1),disabled:c.value},{default:t(()=>e[24]||(e[24]=[n(" 取消 ")])),_:1,__:[24]},8,["disabled"]),a(_,{type:"primary",onClick:Y,loading:c.value},{default:t(()=>e[25]||(e[25]=[n(" 保存修改 ")])),_:1,__:[25]},8,["loading"])]),default:t(()=>[a(R,{ref_key:"editFormRef",ref:B,model:o,rules:H,"label-width":"100px"},{default:t(()=>[a(m,{label:"用户名",prop:"username"},{default:t(()=>[a(i,{modelValue:o.username,"onUpdate:modelValue":e[9]||(e[9]=l=>o.username=l),placeholder:"请输入用户名",disabled:c.value},null,8,["modelValue","disabled"])]),_:1}),a(m,{label:"邮箱",prop:"email"},{default:t(()=>[a(i,{modelValue:o.email,"onUpdate:modelValue":e[10]||(e[10]=l=>o.email=l),placeholder:"请输入邮箱",disabled:c.value},null,8,["modelValue","disabled"])]),_:1}),a(m,{label:"用户角色",prop:"is_admin"},{default:t(()=>[a(L,{modelValue:o.is_admin,"onUpdate:modelValue":e[11]||(e[11]=l=>o.is_admin=l),disabled:c.value},{default:t(()=>[a(x,{label:!1},{default:t(()=>e[22]||(e[22]=[n("普通用户")])),_:1,__:[22]}),a(x,{label:!0},{default:t(()=>e[23]||(e[23]=[n("管理员")])),_:1,__:[23]})]),_:1},8,["modelValue","disabled"])]),_:1}),a(m,{label:"重置密码"},{default:t(()=>[a(i,{modelValue:o.newPassword,"onUpdate:modelValue":e[12]||(e[12]=l=>o.newPassword=l),type:"password",placeholder:"留空则不修改密码",disabled:c.value,"show-password":""},null,8,["modelValue","disabled"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Ne=le($e,[["__scopeId","data-v-597c5659"]]);export{Ne as default};
