import{P as _,aY as le,W as J,X as N,a5 as ae,a7 as H,x as I,cv as se,cw as te,d as q,i as D,e as R,n as U,a0 as E,q as g,H as e,t as ne,f as M,w as m,ae as j,ah as re,cx as ie,af as de,p as Q,cy as ce,b7 as ue,a2 as W,Q as fe,cz as ve,aG as ye,cA as pe,by as Z,a as w,cB as ge,b6 as me,cC as Ce,s as K,o as be,cD as he,cE as Y,bq as Ee,y as we,ax as Ie,b1 as ke,Z as De,at as Se,A as Be,cF as Te,cG as Ae,cH as Pe,a1 as Fe,bl as $e,az as Le,cI as ze,am as Oe,aH as Re}from"./index-XMYcRHxF.js";const X=Symbol("dialogInjectionKey"),x=_({center:Boolean,alignCenter:Boolean,closeIcon:{type:le},draggable:Boolean,overflow:Boolean,fullscreen:Boolean,headerClass:String,bodyClass:String,footerClass:String,showClose:{type:Boolean,default:!0},title:{type:String,default:""},ariaLevel:{type:String,default:"2"}}),Me={close:()=>!0},Ne=N({name:"ElDialogContent"}),Ve=N({...Ne,props:x,emits:Me,setup(o,{expose:i}){const n=o,{t:F}=ae(),{Close:r}=ie,{dialogRef:f,headerRef:C,bodyId:b,ns:a,style:d}=H(X),{focusTrapRef:y}=H(ce),p=I(()=>[a.b(),a.is("fullscreen",n.fullscreen),a.is("draggable",n.draggable),a.is("align-center",n.alignCenter),{[a.m("center")]:n.center}]),h=se(y,f),c=I(()=>n.draggable),v=I(()=>n.overflow),{resetPosition:S,updatePosition:B}=te(f,C,c,v);return i({resetPosition:S,updatePosition:B}),(s,k)=>(D(),q("div",{ref:e(h),class:g(e(p)),style:Q(e(d)),tabindex:"-1"},[R("header",{ref_key:"headerRef",ref:C,class:g([e(a).e("header"),s.headerClass,{"show-close":s.showClose}])},[E(s.$slots,"header",{},()=>[R("span",{role:"heading","aria-level":s.ariaLevel,class:g(e(a).e("title"))},ne(s.title),11,["aria-level"])]),s.showClose?(D(),q("button",{key:0,"aria-label":e(F)("el.dialog.close"),class:g(e(a).e("headerbtn")),type:"button",onClick:$=>s.$emit("close")},[M(e(de),{class:g(e(a).e("close"))},{default:m(()=>[(D(),j(re(s.closeIcon||e(r))))]),_:1},8,["class"])],10,["aria-label","onClick"])):U("v-if",!0)],2),R("div",{id:e(b),class:g([e(a).e("body"),s.bodyClass])},[E(s.$slots,"default")],10,["id"]),s.$slots.footer?(D(),q("footer",{key:0,class:g([e(a).e("footer"),s.footerClass])},[E(s.$slots,"footer")],2)):U("v-if",!0)],6))}});var qe=J(Ve,[["__file","dialog-content.vue"]]);const Ke=_({...x,appendToBody:Boolean,appendTo:{type:ve.to.type,default:"body"},beforeClose:{type:fe(Function)},destroyOnClose:Boolean,closeOnClickModal:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},lockScroll:{type:Boolean,default:!0},modal:{type:Boolean,default:!0},openDelay:{type:Number,default:0},closeDelay:{type:Number,default:0},top:{type:String},modelValue:Boolean,modalClass:String,headerClass:String,bodyClass:String,footerClass:String,width:{type:[String,Number]},zIndex:{type:Number},trapFocus:Boolean,headerAriaLevel:{type:String,default:"2"}}),Ue={open:()=>!0,opened:()=>!0,close:()=>!0,closed:()=>!0,[W]:o=>ue(o),openAutoFocus:()=>!0,closeAutoFocus:()=>!0},je=(o,i)=>{var n;const r=ye().emit,{nextZIndex:f}=pe();let C="";const b=Z(),a=Z(),d=w(!1),y=w(!1),p=w(!1),h=w((n=o.zIndex)!=null?n:f());let c,v;const S=ge("namespace",he),B=I(()=>{const t={},u=`--${S.value}-dialog`;return o.fullscreen||(o.top&&(t[`${u}-margin-top`]=o.top),o.width&&(t[`${u}-width`]=me(o.width))),t}),s=I(()=>o.alignCenter?{display:"flex"}:{});function k(){r("opened")}function $(){r("closed"),r(W,!1),o.destroyOnClose&&(p.value=!1)}function V(){r("close")}function L(){v==null||v(),c==null||c(),o.openDelay&&o.openDelay>0?{stop:c}=Y(()=>z(),o.openDelay):z()}function T(){c==null||c(),v==null||v(),o.closeDelay&&o.closeDelay>0?{stop:v}=Y(()=>O(),o.closeDelay):O()}function A(){function t(u){u||(y.value=!0,d.value=!1)}o.beforeClose?o.beforeClose(t):T()}function P(){o.closeOnClickModal&&A()}function z(){Ee&&(d.value=!0)}function O(){d.value=!1}function l(){r("openAutoFocus")}function G(){r("closeAutoFocus")}function ee(t){var u;((u=t.detail)==null?void 0:u.focusReason)==="pointer"&&t.preventDefault()}o.lockScroll&&Ce(d);function oe(){o.closeOnPressEscape&&A()}return K(()=>o.zIndex,()=>{var t;h.value=(t=o.zIndex)!=null?t:f()}),K(()=>o.modelValue,t=>{var u;t?(y.value=!1,L(),p.value=!0,h.value=(u=o.zIndex)!=null?u:f(),we(()=>{r("open"),i.value&&(i.value.parentElement.scrollTop=0,i.value.parentElement.scrollLeft=0,i.value.scrollTop=0)})):d.value&&T()}),K(()=>o.fullscreen,t=>{i.value&&(t?(C=i.value.style.transform,i.value.style.transform=""):i.value.style.transform=C)}),be(()=>{o.modelValue&&(d.value=!0,p.value=!0,L())}),{afterEnter:k,afterLeave:$,beforeLeave:V,handleClose:A,onModalClick:P,close:T,doClose:O,onOpenAutoFocus:l,onCloseAutoFocus:G,onCloseRequested:oe,onFocusoutPrevented:ee,titleId:b,bodyId:a,closed:y,style:B,overlayDialogStyle:s,rendered:p,visible:d,zIndex:h}},Ge=N({name:"ElDialog",inheritAttrs:!1}),He=N({...Ge,props:Ke,emits:Ue,setup(o,{expose:i}){const n=o,F=Ie();ke({scope:"el-dialog",from:"the title slot",replacement:"the header slot",version:"3.0.0",ref:"https://element-plus.org/en-US/component/dialog.html#slots"},I(()=>!!F.title));const r=De("dialog"),f=w(),C=w(),b=w(),{visible:a,titleId:d,bodyId:y,style:p,overlayDialogStyle:h,rendered:c,zIndex:v,afterEnter:S,afterLeave:B,beforeLeave:s,handleClose:k,onModalClick:$,onOpenAutoFocus:V,onCloseAutoFocus:L,onCloseRequested:T,onFocusoutPrevented:A}=je(n,f);Oe(X,{dialogRef:f,headerRef:C,bodyId:y,ns:r,rendered:c,style:p});const P=Ae($),z=I(()=>n.draggable&&!n.fullscreen);return i({visible:a,dialogContentRef:b,resetPosition:()=>{var l;(l=b.value)==null||l.resetPosition()},handleClose:k}),(l,G)=>(D(),j(e(ze),{to:l.appendTo,disabled:l.appendTo!=="body"?!1:!l.appendToBody},{default:m(()=>[M(Se,{name:"dialog-fade",onAfterEnter:e(S),onAfterLeave:e(B),onBeforeLeave:e(s),persisted:""},{default:m(()=>[Be(M(e(Te),{"custom-mask-event":"",mask:l.modal,"overlay-class":l.modalClass,"z-index":e(v)},{default:m(()=>[R("div",{role:"dialog","aria-modal":"true","aria-label":l.title||void 0,"aria-labelledby":l.title?void 0:e(d),"aria-describedby":e(y),class:g(`${e(r).namespace.value}-overlay-dialog`),style:Q(e(h)),onClick:e(P).onClick,onMousedown:e(P).onMousedown,onMouseup:e(P).onMouseup},[M(e(Pe),{loop:"",trapped:e(a),"focus-start-el":"container",onFocusAfterTrapped:e(V),onFocusAfterReleased:e(L),onFocusoutPrevented:e(A),onReleaseRequested:e(T)},{default:m(()=>[e(c)?(D(),j(qe,Fe({key:0,ref_key:"dialogContentRef",ref:b},l.$attrs,{center:l.center,"align-center":l.alignCenter,"close-icon":l.closeIcon,draggable:e(z),overflow:l.overflow,fullscreen:l.fullscreen,"header-class":l.headerClass,"body-class":l.bodyClass,"footer-class":l.footerClass,"show-close":l.showClose,title:l.title,"aria-level":l.headerAriaLevel,onClose:e(k)}),$e({header:m(()=>[l.$slots.title?E(l.$slots,"title",{key:1}):E(l.$slots,"header",{key:0,close:e(k),titleId:e(d),titleClass:e(r).e("title")})]),default:m(()=>[E(l.$slots,"default")]),_:2},[l.$slots.footer?{name:"footer",fn:m(()=>[E(l.$slots,"footer")])}:void 0]),1040,["center","align-center","close-icon","draggable","overflow","fullscreen","header-class","body-class","footer-class","show-close","title","aria-level","onClose"])):U("v-if",!0)]),_:3},8,["trapped","onFocusAfterTrapped","onFocusAfterReleased","onFocusoutPrevented","onReleaseRequested"])],46,["aria-label","aria-labelledby","aria-describedby","onClick","onMousedown","onMouseup"])]),_:3},8,["mask","overlay-class","z-index"]),[[Le,e(a)]])]),_:3},8,["onAfterEnter","onAfterLeave","onBeforeLeave"])]),_:3},8,["to","disabled"]))}});var Ze=J(He,[["__file","dialog.vue"]]);const _e=Re(Ze);export{_e as E};
