import{d as o,e,f as n,w as r,be as a,i as l,k as i}from"./index-XMYcRHxF.js";const d={class:"page-container flex-center min-h-96"},m={class:"text-center"},c={__name:"index",setup(x){return(p,t)=>{const s=a("router-link");return l(),o("div",d,[e("div",m,[t[1]||(t[1]=e("div",{class:"text-6xl mb-4"},"404",-1)),t[2]||(t[2]=e("h1",{class:"text-2xl font-bold mb-2"},"页面未找到",-1)),t[3]||(t[3]=e("p",{class:"text-text-muted mb-6"},"抱歉，您访问的页面不存在。",-1)),n(s,{to:"/",class:"btn-primary"},{default:r(()=>t[0]||(t[0]=[i(" 返回首页 ")])),_:1,__:[0]})])])}}};export{c as default};
