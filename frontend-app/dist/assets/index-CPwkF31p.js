import{E as _e,a as ve,s as Y}from"./el-select-WZ60w1Eq.js";import{P as te,aY as ee,W as le,X as W,a5 as ie,x as U,d as S,i as f,ae as F,t as $,w as m,ah as ne,H as s,af as Q,a7 as nt,aZ as Me,aI as Ne,Q as be,Z as oe,a as D,s as J,f as l,F as H,C as Z,q,K as st,e as t,E as he,a3 as re,a_ as ot,n as L,aA as lt,a$ as Te,aD as it,j as Be,ar as G,aG as rt,b0 as ct,b1 as ut,am as dt,ak as De,b2 as ae,U as pt,aC as mt,aB as ft,aH as ye,b3 as we,b4 as gt,b5 as ze,b6 as je,a0 as pe,k as j,p as Ae,l as ce,a1 as _t,ai as Ue,b7 as de,au as me,b8 as fe,a2 as ge,R as vt,b9 as bt,a6 as ht,a9 as yt,ba as kt,bb as xt,o as Fe,bc as $t,h as Ct,y as ke,bd as Ee,aJ as St,r as qe,m as R,_ as ue,g as se,be as X,bf as Pt,bg as Tt,bh as wt,bi as Ve}from"./index-XMYcRHxF.js";import{E as xe,a as zt,b as Le,c as Oe}from"./CommonTable-3T2O5_iP.js";import{I as Re}from"./IconButton-CV2L1HCo.js";import{E as $e}from"./el-overlay-r0bpWscF.js";import{a as Et,E as Vt}from"./el-form-item-B8tg80IO.js";/* empty css                 */import{E as It}from"./el-input-number-TtivB0TA.js";import{i as Mt,a as Nt,E as Bt}from"./el-checkbox-CK4bBt59.js";import{E as Dt,a as jt}from"./el-radio-CyHT0Nel.js";import{P as At}from"./PeriodIndicatorMatrix-BVfkmiT2.js";import"./strings-D4TsBRQg.js";import"./CommonPagination-B9js4cyy.js";const We=Symbol("elPaginationKey"),Ut=te({disabled:Boolean,currentPage:{type:Number,default:1},prevText:{type:String},prevIcon:{type:ee}}),Ft={click:e=>e instanceof MouseEvent},qt=W({name:"ElPaginationPrev"}),Lt=W({...qt,props:Ut,emits:Ft,setup(e){const b=e,{t:r}=ie(),g=U(()=>b.disabled||b.currentPage<=1);return(x,T)=>(f(),S("button",{type:"button",class:"btn-prev",disabled:s(g),"aria-label":x.prevText||s(r)("el.pagination.prev"),"aria-disabled":s(g),onClick:_=>x.$emit("click",_)},[x.prevText?(f(),S("span",{key:0},$(x.prevText),1)):(f(),F(s(Q),{key:1},{default:m(()=>[(f(),F(ne(x.prevIcon)))]),_:1}))],8,["disabled","aria-label","aria-disabled","onClick"]))}});var Ot=le(Lt,[["__file","prev.vue"]]);const Rt=te({disabled:Boolean,currentPage:{type:Number,default:1},pageCount:{type:Number,default:50},nextText:{type:String},nextIcon:{type:ee}}),Wt=W({name:"ElPaginationNext"}),Kt=W({...Wt,props:Rt,emits:["click"],setup(e){const b=e,{t:r}=ie(),g=U(()=>b.disabled||b.currentPage===b.pageCount||b.pageCount===0);return(x,T)=>(f(),S("button",{type:"button",class:"btn-next",disabled:s(g),"aria-label":x.nextText||s(r)("el.pagination.next"),"aria-disabled":s(g),onClick:_=>x.$emit("click",_)},[x.nextText?(f(),S("span",{key:0},$(x.nextText),1)):(f(),F(s(Q),{key:1},{default:m(()=>[(f(),F(ne(x.nextIcon)))]),_:1}))],8,["disabled","aria-label","aria-disabled","onClick"]))}});var Ht=le(Kt,[["__file","next.vue"]]);const Ce=()=>nt(We,{}),Jt=te({pageSize:{type:Number,required:!0},pageSizes:{type:be(Array),default:()=>Ne([10,20,30,40,50,100])},popperClass:{type:String},disabled:Boolean,teleported:Boolean,size:{type:String,values:Me},appendSizeTo:String}),Gt=W({name:"ElPaginationSizes"}),Yt=W({...Gt,props:Jt,emits:["page-size-change"],setup(e,{emit:b}){const r=e,{t:g}=ie(),x=oe("pagination"),T=Ce(),_=D(r.pageSize);J(()=>r.pageSizes,(i,d)=>{if(!Mt(i,d)&&st(i)){const y=i.includes(r.pageSize)?r.pageSize:r.pageSizes[0];b("page-size-change",y)}}),J(()=>r.pageSize,i=>{_.value=i});const P=U(()=>r.pageSizes);function C(i){var d;i!==_.value&&(_.value=i,(d=T.handleSizeChange)==null||d.call(T,Number(i)))}return(i,d)=>(f(),S("span",{class:q(s(x).e("sizes"))},[l(s(ve),{"model-value":_.value,disabled:i.disabled,"popper-class":i.popperClass,size:i.size,teleported:i.teleported,"validate-event":!1,"append-to":i.appendSizeTo,onChange:C},{default:m(()=>[(f(!0),S(H,null,Z(s(P),y=>(f(),F(s(_e),{key:y,value:y,label:y+s(g)("el.pagination.pagesize")},null,8,["value","label"]))),128))]),_:1},8,["model-value","disabled","popper-class","size","teleported","append-to"])],2))}});var Zt=le(Yt,[["__file","sizes.vue"]]);const Qt=te({size:{type:String,values:Me}}),Xt=W({name:"ElPaginationJumper"}),ea=W({...Xt,props:Qt,setup(e){const{t:b}=ie(),r=oe("pagination"),{pageCount:g,disabled:x,currentPage:T,changeEvent:_}=Ce(),P=D(),C=U(()=>{var y;return(y=P.value)!=null?y:T==null?void 0:T.value});function i(y){P.value=y?+y:""}function d(y){y=Math.trunc(+y),_==null||_(y),P.value=void 0}return(y,h)=>(f(),S("span",{class:q(s(r).e("jump")),disabled:s(x)},[t("span",{class:q([s(r).e("goto")])},$(s(b)("el.pagination.goto")),3),l(s(he),{size:y.size,class:q([s(r).e("editor"),s(r).is("in-pagination")]),min:1,max:s(g),disabled:s(x),"model-value":s(C),"validate-event":!1,"aria-label":s(b)("el.pagination.page"),type:"number","onUpdate:modelValue":i,onChange:d},null,8,["size","class","max","disabled","model-value","aria-label"]),t("span",{class:q([s(r).e("classifier")])},$(s(b)("el.pagination.pageClassifier")),3)],10,["disabled"]))}});var ta=le(ea,[["__file","jumper.vue"]]);const aa=te({total:{type:Number,default:1e3}}),na=W({name:"ElPaginationTotal"}),sa=W({...na,props:aa,setup(e){const{t:b}=ie(),r=oe("pagination"),{disabled:g}=Ce();return(x,T)=>(f(),S("span",{class:q(s(r).e("total")),disabled:s(g)},$(s(b)("el.pagination.total",{total:x.total})),11,["disabled"]))}});var oa=le(sa,[["__file","total.vue"]]);const la=te({currentPage:{type:Number,default:1},pageCount:{type:Number,required:!0},pagerCount:{type:Number,default:7},disabled:Boolean}),ia=W({name:"ElPaginationPager"}),ra=W({...ia,props:la,emits:[re],setup(e,{emit:b}){const r=e,g=oe("pager"),x=oe("icon"),{t:T}=ie(),_=D(!1),P=D(!1),C=D(!1),i=D(!1),d=D(!1),y=D(!1),h=U(()=>{const v=r.pagerCount,a=(v-1)/2,o=Number(r.currentPage),V=Number(r.pageCount);let w=!1,E=!1;V>v&&(o>v-a&&(w=!0),o<V-a&&(E=!0));const z=[];if(w&&!E){const I=V-(v-2);for(let O=I;O<V;O++)z.push(O)}else if(!w&&E)for(let I=2;I<v;I++)z.push(I);else if(w&&E){const I=Math.floor(v/2)-1;for(let O=o-I;O<=o+I;O++)z.push(O)}else for(let I=2;I<V;I++)z.push(I);return z}),M=U(()=>["more","btn-quickprev",x.b(),g.is("disabled",r.disabled)]),p=U(()=>["more","btn-quicknext",x.b(),g.is("disabled",r.disabled)]),n=U(()=>r.disabled?-1:0);ot(()=>{const v=(r.pagerCount-1)/2;_.value=!1,P.value=!1,r.pageCount>r.pagerCount&&(r.currentPage>r.pagerCount-v&&(_.value=!0),r.currentPage<r.pageCount-v&&(P.value=!0))});function c(v=!1){r.disabled||(v?C.value=!0:i.value=!0)}function u(v=!1){v?d.value=!0:y.value=!0}function N(v){const a=v.target;if(a.tagName.toLowerCase()==="li"&&Array.from(a.classList).includes("number")){const o=Number(a.textContent);o!==r.currentPage&&b(re,o)}else a.tagName.toLowerCase()==="li"&&Array.from(a.classList).includes("more")&&k(v)}function k(v){const a=v.target;if(a.tagName.toLowerCase()==="ul"||r.disabled)return;let o=Number(a.textContent);const V=r.pageCount,w=r.currentPage,E=r.pagerCount-2;a.className.includes("more")&&(a.className.includes("quickprev")?o=w-E:a.className.includes("quicknext")&&(o=w+E)),Number.isNaN(+o)||(o<1&&(o=1),o>V&&(o=V)),o!==w&&b(re,o)}return(v,a)=>(f(),S("ul",{class:q(s(g).b()),onClick:k,onKeyup:Be(N,["enter"])},[v.pageCount>0?(f(),S("li",{key:0,class:q([[s(g).is("active",v.currentPage===1),s(g).is("disabled",v.disabled)],"number"]),"aria-current":v.currentPage===1,"aria-label":s(T)("el.pagination.currentPage",{pager:1}),tabindex:s(n)}," 1 ",10,["aria-current","aria-label","tabindex"])):L("v-if",!0),_.value?(f(),S("li",{key:1,class:q(s(M)),tabindex:s(n),"aria-label":s(T)("el.pagination.prevPages",{pager:v.pagerCount-2}),onMouseenter:o=>c(!0),onMouseleave:o=>C.value=!1,onFocus:o=>u(!0),onBlur:o=>d.value=!1},[(C.value||d.value)&&!v.disabled?(f(),F(s(lt),{key:0})):(f(),F(s(Te),{key:1}))],42,["tabindex","aria-label","onMouseenter","onMouseleave","onFocus","onBlur"])):L("v-if",!0),(f(!0),S(H,null,Z(s(h),o=>(f(),S("li",{key:o,class:q([[s(g).is("active",v.currentPage===o),s(g).is("disabled",v.disabled)],"number"]),"aria-current":v.currentPage===o,"aria-label":s(T)("el.pagination.currentPage",{pager:o}),tabindex:s(n)},$(o),11,["aria-current","aria-label","tabindex"]))),128)),P.value?(f(),S("li",{key:2,class:q(s(p)),tabindex:s(n),"aria-label":s(T)("el.pagination.nextPages",{pager:v.pagerCount-2}),onMouseenter:o=>c(),onMouseleave:o=>i.value=!1,onFocus:o=>u(),onBlur:o=>y.value=!1},[(i.value||y.value)&&!v.disabled?(f(),F(s(it),{key:0})):(f(),F(s(Te),{key:1}))],42,["tabindex","aria-label","onMouseenter","onMouseleave","onFocus","onBlur"])):L("v-if",!0),v.pageCount>1?(f(),S("li",{key:3,class:q([[s(g).is("active",v.currentPage===v.pageCount),s(g).is("disabled",v.disabled)],"number"]),"aria-current":v.currentPage===v.pageCount,"aria-label":s(T)("el.pagination.currentPage",{pager:v.pageCount}),tabindex:s(n)},$(v.pageCount),11,["aria-current","aria-label","tabindex"])):L("v-if",!0)],42,["onKeyup"]))}});var ca=le(ra,[["__file","pager.vue"]]);const K=e=>typeof e!="number",ua=te({pageSize:Number,defaultPageSize:Number,total:Number,pageCount:Number,pagerCount:{type:Number,validator:e=>G(e)&&Math.trunc(e)===e&&e>4&&e<22&&e%2===1,default:7},currentPage:Number,defaultCurrentPage:Number,layout:{type:String,default:["prev","pager","next","jumper","->","total"].join(", ")},pageSizes:{type:be(Array),default:()=>Ne([10,20,30,40,50,100])},popperClass:{type:String,default:""},prevText:{type:String,default:""},prevIcon:{type:ee,default:()=>ft},nextText:{type:String,default:""},nextIcon:{type:ee,default:()=>mt},teleported:{type:Boolean,default:!0},small:Boolean,size:pt,background:Boolean,disabled:Boolean,hideOnSinglePage:Boolean,appendSizeTo:String}),da={"update:current-page":e=>G(e),"update:page-size":e=>G(e),"size-change":e=>G(e),change:(e,b)=>G(e)&&G(b),"current-change":e=>G(e),"prev-click":e=>G(e),"next-click":e=>G(e)},Ie="ElPagination";var pa=W({name:Ie,props:ua,emits:da,setup(e,{emit:b,slots:r}){const{t:g}=ie(),x=oe("pagination"),T=rt().vnode.props||{},_=ct(),P=U(()=>{var a;return e.small?"small":(a=e.size)!=null?a:_.value});ut({from:"small",replacement:"size",version:"3.0.0",scope:"el-pagination",ref:"https://element-plus.org/zh-CN/component/pagination.html"},U(()=>!!e.small));const C="onUpdate:currentPage"in T||"onUpdate:current-page"in T||"onCurrentChange"in T,i="onUpdate:pageSize"in T||"onUpdate:page-size"in T||"onSizeChange"in T,d=U(()=>{if(K(e.total)&&K(e.pageCount)||!K(e.currentPage)&&!C)return!1;if(e.layout.includes("sizes")){if(K(e.pageCount)){if(!K(e.total)&&!K(e.pageSize)&&!i)return!1}else if(!i)return!1}return!0}),y=D(K(e.defaultPageSize)?10:e.defaultPageSize),h=D(K(e.defaultCurrentPage)?1:e.defaultCurrentPage),M=U({get(){return K(e.pageSize)?y.value:e.pageSize},set(a){K(e.pageSize)&&(y.value=a),i&&(b("update:page-size",a),b("size-change",a))}}),p=U(()=>{let a=0;return K(e.pageCount)?K(e.total)||(a=Math.max(1,Math.ceil(e.total/M.value))):a=e.pageCount,a}),n=U({get(){return K(e.currentPage)?h.value:e.currentPage},set(a){let o=a;a<1?o=1:a>p.value&&(o=p.value),K(e.currentPage)&&(h.value=o),C&&(b("update:current-page",o),b("current-change",o))}});J(p,a=>{n.value>a&&(n.value=a)}),J([n,M],a=>{b(re,...a)},{flush:"post"});function c(a){n.value=a}function u(a){M.value=a;const o=p.value;n.value>o&&(n.value=o)}function N(){e.disabled||(n.value-=1,b("prev-click",n.value))}function k(){e.disabled||(n.value+=1,b("next-click",n.value))}function v(a,o){a&&(a.props||(a.props={}),a.props.class=[a.props.class,o].join(" "))}return dt(We,{pageCount:p,disabled:U(()=>e.disabled),currentPage:n,changeEvent:c,handleSizeChange:u}),()=>{var a,o;if(!d.value)return De(Ie,g("el.pagination.deprecationWarning")),null;if(!e.layout||e.hideOnSinglePage&&p.value<=1)return null;const V=[],w=[],E=ae("div",{class:x.e("rightwrapper")},w),z={prev:ae(Ot,{disabled:e.disabled,currentPage:n.value,prevText:e.prevText,prevIcon:e.prevIcon,onClick:N}),jumper:ae(ta,{size:P.value}),pager:ae(ca,{currentPage:n.value,pageCount:p.value,pagerCount:e.pagerCount,onChange:c,disabled:e.disabled}),next:ae(Ht,{disabled:e.disabled,currentPage:n.value,pageCount:p.value,nextText:e.nextText,nextIcon:e.nextIcon,onClick:k}),sizes:ae(Zt,{pageSize:M.value,pageSizes:e.pageSizes,popperClass:e.popperClass,disabled:e.disabled,teleported:e.teleported,size:P.value,appendSizeTo:e.appendSizeTo}),slot:(o=(a=r==null?void 0:r.default)==null?void 0:a.call(r))!=null?o:null,total:ae(oa,{total:K(e.total)?0:e.total})},I=e.layout.split(",").map(A=>A.trim());let O=!1;return I.forEach(A=>{if(A==="->"){O=!0;return}O?w.push(z[A]):V.push(z[A])}),v(V[0],x.is("first")),v(V[V.length-1],x.is("last")),O&&w.length>0&&(v(w[0],x.is("first")),v(w[w.length-1],x.is("last")),V.push(E)),ae("div",{class:[x.b(),x.is("background",e.background),x.m(P.value)]},V)}}});const ma=ye(pa),fa=te({title:String,confirmButtonText:String,cancelButtonText:String,confirmButtonType:{type:String,values:ze,default:"primary"},cancelButtonType:{type:String,values:ze,default:"text"},icon:{type:ee,default:()=>gt},iconColor:{type:String,default:"#f90"},hideIcon:{type:Boolean,default:!1},hideAfter:{type:Number,default:200},teleported:we.teleported,persistent:we.persistent,width:{type:[String,Number],default:150}}),ga={confirm:e=>e instanceof MouseEvent,cancel:e=>e instanceof MouseEvent},_a=W({name:"ElPopconfirm"}),va=W({..._a,props:fa,emits:ga,setup(e,{emit:b}){const r=e,{t:g}=ie(),x=oe("popconfirm"),T=D(),_=()=>{var h,M;(M=(h=T.value)==null?void 0:h.onClose)==null||M.call(h)},P=U(()=>({width:je(r.width)})),C=h=>{b("confirm",h),_()},i=h=>{b("cancel",h),_()},d=U(()=>r.confirmButtonText||g("el.popconfirm.confirmButtonText")),y=U(()=>r.cancelButtonText||g("el.popconfirm.cancelButtonText"));return(h,M)=>(f(),F(s(Ue),_t({ref_key:"tooltipRef",ref:T,trigger:"click",effect:"light"},h.$attrs,{"popper-class":`${s(x).namespace.value}-popover`,"popper-style":s(P),teleported:h.teleported,"fallback-placements":["bottom","top","right","left"],"hide-after":h.hideAfter,persistent:h.persistent}),{content:m(()=>[t("div",{class:q(s(x).b())},[t("div",{class:q(s(x).e("main"))},[!h.hideIcon&&h.icon?(f(),F(s(Q),{key:0,class:q(s(x).e("icon")),style:Ae({color:h.iconColor})},{default:m(()=>[(f(),F(ne(h.icon)))]),_:1},8,["class","style"])):L("v-if",!0),j(" "+$(h.title),1)],2),t("div",{class:q(s(x).e("action"))},[pe(h.$slots,"actions",{confirm:C,cancel:i},()=>[l(s(ce),{size:"small",type:h.cancelButtonType==="text"?"":h.cancelButtonType,text:h.cancelButtonType==="text",onClick:i},{default:m(()=>[j($(s(y)),1)]),_:1},8,["type","text"]),l(s(ce),{size:"small",type:h.confirmButtonType==="text"?"":h.confirmButtonType,text:h.confirmButtonType==="text",onClick:C},{default:m(()=>[j($(s(d)),1)]),_:1},8,["type","text"])])],2)],2)]),default:m(()=>[h.$slots.reference?pe(h.$slots,"reference",{key:0}):L("v-if",!0)]),_:3},16,["popper-class","popper-style","teleported","hide-after","persistent"]))}});var ba=le(va,[["__file","popconfirm.vue"]]);const ha=ye(ba),ya=te({modelValue:{type:[Boolean,String,Number],default:!1},disabled:Boolean,loading:Boolean,size:{type:String,validator:bt},width:{type:[String,Number],default:""},inlinePrompt:Boolean,inactiveActionIcon:{type:ee},activeActionIcon:{type:ee},activeIcon:{type:ee},inactiveIcon:{type:ee},activeText:{type:String,default:""},inactiveText:{type:String,default:""},activeValue:{type:[Boolean,String,Number],default:!0},inactiveValue:{type:[Boolean,String,Number],default:!1},name:{type:String,default:""},validateEvent:{type:Boolean,default:!0},beforeChange:{type:be(Function)},id:String,tabindex:{type:[String,Number]},...vt(["ariaLabel"])}),ka={[ge]:e=>de(e)||me(e)||G(e),[re]:e=>de(e)||me(e)||G(e),[fe]:e=>de(e)||me(e)||G(e)},Ke="ElSwitch",xa=W({name:Ke}),$a=W({...xa,props:ya,emits:ka,setup(e,{expose:b,emit:r}){const g=e,{formItem:x}=ht(),T=yt(),_=oe("switch"),{inputId:P}=kt(g,{formItemContext:x}),C=xt(U(()=>g.loading)),i=D(g.modelValue!==!1),d=D(),y=D(),h=U(()=>[_.b(),_.m(T.value),_.is("disabled",C.value),_.is("checked",u.value)]),M=U(()=>[_.e("label"),_.em("label","left"),_.is("active",!u.value)]),p=U(()=>[_.e("label"),_.em("label","right"),_.is("active",u.value)]),n=U(()=>({width:je(g.width)}));J(()=>g.modelValue,()=>{i.value=!0});const c=U(()=>i.value?g.modelValue:!1),u=U(()=>c.value===g.activeValue);[g.activeValue,g.inactiveValue].includes(c.value)||(r(ge,g.inactiveValue),r(re,g.inactiveValue),r(fe,g.inactiveValue)),J(u,a=>{var o;d.value.checked=a,g.validateEvent&&((o=x==null?void 0:x.validate)==null||o.call(x,"change").catch(V=>De()))});const N=()=>{const a=u.value?g.inactiveValue:g.activeValue;r(ge,a),r(re,a),r(fe,a),ke(()=>{d.value.checked=u.value})},k=()=>{if(C.value)return;const{beforeChange:a}=g;if(!a){N();return}const o=a();[Ee(o),de(o)].includes(!0)||St(Ke,"beforeChange must return type `Promise<boolean>` or `boolean`"),Ee(o)?o.then(w=>{w&&N()}).catch(w=>{}):o&&N()},v=()=>{var a,o;(o=(a=d.value)==null?void 0:a.focus)==null||o.call(a)};return Fe(()=>{d.value.checked=u.value}),b({focus:v,checked:u}),(a,o)=>(f(),S("div",{class:q(s(h)),onClick:Ct(k,["prevent"])},[t("input",{id:s(P),ref_key:"input",ref:d,class:q(s(_).e("input")),type:"checkbox",role:"switch","aria-checked":s(u),"aria-disabled":s(C),"aria-label":a.ariaLabel,name:a.name,"true-value":a.activeValue,"false-value":a.inactiveValue,disabled:s(C),tabindex:a.tabindex,onChange:N,onKeydown:Be(k,["enter"])},null,42,["id","aria-checked","aria-disabled","aria-label","name","true-value","false-value","disabled","tabindex","onKeydown"]),!a.inlinePrompt&&(a.inactiveIcon||a.inactiveText)?(f(),S("span",{key:0,class:q(s(M))},[a.inactiveIcon?(f(),F(s(Q),{key:0},{default:m(()=>[(f(),F(ne(a.inactiveIcon)))]),_:1})):L("v-if",!0),!a.inactiveIcon&&a.inactiveText?(f(),S("span",{key:1,"aria-hidden":s(u)},$(a.inactiveText),9,["aria-hidden"])):L("v-if",!0)],2)):L("v-if",!0),t("span",{ref_key:"core",ref:y,class:q(s(_).e("core")),style:Ae(s(n))},[a.inlinePrompt?(f(),S("div",{key:0,class:q(s(_).e("inner"))},[a.activeIcon||a.inactiveIcon?(f(),F(s(Q),{key:0,class:q(s(_).is("icon"))},{default:m(()=>[(f(),F(ne(s(u)?a.activeIcon:a.inactiveIcon)))]),_:1},8,["class"])):a.activeText||a.inactiveText?(f(),S("span",{key:1,class:q(s(_).is("text")),"aria-hidden":!s(u)},$(s(u)?a.activeText:a.inactiveText),11,["aria-hidden"])):L("v-if",!0)],2)):L("v-if",!0),t("div",{class:q(s(_).e("action"))},[a.loading?(f(),F(s(Q),{key:0,class:q(s(_).is("loading"))},{default:m(()=>[l(s($t))]),_:1},8,["class"])):s(u)?pe(a.$slots,"active-action",{key:1},()=>[a.activeActionIcon?(f(),F(s(Q),{key:0},{default:m(()=>[(f(),F(ne(a.activeActionIcon)))]),_:1})):L("v-if",!0)]):s(u)?L("v-if",!0):pe(a.$slots,"inactive-action",{key:2},()=>[a.inactiveActionIcon?(f(),F(s(Q),{key:0},{default:m(()=>[(f(),F(ne(a.inactiveActionIcon)))]),_:1})):L("v-if",!0)])],2)],6),!a.inlinePrompt&&(a.activeIcon||a.activeText)?(f(),S("span",{key:1,class:q(s(p))},[a.activeIcon?(f(),F(s(Q),{key:0},{default:m(()=>[(f(),F(ne(a.activeIcon)))]),_:1})):L("v-if",!0),!a.activeIcon&&a.activeText?(f(),S("span",{key:1,"aria-hidden":!s(u)},$(a.activeText),9,["aria-hidden"])):L("v-if",!0)],2)):L("v-if",!0)],10,["onClick"]))}});var Ca=le($a,[["__file","switch.vue"]]);const He=ye(Ca);function Sa(){const e=D([]),b=D(!1),r=qe({current:1,pageSize:20,total:0}),g=async(i={})=>{b.value=!0;try{const d=await Y.getTasks({skip:(r.current-1)*r.pageSize,limit:r.pageSize,...i});e.value=d.data||d,d.total!==void 0&&(r.total=d.total)}catch(d){R.error("加载任务列表失败"),console.error(d)}finally{b.value=!1}};return{tasks:e,loading:b,pagination:r,loadTasks:g,createTask:async i=>{try{await Y.createTask(i),R.success("任务创建成功")}catch(d){throw R.error("任务创建失败"),d}},updateTask:async(i,d)=>{try{await Y.updateTask(i,d),R.success("任务更新成功")}catch(y){throw R.error("任务更新失败"),y}},deleteTask:async i=>{try{await Y.deleteTask(i),R.success("任务删除成功"),g()}catch(d){if(d!=="cancel")throw R.error("任务删除失败"),d}},executeTask:async i=>{try{return await Y.executeTask(i)}catch(d){throw R.error("任务执行失败"),d}},toggleTaskStatus:async(i,d)=>{try{await Y.updateTask(i,{is_active:d}),R.success(d?"任务已启用":"任务已禁用"),await g()}catch(y){throw R.error("状态更新失败"),y}}}}function Je(){const e=D([]),b=D(!1),r=qe({current:1,pageSize:20,total:0}),g=async(d=null,y={})=>{var h,M;b.value=!0;try{const p={skip:(r.current-1)*r.pageSize,limit:r.pageSize,...y};let n;d?n=await Y.getExecutions(d,p):n=await Y.getAllExecutions(p),e.value=((h=n.data)==null?void 0:h.items)||n.items||n,r.total=((M=n.data)==null?void 0:M.total)||n.total||0}catch(p){R.error("加载执行记录失败"),console.error(p)}finally{b.value=!1}};return{executions:e,loading:b,pagination:r,loadExecutions:g,getExecutionDetail:async d=>{try{const y=await Y.getExecutionDetail(d);return y.data||y}catch(y){throw R.error("获取执行详情失败"),y}},deleteExecution:async d=>{try{await Y.deleteExecution(d),R.success("执行记录删除成功"),await g()}catch(y){throw R.error("删除执行记录失败"),y}},cancelExecution:async d=>{try{await Y.cancelExecution(d),R.success("任务已取消"),await g()}catch(y){throw R.error("取消任务失败"),y}},getStatusText:d=>({pending:"待执行",running:"执行中",completed:"已完成",failed:"失败",cancelled:"已取消"})[d]||d,getStatusType:d=>({pending:"warning",running:"primary",completed:"success",failed:"danger",cancelled:"info"})[d]||"info",getTriggerTypeText:d=>({scheduled:"定时触发",manual:"手动触发"})[d]||d}}const Pa={class:"task-list"},Ta={class:"task-list-container"},wa={class:"task-list-header"},za={class:"header-content"},Ea={class:"header-actions"},Va={class:"task-list-content"},Ia={class:"task-name-cell"},Ma={class:"task-name"},Na={class:"task-description"},Ba={class:"cron-time-cell"},Da={class:"cron-expression"},ja={class:"cron-description"},Aa={class:"task-config-cell"},Ua={class:"config-summary"},Fa={class:"execution-count-cell"},qa={class:"execution-count"},La={class:"execution-limit"},Oa={class:"next-execution-cell"},Ra={class:"last-execution-cell"},Wa={class:"table-actions"},Ka={__name:"TaskList",props:{tasks:{type:Array,default:()=>[]},loading:{type:Boolean,default:!1}},emits:["edit","delete","execute","toggle-status","view-executions","refresh"],setup(e,{emit:b}){const r=b,g=[{prop:"name",label:"任务名称",minWidth:120,slot:"name"},{prop:"task_type",label:"任务类型",minWidth:100,slot:"task_type"},{prop:"cron_expression",label:"执行时间",minWidth:120,slot:"cron_expression"},{prop:"task_config",label:"任务配置",minWidth:180,slot:"task_config"},{prop:"is_active",label:"状态",minWidth:120,slot:"is_active"},{prop:"current_executions",label:"执行次数",minWidth:120,slot:"current_executions"},{prop:"next_execution",label:"下次执行",minWidth:140,slot:"next_execution"},{prop:"last_execution",label:"最后执行",minWidth:140,slot:"last_execution"}],x=i=>({indicator_scan:"指标扫描",ai_analysis:"AI分析"})[i]||i,T=i=>({"* * * * *":"每分钟","0 * * * *":"每小时","0 9 * * *":"每天9点","0 18 * * *":"每天18点","0 9 * * MON":"每周一9点","0 9 1 * *":"每月1号9点","0 9 * * MON-FRI":"工作日9点","*/5 * * * *":"每5分钟","*/30 * * * *":"每30分钟"})[i]||"自定义时间",_=i=>{if(!i||typeof i!="object")return"-";try{if(i.indicators&&Array.isArray(i.indicators)){const d=i.indicators,y=i.periods||["d"],h=i.stock_codes,M={volume_pressure:"成交量压力",kdj:"KDJ指标",bollinger:"布林带",macd:"MACD",rsi:"RSI指标",arbr:"ARBR指标"},p={d:"日线",w:"周线",m:"月线"},n=d.map(N=>M[N]||N).join("、"),c=y.map(N=>p[N]||N).join("、"),u=h&&h.length>0?`指定${h.length}只股票`:"全市场";return`${n} | ${c} | ${u}`}return"自定义配置"}catch{return"配置解析失败"}},P=i=>i?new Date(i).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"",C=async i=>{var d,y;try{const h=i.is_active;await Y.toggleTaskStatus(i.id,i.is_active),R.success(`任务已${i.is_active?"启用":"禁用"}`),r("refresh")}catch(h){i.is_active=!i.is_active,R.error(((y=(d=h.response)==null?void 0:d.data)==null?void 0:y.detail)||"操作失败")}};return(i,d)=>{const y=xe,h=He,M=ce,p=Ue,n=ha;return f(),S("div",Pa,[t("div",Ta,[t("div",wa,[t("div",za,[d[2]||(d[2]=t("h3",{class:"list-title"},"任务列表",-1)),t("div",Ea,[l(Re,{icon:"refresh",variant:"ghost",size:"sm",onClick:d[0]||(d[0]=c=>i.$emit("refresh"))},{default:m(()=>d[1]||(d[1]=[j(" 刷新 ")])),_:1,__:[1]})])])]),t("div",Va,[l(zt,{data:e.tasks,loading:e.loading,columns:g,"show-pagination":!1,stripe:!0,actionsMinWidth:180,"empty-text":"暂无任务数据",class:"task-table"},{name:m(({row:c})=>[t("div",Ia,[t("div",Ma,$(c.name),1),t("div",Na,$(c.description||"无描述"),1)])]),task_type:m(({row:c})=>[l(y,{size:"small",type:"info",class:"task-type-tag"},{default:m(()=>[j($(x(c.task_type)),1)]),_:2},1024)]),cron_expression:m(({row:c})=>[t("div",Ba,[t("div",Da,$(c.cron_expression),1),t("div",ja,$(T(c.cron_expression)),1)])]),task_config:m(({row:c})=>[t("div",Aa,[t("div",Ua,$(_(c.task_config)),1)])]),is_active:m(({row:c})=>[l(h,{modelValue:c.is_active,"onUpdate:modelValue":u=>c.is_active=u,onChange:u=>C(c),"active-text":"启用","inactive-text":"禁用",size:"small",class:"task-status-switch"},null,8,["modelValue","onUpdate:modelValue","onChange"])]),current_executions:m(({row:c})=>[t("div",Fa,[t("div",qa,$(c.current_executions),1),t("div",La,$(c.max_executions?`/ ${c.max_executions}`:"/ 无限制"),1)])]),next_execution:m(({row:c})=>[t("div",Oa,$(c.next_execution?P(c.next_execution):"已停用"),1)]),last_execution:m(({row:c})=>[t("div",Ra,$(c.last_execution?P(c.last_execution):"未执行"),1)]),actions:m(({row:c})=>[t("div",Wa,[l(p,{content:"立即执行",placement:"top"},{default:m(()=>[l(M,{text:"",size:"small",circle:"",onClick:u=>i.$emit("execute",c.id),class:"action-btn-execute"},{default:m(()=>[l(se,{name:"play"})]),_:2},1032,["onClick"])]),_:2},1024),l(p,{content:"查看记录",placement:"top"},{default:m(()=>[l(M,{text:"",size:"small",circle:"",onClick:u=>i.$emit("view-executions",c.id),class:"action-btn-view"},{default:m(()=>[l(se,{name:"list"})]),_:2},1032,["onClick"])]),_:2},1024),l(p,{content:"编辑任务",placement:"top"},{default:m(()=>[l(M,{text:"",size:"small",circle:"",onClick:u=>i.$emit("edit",c),class:"action-btn-edit"},{default:m(()=>[l(se,{name:"edit"})]),_:2},1032,["onClick"])]),_:2},1024),l(p,{content:"删除任务",placement:"top",disabled:!1},{default:m(()=>[l(n,{title:"确认删除此任务吗？","confirm-button-text":"确认","cancel-button-text":"取消",width:"220",placement:"bottom",onConfirm:u=>i.$emit("delete",c.id)},{reference:m(()=>[l(M,{text:"",size:"small",circle:"",class:"action-btn-delete"},{default:m(()=>[l(se,{name:"delete"})]),_:1})]),_:2},1032,["onConfirm"])]),_:2},1024)])]),_:1},8,["data","loading"])])])])}}},Ha=ue(Ka,[["__scopeId","data-v-2b84186b"]]),Ja={presets:{"0 9 * * *":"每天早上9点执行","0 12 * * *":"每天中午12点执行","0 18 * * *":"每天下午6点执行","0 9 * * 1-5":"工作日早上9点执行","30 14 * * *":"每天下午2:30执行","0 8,12,16 * * 1-5":"工作日8点、12点、16点执行","0 9 1 * *":"每月1号早上9点执行","0 9 * * 1":"每周一早上9点执行","0 */4 * * *":"每4小时执行一次","*/15 * * * *":"每15分钟执行一次"},getCronDescription(e){if(!e||typeof e!="string")return"无效的Cron表达式";if(this.presets[e])return this.presets[e];const b=e.trim().split(/\s+/);if(b.length!==5)return"无效的Cron表达式格式";const[r,g,x,T,_]=b;try{let P="";const C=this.parseMinute(r),i=this.parseHour(g),d=this.parseDay(x),y=this.parseMonth(T),h=this.parseWeekday(_);return h&&h!=="每天"?P=h:d&&d!=="每日"?P=d:P="每天",y&&y!=="每月"&&(P=y+P),P+=i+C,P||"自定义时间"}catch{return"无效的Cron表达式"}},parseMinute(e){return e==="*"||e==="0"?"":e.includes("/")?`:${e.split("/")[1]}分钟间隔`:e.includes(",")?`:${e.split(",").join("、")}分`:`:${e}分`},parseHour(e){if(e==="*")return"每小时";if(e.includes("/"))return`每${e.split("/")[1]}小时`;if(e.includes(","))return e.split(",").map(r=>`${r}点`).join("、");if(e.includes("-")){const[b,r]=e.split("-");return`${b}点到${r}点每小时`}return`${e}点`},parseDay(e){return e==="*"?"":e==="1"?"每月1号":e.includes("/")?`每${e.split("/")[1]}天`:e.includes(",")?e.split(",").map(r=>`${r}号`).join("、"):`${e}号`},parseMonth(e){if(e==="*")return"";const b=["","1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"];return e.includes(",")?e.split(",").map(g=>b[parseInt(g)]).join("、"):b[parseInt(e)]||e+"月"},parseWeekday(e){if(e==="*")return"";const b=["周日","周一","周二","周三","周四","周五","周六","周日"];if(e==="1-5")return"工作日";if(e==="6,0"||e==="0,6")return"周末";if(e.includes(","))return e.split(",").map(g=>b[parseInt(g)]).join("、");if(e.includes("-")){const[r,g]=e.split("-");return`${b[parseInt(r)]}到${b[parseInt(g)]}`}return b[parseInt(e)]||"每天"}},Ga={class:"cron-builder"},Ya={class:"cron-display-section"},Za={class:"cron-expression-display"},Qa={class:"cron-value"},Xa={class:"cron-description"},en={class:"description-text"},tn={class:"cron-builder-section"},an={class:"builder-tabs"},nn=["onClick"],sn={class:"builder-content"},on={key:0,class:"simple-mode"},ln={class:"time-picker-row"},rn={class:"time-input-group"},cn={class:"time-input-group"},un={class:"frequency-section"},dn={class:"frequency-options"},pn=["onClick"],mn={class:"frequency-icon"},fn={class:"frequency-text"},gn={key:1,class:"advanced-mode"},_n={class:"cron-field-grid"},vn={class:"field-label"},bn={class:"field-help"},hn={key:2,class:"preset-mode"},yn={class:"preset-grid"},kn=["onClick"],xn={class:"preset-icon"},$n={class:"preset-title"},Cn={class:"preset-description"},Sn={__name:"CronBuilder",props:{modelValue:{type:String,default:"0 9 * * *"}},emits:["update:modelValue"],setup(e,{emit:b}){const r=e,g=b,x=D("simple"),T=[{key:"simple",label:"简单模式"},{key:"preset",label:"常用预设"},{key:"advanced",label:"高级模式"}],_=D({hour:9,minute:0,frequency:"daily"}),P=D({minute:"0",hour:"9",day:"*",month:"*",weekday:"*"}),C=[{value:"daily",label:"每天",icon:"📅"},{value:"weekdays",label:"工作日",icon:"💼"},{value:"weekly",label:"每周",icon:"📆"},{value:"monthly",label:"每月",icon:"🗓️"}],i=[{name:"minute",label:"分钟",placeholder:"0-59",help:"0-59 或 * 表示任意"},{name:"hour",label:"小时",placeholder:"0-23",help:"0-23 或 * 表示任意"},{name:"day",label:"日期",placeholder:"1-31",help:"1-31 或 * 表示任意"},{name:"month",label:"月份",placeholder:"1-12",help:"1-12 或 * 表示任意"},{name:"weekday",label:"星期",placeholder:"0-7",help:"0-7 (0和7都表示周日) 或 * 表示任意"}],d=[{value:"0 9 * * *",label:"每日早上9点",description:"适合开盘前扫描",icon:"🌅"},{value:"30 14 * * *",label:"每日下午2:30",description:"适合午盘分析",icon:"🕐"},{value:"0 9 * * 1-5",label:"工作日早上9点",description:"仅交易日执行",icon:"💼"},{value:"0 8,12,16 * * 1-5",label:"工作日多次扫描",description:"8点、12点、16点",icon:"⏰"},{value:"0 9 1 * *",label:"每月1号早上9点",description:"月度扫描",icon:"🗓️"},{value:"0 9 * * 1",label:"每周一早上9点",description:"周度扫描",icon:"📅"}],y=U(()=>Ja.getCronDescription(r.modelValue));J(_,p=>{if(x.value==="simple"){let n="";switch(p.frequency){case"daily":n=`${p.minute} ${p.hour} * * *`;break;case"weekdays":n=`${p.minute} ${p.hour} * * 1-5`;break;case"weekly":n=`${p.minute} ${p.hour} * * 1`;break;case"monthly":n=`${p.minute} ${p.hour} 1 * *`;break}g("update:modelValue",n)}},{deep:!0}),J(P,p=>{if(x.value==="advanced"){const n=`${p.minute} ${p.hour} ${p.day} ${p.month} ${p.weekday}`;g("update:modelValue",n)}},{deep:!0});const h=p=>{g("update:modelValue",p)},M=p=>{const n=p.split(" ");n.length===5&&(_.value.minute=parseInt(n[0])||0,_.value.hour=parseInt(n[1])||9,n[4]==="1-5"?_.value.frequency="weekdays":n[4]==="1"?_.value.frequency="weekly":n[2]==="1"?_.value.frequency="monthly":_.value.frequency="daily",P.value.minute=n[0],P.value.hour=n[1],P.value.day=n[2],P.value.month=n[3],P.value.weekday=n[4])};return M(r.modelValue),J(()=>r.modelValue,p=>{M(p)}),(p,n)=>{const c=_e,u=ve,N=he;return f(),S("div",Ga,[t("div",Ya,[t("div",Za,[n[2]||(n[2]=t("div",{class:"cron-label"},"Cron 表达式:",-1)),t("div",Qa,$(e.modelValue),1)]),t("div",Xa,[n[3]||(n[3]=t("div",{class:"description-label"},"执行时间:",-1)),t("div",en,$(y.value),1)])]),t("div",tn,[t("div",an,[(f(),S(H,null,Z(T,k=>t("div",{key:k.key,class:q(["builder-tab",{"builder-tab--active":x.value===k.key}]),onClick:v=>x.value=k.key},$(k.label),11,nn)),64))]),t("div",sn,[x.value==="simple"?(f(),S("div",on,[t("div",ln,[t("div",rn,[n[4]||(n[4]=t("label",{class:"input-label"},"小时",-1)),l(u,{modelValue:_.value.hour,"onUpdate:modelValue":n[0]||(n[0]=k=>_.value.hour=k),class:"time-select"},{default:m(()=>[(f(),S(H,null,Z(24,k=>l(c,{key:k-1,label:String(k-1).padStart(2,"0"),value:k-1},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),n[6]||(n[6]=t("div",{class:"time-separator"},":",-1)),t("div",cn,[n[5]||(n[5]=t("label",{class:"input-label"},"分钟",-1)),l(u,{modelValue:_.value.minute,"onUpdate:modelValue":n[1]||(n[1]=k=>_.value.minute=k),class:"time-select"},{default:m(()=>[(f(),S(H,null,Z([0,15,30,45],k=>l(c,{key:k,label:String(k).padStart(2,"0"),value:k},null,8,["label","value"])),64))]),_:1},8,["modelValue"])])]),t("div",un,[n[7]||(n[7]=t("label",{class:"section-label"},"执行频率",-1)),t("div",dn,[(f(),S(H,null,Z(C,k=>t("div",{key:k.value,class:q(["frequency-option",{"frequency-option--active":_.value.frequency===k.value}]),onClick:v=>_.value.frequency=k.value},[t("div",mn,$(k.icon),1),t("div",fn,$(k.label),1)],10,pn)),64))])])])):x.value==="advanced"?(f(),S("div",gn,[t("div",_n,[(f(),S(H,null,Z(i,k=>t("div",{key:k.name,class:"cron-field"},[t("label",vn,$(k.label),1),l(N,{modelValue:P.value[k.name],"onUpdate:modelValue":v=>P.value[k.name]=v,placeholder:k.placeholder,class:"field-input"},null,8,["modelValue","onUpdate:modelValue","placeholder"]),t("div",bn,$(k.help),1)])),64))])])):x.value==="preset"?(f(),S("div",hn,[t("div",yn,[(f(),S(H,null,Z(d,k=>t("div",{key:k.value,class:q(["preset-card",{"preset-card--active":e.modelValue===k.value}]),onClick:v=>h(k.value)},[t("div",xn,$(k.icon),1),t("div",$n,$(k.label),1),t("div",Cn,$(k.description),1)],10,kn)),64))])])):L("",!0)])])])}}},Pn=ue(Sn,[["__scopeId","data-v-02f88ed7"]]),Tn={class:"cron-builder-wrapper"},wn={class:"w-full"},zn={class:"form-help-text"},En={class:"max-executions-control"},Vn={class:"dialog-footer"},In={__name:"TaskForm",props:{modelValue:{type:Boolean,default:!1},task:{type:Object,default:null}},emits:["update:modelValue","submit","cancel"],setup(e,{emit:b}){const r=e,g=b,x=D(null),T=D("all"),_=D(""),P=D(!1),C=D({name:"",description:"",cron_expression:"0 9 * * *",max_executions:null,task_config:{indicators:["kdj"],stock_codes:null,parameters:null,scan_mode:"traditional",periods:["d"],adjust:"n"}}),i=D({d:["kdj"],w:[],m:[]}),d={name:[{required:!0,message:"请输入任务名称",trigger:"blur"},{min:2,max:100,message:"任务名称长度在2-100个字符",trigger:"blur"}],cron_expression:[{required:!0,message:"请设置执行时间",trigger:"blur"}],"task_config.indicators":[{type:"array",required:!0,min:1,message:"请至少选择一个扫描指标",trigger:"change",validator:(c,u,N)=>{if(C.value.task_config.scan_mode==="traditional"){if(!u||u.length===0){N(new Error("请至少选择一个扫描指标"));return}}else if(!Object.values(i.value).some(v=>v&&v.length>0)){N(new Error("请为至少一个周期选择指标"));return}N()}}]},y=U(()=>!!r.task),h=()=>{C.value={name:"",description:"",cron_expression:"0 9 * * *",max_executions:null,task_config:{indicators:["kdj"],stock_codes:null,parameters:null,scan_mode:"traditional",periods:["d"],adjust:"n"}},i.value={d:["kdj"],w:[],m:[]},T.value="all",_.value="",P.value=!1,ke(()=>{var c;(c=x.value)==null||c.clearValidate()})};J(()=>r.task,c=>{var u,N,k,v,a,o;c?(C.value={name:c.name||"",description:c.description||"",cron_expression:c.cron_expression||"0 9 * * *",max_executions:c.max_executions,task_config:{indicators:((u=c.task_config)==null?void 0:u.indicators)||["kdj"],stock_codes:null,parameters:(N=c.task_config)==null?void 0:N.parameters,scan_mode:((k=c.task_config)==null?void 0:k.scan_mode)||"traditional",periods:((v=c.task_config)==null?void 0:v.periods)||["d"],adjust:"n"}},T.value="all",_.value="",(a=c.task_config)!=null&&a.period_indicators?i.value={d:c.task_config.period_indicators.d||[],w:c.task_config.period_indicators.w||[],m:c.task_config.period_indicators.m||[]}:i.value={d:((o=c.task_config)==null?void 0:o.indicators)||["kdj"],w:[],m:[]},P.value=!!c.max_executions):h()},{immediate:!0}),J(i,c=>{if(C.value.task_config.scan_mode==="traditional")C.value.task_config.indicators=c.d||[];else{const u=new Set;Object.values(c).forEach(N=>{N.forEach(k=>u.add(k))}),C.value.task_config.indicators=Array.from(u)}},{deep:!0}),J(()=>C.value.task_config.scan_mode,c=>{c==="traditional"?(i.value={d:i.value.d||[],w:[],m:[]},C.value.task_config.indicators=i.value.d||[]):(!i.value.d||i.value.d.length===0)&&(i.value.d=["kdj"])});const M=()=>{C.value.task_config.stock_codes=null},p=()=>{P.value?C.value.max_executions=10:C.value.max_executions=null},n=async()=>{if(x.value)try{await x.value.validate(),M();const c={...C.value};C.value.task_config.scan_mode==="multi_period"?(c.task_config.period_indicators=i.value,Object.keys(c.task_config.period_indicators).forEach(u=>{(!c.task_config.period_indicators[u]||c.task_config.period_indicators[u].length===0)&&delete c.task_config.period_indicators[u]})):(delete c.task_config.period_indicators,c.task_config.indicators=i.value.d||[]),console.log("提交数据:",{scanMode:c.task_config.scan_mode,indicators:c.task_config.indicators,periods:c.task_config.periods,periodIndicators:c.task_config.period_indicators}),g("submit",c)}catch{R.error("请检查表单输入")}};return(c,u)=>{const N=he,k=Et,v=jt,a=Dt,o=Bt,V=Nt,w=He,E=It,z=Vt,I=ce,O=$e;return f(),F(O,{"model-value":e.modelValue,title:y.value?"编辑定时任务":"创建定时任务",width:"800px",onClose:u[9]||(u[9]=A=>c.$emit("cancel")),class:"scheduled-task-dialog"},{footer:m(()=>[t("div",Vn,[l(I,{onClick:u[8]||(u[8]=A=>c.$emit("cancel")),class:"footer-btn-cancel"},{default:m(()=>u[15]||(u[15]=[j("取消")])),_:1,__:[15]}),l(I,{type:"primary",onClick:n,class:"footer-btn-primary"},{default:m(()=>[j($(y.value?"更新任务":"创建任务"),1)]),_:1})])]),default:m(()=>[l(z,{ref_key:"formRef",ref:x,model:C.value,rules:d,"label-width":"120px",class:"scheduled-task-form"},{default:m(()=>[l(k,{label:"任务名称",prop:"name"},{default:m(()=>[l(N,{modelValue:C.value.name,"onUpdate:modelValue":u[0]||(u[0]=A=>C.value.name=A),placeholder:"请输入任务名称",maxlength:"100","show-word-limit":""},null,8,["modelValue"])]),_:1}),l(k,{label:"任务描述",prop:"description"},{default:m(()=>[l(N,{modelValue:C.value.description,"onUpdate:modelValue":u[1]||(u[1]=A=>C.value.description=A),type:"textarea",rows:3,placeholder:"请输入任务描述（可选）",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1}),l(k,{label:"执行时间",prop:"cron_expression",class:"cron-expression-form-item"},{default:m(()=>[t("div",Tn,[l(Pn,{modelValue:C.value.cron_expression,"onUpdate:modelValue":u[2]||(u[2]=A=>C.value.cron_expression=A)},null,8,["modelValue"])])]),_:1}),l(k,{label:"扫描模式",prop:"scan_mode"},{default:m(()=>[l(a,{modelValue:C.value.task_config.scan_mode,"onUpdate:modelValue":u[3]||(u[3]=A=>C.value.task_config.scan_mode=A),class:"scan-mode-radio-group"},{default:m(()=>[l(v,{value:"traditional",class:"scan-mode-radio"},{default:m(()=>u[10]||(u[10]=[j("传统模式（仅日线）")])),_:1,__:[10]}),l(v,{value:"multi_period",class:"scan-mode-radio"},{default:m(()=>u[11]||(u[11]=[j("多周期模式")])),_:1,__:[11]})]),_:1},8,["modelValue"])]),_:1}),C.value.task_config.scan_mode==="multi_period"?(f(),F(k,{key:0,label:"扫描周期",prop:"periods"},{default:m(()=>[l(V,{modelValue:C.value.task_config.periods,"onUpdate:modelValue":u[4]||(u[4]=A=>C.value.task_config.periods=A),class:"periods-checkbox-group"},{default:m(()=>[l(o,{value:"d",class:"period-checkbox"},{default:m(()=>u[12]||(u[12]=[j("日线")])),_:1,__:[12]}),l(o,{value:"w",class:"period-checkbox"},{default:m(()=>u[13]||(u[13]=[j("周线")])),_:1,__:[13]}),l(o,{value:"m",class:"period-checkbox"},{default:m(()=>u[14]||(u[14]=[j("月线")])),_:1,__:[14]})]),_:1},8,["modelValue"])]),_:1})):L("",!0),l(k,{label:"扫描指标",prop:"task_config.indicators"},{default:m(()=>[t("div",wn,[l(At,{modelValue:i.value,"onUpdate:modelValue":u[5]||(u[5]=A=>i.value=A),"scan-mode":C.value.task_config.scan_mode,"selected-periods":C.value.task_config.periods,type:"small"},null,8,["modelValue","scan-mode","selected-periods"]),t("div",zn,[C.value.task_config.scan_mode==="traditional"?(f(),S(H,{key:0},[j(" 请选择要扫描的技术指标（至少选择一个） ")],64)):(f(),S(H,{key:1},[j(" 请为至少一个周期选择指标进行扫描 ")],64))])])]),_:1}),l(k,{label:"最大执行次数",prop:"max_executions"},{default:m(()=>[t("div",En,[l(w,{modelValue:P.value,"onUpdate:modelValue":u[6]||(u[6]=A=>P.value=A),"active-text":"限制执行次数","inactive-text":"无限制执行",onChange:p,class:"executions-switch"},null,8,["modelValue"]),P.value?(f(),F(E,{key:0,modelValue:C.value.max_executions,"onUpdate:modelValue":u[7]||(u[7]=A=>C.value.max_executions=A),min:1,max:1e3,"controls-position":"right",placeholder:"最大执行次数",class:"executions-input"},null,8,["modelValue"])):L("",!0)])]),_:1})]),_:1},8,["model"])]),_:1},8,["model-value","title"])}}},Mn=ue(In,[["__scopeId","data-v-fea6cbe1"]]),Nn={key:0,class:"execution-results"},Bn={class:"execution-info bg-gray-50 dark:bg-gray-900 p-4 rounded-lg mb-4"},Dn={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},jn={class:"text-sm font-medium mt-1"},An={class:"text-lg font-bold text-blue-600 dark:text-blue-400 mt-1"},Un={class:"text-sm font-medium mt-1"},Fn={key:0,class:"mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded"},qn={class:"text-sm text-red-600 dark:text-red-400"},Ln={key:0,class:"scan-results"},On={class:"flex items-center justify-between mb-4"},Rn={class:"flex items-center space-x-2"},Wn={class:"font-medium"},Kn={class:"flex flex-wrap gap-1"},Hn={class:"text-xs space-y-1"},Jn={key:0},Gn={key:1},Yn={key:2},Zn={key:1,class:"no-results text-center py-8"},Qn={class:"text-gray-400 mb-2"},Xn={class:"task-config mt-6"},es={class:"bg-gray-50 dark:bg-gray-900 p-4 rounded-lg"},ts={key:0,class:"formatted-config"},as={class:"config-header mb-3"},ns={class:"text-base font-medium text-gray-800 dark:text-gray-200"},ss={class:"text-sm text-gray-600 dark:text-gray-400 mt-1"},os={class:"config-details"},ls={class:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},is={class:"config-content pl-3"},rs={class:"text-xs text-gray-500 dark:text-gray-500"},cs={class:"text-sm text-gray-700 dark:text-gray-300 ml-2"},us={key:1,class:"raw-config"},ds={class:"text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap"},ps={__name:"ExecutionResults",props:{modelValue:{type:Boolean,default:!1},execution:{type:Object,default:null}},emits:["update:modelValue"],setup(e,{emit:b}){const r=e,g=U(()=>{var p;return((p=r.execution)==null?void 0:p.results_data)||[]}),x=p=>({pending:"待执行",running:"执行中",completed:"已完成",failed:"失败",cancelled:"已取消"})[p]||p,T=p=>({pending:"warning",running:"primary",completed:"success",failed:"danger",cancelled:"info"})[p]||"info",_=p=>({scheduled:"定时触发",manual:"手动触发"})[p]||p,P=p=>({BUY:"买入",SELL:"卖出",HOLD:"持有",STOP_LOSS:"止损"})[p]||p,C=p=>({BUY:"success",SELL:"danger",HOLD:"warning",STOP_LOSS:"danger"})[p]||"info",i=p=>({d:"日线",w:"周线",m:"月线"})[p]||p,d=p=>{if(!p)return"";if(p<60)return`${p}秒`;if(p<3600)return`${Math.floor(p/60)}分${p%60}秒`;const n=Math.floor(p/3600),c=Math.floor(p%3600/60),u=p%60;return`${n}时${c}分${u}秒`},y=p=>{try{const n=typeof p=="string"?JSON.parse(p):p;return JSON.stringify(n,null,2)}catch{return p||""}},h=()=>{var k;if(!g.value||g.value.length===0){R.warning("没有可导出的数据");return}const n=[["股票代码","股票名称","价格","涨跌幅","信号","周期"].join(","),...g.value.map(v=>{var a,o;return[v.stock_code,v.stock_name,((a=v.price)==null?void 0:a.toFixed(2))||"",v.change_percent?`${v.change_percent.toFixed(2)}%`:"",((o=v.signals)==null?void 0:o.join(";"))||"",i(v.period)].join(",")})].join(`
`),c=new Blob([n],{type:"text/csv;charset=utf-8;"}),u=document.createElement("a"),N=URL.createObjectURL(c);u.setAttribute("href",N),u.setAttribute("download",`scan_results_${((k=r.execution)==null?void 0:k.id)||"unknown"}.csv`),u.style.visibility="hidden",document.body.appendChild(u),u.click(),document.body.removeChild(u),R.success("结果已导出")},M=()=>{R.info("结果已是最新数据")};return(p,n)=>{var E;const c=xe,u=X("i-carbon-warning"),N=X("i-carbon-download"),k=ce,v=X("i-carbon-refresh"),a=Oe,o=Le,V=X("i-carbon-data-1"),w=$e;return f(),F(w,{"model-value":e.modelValue,title:`执行结果详情 - ID: ${((E=e.execution)==null?void 0:E.id)||""}`,width:"1000px",onClose:n[0]||(n[0]=z=>p.$emit("update:modelValue",!1))},{default:m(()=>[e.execution?(f(),S("div",Nn,[t("div",Bn,[t("div",Dn,[t("div",null,[n[1]||(n[1]=t("label",{class:"block text-sm text-gray-600 dark:text-gray-400"},"执行状态",-1)),l(c,{type:T(e.execution.status),size:"large",class:"mt-1"},{default:m(()=>[j($(x(e.execution.status)),1)]),_:1},8,["type"])]),t("div",null,[n[2]||(n[2]=t("label",{class:"block text-sm text-gray-600 dark:text-gray-400"},"触发方式",-1)),t("div",jn,$(_(e.execution.trigger_type)),1)]),t("div",null,[n[3]||(n[3]=t("label",{class:"block text-sm text-gray-600 dark:text-gray-400"},"结果数量",-1)),t("div",An,$(e.execution.results_count),1)]),t("div",null,[n[4]||(n[4]=t("label",{class:"block text-sm text-gray-600 dark:text-gray-400"},"执行时长",-1)),t("div",Un,$(e.execution.duration_seconds?d(e.execution.duration_seconds):"-"),1)])]),e.execution.error_message?(f(),S("div",Fn,[t("div",qn,[l(u,{class:"mr-1"}),j(" 错误信息："+$(e.execution.error_message),1)])])):L("",!0)]),e.execution.results_data&&e.execution.results_data.length>0?(f(),S("div",Ln,[t("div",On,[n[7]||(n[7]=t("h3",{class:"text-lg font-medium"},"扫描结果",-1)),t("div",Rn,[l(k,{size:"small",onClick:h},{default:m(()=>[l(N,{class:"mr-1"}),n[5]||(n[5]=j(" 导出结果 "))]),_:1,__:[5]}),l(k,{size:"small",onClick:M},{default:m(()=>[l(v,{class:"mr-1"}),n[6]||(n[6]=j(" 刷新 "))]),_:1,__:[6]})])]),l(o,{data:g.value,stripe:"","max-height":"400",class:"w-full"},{default:m(()=>[l(a,{prop:"stock_code",label:"股票代码",width:"100",fixed:"left"}),l(a,{prop:"stock_name",label:"股票名称",width:"120",fixed:"left"}),l(a,{prop:"price",label:"价格",width:"80"},{default:m(({row:z})=>{var I;return[t("span",Wn,$(((I=z.price)==null?void 0:I.toFixed(2))||"-"),1)]}),_:1}),l(a,{prop:"change_percent",label:"涨跌幅",width:"80"},{default:m(({row:z})=>[t("span",{class:q({"text-red-600 dark:text-red-400":z.change_percent>0,"text-green-600 dark:text-green-400":z.change_percent<0,"text-gray-600 dark:text-gray-400":z.change_percent===0})},$(z.change_percent?`${z.change_percent>0?"+":""}${z.change_percent.toFixed(2)}%`:"-"),3)]),_:1}),l(a,{prop:"signals",label:"信号类型",width:"120"},{default:m(({row:z})=>[t("div",Kn,[(f(!0),S(H,null,Z(z.signals,I=>(f(),F(c,{key:I,size:"small",type:C(I)},{default:m(()=>[j($(P(I)),1)]),_:2},1032,["type"]))),128))])]),_:1}),l(a,{prop:"period",label:"周期",width:"60"},{default:m(({row:z})=>[j($(i(z.period)),1)]),_:1}),l(a,{prop:"indicator_data",label:"指标数据","min-width":"200"},{default:m(({row:z})=>{var I,O,A;return[t("div",Hn,[(I=z.indicator_data)!=null&&I.kdj_k?(f(),S("div",Jn," KDJ: K="+$(z.indicator_data.kdj_k.toFixed(2))+", D="+$(z.indicator_data.kdj_d.toFixed(2))+", J="+$(z.indicator_data.kdj_j.toFixed(2)),1)):L("",!0),(O=z.indicator_data)!=null&&O.macd?(f(),S("div",Gn," MACD: "+$(z.indicator_data.macd.toFixed(4)),1)):L("",!0),(A=z.indicator_data)!=null&&A.volume_pressure?(f(),S("div",Yn," 量压: "+$(z.indicator_data.volume_pressure.toFixed(2)),1)):L("",!0)])]}),_:1})]),_:1},8,["data"])])):e.execution.results_count===0?(f(),S("div",Zn,[t("div",Qn,[l(V,{class:"text-4xl"})]),n[8]||(n[8]=t("div",{class:"text-gray-600 dark:text-gray-400"}," 本次扫描未找到符合条件的股票 ",-1))])):L("",!0),t("div",Xn,[n[9]||(n[9]=t("h3",{class:"text-lg font-medium mb-3"},"任务配置",-1)),t("div",es,[e.execution.task_config&&e.execution.task_config.summary?(f(),S("div",ts,[t("div",as,[t("h4",ns,$(e.execution.task_config.display_name||"任务配置"),1),t("div",ss,$(e.execution.task_config.summary),1)]),t("div",os,[(f(!0),S(H,null,Z(e.execution.task_config.details,(z,I)=>(f(),S("div",{key:I,class:"config-section mb-4"},[t("h5",ls,$(I),1),t("div",is,[(f(!0),S(H,null,Z(z,(O,A)=>(f(),S("div",{key:A,class:"config-item mb-1"},[t("span",rs,$(A)+":",1),t("span",cs,[Array.isArray(O)?(f(),S(H,{key:0},[j($(O.join(", ")),1)],64)):(f(),S(H,{key:1},[j($(O),1)],64))])]))),128))])]))),128))])])):(f(),S("div",us,[t("pre",ds,$(y(e.execution.task_config_raw||e.execution.task_config)),1)]))])])])):L("",!0)]),_:1},8,["model-value","title"])}}},ms=ue(ps,[["__scopeId","data-v-b12b5a2c"]]),fs={class:"task-executions"},gs={class:"filters bg-gray-50 dark:bg-gray-800 p-4 rounded-lg mb-4"},_s={class:"flex items-center space-x-4"},vs={class:"w-100px"},bs={class:"w-100px"},hs={class:"font-medium text-blue-600 dark:text-blue-400"},ys={key:0,class:"text-red-600 dark:text-red-400 text-sm"},ks={key:1,class:"text-gray-400"},xs={class:"flex items-center space-x-1"},$s={class:"flex justify-center mt-4"},Cs={__name:"TaskExecutions",props:{modelValue:{type:Boolean,default:!1},taskId:{type:Number,default:null}},emits:["update:modelValue"],setup(e,{emit:b}){const r=e,{executions:g,loading:x,pagination:T,loadExecutions:_,deleteExecution:P,cancelExecution:C,getStatusText:i,getStatusType:d,getTriggerTypeText:y}=Je(),h=D({trigger_type:null,status:null}),M=D(!1),p=D(null);J(()=>r.modelValue,a=>{a&&ke(()=>{_(r.taskId)})});const n=()=>{T.current=1,_(r.taskId,h.value)},c=()=>{_(r.taskId,h.value)},u=a=>{p.value=a,M.value=!0},N=async(a,o)=>{switch(a){case"cancel":try{await Ve.confirm("确认取消此次执行？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await C(o.id),await _(r.taskId,h.value)}catch(V){V!=="cancel"&&console.error("取消执行失败:",V)}break;case"delete":try{await Ve.confirm("确认删除此执行记录？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await P(o.id),await _(r.taskId,h.value)}catch(V){V!=="cancel"&&console.error("删除记录失败:",V)}break}},k=a=>a?new Date(a).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}):"",v=a=>{if(!a)return"";if(a<60)return`${a}秒`;if(a<3600)return`${Math.floor(a/60)}分${a%60}秒`;const o=Math.floor(a/3600),V=Math.floor(a%3600/60),w=a%60;return`${o}时${V}分${w}秒`};return(a,o)=>{const V=_e,w=ve,E=X("i-carbon-refresh"),z=ce,I=Oe,O=xe,A=X("i-carbon-view"),Ge=X("i-carbon-overflow-menu-horizontal"),Ye=X("i-carbon-stop"),Se=wt,Ze=X("i-carbon-trash-can"),Qe=Tt,Xe=Pt,et=Le,tt=ma,at=$e;return f(),F(at,{"model-value":e.modelValue,title:"任务执行记录",width:"1200px",onClose:o[5]||(o[5]=B=>a.$emit("update:modelValue",!1))},{default:m(()=>[t("div",fs,[t("div",gs,[t("div",_s,[t("div",vs,[o[6]||(o[6]=t("label",{class:"block text-sm text-gray-700 dark:text-gray-300 mb-1"},"触发类型",-1)),l(w,{modelValue:h.value.trigger_type,"onUpdate:modelValue":o[0]||(o[0]=B=>h.value.trigger_type=B),placeholder:"全部",clearable:"",onChange:n},{default:m(()=>[l(V,{label:"定时触发",value:"scheduled"}),l(V,{label:"手动触发",value:"manual"})]),_:1},8,["modelValue"])]),t("div",bs,[o[7]||(o[7]=t("label",{class:"block text-sm text-gray-700 dark:text-gray-300 mb-1"},"执行状态",-1)),l(w,{modelValue:h.value.status,"onUpdate:modelValue":o[1]||(o[1]=B=>h.value.status=B),placeholder:"全部",clearable:"",onChange:n},{default:m(()=>[l(V,{label:"待执行",value:"pending"}),l(V,{label:"执行中",value:"running"}),l(V,{label:"已完成",value:"completed"}),l(V,{label:"失败",value:"failed"}),l(V,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue"])]),o[9]||(o[9]=t("div",{class:"flex-1"},null,-1)),l(z,{onClick:c},{default:m(()=>[l(E,{class:"mr-1"}),o[8]||(o[8]=j(" 刷新 "))]),_:1,__:[8]})])]),l(et,{data:s(g),loading:s(x),stripe:"",class:"w-full","empty-text":"暂无执行记录"},{default:m(()=>[l(I,{prop:"id",label:"执行ID",width:"80"}),l(I,{prop:"trigger_type",label:"触发方式",width:"100"},{default:m(({row:B})=>[l(O,{type:B.trigger_type==="scheduled"?"primary":"success",size:"small"},{default:m(()=>[j($(s(y)(B.trigger_type)),1)]),_:2},1032,["type"])]),_:1}),l(I,{prop:"status",label:"执行状态",width:"100"},{default:m(({row:B})=>[l(O,{type:s(d)(B.status),size:"small"},{default:m(()=>[j($(s(i)(B.status)),1)]),_:2},1032,["type"])]),_:1}),l(I,{prop:"start_time",label:"开始时间",width:"140"},{default:m(({row:B})=>[j($(B.start_time?k(B.start_time):"-"),1)]),_:1}),l(I,{prop:"end_time",label:"结束时间",width:"140"},{default:m(({row:B})=>[j($(B.end_time?k(B.end_time):"-"),1)]),_:1}),l(I,{prop:"duration_seconds",label:"执行时长",width:"100"},{default:m(({row:B})=>[j($(B.duration_seconds?v(B.duration_seconds):"-"),1)]),_:1}),l(I,{prop:"results_count",label:"结果数量",width:"100"},{default:m(({row:B})=>[t("span",hs,$(B.results_count),1)]),_:1}),l(I,{prop:"error_message",label:"错误信息","min-width":"200"},{default:m(({row:B})=>[B.error_message?(f(),S("div",ys,$(B.error_message),1)):(f(),S("span",ks,"-"))]),_:1}),l(I,{label:"操作",width:"150",fixed:"right"},{default:m(({row:B})=>[t("div",xs,[l(z,{size:"small",type:"primary",onClick:Pe=>u(B),disabled:!B.results_data},{default:m(()=>[l(A,{class:"mr-1"}),o[10]||(o[10]=j(" 查看结果 "))]),_:2,__:[10]},1032,["onClick","disabled"]),l(Xe,{onCommand:Pe=>N(Pe,B)},{dropdown:m(()=>[l(Qe,null,{default:m(()=>[B.status==="pending"||B.status==="running"?(f(),F(Se,{key:0,command:"cancel"},{default:m(()=>[l(Ye,{class:"mr-2"}),o[11]||(o[11]=j(" 取消执行 "))]),_:1,__:[11]})):L("",!0),l(Se,{command:"delete",divided:""},{default:m(()=>[l(Ze,{class:"mr-2"}),o[12]||(o[12]=j(" 删除记录 "))]),_:1,__:[12]})]),_:2},1024)]),default:m(()=>[l(z,{size:"small",type:"text"},{default:m(()=>[l(Ge)]),_:1})]),_:2},1032,["onCommand"])])]),_:1})]),_:1},8,["data","loading"]),t("div",$s,[l(tt,{"current-page":s(T).current,"onUpdate:currentPage":o[2]||(o[2]=B=>s(T).current=B),"page-size":s(T).pageSize,"onUpdate:pageSize":o[3]||(o[3]=B=>s(T).pageSize=B),total:s(T).total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onCurrentChange:s(_),onSizeChange:s(_)},null,8,["current-page","page-size","total","onCurrentChange","onSizeChange"])])]),l(ms,{modelValue:M.value,"onUpdate:modelValue":o[4]||(o[4]=B=>M.value=B),execution:p.value},null,8,["modelValue","execution"])]),_:1},8,["model-value"])}}},Ss=ue(Cs,[["__scopeId","data-v-e51d3965"]]),Ps={class:"scheduled-tasks-page"},Ts={class:"page-header"},ws={class:"stats-grid"},zs={class:"stat-card"},Es={class:"stat-content"},Vs={class:"stat-icon-wrapper stat-icon--primary"},Is={class:"stat-info"},Ms={class:"stat-value"},Ns={class:"stat-card"},Bs={class:"stat-content"},Ds={class:"stat-icon-wrapper stat-icon--success"},js={class:"stat-info"},As={class:"stat-value"},Us={class:"stat-card"},Fs={class:"stat-content"},qs={class:"stat-icon-wrapper stat-icon--warning"},Ls={class:"stat-info"},Os={class:"stat-value"},Rs={class:"stat-card"},Ws={class:"stat-content"},Ks={class:"stat-icon-wrapper stat-icon--danger"},Hs={class:"stat-info"},Js={class:"stat-value"},Gs={__name:"index",setup(e){const{tasks:b,loading:r,loadTasks:g,createTask:x,updateTask:T,deleteTask:_,executeTask:P,toggleTaskStatus:C}=Sa(),{executions:i}=Je(),d=D(!1),y=D(!1),h=D(null),M=D(null),p=U(()=>b.value.filter(w=>w.is_active).length),n=U(()=>{const w=new Date().toDateString();return i.value.filter(E=>new Date(E.created_at).toDateString()===w).length}),c=U(()=>i.value.filter(w=>w.status==="failed").length);Fe(()=>{g()});const u=w=>{h.value={...w},d.value=!0},N=async w=>{try{await _(w),await g()}catch{}},k=async w=>{try{await P(w),R.success("任务已提交执行")}catch{}},v=async(w,E)=>{try{await C(w,E)}catch{}},a=w=>{M.value=w,y.value=!0},o=async w=>{try{h.value?await T(h.value.id,w):await x(w),await g(),V()}catch{}},V=()=>{d.value=!1,h.value=null};return(w,E)=>(f(),S("div",Ps,[t("div",Ts,[E[4]||(E[4]=t("div",{class:"header-content"},[t("h1",{class:"page-title"},"定时任务管理")],-1)),l(Re,{icon:"add",variant:"primary",size:"md",onClick:E[0]||(E[0]=z=>d.value=!0),class:"create-task-btn"},{default:m(()=>E[3]||(E[3]=[j(" 创建任务 ")])),_:1,__:[3]})]),t("div",ws,[t("div",zs,[t("div",Es,[t("div",Vs,[l(se,{name:"task",class:"stat-icon"})]),t("div",Is,[E[5]||(E[5]=t("p",{class:"stat-label"},"总任务数",-1)),t("p",Ms,$(s(b).length),1)])])]),t("div",Ns,[t("div",Bs,[t("div",Ds,[l(se,{name:"checkmark",class:"stat-icon"})]),t("div",js,[E[6]||(E[6]=t("p",{class:"stat-label"},"活跃任务",-1)),t("p",As,$(p.value),1)])])]),t("div",Us,[t("div",Fs,[t("div",qs,[l(se,{name:"time",class:"stat-icon"})]),t("div",Ls,[E[7]||(E[7]=t("p",{class:"stat-label"},"今日执行",-1)),t("p",Os,$(n.value),1)])])]),t("div",Rs,[t("div",Ws,[t("div",Ks,[l(se,{name:"warning",class:"stat-icon"})]),t("div",Hs,[E[8]||(E[8]=t("p",{class:"stat-label"},"失败任务",-1)),t("p",Js,$(c.value),1)])])])]),l(Ha,{tasks:s(b),loading:s(r),onEdit:u,onDelete:N,onExecute:k,onToggleStatus:v,onViewExecutions:a,onRefresh:s(g)},null,8,["tasks","loading","onRefresh"]),l(Mn,{modelValue:d.value,"onUpdate:modelValue":E[1]||(E[1]=z=>d.value=z),task:h.value,onSubmit:o,onCancel:V},null,8,["modelValue","task"]),l(Ss,{modelValue:y.value,"onUpdate:modelValue":E[2]||(E[2]=z=>y.value=z),"task-id":M.value},null,8,["modelValue","task-id"])]))}},co=ue(Gs,[["__scopeId","data-v-1e9b2f7e"]]);export{co as default};
