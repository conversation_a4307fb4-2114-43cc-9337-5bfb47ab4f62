import{_ as A,a as y,x as P,o as K,b as M,s as J,d as n,n as v,e as t,k,f as x,g as u,t as i,q as h,w as D,A as c,aV as f,I as p,F as $,C as N,bK as g,c as q,i as o}from"./index-XMYcRHxF.js";import{I as V}from"./IconButton-CV2L1HCo.js";const z={class:"technical-indicators-page"},T={key:0,class:"card p-6 mb-6 bg-gradient-to-r from-blue-600/10 to-purple-600/10 border border-blue-500/20"},F={class:"flex items-center justify-between"},L={class:"flex items-center gap-6"},E={class:"flex items-center gap-4"},O={class:"w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center"},G={class:"text-2xl font-bold text-white"},H={class:"text-gray-400 font-mono text-sm"},Q={class:"flex items-center gap-8"},W={class:"text-center"},X={class:"flex flex-col gap-1"},Y={class:"text-xs text-gray-400"},Z={class:"flex items-center gap-3"},ee={class:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6"},te={class:"card p-6"},se={class:"space-y-3"},ae={class:"flex items-center space-x-3"},le={class:"flex items-center space-x-3"},de={class:"flex items-center space-x-3"},ne={class:"flex items-center space-x-3"},ie={class:"flex items-center space-x-3"},oe={class:"card p-6"},re={class:"space-y-4"},ce={key:0},ve={class:"grid grid-cols-3 gap-2"},xe={key:1},ue={key:2},pe={class:"grid grid-cols-3 gap-2"},me={class:"card p-6"},fe={class:"space-y-3"},ge={class:"text-sm"},be={key:0,class:"text-center py-4"},ye={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},ke={key:0,class:"card p-6"},he={class:"chart-container"},we={class:"flex items-center justify-center h-full"},_e={class:"text-center"},je={class:"text-xs text-gray-500"},Ce={key:1,class:"card p-6"},De={class:"chart-container"},Ve={class:"flex items-center justify-center h-full"},Re={class:"text-center"},Ie={class:"text-xs text-gray-500"},Se={key:2,class:"card p-6"},Be={class:"chart-container"},Ue={class:"flex items-center justify-center h-full"},Ae={class:"text-center"},Pe={class:"text-xs text-gray-500"},Ke={key:3,class:"card p-6"},Me={class:"chart-container"},Je={class:"flex items-center justify-center h-full"},$e={class:"text-center"},Ne={key:4,class:"card p-6"},qe={class:"chart-container"},ze={class:"flex items-center justify-center h-full"},Te={class:"text-center"},Fe={key:1,class:"card p-12 text-center"},Le={__name:"index",setup(Ee){const w=M(),R=q(),r=y(null),d=y({macd:!0,kdj:!1,rsi:!1,bollinger:!1,arbr:!1}),l=y({macd:{fast:12,slow:26,signal:9},rsi:{period:14},kdj:{k:9,d:3,j:3}}),_=y([{id:1,type:"buy",message:"MACD金叉买入信号"},{id:2,type:"warning",message:"RSI超买预警"},{id:3,type:"sell",message:"KDJ死叉卖出信号"}]),I=P(()=>Object.values(d.value).some(a=>a));K(()=>{const a=w.query.stock;b(a||"600519")}),J(()=>w.query.stock,a=>{a&&b(a)});const b=a=>{const e={600519:{code:"600519",name:"贵州茅台",price:"1678.90",changePercent:2.15},"000858":{code:"000858",name:"五粮液",price:"156.78",changePercent:3.42},"002415":{code:"002415",name:"海康威视",price:"34.56",changePercent:-.82},600036:{code:"600036",name:"招商银行",price:"45.67",changePercent:1.85},"000001":{code:"000001",name:"平安银行",price:"12.34",changePercent:-1.2}};r.value=e[a]||{code:a,name:"未知股票",price:"0.00",changePercent:0},j()},j=()=>{var a;console.log("加载指标数据:",(a=r.value)==null?void 0:a.code)},m=a=>{console.log("切换指标:",a,d.value[a]),j()},S=()=>{R.go(-1)},B=()=>{r.value&&b(r.value.code)},C=a=>a>0?"text-green-400":a<0?"text-red-400":"text-gray-400",U=a=>{switch(a){case"buy":return"bg-green-500";case"sell":return"bg-red-500";case"warning":return"bg-yellow-500";default:return"bg-gray-500"}};return(a,e)=>(o(),n("div",z,[r.value?(o(),n("div",T,[t("div",F,[t("div",L,[t("div",E,[t("div",O,[x(u,{name:"chart-line",class:"text-white text-xl"})]),t("div",null,[t("h2",G,i(r.value.name),1),t("p",H,i(r.value.code),1)])]),t("div",Q,[t("div",W,[t("div",{class:h(["text-3xl font-bold mono-font",C(r.value.changePercent)])}," ¥"+i(r.value.price),3),t("div",{class:h(["text-sm font-medium",C(r.value.changePercent)])},i(r.value.changePercent>=0?"+":"")+i(r.value.changePercent)+"% ",3)]),e[18]||(e[18]=t("div",{class:"h-12 w-px bg-gray-600"},null,-1)),t("div",X,[e[17]||(e[17]=t("div",{class:"flex items-center gap-2"},[t("div",{class:"w-2 h-2 rounded-full bg-green-400"}),t("span",{class:"text-sm text-gray-300"},"实时数据")],-1)),t("div",Y,i(new Date().toLocaleTimeString()),1)])])]),e[21]||(e[21]=k()),t("div",Z,[x(V,{icon:"arrow-left",variant:"secondary",size:"sm",onClick:S},{default:D(()=>e[19]||(e[19]=[k(" 返回 ")])),_:1,__:[19]}),x(V,{icon:"renew",variant:"primary",size:"sm",onClick:B},{default:D(()=>e[20]||(e[20]=[k(" 刷新 ")])),_:1,__:[20]})])])])):v("",!0),t("div",ee,[t("div",te,[e[27]||(e[27]=t("h3",{class:"text-lg font-semibold mb-4"},"指标选择",-1)),t("div",se,[t("label",ae,[c(t("input",{type:"checkbox","onUpdate:modelValue":e[0]||(e[0]=s=>d.value.macd=s),onChange:e[1]||(e[1]=s=>m("macd")),class:"form-checkbox"},null,544),[[f,d.value.macd]]),e[22]||(e[22]=t("span",null,"MACD",-1))]),t("label",le,[c(t("input",{type:"checkbox","onUpdate:modelValue":e[2]||(e[2]=s=>d.value.kdj=s),onChange:e[3]||(e[3]=s=>m("kdj")),class:"form-checkbox"},null,544),[[f,d.value.kdj]]),e[23]||(e[23]=t("span",null,"KDJ",-1))]),t("label",de,[c(t("input",{type:"checkbox","onUpdate:modelValue":e[4]||(e[4]=s=>d.value.rsi=s),onChange:e[5]||(e[5]=s=>m("rsi")),class:"form-checkbox"},null,544),[[f,d.value.rsi]]),e[24]||(e[24]=t("span",null,"RSI",-1))]),t("label",ne,[c(t("input",{type:"checkbox","onUpdate:modelValue":e[6]||(e[6]=s=>d.value.bollinger=s),onChange:e[7]||(e[7]=s=>m("bollinger")),class:"form-checkbox"},null,544),[[f,d.value.bollinger]]),e[25]||(e[25]=t("span",null,"Bollinger Bands",-1))]),t("label",ie,[c(t("input",{type:"checkbox","onUpdate:modelValue":e[8]||(e[8]=s=>d.value.arbr=s),onChange:e[9]||(e[9]=s=>m("arbr")),class:"form-checkbox"},null,544),[[f,d.value.arbr]]),e[26]||(e[26]=t("span",null,"ARBR",-1))])])]),t("div",oe,[e[31]||(e[31]=t("h3",{class:"text-lg font-semibold mb-4"},"参数设置",-1)),t("div",re,[d.value.macd?(o(),n("div",ce,[e[28]||(e[28]=t("label",{class:"block text-sm text-gray-400 mb-2"},"MACD参数",-1)),t("div",ve,[c(t("input",{type:"number","onUpdate:modelValue":e[10]||(e[10]=s=>l.value.macd.fast=s),class:"input-field text-sm",placeholder:"快线"},null,512),[[p,l.value.macd.fast]]),c(t("input",{type:"number","onUpdate:modelValue":e[11]||(e[11]=s=>l.value.macd.slow=s),class:"input-field text-sm",placeholder:"慢线"},null,512),[[p,l.value.macd.slow]]),c(t("input",{type:"number","onUpdate:modelValue":e[12]||(e[12]=s=>l.value.macd.signal=s),class:"input-field text-sm",placeholder:"信号线"},null,512),[[p,l.value.macd.signal]])])])):v("",!0),d.value.rsi?(o(),n("div",xe,[e[29]||(e[29]=t("label",{class:"block text-sm text-gray-400 mb-2"},"RSI参数",-1)),c(t("input",{type:"number","onUpdate:modelValue":e[13]||(e[13]=s=>l.value.rsi.period=s),class:"input-field text-sm w-full",placeholder:"周期"},null,512),[[p,l.value.rsi.period]])])):v("",!0),d.value.kdj?(o(),n("div",ue,[e[30]||(e[30]=t("label",{class:"block text-sm text-gray-400 mb-2"},"KDJ参数",-1)),t("div",pe,[c(t("input",{type:"number","onUpdate:modelValue":e[14]||(e[14]=s=>l.value.kdj.k=s),class:"input-field text-sm",placeholder:"K值"},null,512),[[p,l.value.kdj.k]]),c(t("input",{type:"number","onUpdate:modelValue":e[15]||(e[15]=s=>l.value.kdj.d=s),class:"input-field text-sm",placeholder:"D值"},null,512),[[p,l.value.kdj.d]]),c(t("input",{type:"number","onUpdate:modelValue":e[16]||(e[16]=s=>l.value.kdj.j=s),class:"input-field text-sm",placeholder:"J值"},null,512),[[p,l.value.kdj.j]])])])):v("",!0)])]),t("div",me,[e[33]||(e[33]=t("h3",{class:"text-lg font-semibold mb-4"},"交易信号",-1)),t("div",fe,[(o(!0),n($,null,N(_.value,s=>(o(),n("div",{key:s.id,class:"flex items-center space-x-3"},[t("div",{class:h(["w-3 h-3 rounded-full",U(s.type)])},null,2),t("span",ge,i(s.message),1)]))),128)),_.value.length===0?(o(),n("div",be,[x(u,{name:"chart-line",class:"text-2xl text-gray-500 mb-2"}),e[32]||(e[32]=t("p",{class:"text-gray-400 text-sm"},"暂无交易信号",-1))])):v("",!0)])])]),t("div",ye,[d.value.macd?(o(),n("div",ke,[e[35]||(e[35]=g('<div class="flex items-center justify-between mb-4" data-v-386f6a6d><h3 class="text-lg font-semibold" data-v-386f6a6d>MACD指标</h3><div class="flex items-center gap-2" data-v-386f6a6d><div class="flex items-center gap-1" data-v-386f6a6d><div class="w-3 h-3 bg-blue-500 rounded" data-v-386f6a6d></div><span class="text-xs text-gray-400" data-v-386f6a6d>MACD</span></div><div class="flex items-center gap-1" data-v-386f6a6d><div class="w-3 h-3 bg-red-500 rounded" data-v-386f6a6d></div><span class="text-xs text-gray-400" data-v-386f6a6d>信号线</span></div></div></div>',1)),t("div",he,[t("div",we,[t("div",_e,[x(u,{name:"chart-line",class:"text-4xl text-gray-500 mb-2"}),e[34]||(e[34]=t("p",{class:"text-gray-400 text-sm"},"MACD指标图表",-1)),t("p",je,"快线: "+i(l.value.macd.fast)+" | 慢线: "+i(l.value.macd.slow)+" | 信号线: "+i(l.value.macd.signal),1)])])])])):v("",!0),d.value.kdj?(o(),n("div",Ce,[e[37]||(e[37]=g('<div class="flex items-center justify-between mb-4" data-v-386f6a6d><h3 class="text-lg font-semibold" data-v-386f6a6d>KDJ指标</h3><div class="flex items-center gap-2" data-v-386f6a6d><div class="flex items-center gap-1" data-v-386f6a6d><div class="w-3 h-3 bg-yellow-500 rounded" data-v-386f6a6d></div><span class="text-xs text-gray-400" data-v-386f6a6d>K线</span></div><div class="flex items-center gap-1" data-v-386f6a6d><div class="w-3 h-3 bg-green-500 rounded" data-v-386f6a6d></div><span class="text-xs text-gray-400" data-v-386f6a6d>D线</span></div><div class="flex items-center gap-1" data-v-386f6a6d><div class="w-3 h-3 bg-purple-500 rounded" data-v-386f6a6d></div><span class="text-xs text-gray-400" data-v-386f6a6d>J线</span></div></div></div>',1)),t("div",De,[t("div",Ve,[t("div",Re,[x(u,{name:"chart-line",class:"text-4xl text-gray-500 mb-2"}),e[36]||(e[36]=t("p",{class:"text-gray-400 text-sm"},"KDJ指标图表",-1)),t("p",Ie,"K: "+i(l.value.kdj.k)+" | D: "+i(l.value.kdj.d)+" | J: "+i(l.value.kdj.j),1)])])])])):v("",!0),d.value.rsi?(o(),n("div",Se,[e[39]||(e[39]=g('<div class="flex items-center justify-between mb-4" data-v-386f6a6d><h3 class="text-lg font-semibold" data-v-386f6a6d>RSI指标</h3><div class="flex items-center gap-2" data-v-386f6a6d><div class="flex items-center gap-1" data-v-386f6a6d><div class="w-3 h-3 bg-orange-500 rounded" data-v-386f6a6d></div><span class="text-xs text-gray-400" data-v-386f6a6d>RSI</span></div><span class="text-xs text-gray-500" data-v-386f6a6d>超买线: 70 | 超卖线: 30</span></div></div>',1)),t("div",Be,[t("div",Ue,[t("div",Ae,[x(u,{name:"chart-line",class:"text-4xl text-gray-500 mb-2"}),e[38]||(e[38]=t("p",{class:"text-gray-400 text-sm"},"RSI指标图表",-1)),t("p",Pe,"周期: "+i(l.value.rsi.period),1)])])])])):v("",!0),d.value.bollinger?(o(),n("div",Ke,[e[42]||(e[42]=g('<div class="flex items-center justify-between mb-4" data-v-386f6a6d><h3 class="text-lg font-semibold" data-v-386f6a6d>Bollinger Bands</h3><div class="flex items-center gap-2" data-v-386f6a6d><div class="flex items-center gap-1" data-v-386f6a6d><div class="w-3 h-3 bg-blue-500 rounded" data-v-386f6a6d></div><span class="text-xs text-gray-400" data-v-386f6a6d>上轨</span></div><div class="flex items-center gap-1" data-v-386f6a6d><div class="w-3 h-3 bg-gray-500 rounded" data-v-386f6a6d></div><span class="text-xs text-gray-400" data-v-386f6a6d>中轨</span></div><div class="flex items-center gap-1" data-v-386f6a6d><div class="w-3 h-3 bg-red-500 rounded" data-v-386f6a6d></div><span class="text-xs text-gray-400" data-v-386f6a6d>下轨</span></div></div></div>',1)),t("div",Me,[t("div",Je,[t("div",$e,[x(u,{name:"chart-line",class:"text-4xl text-gray-500 mb-2"}),e[40]||(e[40]=t("p",{class:"text-gray-400 text-sm"},"布林带指标图表",-1)),e[41]||(e[41]=t("p",{class:"text-xs text-gray-500"},"周期: 20 | 标准差: 2",-1))])])])])):v("",!0),d.value.arbr?(o(),n("div",Ne,[e[45]||(e[45]=g('<div class="flex items-center justify-between mb-4" data-v-386f6a6d><h3 class="text-lg font-semibold" data-v-386f6a6d>ARBR指标</h3><div class="flex items-center gap-2" data-v-386f6a6d><div class="flex items-center gap-1" data-v-386f6a6d><div class="w-3 h-3 bg-cyan-500 rounded" data-v-386f6a6d></div><span class="text-xs text-gray-400" data-v-386f6a6d>AR线</span></div><div class="flex items-center gap-1" data-v-386f6a6d><div class="w-3 h-3 bg-pink-500 rounded" data-v-386f6a6d></div><span class="text-xs text-gray-400" data-v-386f6a6d>BR线</span></div></div></div>',1)),t("div",qe,[t("div",ze,[t("div",Te,[x(u,{name:"chart-line",class:"text-4xl text-gray-500 mb-2"}),e[43]||(e[43]=t("p",{class:"text-gray-400 text-sm"},"ARBR指标图表",-1)),e[44]||(e[44]=t("p",{class:"text-xs text-gray-500"},"周期: 26",-1))])])])])):v("",!0)]),I.value?v("",!0):(o(),n("div",Fe,[x(u,{name:"chart-line",class:"text-6xl text-gray-500 mb-4"}),e[46]||(e[46]=t("h3",{class:"text-xl font-semibold mb-2"},"请选择技术指标",-1)),e[47]||(e[47]=t("p",{class:"text-gray-400"},"在左侧选择您要查看的技术指标",-1))]))]))}},He=A(Le,[["__scopeId","data-v-386f6a6d"]]);export{He as default};
