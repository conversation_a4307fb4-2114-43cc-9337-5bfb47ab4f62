import{_ as m,a as g,x as d,d as l,ae as x,n as i,q as c,g as y,a0 as b,i as a}from"./index-XMYcRHxF.js";const k=["disabled","title"],v={key:1,class:"loading-spinner"},p={__name:"IconButton",props:{icon:{type:String,required:!0},variant:{type:String,default:"primary",validator:t=>["primary","secondary","success","danger","warning","info","ghost"].includes(t)},size:{type:String,default:"md",validator:t=>["xs","sm","md","lg","xl"].includes(t)},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},title:{type:String,default:""}},emits:["click"],setup(t){const n=t,s=g(null),r=d(()=>({xs:"text-xs",sm:"text-sm",md:"text-base",lg:"text-lg",xl:"text-xl"})[n.size]||"text-base"),u=d(()=>n.title?n.title:s.value&&s.value.textContent||"");return(e,o)=>(a(),l("button",{class:c(["icon-button",`icon-button--${t.variant}`,`icon-button--${t.size}`,{"icon-button--loading":t.loading,"icon-only":!e.$slots.default}]),disabled:t.disabled||t.loading,title:u.value,onClick:o[0]||(o[0]=f=>e.$emit("click",f))},[t.loading?i("",!0):(a(),x(y,{key:0,name:t.icon,class:c(r.value)},null,8,["name","class"])),t.loading?(a(),l("div",v)):i("",!0),e.$slots.default?(a(),l("span",{key:2,ref_key:"slotRef",ref:s,class:"button-text"},[b(e.$slots,"default",{},void 0,!0)],512)):i("",!0)],10,k))}},$=m(p,[["__scopeId","data-v-679fcac6"]]);export{$ as I};
