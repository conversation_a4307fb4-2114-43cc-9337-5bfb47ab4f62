import{aE as q,ar as b,bj as y,P as ce,a2 as N,b8 as x,a3 as ee,R as de,U as me,W as pe,X as ne,a5 as fe,Z as be,a as ve,r as Ne,a6 as Ve,x as I,as as P,a9 as he,bb as Ie,s as ye,o as Ee,aN as we,d as z,i as v,A as G,n as J,f as K,H as a,j as A,q as O,a0 as F,w as T,ae as k,aq as ge,bk as _e,af as X,ap as Se,aP as Pe,bl as Ae,h as R,E as Fe,ak as Z,aJ as Te,au as ke,aH as xe}from"./index-XMYcRHxF.js";const Ce=100,De=600,Q={beforeMount(u,V){const o=V.value,{interval:r=Ce,delay:E=De}=q(o)?{}:o;let c,m;const l=()=>q(o)?o():o.handler(),p=()=>{m&&(clearTimeout(m),m=void 0),c&&(clearInterval(c),c=void 0)};u.addEventListener("mousedown",w=>{w.button===0&&(p(),l(),document.addEventListener("mouseup",()=>p(),{once:!0}),m=setTimeout(()=>{c=setInterval(()=>{l()},r)},E))})}},Be=ce({id:{type:String,default:void 0},step:{type:Number,default:1},stepStrictly:Boolean,max:{type:Number,default:Number.POSITIVE_INFINITY},min:{type:Number,default:Number.NEGATIVE_INFINITY},modelValue:{type:[Number,null]},readonly:Boolean,disabled:Boolean,size:me,controls:{type:Boolean,default:!0},controlsPosition:{type:String,default:"",values:["","right"]},valueOnClear:{type:[String,Number,null],validator:u=>u===null||b(u)||["min","max"].includes(u),default:null},name:String,placeholder:String,precision:{type:Number,validator:u=>u>=0&&u===Number.parseInt(`${u}`,10)},validateEvent:{type:Boolean,default:!0},...de(["ariaLabel"])}),Le={[ee]:(u,V)=>V!==u,blur:u=>u instanceof FocusEvent,focus:u=>u instanceof FocusEvent,[x]:u=>b(u)||y(u),[N]:u=>b(u)||y(u)},Me=ne({name:"ElInputNumber"}),$e=ne({...Me,props:Be,emits:Le,setup(u,{expose:V,emit:o}){const r=u,{t:E}=fe(),c=be("input-number"),m=ve(),l=Ne({currentValue:r.modelValue,userInput:null}),{formItem:p}=Ve(),w=I(()=>b(r.modelValue)&&r.modelValue<=r.min),U=I(()=>b(r.modelValue)&&r.modelValue>=r.max),te=I(()=>{const e=W(r.step);return P(r.precision)?Math.max(W(r.modelValue),e):(e>r.precision,r.precision)}),C=I(()=>r.controls&&r.controlsPosition==="right"),H=he(),h=Ie(),D=I(()=>{if(l.userInput!==null)return l.userInput;let e=l.currentValue;if(y(e))return"";if(b(e)){if(Number.isNaN(e))return"";P(r.precision)||(e=e.toFixed(r.precision))}return e}),B=(e,n)=>{if(P(n)&&(n=te.value),n===0)return Math.round(e);let t=String(e);const s=t.indexOf(".");if(s===-1||!t.replace(".","").split("")[s+n])return e;const _=t.length;return t.charAt(_-1)==="5"&&(t=`${t.slice(0,Math.max(0,_-1))}6`),Number.parseFloat(Number(t).toFixed(n))},W=e=>{if(y(e))return 0;const n=e.toString(),t=n.indexOf(".");let s=0;return t!==-1&&(s=n.length-t-1),s},Y=(e,n=1)=>b(e)?B(e+r.step*n):l.currentValue,L=()=>{if(r.readonly||h.value||U.value)return;const e=Number(D.value)||0,n=Y(e);g(n),o(x,l.currentValue),$()},M=()=>{if(r.readonly||h.value||w.value)return;const e=Number(D.value)||0,n=Y(e,-1);g(n),o(x,l.currentValue),$()},j=(e,n)=>{const{max:t,min:s,step:i,precision:f,stepStrictly:_,valueOnClear:S}=r;t<s&&Te("InputNumber","min should not be greater than max.");let d=Number(e);if(y(e)||Number.isNaN(d))return null;if(e===""){if(S===null)return null;d=ke(S)?{min:s,max:t}[S]:S}return _&&(d=B(Math.round(d/i)*i,f),d!==e&&n&&o(N,d)),P(f)||(d=B(d,f)),(d>t||d<s)&&(d=d>t?t:s,n&&o(N,d)),d},g=(e,n=!0)=>{var t;const s=l.currentValue,i=j(e);if(!n){o(N,i);return}s===i&&e||(l.userInput=null,o(N,i),s!==i&&o(ee,i,s),r.validateEvent&&((t=p==null?void 0:p.validate)==null||t.call(p,"change").catch(f=>Z())),l.currentValue=i)},ae=e=>{l.userInput=e;const n=e===""?null:Number(e);o(x,n),g(n,!1)},re=e=>{const n=e!==""?Number(e):"";(b(n)&&!Number.isNaN(n)||e==="")&&g(n),$(),l.userInput=null},le=()=>{var e,n;(n=(e=m.value)==null?void 0:e.focus)==null||n.call(e)},ue=()=>{var e,n;(n=(e=m.value)==null?void 0:e.blur)==null||n.call(e)},se=e=>{o("focus",e)},ie=e=>{var n,t;l.userInput=null,l.currentValue===null&&((n=m.value)!=null&&n.input)&&(m.value.input.value=""),o("blur",e),r.validateEvent&&((t=p==null?void 0:p.validate)==null||t.call(p,"blur").catch(s=>Z()))},$=()=>{l.currentValue!==r.modelValue&&(l.currentValue=r.modelValue)},oe=e=>{document.activeElement===e.target&&e.preventDefault()};return ye(()=>r.modelValue,(e,n)=>{const t=j(e,!0);l.userInput===null&&t!==n&&(l.currentValue=t)},{immediate:!0}),Ee(()=>{var e;const{min:n,max:t,modelValue:s}=r,i=(e=m.value)==null?void 0:e.input;if(i.setAttribute("role","spinbutton"),Number.isFinite(t)?i.setAttribute("aria-valuemax",String(t)):i.removeAttribute("aria-valuemax"),Number.isFinite(n)?i.setAttribute("aria-valuemin",String(n)):i.removeAttribute("aria-valuemin"),i.setAttribute("aria-valuenow",l.currentValue||l.currentValue===0?String(l.currentValue):""),i.setAttribute("aria-disabled",String(h.value)),!b(s)&&s!=null){let f=Number(s);Number.isNaN(f)&&(f=null),o(N,f)}i.addEventListener("wheel",oe,{passive:!1})}),we(()=>{var e,n;const t=(e=m.value)==null?void 0:e.input;t==null||t.setAttribute("aria-valuenow",`${(n=l.currentValue)!=null?n:""}`)}),V({focus:le,blur:ue}),(e,n)=>(v(),z("div",{class:O([a(c).b(),a(c).m(a(H)),a(c).is("disabled",a(h)),a(c).is("without-controls",!e.controls),a(c).is("controls-right",a(C))]),onDragstart:R(()=>{},["prevent"])},[e.controls?G((v(),z("span",{key:0,role:"button","aria-label":a(E)("el.inputNumber.decrease"),class:O([a(c).e("decrease"),a(c).is("disabled",a(w))]),onKeydown:A(M,["enter"])},[F(e.$slots,"decrease-icon",{},()=>[K(a(X),null,{default:T(()=>[a(C)?(v(),k(a(ge),{key:0})):(v(),k(a(_e),{key:1}))]),_:1})])],42,["aria-label","onKeydown"])),[[a(Q),M]]):J("v-if",!0),e.controls?G((v(),z("span",{key:1,role:"button","aria-label":a(E)("el.inputNumber.increase"),class:O([a(c).e("increase"),a(c).is("disabled",a(U))]),onKeydown:A(L,["enter"])},[F(e.$slots,"increase-icon",{},()=>[K(a(X),null,{default:T(()=>[a(C)?(v(),k(a(Se),{key:0})):(v(),k(a(Pe),{key:1}))]),_:1})])],42,["aria-label","onKeydown"])),[[a(Q),L]]):J("v-if",!0),K(a(Fe),{id:e.id,ref_key:"input",ref:m,type:"number",step:e.step,"model-value":a(D),placeholder:e.placeholder,readonly:e.readonly,disabled:a(h),size:a(H),max:e.max,min:e.min,name:e.name,"aria-label":e.ariaLabel,"validate-event":!1,onKeydown:[A(R(L,["prevent"]),["up"]),A(R(M,["prevent"]),["down"])],onBlur:ie,onFocus:se,onInput:ae,onChange:re},Ae({_:2},[e.$slots.prefix?{name:"prefix",fn:T(()=>[F(e.$slots,"prefix")])}:void 0,e.$slots.suffix?{name:"suffix",fn:T(()=>[F(e.$slots,"suffix")])}:void 0]),1032,["id","step","model-value","placeholder","readonly","disabled","size","max","min","name","aria-label","onKeydown"])],42,["onDragstart"]))}});var ze=pe($e,[["__file","input-number.vue"]]);const Oe=xe(ze);export{Oe as E,Q as v};
