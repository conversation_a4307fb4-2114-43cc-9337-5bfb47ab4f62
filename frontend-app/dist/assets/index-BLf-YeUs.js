import{_ as R,u as q,r as z,a as y,o as B,b as C,c as I,d as p,e as s,f as o,g as d,w as r,h as K,i as g,E as M,j as k,k as _,l as N,F as V,m as f}from"./index-XMYcRHxF.js";import{E as S,a as U}from"./el-form-item-B8tg80IO.js";import{E as A}from"./el-checkbox-CK4bBt59.js";/* empty css                 */const P={class:"login-container"},T={class:"login-content"},j={class:"login-header"},J={class:"logo-section"},L={class:"logo-icon"},W={class:"login-card"},$={class:"input-wrapper"},D={class:"input-wrapper"},G={__name:"index",setup(H){const v=I(),b=C(),w=q(),t=z({username:"",password:"",remember_me:!0}),h={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:50,message:"用户名长度在 3 到 50 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码至少6位",trigger:"blur"}]},c=y(null),n=y(!1),i=async()=>{if(c.value)try{if(!await c.value.validate())return;n.value=!0;const e=await w.login(t);if(e.success){f.success("登录成功！");const u=b.query.redirect||"/";v.push(u)}else f.error(e.message||"登录失败")}catch(l){console.error("登录失败:",l),f.error(l.message||"登录失败，请检查用户名和密码")}finally{n.value=!1}};return B(()=>{if(w.isAuthenticated){const l=b.query.redirect||"/";v.push(l)}}),(l,e)=>{const u=M,m=U,x=A,E=N,F=S;return g(),p("div",P,[e[9]||(e[9]=s("div",{class:"background-gradient"},null,-1)),e[10]||(e[10]=s("div",{class:"background-pattern"},null,-1)),s("div",T,[s("div",j,[s("div",J,[s("div",L,[o(d,{name:"chart-line",size:48})]),e[3]||(e[3]=s("h1",{class:"system-title"},"股票量化分析系统",-1)),e[4]||(e[4]=s("p",{class:"system-subtitle"},"用户认证登录",-1))])]),s("div",W,[o(F,{ref_key:"loginFormRef",ref:c,model:t,rules:h,class:"login-form",onSubmit:K(i,["prevent"])},{default:r(()=>[e[7]||(e[7]=s("div",{class:"form-title"},[s("h2",null,"登录账户"),s("p",null,"请输入您的用户名和密码")],-1)),o(m,{prop:"username",class:"form-item"},{default:r(()=>[s("div",$,[o(d,{name:"user",class:"input-icon"}),o(u,{modelValue:t.username,"onUpdate:modelValue":e[0]||(e[0]=a=>t.username=a),placeholder:"请输入用户名",size:"large",class:"custom-input",onKeyup:k(i,["enter"])},null,8,["modelValue"])])]),_:1}),o(m,{prop:"password",class:"form-item"},{default:r(()=>[s("div",D,[o(d,{name:"locked",class:"input-icon"}),o(u,{modelValue:t.password,"onUpdate:modelValue":e[1]||(e[1]=a=>t.password=a),type:"password",placeholder:"请输入密码",size:"large",class:"custom-input","show-password":"",onKeyup:k(i,["enter"])},null,8,["modelValue"])])]),_:1}),o(m,{class:"remember-item"},{default:r(()=>[o(x,{modelValue:t.remember_me,"onUpdate:modelValue":e[2]||(e[2]=a=>t.remember_me=a),class:"remember-checkbox"},{default:r(()=>e[5]||(e[5]=[_(" 记住登录状态 (30天) ")])),_:1,__:[5]},8,["modelValue"])]),_:1}),o(m,{class:"login-button-item"},{default:r(()=>[o(E,{type:"primary",size:"large",class:"login-button",loading:n.value,onClick:i},{default:r(()=>[n.value?(g(),p(V,{key:1},[_(" 登录中... ")],64)):(g(),p(V,{key:0},[o(d,{name:"arrow-right",class:"button-icon"}),e[6]||(e[6]=_(" 登录系统 "))],64))]),_:1},8,["loading"])]),_:1})]),_:1,__:[7]},8,["model"])]),e[8]||(e[8]=s("div",{class:"login-footer"},[s("p",null,"© 2024 股票量化分析系统 - JWT认证版本"),s("p",{class:"contact-info"},"还没有账号？请联系管理员创建")],-1))])])}}},Z=R(G,[["__scopeId","data-v-b832fbf5"]]);export{Z as default};
