import{_ as ct,a as i,s as ut,o as dt,d as j,e as s,f as R,k as W,g as G,t as d,A as Z,I as ft,H as st,B as nt,n as mt,F as xt,C as vt,v as Q,i as T,q as pt}from"./index-XMYcRHxF.js";import{C as gt}from"./CommonPagination-B9js4cyy.js";import{c as B,g as bt}from"./_commonjsHelpers-Cpj98o6Y.js";var X,at;function ht(){if(at)return X;at=1;var K="Expected a function",g=NaN,f="[object Symbol]",b=/^\s+|\s+$/g,h=/^[-+]0x[0-9a-f]+$/i,L=/^0b[01]+$/i,y=/^0o[0-7]+$/i,_=parseInt,F=typeof B=="object"&&B&&B.Object===Object&&B,m=typeof self=="object"&&self&&self.Object===Object&&self,k=F||m||Function("return this")(),P=Object.prototype,x=P.toString,M=Math.max,O=Math.min,C=function(){return k.Date.now()};function V(e,t,n){var r,c,v,u,o,l,p=0,I=!1,w=!1,z=!0;if(typeof e!="function")throw new TypeError(K);t=N(t)||0,$(n)&&(I=!!n.leading,w="maxWait"in n,v=w?M(N(n.maxWait)||0,t):v,z="trailing"in n?!!n.trailing:z);function H(a){var S=r,E=c;return r=c=void 0,p=a,u=e.apply(E,S),u}function ot(a){return p=a,o=setTimeout(D,t),I?H(a):u}function rt(a){var S=a-l,E=a-p,et=t-S;return w?O(et,v-E):et}function Y(a){var S=a-l,E=a-p;return l===void 0||S>=t||S<0||w&&E>=v}function D(){var a=C();if(Y(a))return tt(a);o=setTimeout(D,rt(a))}function tt(a){return o=void 0,z&&r?H(a):(r=c=void 0,u)}function lt(){o!==void 0&&clearTimeout(o),p=0,r=l=c=o=void 0}function it(){return o===void 0?u:tt(C())}function U(){var a=C(),S=Y(a);if(r=arguments,c=this,l=a,S){if(o===void 0)return ot(l);if(w)return o=setTimeout(D,t),H(l)}return o===void 0&&(o=setTimeout(D,t)),u}return U.cancel=lt,U.flush=it,U}function $(e){var t=typeof e;return!!e&&(t=="object"||t=="function")}function A(e){return!!e&&typeof e=="object"}function q(e){return typeof e=="symbol"||A(e)&&x.call(e)==f}function N(e){if(typeof e=="number")return e;if(q(e))return g;if($(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=$(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=e.replace(b,"");var n=L.test(e);return n||y.test(e)?_(e.slice(2),n?2:8):h.test(e)?g:+e}return X=V,X}var yt=ht();const _t=bt(yt),St={class:"page-container p-6 bg-bg-primary text-text-primary"},kt={class:"card bg-bg-secondary p-6 rounded-lg shadow-lg"},Ct={class:"flex items-center mb-6 gap-4"},It={class:"flex space-x-3"},jt=["disabled"],Tt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6"},Lt={class:"overflow-x-auto"},$t={class:"data-table w-full"},Nt={key:0},wt={key:1},Et={colspan:"9",class:"p-8 text-center"},Ft={class:"text-text-muted"},Mt={class:"p-3 text-sm text-text-secondary font-mono"},Ot={class:"p-3 text-sm text-text-primary font-medium"},Dt={class:"p-3 text-sm text-text-secondary"},Rt={class:"p-3 text-sm text-text-secondary"},Bt={class:"p-3 text-sm text-text-secondary"},Pt={class:"p-3 text-sm text-right text-text-secondary"},Vt={class:"p-3 text-sm text-right text-text-secondary"},At={class:"p-3 text-center"},J=20,qt={__name:"index",setup(K){const g=i([]),f=i(!1);i(!1);const b=i(null),h=i(""),L=i(""),y=i(""),_=i(""),F=i([]),m=i(1),k=i(0),P=({page:e})=>{m.value=e},x=async()=>{try{f.value=!0,b.value=null;const e={skip:(m.value-1)*J,limit:J};h.value.trim()&&(e.search=h.value.trim()),L.value&&(e.industry=L.value),y.value&&(e.exchange=y.value),_.value&&(e.is_active=_.value==="true");const{data:t}=await Q.stockList.getStockList(e);if(console.log(t),t&&t.stocks){if(g.value=t.stocks,k.value=t.total||0,F.value.length===0){const n=[...new Set(t.stocks.map(r=>r.industry).filter(r=>r&&r.trim()))];F.value=n.sort()}}else g.value=[],k.value=0}catch(e){console.error("获取股票列表失败:",e),b.value=e.message||"获取股票列表失败",g.value=[],k.value=0}finally{f.value=!1}},M=_t(()=>{m.value=1,x()},300),O=async()=>{var e,t;try{f.value=!0,b.value=null,console.log("正在从运营商拉取最新股票和指数数据，请稍候...");const r=[Q.stockList.refreshStockList(),Q.indexList.refreshIndexList()],[c,v]=await Promise.allSettled(r);if(c.status==="fulfilled"&&c.value){const{deleted_count:u,inserted_count:o,total_fetched:l,data_source:p,duration_seconds:I}=c.value;console.log(`股票数据刷新成功：删除${u}条，插入${o}条，共获取${l}条数据，使用${p}数据源，耗时${I.toFixed(2)}秒`)}else console.warn("股票数据刷新失败:",((e=c.reason)==null?void 0:e.message)||"未知错误");if(v.status==="fulfilled"&&v.value){const{deleted_count:u,inserted_count:o,total_fetched:l,data_source:p,duration_seconds:I}=v.value;console.log(`指数数据刷新成功：删除${u}条，插入${o}条，共获取${l}条数据，使用${p}数据源，耗时${I.toFixed(2)}秒`)}else console.warn("指数数据刷新失败:",((t=v.reason)==null?void 0:t.message)||"未知错误");await x(),console.log("数据刷新操作完成")}catch(n){console.error("刷新数据失败:",n),b.value=n.message||"刷新数据失败",await x()}finally{f.value=!1}},C=()=>{m.value=1,x()},V=()=>{h.value="",L.value="",y.value="",_.value="",m.value=1,x()},$=()=>{console.log("导出数据功能待实现")},A=e=>({SH:"上证",SZ:"深证"})[e]||e,q=e=>{if(!e||e===null||e==="null")return"暂无数据";try{const t=new Date(e);return isNaN(t.getTime())?"暂无数据":t.toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"})}catch{return"暂无数据"}},N=e=>{if(!e||e===null||e===0||e==="null")return"暂无数据";const t=parseInt(e);return isNaN(t)||t<=0?"暂无数据":t>=1e8?(t/1e8).toFixed(1)+"亿":t>=1e4?(t/1e4).toFixed(1)+"万":t.toLocaleString()};return ut(m,()=>{x()}),dt(()=>{x()}),(e,t)=>(T(),j("div",St,[s("div",kt,[s("div",Ct,[t[5]||(t[5]=s("h2",{class:"text-2xl font-semibold text-text-primary"},"股票列表管理",-1)),t[6]||(t[6]=s("div",{id:"gap",class:"flex-1"},null,-1)),s("div",It,[s("button",{class:"btn-primary",onClick:O,disabled:f.value},[R(G,{name:"activity",class:"mr-2"}),W(" "+d(f.value?"拉取中...":"拉取数据"),1)],8,jt),s("button",{class:"btn-secondary",onClick:$},[R(G,{name:"download",class:"mr-2"}),t[4]||(t[4]=W("导出数据 "))])])]),s("div",Tt,[Z(s("input",{type:"text",class:"input-field",placeholder:"搜索股票代码或名称","onUpdate:modelValue":t[0]||(t[0]=n=>h.value=n),onInput:t[1]||(t[1]=(...n)=>st(M)&&st(M)(...n))},null,544),[[ft,h.value]]),Z(s("select",{class:"input-field","onUpdate:modelValue":t[2]||(t[2]=n=>y.value=n),onChange:C},t[7]||(t[7]=[s("option",{value:""},"所有市场",-1),s("option",{value:"SH"},"上证",-1),s("option",{value:"SZ"},"深证",-1)]),544),[[nt,y.value]]),Z(s("select",{class:"input-field","onUpdate:modelValue":t[3]||(t[3]=n=>_.value=n),onChange:C},t[8]||(t[8]=[s("option",{value:""},"所有状态",-1),s("option",{value:"true"},"活跃",-1),s("option",{value:"false"},"非活跃",-1)]),544),[[nt,_.value]]),s("button",{class:"btn-primary",onClick:V},[R(G,{name:"filter",class:"mr-2"}),t[9]||(t[9]=W("重置筛选 "))])]),s("div",Lt,[s("table",$t,[t[11]||(t[11]=s("thead",null,[s("tr",null,[s("th",{class:"p-3 text-left text-sm font-semibold text-text-muted"}," 代码 "),s("th",{class:"p-3 text-left text-sm font-semibold text-text-muted"}," 名称 "),s("th",{class:"p-3 text-left text-sm font-semibold text-text-muted"}," 行业 "),s("th",{class:"p-3 text-left text-sm font-semibold text-text-muted"}," 市场 "),s("th",{class:"p-3 text-left text-sm font-semibold text-text-muted"}," 上市日期 "),s("th",{class:"p-3 text-right text-sm font-semibold text-text-muted"}," 总股本 "),s("th",{class:"p-3 text-right text-sm font-semibold text-text-muted"}," 流通股本 "),s("th",{class:"p-3 text-center text-sm font-semibold text-text-muted"}," 状态 ")])],-1)),s("tbody",null,[f.value?(T(),j("tr",Nt,t[10]||(t[10]=[s("td",{colspan:"9",class:"p-8 text-center"},[s("div",{class:"flex items-center justify-center"},[s("div",{class:"spinner mr-2"}),s("span",{class:"text-text-muted"},"加载中...")])],-1)]))):g.value.length===0?(T(),j("tr",wt,[s("td",Et,[s("p",Ft,d(b.value?"数据加载失败":"暂无数据"),1),b.value?(T(),j("button",{key:0,class:"btn-primary mt-2",onClick:O}," 重试 ")):mt("",!0)])])):(T(!0),j(xt,{key:2},vt(g.value,n=>(T(),j("tr",{key:n.code,class:"border-b border-border-color hover:bg-bg-tertiary transition-colors"},[s("td",Mt,d(n.code),1),s("td",Ot,d(n.name),1),s("td",Dt,d(n.industry||"未分类"),1),s("td",Rt,d(A(n.exchange)),1),s("td",Bt,d(q(n.listing_date)),1),s("td",Pt,d(N(n.total_shares)),1),s("td",Vt,d(N(n.circulating_shares)),1),s("td",At,[s("span",{class:pt(n.is_active?"text-green-500":"text-red-500")},d(n.is_active?"活跃":"非活跃"),3)])]))),128))])])]),R(gt,{total:k.value,"current-page":m.value,"page-size":J,"show-filtered":!!(h.value||y.value||_.value),onPageChange:P,class:"mt-6"},null,8,["total","current-page","show-filtered"])])]))}},Wt=ct(qt,[["__scopeId","data-v-60afc630"]]);export{Wt as default};
