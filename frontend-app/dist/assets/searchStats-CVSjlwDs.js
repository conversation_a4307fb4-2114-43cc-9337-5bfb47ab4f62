const c="stock_search_stats";class o{constructor(){this.stats=this.loadStats()}loadStats(){try{const t=localStorage.getItem(c);return t?JSON.parse(t):{}}catch(t){return console.error("加载搜索统计失败:",t),{}}}saveStats(){try{localStorage.setItem(c,JSON.stringify(this.stats))}catch(t){console.error("保存搜索统计失败:",t)}}incrementSearchCount(t,s,a=""){this.stats[t]||(this.stats[t]={code:t,name:s,industry:a,searchCount:0,lastSearchTime:Date.now()}),this.stats[t].searchCount++,this.stats[t].lastSearchTime=Date.now(),this.saveStats()}getPopularStocks(t=10){const s=Object.values(this.stats);return s.sort((a,e)=>a.searchCount!==e.searchCount?e.searchCount-a.searchCount:e.lastSearchTime-a.lastSearchTime),s.slice(0,t)}cleanup(t=2,s=30*24*60*60*1e3){const a=Date.now(),e={};Object.entries(this.stats).forEach(([n,r])=>{(r.searchCount>=t||a-r.lastSearchTime<s)&&(e[n]=r)}),this.stats=e,this.saveStats()}getSearchCount(t){var s;return((s=this.stats[t])==null?void 0:s.searchCount)||0}clear(){this.stats={},this.saveStats()}}const h=new o;setTimeout(()=>{h.cleanup()},1e3);export{h as s};
