import{as as Me,P as se,bm as Ne,bn as Pe,bo as xe,W as me,X as Q,ax as ke,Z as fe,a as F,x as K,bp as Ie,ay as re,bq as Ve,ae as A,i as s,w as l,A as te,e as g,q as V,H as t,n as P,af as he,a0 as Y,ah as Be,d as p,k as T,t as h,F as B,f as r,br as We,az as Oe,at as Ue,aH as we,bs as je,b6 as ce,b2 as Z,bj as Fe,a7 as Se,Q as Re,C as R,U as qe,a9 as Le,N as He,am as Ke,aR as Qe,u as $e,r as ve,m as H,_ as Ee,s as ue,l as Te,bi as pe,o as Xe,z as Ze,h as Ge,bt as Je,ai as Ye,g as de}from"./index-XMYcRHxF.js";import{E as j,a as Ce}from"./CommonTable-3T2O5_iP.js";import{E as et,a as tt}from"./el-form-item-B8tg80IO.js";import{s as J,a as st,E as at}from"./el-select-WZ60w1Eq.js";import{E as nt}from"./el-overlay-r0bpWscF.js";import"./el-checkbox-CK4bBt59.js";import"./CommonPagination-B9js4cyy.js";import"./strings-D4TsBRQg.js";const lt=["light","dark"],ot=se({title:{type:String,default:""},description:{type:String,default:""},type:{type:String,values:Pe(xe),default:"info"},closable:{type:Boolean,default:!0},closeText:{type:String,default:""},showIcon:Boolean,center:Boolean,effect:{type:String,values:lt,default:"light"},...Ne}),it={open:()=>!0,close:u=>Me(u)||u instanceof Event},rt=Q({name:"ElAlert"}),ct=Q({...rt,props:ot,emits:it,setup(u,{emit:m}){const i=u,{Close:$}=We,v=ke(),c=fe("alert"),b=F(!1),W=K(()=>xe[i.type]),y=K(()=>!!(i.description||v.default)),z=()=>{b.value=!0,m("open")},E=f=>{b.value=!1,m("close",f)},{onOpen:k,onClose:I}=Ie({showAfter:re(i,"showAfter"),hideAfter:re(i,"hideAfter"),autoClose:re(i,"autoClose"),open:z,close:E});return Ve&&k(),(f,M)=>(s(),A(Ue,{name:t(c).b("fade"),persisted:""},{default:l(()=>[te(g("div",{class:V([t(c).b(),t(c).m(f.type),t(c).is("center",f.center),t(c).is(f.effect)]),role:"alert"},[f.showIcon&&(f.$slots.icon||t(W))?(s(),A(t(he),{key:0,class:V([t(c).e("icon"),{[t(c).is("big")]:t(y)}])},{default:l(()=>[Y(f.$slots,"icon",{},()=>[(s(),A(Be(t(W))))])]),_:3},8,["class"])):P("v-if",!0),g("div",{class:V(t(c).e("content"))},[f.title||f.$slots.title?(s(),p("span",{key:0,class:V([t(c).e("title"),{"with-description":t(y)}])},[Y(f.$slots,"title",{},()=>[T(h(f.title),1)])],2)):P("v-if",!0),t(y)?(s(),p("p",{key:1,class:V(t(c).e("description"))},[Y(f.$slots,"default",{},()=>[T(h(f.description),1)])],2)):P("v-if",!0),f.closable?(s(),p(B,{key:2},[f.closeText?(s(),p("div",{key:0,class:V([t(c).e("close-btn"),t(c).is("customed")]),onClick:E},h(f.closeText),3)):(s(),A(t(he),{key:1,class:V(t(c).e("close-btn")),onClick:t(I)},{default:l(()=>[r(t($))]),_:1},8,["class","onClick"]))],64)):P("v-if",!0)],2)],2),[[Oe,b.value]])]),_:3},8,["name"]))}});var ut=me(ct,[["__file","alert.vue"]]);const dt=we(ut),ge=Symbol("elDescriptions");var G=Q({name:"ElDescriptionsCell",props:{cell:{type:Object},tag:{type:String,default:"td"},type:{type:String}},setup(){return{descriptions:Se(ge,{})}},render(){var u;const m=je(this.cell),i=(((u=this.cell)==null?void 0:u.dirs)||[]).map(_=>{const{dir:w,arg:C,modifiers:q,value:a}=_;return[w,a,C,q]}),{border:$,direction:v}=this.descriptions,c=v==="vertical",b=()=>{var _,w,C;return((C=(w=(_=this.cell)==null?void 0:_.children)==null?void 0:w.label)==null?void 0:C.call(w))||m.label},W=()=>{var _,w,C;return(C=(w=(_=this.cell)==null?void 0:_.children)==null?void 0:w.default)==null?void 0:C.call(w)},y=m.span,z=m.rowspan,E=m.align?`is-${m.align}`:"",k=m.labelAlign?`is-${m.labelAlign}`:E,I=m.className,f=m.labelClassName,M=this.type==="label"&&(m.labelWidth||this.descriptions.labelWidth)||m.width,o={width:ce(M),minWidth:ce(m.minWidth)},e=fe("descriptions");switch(this.type){case"label":return te(Z(this.tag,{style:o,class:[e.e("cell"),e.e("label"),e.is("bordered-label",$),e.is("vertical-label",c),k,f],colSpan:c?y:1,rowspan:c?1:z},b()),i);case"content":return te(Z(this.tag,{style:o,class:[e.e("cell"),e.e("content"),e.is("bordered-content",$),e.is("vertical-content",c),E,I],colSpan:c?y:y*2-1,rowspan:c?z*2-1:z},W()),i);default:{const _=b(),w={},C=ce(m.labelWidth||this.descriptions.labelWidth);return C&&(w.width=C,w.display="inline-block"),te(Z("td",{style:o,class:[e.e("cell"),E],colSpan:y,rowspan:z},[Fe(_)?void 0:Z("span",{style:w,class:[e.e("label"),f]},_),Z("span",{class:[e.e("content"),I]},W())]),i)}}}});const pt=se({row:{type:Re(Array),default:()=>[]}}),mt=Q({name:"ElDescriptionsRow"}),ft=Q({...mt,props:pt,setup(u){const m=Se(ge,{});return(i,$)=>t(m).direction==="vertical"?(s(),p(B,{key:0},[g("tr",null,[(s(!0),p(B,null,R(i.row,(v,c)=>(s(),A(t(G),{key:`tr1-${c}`,cell:v,tag:"th",type:"label"},null,8,["cell"]))),128))]),g("tr",null,[(s(!0),p(B,null,R(i.row,(v,c)=>(s(),A(t(G),{key:`tr2-${c}`,cell:v,tag:"td",type:"content"},null,8,["cell"]))),128))])],64)):(s(),p("tr",{key:1},[(s(!0),p(B,null,R(i.row,(v,c)=>(s(),p(B,{key:`tr3-${c}`},[t(m).border?(s(),p(B,{key:0},[r(t(G),{cell:v,tag:"td",type:"label"},null,8,["cell"]),r(t(G),{cell:v,tag:"td",type:"content"},null,8,["cell"])],64)):(s(),A(t(G),{key:1,cell:v,tag:"td",type:"both"},null,8,["cell"]))],64))),128))]))}});var gt=me(ft,[["__file","descriptions-row.vue"]]);const yt=se({border:Boolean,column:{type:Number,default:3},direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},size:qe,title:{type:String,default:""},extra:{type:String,default:""},labelWidth:{type:[String,Number],default:""}}),De="ElDescriptionsItem",_t=Q({name:"ElDescriptions"}),ht=Q({..._t,props:yt,setup(u){const m=u,i=fe("descriptions"),$=Le(),v=ke();Ke(ge,m);const c=K(()=>[i.b(),i.m($.value)]),b=(y,z,E,k=!1)=>(y.props||(y.props={}),z>E&&(y.props.span=E),k&&(y.props.span=z),y),W=()=>{if(!v.default)return[];const y=He(v.default()).filter(M=>{var o;return((o=M==null?void 0:M.type)==null?void 0:o.name)===De}),z=[];let E=[],k=m.column,I=0;const f=[];return y.forEach((M,o)=>{var e,_,w;const C=((e=M.props)==null?void 0:e.span)||1,q=((_=M.props)==null?void 0:_.rowspan)||1,a=z.length;if(f[a]||(f[a]=0),q>1)for(let n=1;n<q;n++)f[w=a+n]||(f[w]=0),f[a+n]++,I++;if(f[a]>0&&(k-=f[a],f[a]=0),o<y.length-1&&(I+=C>k?k:C),o===y.length-1){const n=m.column-I%m.column;E.push(b(M,n,k,!0)),z.push(E);return}C<k?(k-=C,E.push(M)):(E.push(b(M,C,k)),z.push(E),k=m.column,E=[])}),z};return(y,z)=>(s(),p("div",{class:V(t(c))},[y.title||y.extra||y.$slots.title||y.$slots.extra?(s(),p("div",{key:0,class:V(t(i).e("header"))},[g("div",{class:V(t(i).e("title"))},[Y(y.$slots,"title",{},()=>[T(h(y.title),1)])],2),g("div",{class:V(t(i).e("extra"))},[Y(y.$slots,"extra",{},()=>[T(h(y.extra),1)])],2)],2)):P("v-if",!0),g("div",{class:V(t(i).e("body"))},[g("table",{class:V([t(i).e("table"),t(i).is("bordered",y.border)])},[g("tbody",null,[(s(!0),p(B,null,R(W(),(E,k)=>(s(),A(gt,{key:k,row:E},null,8,["row"]))),128))])],2)],2)],2))}});var vt=me(ht,[["__file","description.vue"]]);const be=["left","center","right"],bt=se({label:{type:String,default:""},span:{type:Number,default:1},rowspan:{type:Number,default:1},width:{type:[String,Number],default:""},minWidth:{type:[String,Number],default:""},labelWidth:{type:[String,Number],default:""},align:{type:String,values:be,default:"left"},labelAlign:{type:String,values:be},className:{type:String,default:""},labelClassName:{type:String,default:""}}),ze=Q({name:De,props:bt}),xt=we(vt,{DescriptionsItem:ze}),kt=Qe(ze);function wt(){const u=$e(),m=F(!1),i=F([]),$=ve({current:1,pageSize:20,total:0}),v=ve({triggerType:null,status:null,userId:null}),c=K(()=>u.isAdmin),b=async()=>{m.value=!0;try{const o={skip:($.current-1)*$.pageSize,limit:$.pageSize,...Object.fromEntries(Object.entries(v).filter(([_,w])=>w!=null))},e=c.value?await J.getAllExecutionsAdmin(o):await J.getAllExecutions(o);console.log(e),e&&e.data?(i.value=Array.isArray(e.data.items)?e.data.items:[],$.total=e.data.total||0):(console.warn("API响应格式异常:",e),i.value=[],$.total=0)}catch(o){console.error("加载任务执行记录失败:",o);let e="加载任务执行记录失败";o.response?o.response.status===401?e="未授权，请重新登录":o.response.status===403?e="权限不足":o.response.status>=500?e="服务器内部错误，请稍后重试":o.response.data&&o.response.data.detail&&(e=o.response.data.detail):o.request?e="网络连接失败，请检查网络状态":o.message&&(e=o.message),H.error(e),i.value=[],$.total=0}finally{m.value=!1}};return{loading:m,taskExecutions:i,pagination:$,filters:v,isAdmin:c,loadTaskExecutions:b,cancelExecution:async o=>{try{await J.cancelExecution(o),H.success("任务取消成功"),await b()}catch(e){console.error("取消任务执行失败:",e);let _="任务取消失败";throw e.response&&e.response.data&&e.response.data.detail?_=e.response.data.detail:e.message&&(_=e.message),H.error(_),e}},deleteExecution:async o=>{try{await J.deleteExecution(o),H.success("执行记录删除成功"),await b()}catch(e){console.error("删除执行记录失败:",e);let _="执行记录删除失败";throw e.response&&e.response.data&&e.response.data.detail?_=e.response.data.detail:e.message&&(_=e.message),H.error(_),e}},refreshData:async()=>{await b()},resetFilters:()=>{v.triggerType=null,v.status=null,v.userId=null,$.current=1,b()},getStatusTagType:o=>({pending:"info",running:"warning",completed:"success",failed:"danger",cancelled:"info"})[o]||"info",getStatusText:o=>({pending:"待执行",running:"执行中",completed:"已完成",failed:"已失败",cancelled:"已取消"})[o]||o,formatDuration:o=>{if(!o)return"-";if(o<60)return`${o}秒`;const e=Math.floor(o/60),_=o%60;if(e<60)return`${e}分${_}秒`;const w=Math.floor(e/60),C=e%60;return`${w}时${C}分${_}秒`},formatDateTime:o=>o?new Date(o).toLocaleString("zh-CN"):"-"}}const St={key:0,class:"execution-details"},$t={class:"detail-section"},Et={key:0},Tt={key:1,class:"text-gray-500"},Ct={class:"font-medium"},Dt={class:"text-xs text-gray-500"},zt={class:"config-display"},At={class:"config-item"},Mt={key:0,class:"config-item"},Nt={class:"stock-codes"},Pt={key:0},It={class:"config-item"},Vt={class:"config-item"},Bt={key:0,class:"detail-section"},Wt={key:1,class:"detail-section"},Ot={class:"dialog-footer"},Ut={__name:"TaskExecutionDetails",props:{modelValue:{type:Boolean,default:!1},execution:{type:Object,default:null}},emits:["update:modelValue","cancel-execution"],setup(u,{emit:m}){const i=u,$=m,v=$e(),c=F(i.modelValue),b=F({currentPage:1,pageSize:20,total:0}),W=K(()=>v.isAdmin),y=K(()=>i.execution&&(i.execution.status==="pending"||i.execution.status==="running")),z=K(()=>{var U;if(!((U=i.execution)!=null&&U.results_data))return b.value.total=0,[];const a=Array.isArray(i.execution.results_data)?i.execution.results_data:[];b.value.total=a.length;const n=(b.value.currentPage-1)*b.value.pageSize,x=n+b.value.pageSize,O=a.slice(n,x);return console.log(`Paginating results: ${n} to ${x}, total: ${a.length}, result:`,O),O}),E=[{prop:"stock_code",label:"股票代码","min-width":100,fixed:"left"},{prop:"stock_name",label:"股票名称",minWidth:120},{prop:"price",label:"股价","min-width":80,formatter:(a,n,x,O)=>typeof x=="number"?x.toFixed(2):x},{prop:"signals",label:"信号",minWidth:100,slot:"signals"},{prop:"scan_time",label:"扫描时间","min-width":180,formatter:(a,n,x,O)=>x?x.replace("T"," ").substring(0,19):""},{prop:"period",label:"周期","min-width":80,formatter:(a,n,x,O)=>({d:"日线",w:"周线",m:"月线"})[x]||x}];ue(()=>i.modelValue,a=>{c.value=a}),ue(c,a=>{$("update:modelValue",a)}),ue(()=>i.execution,()=>{b.value.currentPage=1});const k=()=>{var a;return(a=i.execution)!=null&&a.task_config_raw?i.execution.task_config_raw:{}},I=a=>({pending:"info",running:"warning",completed:"success",failed:"danger",cancelled:"info"})[a]||"info",f=a=>({pending:"待执行",running:"执行中",completed:"已完成",failed:"已失败",cancelled:"已取消"})[a]||a,M=a=>({buy:"success",sell:"danger",hold:"info"})[a]||"info",o=a=>({d:"日线",w:"周线",m:"月线"})[a]||a,e=a=>({n:"不复权",qfq:"前复权",hfq:"后复权"})[a]||a,_=a=>a?new Date(a).toLocaleString("zh-CN"):"-",w=a=>{if(!a)return"-";if(a<60)return`${a}秒`;const n=Math.floor(a/60),x=a%60;if(n<60)return`${n}分${x}秒`;const O=Math.floor(n/60),U=n%60;return`${O}时${U}分${x}秒`},C=({page:a,size:n})=>{b.value.currentPage=a,b.value.pageSize=n},q=async()=>{if(i.execution)try{await pe.confirm("确认取消此任务执行？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await J.cancelExecution(i.execution.id),H.success("任务取消成功"),$("cancel-execution",i.execution.id),c.value=!1}catch(a){a!=="cancel"&&(H.error("任务取消失败"),console.error(a))}};return(a,n)=>{const x=kt,O=xt,U=dt,ee=Te,ae=nt;return s(),A(ae,{modelValue:c.value,"onUpdate:modelValue":n[1]||(n[1]=N=>c.value=N),title:"任务执行详情",width:"80%","close-on-click-modal":!1,"append-to-body":"",style:{"--el-dialog-margin-top":"5vh"}},{footer:l(()=>[g("span",Ot,[r(ee,{onClick:n[0]||(n[0]=N=>c.value=!1)},{default:l(()=>n[9]||(n[9]=[T("关闭")])),_:1,__:[9]}),y.value?(s(),A(ee,{key:0,type:"danger",onClick:q},{default:l(()=>n[10]||(n[10]=[T(" 取消执行 ")])),_:1,__:[10]})):P("",!0)])]),default:l(()=>[u.execution?(s(),p("div",St,[g("div",$t,[n[6]||(n[6]=g("h3",null,"基本信息",-1)),r(O,{column:2,border:""},{default:l(()=>[r(x,{label:"执行ID"},{default:l(()=>[T(h(u.execution.id),1)]),_:1}),r(x,{label:"任务名称"},{default:l(()=>[u.execution.scheduled_task_name?(s(),p("span",Et,h(u.execution.scheduled_task_name),1)):(s(),p("span",Tt,"手动扫描"))]),_:1}),r(x,{label:"触发方式"},{default:l(()=>[r(t(j),{type:u.execution.trigger_type==="scheduled"?"success":"info",size:"small"},{default:l(()=>[T(h(u.execution.trigger_type==="scheduled"?"定时触发":"手动触发"),1)]),_:1},8,["type"])]),_:1}),r(x,{label:"执行状态"},{default:l(()=>[r(t(j),{type:I(u.execution.status),size:"small"},{default:l(()=>[T(h(f(u.execution.status)),1)]),_:1},8,["type"])]),_:1}),r(x,{label:"开始时间"},{default:l(()=>[T(h(_(u.execution.start_time)),1)]),_:1}),r(x,{label:"结束时间"},{default:l(()=>[T(h(_(u.execution.end_time)),1)]),_:1}),r(x,{label:"执行时长"},{default:l(()=>[T(h(w(u.execution.duration_seconds)),1)]),_:1}),r(x,{label:"结果数量"},{default:l(()=>[r(t(j),{type:"info",size:"small"},{default:l(()=>[T(h(u.execution.results_count)+" 条 ",1)]),_:1})]),_:1}),W.value&&u.execution.user_username?(s(),A(x,{key:0,label:"执行用户"},{default:l(()=>[g("div",null,[g("div",Ct,h(u.execution.user_username),1),g("div",Dt,h(u.execution.user_email),1)])]),_:1})):P("",!0),r(x,{label:"创建时间"},{default:l(()=>[T(h(_(u.execution.created_at)),1)]),_:1}),u.execution.task_config?(s(),A(x,{key:1,label:"任务配置",span:2},{default:l(()=>[g("div",zt,[g("div",At,[n[2]||(n[2]=g("span",{class:"config-label"},"扫描指标：",-1)),(s(!0),p(B,null,R(k().indicators||[],N=>(s(),A(t(j),{key:N,class:"mr-1 mb-1",size:"small"},{default:l(()=>[T(h(N),1)]),_:2},1024))),128))]),k().stock_codes?(s(),p("div",Mt,[n[3]||(n[3]=g("span",{class:"config-label"},"股票代码：",-1)),g("div",Nt,[(s(!0),p(B,null,R(k().stock_codes.slice(0,10),N=>(s(),A(t(j),{key:N,class:"mr-1 mb-1",size:"small",type:"success"},{default:l(()=>[T(h(N),1)]),_:2},1024))),128)),k().stock_codes.length>10?(s(),p("span",Pt," ... 等 "+h(k().stock_codes.length)+" 只股票 ",1)):P("",!0)])])):P("",!0),g("div",It,[n[4]||(n[4]=g("span",{class:"config-label"},"扫描周期：",-1)),(s(!0),p(B,null,R(k().periods||["d"],N=>(s(),A(t(j),{key:N,class:"mr-1",size:"small",type:"warning"},{default:l(()=>[T(h(o(N)),1)]),_:2},1024))),128))]),g("div",Vt,[n[5]||(n[5]=g("span",{class:"config-label"},"复权方式：",-1)),T(" "+h(e(k().adjust)),1)])])]),_:1})):P("",!0)]),_:1})]),u.execution.error_message?(s(),p("div",Bt,[n[7]||(n[7]=g("h3",null,"错误信息",-1)),r(U,{title:u.execution.error_message,type:"error",closable:!1,"show-icon":""},null,8,["title"])])):P("",!0),u.execution.results_data&&u.execution.results_data.length>0?(s(),p("div",Wt,[g("h3",null,[n[8]||(n[8]=T(" 执行结果 ")),r(t(j),{size:"small",type:"info"},{default:l(()=>[T(h(u.execution.results_count)+" 条",1)]),_:1})]),r(Ce,{data:z.value,columns:E,pagination:b.value,onPageChange:C},{signals:l(({row:N})=>[N.signals&&Array.isArray(N.signals)?(s(!0),p(B,{key:0},R(N.signals,X=>(s(),A(t(j),{key:X,type:M(X),size:"small",class:"mr-1"},{default:l(()=>[T(h(X),1)]),_:2},1032,["type"]))),128)):P("",!0)]),_:1},8,["data","pagination"])])):P("",!0)])):P("",!0)]),_:1},8,["modelValue"])}}},jt=Ee(Ut,[["__scopeId","data-v-3935e595"]]),Ft={class:"task-history-page card"},Rt={class:"filters-section"},qt={class:"task-list-section card"},Lt={key:0},Ht={class:"font-medium"},Kt={class:"text-xs text-gray-500"},Qt={key:1,class:"text-gray-400"},Xt={key:0,class:"font-medium"},Zt={key:1,class:"text-gray-500 italic"},Gt={key:0},Jt={key:1},Yt={key:2},es={key:0},ts={key:1},ss={class:"table-actions"},as={__name:"index",setup(u){const{loading:m,taskExecutions:i,pagination:$,filters:v,isAdmin:c,loadTaskExecutions:b,cancelExecution:W,deleteExecution:y,refreshData:z,getStatusTagType:E,getStatusText:k,formatDuration:I,formatDateTime:f}=wt(),M=F([]),o=F(!1),e=F(null),_=F(!1);let w=null;const C=K(()=>{const S=[{prop:"id",label:"ID","min-width":80,fixed:"left"}];return c.value&&S.push({prop:"user_info",label:"用户","min-width":140,slot:"user_info"}),S.push({prop:"task_name",label:"任务名称","min-width":160,slot:"task_name"},{prop:"trigger_type",label:"触发方式","min-width":100,slot:"trigger_type"},{prop:"status",label:"执行状态","min-width":100,slot:"status"},{prop:"results_count",label:"结果数量","min-width":100},{prop:"duration",label:"执行时长","min-width":100,slot:"duration"},{prop:"start_time",label:"开始时间","min-width":160,slot:"start_time"}),S}),q=async()=>{if(c.value){_.value=!0;try{const S=await Je.getUserList();S&&S.data?M.value=Array.isArray(S.data)?S.data:[]:M.value=[]}catch(S){console.error("加载用户列表失败:",S),H.error("加载用户列表失败"),M.value=[]}finally{_.value=!1}}},a=()=>{$.current=1,b()},n=({page:S,size:D})=>{$.current=S,$.pageSize=D,b()},x=S=>{e.value=S,o.value=!0},O=S=>S.status==="pending"||S.status==="running",U=S=>S.status!=="pending"&&S.status!=="running",ee=async S=>{try{await pe.confirm("确认取消此任务执行？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await W(S)}catch(D){D!=="cancel"&&console.error(D)}},ae=async S=>{try{await pe.confirm("确认删除此执行记录？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await y(S)}catch(D){D!=="cancel"&&console.error(D)}},N=()=>{w=setInterval(async()=>{try{await b()}catch(S){console.warn("轮询更新失败:",S)}},1e4)},X=()=>{w&&(clearInterval(w),w=null)};return Xe(async()=>{c.value&&await q(),await z(),N()}),Ze(()=>{X()}),(S,D)=>{const L=at,ne=st,le=tt,Ae=et,ye=j,oe=Te,ie=Ye;return s(),p("div",Ft,[D[6]||(D[6]=g("div",{class:"page-header"},[g("h1",null,"任务历史")],-1)),g("div",Rt,[r(Ae,{inline:"",onSubmit:D[3]||(D[3]=Ge(()=>{},["prevent"]))},{default:l(()=>[r(le,{label:"触发类型"},{default:l(()=>[r(ne,{modelValue:t(v).triggerType,"onUpdate:modelValue":D[0]||(D[0]=d=>t(v).triggerType=d),placeholder:"全部",clearable:"",onChange:a,style:{width:"150px"}},{default:l(()=>[r(L,{label:"手动触发",value:"manual"}),r(L,{label:"定时触发",value:"scheduled"})]),_:1},8,["modelValue"])]),_:1}),r(le,{label:"执行状态"},{default:l(()=>[r(ne,{modelValue:t(v).status,"onUpdate:modelValue":D[1]||(D[1]=d=>t(v).status=d),placeholder:"全部",clearable:"",onChange:a,style:{width:"150px"}},{default:l(()=>[r(L,{label:"待执行",value:"pending"}),r(L,{label:"执行中",value:"running"}),r(L,{label:"已完成",value:"completed"}),r(L,{label:"已失败",value:"failed"}),r(L,{label:"已取消",value:"cancelled"})]),_:1},8,["modelValue"])]),_:1}),t(c)?(s(),A(le,{key:0,label:"用户"},{default:l(()=>[r(ne,{modelValue:t(v).userId,"onUpdate:modelValue":D[2]||(D[2]=d=>t(v).userId=d),placeholder:"全部用户",clearable:"",filterable:"",loading:_.value,onChange:a,style:{width:"150px"}},{default:l(()=>[(s(!0),p(B,null,R(M.value,d=>(s(),A(L,{key:d.id,label:`${d.username} (${d.email})`,value:d.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1})):P("",!0)]),_:1})]),g("div",qt,[r(Ce,{data:t(i),loading:t(m),columns:C.value,stripe:!0,"show-pagination":!0,pagination:{total:t($).total,pageSizes:[20,50,100],layout:"total, sizes, prev, pager, next, jumper"},"current-page":t($).current,"page-size":t($).pageSize,"empty-text":"暂无任务执行记录",onPageChange:n,class:"task-history-table"},{user_info:l(({row:d})=>[d.user_username?(s(),p("div",Lt,[g("div",Ht,h(d.user_username),1),g("div",Kt,h(d.user_email),1)])):(s(),p("div",Qt,"未知用户"))]),task_name:l(({row:d})=>[d.scheduled_task_name?(s(),p("div",Xt,h(d.scheduled_task_name),1)):(s(),p("div",Zt," 手动扫描 "))]),trigger_type:l(({row:d})=>[r(ye,{type:d.trigger_type==="scheduled"?"success":"info",size:"small"},{default:l(()=>[T(h(d.trigger_type==="scheduled"?"定时":"手动"),1)]),_:2},1032,["type"])]),status:l(({row:d})=>[r(ye,{type:t(E)(d.status),size:"small"},{default:l(()=>[T(h(t(k)(d.status)),1)]),_:2},1032,["type"])]),duration:l(({row:d})=>[d.duration_seconds?(s(),p("span",Gt,h(t(I)(d.duration_seconds)),1)):d.status==="running"?(s(),p("span",Jt,D[5]||(D[5]=[g("i",{class:"i-carbon-time animate-spin"},null,-1)]))):(s(),p("span",Yt,"-"))]),start_time:l(({row:d})=>[d.start_time?(s(),p("span",es,h(t(f)(d.start_time)),1)):(s(),p("span",ts,"-"))]),actions:l(({row:d})=>[g("div",ss,[r(ie,{content:"查看详情",placement:"top"},{default:l(()=>[r(oe,{text:"",size:"small",circle:"",onClick:_e=>x(d),class:"action-btn-view"},{default:l(()=>[r(de,{name:"view"})]),_:2},1032,["onClick"])]),_:2},1024),O(d)?(s(),A(ie,{key:0,content:"取消执行",placement:"top"},{default:l(()=>[r(oe,{text:"",size:"small",circle:"",onClick:_e=>ee(d.id),class:"action-btn-cancel"},{default:l(()=>[r(de,{name:"stop"})]),_:2},1032,["onClick"])]),_:2},1024)):P("",!0),U(d)?(s(),A(ie,{key:1,content:"删除记录",placement:"top"},{default:l(()=>[r(oe,{text:"",size:"small",circle:"",onClick:_e=>ae(d.id),class:"action-btn-delete"},{default:l(()=>[r(de,{name:"delete"})]),_:2},1032,["onClick"])]),_:2},1024)):P("",!0)])]),_:1},8,["data","loading","columns","pagination","current-page","page-size"])]),r(jt,{modelValue:o.value,"onUpdate:modelValue":D[4]||(D[4]=d=>o.value=d),execution:e.value},null,8,["modelValue","execution"])])}}},ps=Ee(as,[["__scopeId","data-v-e67d9701"]]);export{ps as default};
