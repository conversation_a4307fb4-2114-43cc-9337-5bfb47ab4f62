import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建 axios 实例
const apiClient = axios.create({
  baseURL: '/api/v1', // 通过 Vite 代理到后端，包含API版本前缀
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  },
  withCredentials: true // 允许携带cookie
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // JWT token处理
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    // 统一处理后端格式 {data, status, message}
    const res = response.data
    console.log(res, typeof res);
    
    // 只有当响应明确包含这三个字段时才提取data
    if (res && typeof res === 'object' && 
        'data' in res && 'status' in res && 'message' in res &&
        Object.keys(res).length === 3) {
      return res.data
    }
    return res
  },
  async (error) => {
    const { response, config } = error
    
    if (response) {
      switch (response.status) {
        case 401:
          // JWT token过期，尝试刷新（但不对refresh请求本身进行重试）
          if (!config._retry && !config.url.includes('/auth/refresh')) {
            config._retry = true
            
            try {
              // 动态导入auth store并尝试刷新token
              const { useAuthStore } = await import('@/store/modules/auth')
              const authStore = useAuthStore()
              const success = await authStore.refreshToken()
              
              if (success) {
                // 重试原请求
                const newToken = localStorage.getItem('access_token')
                config.headers.Authorization = `Bearer ${newToken}`
                return apiClient.request(config)
              }
            } catch (refreshError) {
              console.error('Token刷新失败:', refreshError)
            }
          }
          
          // 刷新失败，清除认证状态并跳转到登录页
          ElMessage.error('会话已过期，请重新登录')
          
          try {
            const { useAuthStore } = await import('@/store/modules/auth')
            const authStore = useAuthStore()
            authStore.clearAuth()
            
            // 使用路由跳转而不是window.location
            const router = (await import('@/router')).default
            const currentPath = router.currentRoute.value.fullPath
            if (currentPath !== '/login') {
              router.push({
                path: '/login',
                query: { redirect: currentPath }
              })
            }
          } catch (error) {
            console.error('清除认证状态失败:', error)
            // 降级处理
            localStorage.removeItem('access_token')
            if (!window.location.pathname.includes('/login')) {
              window.location.href = `/login?redirect=${encodeURIComponent(window.location.pathname)}`
            }
          }
          break
        case 403:
          console.error('权限不足')
          break
        case 404:
          console.error('资源不存在')
          break
        case 500:
          console.error('服务器内部错误')
          break
        default:
          console.error(`请求失败: ${response.status}`)
      }
      return Promise.reject(new Error(response.data?.detail || response.data?.message || '请求失败'))
    } else {
      return Promise.reject(new Error('网络连接失败，请检查网络设置'))
    }
  }
)

export default apiClient
