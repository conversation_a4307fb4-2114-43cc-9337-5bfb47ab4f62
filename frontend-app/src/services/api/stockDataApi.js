import apiClient from '../apiClient'

/**
 * 股票数据API服务
 * 提供股票数据相关的API调用
 */
export const stockDataApi = {  /**
   * 获取股票K线数据
   * @param {string} stockCode - 股票代码
   * @param {string} freq - 时间周期，例如：D(日线), W(周线), M(月线)
   * @param {Object} params - 额外参数，如开始结束时间等
   * @returns {Promise}
   */
  getKlineData(stockCode, freq = 'D', params = {}) {
    // 基于后端K线分析API
    return apiClient.get(`/analytics/kline/${stockCode}`, { 
      params: { freq, ...params }
    })
  },
  
  /**
   * 获取股票历史数据（指定时间范围）
   * @param {string} stockCode - 股票代码
   * @param {string} timeframe - 时间周期
   * @param {string} startDate - 开始时间
   * @param {string} endDate - 结束时间
   * @returns {Promise}
   */
  getHistoricalData(stockCode, timeframe, startDate, endDate) {
    // 基于API文档中的自选时段历史分时交易API
    return apiClient.get(`/stocks/kline/${stockCode}/${timeframe}/${startDate}/${endDate}`)
  },
  
  /**
   * 获取实时交易数据
   * @param {string} stockCode - 股票代码
   * @returns {Promise}
   */
  getRealtimeData(stockCode) {
    return apiClient.get(`/stocks/realtime/${stockCode}`)
  },
  
  /**
   * 批量获取实时交易数据
   * @param {Array} stockCodes - 股票代码数组
   * @returns {Promise}
   */
  getBatchRealtimeData(stockCodes) {
    return apiClient.post('/stocks/realtime/batch', { codes: stockCodes })
  }
}

export default stockDataApi
