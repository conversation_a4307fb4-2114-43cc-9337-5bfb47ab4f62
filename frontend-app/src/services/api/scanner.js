import apiClient from '../apiClient'

// 扫描器相关API
export const scannerApi = {
  // 创建会话
  createSession() {
    return apiClient({
      url: 'v1/scan/session/create',
      method: 'post',
      data: {}
    })
  },

  // 开始扫描
  startScan(data) {
    return apiClient({
      url: 'v1/scan/start',
      method: 'post',
      data
    })
  },

  // 停止扫描
  stopScan(taskId) {
    return apiClient({
      url: `v1/scan/${taskId}/stop`,
      method: 'post'
    })
  },

  // 获取扫描进度
  getProgress(taskId) {
    return apiClient({
      url: `v1/scan/${taskId}/progress`,
      method: 'get'
    })
  },

  // 获取扫描结果
  getResults(taskId, page = 1, pageSize = 20) {
    return apiClient({
      url: `v1/scan/${taskId}/results`,
      method: 'get',
      params: {
        page,
        page_size: pageSize
      }
    })
  },

  // 获取扫描任务详情
  getTask(taskId) {
    return apiClient({
      url: `v1/scan/${taskId}`,
      method: 'get'
    })
  },

  // 获取活跃的扫描任务
  getActiveTasks() {
    return apiClient({
      url: 'v1/scan/active',
      method: 'get'
    })
  },

  // 获取当前活跃任务 (单个)
  async getActiveTask() {
    const response = await this.getActiveTasks()
    // 返回第一个活跃任务，如果有的话
    return response && response.length > 0 ? response[0] : null
  }
}

export default scannerApi