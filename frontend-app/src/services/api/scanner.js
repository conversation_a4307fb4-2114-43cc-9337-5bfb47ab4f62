import apiClient from '../apiClient'

// 扫描器相关API (已移除session相关API，直接使用JWT认证)
export const scannerApi = {
  // 开始扫描
  startScan(data) {
    return apiClient({
      url: 'scan/start',
      method: 'post',
      data
    })
  },

  // 停止扫描
  stopScan(taskId) {
    return apiClient({
      url: `scan/${taskId}/stop`,
      method: 'post'
    })
  },

  // 获取扫描进度
  getProgress(taskId) {
    return apiClient({
      url: `scan/${taskId}/progress`,
      method: 'get'
    })
  },

  // 获取扫描结果
  getResults(taskId, page = 1, pageSize = 20) {
    return apiClient({
      url: `scan/${taskId}/results`,
      method: 'get',
      params: {
        page,
        page_size: pageSize
      }
    })
  },

  // 获取扫描任务详情
  getTask(taskId) {
    return apiClient({
      url: `scan/${taskId}`,
      method: 'get'
    })
  },

  // 获取活跃的扫描任务（从TaskExecution获取）
  getActiveTasks() {
    return apiClient({
      url: 'scan/active',
      method: 'get'
    })
  }
}

export default scannerApi