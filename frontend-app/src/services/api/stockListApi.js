import apiClient from '../apiClient'

/**
 * 股票列表API服务
 * 提供股票列表相关的API调用
 */
export const stockListApi = {
  /**
   * 获取股票列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getStockList(params = {}) {
    return apiClient.get('/stocks/list/', { params })
  },

  /**
   * 获取单个股票信息
   * @param {string} stockCode - 股票代码
   * @returns {Promise}
   */
  getStockInfo(stockCode) {
    return apiClient.get(`/stocks/list/${stockCode}`)
  },

  /**
   * 搜索股票
   * @param {string} query - 搜索关键词
   * @param {number} limit - 限制返回数量
   * @returns {Promise}
   */
  searchStocks(query, limit = 20) {
    return apiClient.get('/stocks/list/', {
      params: {
        search: query,
        limit
      }
    })
  },

  /**
   * 补充股票数据
   * @returns {Promise}
   */
  enrichStockData() {
    return apiClient.post('/stocks/list/enrich')
  },

  /**
   * 刷新股票列表数据
   * 从运营商处拉取最新股票列表数据
   * @param {Object} options - 刷新选项
   * @param {string} options.data_source - 数据源（tushare/akshare/mairui），为空时使用系统默认
   * @returns {Promise}
   */
  refreshStockList(options = {}) {
    return apiClient.post('/stocks/list/refresh', {
      data_source: options.data_source || null
    })
  }
}

export default stockListApi
