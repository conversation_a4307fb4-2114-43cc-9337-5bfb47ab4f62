import apiClient from '../apiClient'

export const scheduledTasksApi = {
  // 获取任务列表
  getTasks(params) {
    return apiClient.get('/scheduled-tasks/', { params })
  },

  // 创建任务
  createTask(data) {
    return apiClient.post('/scheduled-tasks/', data)
  },

  // 获取任务详情
  getTask(taskId) {
    return apiClient.get(`/scheduled-tasks/${taskId}`)
  },

  // 更新任务
  updateTask(taskId, data) {
    return apiClient.put(`/scheduled-tasks/${taskId}`, data)
  },

  // 删除任务
  deleteTask(taskId) {
    return apiClient.delete(`/scheduled-tasks/${taskId}`)
  },

  // 手动执行任务
  executeTask(taskId) {
    return apiClient.post(`/scheduled-tasks/${taskId}/execute`)
  },

  // 切换任务状态
  toggleTaskStatus(taskId, isActive) {
    return apiClient.put(`/scheduled-tasks/${taskId}/toggle`, null, {
      params: { is_active: isActive }
    })
  },

  // 获取执行记录
  getExecutions(taskId, params) {
    return apiClient.get(`/scheduled-tasks/${taskId}/executions`, { params })
  },

  // 获取所有执行记录
  getAllExecutions(params) {
    return apiClient.get('/task-executions/', { params })
  },

  // 获取执行详情
  getExecutionDetail(executionId) {
    return apiClient.get(`/task-executions/${executionId}`)
  },

  // 删除执行记录
  deleteExecution(executionId) {
    return apiClient.delete(`/task-executions/${executionId}`)
  },

  // 取消执行
  cancelExecution(executionId) {
    return apiClient.post(`/task-executions/${executionId}/cancel`)
  },

  // 管理员获取所有任务执行记录
  getAllExecutionsAdmin(params) {
    return apiClient.get('/task-executions/admin/all', { params })
  }
}