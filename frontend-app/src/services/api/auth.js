import apiClient from '@/services/apiClient'

export const authApi = {
  // 用户登录
  login: (credentials) => {
    return apiClient.post('/auth/login', credentials)
  },

  // 刷新token
  refreshToken: (token) => {
    return apiClient.post('/auth/refresh', { token })
  },

  // 获取当前用户信息
  getCurrentUser: () => {
    return apiClient.get('/auth/me')
  },

  // 用户登出
  logout: () => {
    return apiClient.post('/auth/logout')
  },

  // 获取用户列表（管理员）
  getUserList: (page = 1, pageSize = 20) => {
    return apiClient.get('/users/list', {
      params: { page, page_size: pageSize }
    })
  },

  // 创建用户（管理员）
  createUser: (userData) => {
    return apiClient.post('/users/', userData)
  },

  // 更新用户信息（管理员）
  updateUser: (userId, userData) => {
    return apiClient.put(`/users/${userId}`, userData)
  },

  // 切换用户状态（管理员）
  toggleUserStatus: (userId, isActive) => {
    return apiClient.patch(`/users/${userId}/status`, { is_active: isActive })
  },

  // 删除用户（管理员）
  deleteUser: (userId) => {
    return apiClient.delete(`/users/${userId}`)
  },

  // 重置用户密码（管理员）
  resetPassword: (userId, newPassword) => {
    return apiClient.patch(`/users/${userId}/password`, { password: newPassword })
  }
}

export default authApi