import apiClient from '../apiClient'

/**
 * 指数技术指标API服务
 * 提供指数技术分析指标相关的API调用
 */
export const indexIndicatorApi = {
  /**
   * 获取指数基础技术指标
   * @param {string} indexCode - 指数代码
   * @param {string} indicator - 指标名称
   * @param {Object} params - 指标参数
   * @returns {Promise}
   */
  getIndicator(indexCode, indicator, params = {}) {
    return apiClient.get(`/index-indicators/${indicator}/${indexCode}`, { params })
  },
  
  /**
   * 获取指数MACD指标
   * @param {string} indexCode - 指数代码
   * @param {Object} params - 指标参数
   * @returns {Promise}
   */
  getMacd(indexCode, params = {}) {
    return apiClient.get(`/index-indicators/macd/${indexCode}`, { params })
  },

  /**
   * 获取指数KDJ指标
   * @param {string} indexCode - 指数代码
   * @param {Object} params - 指标参数
   * @returns {Promise}
   */
  getKdj(indexCode, params = {}) {
    return apiClient.get(`/index-indicators/kdj/${indexCode}`, { params })
  },

  /**
   * 获取指数RSI指标
   * @param {string} indexCode - 指数代码
   * @param {Object} params - 指标参数
   * @returns {Promise}
   */
  getRsi(indexCode, params = {}) {
    return apiClient.get(`/index-indicators/rsi/${indexCode}`, { params })
  },

  /**
   * 获取指数成交量分析
   * @param {string} indexCode - 指数代码
   * @param {Object} params - 指标参数
   * @returns {Promise}
   */
  getVolumeAnalysis(indexCode, params = {}) {
    return apiClient.get(`/index-indicators/volume/${indexCode}`, { params })
  },

  /**
   * 批量获取指数多个指标
   * @param {string} indexCode - 指数代码
   * @param {Array} indicators - 指标名称数组
   * @param {Object} commonParams - 通用参数
   * @returns {Promise}
   */
  getBatchIndicators(indexCode, indicators, commonParams = {}) {
    const promises = indicators.map(indicator => 
      this.getIndicator(indexCode, indicator, commonParams)
        .catch(err => {
          console.warn(`获取指数指标 ${indicator} 失败:`, err)
          return null
        })
    )
    return Promise.all(promises)
  }
}

export default indexIndicatorApi