import apiClient from '../apiClient'

/**
 * 指数列表API服务
 * 提供指数列表相关的API调用
 */
export const indexListApi = {
  /**
   * 获取指数列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getIndexList(params = {}) {
    return apiClient.get('/indices/', { params })
  },

  /**
   * 获取单个指数信息
   * @param {string} indexCode - 指数代码
   * @returns {Promise}
   */
  getIndexInfo(indexCode) {
    return apiClient.get(`/indices/${indexCode}`)
  },

  /**
   * 搜索指数
   * @param {string} query - 搜索关键词
   * @param {number} limit - 限制返回数量
   * @returns {Promise}
   */
  searchIndices(query, limit = 20) {
    return apiClient.get('/indices/', {
      params: {
        search: query,
        limit
      }
    })
  },

  /**
   * 刷新指数列表数据
   * 从运营商处拉取最新指数列表数据
   * @param {Object} options - 刷新选项
   * @param {string} options.data_source - 数据源（tushare/akshare/mairui），为空时使用系统默认
   * @returns {Promise}
   */
  refreshIndexList(options = {}) {
    return apiClient.post('/indices/refresh', {
      data_source: options.data_source || null
    })
  }
}

export default indexListApi