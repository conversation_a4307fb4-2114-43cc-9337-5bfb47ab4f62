// 指数相关API服务
import apiClient from '../apiClient'

// 获取指数列表
export async function getIndices(params = {}) {
  const {
    skip = 0,
    limit = 100,
    exchange = null,
    is_active = null,
    search = null
  } = params
  
  const queryParams = new URLSearchParams({ skip, limit })
  if (exchange) queryParams.append('exchange', exchange)
  if (is_active !== null) queryParams.append('is_active', is_active)
  if (search) queryParams.append('search', search)
  
  const response = await apiClient.get(`/indices/?${queryParams}`)
  return response.data
}

// 获取单个指数信息
export async function getIndex(indexCode, includeLatestData = true) {
  const queryParams = new URLSearchParams()
  if (includeLatestData) queryParams.append('include_latest_data', 'true')
  
  const url = queryParams.toString() 
    ? `/indices/${indexCode}?${queryParams}`
    : `/indices/${indexCode}`
    
  const response = await apiClient.get(url)
  return response.data
}

// 获取指数日线数据
export async function getIndexDailyData(indexCode, params = {}) {
  const {
    start_date = null,
    end_date = null,
    limit = 250
  } = params
  
  const queryParams = new URLSearchParams({ limit })
  if (start_date) queryParams.append('start_date', start_date)
  if (end_date) queryParams.append('end_date', end_date)
  
  const response = await apiClient.get(`/indices/${indexCode}/daily?${queryParams}`)
  return response.data
}

// 获取指数建议
export async function getIndexSuggestions(query, limit = 10) {
  const queryParams = new URLSearchParams({ query, limit })
  const response = await apiClient.get(`/indices/suggestions/?${queryParams}`)
  return response.data
}

// 刷新指数列表
export async function refreshIndexList(dataSource = null) {
  const payload = {}
  if (dataSource) payload.data_source = dataSource
  
  const response = await apiClient.post('/indices/refresh', payload)
  return response.data
}

// 同步指数历史数据
export async function syncIndexDailyData(indexCode, params = {}) {
  const {
    start_date = null,
    end_date = null
  } = params
  
  const queryParams = new URLSearchParams()
  if (start_date) queryParams.append('start_date', start_date)
  if (end_date) queryParams.append('end_date', end_date)

  const response = await apiClient.post(`/indices/${indexCode}/sync-daily?${queryParams}`)
  return response.data
}