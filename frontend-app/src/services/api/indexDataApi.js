import apiClient from '../apiClient'

/**
 * 指数数据API服务
 * 提供指数K线数据相关的API调用，模仿股票数据API
 */
export const indexDataApi = {
  /**
   * 获取指数K线数据
   * @param {string} indexCode - 指数代码
   * @param {string} freq - 时间周期，例如：D(日线), W(周线), M(月线)
   * @param {Object} params - 额外参数，如开始结束时间等
   * @returns {Promise}
   */
  getKlineData(indexCode, freq = 'D', params = {}) {
    // 基于后端指数K线分析API
    return apiClient.get(`/analytics/index-kline/${indexCode}`, { 
      params: { freq, ...params }
    })
  },
  
  /**
   * 获取指数历史数据（指定时间范围）
   * @param {string} indexCode - 指数代码
   * @param {string} timeframe - 时间周期
   * @param {string} startDate - 开始时间
   * @param {string} endDate - 结束时间
   * @returns {Promise}
   */
  getHistoricalData(indexCode, timeframe, startDate, endDate) {
    // 使用指数K线API获取历史数据
    return apiClient.get(`/analytics/index-kline/${indexCode}`, {
      params: {
        freq: timeframe,
        start_date: startDate,
        end_date: endDate
      }
    })
  },

  /**
   * 获取指数实时数据
   * @param {string} indexCode - 指数代码
   * @returns {Promise}
   */
  getRealTimeData(indexCode) {
    // 使用指数基础API获取实时信息
    return apiClient.get(`/indices/${indexCode}`, {
      params: { include_latest_data: true }
    })
  },

  /**
   * 批量获取指数基础信息
   * @param {Array} indexCodes - 指数代码数组
   * @returns {Promise}
   */
  getBatchIndexInfo(indexCodes) {
    const promises = indexCodes.map(code => 
      apiClient.get(`/indices/${code}`, {
        params: { include_latest_data: true }
      })
    )
    return Promise.all(promises)
  }
}

export default indexDataApi