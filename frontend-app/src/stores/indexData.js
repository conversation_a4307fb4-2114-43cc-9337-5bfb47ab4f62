// 指数数据状态管理
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import * as indicesApi from '@/services/api/indices'

export const useIndexDataStore = defineStore('indexData', () => {
  // 状态
  const indices = ref([])
  const selectedIndex = ref(null)
  const indexDailyData = ref([])
  const loading = ref(false)
  const error = ref(null)
  
  // 常见的大盘指数配置
  const popularIndices = ref([
    { code: '000001.SH', name: '上证指数', exchange: 'SH' },
    { code: '399001.SZ', name: '深证成指', exchange: 'SZ' },
    { code: '399006.SZ', name: '创业板指', exchange: 'SZ' },
    { code: '000300.SH', name: '沪深300', exchange: 'SH' },
    { code: '000905.SH', name: '中证500', exchange: 'SH' },
    { code: '000688.SH', name: '科创50', exchange: 'SH' }
  ])
  
  // 指数名称映射，用于判断是否为指数
  const indexNames = computed(() => {
    return new Set([
      '上证指数', '深证成指', '创业板指', '沪深300', 
      '中证500', '科创50', '中小板指', '上证50',
      '中证1000', '恒生指数', '纳斯达克', '标普500'
    ])
  })
  
  // Getters/Computed
  const getIndexByCode = computed(() => {
    return (code) => indices.value.find(index => index.code === code)
  })
  
  const isIndexName = computed(() => {
    return (name) => indexNames.value.has(name)
  })
  
  // Actions
  const fetchIndices = async (params = {}) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await indicesApi.getIndices(params)
      if (response) {
        indices.value = response.indices || []
        return response
      } else {
        throw new Error(response.message || '获取指数列表失败')
      }
    } catch (err) {
      error.value = err.message
      console.error('获取指数列表失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const fetchIndexInfo = async (indexCode) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await indicesApi.getIndex(indexCode)
      if (response) {
        selectedIndex.value = response
        return response
      } else {
        throw new Error(response.message || '获取指数信息失败')
      }
    } catch (err) {
      error.value = err.message
      console.error('获取指数信息失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const fetchIndexDailyData = async (indexCode, params = {}) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await indicesApi.getIndexDailyData(indexCode, params)
      if (response) {
        indexDailyData.value = response
        return response
      } else {
        throw new Error(response.message || '获取指数数据失败')
      }
    } catch (err) {
      error.value = err.message
      console.error('获取指数数据失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const refreshIndices = async (dataSource = null) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await indicesApi.refreshIndexList(dataSource)
      if (response.success) {
        // 刷新后重新获取列表
        await fetchIndices()
        return response
      } else {
        throw new Error(response.message || '刷新指数列表失败')
      }
    } catch (err) {
      error.value = err.message
      console.error('刷新指数列表失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const syncIndexData = async (indexCode, params = {}) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await indicesApi.syncIndexDailyData(indexCode, params)
      if (response) {
        return response
      } else {
        throw new Error(response.message || '同步指数数据失败')
      }
    } catch (err) {
      error.value = err.message
      console.error('同步指数数据失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const getIndexSuggestions = async (query, limit = 10) => {
    try {
      const response = await indicesApi.getIndexSuggestions(query, limit)
      if (response) {
        return response
      } else {
        throw new Error(response.message || '获取指数建议失败')
      }
    } catch (err) {
      console.error('获取指数建议失败:', err)
      throw err
    }
  }
  
  // 重置状态
  const resetState = () => {
    indices.value = []
    selectedIndex.value = null
    indexDailyData.value = []
    loading.value = false
    error.value = null
  }
  
  // 清除错误
  const clearError = () => {
    error.value = null
  }
  
  return {
    // State
    indices,
    selectedIndex,
    indexDailyData,
    loading,
    error,
    popularIndices,
    
    // Getters
    getIndexByCode,
    isIndexName,
    indexNames,
    
    // Actions
    fetchIndices,
    fetchIndexInfo,
    fetchIndexDailyData,
    refreshIndices,
    syncIndexData,
    getIndexSuggestions,
    resetState,
    clearError
  }
})