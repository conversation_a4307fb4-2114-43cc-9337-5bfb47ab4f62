import { useLocalStorage } from '@vueuse/core'

// 默认指标参数配置（基于后端 scanner_calculator.py 常量）
const DEFAULT_PARAMETERS = {
  kdj: {
    n: 9,    // KDJ周期
    m1: 3,    // K值平滑周期
    m2: 3     // D值平滑周期
  },
  volume_pressure: {
    ema_period: 10  // EMA周期
  },
  bollinger: {
    window: 20,    // 布林带窗口
    std_dev: 2.0   // 标准差倍数
  }
}

/**
 * 统一的存储管理 Composable
 * 使用 VueUse 管理 localStorage 和参数配置
 */
export function useStorage() {
  // 使用 useLocalStorage 持久化指标参数配置
  const indicatorParameters = useLocalStorage('scanner-indicator-parameters', DEFAULT_PARAMETERS)

  // 重置参数为默认值
  const resetParameters = () => {
    indicatorParameters.value = { ...DEFAULT_PARAMETERS }
  }

  // 更新指定指标的参数
  const updateIndicatorParameters = (indicator, params) => {
    if (indicatorParameters.value[indicator]) {
      indicatorParameters.value[indicator] = { ...indicatorParameters.value[indicator], ...params }
    }
  }

  // 获取指定指标的参数
  const getIndicatorParameters = (indicator) => {
    return indicatorParameters.value[indicator] || DEFAULT_PARAMETERS[indicator]
  }

  // 格式化参数显示文本
  const formatParameterText = (indicator) => {
    const params = getIndicatorParameters(indicator)
    
    switch (indicator) {
      case 'kdj':
        return `n=${params.n}, m1=${params.m1}, m2=${params.m2}`
      case 'volume_pressure':
        return `EMA=${params.ema_period}`
      case 'bollinger':
        return `窗口=${params.window}, 标准差=${params.std_dev}`
      default:
        return ''
    }
  }

  return {
    indicatorParameters,
    resetParameters,
    updateIndicatorParameters,
    getIndicatorParameters,
    formatParameterText,
    DEFAULT_PARAMETERS
  }
}