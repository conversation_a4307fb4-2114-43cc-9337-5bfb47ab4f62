// SCSS 变量文件 - 迁移自 doc/index.html 的 CSS 变量
// 深色主题变量 - 优化版
$bg-primary: #0a0e1a;
$bg-secondary: #1a1f2e;
$bg-tertiary: #2a2f3e;
$bg-quaternary: #3a3f4e;
$text-primary: #ffffff;
$text-secondary: #e2e8f0;
$text-muted: #94a3b8;
$text-accent: #60a5fa;
$border-color: #374151;
$border-light: #4b5563;
$accent-primary: #3b82f6;
$accent-secondary: #8b5cf6;
$accent-tertiary: #06b6d4;

// 按钮背景变量 - 半透明玻璃态效果
$btn-bg-primary: rgba(255, 255, 255, 0.05);
$btn-bg-secondary: rgba(255, 255, 255, 0.1);
$btn-bg-tertiary: rgba(255, 255, 255, 0.15);
$btn-bg-quaternary: rgba(255, 255, 255, 0.2);

// 卡片背景变量 - 深色主题 (透明白)
$card-bg-primary-dark: rgba(255, 255, 255, 0.02);
$card-bg-secondary-dark: rgba(255, 255, 255, 0.05);
$card-bg-tertiary-dark: rgba(255, 255, 255, 0.08);
$card-bg-quaternary-dark: rgba(255, 255, 255, 0.12);

// 卡片背景变量 - 浅色主题 (透明黑)
$card-bg-primary-light: rgba(0, 0, 0, 0.02);
$card-bg-secondary-light: rgba(0, 0, 0, 0.04);
$card-bg-tertiary-light: rgba(0, 0, 0, 0.06);
$card-bg-quaternary-light: rgba(0, 0, 0, 0.08);

// 金融色彩 - 中国习惯（涨红跌绿）
$price-up: #ef4444;       // 红色表示涨
$price-up-bg: rgba(239, 68, 68, 0.1);
$price-down: #10b981;     // 绿色表示跌  
$price-down-bg: rgba(16, 185, 129, 0.1);
$price-flat: #6b7280;
$volume-color: #f59e0b;
$volume-bg: rgba(245, 158, 11, 0.1);

// 现代渐变色
$gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
$gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
$gradient-warning: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
$gradient-danger: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
$gradient-info: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
$gradient-dark: linear-gradient(135deg, #232526 0%, #414345 100%);

// 玻璃态效果
$glass-bg: rgba(255, 255, 255, 0.05);
$glass-border: rgba(255, 255, 255, 0.1);
$glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

// 阴影系统
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
$shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);

// 浅色主题变量 - 优化版
$light-bg-primary: #ffffff;
$light-bg-secondary: #f8fafc;
$light-bg-tertiary: #f1f5f9;
$light-bg-quaternary: #e2e8f0;
$light-text-primary: #0f172a;
$light-text-secondary: #334155;
$light-text-muted: #64748b;
$light-text-accent: #3b82f6;
$light-border-color: #e2e8f0;
$light-border-light: #f1f5f9;
$light-accent-primary: #3b82f6;
$light-accent-secondary: #8b5cf6;
$light-accent-tertiary: #06b6d4;
$light-glass-bg: rgba(255, 255, 255, 0.9);
$light-glass-border: rgba(0, 0, 0, 0.1);
$light-shadow-glow: 0 0 20px rgba(59, 130, 246, 0.15);

// 扫描指标卡片背景色
$indicator-card-bg: rgba(0,0,0,0.03); // 浅色未选中
$indicator-card-bg-active: rgba(59,130,246,0.18); // 浅色选中（主色高亮）
$indicator-card-bg-dark: rgba(255,255,255,0.05); // 未选中：主色高亮
$indicator-card-bg-active-dark: rgba(255,255,255,0.2);        // 选中：灰色卡片

// 过渡动画
$transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
$transition-fast: all 0.2s ease;
$transition-slow: all 0.5s ease;

// 字体
$font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
$font-mono: 'JetBrains Mono', 'Monaco', 'Consolas', 'SF Mono', monospace;

// 断点
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;
$breakpoint-2xl: 1536px;
