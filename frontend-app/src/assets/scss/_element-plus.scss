// Element Plus 全局样式覆盖
// 使用更高权重覆盖 Element Plus 默认样式

// 定义 Element Plus CSS 变量映射
html,
body,
#app {
  // 主要颜色变量
  --el-color-primary: var(--accent-primary);
  --el-color-primary-light-3: rgba(59, 130, 246, 0.7);
  --el-color-primary-light-5: rgba(59, 130, 246, 0.5);
  --el-color-primary-light-7: rgba(59, 130, 246, 0.3);
  --el-color-primary-light-8: rgba(59, 130, 246, 0.2);
  --el-color-primary-light-9: rgba(59, 130, 246, 0.1);
  --el-color-primary-dark-2: #2563eb;
  
  // 成功色
  --el-color-success: var(--price-up);
  --el-color-success-light-3: rgba(16, 185, 129, 0.7);
  --el-color-success-light-5: rgba(16, 185, 129, 0.5);
  --el-color-success-light-7: rgba(16, 185, 129, 0.3);
  --el-color-success-light-8: rgba(16, 185, 129, 0.2);
  --el-color-success-light-9: rgba(16, 185, 129, 0.1);
  
  // 危险色
  --el-color-danger: var(--price-down);
  --el-color-danger-light-3: rgba(239, 68, 68, 0.7);
  --el-color-danger-light-5: rgba(239, 68, 68, 0.5);
  --el-color-danger-light-7: rgba(239, 68, 68, 0.3);
  --el-color-danger-light-8: rgba(239, 68, 68, 0.2);
  --el-color-danger-light-9: rgba(239, 68, 68, 0.1);
  
  // 警告色
  --el-color-warning: var(--volume-color);
  --el-color-warning-light-3: rgba(245, 158, 11, 0.7);
  --el-color-warning-light-5: rgba(245, 158, 11, 0.5);
  --el-color-warning-light-7: rgba(245, 158, 11, 0.3);
  --el-color-warning-light-8: rgba(245, 158, 11, 0.2);
  --el-color-warning-light-9: rgba(245, 158, 11, 0.1);
  
  // 信息色
  --el-color-info: var(--text-muted);
  --el-color-info-light-3: rgba(148, 163, 184, 0.7);
  --el-color-info-light-5: rgba(148, 163, 184, 0.5);
  --el-color-info-light-7: rgba(148, 163, 184, 0.3);
  --el-color-info-light-8: rgba(148, 163, 184, 0.2);
  --el-color-info-light-9: rgba(148, 163, 184, 0.1);
  
  // 文本颜色
  --el-text-color-primary: var(--text-primary);
  --el-text-color-regular: var(--text-secondary);
  --el-text-color-secondary: var(--text-muted);
  --el-text-color-placeholder: var(--text-muted);
  
  // 边框颜色
  --el-border-color: var(--border-color);
  --el-border-color-light: var(--border-light);
  --el-border-color-lighter: rgba(55, 65, 81, 0.5);
  --el-border-color-extra-light: rgba(55, 65, 81, 0.3);
  --el-border: var(--el-border-width) var(--el-border-style) var(--el-border-color);

  
  // 背景颜色
  --el-bg-color: var(--bg-secondary);
  --el-bg-color-page: var(--bg-primary);
  --el-bg-color-overlay: var(--bg-tertiary);
  
  // 填充颜色
  --el-fill-color: var(--bg-tertiary);
  --el-fill-color-light: var(--bg-quaternary);
  --el-fill-color-lighter: rgba(58, 63, 78, 0.5);
  --el-fill-color-extra-light: rgba(58, 63, 78, 0.3);
  --el-fill-color-blank: transparent;
  
  // 阴影
  --el-box-shadow: var(--shadow-md);
  --el-box-shadow-light: var(--shadow-sm);
  --el-box-shadow-base: var(--shadow-md);
  --el-box-shadow-dark: var(--shadow-lg);
  
  // 禁用状态
  --el-disabled-bg-color: var(--bg-tertiary);
  --el-disabled-text-color: var(--text-muted);
  --el-disabled-border-color: var(--border-color);
  
  // 蒙层颜色
  --el-overlay-color: rgba(0, 0, 0, 0.8);
  --el-overlay-color-light: rgba(0, 0, 0, 0.7);
  --el-overlay-color-lighter: rgba(0, 0, 0, 0.5);
  
  // 遮罩颜色
  --el-mask-color: rgba(255, 255, 255, 0.9);
  --el-mask-color-extra-light: rgba(255, 255, 255, 0.3);
  
  // 边框半径
  --el-border-radius-base: 4px;
  --el-border-radius-small: 2px;
  --el-border-radius-round: 50%;
  --el-border-radius-circle: 50%;
}

// Dialog 组件样式覆盖 - 使用更高权重
.el-dialog {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 16px !important;
  box-shadow: var(--shadow-xl) !important;
  
  .el-dialog__header {
    background-color: var(--bg-secondary) !important;
    border-bottom: 1px solid var(--border-color) !important;
    padding: 20px 24px 16px !important;
    
    .el-dialog__title {
      color: var(--text-primary) !important;
      font-weight: 600 !important;
      font-size: 1.125rem !important;
    }
  }
  
  .el-dialog__headerbtn {
    top: 16px !important;
    right: 16px !important;
    width: 32px !important;
    height: 32px !important;
    
    .el-dialog__close {
      color: var(--text-secondary) !important;
      font-size: 18px !important;
      
      &:hover {
        color: var(--text-primary) !important;
      }
    }
  }
  
  .el-dialog__body {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    padding: 20px 24px !important;
  }
  
  .el-dialog__footer {
    background-color: var(--bg-secondary) !important;
    border-top: 1px solid var(--border-color) !important;
    padding: 16px 24px 0px !important;
    text-align: right !important;
  }
}

// Message Box 样式覆盖
.el-message-box {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 16px !important;
  box-shadow: var(--shadow-xl) !important;
  
  .el-message-box__header {
    .el-message-box__title {
      color: var(--text-primary) !important;
    }
  }
  
  .el-message-box__content {
    color: var(--text-secondary) !important;
    
    .el-message-box__message {
      color: var(--text-secondary) !important;
    }
  }
}

// Input 输入框样式覆盖
.el-input {
  .el-input__wrapper {
    background-color: var(--bg-tertiary) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: none !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    
    &:hover {
      border-color: var(--border-light) !important;
    }
    
    &.is-focus {
      border-color: var(--accent-primary) !important;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
    }
    
    .el-input__inner {
      color: var(--text-primary) !important;
      
      &::placeholder {
        color: var(--text-muted) !important;
      }
    }
  }
  
  &.is-disabled {
    .el-input__wrapper {
      background-color: var(--bg-quaternary) !important;
      border-color: var(--border-color) !important;
      
      .el-input__inner {
        color: var(--text-muted) !important;
      }
    }
  }
}

// Select 选择器样式覆盖
.el-select {
  .el-select__wrapper {
    background-color: var(--bg-tertiary) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 12px !important;
    
    &:hover {
      border-color: var(--border-light) !important;
    }
    
    &.is-focused {
      border-color: var(--accent-primary) !important;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
    }
    
    .el-select__selected-item {
      color: var(--text-primary) !important;
    }
    
    .el-select__placeholder {
      color: var(--text-muted) !important;
    }
  }
}

// Dropdown 下拉菜单样式覆盖
.el-select-dropdown {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 12px !important;
  box-shadow: var(--shadow-lg) !important;
  
  .el-select-dropdown__item {
    color: var(--text-primary) !important;
    
    &:hover {
      background-color: var(--bg-tertiary) !important;
    }
    
    &.is-selected {
      background-color: rgba(59, 130, 246, 0.1) !important;
      color: var(--accent-primary) !important;
    }
  }
}

// Button 按钮样式覆盖
.el-button {
  border-radius: 12px !important;
  font-weight: 500 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  
  &--primary {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary)) !important;
    border: none !important;
    
    &:hover {
      transform: translateY(-2px) !important;
      box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4) !important;
    }
  }
  
  &--default {
    background-color: $btn-bg-secondary !important;
    border: none !important;
    color: var(--text-primary) !important;
    
    &:hover {
      background-color: $btn-bg-tertiary !important;
      transform: translateY(-1px) !important;
    }
    
    &:active {
      background-color: $btn-bg-primary !important;
      transform: translateY(0) !important;
    }
  }
}

// Card 卡片样式覆盖
.el-card {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 16px !important;
  box-shadow: var(--shadow-md) !important;
  
  .el-card__header {
    background-color: var(--bg-secondary) !important;
    border-bottom: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
  }
  
  .el-card__body {
    color: var(--text-primary) !important;
  }
}

// Table 表格样式覆盖
.el-table {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
  
  .el-table__header {
    th {
      background-color: var(--bg-tertiary) !important;
      border-bottom: 1px solid var(--border-color) !important;
      color: var(--text-primary) !important;
    }
  }
  
  .el-table__body {
    tr {
      &:hover {
        background-color: var(--bg-tertiary) !important;
      }
      
      td {
        border-bottom: 1px solid var(--border-color) !important;
        color: var(--text-primary) !important;
      }
    }
  }
}

// Form 表单样式覆盖
.el-form {
  .el-form-item {
    .el-form-item__label {
      color: var(--text-primary) !important;
      font-weight: 500 !important;
    }
    
    .el-form-item__error {
      color: var(--price-down) !important;
    }
  }
}

// Pagination 分页样式覆盖
.el-pagination {
  .el-pager {
    .number {
      background-color: var(--bg-tertiary) !important;
      border: 1px solid var(--border-color) !important;
      color: var(--text-primary) !important;
      border-radius: 8px !important;
      
      &:hover {
        background-color: var(--bg-quaternary) !important;
      }
      
      &.is-active {
        background-color: var(--accent-primary) !important;
        border-color: var(--accent-primary) !important;
      }
    }
  }
  
  .btn-prev,
  .btn-next {
    background-color: var(--bg-tertiary) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
    border-radius: 8px !important;
    
    &:hover {
      background-color: var(--bg-quaternary) !important;
    }
  }
}

// Tooltip 工具提示样式覆盖
.el-tooltip__popper {
  background-color: var(--bg-quaternary) !important;
  border: 1px solid var(--border-color) !important;
  color: var(--text-primary) !important;
  border-radius: 8px !important;
  box-shadow: var(--shadow-lg) !important;
  
  .el-tooltip__arrow {
    &::before {
      border-top-color: var(--bg-quaternary) !important;
      border-right-color: var(--bg-quaternary) !important;
      border-bottom-color: var(--bg-quaternary) !important;
      border-left-color: var(--bg-quaternary) !important;
    }
  }
}

// Loading 加载样式覆盖
.el-loading-mask {
  background-color: rgba(10, 14, 26, 0.8) !important;
  
  .el-loading-spinner {
    .path {
      stroke: var(--accent-primary) !important;
    }
    
    .el-loading-text {
      color: var(--text-primary) !important;
    }
  }
}

// Message 消息样式覆盖
.el-message {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 12px !important;
  box-shadow: var(--shadow-lg) !important;
  color: var(--text-primary) !important;
  position: fixed !important;
  min-width: 300px;
  padding: 16px !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  right: 20px !important;
  z-index: 9999 !important;
  
  &--error {
    border-color: var(--price-up) !important;
    
    .el-message__icon {
      color: var(--price-up) !important;
    }
  }
  
  &--success {
    border-color: var(--price-down) !important;
    
    .el-message__icon {
      color: var(--price-down) !important;
    }
  }
  
  &--warning {
    border-color: var(--volume-color) !important;
    
    .el-message__icon {
      color: var(--volume-color) !important;
    }
  }
  
  &--info {
    border-color: var(--accent-primary) !important;
    
    .el-message__icon {
      color: var(--accent-primary) !important;
    }
  }
}

// Notification 通知样式覆盖
.el-notification {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 12px !important;
  box-shadow: var(--shadow-xl) !important;
  
  .el-notification__title {
    color: var(--text-primary) !important;
  }
  
  .el-notification__content {
    color: var(--text-secondary) !important;
  }
}

// Popover 弹出框样式覆盖
.el-popover.el-popper {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 12px !important;
  box-shadow: var(--shadow-lg) !important;
  color: var(--text-primary) !important;
}

// DatePicker 日期选择器样式覆盖
.el-picker {
  .el-input {
    .el-input__wrapper {
      background-color: var(--bg-tertiary) !important;
      border: 1px solid var(--border-color) !important;
      // border-radius: 12px !important;
    }
  }
}

.el-picker-panel {
  background-color: var(--bg-secondary) !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 12px !important;
  box-shadow: var(--shadow-lg) !important;
  color: var(--text-primary) !important;
}