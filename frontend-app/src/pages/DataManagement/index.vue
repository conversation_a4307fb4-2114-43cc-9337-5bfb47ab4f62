<template>
  <div class="page-container p-6 bg-bg-primary text-text-primary">
    <!-- 股票列表管理 -->
    <div class="card bg-bg-secondary p-6 rounded-lg shadow-lg">
      <div class="flex items-center mb-6 gap-4">
        <h2 class="text-2xl font-semibold text-text-primary">股票列表管理</h2>
        <!-- <input
          type="text"
          class="input-field"
          placeholder="搜索股票代码或名称"
          v-model="searchQuery"
          @input="debouncedSearch"
        /> -->
        <div id="gap" class="flex-1"></div>
        <div class="flex space-x-3">
          <button class="btn-primary" @click="refreshData" :disabled="loading">
            <AppIcon name="activity" class="mr-2" />
            {{ loading ? "加载中..." : "刷新数据" }}
          </button>
          <!-- <button class="btn-secondary" @click="enrichData" :disabled="enriching">
            <AppIcon name="database" class="mr-2" />
            {{ enriching ? "补充中..." : "补充数据" }}
          </button> -->
          <button class="btn-secondary" @click="exportData">
            <AppIcon name="download" class="mr-2" />导出数据
          </button>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <input
          type="text"
          class="input-field"
          placeholder="搜索股票代码或名称"
          v-model="searchQuery"
          @input="debouncedSearch"
        />
        <!-- <select
          class="input-field"
          v-model="selectedIndustry"
          @change="filterData"
        >
          <option value="">所有行业</option>
          <option
            v-for="industry in industries"
            :key="industry"
            :value="industry"
          >
            {{ industry }}
          </option>
        </select> -->
        <select
          class="input-field"
          v-model="selectedExchange"
          @change="filterData"
        >
          <option value="">所有市场</option>
          <option value="SH">上证</option>
          <option value="SZ">深证</option>
        </select>
        <select class="input-field" v-model="isActive" @change="filterData">
          <option value="">所有状态</option>
          <option value="true">活跃</option>
          <option value="false">非活跃</option>
        </select>
        <button class="btn-primary" @click="resetFilters">
          <AppIcon name="filter" class="mr-2" />重置筛选
        </button>
      </div>
      <!-- 股票数据表格 -->
      <div class="overflow-x-auto">
        <table class="data-table w-full">
          <thead>
            <tr>
              <th class="p-3 text-left text-sm font-semibold text-text-muted">
                代码
              </th>
              <th class="p-3 text-left text-sm font-semibold text-text-muted">
                名称
              </th>
              <th class="p-3 text-left text-sm font-semibold text-text-muted">
                行业
              </th>
              <th class="p-3 text-left text-sm font-semibold text-text-muted">
                市场
              </th>
              <th class="p-3 text-left text-sm font-semibold text-text-muted">
                上市日期
              </th>
              <th class="p-3 text-right text-sm font-semibold text-text-muted">
                总股本
              </th>
              <th class="p-3 text-right text-sm font-semibold text-text-muted">
                流通股本
              </th>
              <th class="p-3 text-center text-sm font-semibold text-text-muted">
                状态
              </th>
              <!-- <th class="p-3 text-center text-sm font-semibold text-text-muted">
                操作
              </th> -->
            </tr>
          </thead>
          <tbody>
            <!-- 加载状态 -->
            <tr v-if="loading">
              <td colspan="9" class="p-8 text-center">
                <div class="flex items-center justify-center">
                  <div class="spinner mr-2"></div>
                  <span class="text-text-muted">加载中...</span>
                </div>
              </td>
            </tr>
            <!-- 无数据状态 -->
            <tr v-else-if="stocks.length === 0">
              <td colspan="9" class="p-8 text-center">
                <p class="text-text-muted">
                  {{ error ? "数据加载失败" : "暂无数据" }}
                </p>
                <button
                  v-if="error"
                  class="btn-primary mt-2"
                  @click="refreshData"
                >
                  重试
                </button>
              </td>
            </tr>
            <!-- 数据行 -->
            <tr
              v-else
              v-for="stock in stocks"
              :key="stock.code"
              class="border-b border-border-color hover:bg-bg-tertiary transition-colors"
            >
              <td class="p-3 text-sm text-text-secondary font-mono">
                {{ stock.code }}
              </td>
              <td class="p-3 text-sm text-text-primary font-medium">
                {{ stock.name }}
              </td>
              <td class="p-3 text-sm text-text-secondary">
                {{ stock.industry || "未分类" }}
              </td>
              <td class="p-3 text-sm text-text-secondary">
                {{ getExchangeName(stock.exchange) }}
              </td>
              <td class="p-3 text-sm text-text-secondary">
                {{ formatDate(stock.listing_date) }}
              </td>
              <td class="p-3 text-sm text-right text-text-secondary">
                {{ formatShares(stock.total_shares) }}
              </td>
              <td class="p-3 text-sm text-right text-text-secondary">
                {{ formatShares(stock.circulating_shares) }}
              </td>
              <td class="p-3 text-center">
                <span
                  :class="stock.is_active ? 'text-green-500' : 'text-red-500'"
                >
                  {{ stock.is_active ? "活跃" : "非活跃" }}
                </span>
              </td>
              <!-- <td class="p-3 text-center">
                <button
                  class="text-accent-primary hover:text-accent-secondary transition-colors mr-2"
                  @click="viewStock(stock)"
                  title="查看详情"
                >
                  <AppIcon name="align-box-middle-center" />
                </button>
                <button
                  class="text-blue-500 hover:text-blue-400 transition-colors mr-2"
                  @click="editStock(stock)"
                  title="编辑"
                >
                  <AppIcon name="edit" />
                </button>
                <button
                  class="text-red-500 hover:text-red-400 transition-colors"
                  @click="deleteStock(stock)"
                  title="删除"
                >
                  <AppIcon name="trash-can" />
                </button>
              </td> -->
            </tr>
          </tbody>
        </table>
      </div>     
       <!-- 分页 -->
      <CommonPagination
        :total="totalRecords"
        :current-page="currentPage"
        :page-size="pageSize"
        :show-filtered="!!(searchQuery || selectedExchange || isActive)"
        @page-change="handlePageChange"
        class="mt-6"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
// import { useRouter } from "vue-router"; // 暂时未使用
import AppIcon from "@/components/common/Icon.vue";
import CommonPagination from "@/components/common/CommonPagination.vue";
import api from "@/services/api";
import debounce from "lodash.debounce";

// const router = useRouter(); // 暂时未使用

// 响应式数据
const stocks = ref([]);
const loading = ref(false);
const enriching = ref(false);
const error = ref(null);
const searchQuery = ref("");
const selectedIndustry = ref("");
const selectedExchange = ref("");
const isActive = ref("");
const industries = ref([]);

// 分页相关
const currentPage = ref(1);
const pageSize = 20;
const totalRecords = ref(0);

// 页面变化处理
const handlePageChange = ({ page }) => {
  currentPage.value = page;
};


// 获取股票列表数据
const fetchStocks = async () => {
  try {
    loading.value = true;
    error.value = null;

    const params = {
      skip: (currentPage.value - 1) * pageSize,
      limit: pageSize,
    };

    // 添加筛选条件
    if (searchQuery.value.trim()) {
      params.search = searchQuery.value.trim();
    }
    if (selectedIndustry.value) {
      params.industry = selectedIndustry.value;
    }
    if (selectedExchange.value) {
      params.exchange = selectedExchange.value;
    }
    if (isActive.value) {
      params.is_active = isActive.value === "true";
    }
    const { data } = await api.stockList.getStockList(params);    console.log(data);
    if (data && data.stocks) {
      stocks.value = data.stocks;
      totalRecords.value = data.total || 0;

      // 提取行业列表
      if (industries.value.length === 0) {
        const uniqueIndustries = [
          ...new Set(
            data.stocks
              .map((stock) => stock.industry)
              .filter((industry) => industry && industry.trim())
          ),
        ];
        industries.value = uniqueIndustries.sort();
      }
    } else {
      stocks.value = [];
      totalRecords.value = 0;
    }
  } catch (err) {
    console.error("获取股票列表失败:", err);
    error.value = err.message || "获取股票列表失败";
    stocks.value = [];
    totalRecords.value = 0;
  } finally {
    loading.value = false;
  }
};

// 防抖搜索
const debouncedSearch = debounce(() => {
  currentPage.value = 1;
  fetchStocks();
}, 300);

// 刷新数据
const refreshData = () => {
  fetchStocks();
};

// 筛选数据
const filterData = () => {
  currentPage.value = 1;
  fetchStocks();
};

// 重置筛选
const resetFilters = () => {
  searchQuery.value = "";
  selectedIndustry.value = "";
  selectedExchange.value = "";
  isActive.value = "";
  currentPage.value = 1;
  fetchStocks();
};

// 导出数据
const exportData = () => {
  // TODO: 实现数据导出功能
  console.log("导出数据功能待实现");
};

// 数据补充 (暂时保留，虽然未使用)
// const enrichData = async () => {
//   try {
//     enriching.value = true;
//     error.value = null;

//     // 调用补充数据的 API
//     const response = await api.stockList.enrichStockData();

//     if (response?.data?.success !== false) {
//       // 补充成功后刷新数据
//       await fetchStocks();
      
//       const enrichedCount = response?.data?.data?.enriched_count || 0;
//       const message = response?.data?.data?.message || "数据补充完成";
      
//       // 显示成功消息（这里简单使用 alert，实际项目中可以使用更好的通知组件）
//       alert(`${message}\n补充了 ${enrichedCount} 只股票的详细信息`);
//     } else {
//       alert("数据补充失败：" + (response?.data?.error || "未知错误"));
//     }
//   } catch (err) {
//     console.error("补充数据失败:", err);
//     error.value = err.message || "补充数据失败";
//     alert("数据补充失败：" + (err.message || "网络错误"));
//   } finally {
//     enriching.value = false;
//   }
// };


// 股票操作方法 (暂时保留，虽然未使用)
// const viewStock = (stock) => {
//   router.push(`/stock/${stock.code}`);
// };

// const editStock = (stock) => {
//   // TODO: 实现编辑股票信息功能
//   console.log("编辑股票:", stock);
// };

// const deleteStock = (stock) => {
//   // TODO: 实现删除股票功能
//   console.log("删除股票:", stock);
// };

// 工具方法
const getExchangeName = (exchange) => {
  const exchangeMap = {
    SH: "上证",
    SZ: "深证",
  };
  return exchangeMap[exchange] || exchange;
};

const formatDate = (dateStr) => {
  if (!dateStr || dateStr === null || dateStr === 'null') return "暂无数据";
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return "暂无数据";
    return date.toLocaleDateString("zh-CN", {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  } catch {
    return "暂无数据";
  }
};

const formatShares = (shares) => {
  if (!shares || shares === null || shares === 0 || shares === 'null') return "暂无数据";
  const num = parseInt(shares);
  if (isNaN(num) || num <= 0) return "暂无数据";
  
  if (num >= 100000000) {
    return (num / 100000000).toFixed(1) + "亿";
  } else if (num >= 10000) {
    return (num / 10000).toFixed(1) + "万";
  }
  return num.toLocaleString();
};

// 监听页码变化
watch(currentPage, () => {
  fetchStocks();
});

// 组件挂载后加载数据
onMounted(() => {
  fetchStocks();
});
</script>

<style scoped>
/* 基础页面容器样式 */
.page-container {
  min-height: calc(100vh - var(--navbar-height, 60px)); /* 假设导航栏高度 */
}

/* 卡片样式 */
.card {
  border: 1px solid var(--border-color);
  /* box-shadow: var(--shadow-lg); */ /* 使用 Tailwind 的 shadow-lg 替代 */
}

/* 按钮样式 */
.btn-primary {
  background: linear-gradient(
    135deg,
    var(--accent-primary),
    var(--accent-secondary)
  );
  color: white;
  padding: 0.625rem 1.25rem; /* 10px 20px */
  border-radius: 0.5rem; /* 8px */
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem; /* 8px */
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
}

.btn-primary:disabled {
  background: var(--bg-tertiary);
  color: var(--text-muted);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-secondary {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  padding: 0.625rem 1.25rem;
  border-radius: 0.5rem;
  border: 1px solid var(--border-color);
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-secondary:hover {
  background-color: var(--bg-quaternary);
  border-color: var(--accent-primary);
}

.btn-secondary:disabled {
  background-color: var(--bg-secondary);
  color: var(--text-muted);
  cursor: not-allowed;
  border-color: var(--border-color);
}

/* 输入框样式 */
.input-field {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  padding: 0.625rem 1rem; /* 10px 16px */
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.input-field:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* 表格样式 */
.data-table th {
  background-color: var(--bg-tertiary);
}

.data-table th,
.data-table td {
  white-space: nowrap;
}

.text-price-up {
  color: var(--price-up);
}

.text-price-down {
  color: var(--price-down);
}

/* 加载动画 */
.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
