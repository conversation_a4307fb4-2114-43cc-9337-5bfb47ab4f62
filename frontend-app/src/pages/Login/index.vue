<template>
  <div class="login-container">
    <!-- 背景效果 -->
    <div class="background-gradient"></div>
    <div class="background-pattern"></div>
    
    <!-- 主要登录内容 -->
    <div class="login-content">
      <!-- Logo 和标题区域 -->
      <div class="login-header">
        <div class="logo-section">
          <div class="logo-icon">
            <Icon name="chart-line" :size="48" />
          </div>
          <h1 class="system-title">股票量化分析系统</h1>
          <p class="system-subtitle">用户认证登录</p>
        </div>
      </div>
      
      <!-- 登录表单卡片 -->
      <div class="login-card">
        <el-form 
          ref="loginFormRef" 
          :model="loginForm" 
          :rules="loginRules" 
          class="login-form"
          @submit.prevent="handleLogin"
        >
          <div class="form-title">
            <h2>登录账户</h2>
            <p>请输入您的用户名和密码</p>
          </div>
          
          <!-- 用户名输入框 -->
          <el-form-item prop="username" class="form-item">
            <div class="input-wrapper">
              <Icon name="user" class="input-icon" />
              <el-input
                v-model="loginForm.username"
                placeholder="请输入用户名"
                size="large"
                class="custom-input"
                @keyup.enter="handleLogin"
              />
            </div>
          </el-form-item>
          
          <!-- 密码输入框 -->
          <el-form-item prop="password" class="form-item">
            <div class="input-wrapper">
              <Icon name="locked" class="input-icon" />
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                size="large"
                class="custom-input"
                show-password
                @keyup.enter="handleLogin"
              />
            </div>
          </el-form-item>
          
          <!-- 记住登录选项 -->
          <el-form-item class="remember-item">
            <el-checkbox v-model="loginForm.remember_me" class="remember-checkbox">
              记住登录状态 (30天)
            </el-checkbox>
          </el-form-item>
          
          <!-- 登录按钮 -->
          <el-form-item class="login-button-item">
            <el-button
              type="primary"
              size="large"
              class="login-button"
              :loading="loading"
              @click="handleLogin"
            >
              <template v-if="!loading">
                <Icon name="arrow-right" class="button-icon" />
                登录系统
              </template>
              <template v-else>
                登录中...
              </template>
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 页脚信息 -->
      <div class="login-footer">
        <p>&copy; 2024 股票量化分析系统 - JWT认证版本</p>
        <p class="contact-info">还没有账号？请联系管理员创建</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/store'
import Icon from '@/components/common/Icon.vue'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 表单数据
const loginForm = reactive({
  username: '',
  password: '',
  remember_me: true
})

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码至少6位', trigger: 'blur' }
  ]
}

const loginFormRef = ref(null)
const loading = ref(false)

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    // 验证表单
    const valid = await loginFormRef.value.validate()
    if (!valid) return
    
    loading.value = true
    
    // 执行登录
    const result = await authStore.login(loginForm)
    
    if (result.success) {
      ElMessage.success('登录成功！')
      
      // 登录成功后跳转
      const redirectPath = route.query.redirect || '/'
      router.push(redirectPath)
    } else {
      ElMessage.error(result.message || '登录失败')
    }
    
  } catch (error) {
    console.error('登录失败:', error)
    ElMessage.error(error.message || '登录失败，请检查用户名和密码')
  } finally {
    loading.value = false
  }
}

// 组件挂载时检查是否已登录
onMounted(() => {
  if (authStore.isAuthenticated) {
    const redirectPath = route.query.redirect || '/'
    router.push(redirectPath)
  }
})
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-primary);
  position: relative;
  overflow: hidden;
  padding: 20px;
}

// 背景效果
.background-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at top, 
    rgba(59, 130, 246, 0.15) 0%, 
    transparent 50%),
    radial-gradient(ellipse at bottom right, 
    rgba(168, 85, 247, 0.1) 0%, 
    transparent 50%);
  z-index: 1;
}

.background-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(45deg, rgba(255, 255, 255, 0.02) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(255, 255, 255, 0.02) 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, rgba(255, 255, 255, 0.02) 75%),
    linear-gradient(-45deg, transparent 75%, rgba(255, 255, 255, 0.02) 75%);
  background-size: 60px 60px;
  background-position: 0 0, 0 30px, 30px -30px, -30px 0;
  z-index: 1;
}

.login-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 480px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

// 头部区域
.login-header {
  text-align: center;
  margin-bottom: 40px;
  animation: fadeIn 0.8s ease-out;
}

.logo-section {
  .logo-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    border-radius: 20px;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
    
    :deep(.icon) {
      color: white;
    }
  }
  
  .system-title {
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 8px;
    letter-spacing: -0.025em;
  }
  
  .system-subtitle {
    color: var(--text-secondary);
    font-size: 1rem;
    font-weight: 500;
  }
}

// 登录卡片
.login-card {
  width: 100%;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 24px;
  padding: 40px;
  box-shadow: var(--glass-shadow);
  animation: slideInUp 0.8s ease-out 0.2s both;
}

.login-form {
  .form-title {
    text-align: center;
    margin-bottom: 32px;
    
    h2 {
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 8px;
    }
    
    p {
      color: var(--text-secondary);
      font-size: 0.875rem;
    }
  }
  
  .form-item {
    margin-bottom: 24px;
    
    :deep(.el-form-item__error) {
      color: var(--price-down);
      font-size: 0.75rem;
      margin-top: 6px;
    }
  }
  
  .input-wrapper {
    position: relative;
    
    .input-icon {
      position: absolute;
      left: 16px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;
      color: var(--text-muted);
      transition: color 0.3s ease;
    }
    
    :deep(.el-input) {
      .el-input__wrapper {
        background: var(--bg-tertiary);
        border: 2px solid var(--border-color);
        border-radius: 16px;
        padding: 0 16px 0 48px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: none;
        
        &:hover {
          border-color: var(--accent-primary);
        }
        
        &.is-focus {
          border-color: var(--accent-primary);
          box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
        }
      }
      
      .el-input__inner {
        color: var(--text-primary);
        font-size: 0.875rem;
        font-weight: 500;
        height: 48px;
        
        &::placeholder {
          color: var(--text-muted);
        }
      }
    }
    
    &:focus-within .input-icon {
      color: var(--accent-primary);
    }
  }
  
  .remember-item {
    margin-bottom: 32px;
    
    :deep(.el-form-item__content) {
      justify-content: flex-start;
      align-items: center;
    }
    
    .remember-checkbox {
      :deep(.el-checkbox__label) {
        color: var(--text-secondary);
        font-size: 0.875rem;
        font-weight: 500;
      }
      
      :deep(.el-checkbox__inner) {
        background: var(--bg-tertiary);
        border-color: var(--border-color);
        border-radius: 6px;
        
        &:hover {
          border-color: var(--accent-primary);
        }
      }
      
      :deep(.el-checkbox__input.is-checked) {
        .el-checkbox__inner {
          background-color: var(--accent-primary);
          border-color: var(--accent-primary);
        }
      }
    }
  }
  
  .login-button-item {
    margin-bottom: 0;
    
    .login-button {
      width: 100%;
      height: 52px;
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
      border: none;
      border-radius: 16px;
      font-size: 1rem;
      font-weight: 600;
      color: white;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      
      .button-icon {
        margin-right: 8px;
      }
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.6s ease;
      }
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 40px rgba(59, 130, 246, 0.4);
        
        &::before {
          left: 100%;
        }
      }
      
      &:active {
        transform: translateY(0);
      }
      
      &.is-loading {
        background: var(--bg-quaternary);
        color: var(--text-secondary);
        
        &:hover {
          transform: none;
          box-shadow: none;
        }
      }
    }
  }
}

// 页脚
.login-footer {
  margin-top: 32px;
  text-align: center;
  color: var(--text-muted);
  font-size: 0.75rem;
  animation: fadeIn 1s ease-out 0.4s both;
  
  .contact-info {
    margin-top: 8px;
    font-size: 0.8rem;
  }
}

// 动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 640px) {
  .login-container {
    padding: 16px;
  }
  
  .login-card {
    padding: 32px 24px;
  }
  
  .login-header .logo-section {
    .logo-icon {
      width: 64px;
      height: 64px;
      margin-bottom: 16px;
    }
    
    .system-title {
      font-size: 1.5rem;
    }
  }
}
</style>