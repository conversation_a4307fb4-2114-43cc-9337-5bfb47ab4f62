<template>
  <div class="scanner-page">
    <div class="grid grid-cols-1 gap-6 mb-6">
      <!-- 扫描配置和操作 -->
      <div class="card p-6">
        <!-- 扫描模式选择 -->
        <div class="mb-6">
          <h2 class="text-2xl font-semibold text-text-primary mb-4">
            扫描模式
          </h2>
          <ScanModeSelector v-model="scanMode" :disabled="isScanning" />
        </div>

        <!-- 指标选择标题 -->
        <div class="flex items-end justify-start mb-4 gap-2">
          <h2 class="text-2xl font-semibold text-text-primary">扫描指标</h2>
          <div class="text-gray-500 text-sm">
            基于服务商提供的历史数据进行技术指标回测分析
          </div>
        </div>

        <!-- 指标网格和操作面板 -->
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
          <!-- 指标选择区域 -->
          <div class="xl:col-span-3">
            <div
              v-if="scanMode.scanMode === 'traditional'"
              class="h-full grid grid-cols-1 md:grid-cols-4 gap-4"
            >
              <label
                v-for="indicator in indicatorOptions"
                :key="indicator.value"
                class="relative cursor-pointer"
                :class="{ 'cursor-not-allowed opacity-50': isScanning }"
              >
                <input
                  type="checkbox"
                  v-model="scanForm.indicators"
                  :value="indicator.value"
                  :disabled="isScanning"
                  class="sr-only peer"
                />
                <div
                  class="indicator-card"
                  :class="{
                    'indicator-card--active': scanForm.indicators.includes(
                      indicator.value
                    ),
                    'indicator-card--disabled': isScanning,
                  }"
                >
                  <div class="text-center">
                    <Icon
                      :name="indicator.icon"
                      class="text-2xl mb-2 transition-colors"
                      :class="
                        scanForm.indicators.includes(indicator.value)
                          ? 'text-primary'
                          : themeStore.isDark
                          ? 'text-white text-opacity-90'
                          : 'text-gray-800'
                      "
                    />
                    <div
                      class="font-medium text-sm transition-colors"
                      :class="
                        scanForm.indicators.includes(indicator.value)
                          ? 'text-primary'
                          : themeStore.isDark
                          ? 'text-white text-opacity-90'
                          : 'text-gray-800'
                      "
                    >
                      {{ indicator.label }}
                    </div>
                    <div
                      class="text-xs mt-1 transition-colors"
                      :class="
                        scanForm.indicators.includes(indicator.value)
                          ? 'text-primary opacity-80'
                          : themeStore.isDark
                          ? 'text-white opacity-60'
                          : 'text-gray-500'
                      "
                    >
                      {{ formatParameterText(indicator.value) }}
                    </div>
                  </div>
                </div>
              </label>
            </div>
            <!-- 多周期矩阵式指标选择 -->
            <div v-else>
              <PeriodIndicatorMatrix
                v-model="periodIndicators"
                :scan-mode="scanMode.scanMode"
                :selected-periods="scanMode.periods"
                :disabled="isScanning"
              />
            </div>
          </div>

          <!-- 操作面板 -->
          <div class="xl:col-span-1">
            <div class="space-y-3">
              <!-- 参数配置按钮 -->
              <button
                @click="showParameterDialog = true"
                :disabled="isScanning"
                class="w-full px-4 py-3 rounded-lg transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center border-0"
                :class="
                  themeStore.isDark
                    ? 'bg-white bg-opacity-10 text-white hover:bg-opacity-20'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                "
              >
                <Icon name="settings" class="mr-2" />
                参数配置
              </button>

              <!-- 开始/停止按钮组 -->
              <div class="flex gap-2 items-center">
                <!-- 日期选择器 -->
                <DatePicker
                  v-model="endDate"
                  :disabled="isScanning"
                  placeholder="选择截止日期"
                  class="h-48px flex-1"
                />
                <button
                  v-if="!isScanning"
                  @click="startScan"
                  :disabled="isStarting || scanForm.indicators.length === 0"
                  class="btn-primary px-3 py-3 flex items-center justify-center min-w-80px"
                >
                  <Icon name="play" class="text-lg" />
                </button>
                <button
                  v-else
                  @click="stopScan"
                  :disabled="isStopping"
                  class="px-3 py-3 min-w-80px bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  <Icon name="stop" class="text-lg" />
                </button>
              </div>

              <!-- 扫描进度 -->
              <div class="space-y-2">
                <div class="flex items-center justify-between text-sm">
                  <span class="text-text-secondary">{{
                    currentTask ? progressText : "0 / 0"
                  }}</span>
                  <span class="text-primary font-medium"
                    >{{ currentTask ? progressPercentage : 0 }}%</span
                  >
                </div>
                <div
                  class="w-full rounded-full h-2 overflow-hidden"
                  :class="themeStore.isDark ? 'bg-bg-primary' : 'bg-gray-200'"
                >
                  <div
                    class="h-full transition-all duration-300 ease-out rounded-full"
                    :class="currentTask ? progressBarClass : 'bg-gray-400'"
                    :style="{
                      width: `${currentTask ? progressPercentage : 0}%`,
                    }"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 扫描结果 -->
        <ScannerResults class="mt-6" />

        <!-- 参数配置对话框 -->
        <ParameterConfigDialog
          v-model="showParameterDialog"
          :scan-mode="scanMode.scanMode"
          :selected-periods="scanMode.periods"
          @confirm="handleParameterConfirm"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import { ElMessage } from "element-plus";
import Icon from "@/components/common/Icon.vue";
import ScannerResults from "./ScannerResults.vue";
import ParameterConfigDialog from "./ParameterConfigDialog.vue";
import ScanModeSelector from "@/components/scanner/ScanModeSelector.vue";
import PeriodIndicatorMatrix from "@/components/scanner/PeriodIndicatorMatrix.vue";
import DatePicker from "@/components/scanner/DatePicker.vue";
import { useScannerStore } from "@/store";
import { useThemeStore } from "@/store";
import { useStorage } from "@/composables/useStorage";
import { scannerApi } from "@/services/api/scanner";
const themeStore = useThemeStore();

const scannerStore = useScannerStore();
const { formatParameterText, indicatorParameters, periodIndicatorParameters } =
  useStorage();

// 扫描模式配置
const scanMode = ref({
  scanMode: "traditional",
  scanStrategy: "parallel",
  periods: ["d"],
});

// 日期选择
const endDate = ref(null);

// 参数配置对话框显示状态
const showParameterDialog = ref(false);

// 指标选项
const indicatorOptions = [
  {
    value: "kdj",
    label: "KDJ指标",
    desc: "动量振荡器",
    icon: "chart-line",
  },
  {
    value: "volume_pressure",
    label: "成交量压力",
    desc: "成交量分析",
    icon: "volume-file-storage",
  },
  {
    value: "macd",
    label: "MACD指标",
    desc: "趋势指标",
    icon: "chart-line-data",
  },
  {
    value: "bollinger",
    label: "布林带",
    desc: "波动率指标",
    icon: "chart-area",
  },
];

// 扫描表单
const scanForm = ref({
  indicators: ["kdj", "volume_pressure"],
});

// 多周期指标选择
const periodIndicators = ref({
  d: ["kdj", "volume_pressure"],
  w: [],
  m: [],
});

// 状态标志
const isStarting = ref(false);
const isStopping = ref(false);

// 计算属性
const currentTask = computed(() => scannerStore.currentTask);
const isScanning = computed(() => scannerStore.isScanning);

const progressPercentage = computed(() => {
  if (!currentTask.value?.progress) return 0;
  return Math.round(currentTask.value.progress.percentage || 0);
});

const progressText = computed(() => {
  if (!currentTask.value?.progress) return "0 / 0";
  const { current, total } = currentTask.value.progress;
  return `${current} / ${total}`;
});

const progressBarClass = computed(() => {
  if (!currentTask.value) return "bg-gray-400";
  switch (currentTask.value.status) {
    case "completed":
      return "bg-green-500";
    case "failed":
      return "bg-red-500";
    default:
      return "bg-gray-400";
  }
});

// 方法
const startScan = async () => {
  // 根据扫描模式准备数据
  let scanData;

  if (scanMode.value.scanMode === "traditional") {
    // 传统模式验证
    if (scanForm.value.indicators.length === 0) {
      ElMessage.warning("请选择至少一个扫描指标");
      return;
    }

    scanData = {
      indicators: scanForm.value.indicators,
      parameters: indicatorParameters.value,
      scan_mode: scanMode.value.scanMode,
      scan_strategy: scanMode.value.scanStrategy,
      periods: scanMode.value.periods,
      adjust: "n",
      end_date: endDate.value,
    };
  } else {
    // 多周期模式验证
    const hasAnyIndicator = Object.values(periodIndicators.value).some(
      (indicators) => indicators.length > 0
    );
    if (!hasAnyIndicator) {
      ElMessage.warning("请为至少一个周期选择指标");
      return;
    }

    // 准备分周期指标和参数配置
    scanData = {
      indicators: [], // 多周期模式下传统indicators字段为空
      period_indicators: periodIndicators.value,
      period_parameters: periodIndicatorParameters.value, // 启用分周期参数配置
      scan_mode: scanMode.value.scanMode,
      scan_strategy: scanMode.value.scanStrategy,
      periods: scanMode.value.periods,
      adjust: "n",
      end_date: endDate.value,
    };
  }

  try {
    isStarting.value = true;
    await scannerStore.startScan(scanData);
    ElMessage.success("扫描任务已启动");
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message);
    }
  } finally {
    isStarting.value = false;
  }
};

const stopScan = async () => {
  try {
    isStopping.value = true;
    await scannerStore.stopScan();
    ElMessage.success("扫描任务已停止");
  } catch (error) {
    ElMessage.error(error.message || "停止失败");
  } finally {
    isStopping.value = false;
  }
};

// 参数配置确认处理
const handleParameterConfirm = (parameters) => {
  // 参数已在对话框组件中保存到localStorage
  // 这里可以添加额外的处理逻辑，比如更新界面显示
  console.log("参数配置已更新:", parameters);
};

// 生命周期
onMounted(async () => {
  // 初始化会话
  const hasActiveTask = await scannerStore.initSession();

  // 如果有活跃任务，尝试从任务配置中恢复参数
  if (hasActiveTask && scannerStore.currentTask) {
    try {
      // 获取任务详情以恢复配置
      const taskDetails = await scannerApi.getTask(scannerStore.currentTask.id);
      if (taskDetails && taskDetails.end_date) {
        endDate.value = taskDetails.end_date;
      }
    } catch (error) {
      console.warn("无法恢复任务配置:", error);
    }
  }
});

onUnmounted(() => {
  // 清理
  scannerStore.cleanup();
});
</script>

<style scoped lang="scss">
.scanner-page {
  // max-width: 1400px;
  margin: 0 auto;
}

.indicator-card {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background-color: var(--indicator-card-bg);
  border-radius: 0.5rem;
  transition: all 200ms;
  cursor: pointer;

  &:hover {
    background-color: var(--indicator-card-bg-active);
    transform: scale(1.01);
  }

  &--active {
    background-color: var(--indicator-card-bg-active) !important;
    transform: scale(1.01);
  }

  &--disabled {
    cursor: not-allowed;
    opacity: 0.5;

    &:hover {
      transform: none;
    }
  }
}

.hover\:bg-bg-primary:hover {
  background-color: var(--bg-primary);
}

.hover\:border-primary:hover {
  border-color: var(--primary);
}

.hover\:bg-primary-dark:hover {
  background-color: var(--primary-dark);
}
</style>
