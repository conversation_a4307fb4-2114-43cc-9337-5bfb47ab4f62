<template>
  <el-dialog
    v-model="visible"
    title="指标参数配置"
    width="600px"
    :before-close="handleClose"
    append-to-body
  >
    <div class="parameter-config">
      <!-- KDJ 参数配置 -->
      <div class="parameter-group">
        <div class="group-title">
          <Icon name="chart-line" class="mr-2 text-primary" />
          <span>KDJ 指标参数</span>
        </div>
        <div class="parameter-grid">
          <div class="parameter-item">
            <label>周期 (n)</label>
            <el-input-number
              v-model="localParameters.kdj.n"
              :min="5"
              :max="100"
              size="small"
              controls-position="right"
            />
          </div>
          <div class="parameter-item">
            <label>K值平滑 (m1)</label>
            <el-input-number
              v-model="localParameters.kdj.m1"
              :min="1"
              :max="20"
              size="small"
              controls-position="right"
            />
          </div>
          <div class="parameter-item">
            <label>D值平滑 (m2)</label>
            <el-input-number
              v-model="localParameters.kdj.m2"
              :min="1"
              :max="20"
              size="small"
              controls-position="right"
            />
          </div>
        </div>
      </div>

      <!-- Volume Pressure 参数配置 -->
      <div class="parameter-group">
        <div class="group-title">
          <Icon name="volume-file-storage" class="mr-2 text-primary" />
          <span>成交量压力参数</span>
        </div>
        <div class="parameter-grid">
          <div class="parameter-item">
            <label>EMA周期</label>
            <el-input-number
              v-model="localParameters.volume_pressure.ema_period"
              :min="3"
              :max="50"
              size="small"
              controls-position="right"
            />
          </div>
        </div>
      </div>

      <!-- Bollinger 参数配置 -->
      <div class="parameter-group">
        <div class="group-title">
          <Icon name="chart-area" class="mr-2 text-primary" />
          <span>布林带参数</span>
        </div>
        <div class="parameter-grid">
          <div class="parameter-item">
            <label>窗口大小</label>
            <el-input-number
              v-model="localParameters.bollinger.window"
              :min="5"
              :max="100"
              size="small"
              controls-position="right"
            />
          </div>
          <div class="parameter-item">
            <label>标准差倍数</label>
            <el-input-number
              v-model="localParameters.bollinger.std_dev"
              :min="0.5"
              :max="5"
              :step="0.1"
              :precision="1"
              size="small"
              controls-position="right"
            />
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="resetToDefaults" type="info" plain>
          重置默认
        </el-button>
        <div class="flex-1"></div>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import Icon from '@/components/common/Icon.vue'
import { useStorage } from '@/composables/useStorage'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'confirm'])

const { indicatorParameters, updateIndicatorParameters, DEFAULT_PARAMETERS } = useStorage()

// 对话框显示状态
const visible = ref(props.modelValue)

// 本地参数副本，避免直接修改原参数
const localParameters = ref({
  kdj: { ...indicatorParameters.value.kdj },
  volume_pressure: { ...indicatorParameters.value.volume_pressure },
  bollinger: { ...indicatorParameters.value.bollinger }
})

// 监听外部传入的显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    // 对话框打开时重置本地参数
    localParameters.value = {
      kdj: { ...indicatorParameters.value.kdj },
      volume_pressure: { ...indicatorParameters.value.volume_pressure },
      bollinger: { ...indicatorParameters.value.bollinger }
    }
  }
})

// 监听对话框关闭
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 重置为默认参数
const resetToDefaults = () => {
  localParameters.value = {
    kdj: { ...DEFAULT_PARAMETERS.kdj },
    volume_pressure: { ...DEFAULT_PARAMETERS.volume_pressure },
    bollinger: { ...DEFAULT_PARAMETERS.bollinger }
  }
  ElMessage.success('已重置为默认参数')
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 确认配置
const handleConfirm = () => {
  try {
    // 参数验证
    if (localParameters.value.kdj.n < 5 || localParameters.value.kdj.n > 100) {
      ElMessage.error('KDJ周期必须在5-100之间')
      return
    }
    if (localParameters.value.volume_pressure.ema_period < 3 || localParameters.value.volume_pressure.ema_period > 50) {
      ElMessage.error('EMA周期必须在3-50之间')
      return
    }
    if (localParameters.value.bollinger.window < 5 || localParameters.value.bollinger.window > 100) {
      ElMessage.error('布林带窗口必须在5-100之间')
      return
    }

    // 更新全局参数
    updateIndicatorParameters('kdj', localParameters.value.kdj)
    updateIndicatorParameters('volume_pressure', localParameters.value.volume_pressure)
    updateIndicatorParameters('bollinger', localParameters.value.bollinger)

    ElMessage.success('参数配置已保存')
    emit('confirm', localParameters.value)
    visible.value = false
  } catch (error) {
    ElMessage.error('保存参数失败')
    console.error('Parameter save error:', error)
  }
}
</script>

<style scoped lang="scss">
.parameter-config {
  .parameter-group {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    .group-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 16px;
      color: var(--text-primary);
    }

    .parameter-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
      gap: 16px;

      .parameter-item {
        display: flex;
        flex-direction: column;
        gap: 8px;

        label {
          font-size: 14px;
          font-weight: 500;
          color: var(--text-secondary);
        }

        :deep(.el-input-number) {
          width: 100%;
          
          .el-input__wrapper {
            border-radius: 6px;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  align-items: center;
  gap: 12px;

  .flex-1 {
    flex: 1;
  }
}
</style>