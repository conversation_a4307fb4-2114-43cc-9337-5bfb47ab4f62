<template>
  <el-dialog
    v-model="visible"
    title="指标参数配置"
    width="800px"
    :before-close="handleClose"
    append-to-body
  >
    <div class="parameter-config">
      <!-- 扫描模式显示 -->
      <div class="mode-indicator mb-4">
        <el-tag :type="scanMode === 'traditional' ? 'success' : 'primary'" size="large">
          {{ scanMode === 'traditional' ? '传统模式配置' : '多周期模式配置' }}
        </el-tag>
      </div>

      <!-- 传统模式配置 -->
      <div v-if="scanMode === 'traditional'">
        <ParameterSection 
          v-for="indicator in ['kdj', 'volume_pressure', 'bollinger', 'macd']" 
          :key="indicator"
          :indicator="indicator"
          :parameters="localParameters[indicator]"
          @update="updateIndicatorParameter"
        />
      </div>

      <!-- 多周期模式配置 -->
      <div v-else>
        <el-tabs v-model="activeTab" type="card" class="period-tabs">
          <el-tab-pane 
            v-for="period in availablePeriods" 
            :key="period.value"
            :label="period.label" 
            :name="period.value"
          >
            <template #label>
              <div class="flex items-center">
                <Icon :name="period.icon" class="mr-2" />
                {{ period.label }}参数
              </div>
            </template>
            
            <div class="period-content">
              <!-- 周期参数复制功能 -->
              <div class="copy-controls mb-4" :class="$themeStore?.isDark ? 'copy-controls--dark' : 'copy-controls--light'">
                <span class="text-sm mr-3" :class="$themeStore?.isDark ? 'text-gray-400' : 'text-gray-600'">快速配置：</span>
                <el-button 
                  v-for="otherPeriod in availablePeriods.filter(p => p.value !== period.value)"
                  :key="otherPeriod.value"
                  size="small" 
                  type="info" 
                  plain
                  @click="copyFromPeriod(otherPeriod.value, period.value)"
                >
                  从{{ otherPeriod.label }}复制
                </el-button>
                <el-button 
                  size="small" 
                  type="warning" 
                  plain
                  @click="resetPeriodToDefault(period.value)"
                >
                  重置为默认
                </el-button>
              </div>

              <!-- 该周期的指标参数配置 -->
              <ParameterSection 
                v-for="indicator in ['kdj', 'volume_pressure', 'bollinger', 'macd']" 
                :key="`${period.value}-${indicator}`"
                :indicator="indicator"
                :parameters="localPeriodParameters[period.value][indicator]"
                @update="updatePeriodIndicatorParameter(period.value, indicator, $event)"
              />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="resetToDefaults" type="info" plain>
          重置全部默认
        </el-button>
        <div class="flex-1"></div>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import Icon from '@/components/common/Icon.vue'
import ParameterSection from '@/components/scanner/ParameterSection.vue'
import { useStorage } from '@/composables/useStorage'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  scanMode: {
    type: String,
    default: 'traditional'
  },
  selectedPeriods: {
    type: Array,
    default: () => ['d']
  }
})

const emit = defineEmits(['update:modelValue', 'confirm'])

const { 
  indicatorParameters, 
  periodIndicatorParameters,
  updateIndicatorParameters,
  updatePeriodIndicatorParameters,
  copyParametersBetweenPeriods,
  DEFAULT_PARAMETERS,
  DEFAULT_PERIOD_PARAMETERS
} = useStorage()

// 对话框显示状态
const visible = ref(props.modelValue)

// 当前激活的标签页
const activeTab = ref('d')

// 可用周期选项
const availablePeriods = computed(() => {
  const allPeriods = [
    { value: 'd', label: '日线', icon: 'calendar-today' },
    { value: 'w', label: '周线', icon: 'calendar-week' },
    { value: 'm', label: '月线', icon: 'calendar-month' }
  ]
  return allPeriods.filter(period => props.selectedPeriods.includes(period.value))
})

// 本地参数副本（传统模式）
const localParameters = ref({
  kdj: { ...indicatorParameters.value.kdj },
  volume_pressure: { ...indicatorParameters.value.volume_pressure },
  bollinger: { ...indicatorParameters.value.bollinger },
  macd: { ...indicatorParameters.value.macd }
})


// 本地参数副本（多周期模式）
const localPeriodParameters = ref({
  d: {
    kdj: { ...periodIndicatorParameters.value.d.kdj },
    volume_pressure: { ...periodIndicatorParameters.value.d.volume_pressure },
    bollinger: { ...periodIndicatorParameters.value.d.bollinger },
    macd: { ...periodIndicatorParameters.value.d.macd }
  },
  w: {
    kdj: { ...periodIndicatorParameters.value.w.kdj },
    volume_pressure: { ...periodIndicatorParameters.value.w.volume_pressure },
    bollinger: { ...periodIndicatorParameters.value.w.bollinger },
    macd: { ...periodIndicatorParameters.value.w.macd }
  },
  m: {
    kdj: { ...periodIndicatorParameters.value.m.kdj },
    volume_pressure: { ...periodIndicatorParameters.value.m.volume_pressure },
    bollinger: { ...periodIndicatorParameters.value.m.bollinger },
    macd: { ...periodIndicatorParameters.value.m.macd }
  }
})

// 监听外部传入的显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    // 对话框打开时重置本地参数
    resetLocalParameters()
    // 设置默认激活的标签页为第一个可用周期
    if (availablePeriods.value.length > 0) {
      activeTab.value = availablePeriods.value[0].value
    }
  }
})

// 监听对话框关闭
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 重置本地参数
const resetLocalParameters = () => {
  // 传统模式参数
  localParameters.value = {
    kdj: { ...indicatorParameters.value.kdj },
    volume_pressure: { ...indicatorParameters.value.volume_pressure },
    bollinger: { ...indicatorParameters.value.bollinger },
    macd: { ...indicatorParameters.value.macd }
  }
  
  // 多周期模式参数
  localPeriodParameters.value = {
    d: {
      kdj: { ...periodIndicatorParameters.value.d.kdj },
      volume_pressure: { ...periodIndicatorParameters.value.d.volume_pressure },
      bollinger: { ...periodIndicatorParameters.value.d.bollinger },
      macd: { ...periodIndicatorParameters.value.d.macd }
    },
    w: {
      kdj: { ...periodIndicatorParameters.value.w.kdj },
      volume_pressure: { ...periodIndicatorParameters.value.w.volume_pressure },
      bollinger: { ...periodIndicatorParameters.value.w.bollinger },
      macd: { ...periodIndicatorParameters.value.w.macd }
    },
    m: {
      kdj: { ...periodIndicatorParameters.value.m.kdj },
      volume_pressure: { ...periodIndicatorParameters.value.m.volume_pressure },
      bollinger: { ...periodIndicatorParameters.value.m.bollinger },
      macd: { ...periodIndicatorParameters.value.m.macd }
    }
  }
}

// 更新指标参数（传统模式）
const updateIndicatorParameter = (indicator, params) => {
  localParameters.value[indicator] = { ...localParameters.value[indicator], ...params }
}

// 更新周期指标参数（多周期模式）
const updatePeriodIndicatorParameter = (period, indicator, params) => {
  localPeriodParameters.value[period][indicator] = { 
    ...localPeriodParameters.value[period][indicator], 
    ...params 
  }
}

// 从其他周期复制参数
const copyFromPeriod = (fromPeriod, toPeriod) => {
  localPeriodParameters.value[toPeriod] = JSON.parse(
    JSON.stringify(localPeriodParameters.value[fromPeriod])
  )
  ElMessage.success(`已从${getPeriodLabel(fromPeriod)}复制参数到${getPeriodLabel(toPeriod)}`)
}

// 重置指定周期为默认值
const resetPeriodToDefault = (period) => {
  localPeriodParameters.value[period] = JSON.parse(JSON.stringify(DEFAULT_PARAMETERS))
  ElMessage.success(`已重置${getPeriodLabel(period)}参数为默认值`)
}

// 获取周期标签
const getPeriodLabel = (period) => {
  const periodMap = { d: '日线', w: '周线', m: '月线' }
  return periodMap[period] || period
}

// 重置为默认参数
const resetToDefaults = () => {
  if (props.scanMode === 'traditional') {
    localParameters.value = JSON.parse(JSON.stringify(DEFAULT_PARAMETERS))
  } else {
    localPeriodParameters.value = JSON.parse(JSON.stringify(DEFAULT_PERIOD_PARAMETERS))
  }
  ElMessage.success('已重置为默认参数')
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 确认配置
const handleConfirm = () => {
  try {
    if (props.scanMode === 'traditional') {
      // 传统模式：更新全局参数
      updateIndicatorParameters('kdj', localParameters.value.kdj)
      updateIndicatorParameters('volume_pressure', localParameters.value.volume_pressure)
      updateIndicatorParameters('bollinger', localParameters.value.bollinger)
      updateIndicatorParameters('macd', localParameters.value.macd)
      
      ElMessage.success('传统模式参数配置已保存')
      emit('confirm', localParameters.value)
    } else {
      // 多周期模式：更新多周期参数
      for (const period of ['d', 'w', 'm']) {
        for (const indicator of ['kdj', 'volume_pressure', 'bollinger', 'macd']) {
          updatePeriodIndicatorParameters(period, indicator, localPeriodParameters.value[period][indicator])
        }
      }
      
      ElMessage.success('多周期参数配置已保存')
      emit('confirm', localPeriodParameters.value)
    }

    visible.value = false
  } catch (error) {
    ElMessage.error('保存参数失败')
    console.error('Parameter save error:', error)
  }
}
</script>

<style scoped lang="scss">
.parameter-config {
  .mode-indicator {
    text-align: center;
    
    :deep(.el-tag) {
      font-size: 14px;
      padding: 8px 16px;
    }
  }
  
  .period-tabs {
    :deep(.el-tabs__header) {
      margin-bottom: 20px;
    }
    
    :deep(.el-tabs__item) {
      padding: 12px 20px;
      font-weight: 500;
    }
  }
  
  .period-content {
    .copy-controls {
      padding: 12px;
      border-radius: 8px;
      border: 1px solid;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 8px;
      
      &--dark {
        background: rgba(255, 255, 255, 0.02);
        border-color: rgba(255, 255, 255, 0.05);
      }
      
      &--light {
        background: rgba(0, 0, 0, 0.02);
        border-color: rgba(0, 0, 0, 0.05);
      }
    }
  }
}

.dialog-footer {
  display: flex;
  align-items: center;
  gap: 12px;

  .flex-1 {
    flex: 1;
  }
}
</style>