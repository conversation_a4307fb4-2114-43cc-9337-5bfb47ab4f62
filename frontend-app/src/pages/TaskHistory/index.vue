<template>
  <div class="task-history-page card">
    <div class="page-header">
      <h1>任务历史</h1>
      <!-- <div class="header-actions">
        <el-button
          @click="refreshData"
          :loading="loading"
          icon="i-carbon-renew"
        >
          刷新
        </el-button>
      </div> -->
    </div>

    <!-- 过滤器 -->
    <div class="filters-section">
      <el-form inline @submit.prevent>
        <el-form-item label="触发类型">
          <el-select
            v-model="filters.triggerType"
            placeholder="全部"
            clearable
            @change="handleFilterChange"
            style="width: 150px;"
          >
            <el-option label="手动触发" value="manual" />
            <el-option label="定时触发" value="scheduled" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="执行状态">
          <el-select
            v-model="filters.status"
            placeholder="全部"
            clearable
            @change="handleFilterChange"
            style="width: 150px;"
          >
            <el-option label="待执行" value="pending" />
            <el-option label="执行中" value="running" />
            <el-option label="已完成" value="completed" />
            <el-option label="已失败" value="failed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        
        <!-- 管理员额外过滤条件 -->
        <el-form-item v-if="isAdmin" label="用户">
          <el-select
            v-model="filters.userId"
            placeholder="全部用户"
            clearable
            filterable
            :loading="userLoading"
            @change="handleFilterChange"
            style="width: 150px;"
          >
            <el-option
              v-for="user in userOptions"
              :key="user.id"
              :label="`${user.username} (${user.email})`"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <!-- 任务列表 -->
    <div class="task-list-section card">
      <CommonTable
        :data="taskExecutions"
        :loading="loading"
        :columns="tableColumns"
        :stripe="true"
        :show-pagination="true"
        :pagination="{
          total: pagination.total,
          pageSizes: [20, 50, 100],
          layout: 'total, sizes, prev, pager, next, jumper'
        }"
        :current-page="pagination.current"
        :page-size="pagination.pageSize"
        empty-text="暂无任务执行记录"
        @page-change="handlePageChange"
        class="task-history-table"
      >
        <!-- 用户信息插槽 -->
        <template #user_info="{ row }">
          <div v-if="row.user_username">
            <div class="font-medium">{{ row.user_username }}</div>
            <div class="text-xs text-gray-500">{{ row.user_email }}</div>
          </div>
          <div v-else class="text-gray-400">未知用户</div>
        </template>

        <!-- 任务名称插槽 -->
        <template #task_name="{ row }">
          <div v-if="row.scheduled_task_name" class="font-medium">
            {{ row.scheduled_task_name }}
          </div>
          <div v-else class="text-gray-500 italic">
            手动扫描
          </div>
        </template>

        <!-- 触发方式插槽 -->
        <template #trigger_type="{ row }">
          <el-tag 
            :type="row.trigger_type === 'scheduled' ? 'success' : 'info'" 
            size="small"
          >
            {{ row.trigger_type === 'scheduled' ? '定时' : '手动' }}
          </el-tag>
        </template>

        <!-- 执行状态插槽 -->
        <template #status="{ row }">
          <el-tag 
            :type="getStatusTagType(row.status)" 
            size="small"
          >
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>

        <!-- 执行时长插槽 -->
        <template #duration="{ row }">
          <span v-if="row.duration_seconds">
            {{ formatDuration(row.duration_seconds) }}
          </span>
          <span v-else-if="row.status === 'running'">
            <i class="i-carbon-time animate-spin" />
          </span>
          <span v-else>-</span>
        </template>

        <!-- 开始时间插槽 -->
        <template #start_time="{ row }">
          <span v-if="row.start_time">
            {{ formatDateTime(row.start_time) }}
          </span>
          <span v-else>-</span>
        </template>

        <!-- 操作列插槽 -->
        <template #actions="{ row }">
          <div class="table-actions">
            <el-tooltip content="查看详情" placement="top">
              <el-button
                text
                size="small"
                circle
                @click="viewDetails(row)"
                class="action-btn-view"
              >
                <Icon name="view" />
              </el-button>
            </el-tooltip>
            
            <el-tooltip content="取消执行" placement="top" v-if="canCancel(row)">
              <el-button
                text
                size="small"
                circle
                @click="handleCancelExecution(row.id)"
                class="action-btn-cancel"
              >
                <Icon name="stop" />
              </el-button>
            </el-tooltip>
            
            <el-tooltip content="删除记录" placement="top" v-if="canDelete(row)">
              <el-button
                text
                size="small"
                circle
                @click="handleDeleteExecution(row.id)"
                class="action-btn-delete"
              >
                <Icon name="delete" />
              </el-button>
            </el-tooltip>
          </div>
        </template>
      </CommonTable>
    </div>

    <!-- 详情弹窗 -->
    <TaskExecutionDetails
      v-model="showDetailsDialog"
      :execution="selectedExecution"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useTaskHistory } from './composables/useTaskHistory'
import { authApi } from '@/services/api/auth'
import TaskExecutionDetails from './components/TaskExecutionDetails.vue'
import CommonTable from '@/components/common/CommonTable.vue'
import Icon from '@/components/common/Icon.vue'

const {
  loading,
  taskExecutions,
  pagination,
  filters,
  isAdmin,
  loadTaskExecutions,
  cancelExecution,
  deleteExecution,
  refreshData,
  getStatusTagType,
  getStatusText,
  formatDuration,
  formatDateTime
} = useTaskHistory()

// 响应式数据
const userOptions = ref([])
const showDetailsDialog = ref(false)
const selectedExecution = ref(null)
const userLoading = ref(false)

// 轮询定时器
let pollingTimer = null

// 表格列配置
const tableColumns = computed(() => {
  const columns = [
    {
      prop: 'id',
      label: 'ID',
      'min-width': 80,
      fixed: 'left'
    }
  ]
  
  // 管理员才显示用户列
  if (isAdmin.value) {
    columns.push({
      prop: 'user_info',
      label: '用户',
      'min-width': 140,
      slot: 'user_info'
    })
  }
  
  columns.push(
    {
      prop: 'task_name',
      label: '任务名称',
      'min-width': 160,
      slot: 'task_name'
    },
    {
      prop: 'trigger_type',
      label: '触发方式',
      'min-width': 100,
      slot: 'trigger_type'
    },
    {
      prop: 'status',
      label: '执行状态',
      'min-width': 100,
      slot: 'status'
    },
    {
      prop: 'results_count',
      label: '结果数量',
      'min-width': 100
    },
    {
      prop: 'duration',
      label: '执行时长',
      'min-width': 100,
      slot: 'duration'
    },
    {
      prop: 'start_time',
      label: '开始时间',
      'min-width': 160,
      slot: 'start_time'
    }
  )
  
  return columns
})

// 方法
const loadUserOptions = async () => {
  if (!isAdmin.value) return
  
  userLoading.value = true
  try {
    const response = await authApi.getUserList()
    if (response && response.data) {
      userOptions.value = Array.isArray(response.data) ? response.data : []
    } else {
      userOptions.value = []
    }
  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
    userOptions.value = []
  } finally {
    userLoading.value = false
  }
}

const handleFilterChange = () => {
  pagination.current = 1
  loadTaskExecutions()
}

// CommonTable的分页处理方法
const handlePageChange = ({ page, size }) => {
  pagination.current = page
  pagination.pageSize = size
  loadTaskExecutions()
}

const viewDetails = (execution) => {
  selectedExecution.value = execution
  showDetailsDialog.value = true
}

const canCancel = (execution) => {
  return execution.status === 'pending' || execution.status === 'running'
}

const canDelete = (execution) => {
  return execution.status !== 'pending' && execution.status !== 'running'
}

const handleCancelExecution = async (executionId) => {
  try {
    await ElMessageBox.confirm('确认取消此任务执行？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await cancelExecution(executionId)
  } catch (error) {
    if (error !== 'cancel') {
      console.error(error)
    }
  }
}

const handleDeleteExecution = async (executionId) => {
  try {
    await ElMessageBox.confirm('确认删除此执行记录？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deleteExecution(executionId)
  } catch (error) {
    if (error !== 'cancel') {
      console.error(error)
    }
  }
}

const startPolling = () => {
  // 每10秒轮询一次，带错误处理
  pollingTimer = setInterval(async () => {
    try {
      await loadTaskExecutions()
    } catch (error) {
      console.warn('轮询更新失败:', error)
      // 轮询错误不显示用户通知，避免频繁弹窗
    }
  }, 10000)
}

const stopPolling = () => {
  if (pollingTimer) {
    clearInterval(pollingTimer)
    pollingTimer = null
  }
}

// 生命周期
onMounted(async () => {
  // 如果是管理员，先加载用户列表
  if (isAdmin.value) {
    await loadUserOptions()
  }
  
  // 然后加载任务执行记录
  await refreshData()
  startPolling()
})

onUnmounted(() => {
  stopPolling()
})
</script>

<style scoped>
.task-history-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.filters-section {
  /* padding: 16px; */
  border-radius: 8px;
}

.task-list-section {
  border-radius: 8px;
}

.task-history-table {
  width: 100%;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.table-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;

  .el-button {
    width: 32px;
    height: 32px;
    padding: 0;
    background: transparent !important;
    border: none !important;

    &.is-circle {
      border-radius: 50%;

      .icon {
        width: 16px;
        height: 16px;
      }
    }

    &.action-btn-view {
      color: var(--accent-primary);

      &:hover {
        color: var(--accent-primary-hover, #4a9eff);
        background: rgba(64, 158, 255, 0.1) !important;
      }
    }

    &.action-btn-cancel {
      color: #e6a23c;

      &:hover {
        color: #ebb563;
        background: rgba(230, 162, 60, 0.1) !important;
      }
    }

    &.action-btn-delete {
      color: #f56c6c;

      &:hover {
        color: #f78989;
        background: rgba(245, 108, 108, 0.1) !important;
      }
    }
  }
}
</style>