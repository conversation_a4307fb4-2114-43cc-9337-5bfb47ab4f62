<template>
  <el-dialog
    v-model="dialogVisible"
    title="任务执行详情"
    width="80%"
    :close-on-click-modal="false"
    append-to-body
    style="--el-dialog-margin-top:5vh;"
  >
    <div v-if="execution" class="execution-details">
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3>基本信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="执行ID">
            {{ execution.id }}
          </el-descriptions-item>

          <el-descriptions-item label="任务名称">
            <span v-if="execution.scheduled_task_name">
              {{ execution.scheduled_task_name }}
            </span>
            <span v-else class="text-gray-500">手动扫描</span>
          </el-descriptions-item>

          <el-descriptions-item label="触发方式">
            <el-tag
              :type="
                execution.trigger_type === 'scheduled' ? 'success' : 'info'
              "
              size="small"
            >
              {{
                execution.trigger_type === "scheduled" ? "定时触发" : "手动触发"
              }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="执行状态">
            <el-tag :type="getStatusTagType(execution.status)" size="small">
              {{ getStatusText(execution.status) }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="开始时间">
            {{ formatDateTime(execution.start_time) }}
          </el-descriptions-item>

          <el-descriptions-item label="结束时间">
            {{ formatDateTime(execution.end_time) }}
          </el-descriptions-item>

          <el-descriptions-item label="执行时长">
            {{ formatDuration(execution.duration_seconds) }}
          </el-descriptions-item>

          <el-descriptions-item label="结果数量">
            <el-tag type="info" size="small">
              {{ execution.results_count }} 条
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item
            v-if="isAdmin && execution.user_username"
            label="执行用户"
          >
            <div>
              <div class="font-medium">{{ execution.user_username }}</div>
              <div class="text-xs text-gray-500">
                {{ execution.user_email }}
              </div>
            </div>
          </el-descriptions-item>

          <el-descriptions-item label="创建时间">
            {{ formatDateTime(execution.created_at) }}
          </el-descriptions-item>
          <!-- 任务配置 -->
          <el-descriptions-item
            v-if="execution.task_config"
            label="任务配置"
            :span="2"
          >
            <div class="config-display">
              <div class="config-item">
                <span class="config-label">扫描指标：</span>
                <el-tag
                  v-for="indicator in getTaskConfig().indicators || []"
                  :key="indicator"
                  class="mr-1 mb-1"
                  size="small"
                >
                  {{ indicator }}
                </el-tag>
              </div>

              <div v-if="getTaskConfig().stock_codes" class="config-item">
                <span class="config-label">股票代码：</span>
                <div class="stock-codes">
                  <el-tag
                    v-for="code in getTaskConfig().stock_codes.slice(0, 10)"
                    :key="code"
                    class="mr-1 mb-1"
                    size="small"
                    type="success"
                  >
                    {{ code }}
                  </el-tag>
                  <span v-if="getTaskConfig().stock_codes.length > 10">
                    ... 等 {{ getTaskConfig().stock_codes.length }} 只股票
                  </span>
                </div>
              </div>

              <div class="config-item">
                <span class="config-label">扫描周期：</span>
                <el-tag
                  v-for="period in getTaskConfig().periods || ['d']"
                  :key="period"
                  class="mr-1"
                  size="small"
                  type="warning"
                >
                  {{ getPeriodText(period) }}
                </el-tag>
              </div>

              <div class="config-item">
                <span class="config-label">复权方式：</span>
                {{ getAdjustText(getTaskConfig().adjust) }}
              </div>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 错误信息 -->
      <div v-if="execution.error_message" class="detail-section">
        <h3>错误信息</h3>
        <el-alert
          :title="execution.error_message"
          type="error"
          :closable="false"
          show-icon
        />
      </div>

      <!-- 执行结果 -->
      <div
        v-if="execution.results_data && execution.results_data.length > 0"
        class="detail-section"
      >
        <h3>
          执行结果
          <el-tag size="small" type="info"
            >{{ execution.results_count }} 条</el-tag
          >
        </h3>

        <!-- <div class="results-toolbar">
          <el-button
            size="small"
            @click="exportResults"
            icon="i-carbon-download"
          >
            导出结果
          </el-button>
        </div> -->

        <!-- 结果数据表格 -->
        <CommonTable
          :data="paginatedResultsData"
          :columns="resultColumns"
          :pagination="resultsPagination"
          @page-change="handleResultsPaginationChange"
        >
          <template #signals="{ row }">
            <template v-if="row.signals && Array.isArray(row.signals)">
              <el-tag
                v-for="signal in row.signals"
                :key="signal"
                :type="getSignalTagType(signal)"
                size="small"
                class="mr-1"
              >
                {{ signal }}
              </el-tag>
            </template>
          </template>
        </CommonTable>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button v-if="canCancel" type="danger" @click="handleCancel">
          取消执行
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { ElMessage, ElMessageBox, ElTag } from "element-plus";
import { useAuthStore } from "@/store";
import { scheduledTasksApi } from "@/services/api/scheduledTasks";
import CommonTable from "@/components/common/CommonTable.vue";

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  execution: {
    type: Object,
    default: null,
  },
});

const emit = defineEmits(["update:modelValue", "cancel-execution"]);

const authStore = useAuthStore();

// 响应式数据
const dialogVisible = ref(props.modelValue);

// 分页数据
const resultsPagination = ref({
  currentPage: 1,
  pageSize: 20,
  total: 0,
});

// 计算属性
const isAdmin = computed(() => authStore.isAdmin);
const canCancel = computed(() => {
  return (
    props.execution &&
    (props.execution.status === "pending" ||
      props.execution.status === "running")
  );
});

// 分页计算属性
const paginatedResultsData = computed(() => {
  if (!props.execution?.results_data) {
    resultsPagination.value.total = 0;
    return [];
  }

  const data = Array.isArray(props.execution.results_data) 
    ? props.execution.results_data 
    : [];
  
  resultsPagination.value.total = data.length;
  
  const start = (resultsPagination.value.currentPage - 1) * resultsPagination.value.pageSize;
  const end = start + resultsPagination.value.pageSize;
  const result = data.slice(start, end);
  console.log(`Paginating results: ${start} to ${end}, total: ${data.length}, result:`, result);

  return result;
});

// 结果表格列定义
const resultColumns = [
  {
    prop: "stock_code",
    label: "股票代码",
    'min-width': 100,
    fixed: "left",
  },
  {
    prop: "stock_name",
    label: "股票名称",
    minWidth: 120,
  },
  {
    prop: "price",
    label: "股价",
    'min-width': 80,
    formatter: (row, column, cellValue, index) => {
      return typeof cellValue === "number" ? cellValue.toFixed(2) : cellValue;
    },
  },
  {
    prop: "signals",
    label: "信号",
    minWidth: 100,
    slot: "signals"
  },
  {
    prop: "scan_time",
    label: "扫描时间",
    'min-width': 180,
    formatter: (row, column, cellValue, index) => {
      if (!cellValue) return "";
      // 格式化时间显示
      return cellValue.replace('T', ' ').substring(0, 19);
    },
  },
  {
    prop: "period",
    label: "周期",
    'min-width': 80,
    formatter: (row, column, cellValue, index) => {
      const periodMap = {
        d: "日线",
        w: "周线",
        m: "月线",
      };
      return periodMap[cellValue] || cellValue;
    },
  },
];

// 监听对话框状态
watch(
  () => props.modelValue,
  (val) => {
    dialogVisible.value = val;
  }
);

watch(dialogVisible, (val) => {
  emit("update:modelValue", val);
});

// 重置分页当execution改变时
watch(() => props.execution, () => {
  resultsPagination.value.currentPage = 1;
});

// 方法
const getTaskConfig = () => {
  if (!props.execution?.task_config_raw) return {};
  return props.execution.task_config_raw;
};

const getStatusTagType = (status) => {
  const typeMap = {
    pending: "info",
    running: "warning",
    completed: "success",
    failed: "danger",
    cancelled: "info",
  };
  return typeMap[status] || "info";
};

const getStatusText = (status) => {
  const textMap = {
    pending: "待执行",
    running: "执行中",
    completed: "已完成",
    failed: "已失败",
    cancelled: "已取消",
  };
  return textMap[status] || status;
};

const getSignalTagType = (signal) => {
  const typeMap = {
    buy: "success",
    sell: "danger", 
    hold: "info",
  };
  return typeMap[signal] || "info";
};

const getPeriodText = (period) => {
  const periodMap = {
    d: "日线",
    w: "周线",
    m: "月线",
  };
  return periodMap[period] || period;
};

const getAdjustText = (adjust) => {
  const adjustMap = {
    n: "不复权",
    qfq: "前复权",
    hfq: "后复权",
  };
  return adjustMap[adjust] || adjust;
};

const formatDateTime = (dateTime) => {
  if (!dateTime) return "-";
  const date = new Date(dateTime);
  return date.toLocaleString("zh-CN");
};

const formatDuration = (seconds) => {
  if (!seconds) return "-";
  if (seconds < 60) return `${seconds}秒`;
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  if (minutes < 60) return `${minutes}分${remainingSeconds}秒`;
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  return `${hours}时${remainingMinutes}分${remainingSeconds}秒`;
};

const exportResults = () => {
  if (!props.execution?.results_data) return;

  try {
    const data = props.execution.results_data;
    const csvContent = [
      // CSV 头部
      ["股票代码", "股票名称", "股价", "信号", "扫描时间", "周期"].join(","),
      // 数据行
      ...data.map((row) =>
        [
          row.stock_code || "",
          row.stock_name || "",
          row.price || "",
          Array.isArray(row.signals) ? row.signals.join(";") : "",
          row.scan_time || "",
          row.period || "",
        ].join(",")
      ),
    ].join("\n");

    const blob = new Blob(["\uFEFF" + csvContent], {
      type: "text/csv;charset=utf-8",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `任务执行结果_${
      props.execution.id
    }_${new Date().getTime()}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    ElMessage.success("结果导出成功");
  } catch (error) {
    console.error("导出失败:", error);
    ElMessage.error("结果导出失败");
  }
};

// 分页处理方法
const handleResultsPaginationChange = ({ page, size }) => {
  resultsPagination.value.currentPage = page;
  resultsPagination.value.pageSize = size;
};

const handleCancel = async () => {
  if (!props.execution) return;

  try {
    await ElMessageBox.confirm("确认取消此任务执行？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    await scheduledTasksApi.cancelExecution(props.execution.id);
    ElMessage.success("任务取消成功");
    emit("cancel-execution", props.execution.id);
    dialogVisible.value = false;
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("任务取消失败");
      console.error(error);
    }
  }
};
</script>

<style scoped>
.execution-details {
  max-height: 90vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  border-bottom: 2px solid var(--el-border-color-light);
  padding-bottom: 8px;
}

.config-display .el-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

.stock-codes {
  line-height: 2;
}

.results-toolbar {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
