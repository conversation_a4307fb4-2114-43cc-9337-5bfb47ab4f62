import { ref, reactive, computed } from "vue";
import { ElMessage } from "element-plus";
import { useAuthStore } from "@/store";
import { scheduledTasksApi } from "@/services/api/scheduledTasks";

export function useTaskHistory() {
  const authStore = useAuthStore();

  // 响应式数据
  const loading = ref(false);
  const taskExecutions = ref([]);
  const pagination = reactive({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  // 过滤条件
  const filters = reactive({
    triggerType: null,
    status: null,
    userId: null,
  });

  // 计算属性
  const isAdmin = computed(() => authStore.isAdmin);

  // 加载任务执行记录
  const loadTaskExecutions = async () => {
    loading.value = true;
    try {
      const params = {
        skip: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize,
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value != null)
        ),
      };

      const response = isAdmin.value
        ? await scheduledTasksApi.getAllExecutionsAdmin(params)
        : await scheduledTasksApi.getAllExecutions(params);
      console.log(response);

      // 改进的响应结构处理 - 使用标准 CommonResponse 格式
      if (response && response.data) {
        taskExecutions.value = Array.isArray(response.data.items) ? response.data.items : [];
        pagination.total = response.data.total || 0;
      } else {
        console.warn("API响应格式异常:", response);
        taskExecutions.value = [];
        pagination.total = 0;
      }
    } catch (error) {
      console.error("加载任务执行记录失败:", error);

      // 改进的错误处理
      let errorMessage = "加载任务执行记录失败";
      if (error.response) {
        // 服务器响应错误
        if (error.response.status === 401) {
          errorMessage = "未授权，请重新登录";
        } else if (error.response.status === 403) {
          errorMessage = "权限不足";
        } else if (error.response.status >= 500) {
          errorMessage = "服务器内部错误，请稍后重试";
        } else if (error.response.data && error.response.data.detail) {
          errorMessage = error.response.data.detail;
        }
      } else if (error.request) {
        // 网络错误
        errorMessage = "网络连接失败，请检查网络状态";
      } else if (error.message) {
        // 其他错误
        errorMessage = error.message;
      }

      ElMessage.error(errorMessage);
      taskExecutions.value = [];
      pagination.total = 0;
    } finally {
      loading.value = false;
    }
  };

  // 取消任务执行
  const cancelExecution = async (executionId) => {
    try {
      await scheduledTasksApi.cancelExecution(executionId);
      ElMessage.success("任务取消成功");
      await loadTaskExecutions();
    } catch (error) {
      console.error("取消任务执行失败:", error);

      // 改进的错误处理
      let errorMessage = "任务取消失败";
      if (error.response && error.response.data && error.response.data.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.message) {
        errorMessage = error.message;
      }

      ElMessage.error(errorMessage);
      throw error;
    }
  };

  // 删除执行记录
  const deleteExecution = async (executionId) => {
    try {
      await scheduledTasksApi.deleteExecution(executionId);
      ElMessage.success("执行记录删除成功");
      await loadTaskExecutions();
    } catch (error) {
      console.error("删除执行记录失败:", error);

      // 改进的错误处理
      let errorMessage = "执行记录删除失败";
      if (error.response && error.response.data && error.response.data.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.message) {
        errorMessage = error.message;
      }

      ElMessage.error(errorMessage);
      throw error;
    }
  };

  // 刷新数据
  const refreshData = async () => {
    await loadTaskExecutions();
  };

  // 重置过滤器
  const resetFilters = () => {
    filters.triggerType = null;
    filters.status = null;
    filters.userId = null;
    pagination.current = 1;
    loadTaskExecutions();
  };

  // 工具函数
  const getStatusTagType = (status) => {
    const typeMap = {
      pending: "info",
      running: "warning",
      completed: "success",
      failed: "danger",
      cancelled: "info",
    };
    return typeMap[status] || "info";
  };

  const getStatusText = (status) => {
    const textMap = {
      pending: "待执行",
      running: "执行中",
      completed: "已完成",
      failed: "已失败",
      cancelled: "已取消",
    };
    return textMap[status] || status;
  };

  const formatDuration = (seconds) => {
    if (!seconds) return "-";
    if (seconds < 60) return `${seconds}秒`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    if (minutes < 60) return `${minutes}分${remainingSeconds}秒`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}时${remainingMinutes}分${remainingSeconds}秒`;
  };

  const formatDateTime = (dateTime) => {
    if (!dateTime) return "-";
    const date = new Date(dateTime);
    return date.toLocaleString("zh-CN");
  };

  return {
    // 数据
    loading,
    taskExecutions,
    pagination,
    filters,

    // 计算属性
    isAdmin,

    // 方法
    loadTaskExecutions,
    cancelExecution,
    deleteExecution,
    refreshData,
    resetFilters,

    // 工具函数
    getStatusTagType,
    getStatusText,
    formatDuration,
    formatDateTime,
  };
}
