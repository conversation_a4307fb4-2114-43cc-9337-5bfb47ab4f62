import { ref, reactive } from 'vue'
import { scheduledTasksApi } from '@/services/api/scheduledTasks'
import { ElMessage, ElMessageBox } from 'element-plus'

export function useScheduledTasks() {
  const tasks = ref([])
  const loading = ref(false)
  const pagination = reactive({
    current: 1,
    pageSize: 20,
    total: 0
  })

  const loadTasks = async (params = {}) => {
    loading.value = true
    try {
      const response = await scheduledTasksApi.getTasks({
        skip: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize,
        ...params
      })
      tasks.value = response.data || response
      // 假设API返回total字段
      if (response.total !== undefined) {
        pagination.total = response.total
      }
    } catch (error) {
      ElMessage.error('加载任务列表失败')
      console.error(error)
    } finally {
      loading.value = false
    }
  }

  const createTask = async (taskData) => {
    try {
      await scheduledTasksApi.createTask(taskData)
      ElMessage.success('任务创建成功')
    } catch (error) {
      ElMessage.error('任务创建失败')
      throw error
    }
  }

  const updateTask = async (taskId, taskData) => {
    try {
      await scheduledTasksApi.updateTask(taskId, taskData)
      ElMessage.success('任务更新成功')
    } catch (error) {
      ElMessage.error('任务更新失败')
      throw error
    }
  }

  const deleteTask = async (taskId) => {
    try {
      await scheduledTasksApi.deleteTask(taskId)
      ElMessage.success('任务删除成功')
      loadTasks()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('任务删除失败')
        throw error
      }
    }
  }

  const executeTask = async (taskId) => {
    try {
      const result = await scheduledTasksApi.executeTask(taskId)
      return result
    } catch (error) {
      ElMessage.error('任务执行失败')
      throw error
    }
  }

  const toggleTaskStatus = async (taskId, isActive) => {
    try {
      await scheduledTasksApi.updateTask(taskId, { is_active: isActive })
      ElMessage.success(isActive ? '任务已启用' : '任务已禁用')
      await loadTasks()
    } catch (error) {
      ElMessage.error('状态更新失败')
      throw error
    }
  }

  return {
    tasks,
    loading,
    pagination,
    loadTasks,
    createTask,
    updateTask,
    deleteTask,
    executeTask,
    toggleTaskStatus
  }
}