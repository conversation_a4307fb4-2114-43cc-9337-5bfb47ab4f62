import { ref, watch } from 'vue'

export function useCronBuilder() {
  const cronExpression = ref('')
  const cronError = ref('')
  const nextRuns = ref([])
  
  const cronData = ref({
    minute: '*',
    hour: '*', 
    dayOfMonth: '*',
    month: '*',
    dayOfWeek: '*'
  })

  // 预定义的常用cron表达式
  const commonCrons = ref([
    { label: '每分钟', value: '* * * * *' },
    { label: '每小时', value: '0 * * * *' },
    { label: '每天上午9点', value: '0 9 * * *' },
    { label: '每天下午6点', value: '0 18 * * *' },
    { label: '每周一上午9点', value: '0 9 * * MON' },
    { label: '每月1号上午9点', value: '0 9 1 * *' },
    { label: '工作日上午9点', value: '0 9 * * MON-FRI' },
    { label: '每5分钟', value: '*/5 * * * *' },
    { label: '每30分钟', value: '*/30 * * * *' }
  ])

  const parseCron = (expression) => {
    cronError.value = ''
    nextRuns.value = []
    
    if (!expression) return

    try {
      // 这里应该使用真实的cron解析库，比如cron-parser
      // 简化实现，只做基本验证
      const parts = expression.split(' ')
      if (parts.length !== 5) {
        throw new Error('Cron表达式必须包含5个部分')
      }
      
      // 模拟生成下次执行时间预览
      const now = new Date()
      const preview = []
      for (let i = 0; i < 5; i++) {
        const nextTime = new Date(now.getTime() + (i + 1) * 60 * 1000) // 简化实现
        preview.push(nextTime.toLocaleString('zh-CN'))
      }
      nextRuns.value = preview
      
    } catch (error) {
      cronError.value = error.message
    }
  }

  const buildCron = () => {
    const { minute, hour, dayOfMonth, month, dayOfWeek } = cronData.value
    cronExpression.value = `${minute} ${hour} ${dayOfMonth} ${month} ${dayOfWeek}`
    parseCron(cronExpression.value)
  }

  const setCommonCron = (cronValue) => {
    cronExpression.value = cronValue
    parseCron(cronValue)
    
    // 尝试解析到cronData中
    try {
      const parts = cronValue.split(' ')
      if (parts.length === 5) {
        cronData.value = {
          minute: parts[0],
          hour: parts[1],
          dayOfMonth: parts[2], 
          month: parts[3],
          dayOfWeek: parts[4]
        }
      }
    } catch (error) {
      console.error('解析cron表达式失败:', error)
    }
  }

  // 监听cronExpression变化
  watch(() => cronExpression.value, (newValue) => {
    parseCron(newValue)
  })

  // 监听cronData变化
  watch(() => cronData.value, () => {
    buildCron()
  }, { deep: true })

  return {
    cronExpression,
    cronError,
    nextRuns,
    cronData,
    commonCrons,
    parseCron,
    buildCron,
    setCommonCron
  }
}