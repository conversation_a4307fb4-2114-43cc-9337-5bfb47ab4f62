import { ref, reactive } from 'vue'
import { scheduledTasksApi } from '@/services/api/scheduledTasks'
import { ElMessage } from 'element-plus'

export function useTaskExecutions() {
  const executions = ref([])
  const loading = ref(false)
  const pagination = reactive({
    current: 1,
    pageSize: 20,
    total: 0
  })

  const loadExecutions = async (taskId = null, params = {}) => {
    loading.value = true
    try {
      const queryParams = {
        skip: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize,
        ...params
      }

      let response
      if (taskId) {
        response = await scheduledTasksApi.getExecutions(taskId, queryParams)
      } else {
        response = await scheduledTasksApi.getAllExecutions(queryParams)
      }

      executions.value = response.data?.items || response.items || response
      pagination.total = response.data?.total || response.total || 0
    } catch (error) {
      ElMessage.error('加载执行记录失败')
      console.error(error)
    } finally {
      loading.value = false
    }
  }

  const getExecutionDetail = async (executionId) => {
    try {
      const response = await scheduledTasksApi.getExecutionDetail(executionId)
      return response.data || response
    } catch (error) {
      ElMessage.error('获取执行详情失败')
      throw error
    }
  }

  const deleteExecution = async (executionId) => {
    try {
      await scheduledTasksApi.deleteExecution(executionId)
      ElMessage.success('执行记录删除成功')
      await loadExecutions()
    } catch (error) {
      ElMessage.error('删除执行记录失败')
      throw error
    }
  }

  const cancelExecution = async (executionId) => {
    try {
      await scheduledTasksApi.cancelExecution(executionId)
      ElMessage.success('任务已取消')
      await loadExecutions()
    } catch (error) {
      ElMessage.error('取消任务失败')
      throw error
    }
  }

  const getStatusText = (status) => {
    const statusMap = {
      pending: '待执行',
      running: '执行中',
      completed: '已完成',
      failed: '失败',
      cancelled: '已取消'
    }
    return statusMap[status] || status
  }

  const getStatusType = (status) => {
    const typeMap = {
      pending: 'warning',
      running: 'primary',
      completed: 'success',
      failed: 'danger',
      cancelled: 'info'
    }
    return typeMap[status] || 'info'
  }

  const getTriggerTypeText = (triggerType) => {
    const typeMap = {
      scheduled: '定时触发',
      manual: '手动触发'
    }
    return typeMap[triggerType] || triggerType
  }

  return {
    executions,
    loading,
    pagination,
    loadExecutions,
    getExecutionDetail,
    deleteExecution,
    cancelExecution,
    getStatusText,
    getStatusType,
    getTriggerTypeText
  }
}