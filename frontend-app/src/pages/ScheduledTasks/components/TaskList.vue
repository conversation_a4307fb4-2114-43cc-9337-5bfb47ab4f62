<template>
  <div class="task-list">
    <div class="task-list-container">
      <div class="task-list-header">
        <div class="header-content">
          <h3 class="list-title">任务列表</h3>
          <div class="header-actions">
            <IconButton
              icon="refresh"
              variant="ghost"
              size="sm"
              @click="$emit('refresh')"
            >
              刷新
            </IconButton>
          </div>
        </div>
      </div>

      <div class="task-list-content">
        <CommonTable
          :data="tasks"
          :loading="loading"
          :columns="tableColumns"
          :show-pagination="false"
          :stripe="true"
          :actionsMinWidth="180"
          empty-text="暂无任务数据"
          class="task-table"
        >
          <template #name="{ row }">
            <div class="task-name-cell">
              <div class="task-name">{{ row.name }}</div>
              <div class="task-description">
                {{ row.description || "无描述" }}
              </div>
            </div>
          </template>

          <template #task_type="{ row }">
            <el-tag size="small" type="info" class="task-type-tag">
              {{ getTaskTypeText(row.task_type) }}
            </el-tag>
          </template>

          <template #cron_expression="{ row }">
            <div class="cron-time-cell">
              <div class="cron-expression">{{ row.cron_expression }}</div>
              <div class="cron-description">
                {{ getCronDescription(row.cron_expression) }}
              </div>
            </div>
          </template>

          <template #task_config="{ row }">
            <div class="task-config-cell">
              <div class="config-summary">
                {{ getTaskConfigSummary(row.task_config) }}
              </div>
            </div>
          </template>

          <template #is_active="{ row }">
            <el-switch
              v-model="row.is_active"
              @change="handleToggleStatus(row)"
              active-text="启用"
              inactive-text="禁用"
              size="small"
              class="task-status-switch"
            />
          </template>

          <template #current_executions="{ row }">
            <div class="execution-count-cell">
              <div class="execution-count">{{ row.current_executions }}</div>
              <div class="execution-limit">
                {{
                  row.max_executions ? `/ ${row.max_executions}` : "/ 无限制"
                }}
              </div>
            </div>
          </template>

          <template #next_execution="{ row }">
            <div class="next-execution-cell">
              {{
                row.next_execution
                  ? formatDateTime(row.next_execution)
                  : "已停用"
              }}
            </div>
          </template>

          <template #last_execution="{ row }">
            <div class="last-execution-cell">
              {{
                row.last_execution
                  ? formatDateTime(row.last_execution)
                  : "未执行"
              }}
            </div>
          </template>

          <template #actions="{ row }">
            <div class="table-actions">
              <el-tooltip content="立即执行" placement="top">
                <el-button
                  text
                  size="small"
                  circle
                  @click="$emit('execute', row.id)"
                  class="action-btn-execute"
                >
                  <Icon name="play" />
                </el-button>
              </el-tooltip>
              <el-tooltip content="查看记录" placement="top">
                <el-button
                  text
                  size="small"
                  circle
                  @click="$emit('view-executions', row.id)"
                  class="action-btn-view"
                >
                  <Icon name="list" />
                </el-button>
              </el-tooltip>
              <el-tooltip content="编辑任务" placement="top">
                <el-button
                  text
                  size="small"
                  circle
                  @click="$emit('edit', row)"
                  class="action-btn-edit"
                >
                  <Icon name="edit" />
                </el-button>
              </el-tooltip>
              <el-tooltip 
                content="删除任务" 
                placement="top"
                :disabled="false"
              >
                <el-popconfirm
                  title="确认删除此任务吗？"
                  confirm-button-text="确认"
                  cancel-button-text="取消"
                  width="220"
                  placement="bottom"
                  @confirm="$emit('delete', row.id)"
                >
                  <template #reference>
                    <el-button
                      text
                      size="small"
                      circle
                      class="action-btn-delete"
                    >
                      <Icon name="delete" />
                    </el-button>
                  </template>
                </el-popconfirm>
              </el-tooltip>
            </div>
          </template>
        </CommonTable>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { scheduledTasksApi } from "@/services/api/scheduledTasks";
import CommonTable from "@/components/common/CommonTable.vue";
import Icon from "@/components/common/Icon.vue";
import IconButton from "@/components/common/IconButton.vue";

defineProps({
  tasks: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits([
  "edit",
  "delete",
  "execute",
  "toggle-status",
  "view-executions",
  "refresh",
]);

// 表格列配置
const tableColumns = [
  {
    prop: "name",
    label: "任务名称",
    minWidth: 120,
    slot: "name",
  },
  {
    prop: "task_type",
    label: "任务类型",
    minWidth: 100,
    slot: "task_type",
  },
  {
    prop: "cron_expression",
    label: "执行时间",
    minWidth: 120,
    slot: "cron_expression",
  },
  {
    prop: "task_config",
    label: "任务配置",
    minWidth: 180,
    slot: "task_config",
  },
  {
    prop: "is_active",
    label: "状态",
    minWidth: 120,
    slot: "is_active",
  },
  {
    prop: "current_executions",
    label: "执行次数",
    minWidth: 120,
    slot: "current_executions",
  },
  {
    prop: "next_execution",
    label: "下次执行",
    minWidth: 140,
    slot: "next_execution",
  },
  {
    prop: "last_execution",
    label: "最后执行",
    minWidth: 140,
    slot: "last_execution",
  },
];

const getTaskTypeText = (taskType) => {
  const typeMap = {
    indicator_scan: "指标扫描",
    ai_analysis: "AI分析",
  };
  return typeMap[taskType] || taskType;
};

const getCronDescription = (cronExpression) => {
  // 简化的cron描述，实际项目中可以使用专门的库
  const commonCrons = {
    "* * * * *": "每分钟",
    "0 * * * *": "每小时",
    "0 9 * * *": "每天9点",
    "0 18 * * *": "每天18点",
    "0 9 * * MON": "每周一9点",
    "0 9 1 * *": "每月1号9点",
    "0 9 * * MON-FRI": "工作日9点",
    "*/5 * * * *": "每5分钟",
    "*/30 * * * *": "每30分钟",
  };

  return commonCrons[cronExpression] || "自定义时间";
};

const getTaskConfigSummary = (taskConfig) => {
  if (!taskConfig || typeof taskConfig !== 'object') return '-';
  
  try {
    // 指标扫描任务配置摘要
    if (taskConfig.indicators && Array.isArray(taskConfig.indicators)) {
      const indicators = taskConfig.indicators;
      const periods = taskConfig.periods || ['d'];
      const stockCodes = taskConfig.stock_codes;
      
      // 指标名称映射
      const indicatorNames = {
        'volume_pressure': '成交量压力',
        'kdj': 'KDJ指标',
        'bollinger': '布林带',
        'macd': 'MACD',
        'rsi': 'RSI指标',
        'arbr': 'ARBR指标'
      };
      
      const periodNames = {
        'd': '日线',
        'w': '周线', 
        'm': '月线'
      };
      
      const indicatorText = indicators.map(ind => indicatorNames[ind] || ind).join('、');
      const periodText = periods.map(p => periodNames[p] || p).join('、');
      const scopeText = stockCodes && stockCodes.length > 0 
        ? `指定${stockCodes.length}只股票` 
        : '全市场';
      
      return `${indicatorText} | ${periodText} | ${scopeText}`;
    }
    
    return '自定义配置';
  } catch (error) {
    return '配置解析失败';
  }
};

const formatDateTime = (dateTime) => {
  if (!dateTime) return "";
  const date = new Date(dateTime);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

const handleToggleStatus = async (task) => {
  try {
    // 立即更新UI状态，提供更好的用户体验
    const originalStatus = task.is_active;

    // 调用API
    await scheduledTasksApi.toggleTaskStatus(task.id, task.is_active);

    ElMessage.success(`任务已${task.is_active ? "启用" : "禁用"}`);

    // 刷新任务列表以获取最新的next_execution时间
    emit("refresh");
  } catch (error) {
    // 如果失败，恢复原状态
    task.is_active = !task.is_active;
    ElMessage.error(error.response?.data?.detail || "操作失败");
  }
};
</script>

<style scoped lang="scss">
.task-list {
  .task-list-container {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
  }

  .task-list-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);

    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .list-title {
        color: var(--text-primary);
        font-size: 18px;
        font-weight: 600;
        margin: 0;
      }

      .header-actions {
        display: flex;
        gap: 12px;
      }
    }
  }

  .task-list-content {
    padding: 0;

    .task-table {
      width: 100%;

      :deep(.common-table) {
        .el-table__cell {
          .cell {
            padding: 0 12px;
          }
        }
      }
    }
  }
}

// 单元格样式
.task-name-cell {
  .task-name {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 2px;
  }

  .task-description {
    color: var(--text-muted);
    font-size: 12px;
    line-height: 1.4;
  }
}

.task-type-tag {
  :deep(.el-tag) {
    background: var(--bg-quaternary);
    color: var(--accent-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
  }
}

.cron-time-cell {
  .cron-expression {
    font-family: var(--font-mono, monospace);
    color: var(--text-primary);
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 2px;
  }

  .cron-description {
    color: var(--text-muted);
    font-size: 11px;
    line-height: 1.3;
  }
}

.task-status-switch {
  :deep(.el-switch) {
    .el-switch__core {
      background: var(--bg-quaternary);
      border: 1px solid var(--border-color);

      &::after {
        background: white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }

    &.is-checked .el-switch__core {
      background: var(--accent-primary);
      border-color: var(--accent-primary);
    }

    .el-switch__label {
      color: var(--text-muted);
      font-size: 12px;

      &.is-active {
        color: var(--accent-primary);
      }
    }
  }
}

.execution-count-cell {
  text-align: center;
  display: flex;

  .execution-count {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 2px;
  }

  .execution-limit {
    color: var(--text-muted);
    font-size: 11px;
  }
}

.next-execution-cell,
.last-execution-cell {
  color: var(--text-secondary);
  font-size: 13px;
  font-family: var(--font-mono, monospace);
}

// 操作按钮样式
.table-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;

  .el-button {
    width: 32px;
    height: 32px;
    padding: 0;
    background: transparent !important;
    border: none !important;

    &.is-circle {
      border-radius: 50%;

      .icon {
        width: 16px;
        height: 16px;
      }
    }

    &.action-btn-execute {
      color: var(--accent-primary);

      &:hover {
        color: var(--accent-primary-hover, #4a9eff);
        background: rgba(64, 158, 255, 0.1) !important;
      }
    }

    &.action-btn-view {
      color: #909399;

      &:hover {
        color: #7d8185;
        background: rgba(144, 147, 153, 0.1) !important;
      }
    }

    &.action-btn-edit {
      color: #e6a23c;

      &:hover {
        color: #ebb563;
        background: rgba(230, 162, 60, 0.1) !important;
      }
    }

    &.action-btn-delete {
      color: #f56c6c;

      &:hover {
        color: #f78989;
        background: rgba(245, 108, 108, 0.1) !important;
      }
    }
  }
}

// 响应式
@media (max-width: 768px) {
  .task-list-header {
    padding: 16px 20px;

    .header-content {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }
  }

  .table-actions {
    flex-wrap: wrap;
    gap: 6px;
    justify-content: center;
  }
}

.task-config-cell {
  .config-summary {
    color: var(--text-secondary);
    font-size: 12px;
    line-height: 1.4;
    max-width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
