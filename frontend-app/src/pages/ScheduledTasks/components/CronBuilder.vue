<template>
  <div class="cron-builder">
    <div class="cron-display-section">
      <div class="cron-expression-display">
        <div class="cron-label">Cron 表达式:</div>
        <div class="cron-value">{{ modelValue }}</div>
      </div>
      <div class="cron-description">
        <div class="description-label">执行时间:</div>
        <div class="description-text">{{ cronDescription }}</div>
      </div>
    </div>

    <div class="cron-builder-section">
      <div class="builder-tabs">
        <div 
          v-for="tab in tabs" 
          :key="tab.key"
          class="builder-tab"
          :class="{ 'builder-tab--active': activeTab === tab.key }"
          @click="activeTab = tab.key"
        >
          {{ tab.label }}
        </div>
      </div>

      <div class="builder-content">
        <!-- 简单模式 -->
        <div v-if="activeTab === 'simple'" class="simple-mode">
          <div class="time-picker-row">
            <div class="time-input-group">
              <label class="input-label">小时</label>
              <el-select v-model="simpleTime.hour" class="time-select">
                <el-option 
                  v-for="hour in 24" 
                  :key="hour-1" 
                  :label="String(hour-1).padStart(2, '0')" 
                  :value="hour-1" 
                />
              </el-select>
            </div>
            <div class="time-separator">:</div>
            <div class="time-input-group">
              <label class="input-label">分钟</label>
              <el-select v-model="simpleTime.minute" class="time-select">
                <el-option 
                  v-for="minute in [0, 15, 30, 45]" 
                  :key="minute" 
                  :label="String(minute).padStart(2, '0')" 
                  :value="minute" 
                />
              </el-select>
            </div>
          </div>

          <div class="frequency-section">
            <label class="section-label">执行频率</label>
            <div class="frequency-options">
              <div 
                v-for="freq in frequencies" 
                :key="freq.value"
                class="frequency-option"
                :class="{ 'frequency-option--active': simpleTime.frequency === freq.value }"
                @click="simpleTime.frequency = freq.value"
              >
                <div class="frequency-icon">{{ freq.icon }}</div>
                <div class="frequency-text">{{ freq.label }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 高级模式 -->
        <div v-else-if="activeTab === 'advanced'" class="advanced-mode">
          <div class="cron-field-grid">
            <div v-for="field in cronFields" :key="field.name" class="cron-field">
              <label class="field-label">{{ field.label }}</label>
              <el-input 
                v-model="advancedCron[field.name]" 
                :placeholder="field.placeholder"
                class="field-input"
              />
              <div class="field-help">{{ field.help }}</div>
            </div>
          </div>
        </div>

        <!-- 预设模式 -->
        <div v-else-if="activeTab === 'preset'" class="preset-mode">
          <div class="preset-grid">
            <div 
              v-for="preset in presets" 
              :key="preset.value"
              class="preset-card"
              :class="{ 'preset-card--active': modelValue === preset.value }"
              @click="selectPreset(preset.value)"
            >
              <div class="preset-icon">{{ preset.icon }}</div>
              <div class="preset-title">{{ preset.label }}</div>
              <div class="preset-description">{{ preset.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { cronDescriptions } from './cronUtils'

const props = defineProps({
  modelValue: {
    type: String,
    default: '0 9 * * *'
  }
})

const emit = defineEmits(['update:modelValue'])

const activeTab = ref('simple')

const tabs = [
  { key: 'simple', label: '简单模式' },
  { key: 'preset', label: '常用预设' },
  { key: 'advanced', label: '高级模式' }
]

// 简单模式数据
const simpleTime = ref({
  hour: 9,
  minute: 0,
  frequency: 'daily'
})

// 高级模式数据
const advancedCron = ref({
  minute: '0',
  hour: '9',
  day: '*',
  month: '*',
  weekday: '*'
})

const frequencies = [
  { value: 'daily', label: '每天', icon: '📅' },
  { value: 'weekdays', label: '工作日', icon: '💼' },
  { value: 'weekly', label: '每周', icon: '📆' },
  { value: 'monthly', label: '每月', icon: '🗓️' }
]

const cronFields = [
  { name: 'minute', label: '分钟', placeholder: '0-59', help: '0-59 或 * 表示任意' },
  { name: 'hour', label: '小时', placeholder: '0-23', help: '0-23 或 * 表示任意' },
  { name: 'day', label: '日期', placeholder: '1-31', help: '1-31 或 * 表示任意' },
  { name: 'month', label: '月份', placeholder: '1-12', help: '1-12 或 * 表示任意' },
  { name: 'weekday', label: '星期', placeholder: '0-7', help: '0-7 (0和7都表示周日) 或 * 表示任意' }
]

const presets = [
  {
    value: '0 9 * * *',
    label: '每日早上9点',
    description: '适合开盘前扫描',
    icon: '🌅'
  },
  {
    value: '30 14 * * *',
    label: '每日下午2:30',
    description: '适合午盘分析',
    icon: '🕐'
  },
  {
    value: '0 9 * * 1-5',
    label: '工作日早上9点',
    description: '仅交易日执行',
    icon: '💼'
  },
  {
    value: '0 8,12,16 * * 1-5',
    label: '工作日多次扫描',
    description: '8点、12点、16点',
    icon: '⏰'
  },
  {
    value: '0 9 1 * *',
    label: '每月1号早上9点',
    description: '月度扫描',
    icon: '🗓️'
  },
  {
    value: '0 9 * * 1',
    label: '每周一早上9点',
    description: '周度扫描',
    icon: '📅'
  }
]

// 计算 cron 描述
const cronDescription = computed(() => {
  return cronDescriptions.getCronDescription(props.modelValue)
})

// 监听简单模式变化
watch(simpleTime, (newTime) => {
  if (activeTab.value === 'simple') {
    let cronExpr = ''
    switch (newTime.frequency) {
      case 'daily':
        cronExpr = `${newTime.minute} ${newTime.hour} * * *`
        break
      case 'weekdays':
        cronExpr = `${newTime.minute} ${newTime.hour} * * 1-5`
        break
      case 'weekly':
        cronExpr = `${newTime.minute} ${newTime.hour} * * 1`
        break
      case 'monthly':
        cronExpr = `${newTime.minute} ${newTime.hour} 1 * *`
        break
    }
    emit('update:modelValue', cronExpr)
  }
}, { deep: true })

// 监听高级模式变化
watch(advancedCron, (newCron) => {
  if (activeTab.value === 'advanced') {
    const cronExpr = `${newCron.minute} ${newCron.hour} ${newCron.day} ${newCron.month} ${newCron.weekday}`
    emit('update:modelValue', cronExpr)
  }
}, { deep: true })

// 选择预设
const selectPreset = (preset) => {
  emit('update:modelValue', preset)
}

// 初始化数据
const parseCronExpression = (cron) => {
  const parts = cron.split(' ')
  if (parts.length === 5) {
    simpleTime.value.minute = parseInt(parts[0]) || 0
    simpleTime.value.hour = parseInt(parts[1]) || 9
    
    // 判断频率
    if (parts[4] === '1-5') {
      simpleTime.value.frequency = 'weekdays'
    } else if (parts[4] === '1') {
      simpleTime.value.frequency = 'weekly'
    } else if (parts[2] === '1') {
      simpleTime.value.frequency = 'monthly'
    } else {
      simpleTime.value.frequency = 'daily'
    }
    
    // 高级模式
    advancedCron.value.minute = parts[0]
    advancedCron.value.hour = parts[1]
    advancedCron.value.day = parts[2]
    advancedCron.value.month = parts[3]
    advancedCron.value.weekday = parts[4]
  }
}

// 初始化
parseCronExpression(props.modelValue)

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  parseCronExpression(newValue)
})
</script>

<style scoped lang="scss">
.cron-builder {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
}

// 显示区域
.cron-display-section {
  background: var(--bg-tertiary);
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  
  .cron-expression-display {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
    
    .cron-label {
      color: var(--text-secondary);
      font-size: 14px;
      font-weight: 500;
    }
    
    .cron-value {
      font-family: var(--font-mono, monospace);
      background: var(--bg-quaternary);
      color: var(--accent-primary);
      padding: 4px 8px;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 600;
    }
  }
  
  .cron-description {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .description-label {
      color: var(--text-secondary);
      font-size: 14px;
      font-weight: 500;
    }
    
    .description-text {
      color: var(--accent-primary);
      font-size: 14px;
      font-weight: 500;
    }
  }
}

// 构建器部分
.cron-builder-section {
  padding: 20px;
}

// 选项卡
.builder-tabs {
  display: flex;
  gap: 2px;
  margin-bottom: 20px;
  background: var(--bg-tertiary);
  border-radius: 8px;
  padding: 4px;
  
  .builder-tab {
    flex: 1;
    padding: 10px 16px;
    text-align: center;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      background: var(--bg-quaternary);
      color: var(--text-primary);
    }
    
    &--active {
      background: var(--accent-primary);
      color: white;
      font-weight: 600;
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    }
  }
}

// 构建器内容
.builder-content {
  min-height: 200px;
}

// 简单模式
.simple-mode {
  .time-picker-row {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    margin-bottom: 24px;
    justify-content: center;
    
    .time-input-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
      
      .input-label {
        color: var(--text-secondary);
        font-size: 12px;
        font-weight: 500;
        text-align: center;
      }
      
      .time-select {
        width: 80px;
        
        :deep(.el-select__wrapper) {
          background: var(--bg-tertiary);
          border: 1px solid var(--border-color);
          border-radius: 8px;
          
          &:hover {
            border-color: var(--accent-primary);
          }
        }
        
        :deep(.el-input__inner) {
          color: var(--text-primary);
          text-align: center;
          font-weight: 600;
          font-size: 16px;
        }
      }
    }
    
    .time-separator {
      color: var(--text-primary);
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 8px;
    }
  }
  
  .frequency-section {
    .section-label {
      display: block;
      color: var(--text-secondary);
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 16px;
      text-align: center;
    }
    
    .frequency-options {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 12px;
      
      .frequency-option {
        background: var(--bg-tertiary);
        border: 2px solid var(--border-color);
        border-radius: 12px;
        padding: 16px 12px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          background: var(--bg-quaternary);
          border-color: var(--accent-primary);
          transform: translateY(-2px);
        }
        
        &--active {
          background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
          border-color: var(--accent-primary);
          color: white;
          box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
          
          .frequency-icon {
            transform: scale(1.2);
          }
        }
        
        .frequency-icon {
          font-size: 24px;
          margin-bottom: 8px;
          transition: transform 0.3s ease;
        }
        
        .frequency-text {
          font-size: 14px;
          font-weight: 600;
          color: inherit;
        }
      }
    }
  }
}

// 高级模式
.advanced-mode {
  .cron-field-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 20px;
    
    .cron-field {
      .field-label {
        display: block;
        color: var(--text-secondary);
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 8px;
      }
      
      .field-input {
        :deep(.el-input__wrapper) {
          background: var(--bg-tertiary);
          border: 1px solid var(--border-color);
          border-radius: 8px;
          
          &:hover {
            border-color: var(--accent-primary);
          }
          
          &.is-focus {
            border-color: var(--accent-primary);
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
          }
        }
        
        :deep(.el-input__inner) {
          color: var(--text-primary);
          font-family: var(--font-mono, monospace);
        }
      }
      
      .field-help {
        font-size: 12px;
        color: var(--text-muted);
        margin-top: 4px;
        line-height: 1.4;
      }
    }
  }
}

// 预设模式
.preset-mode {
  .preset-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    
    .preset-card {
      background: var(--bg-tertiary);
      border: 2px solid var(--border-color);
      border-radius: 10px;
      padding: 14px 12px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      min-height: 100px;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 0.5s ease;
      }
      
      &:hover {
        background: var(--bg-quaternary);
        border-color: var(--accent-primary);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(59, 130, 246, 0.2);
        
        &::before {
          left: 100%;
        }
        
        .preset-icon {
          transform: scale(1.1);
        }
      }
      
      &--active {
        background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
        border-color: var(--accent-primary);
        color: white;
        box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        
        .preset-title,
        .preset-description {
          color: white;
        }
        
        .preset-icon {
          transform: scale(1.05);
        }
      }
      
      .preset-icon {
        font-size: 24px;
        margin-bottom: 8px;
        transition: transform 0.3s ease;
      }
      
      .preset-title {
        color: var(--text-primary);
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 4px;
        line-height: 1.3;
      }
      
      .preset-description {
        color: var(--text-secondary);
        font-size: 12px;
        line-height: 1.3;
      }
    }
  }
}
</style>