<template>
  <el-dialog
    :model-value="modelValue"
    :title="`执行结果详情 - ID: ${execution?.id || ''}`"
    width="1000px"
    @close="$emit('update:modelValue', false)"
  >
    <div v-if="execution" class="execution-results">
      <!-- 执行信息概览 -->
      <div class="execution-info bg-gray-50 dark:bg-gray-900 p-4 rounded-lg mb-4">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm text-gray-600 dark:text-gray-400">执行状态</label>
            <el-tag :type="getStatusType(execution.status)" size="large" class="mt-1">
              {{ getStatusText(execution.status) }}
            </el-tag>
          </div>
          <div>
            <label class="block text-sm text-gray-600 dark:text-gray-400">触发方式</label>
            <div class="text-sm font-medium mt-1">{{ getTriggerTypeText(execution.trigger_type) }}</div>
          </div>
          <div>
            <label class="block text-sm text-gray-600 dark:text-gray-400">结果数量</label>
            <div class="text-lg font-bold text-blue-600 dark:text-blue-400 mt-1">
              {{ execution.results_count }}
            </div>
          </div>
          <div>
            <label class="block text-sm text-gray-600 dark:text-gray-400">执行时长</label>
            <div class="text-sm font-medium mt-1">
              {{ execution.duration_seconds ? formatDuration(execution.duration_seconds) : '-' }}
            </div>
          </div>
        </div>
        
        <div v-if="execution.error_message" class="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded">
          <div class="text-sm text-red-600 dark:text-red-400">
            <i-carbon-warning class="mr-1" />
            错误信息：{{ execution.error_message }}
          </div>
        </div>
      </div>

      <!-- 扫描结果 -->
      <div v-if="execution.results_data && execution.results_data.length > 0" class="scan-results">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium">扫描结果</h3>
          <div class="flex items-center space-x-2">
            <el-button size="small" @click="exportResults">
              <i-carbon-download class="mr-1" />
              导出结果
            </el-button>
            <el-button size="small" @click="refreshResults">
              <i-carbon-refresh class="mr-1" />
              刷新
            </el-button>
          </div>
        </div>
        
        <el-table
          :data="resultsData"
          stripe
          max-height="400"
          class="w-full"
        >
          <el-table-column prop="stock_code" label="股票代码" width="100" fixed="left" />
          <el-table-column prop="stock_name" label="股票名称" width="120" fixed="left" />
          <el-table-column prop="price" label="价格" width="80">
            <template #default="{ row }">
              <span class="font-medium">{{ row.price?.toFixed(2) || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="change_percent" label="涨跌幅" width="80">
            <template #default="{ row }">
              <span 
                :class="{
                  'text-red-600 dark:text-red-400': row.change_percent > 0,
                  'text-green-600 dark:text-green-400': row.change_percent < 0,
                  'text-gray-600 dark:text-gray-400': row.change_percent === 0
                }"
              >
                {{ row.change_percent ? `${row.change_percent > 0 ? '+' : ''}${row.change_percent.toFixed(2)}%` : '-' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="signals" label="信号类型" width="120">
            <template #default="{ row }">
              <div class="flex flex-wrap gap-1">
                <el-tag
                  v-for="signal in row.signals"
                  :key="signal"
                  size="small"
                  :type="getSignalType(signal)"
                >
                  {{ getSignalText(signal) }}
                </el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="period" label="周期" width="60">
            <template #default="{ row }">
              {{ getPeriodText(row.period) }}
            </template>
          </el-table-column>
          <el-table-column prop="indicator_data" label="指标数据" min-width="200">
            <template #default="{ row }">
              <div class="text-xs space-y-1">
                <div v-if="row.indicator_data?.kdj_k">
                  KDJ: K={{ row.indicator_data.kdj_k.toFixed(2) }}, 
                  D={{ row.indicator_data.kdj_d.toFixed(2) }}, 
                  J={{ row.indicator_data.kdj_j.toFixed(2) }}
                </div>
                <div v-if="row.indicator_data?.macd">
                  MACD: {{ row.indicator_data.macd.toFixed(4) }}
                </div>
                <div v-if="row.indicator_data?.volume_pressure">
                  量压: {{ row.indicator_data.volume_pressure.toFixed(2) }}
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 无结果提示 -->
      <div v-else-if="execution.results_count === 0" class="no-results text-center py-8">
        <div class="text-gray-400 mb-2">
          <i-carbon-data-1 class="text-4xl" />
        </div>
        <div class="text-gray-600 dark:text-gray-400">
          本次扫描未找到符合条件的股票
        </div>
      </div>

      <!-- 任务配置信息 -->
      <div class="task-config mt-6">
        <h3 class="text-lg font-medium mb-3">任务配置</h3>
        <div class="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg">
          <div v-if="execution.task_config && execution.task_config.summary" class="formatted-config">
            <!-- 使用格式化的配置显示 -->
            <div class="config-header mb-3">
              <h4 class="text-base font-medium text-gray-800 dark:text-gray-200">
                {{ execution.task_config.display_name || '任务配置' }}
              </h4>
              <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {{ execution.task_config.summary }}
              </div>
            </div>
            
            <div class="config-details">
              <div v-for="(section, key) in execution.task_config.details" :key="key" class="config-section mb-4">
                <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ key }}</h5>
                <div class="config-content pl-3">
                  <div v-for="(value, subKey) in section" :key="subKey" class="config-item mb-1">
                    <span class="text-xs text-gray-500 dark:text-gray-500">{{ subKey }}:</span>
                    <span class="text-sm text-gray-700 dark:text-gray-300 ml-2">
                      <template v-if="Array.isArray(value)">
                        {{ value.join(', ') }}
                      </template>
                      <template v-else>
                        {{ value }}
                      </template>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 降级显示：如果没有格式化配置，显示原始JSON -->
          <div v-else class="raw-config">
            <pre class="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">{{ formatTaskConfig(execution.task_config_raw || execution.task_config) }}</pre>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  execution: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue'])

const resultsData = computed(() => {
  return props.execution?.results_data || []
})

const getStatusText = (status) => {
  const statusMap = {
    pending: '待执行',
    running: '执行中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const getStatusType = (status) => {
  const typeMap = {
    pending: 'warning',
    running: 'primary',
    completed: 'success',
    failed: 'danger',
    cancelled: 'info'
  }
  return typeMap[status] || 'info'
}

const getTriggerTypeText = (triggerType) => {
  const typeMap = {
    scheduled: '定时触发',
    manual: '手动触发'
  }
  return typeMap[triggerType] || triggerType
}

const getSignalText = (signal) => {
  const signalMap = {
    BUY: '买入',
    SELL: '卖出',
    HOLD: '持有',
    STOP_LOSS: '止损'
  }
  return signalMap[signal] || signal
}

const getSignalType = (signal) => {
  const typeMap = {
    BUY: 'success',
    SELL: 'danger',
    HOLD: 'warning',
    STOP_LOSS: 'danger'
  }
  return typeMap[signal] || 'info'
}

const getPeriodText = (period) => {
  const periodMap = {
    d: '日线',
    w: '周线',
    m: '月线'
  }
  return periodMap[period] || period
}

const formatDuration = (seconds) => {
  if (!seconds) return ''
  if (seconds < 60) return `${seconds}秒`
  if (seconds < 3600) return `${Math.floor(seconds / 60)}分${seconds % 60}秒`
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  return `${hours}时${minutes}分${secs}秒`
}

const formatTaskConfig = (configStr) => {
  try {
    const config = typeof configStr === 'string' ? JSON.parse(configStr) : configStr
    return JSON.stringify(config, null, 2)
  } catch (error) {
    return configStr || ''
  }
}

const exportResults = () => {
  if (!resultsData.value || resultsData.value.length === 0) {
    ElMessage.warning('没有可导出的数据')
    return
  }

  // 简单的CSV导出
  const headers = ['股票代码', '股票名称', '价格', '涨跌幅', '信号', '周期']
  const csvContent = [
    headers.join(','),
    ...resultsData.value.map(row => [
      row.stock_code,
      row.stock_name,
      row.price?.toFixed(2) || '',
      row.change_percent ? `${row.change_percent.toFixed(2)}%` : '',
      row.signals?.join(';') || '',
      getPeriodText(row.period)
    ].join(','))
  ].join('\n')

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `scan_results_${props.execution?.id || 'unknown'}.csv`)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  ElMessage.success('结果已导出')
}

const refreshResults = () => {
  ElMessage.info('结果已是最新数据')
}
</script>

<style scoped>
.execution-results :deep(.el-table__empty-text) {
  color: #9ca3af;
}

.formatted-config {
  .config-header {
    border-bottom: 1px solid rgba(156, 163, 175, 0.2);
    padding-bottom: 8px;
  }
  
  .config-section {
    border-left: 3px solid rgba(59, 130, 246, 0.3);
    padding-left: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .config-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    line-height: 1.5;
  }
}

.raw-config pre {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  background: rgba(156, 163, 175, 0.1);
  border-radius: 6px;
  padding: 12px;
  margin: 0;
  overflow-x: auto;
}
</style>