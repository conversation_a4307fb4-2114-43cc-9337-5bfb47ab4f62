<template>
  <div class="simple-builder">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div class="form-group">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          执行频率
        </label>
        <el-select v-model="frequency" @change="handleFrequencyChange" class="w-full">
          <el-option label="每分钟" value="minute" />
          <el-option label="每小时" value="hour" />
          <el-option label="每天" value="daily" />
          <el-option label="每周" value="weekly" />
          <el-option label="每月" value="monthly" />
          <el-option label="自定义" value="custom" />
        </el-select>
      </div>
      
      <div v-if="frequency === 'daily'" class="form-group">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          执行时间
        </label>
        <el-time-picker
          v-model="dailyTime"
          format="HH:mm"
          placeholder="选择时间"
          @change="buildFromSimple"
          class="w-full"
        />
      </div>
      
      <div v-if="frequency === 'weekly'" class="form-group">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          星期几
        </label>
        <el-select v-model="weekDay" @change="buildFromSimple" class="w-full">
          <el-option label="周一" value="1" />
          <el-option label="周二" value="2" />
          <el-option label="周三" value="3" />
          <el-option label="周四" value="4" />
          <el-option label="周五" value="5" />
          <el-option label="周六" value="6" />
          <el-option label="周日" value="0" />
        </el-select>
      </div>
      
      <div v-if="frequency === 'weekly'" class="form-group">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          执行时间
        </label>
        <el-time-picker
          v-model="weeklyTime"
          format="HH:mm"
          placeholder="选择时间"
          @change="buildFromSimple"
          class="w-full"
        />
      </div>
      
      <div v-if="frequency === 'monthly'" class="form-group">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          每月第几天
        </label>
        <el-input-number
          v-model="monthDay"
          :min="1"
          :max="31"
          controls-position="right"
          @change="buildFromSimple"
          class="w-full"
        />
      </div>
      
      <div v-if="frequency === 'monthly'" class="form-group">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          执行时间
        </label>
        <el-time-picker
          v-model="monthlyTime"
          format="HH:mm"
          placeholder="选择时间"
          @change="buildFromSimple"
          class="w-full"
        />
      </div>
      
      <div v-if="frequency === 'hour'" class="form-group">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          间隔（小时）
        </label>
        <el-input-number
          v-model="hourInterval"
          :min="1"
          :max="23"
          controls-position="right"
          @change="buildFromSimple"
          class="w-full"
        />
      </div>
      
      <div v-if="frequency === 'minute'" class="form-group">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          间隔（分钟）
        </label>
        <el-input-number
          v-model="minuteInterval"
          :min="1"
          :max="59"
          controls-position="right"
          @change="buildFromSimple"
          class="w-full"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      minute: '*',
      hour: '*',
      dayOfMonth: '*',
      month: '*',
      dayOfWeek: '*'
    })
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const frequency = ref('daily')
const dailyTime = ref(new Date(2024, 0, 1, 9, 0)) // 默认9:00
const weekDay = ref('1') // 周一
const weeklyTime = ref(new Date(2024, 0, 1, 9, 0))
const monthDay = ref(1)
const monthlyTime = ref(new Date(2024, 0, 1, 9, 0))
const hourInterval = ref(1)
const minuteInterval = ref(5)

const handleFrequencyChange = () => {
  buildFromSimple()
}

const buildFromSimple = () => {
  let cronData = {
    minute: '*',
    hour: '*',
    dayOfMonth: '*',
    month: '*',
    dayOfWeek: '*'
  }

  switch (frequency.value) {
    case 'minute':
      cronData.minute = `*/${minuteInterval.value}`
      break
    case 'hour':
      cronData.minute = '0'
      cronData.hour = `*/${hourInterval.value}`
      break
    case 'daily':
      if (dailyTime.value) {
        cronData.minute = dailyTime.value.getMinutes().toString()
        cronData.hour = dailyTime.value.getHours().toString()
      }
      break
    case 'weekly':
      if (weeklyTime.value) {
        cronData.minute = weeklyTime.value.getMinutes().toString()
        cronData.hour = weeklyTime.value.getHours().toString()
        cronData.dayOfWeek = weekDay.value
      }
      break
    case 'monthly':
      if (monthlyTime.value) {
        cronData.minute = monthlyTime.value.getMinutes().toString()
        cronData.hour = monthlyTime.value.getHours().toString()
        cronData.dayOfMonth = monthDay.value.toString()
      }
      break
  }

  emit('update:modelValue', cronData)
  emit('change')
}

// 监听props变化来初始化界面
watch(() => props.modelValue, (newValue) => {
  // 这里可以根据cron数据反推界面状态
  // 简化实现，保持默认值
}, { immediate: true })
</script>

<style scoped>
.simple-builder .form-group {
  min-height: 80px;
}
</style>