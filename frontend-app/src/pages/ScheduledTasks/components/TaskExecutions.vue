<template>
  <el-dialog
    :model-value="modelValue"
    title="任务执行记录"
    width="1200px"
    @close="$emit('update:modelValue', false)"
  >
    <div class="task-executions">
      <!-- 过滤器 -->
      <div class="filters bg-gray-50 dark:bg-gray-800 p-4 rounded-lg mb-4">
        <div class="flex items-center space-x-4">
          <div class="w-100px">
            <label class="block text-sm text-gray-700 dark:text-gray-300 mb-1">触发类型</label>
            <el-select v-model="filters.trigger_type" placeholder="全部" clearable @change="handleFilterChange">
              <el-option label="定时触发" value="scheduled" />
              <el-option label="手动触发" value="manual" />
            </el-select>
          </div>
          
          <div class="w-100px">
            <label class="block text-sm text-gray-700 dark:text-gray-300 mb-1">执行状态</label>
            <el-select v-model="filters.status" placeholder="全部" clearable @change="handleFilterChange">
              <el-option label="待执行" value="pending" />
              <el-option label="执行中" value="running" />
              <el-option label="已完成" value="completed" />
              <el-option label="失败" value="failed" />
              <el-option label="已取消" value="cancelled" />
            </el-select>
          </div>
          
          <div class="flex-1" />
          
          <el-button @click="handleRefresh">
            <i-carbon-refresh class="mr-1" />
            刷新
          </el-button>
        </div>
      </div>

      <!-- 执行记录表格 -->
      <el-table
        :data="executions"
        :loading="loading"
        stripe
        class="w-full"
        empty-text="暂无执行记录"
      >
        <el-table-column prop="id" label="执行ID" width="80" />
        
        <el-table-column prop="trigger_type" label="触发方式" width="100">
          <template #default="{ row }">
            <el-tag :type="row.trigger_type === 'scheduled' ? 'primary' : 'success'" size="small">
              {{ getTriggerTypeText(row.trigger_type) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="执行状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="start_time" label="开始时间" width="140">
          <template #default="{ row }">
            {{ row.start_time ? formatDateTime(row.start_time) : '-' }}
          </template>
        </el-table-column>
        
        <el-table-column prop="end_time" label="结束时间" width="140">
          <template #default="{ row }">
            {{ row.end_time ? formatDateTime(row.end_time) : '-' }}
          </template>
        </el-table-column>
        
        <el-table-column prop="duration_seconds" label="执行时长" width="100">
          <template #default="{ row }">
            {{ row.duration_seconds ? formatDuration(row.duration_seconds) : '-' }}
          </template>
        </el-table-column>
        
        <el-table-column prop="results_count" label="结果数量" width="100">
          <template #default="{ row }">
            <span class="font-medium text-blue-600 dark:text-blue-400">
              {{ row.results_count }}
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="error_message" label="错误信息" min-width="200">
          <template #default="{ row }">
            <div v-if="row.error_message" class="text-red-600 dark:text-red-400 text-sm">
              {{ row.error_message }}
            </div>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <div class="flex items-center space-x-1">
              <el-button 
                size="small" 
                type="primary" 
                @click="viewResults(row)"
                :disabled="!row.results_data"
              >
                <i-carbon-view class="mr-1" />
                查看结果
              </el-button>
              
              <el-dropdown @command="(cmd) => handleDropdownCommand(cmd, row)">
                <el-button size="small" type="text">
                  <i-carbon-overflow-menu-horizontal />
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item 
                      command="cancel" 
                      v-if="row.status === 'pending' || row.status === 'running'"
                    >
                      <i-carbon-stop class="mr-2" />
                      取消执行
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" divided>
                      <i-carbon-trash-can class="mr-2" />
                      删除记录
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="flex justify-center mt-4">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="loadExecutions"
          @size-change="loadExecutions"
        />
      </div>
    </div>

    <!-- 结果详情对话框 -->
    <ExecutionResults
      v-model="showResultsDialog"
      :execution="selectedExecution"
    />
  </el-dialog>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'
import { useTaskExecutions } from '../composables/useTaskExecutions'
import { ElMessage, ElMessageBox } from 'element-plus'
import ExecutionResults from './ExecutionResults.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  taskId: {
    type: Number,
    default: null
  }
})

const emit = defineEmits(['update:modelValue'])

const {
  executions,
  loading,
  pagination,
  loadExecutions,
  deleteExecution,
  cancelExecution,
  getStatusText,
  getStatusType,
  getTriggerTypeText
} = useTaskExecutions()

const filters = ref({
  trigger_type: null,
  status: null
})

const showResultsDialog = ref(false)
const selectedExecution = ref(null)

// 监听对话框显示状态
watch(() => props.modelValue, (visible) => {
  if (visible) {
    nextTick(() => {
      loadExecutions(props.taskId)
    })
  }
})

const handleFilterChange = () => {
  pagination.current = 1
  loadExecutions(props.taskId, filters.value)
}

const handleRefresh = () => {
  loadExecutions(props.taskId, filters.value)
}

const viewResults = (execution) => {
  selectedExecution.value = execution
  showResultsDialog.value = true
}

const handleDropdownCommand = async (command, execution) => {
  switch (command) {
    case 'cancel':
      try {
        await ElMessageBox.confirm('确认取消此次执行？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await cancelExecution(execution.id)
        await loadExecutions(props.taskId, filters.value)
      } catch (error) {
        if (error !== 'cancel') {
          console.error('取消执行失败:', error)
        }
      }
      break
    case 'delete':
      try {
        await ElMessageBox.confirm('确认删除此执行记录？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await deleteExecution(execution.id)
        await loadExecutions(props.taskId, filters.value)
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除记录失败:', error)
        }
      }
      break
  }
}

const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  const date = new Date(dateTime)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const formatDuration = (seconds) => {
  if (!seconds) return ''
  if (seconds < 60) return `${seconds}秒`
  if (seconds < 3600) return `${Math.floor(seconds / 60)}分${seconds % 60}秒`
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  return `${hours}时${minutes}分${secs}秒`
}
</script>

<style scoped>
.task-executions :deep(.el-table__empty-text) {
  color: #9ca3af;
}
</style>