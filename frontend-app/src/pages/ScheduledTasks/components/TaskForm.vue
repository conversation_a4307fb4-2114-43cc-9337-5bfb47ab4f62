<template>
  <el-dialog
    :model-value="modelValue"
    :title="isEditing ? '编辑定时任务' : '创建定时任务'"
    width="800px"
    @close="$emit('cancel')"
    class="scheduled-task-dialog"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      class="scheduled-task-form"
    >
      <el-form-item label="任务名称" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请输入任务名称"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="任务描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入任务描述（可选）"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
      
      <el-form-item label="执行时间" prop="cron_expression" class="cron-expression-form-item">
        <div class="cron-builder-wrapper">
          <CronBuilder v-model="formData.cron_expression" />
        </div>
      </el-form-item>
      
      <el-form-item label="扫描模式" prop="scan_mode">
        <el-radio-group v-model="formData.task_config.scan_mode" class="scan-mode-radio-group">
          <el-radio value="traditional" class="scan-mode-radio">传统模式（仅日线）</el-radio>
          <el-radio value="multi_period" class="scan-mode-radio">多周期模式</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item v-if="formData.task_config.scan_mode === 'multi_period'" label="扫描周期" prop="periods">
        <el-checkbox-group v-model="formData.task_config.periods" class="periods-checkbox-group">
          <el-checkbox value="d" class="period-checkbox">日线</el-checkbox>
          <el-checkbox value="w" class="period-checkbox">周线</el-checkbox>
          <el-checkbox value="m" class="period-checkbox">月线</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      
      <el-form-item label="扫描指标" prop="task_config.indicators">
        <div class="w-full">
          <PeriodIndicatorMatrix
            v-model="periodIndicators"
            :scan-mode="formData.task_config.scan_mode"
            :selected-periods="formData.task_config.periods"
            type="small"
          />
          <div class="form-help-text">
            <template v-if="formData.task_config.scan_mode === 'traditional'">
              请选择要扫描的技术指标（至少选择一个）
            </template>
            <template v-else>
              请为至少一个周期选择指标进行扫描
            </template>
          </div>
        </div>
      </el-form-item>
      
      <!-- 复权方式隐藏，默认不复权 -->
      <!-- 
      <el-form-item label="复权方式" prop="adjust">
        <el-radio-group v-model="formData.task_config.adjust" class="adjust-radio-group">
          <el-radio value="n" class="adjust-radio">不复权</el-radio>
          <el-radio value="f" class="adjust-radio">前复权</el-radio>
          <el-radio value="b" class="adjust-radio">后复权</el-radio>
        </el-radio-group>
      </el-form-item>
      -->
      
      <el-form-item label="最大执行次数" prop="max_executions">
        <div class="max-executions-control">
          <el-switch
            v-model="hasMaxExecutions"
            active-text="限制执行次数"
            inactive-text="无限制执行"
            @change="handleMaxExecutionsToggle"
            class="executions-switch"
          />
          <el-input-number
            v-if="hasMaxExecutions"
            v-model="formData.max_executions"
            :min="1"
            :max="1000"
            controls-position="right"
            placeholder="最大执行次数"
            class="executions-input"
          />
        </div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('cancel')" class="footer-btn-cancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" class="footer-btn-primary">
          {{ isEditing ? '更新任务' : '创建任务' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import CronBuilder from './CronBuilder.vue'
import PeriodIndicatorMatrix from '@/components/scanner/PeriodIndicatorMatrix.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  task: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'submit', 'cancel'])

const formRef = ref(null)
const stockScope = ref('all')
const stockCodesInput = ref('')
const hasMaxExecutions = ref(false)

// 表单数据
const formData = ref({
  name: '',
  description: '',
  cron_expression: '0 9 * * *',
  max_executions: null,
  task_config: {
    indicators: ['kdj'],
    stock_codes: null,
    parameters: null,
    scan_mode: 'traditional',
    periods: ['d'],
    adjust: 'n'
  }
})

// 多周期指标选择数据
const periodIndicators = ref({
  d: ['kdj'],
  w: [],
  m: []
})


// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { min: 2, max: 100, message: '任务名称长度在2-100个字符', trigger: 'blur' }
  ],
  cron_expression: [
    { required: true, message: '请设置执行时间', trigger: 'blur' }
  ],
  'task_config.indicators': [
    { 
      type: 'array', 
      required: true, 
      min: 1, 
      message: '请至少选择一个扫描指标', 
      trigger: 'change',
      validator: (_rule, value, callback) => {
        // 检查是否有选中的指标
        if (formData.value.task_config.scan_mode === 'traditional') {
          // 传统模式：检查indicators数组
          if (!value || value.length === 0) {
            callback(new Error('请至少选择一个扫描指标'))
            return
          }
        } else {
          // 多周期模式：检查periodIndicators是否有任何周期选择了指标
          const hasAnyIndicator = Object.values(periodIndicators.value).some(
            (indicators) => indicators && indicators.length > 0
          )
          if (!hasAnyIndicator) {
            callback(new Error('请为至少一个周期选择指标'))
            return
          }
        }
        callback()
      }
    }
  ]
}

const isEditing = computed(() => !!props.task)

// 重置表单函数
const resetForm = () => {
  formData.value = {
    name: '',
    description: '',
    cron_expression: '0 9 * * *',
    max_executions: null,
    task_config: {
      indicators: ['kdj'],
      stock_codes: null, // 默认扫描全部股票
      parameters: null,
      scan_mode: 'traditional',
      periods: ['d'],
      adjust: 'n' // 默认不复权
    }
  }
  
  // 重置周期指标数据
  periodIndicators.value = {
    d: ['kdj'],
    w: [],
    m: []
  }
  
  stockScope.value = 'all'
  stockCodesInput.value = ''
  hasMaxExecutions.value = false
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 监听任务变化，初始化表单
watch(() => props.task, (newTask) => {
  if (newTask) {
    // 编辑模式，填充表单数据
    formData.value = {
      name: newTask.name || '',
      description: newTask.description || '',
      cron_expression: newTask.cron_expression || '0 9 * * *',
      max_executions: newTask.max_executions,
      task_config: {
        indicators: newTask.task_config?.indicators || ['kdj'],
        stock_codes: null, // 始终设置为扫描全部股票
        parameters: newTask.task_config?.parameters,
        scan_mode: newTask.task_config?.scan_mode || 'traditional',
        periods: newTask.task_config?.periods || ['d'],
        adjust: 'n' // 始终设置为不复权
      }
    }
    
    // 始终设置为扫描全部股票
    stockScope.value = 'all'
    stockCodesInput.value = ''
    
    // 初始化周期指标数据
    if (newTask.task_config?.period_indicators) {
      // 从period_indicators恢复数据（多周期模式）
      periodIndicators.value = {
        d: newTask.task_config.period_indicators.d || [],
        w: newTask.task_config.period_indicators.w || [],
        m: newTask.task_config.period_indicators.m || []
      }
    } else {
      // 从indicators恢复数据（传统模式）
      periodIndicators.value = {
        d: newTask.task_config?.indicators || ['kdj'],
        w: [],
        m: []
      }
    }
    
    // 设置最大执行次数
    hasMaxExecutions.value = !!newTask.max_executions
  } else {
    // 新建模式，重置表单
    resetForm()
  }
}, { immediate: true })

// 监听periodIndicators变化，同步到formData
watch(periodIndicators, (newPeriodIndicators) => {
  if (formData.value.task_config.scan_mode === 'traditional') {
    // 传统模式：使用日线指标
    formData.value.task_config.indicators = newPeriodIndicators.d || []
  } else {
    // 多周期模式：合并所有周期的指标（用于表单验证）
    const allIndicators = new Set()
    Object.values(newPeriodIndicators).forEach(indicators => {
      indicators.forEach(indicator => allIndicators.add(indicator))
    })
    formData.value.task_config.indicators = Array.from(allIndicators)
  }
}, { deep: true })

// 监听扫描模式变化，同步指标数据
watch(() => formData.value.task_config.scan_mode, (newScanMode) => {
  if (newScanMode === 'traditional') {
    // 切换到传统模式：只保留日线指标，清空其他周期
    periodIndicators.value = {
      d: periodIndicators.value.d || [],
      w: [],
      m: []
    }
    formData.value.task_config.indicators = periodIndicators.value.d || []
  } else {
    // 切换到多周期模式：如果日线指标为空，设置默认值
    if (!periodIndicators.value.d || periodIndicators.value.d.length === 0) {
      periodIndicators.value.d = ['kdj']
    }
  }
})

// const handleStockScopeChange = () => {
//   // 始终设置为扫描全部股票
//   formData.value.task_config.stock_codes = null
//   stockCodesInput.value = ''
// }

const parseStockCodes = () => {
  // 始终设置为扫描全部股票，不解析特定代码
  formData.value.task_config.stock_codes = null
}

const handleMaxExecutionsToggle = () => {
  if (!hasMaxExecutions.value) {
    formData.value.max_executions = null
  } else {
    formData.value.max_executions = 10
  }
}


const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    // 表单验证
    await formRef.value.validate()
    
    // 解析股票代码
    parseStockCodes()
    
    // 根据扫描模式准备提交数据
    const submitData = { ...formData.value }
    
    if (formData.value.task_config.scan_mode === 'multi_period') {
      // 多周期模式：添加period_indicators字段
      submitData.task_config.period_indicators = periodIndicators.value
      
      // 清理空的周期数据
      Object.keys(submitData.task_config.period_indicators).forEach(period => {
        if (!submitData.task_config.period_indicators[period] || 
            submitData.task_config.period_indicators[period].length === 0) {
          delete submitData.task_config.period_indicators[period]
        }
      })
    } else {
      // 传统模式：确保没有period_indicators字段
      delete submitData.task_config.period_indicators
      // 传统模式只使用日线指标
      submitData.task_config.indicators = periodIndicators.value.d || []
    }
    
    // 调试输出
    console.log('提交数据:', {
      scanMode: submitData.task_config.scan_mode,
      indicators: submitData.task_config.indicators,
      periods: submitData.task_config.periods,
      periodIndicators: submitData.task_config.period_indicators
    })
    
    // 提交数据
    emit('submit', submitData)
  } catch (error) {
    ElMessage.error('请检查表单输入')
  }
}
</script>

<style scoped lang="scss">
// 对话框样式
.scheduled-task-dialog {
  :deep(.el-dialog) {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    box-shadow: var(--shadow-xl);
  }
  
  :deep(.el-dialog__header) {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    padding: 20px 24px;
  }
  
  :deep(.el-dialog__title) {
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
  }
  
  :deep(.el-dialog__body) {
    padding: 24px;
    background: var(--bg-secondary);
  }
  
  :deep(.el-dialog__footer) {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    padding: 16px 24px;
  }
}

// 表单样式
.scheduled-task-form {
  :deep(.el-form-item__label) {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 14px;
  }
  
  :deep(.el-form-item__content) {
    color: var(--text-primary);
  }
  
  // 输入框样式
  :deep(.el-input__wrapper) {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: var(--accent-primary);
    }
    
    &.is-focus {
      border-color: var(--accent-primary);
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    }
  }
  
  :deep(.el-textarea__inner) {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    transition: all 0.3s ease;
    
    &:hover {
      border-color: var(--accent-primary);
    }
    
    &:focus {
      border-color: var(--accent-primary);
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    }
  }
  
  :deep(.el-input__inner) {
    color: var(--text-primary);
    background: transparent;
    
    &::placeholder {
      color: var(--text-muted);
    }
  }
}


// 股票范围单选框样式
.stock-scope-radio-group {
  display: flex;
  gap: 20px;
  
  .stock-scope-radio {
    :deep(.el-radio__label) {
      color: var(--text-primary);
      font-weight: 500;
    }
    
    :deep(.el-radio__input.is-checked .el-radio__inner) {
      background: var(--accent-primary);
      border-color: var(--accent-primary);
    }
  }
}

// 股票代码输入区域
.stock-codes-input-section {
  .stock-codes-textarea {
    :deep(.el-textarea__inner) {
      min-height: 80px;
      font-family: var(--font-mono, monospace);
      font-size: 14px;
    }
  }
}

// 扫描模式单选框样式
.scan-mode-radio-group {
  display: flex;
  gap: 20px;
  
  .scan-mode-radio {
    :deep(.el-radio__label) {
      color: var(--text-primary);
      font-weight: 500;
    }
    
    :deep(.el-radio__input.is-checked .el-radio__inner) {
      background: var(--accent-primary);
      border-color: var(--accent-primary);
    }
  }
}

// 周期复选框样式
.periods-checkbox-group {
  display: flex;
  gap: 16px;
  
  .period-checkbox {
    :deep(.el-checkbox__label) {
      color: var(--text-primary);
      font-weight: 500;
    }
    
    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
      background: var(--accent-primary);
      border-color: var(--accent-primary);
    }
  }
}

// 复权方式单选框样式
.adjust-radio-group {
  display: flex;
  gap: 20px;
  
  .adjust-radio {
    :deep(.el-radio__label) {
      color: var(--text-primary);
      font-weight: 500;
    }
    
    :deep(.el-radio__input.is-checked .el-radio__inner) {
      background: var(--accent-primary);
      border-color: var(--accent-primary);
    }
  }
}

// 最大执行次数控制
.max-executions-control {
  display: flex;
  align-items: center;
  gap: 16px;
  
  .executions-switch {
    :deep(.el-switch__core) {
      background: var(--bg-quaternary);
      border: 1px solid var(--border-color);
      
      &::after {
        background: white;
      }
    }
    
    :deep(.el-switch.is-checked .el-switch__core) {
      background: var(--accent-primary);
      border-color: var(--accent-primary);
    }
    
    :deep(.el-switch__action) {
      color: var(--text-primary);
      font-size: 14px;
    }
  }
  
  .executions-input {
    width: 160px;
    
    :deep(.el-input-number__decrease),
    :deep(.el-input-number__increase) {
      background: var(--bg-quaternary);
      border-color: var(--border-color);
      color: var(--text-primary);
      
      &:hover {
        background: var(--accent-primary);
        color: white;
      }
    }
  }
}

// 帮助文本
.form-help-text {
  font-size: 12px;
  color: var(--text-muted);
  margin-top: 8px;
  line-height: 1.4;
}

// 对话框底部按钮
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  
  .footer-btn-cancel {
    :deep(.el-button) {
      background: var(--bg-tertiary);
      border: 1px solid var(--border-color);
      color: var(--text-primary);
      border-radius: 8px;
      padding: 10px 20px;
      font-weight: 500;
      transition: all 0.3s ease;
      
      &:hover {
        background: var(--bg-quaternary);
        border-color: var(--accent-primary);
        transform: translateY(-1px);
      }
    }
  }
  
  .footer-btn-primary {
    :deep(.el-button) {
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
      border: none;
      color: white;
      border-radius: 8px;
      padding: 10px 20px;
      font-weight: 600;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
      }
    }
  }
}

// Cron表达式编辑器样式
.cron-expression-form-item {
  :deep(.el-form-item__content) {
    width: 100%;
    
    .cron-builder-wrapper {
      width: 100%;
      min-width: 0; // 允许flex子项缩小
      
      .cron-builder {
        width: 100%;
        max-width: none;
      }
    }
  }
}
</style>