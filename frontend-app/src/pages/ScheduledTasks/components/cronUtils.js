// Cron表达式工具函数

export const cronDescriptions = {
  // 预定义的常见Cron表达式描述
  presets: {
    '0 9 * * *': '每天早上9点执行',
    '0 12 * * *': '每天中午12点执行',
    '0 18 * * *': '每天下午6点执行',
    '0 9 * * 1-5': '工作日早上9点执行',
    '30 14 * * *': '每天下午2:30执行',
    '0 8,12,16 * * 1-5': '工作日8点、12点、16点执行',
    '0 9 1 * *': '每月1号早上9点执行',
    '0 9 * * 1': '每周一早上9点执行',
    '0 */4 * * *': '每4小时执行一次',
    '*/15 * * * *': '每15分钟执行一次'
  },

  // 解析Cron表达式为可读描述
  getCronDescription(cronExpr) {
    if (!cronExpr || typeof cronExpr !== 'string') {
      return '无效的Cron表达式'
    }

    // 检查预设
    if (this.presets[cronExpr]) {
      return this.presets[cronExpr]
    }

    const parts = cronExpr.trim().split(/\s+/)
    if (parts.length !== 5) {
      return '无效的Cron表达式格式'
    }

    const [minute, hour, day, month, weekday] = parts

    try {
      let description = ''
      
      // 解析分钟
      const minuteDesc = this.parseMinute(minute)
      
      // 解析小时
      const hourDesc = this.parseHour(hour)
      
      // 解析日期
      const dayDesc = this.parseDay(day)
      
      // 解析月份
      const monthDesc = this.parseMonth(month)
      
      // 解析星期
      const weekdayDesc = this.parseWeekday(weekday)
      
      // 组合描述
      if (weekdayDesc && weekdayDesc !== '每天') {
        description = weekdayDesc
      } else if (dayDesc && dayDesc !== '每日') {
        description = dayDesc
      } else {
        description = '每天'
      }
      
      if (monthDesc && monthDesc !== '每月') {
        description = monthDesc + description
      }
      
      description += hourDesc + minuteDesc
      
      return description || '自定义时间'
    } catch (error) {
      return '无效的Cron表达式'
    }
  },

  parseMinute(minute) {
    if (minute === '*') return ''
    if (minute === '0') return ''
    if (minute.includes('/')) {
      const interval = minute.split('/')[1]
      return `:${interval}分钟间隔`
    }
    if (minute.includes(',')) {
      return `:${minute.split(',').join('、')}分`
    }
    return `:${minute}分`
  },

  parseHour(hour) {
    if (hour === '*') {
      return '每小时'
    }
    if (hour.includes('/')) {
      const interval = hour.split('/')[1]
      return `每${interval}小时`
    }
    if (hour.includes(',')) {
      const hours = hour.split(',').map(h => `${h}点`).join('、')
      return hours
    }
    if (hour.includes('-')) {
      const [start, end] = hour.split('-')
      return `${start}点到${end}点每小时`
    }
    return `${hour}点`
  },

  parseDay(day) {
    if (day === '*') return ''
    if (day === '1') return '每月1号'
    if (day.includes('/')) {
      const interval = day.split('/')[1]
      return `每${interval}天`
    }
    if (day.includes(',')) {
      const days = day.split(',').map(d => `${d}号`).join('、')
      return days
    }
    return `${day}号`
  },

  parseMonth(month) {
    if (month === '*') return ''
    const monthNames = ['', '1月', '2月', '3月', '4月', '5月', '6月', 
                       '7月', '8月', '9月', '10月', '11月', '12月']
    if (month.includes(',')) {
      const months = month.split(',').map(m => monthNames[parseInt(m)]).join('、')
      return months
    }
    return monthNames[parseInt(month)] || month + '月'
  },

  parseWeekday(weekday) {
    if (weekday === '*') return ''
    
    const weekNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六', '周日']
    
    if (weekday === '1-5') return '工作日'
    if (weekday === '6,0' || weekday === '0,6') return '周末'
    
    if (weekday.includes(',')) {
      const days = weekday.split(',').map(w => weekNames[parseInt(w)]).join('、')
      return days
    }
    
    if (weekday.includes('-')) {
      const [start, end] = weekday.split('-')
      return `${weekNames[parseInt(start)]}到${weekNames[parseInt(end)]}`
    }
    
    return weekNames[parseInt(weekday)] || '每天'
  }
}

// 验证Cron表达式
export const validateCronExpression = (cronExpr) => {
  if (!cronExpr || typeof cronExpr !== 'string') {
    return { valid: false, error: 'Cron表达式不能为空' }
  }

  const parts = cronExpr.trim().split(/\s+/)
  if (parts.length !== 5) {
    return { valid: false, error: 'Cron表达式必须包含5个部分' }
  }

  const [minute, hour, day, month, weekday] = parts

  // 验证分钟 (0-59)
  if (!validateField(minute, 0, 59)) {
    return { valid: false, error: '分钟字段无效 (0-59)' }
  }

  // 验证小时 (0-23)
  if (!validateField(hour, 0, 23)) {
    return { valid: false, error: '小时字段无效 (0-23)' }
  }

  // 验证日期 (1-31)
  if (!validateField(day, 1, 31)) {
    return { valid: false, error: '日期字段无效 (1-31)' }
  }

  // 验证月份 (1-12)
  if (!validateField(month, 1, 12)) {
    return { valid: false, error: '月份字段无效 (1-12)' }
  }

  // 验证星期 (0-7, 0和7都表示周日)
  if (!validateField(weekday, 0, 7)) {
    return { valid: false, error: '星期字段无效 (0-7)' }
  }

  return { valid: true, error: null }
}

// 验证单个字段
const validateField = (field, min, max) => {
  if (field === '*') return true
  
  // 处理步长值 (如 */5)
  if (field.includes('/')) {
    const [base, step] = field.split('/')
    if (base === '*' || (parseInt(base) >= min && parseInt(base) <= max)) {
      const stepNum = parseInt(step)
      return stepNum > 0 && stepNum <= max
    }
    return false
  }
  
  // 处理范围 (如 1-5)
  if (field.includes('-')) {
    const [start, end] = field.split('-').map(n => parseInt(n))
    return start >= min && end <= max && start <= end
  }
  
  // 处理列表 (如 1,3,5)
  if (field.includes(',')) {
    const values = field.split(',').map(n => parseInt(n))
    return values.every(val => val >= min && val <= max)
  }
  
  // 单个数值
  const num = parseInt(field)
  return !isNaN(num) && num >= min && num <= max
}

// 生成下次执行时间预览
export const getNextExecutions = (cronExpr, count = 5) => {
  try {
    // 这里可以集成 cron-parser 或类似库
    // 暂时返回模拟数据
    const now = new Date()
    const executions = []
    
    // 简单的模拟逻辑，实际应该用专门的cron解析库
    const parts = cronExpr.split(' ')
    if (parts.length === 5) {
      const [minute, hour] = parts
      const minNum = parseInt(minute) || 0
      const hourNum = parseInt(hour) || 9
      
      for (let i = 0; i < count; i++) {
        const next = new Date(now)
        next.setDate(next.getDate() + i)
        next.setHours(hourNum, minNum, 0, 0)
        
        if (next <= now) {
          next.setDate(next.getDate() + 1)
        }
        
        executions.push(next.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          weekday: 'short'
        }))
      }
    }
    
    return executions
  } catch (error) {
    return []
  }
}