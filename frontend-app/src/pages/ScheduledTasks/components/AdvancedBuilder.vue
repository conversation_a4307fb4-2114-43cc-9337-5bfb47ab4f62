<template>
  <div class="advanced-builder">
    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
      <div class="form-group">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          分钟 (0-59)
        </label>
        <el-input
          v-model="cronData.minute"
          placeholder="*"
          @input="emit('change')"
        />
        <div class="text-xs text-gray-500 mt-1">
          * 表示任意值<br/>
          */5 表示每5分钟
        </div>
      </div>
      
      <div class="form-group">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          小时 (0-23)
        </label>
        <el-input
          v-model="cronData.hour"
          placeholder="*"
          @input="emit('change')"
        />
        <div class="text-xs text-gray-500 mt-1">
          * 表示任意值<br/>
          9-18 表示9点到18点
        </div>
      </div>
      
      <div class="form-group">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          日期 (1-31)
        </label>
        <el-input
          v-model="cronData.dayOfMonth"
          placeholder="*"
          @input="emit('change')"
        />
        <div class="text-xs text-gray-500 mt-1">
          * 表示任意值<br/>
          1,15 表示每月1号和15号
        </div>
      </div>
      
      <div class="form-group">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          月份 (1-12)
        </label>
        <el-input
          v-model="cronData.month"
          placeholder="*"
          @input="emit('change')"
        />
        <div class="text-xs text-gray-500 mt-1">
          * 表示任意值<br/>
          1-6 表示上半年
        </div>
      </div>
      
      <div class="form-group">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          星期 (0-6)
        </label>
        <el-input
          v-model="cronData.dayOfWeek"
          placeholder="*"
          @input="emit('change')"
        />
        <div class="text-xs text-gray-500 mt-1">
          * 表示任意值<br/>
          0=周日, 1=周一<br/>
          MON-FRI 表示工作日
        </div>
      </div>
    </div>
    
    <!-- 帮助信息 -->
    <div class="help-info bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mt-4">
      <h4 class="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
        <i-carbon-information class="mr-1" />
        Cron表达式语法说明：
      </h4>
      <div class="text-sm text-blue-600 dark:text-blue-300 space-y-1">
        <div><strong>*</strong> - 匹配任意值</div>
        <div><strong>?</strong> - 不指定值（仅日期和星期字段支持）</div>
        <div><strong>-</strong> - 范围，如 1-5 表示 1,2,3,4,5</div>
        <div><strong>,</strong> - 列举，如 1,3,5 表示 1,3,5</div>
        <div><strong>/</strong> - 间隔，如 */5 表示每5个单位</div>
        <div><strong>L</strong> - 最后，如 L 表示月份最后一天</div>
        <div><strong>W</strong> - 工作日，如 15W 表示15号最近的工作日</div>
      </div>
    </div>
    
    <!-- 常用示例 -->
    <div class="examples mt-4">
      <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        常用示例：
      </h4>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
        <div v-for="example in examples" :key="example.cron" class="example-item">
          <el-button
            text
            size="small"
            @click="setExample(example.cron)"
            class="w-full text-left justify-start"
          >
            <div>
              <div class="font-mono text-blue-600 dark:text-blue-400">{{ example.cron }}</div>
              <div class="text-gray-500 dark:text-gray-400">{{ example.description }}</div>
            </div>
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      minute: '*',
      hour: '*',
      dayOfMonth: '*',
      month: '*',
      dayOfWeek: '*'
    })
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const cronData = ref({ ...props.modelValue })

const examples = ref([
  { cron: '0 0 * * *', description: '每天午夜执行' },
  { cron: '0 9 * * *', description: '每天上午9点执行' },
  { cron: '0 9-18 * * *', description: '每天9-18点每小时执行' },
  { cron: '0 9 * * MON-FRI', description: '工作日上午9点执行' },
  { cron: '0 9 1 * *', description: '每月1号上午9点执行' },
  { cron: '*/5 * * * *', description: '每5分钟执行' },
  { cron: '0 */2 * * *', description: '每2小时执行' },
  { cron: '0 9 1,15 * *', description: '每月1号和15号9点执行' }
])

const setExample = (cronExpression) => {
  const parts = cronExpression.split(' ')
  if (parts.length === 5) {
    cronData.value = {
      minute: parts[0],
      hour: parts[1],
      dayOfMonth: parts[2],
      month: parts[3],
      dayOfWeek: parts[4]
    }
    emit('update:modelValue', cronData.value)
    emit('change')
  }
}

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  cronData.value = { ...newValue }
}, { deep: true })

// 监听内部数据变化
watch(() => cronData.value, (newValue) => {
  emit('update:modelValue', newValue)
}, { deep: true })
</script>

<style scoped>
.advanced-builder .form-group {
  min-height: 120px;
}

.example-item :deep(.el-button) {
  height: auto;
  padding: 8px 12px;
}

.help-info {
  border: 1px solid #dbeafe;
}

.dark .help-info {
  border-color: #1e40af;
}
</style>