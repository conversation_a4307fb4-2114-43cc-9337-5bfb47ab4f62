<template>
  <div class="scheduled-tasks-page">
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">定时任务管理</h1>
      </div>
      <IconButton icon="add" variant="primary" size="md" @click="showCreateDialog = true" class="create-task-btn">
        创建任务
      </IconButton>
    </div>
    
    <!-- 任务统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-content">
          <div class="stat-icon-wrapper stat-icon--primary">
            <AppIcon name="task" class="stat-icon" />
          </div>
          <div class="stat-info">
            <p class="stat-label">总任务数</p>
            <p class="stat-value">{{ tasks.length }}</p>
          </div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-content">
          <div class="stat-icon-wrapper stat-icon--success">
            <AppIcon name="checkmark" class="stat-icon" />
          </div>
          <div class="stat-info">
            <p class="stat-label">活跃任务</p>
            <p class="stat-value">{{ activeTasks }}</p>
          </div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-content">
          <div class="stat-icon-wrapper stat-icon--warning">
            <AppIcon name="time" class="stat-icon" />
          </div>
          <div class="stat-info">
            <p class="stat-label">今日执行</p>
            <p class="stat-value">{{ todayExecutions }}</p>
          </div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-content">
          <div class="stat-icon-wrapper stat-icon--danger">
            <AppIcon name="warning" class="stat-icon" />
          </div>
          <div class="stat-info">
            <p class="stat-label">失败任务</p>
            <p class="stat-value">{{ failedExecutions }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务列表 -->
    <TaskList 
      :tasks="tasks"
      :loading="loading"
      @edit="handleEdit"
      @delete="handleDelete"
      @execute="handleManualExecute"
      @toggle-status="handleToggleStatus"
      @view-executions="handleViewExecutions"
      @refresh="loadTasks"
    />
    
    <!-- 创建/编辑任务对话框 -->
    <TaskForm
      v-model="showCreateDialog"
      :task="editingTask"
      @submit="handleSubmit"
      @cancel="handleCancel"
    />
    
    <!-- 任务执行记录对话框 -->
    <TaskExecutions
      v-model="showExecutionsDialog" 
      :task-id="selectedTaskId"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useScheduledTasks } from './composables/useScheduledTasks'
import { useTaskExecutions } from './composables/useTaskExecutions'
import TaskList from './components/TaskList.vue'
import TaskForm from './components/TaskForm.vue'
import TaskExecutions from './components/TaskExecutions.vue'
import IconButton from '@/components/common/IconButton.vue'
import AppIcon from '@/components/common/Icon.vue'
import { ElMessage } from 'element-plus'

const { 
  tasks, 
  loading, 
  loadTasks, 
  createTask, 
  updateTask, 
  deleteTask,
  executeTask,
  toggleTaskStatus 
} = useScheduledTasks()

const { executions } = useTaskExecutions()

const showCreateDialog = ref(false)
const showExecutionsDialog = ref(false)
const editingTask = ref(null)
const selectedTaskId = ref(null)

// 计算统计数据
const activeTasks = computed(() => {
  return tasks.value.filter(task => task.is_active).length
})

const todayExecutions = computed(() => {
  const today = new Date().toDateString()
  return executions.value.filter(exec => 
    new Date(exec.created_at).toDateString() === today
  ).length
})

const failedExecutions = computed(() => {
  return executions.value.filter(exec => exec.status === 'failed').length
})

onMounted(() => {
  loadTasks()
})

const handleEdit = (task) => {
  editingTask.value = { ...task }
  showCreateDialog.value = true
}

const handleDelete = async (taskId) => {
  try {
    await deleteTask(taskId)
    await loadTasks()
  } catch (error) {
    // 错误已在composable中处理
  }
}

const handleManualExecute = async (taskId) => {
  try {
    await executeTask(taskId)
    ElMessage.success('任务已提交执行')
  } catch (error) {
    // 错误已在composable中处理
  }
}

const handleToggleStatus = async (taskId, isActive) => {
  try {
    await toggleTaskStatus(taskId, isActive)
  } catch (error) {
    // 错误已在composable中处理
  }
}

const handleViewExecutions = (taskId) => {
  selectedTaskId.value = taskId
  showExecutionsDialog.value = true
}

const handleSubmit = async (taskData) => {
  try {
    if (editingTask.value) {
      await updateTask(editingTask.value.id, taskData)
    } else {
      await createTask(taskData)
    }
    await loadTasks()
    handleCancel()
  } catch (error) {
    // 错误已在composable中处理
  }
}

const handleCancel = () => {
  showCreateDialog.value = false
  editingTask.value = null
}
</script>

<style scoped lang="scss">
.scheduled-tasks-page {
  padding: 24px;
  // min-height: 100vh;
  background: var(--bg-primary);
}

// 页面头部
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  
  .header-content {
    .page-title {
      color: var(--text-primary);
      font-size: 28px;
      font-weight: 700;
      margin-bottom: 4px;
      background-clip: text;
    }
    
    .page-subtitle {
      color: var(--text-secondary);
      font-size: 16px;
      font-weight: 400;
      line-height: 1.5;
    }
  }
  
  .create-task-btn {
    // IconButton已经有完整的样式，这里不需要额外样式
  }
}

// 统计卡片网格
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

// 统计卡片
.stat-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--border-light);
    
    &::before {
      opacity: 1;
    }
    
    .stat-icon-wrapper {
      transform: scale(1.1);
      
      &.stat-icon--primary {
        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
      }
      
      &.stat-icon--success {
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
      }
      
      &.stat-icon--warning {
        box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
      }
      
      &.stat-icon--danger {
        box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
      }
    }
  }
}

// 统计卡片内容
.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

// 统计图标包装器
.stat-icon-wrapper {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  
  &.stat-icon--primary {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(99, 102, 241, 0.15));
    
    .stat-icon {
      color: var(--accent-primary);
    }
  }
  
  &.stat-icon--success {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.15));
    
    .stat-icon {
      color: #10b981;
    }
  }
  
  &.stat-icon--warning {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(217, 119, 6, 0.15));
    
    .stat-icon {
      color: #f59e0b;
    }
  }
  
  &.stat-icon--danger {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.15));
    
    .stat-icon {
      color: var(--price-up);
    }
  }
}

// 统计图标
.stat-icon {
  font-size: 24px;
  transition: color 0.3s ease;
}

// 统计信息
.stat-info {
  flex: 1;
  
  .stat-label {
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
    line-height: 1.4;
  }
  
  .stat-value {
    color: var(--text-primary);
    font-size: 28px;
    font-weight: 700;
    line-height: 1.2;
    font-variant-numeric: tabular-nums;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .scheduled-tasks-page {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    
    .create-task-btn {
      align-self: stretch;
    }
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .stat-card {
    padding: 20px;
  }
  
  .stat-content {
    gap: 12px;
  }
  
  .stat-icon-wrapper {
    width: 48px;
    height: 48px;
    
    .stat-icon {
      font-size: 20px;
    }
  }
  
  .stat-value {
    font-size: 24px;
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-card {
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:nth-child(1) { animation-delay: 0s; }
  &:nth-child(2) { animation-delay: 0.1s; }
  &:nth-child(3) { animation-delay: 0.2s; }
  &:nth-child(4) { animation-delay: 0.3s; }
}
</style>