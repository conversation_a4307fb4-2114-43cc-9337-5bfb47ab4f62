<template>
  <div class="user-management-container p-6 bg-bg-primary text-text-primary">
    <!-- 用户列表 -->
    <div class="user-list-card bg-bg-secondary rounded-lg shadow-lg">
      <div class="card-header flex items-center mb-6 gap-4 p-6">
        <h2 class="text-2xl font-semibold text-text-primary">
          <AppIcon name="user-admin" class="mr-3" />
          用户管理
        </h2>
        <div class="search-container">
          <el-input
            v-model="searchQuery"
            placeholder="搜索用户名或邮箱"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </div>
        <div id="gap" class="flex-1"></div>
        <div class="flex space-x-3">
          <IconButton 
            icon="add" 
            variant="primary" 
            size="sm"
            @click="showCreateDialog = true"
          >
            新建用户
          </IconButton>
        </div>
      </div>

      <div class="card-content px-6 pb-6">
        <CommonTable
          :data="filteredUsers"
          :columns="tableColumns"
          :loading="loading"
          :pagination="pagination"
          stripe
          @page-change="handlePageChange"
        >
          <!-- 角色列 -->
          <template #role="{ row }">
            <el-tag :type="row.is_admin ? 'danger' : 'success'">
              {{ row.is_admin ? '管理员' : '用户' }}
            </el-tag>
          </template>

          <!-- 状态列 -->
          <template #status="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'">
              {{ row.is_active ? '激活' : '禁用' }}
            </el-tag>
          </template>

          <!-- 最后登录列 -->
          <template #last_login="{ row }">
            {{ formatDate(row.last_login) }}
          </template>

          <!-- 创建时间列 -->
          <template #created_at="{ row }">
            {{ formatDate(row.created_at) }}
          </template>

          <!-- 操作列 -->
          <template #actions="{ row }">
            <div class="table-actions">
              <el-tooltip content="编辑" placement="top">
                <el-button 
                  text
                  size="small" 
                  circle
                  @click="editUser(row)"
                  class="action-btn-edit"
                >
                  <Icon name="edit" />
                </el-button>
              </el-tooltip>
              <el-tooltip :content="row.is_active ? '禁用' : '启用'" placement="top">
                <el-button 
                  text
                  size="small" 
                  circle
                  @click="toggleUserStatus(row)"
                  :class="row.is_active ? 'action-btn-warning' : 'action-btn-success'"
                >
                  <Icon :name="row.is_active ? 'locked' : 'unlocked'" />
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button 
                  text
                  size="small" 
                  circle
                  @click="deleteUser(row)"
                  :disabled="row.id === authStore.user?.id"
                  class="action-btn-delete"
                >
                  <Icon name="delete" />
                </el-button>
              </el-tooltip>
            </div>
          </template>
        </CommonTable>
      </div>
    </div>

    <!-- 创建用户对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建新用户"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="createForm.username"
            placeholder="请输入用户名"
            :disabled="createLoading"
          />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="createForm.email"
            placeholder="请输入邮箱"
            :disabled="createLoading"
          />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="createForm.password"
            type="password"
            placeholder="请输入密码"
            :disabled="createLoading"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="createForm.confirmPassword"
            type="password"
            placeholder="请再次输入密码"
            :disabled="createLoading"
            show-password
          />
        </el-form-item>
        <el-form-item label="用户角色" prop="is_admin">
          <el-radio-group v-model="createForm.is_admin" :disabled="createLoading">
            <el-radio :label="false">普通用户</el-radio>
            <el-radio :label="true">管理员</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false" :disabled="createLoading">
          取消
        </el-button>
        <el-button 
          type="primary" 
          @click="handleCreateUser"
          :loading="createLoading"
        >
          创建用户
        </el-button>
      </template>
    </el-dialog>

    <!-- 编辑用户对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑用户"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="editForm.username"
            placeholder="请输入用户名"
            :disabled="editLoading"
          />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="editForm.email"
            placeholder="请输入邮箱"
            :disabled="editLoading"
          />
        </el-form-item>
        <el-form-item label="用户角色" prop="is_admin">
          <el-radio-group v-model="editForm.is_admin" :disabled="editLoading">
            <el-radio :label="false">普通用户</el-radio>
            <el-radio :label="true">管理员</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="重置密码">
          <el-input
            v-model="editForm.newPassword"
            type="password"
            placeholder="留空则不修改密码"
            :disabled="editLoading"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditDialog = false" :disabled="editLoading">
          取消
        </el-button>
        <el-button 
          type="primary" 
          @click="handleEditUser"
          :loading="editLoading"
        >
          保存修改
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { useAuthStore } from '@/store'
import AppIcon from '@/components/common/Icon.vue'
import Icon from '@/components/common/Icon.vue'
import IconButton from '@/components/common/IconButton.vue'
import CommonTable from '@/components/common/CommonTable.vue'
import { authApi } from '@/services/api/auth'

const authStore = useAuthStore()

// 表格列配置
const tableColumns = [
  { prop: 'id', label: 'ID', width: 80 },
  { prop: 'username', label: '用户名', minWidth: 120 },
  { prop: 'email', label: '邮箱', minWidth: 160 },
  { 
    prop: 'role', 
    label: '角色', 
    width: 100, 
    slot: 'role' 
  },
  { 
    prop: 'status', 
    label: '状态', 
    width: 100, 
    slot: 'status' 
  },
  { 
    prop: 'last_login', 
    label: '最后登录', 
    width: 180, 
    slot: 'last_login' 
  },
  { 
    prop: 'created_at', 
    label: '创建时间', 
    width: 180, 
    slot: 'created_at' 
  }
]

// 数据状态
const loading = ref(false)
const users = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const searchQuery = ref('')

// 分页配置
const pagination = computed(() => ({
  total: total.value,
  currentPage: currentPage.value,
  pageSize: pageSize.value,
  pageSizes: [10, 20, 50, 100],
  layout: 'total, sizes, prev, pager, next, jumper'
}))

// 创建用户
const showCreateDialog = ref(false)
const createLoading = ref(false)
const createFormRef = ref(null)
const createForm = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: '',
  is_admin: false
})

// 编辑用户
const showEditDialog = ref(false)
const editLoading = ref(false)
const editFormRef = ref(null)
const editForm = reactive({
  id: null,
  username: '',
  email: '',
  is_admin: false,
  newPassword: ''
})

// 表单验证规则
const createRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码至少6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== createForm.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

const editRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
  ]
}

// 计算属性
const filteredUsers = computed(() => {
  if (!searchQuery.value) return users.value
  
  const query = searchQuery.value.toLowerCase()
  return users.value.filter(user => 
    user.username.toLowerCase().includes(query) ||
    user.email.toLowerCase().includes(query)
  )
})

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 加载用户列表
const loadUsers = async () => {
  loading.value = true
  try {
    const response = await authApi.getUserList(currentPage.value, pageSize.value)
    users.value = response.data || []
    total.value = response.total || 0
  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  // 前端搜索，如果需要后端搜索可以调用API
}

// 分页处理
const handlePageChange = ({ page, size }) => {
  currentPage.value = page
  pageSize.value = size
  loadUsers()
}

// 创建用户
const handleCreateUser = async () => {
  if (!createFormRef.value) return
  
  const valid = await createFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  createLoading.value = true
  try {
    await authApi.createUser({
      username: createForm.username,
      email: createForm.email,
      password: createForm.password,
      is_admin: createForm.is_admin
    })
    
    ElMessage.success('用户创建成功')
    showCreateDialog.value = false
    resetCreateForm()
    await loadUsers()
  } catch (error) {
    console.error('创建用户失败:', error)
    ElMessage.error(error.message || '创建用户失败')
  } finally {
    createLoading.value = false
  }
}

// 编辑用户
const editUser = (user) => {
  editForm.id = user.id
  editForm.username = user.username
  editForm.email = user.email
  editForm.is_admin = user.is_admin
  editForm.newPassword = ''
  showEditDialog.value = true
}

const handleEditUser = async () => {
  if (!editFormRef.value) return
  
  const valid = await editFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  editLoading.value = true
  try {
    const updateData = {
      username: editForm.username,
      email: editForm.email,
      is_admin: editForm.is_admin
    }
    
    if (editForm.newPassword) {
      updateData.password = editForm.newPassword
    }
    
    await authApi.updateUser(editForm.id, updateData)
    
    ElMessage.success('用户信息更新成功')
    showEditDialog.value = false
    await loadUsers()
  } catch (error) {
    console.error('更新用户失败:', error)
    ElMessage.error(error.message || '更新用户失败')
  } finally {
    editLoading.value = false
  }
}

// 切换用户状态
const toggleUserStatus = async (user) => {
  const action = user.is_active ? '禁用' : '启用'
  
  try {
    await ElMessageBox.confirm(
      `确定要${action}用户 ${user.username} 吗？`,
      `${action}用户`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await authApi.toggleUserStatus(user.id, !user.is_active)
    ElMessage.success(`${action}用户成功`)
    await loadUsers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}用户失败:`, error)
      ElMessage.error(`${action}用户失败`)
    }
  }
}

// 删除用户
const deleteUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 ${user.username} 吗？此操作不可恢复！`,
      '删除用户',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'error'
      }
    )
    
    await authApi.deleteUser(user.id)
    ElMessage.success('删除用户成功')
    await loadUsers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      ElMessage.error('删除用户失败')
    }
  }
}

// 重置创建表单
const resetCreateForm = () => {
  Object.assign(createForm, {
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    is_admin: false
  })
  if (createFormRef.value) {
    createFormRef.value.resetFields()
  }
}

// 生命周期
onMounted(() => {
  loadUsers()
})
</script>

<style scoped lang="scss">
.user-management-container {
  min-height: calc(100vh - 80px);
}

.user-list-card {
  overflow: hidden;
}

.search-container {
  width: 300px;
}

:deep(.el-dialog) {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
}

:deep(.el-dialog__header) {
  border-bottom: 1px solid var(--border-color);
  padding: 20px 24px 16px;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
}

:deep(.el-dialog__footer) {
  border-top: 1px solid var(--border-color);
  padding: 16px 24px 20px;
}

:deep(.el-form-item__label) {
  color: var(--text-primary);
}

:deep(.el-input__wrapper) {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  
  &:hover {
    border-color: var(--accent-primary);
  }
  
  &.is-focus {
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }
}

:deep(.el-radio-group) {
  .el-radio__input.is-checked .el-radio__inner {
    background: var(--accent-primary);
    border-color: var(--accent-primary);
  }
  
  .el-radio__input.is-checked + .el-radio__label {
    color: var(--accent-primary);
  }
}

// 操作按钮样式
.table-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;

  .el-button {
    width: 32px;
    height: 32px;
    padding: 0;
    background: transparent !important;
    border: none !important;

    &.is-circle {
      border-radius: 50%;

      .icon {
        width: 16px;
        height: 16px;
      }
    }

    &.action-btn-edit {
      color: #e6a23c;

      &:hover {
        color: #ebb563;
        background: rgba(230, 162, 60, 0.1) !important;
      }
    }

    &.action-btn-warning {
      color: #f56c6c;

      &:hover {
        color: #f78989;
        background: rgba(245, 108, 108, 0.1) !important;
      }
    }

    &.action-btn-success {
      color: #67c23a;

      &:hover {
        color: #85ce61;
        background: rgba(103, 194, 58, 0.1) !important;
      }
    }

    &.action-btn-delete {
      color: #f56c6c;

      &:hover {
        color: #f78989;
        background: rgba(245, 108, 108, 0.1) !important;
      }
      
      &:disabled {
        opacity: 0.4;
        cursor: not-allowed;
        
        &:hover {
          background: transparent !important;
          color: #f56c6c;
        }
      }
    }
  }
}
</style>