<template>
  <div class="analysis-page">
    <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
      <!-- 左侧：股票选择和基本信息 -->
      <div class="xl:col-span-1">
        <!-- 股票选择面板 -->
        <div class="card p-6 mb-6">
          <h3 class="text-lg font-semibold mb-4">股票选择</h3>
          <div class="space-y-4">
            <div>
              <label class="block text-sm text-gray-400 mb-2">股票代码</label>
              <select
                v-model="selectedStock"
                class="input-field w-full"
                @change="onStockChange"
              >
                <option value="">选择股票</option>
                <optgroup
                  v-if="combinedPopularStocks.length > 0"
                  label="热门推荐"
                >
                  <option
                    v-for="stock in combinedPopularStocks"
                    :key="stock.code"
                    :value="stock.code"
                  >
                    {{ stock.code }} {{ stock.name }}
                    <span v-if="stock.searchCount > 0"
                      >({{ stock.searchCount }}次搜索)</span
                    >
                  </option>
                </optgroup>
                <!-- 当前选中的股票如果不在列表中，单独显示 -->
                <option 
                  v-if="selectedStock && !combinedPopularStocks.some(stock => stock.code === selectedStock)"
                  :value="selectedStock"
                  :selected="true"
                >
                  {{ selectedStock }} {{ stockInfo?.name || '加载中...' }}
                </option>
              </select>
            </div>

            <div>
              <label class="block text-sm text-gray-400 mb-2">时间范围</label>
              <select
                v-model="selectedRange"
                class="input-field w-full"
                @change="onRangeChange"
              >
                <option value="30">最近30天</option>
                <option value="60">最近60天</option>
                <option value="120">最近120天</option>
                <option value="250">最近250天</option>
              </select>
            </div>
            <button
              class="btn-primary w-full flex items-center justify-center"
              @click="loadStockAnalysis"
              :disabled="!selectedStock || loading"
            >
              <AppIcon name="chart-line" class="mr-2" />
              {{ loading ? "分析中..." : "开始分析" }}
            </button>
          </div>
        </div>

        <!-- 股票基本信息 -->
        <div v-if="stockInfo" class="card p-6">
          <h3 class="text-lg font-semibold mb-4">基本信息</h3>
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-gray-400">股票名称</span>
              <span>{{ stockInfo.name }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-400">股票代码</span>
              <span>{{ stockInfo.code }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-400">当前价格</span>
              <span
                :class="
                  stockInfo.changePercent >= 0
                    ? 'text-red-400'
                    : 'text-green-400'
                "
              >
                ¥{{ stockInfo.price }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-400">涨跌幅</span>
              <span
                :class="
                  stockInfo.changePercent >= 0
                    ? 'text-red-400'
                    : 'text-green-400'
                "
              >
                {{ stockInfo.changePercent >= 0 ? "+" : ""
                }}{{ stockInfo.changePercent }}%
              </span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-400">成交量</span>
              <span>{{ stockInfo.volume }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-400">市值</span>
              <span>{{ stockInfo.marketCap }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：图表区域 -->
      <div class="xl:col-span-3">
        <!-- K线图表 -->
        <div class="card p-6 mb-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">K线图表</h3>
            <div class="flex space-x-2">
              <button
                class="px-3 py-1 text-sm rounded"
                :class="
                  chartOverlay === 'none'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-400 hover:text-white'
                "
                @click="setChartOverlay('none')"
              >
                主图
              </button>
              <button
                class="px-3 py-1 text-sm rounded"
                :class="
                  chartOverlay === 'ma'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-400 hover:text-white'
                "
                @click="setChartOverlay('ma')"
              >
                均线
              </button>
              <button
                class="px-3 py-1 text-sm rounded"
                :class="
                  chartOverlay === 'bollinger'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-400 hover:text-white'
                "
                @click="setChartOverlay('bollinger')"
              >
                布林带
              </button>
            </div>
          </div>
          <ChartContainer
            ref="klineChartContainer"
            height="500px"
            :loading="loading"
            :error="error"
            :has-data="!!chartData?.kline && !!selectedStock"
            empty-text="请选择股票开始分析"
            loading-text="K线图表加载中..."
            empty-icon="i-carbon-chart-candlestick text-blue-400"
            @retry="loadStockAnalysis"
          />
        </div>

        <!-- 技术指标图表 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="card p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold">MACD指标</h3>
              <button
                class="text-sm text-blue-400 hover:text-blue-300"
                @click="showIndicatorSettings('macd')"
              >
                参数设置
              </button>
            </div>
            <ChartContainer
              ref="macdChartContainer"
              height="300px"
              :loading="loading"
              :error="error"
              :has-data="!!chartData?.macd && !!selectedStock"
              empty-text="请选择股票开始分析"
              loading-text="MACD指标加载中..."
              empty-icon="i-carbon-chart-line text-purple-400"
              @retry="loadStockAnalysis"
            />
          </div>

          <div class="card p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold">成交量</h3>
              <button class="text-sm text-blue-400 hover:text-blue-300">
                详细分析
              </button>
            </div>
            <ChartContainer
              ref="volumeChartContainer"
              height="300px"
              :loading="loading"
              :error="error"
              :has-data="!!chartData?.volume && !!selectedStock"
              empty-text="请选择股票开始分析"
              loading-text="成交量图表加载中..."
              empty-icon="i-carbon-chart-column text-green-400"
              @retry="loadStockAnalysis"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 指标参数设置对话框 -->
    <IndicatorSettings
      v-model="showSettingsDialog"
      :indicator-type="currentIndicatorType"
      :current-settings="currentIndicatorSettings"
      @confirm="handleIndicatorSettingsConfirm"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed, watch } from "vue";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import AppIcon from "@/components/common/Icon.vue";
import ChartContainer from "@/components/analysis/ChartContainer.vue";
import IndicatorSettings from "@/components/analysis/IndicatorSettings.vue";
import { searchStatsManager } from "@/utils/searchStats";
import { StockDataManager } from "@/utils/stockDataManager";
import { 
  KlineChartRenderer, 
  MacdChartRenderer, 
  VolumeChartRenderer 
} from "@/utils/chartRenderers";

const route = useRoute();

// 基础状态
const selectedStock = ref("");
const selectedPeriod = ref("D");
const selectedRange = ref("30");
const chartOverlay = ref("none");

// 数据管理器
const dataManager = new StockDataManager();

// 响应式数据
const loading = ref(false);
const error = ref(null);
const chartData = ref(null);
const stockInfo = ref(null);

// 封装数据管理器方法
const updateDataStates = () => {
  loading.value = dataManager.isLoading();
  error.value = dataManager.getError();
  chartData.value = dataManager.getChartData();
  stockInfo.value = dataManager.getStockInfo();
};

// 热门股票
const hotStocks = ref([]);
const popularStocks = computed(() => searchStatsManager.getPopularStocks(5));
const combinedPopularStocks = computed(() => {
  const searchBased = popularStocks.value;
  const backendBased = hotStocks.value;
  const combined = [...searchBased];
  const existingCodes = new Set(searchBased.map((stock) => stock.code));

  for (const stock of backendBased) {
    if (!existingCodes.has(stock.code)) {
      combined.push({
        code: stock.code,
        name: stock.name,
        industry: stock.industry || "未分类",
        searchCount: 0,
      });
      if (combined.length >= 10) break;
    }
  }
  return combined;
});

// 图表容器和渲染器
const klineChartContainer = ref(null);
const macdChartContainer = ref(null);
const volumeChartContainer = ref(null);

let klineRenderer = null;
let macdRenderer = null;
let volumeRenderer = null;

// 指标设置
const showSettingsDialog = ref(false);
const currentIndicatorType = ref("macd");
const currentIndicatorSettings = ref({});

// 获取指标设置
const getIndicatorSettings = (indicatorType) => {
  const savedSettings = localStorage.getItem(`indicator_settings_${indicatorType}`);
  return savedSettings ? JSON.parse(savedSettings) : {};
};

// 事件处理
const onStockChange = (stockCode) => {
  if (typeof stockCode === 'object' && stockCode.target) {
    stockCode = stockCode.target.value;
  }
  
  if (!stockCode) return;
  
  selectedStock.value = stockCode;
  
  // 记录搜索统计
  const stockInfo = combinedPopularStocks.value.find(stock => stock.code === stockCode);
  if (stockInfo) {
    searchStatsManager.incrementSearchCount(
      stockInfo.code, 
      stockInfo.name, 
      stockInfo.industry || "未分类"
    );
  }

  if (selectedStock.value) {
    loadStockAnalysis();
  }
};

const onRangeChange = () => {
  if (selectedStock.value) {
    loadStockAnalysis();
  }
};

const setChartOverlay = (overlay) => {
  chartOverlay.value = overlay;
  renderKlineChart();
};

const showIndicatorSettings = (indicatorType) => {
  currentIndicatorType.value = indicatorType;
  currentIndicatorSettings.value = getIndicatorSettings(indicatorType);
  showSettingsDialog.value = true;
};

const handleIndicatorSettingsConfirm = (data) => {
  localStorage.setItem(`indicator_settings_${data.type}`, JSON.stringify(data.settings));
  
  if (selectedStock.value) {
    if (['ma', 'bollinger'].includes(data.type)) {
      renderKlineChart();
    } else {
      loadStockAnalysis();
    }
  }
};

// 加载股票分析数据
const loadStockAnalysis = async () => {
  if (!selectedStock.value) return;

  try {
    console.log("开始加载股票分析数据", selectedStock.value);
    loading.value = true;
    error.value = null;
    
    await dataManager.loadStockAnalysis(
      selectedStock.value, 
      selectedPeriod.value, 
      selectedRange.value
    );
    
    // 更新响应式数据
    updateDataStates();
    
    console.log("数据加载完成，chartData:", chartData.value);
    console.log("图表容器状态:", {
      kline: !!klineChartContainer.value,
      macd: !!macdChartContainer.value,
      volume: !!volumeChartContainer.value
    });
    
    await nextTick();
    console.log("nextTick 后，开始渲染图表");
    await renderCharts();
  } catch (err) {
    console.error("加载股票分析数据失败:", err);
    updateDataStates();
    ElMessage.error(error.value || "加载失败");
  }
};

// 渲染图表
const renderCharts = async () => {
  if (!chartData.value) return;

  try {
    // 添加短暂延时确保DOM完全渲染
    await new Promise(resolve => setTimeout(resolve, 100));
    
    await Promise.all([
      renderKlineChart(),
      renderMacdChart(),
      renderVolumeChart(),
    ]);
  } catch (err) {
    console.error("图表渲染失败:", err);
    ElMessage.error("图表渲染失败");
  }
};

const renderKlineChart = async () => {
  console.log("准备渲染K线图表:", {
    containerExists: !!klineChartContainer.value,
    hasData: !!chartData.value?.kline,
    data: chartData.value?.kline
  });
  
  if (!klineChartContainer.value) {
    console.warn("K线图表容器未找到");
    return;
  }
  
  if (!chartData.value?.kline) {
    console.warn("K线数据为空");
    return;
  }

  try {
    if (!klineRenderer) {
      console.log("创建K线渲染器");
      klineRenderer = new KlineChartRenderer(klineChartContainer.value, getIndicatorSettings);
    }
    console.log("开始渲染K线图表", chartData.value.kline);
    klineRenderer.render(chartData.value.kline, chartOverlay.value);
    console.log("K线图表渲染完成");
  } catch (err) {
    console.error("K线图表渲染失败:", err);
  }
};

const renderMacdChart = async () => {
  if (!macdChartContainer.value) {
    console.warn("MACD图表容器未找到");
    return;
  }
  
  if (!chartData.value?.macd) {
    console.warn("MACD数据为空");
    return;
  }

  try {
    if (!macdRenderer) {
      macdRenderer = new MacdChartRenderer(macdChartContainer.value);
    }
    console.log("开始渲染MACD图表", chartData.value.macd);
    macdRenderer.render(chartData.value.macd);
    console.log("MACD图表渲染完成");
  } catch (err) {
    console.error("MACD图表渲染失败:", err);
  }
};

const renderVolumeChart = async () => {
  if (!volumeChartContainer.value) {
    console.warn("成交量图表容器未找到");
    return;
  }
  
  if (!chartData.value?.volume) {
    console.warn("成交量数据为空");
    return;
  }

  try {
    if (!volumeRenderer) {
      volumeRenderer = new VolumeChartRenderer(volumeChartContainer.value);
    }
    console.log("开始渲染成交量图表", chartData.value.volume);
    volumeRenderer.render(chartData.value.volume);
    console.log("成交量图表渲染完成");
  } catch (err) {
    console.error("成交量图表渲染失败:", err);
  }
};

// 处理窗口大小变化
const handleResize = () => {
  klineRenderer?.resize();
  macdRenderer?.resize();
  volumeRenderer?.resize();
};

// 通用的股票加载函数
const loadStockByCode = async (stockCode, stockName = null, industry = null) => {
  if (!stockCode) return;
  
  selectedStock.value = stockCode;
  
  // 如果传入了股票名称，或者股票不在热门列表中，确保能通过搜索统计正确处理
  if (stockName) {
    searchStatsManager.incrementSearchCount(stockCode, stockName, industry || "未分类");
  } else {
    // 如果没有传入股票名称，尝试从热门股票列表中查找
    const foundStock = combinedPopularStocks.value.find(stock => stock.code === stockCode);
    if (foundStock) {
      searchStatsManager.incrementSearchCount(stockCode, foundStock.name, foundStock.industry || "未分类");
    } else {
      // 如果都找不到，至少记录代码
      searchStatsManager.incrementSearchCount(stockCode, stockCode, "未分类");
    }
  }
  
  await loadStockAnalysis();
};

// 监听路由变化
watch(
  () => route.query,
  async (newQuery, oldQuery) => {
    if (newQuery.stock && newQuery.stock !== oldQuery?.stock) {
      await loadStockByCode(newQuery.stock, newQuery.name, newQuery.industry);
    }
  },
  { immediate: true, deep: true } // 改为immediate: true，确保页面加载时就触发
);

// 初始化
onMounted(async () => {
  // 先加载热门股票，确保combinedPopularStocks有数据
  hotStocks.value = await dataManager.loadHotStocks();
  
  // 初始化响应式数据
  updateDataStates();

  // 等待一个tick，确保DOM更新完成
  await nextTick();

  // 检查路由参数，注意这里不再需要单独处理，因为watch已经设置了immediate: true
  // 但为了确保兼容性，我们保留这个检查
  if (route.query.stock && !selectedStock.value) {
    await loadStockByCode(route.query.stock, route.query.name, route.query.industry);
  }

  window.addEventListener("resize", handleResize);
});

// 清理
onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
  klineRenderer?.dispose();
  macdRenderer?.dispose();
  volumeRenderer?.dispose();
});
</script>

<style scoped lang="scss">
.analysis-page {
  max-width: 1400px;
  margin: 0 auto;
}

.chart-container {
  position: relative;
  background: linear-gradient(
    135deg,
    var(--bg-tertiary) 0%,
    var(--bg-secondary) 100%
  );
  border: 1px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    border-color: var(--accent-primary);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
  }
}
</style>

