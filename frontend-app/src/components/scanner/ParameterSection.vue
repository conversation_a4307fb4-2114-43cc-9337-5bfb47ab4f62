<template>
  <div class="parameter-section">
    <div class="section-title">
      <Icon :name="getIndicatorIcon(indicator)" class="mr-2 text-primary" />
      <span>{{ getIndicator<PERSON>abel(indicator) }}</span>
    </div>
    
    <div class="parameter-grid">
      <!-- KDJ参数 -->
      <template v-if="indicator === 'kdj'">
        <div class="parameter-item">
          <label>周期 (n)</label>
          <el-input-number
            :model-value="parameters.n"
            @update:model-value="updateParameter('n', $event)"
            :min="5"
            :max="100"
            size="small"
            controls-position="right"
          />
        </div>
        <div class="parameter-item">
          <label>K值平滑 (m1)</label>
          <el-input-number
            :model-value="parameters.m1"
            @update:model-value="updateParameter('m1', $event)"
            :min="1"
            :max="20"
            size="small"
            controls-position="right"
          />
        </div>
        <div class="parameter-item">
          <label>D值平滑 (m2)</label>
          <el-input-number
            :model-value="parameters.m2"
            @update:model-value="updateParameter('m2', $event)"
            :min="1"
            :max="20"
            size="small"
            controls-position="right"
          />
        </div>
      </template>
      
      <!-- 成交量压力参数 -->
      <template v-else-if="indicator === 'volume_pressure'">
        <div class="parameter-item">
          <label>EMA周期</label>
          <el-input-number
            :model-value="parameters.ema_period"
            @update:model-value="updateParameter('ema_period', $event)"
            :min="3"
            :max="50"
            size="small"
            controls-position="right"
          />
        </div>
      </template>
      
      <!-- 布林带参数 -->
      <template v-else-if="indicator === 'bollinger'">
        <div class="parameter-item">
          <label>窗口大小</label>
          <el-input-number
            :model-value="parameters.window"
            @update:model-value="updateParameter('window', $event)"
            :min="5"
            :max="100"
            size="small"
            controls-position="right"
          />
        </div>
        <div class="parameter-item">
          <label>标准差倍数</label>
          <el-input-number
            :model-value="parameters.std_dev"
            @update:model-value="updateParameter('std_dev', $event)"
            :min="0.5"
            :max="5"
            :step="0.1"
            :precision="1"
            size="small"
            controls-position="right"
          />
        </div>
      </template>
      
      <!-- MACD参数 -->
      <template v-else-if="indicator === 'macd'">
        <div class="parameter-item">
          <label>快线周期</label>
          <el-input-number
            :model-value="parameters.fast_period"
            @update:model-value="updateParameter('fast_period', $event)"
            :min="5"
            :max="50"
            size="small"
            controls-position="right"
          />
        </div>
        <div class="parameter-item">
          <label>慢线周期</label>
          <el-input-number
            :model-value="parameters.slow_period"
            @update:model-value="updateParameter('slow_period', $event)"
            :min="10"
            :max="100"
            size="small"
            controls-position="right"
          />
        </div>
        <div class="parameter-item">
          <label>信号线周期</label>
          <el-input-number
            :model-value="parameters.signal_period"
            @update:model-value="updateParameter('signal_period', $event)"
            :min="3"
            :max="30"
            size="small"
            controls-position="right"
          />
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import Icon from '@/components/common/Icon.vue'

const props = defineProps({
  indicator: {
    type: String,
    required: true
  },
  parameters: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['update'])

// 获取指标图标
const getIndicatorIcon = (indicator) => {
  const iconMap = {
    kdj: 'chart-line',
    volume_pressure: 'chart-column',
    bollinger: 'chart-area',
    macd: 'chart-line-data'
  }
  return iconMap[indicator] || 'chart-line'
}

// 获取指标标签
const getIndicatorLabel = (indicator) => {
  const labelMap = {
    kdj: 'KDJ 指标参数',
    volume_pressure: '成交量压力参数',
    bollinger: '布林带参数',
    macd: 'MACD 指标参数'
  }
  return labelMap[indicator] || indicator
}

// 更新参数
const updateParameter = (paramName, value) => {
  emit('update', props.indicator, {
    ...props.parameters,
    [paramName]: value
  })
}
</script>

<style scoped lang="scss">
.parameter-section {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }

  .section-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-primary);
  }

  .parameter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;

    .parameter-item {
      display: flex;
      flex-direction: column;
      gap: 8px;

      label {
        font-size: 14px;
        font-weight: 500;
        color: var(--text-secondary);
      }

      :deep(.el-input-number) {
        width: 100%;
        
        .el-input__wrapper {
          border-radius: 6px;
        }
      }
    }
  }
}
</style>