<template>
  <div class="scan-mode-selector">
    <!-- 第一层：扫描复杂度选择 -->
    <div class="mode-selection-layer mb-6">
      <!-- <label class="block text-sm text-gray-400 mb-3">选择扫描模式</label> -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <!-- 传统扫描 -->
        <div 
          class="mode-card" 
          :class="{ 'mode-card--active': scanMode === 'traditional' }"
          @click="selectScanMode('traditional')"
        >
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-3">
              <div class="radio-button" :class="{ 'radio-button--active': scanMode === 'traditional' }">
                <div class="radio-dot" :class="{ 'radio-dot--active': scanMode === 'traditional' }"></div>
              </div>
              <h4 class="text-lg font-semibold">传统扫描</h4>
            </div>
            <div class="mode-badge mode-badge--basic">基础</div>
          </div>
          <p class="text-sm text-gray-400 mb-3">单周期基础指标快速筛选</p>
          <div class="text-xs text-gray-500">
            <div class="flex items-center space-x-2 mb-1">
              <Icon name="chart-line" class="text-green-400" />
              <span>仅日线指标分析</span>
            </div>
            <div class="flex items-center space-x-2">
              <Icon name="lightning" class="text-yellow-400" />
              <span>速度最快，适合初筛</span>
            </div>
          </div>
        </div>

        <!-- 多周期扫描 -->
        <div 
          class="mode-card" 
          :class="{ 'mode-card--active': scanMode === 'multi_period' }"
          @click="selectScanMode('multi_period')"
        >
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-3">
              <div class="radio-button" :class="{ 'radio-button--active': scanMode === 'multi_period' }">
                <div class="radio-dot" :class="{ 'radio-dot--active': scanMode === 'multi_period' }"></div>
              </div>
              <h4 class="text-lg font-semibold">多周期扫描</h4>
            </div>
            <div class="mode-badge mode-badge--advanced">高级</div>
          </div>
          <p class="text-sm text-gray-400 mb-3">主周期完整分析，辅助周期上升通道确认</p>
          <div class="text-xs text-gray-500">
            <div class="flex items-center space-x-2 mb-1">
              <Icon name="layers" class="text-purple-400" />
              <span>智能分层筛选策略</span>
            </div>
            <div class="flex items-center space-x-2">
              <Icon name="trending-up" class="text-green-400" />
              <span>主周期完整计算+辅助周期趋势确认</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第二层：多周期策略选择（仅在选择多周期扫描时显示）-->
    <div v-show="scanMode === 'multi_period'" class="strategy-selection-layer mb-6">
      <label class="block text-sm text-gray-400 mb-3">选择多周期实现策略</label>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <!-- 并行计算 -->
        <div 
          class="strategy-card" 
          :class="{ 'strategy-card--active': scanStrategy === 'parallel' }"
          @click="selectScanStrategy('parallel')"
        >
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-3">
              <div class="radio-button radio-button--purple" :class="{ 'radio-button--active': scanStrategy === 'parallel' }">
                <div class="radio-dot" :class="{ 'radio-dot--active': scanStrategy === 'parallel' }"></div>
              </div>
              <h4 class="text-lg font-semibold">并行计算</h4>
            </div>
            <div class="strategy-badge strategy-badge--comprehensive">全面分析</div>
          </div>
          <p class="text-sm text-gray-400 mb-3">同时分析所有周期，主周期完整指标</p>
          <div class="text-xs text-gray-500">
            <div class="flex items-center space-x-2 mb-1">
              <Icon name="connection-signal" class="text-orange-400" />
              <span>并行处理所有周期</span>
            </div>
            <div class="flex items-center space-x-2 mb-1">
              <Icon name="chart-column" class="text-blue-400" />
              <span>主周期全指标+辅助周期趋势</span>
            </div>
            <div class="flex items-center space-x-2">
              <Icon name="lightning" class="text-yellow-400" />
              <span>速度快，结果全面</span>
            </div>
          </div>
        </div>

        <!-- 多层级复筛 -->
        <div 
          class="strategy-card" 
          :class="{ 'strategy-card--active': scanStrategy === 'cascade' }"
          @click="selectScanStrategy('cascade')"
        >
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-3">
              <div class="radio-button radio-button--purple" :class="{ 'radio-button--active': scanStrategy === 'cascade' }">
                <div class="radio-dot" :class="{ 'radio-dot--active': scanStrategy === 'cascade' }"></div>
              </div>
              <h4 class="text-lg font-semibold">多层级复筛</h4>
            </div>
            <div class="strategy-badge strategy-badge--optimized">性能优化</div>
          </div>
          <p class="text-sm text-gray-400 mb-3">先筛主周期，通过后验证辅助周期</p>
          <div class="text-xs text-gray-500">
            <div class="flex items-center space-x-2 mb-1">
              <Icon name="data-share" class="text-blue-400" />
              <span>分层过滤，逐步筛选</span>
            </div>
            <div class="flex items-center space-x-2 mb-1">
              <Icon name="rocket" class="text-green-400" />
              <span>主周期完整+辅助周期确认</span>
            </div>
            <div class="flex items-center space-x-2">
              <Icon name="arrow-up" class="text-green-400" />
              <span>性能优化，精确筛选</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 周期选择 -->
    <div v-show="scanMode === 'multi_period'" class="period-selection mb-6">
      <label class="block text-sm text-gray-400 mb-3">选择扫描周期</label>
      <div class="flex flex-wrap gap-3">
        <label v-for="period in periodOptions" :key="period.value" class="period-checkbox">
          <input 
            type="checkbox" 
            :value="period.value" 
            v-model="selectedPeriods"
            class="sr-only"
          />
          <div class="period-item" :class="{ 'period-item--active': selectedPeriods.includes(period.value) }">
            <Icon :name="period.icon" class="text-sm mr-2" />
            <span>{{ period.label }}</span>
          </div>
        </label>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'
import Icon from '@/components/common/Icon.vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      scanMode: 'traditional',
      scanStrategy: 'parallel',
      periods: ['d']
    })
  }
})

const emit = defineEmits(['update:modelValue'])

// 扫描模式
const scanMode = ref(props.modelValue.scanMode || 'traditional')
const scanStrategy = ref(props.modelValue.scanStrategy || 'parallel')
const selectedPeriods = ref([...props.modelValue.periods] || ['d'])

// 周期选项
const periodOptions = [
  { value: 'd', label: '日线', icon: 'calendar' },
  { value: 'w', label: '周线', icon: 'calendar' },
  { value: 'm', label: '月线', icon: 'calendar' }
]

// 选择扫描模式
const selectScanMode = (mode) => {
  scanMode.value = mode
  if (mode === 'traditional') {
    selectedPeriods.value = ['d']
  } else {
    // 多周期模式默认选择日线和周线
    if (selectedPeriods.value.length === 1 && selectedPeriods.value[0] === 'd') {
      selectedPeriods.value = ['d', 'w']
    }
  }
  emitValue()
}

// 选择扫描策略
const selectScanStrategy = (strategy) => {
  scanStrategy.value = strategy
  emitValue()
}

// 发送值变化
const emitValue = () => {
  emit('update:modelValue', {
    scanMode: scanMode.value,
    scanStrategy: scanStrategy.value,
    periods: [...selectedPeriods.value]
  })
}

// 标志位防止循环更新
const isUpdatingFromProps = ref(false)

// 监听周期选择变化
watch(selectedPeriods, () => {
  // 防止props更新时触发emit
  if (isUpdatingFromProps.value) return
  
  // 确保至少选择一个周期
  if (selectedPeriods.value.length === 0) {
    selectedPeriods.value = ['d']
  }
  emitValue()
}, { deep: true })

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  // 设置标志位防止循环
  isUpdatingFromProps.value = true
  
  scanMode.value = newVal.scanMode || 'traditional'
  scanStrategy.value = newVal.scanStrategy || 'parallel'
  selectedPeriods.value = [...(newVal.periods || ['d'])]
  
  // 下一个tick后重置标志位
  nextTick(() => {
    isUpdatingFromProps.value = false
  })
}, { deep: true, immediate: true })
</script>

<style scoped lang="scss">
.scan-mode-selector {
  .mode-card, .strategy-card {
    padding: 1rem;
    background: var(--card-bg-primary);
    border: 1px solid var(--glass-border);
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: rgba(59, 130, 246, 0.5);
      background: var(--card-bg-secondary);
      transform: translateY(-1px);
    }
    
    &--active {
      border-color: var(--accent-primary);
      background: var(--card-bg-tertiary);
    }
  }
  
  .strategy-card {
    &:hover {
      border-color: rgba(147, 51, 234, 0.5);
    }
    
    &--active {
      border-color: #9333ea;
      background: var(--card-bg-quaternary);
    }
  }
  
  .radio-button {
    width: 1rem;
    height: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    
    &--active {
      border-color: var(--accent-primary);
    }
    
    &--purple.radio-button--active {
      border-color: #9333ea;
    }
    
    .radio-dot {
      width: 0.5rem;
      height: 0.5rem;
      background: var(--accent-primary);
      border-radius: 50%;
      opacity: 0;
      transition: opacity 0.2s ease;
      
      &--active {
        opacity: 1;
      }
    }
    
    &--purple .radio-dot {
      background: #9333ea;
    }
  }
  
  .mode-badge, .strategy-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    
    &--basic {
      background: rgba(34, 197, 94, 0.2);
      color: #16a34a;
    }
    
    &--advanced {
      background: rgba(147, 51, 234, 0.2);
      color: #a855f7;
    }
    
    &--comprehensive {
      background: rgba(245, 158, 11, 0.2);
      color: #d97706;
    }
    
    &--optimized {
      background: rgba(59, 130, 246, 0.2);
      color: #3b82f6;
    }
  }
  
  .period-checkbox {
    cursor: pointer;
    
    .period-item {
      display: flex;
      align-items: center;
      padding: 0.5rem 0.75rem;
      background: var(--card-bg-primary);
      border: 1px solid var(--glass-border);
      border-radius: 0.375rem;
      transition: all 0.2s ease;
      font-size: 0.875rem;
      
      &:hover {
        border-color: rgba(59, 130, 246, 0.5);
        background: var(--card-bg-secondary);
      }
      
      &--active {
        border-color: var(--accent-primary);
        background: var(--card-bg-tertiary);
        color: var(--accent-primary);
      }
    }
  }
}
</style>