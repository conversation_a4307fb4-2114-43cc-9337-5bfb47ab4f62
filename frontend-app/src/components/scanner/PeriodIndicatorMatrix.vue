<template>
  <div class="period-indicator-matrix" :class="{ 'period-indicator-matrix--small': type === 'small' }">
    <!-- 表头 -->
    <div class="matrix-header">
      <div class="corner-cell">指标</div>
      <div
        v-for="(period, index) in periodOptions"
        :key="period.value"
        class="period-cell"
      >
        <div class="period-header">
          <div class="flex items-center justify-center">
            <Icon v-if="type !== 'small'" :name="period.icon" class="text-sm mr-1" />
            <span>{{ period.label }}</span>
            <span 
              v-if="scanMode === 'multi_period' && selectedPeriods.length > 1"
              :class="[
                'role-badge ml-1',
                index === 0 ? 'role-badge--primary' : 'role-badge--secondary'
              ]"
            >
              {{ index === 0 ? '主' : '辅' }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 指标行 -->
    <div
      v-for="indicator in indicatorOptions"
      :key="indicator.value"
      class="matrix-row"
    >
      <!-- 指标名称列 -->
      <div class="indicator-cell">
        <Icon
          v-if="type !== 'small'"
          :name="indicator.icon"
          class="text-lg mr-2"
          :class="getIndicatorIconClass(indicator.value)"
        />
        <div>
          <div class="font-medium">{{ indicator.label }}</div>
          <div v-if="type !== 'small'" class="text-xs text-gray-500">{{ indicator.desc }}</div>
        </div>
      </div>

      <!-- 周期选择列 -->
      <div
        v-for="(period, index) in periodOptions"
        :key="period.value"
        class="checkbox-cell"
      >
        <!-- 主周期：正常的指标选择 -->
        <template v-if="index === 0 || scanMode === 'traditional'">
          <label class="checkbox-wrapper">
            <input
              type="checkbox"
              :checked="
                isIndicatorSelectedForPeriod(indicator.value, period.value)
              "
              @change="
                toggleIndicatorForPeriod(
                  indicator.value,
                  period.value,
                  $event.target.checked
                )
              "
              :disabled="
                disabled || (scanMode === 'traditional' && period.value !== 'd')
              "
              class="sr-only"
            />
            <div
              class="custom-checkbox"
              :class="{
                'custom-checkbox--checked': isIndicatorSelectedForPeriod(
                  indicator.value,
                  period.value
                ),
                'custom-checkbox--disabled':
                  disabled ||
                  (scanMode === 'traditional' && period.value !== 'd'),
              }"
            >
              <Icon name="checkmark" class="check-icon" />
            </div>
          </label>
        </template>

        <!-- 辅助周期：只有KDJ可选，其他显示为不适用 -->
        <template v-else>
          <div v-if="indicator.value === 'kdj'" class="secondary-indicator">
            <Icon v-if="type !== 'small'" name="chart-line-smooth" class="text-green-400 text-sm" />
            <span class="text-xs text-green-400">{{ type === 'small' ? '可用' : '上升通道' }}</span>
          </div>
          <div v-else class="unavailable-indicator">
            <Icon v-if="type !== 'small'" name="subtract-alt" class="text-gray-500 text-sm" />
            <span class="text-xs text-gray-500">{{ type === 'small' ? '-' : '不适用' }}</span>
          </div>
        </template>
      </div>
    </div>

    <!-- 说明文字 -->
    <div class="matrix-note">
      <Icon v-if="type !== 'small'" name="information" class="text-blue-400 mr-2" />
      <span class="text-sm text-gray-400">
        <template v-if="scanMode === 'traditional'">
          传统模式下只能选择日线指标
        </template>
        <template v-else-if="selectedPeriods.length === 1">
          单周期模式：完整指标分析
        </template>
        <template v-else>
          多周期模式：主周期完整分析，辅助周期仅KDJ上升通道确认
        </template>
      </span>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";
import Icon from "@/components/common/Icon.vue";

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({}),
  },
  scanMode: {
    type: String,
    default: "traditional",
  },
  selectedPeriods: {
    type: Array,
    default: () => ["d"],
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: "normal", // normal | small
  },
});

const emit = defineEmits(["update:modelValue"]);

// 指标选项
const indicatorOptions = [
  {
    value: "kdj",
    label: "KDJ指标",
    desc: "动量振荡器",
    icon: "chart-line",
  },
  {
    value: "volume_pressure",
    label: "成交量压力",
    desc: "成交量分析",
    icon: "chart-column",
  },
  {
    value: "macd",
    label: "MACD指标",
    desc: "趋势指标",
    icon: "chart-line-data",
  },
  {
    value: "bollinger",
    label: "布林带",
    desc: "波动率指标",
    icon: "chart-area",
  },
];

// 周期选项
const periodOptions = computed(() => {
  const allPeriods = [
    { value: "d", label: "日线", icon: "calendar" },
    { value: "w", label: "周线", icon: "calendar" },
    { value: "m", label: "月线", icon: "calendar" },
  ];

  // 根据选择的周期过滤
  return allPeriods.filter((period) =>
    props.selectedPeriods.includes(period.value)
  );
});

// 判断指标在指定周期是否被选中
const isIndicatorSelectedForPeriod = (indicator, period) => {
  return props.modelValue[period]?.includes(indicator) || false;
};

// 切换指标在指定周期的选择状态
const toggleIndicatorForPeriod = (indicator, period, checked) => {
  const newValue = { ...props.modelValue };

  if (!newValue[period]) {
    newValue[period] = [];
  }

  if (checked) {
    if (!newValue[period].includes(indicator)) {
      newValue[period] = [...newValue[period], indicator];
    }
  } else {
    newValue[period] = newValue[period].filter((ind) => ind !== indicator);
  }

  emit("update:modelValue", newValue);
};

// 获取指标图标样式
const getIndicatorIconClass = (indicator) => {
  // 检查该指标是否在任何周期被选中
  const isSelected = periodOptions.value.some((period) =>
    isIndicatorSelectedForPeriod(indicator, period.value)
  );
  return isSelected ? "text-primary" : "text-gray-400";
};
</script>

<style scoped lang="scss">
.period-indicator-matrix {
  .matrix-header {
    display: grid;
    grid-template-columns: 200px repeat(auto-fit, minmax(100px, 1fr));
    gap: 1px;
    margin-bottom: 1px;

    .corner-cell {
      background: rgba(255, 255, 255, 0.05);
      padding: 0.75rem;
      font-weight: 600;
      text-align: center;
      border-radius: 0.375rem 0 0 0;
      color: var(--text-secondary);
    }

    .period-cell {
      background: rgba(255, 255, 255, 0.05);
      padding: 0.75rem;
      font-weight: 600;
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: var(--text-secondary);

      &:last-child {
        border-radius: 0 0.375rem 0 0;
      }

      .period-header {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
      }

      .role-badge {
        padding: 0.125rem 0.5rem;
        border-radius: 9999px;
        font-size: 0.625rem;
        font-weight: 600;
        letter-spacing: 0.025em;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 24px;
        height: 18px;
        transition: all 0.2s ease;

        &--primary {
          background: linear-gradient(135deg, #3b82f6, #1d4ed8);
          color: white;
          box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
        }

        &--secondary {
          background: linear-gradient(135deg, #6b7280, #4b5563);
          color: white;
          box-shadow: 0 2px 4px rgba(107, 114, 128, 0.3);
        }
      }
    }
  }

  .matrix-row {
    display: grid;
    grid-template-columns: 200px repeat(auto-fit, minmax(100px, 1fr));
    gap: 1px;
    margin-bottom: 1px;

    .indicator-cell {
      background: rgba(255, 255, 255, 0.02);
      padding: 0.75rem;
      display: flex;
      align-items: center;
      border-radius: 0 0 0 0.375rem;

      &:last-of-type {
        border-radius: 0 0 0.375rem 0.375rem;
      }
    }

    .checkbox-cell {
      background: rgba(255, 255, 255, 0.02);
      padding: 0.75rem;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.2s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.05);
      }

      &:last-child {
        border-radius: 0 0.375rem 0 0;
      }
    }

    &:last-child {
      .indicator-cell {
        border-radius: 0 0 0 0.375rem;
      }

      .checkbox-cell:last-child {
        border-radius: 0 0 0.375rem 0;
      }
    }
  }

  .checkbox-wrapper {
    cursor: pointer;

    .custom-checkbox {
      width: 1.25rem;
      height: 1.25rem;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: 0.25rem;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      position: relative;

      .check-icon {
        font-size: 0.875rem;
        color: white;
        opacity: 0;
        transition: opacity 0.2s ease;
      }

      &--checked {
        background: var(--accent-primary);
        border-color: var(--accent-primary);

        .check-icon {
          opacity: 1;
        }
      }

      &--disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      &:not(&--disabled):hover {
        border-color: var(--accent-primary);
        transform: scale(1.05);
      }
    }
  }

  .secondary-indicator,
  .unavailable-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    padding: 0.5rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
    width: 70px;
  }

  .secondary-indicator {
    background: rgba(34, 197, 94, 0.1);
    border: 1px solid rgba(34, 197, 94, 0.2);

    &:hover {
      background: rgba(34, 197, 94, 0.15);
    }
  }

  .unavailable-indicator {
    background: rgba(107, 114, 128, 0.1);
    border: 1px solid rgba(107, 114, 128, 0.2);
  }

  .matrix-note {
    margin-top: 1rem;
    padding: 0.75rem;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 0.375rem;
    display: flex;
    align-items: center;
    border-left: 3px solid #3b82f6;
  }

  // 小尺寸变体
  &--small {
    .matrix-header {
      grid-template-columns: 120px repeat(auto-fit, minmax(70px, 1fr));
      
      .corner-cell {
        padding: 0.375rem;
        font-size: 0.75rem;
      }
      
      .period-cell {
        padding: 0.375rem;
        font-size: 0.75rem;
        
        .role-badge {
          padding: 0.0625rem 0.375rem;
          font-size: 0.5rem;
          min-width: 20px;
          height: 14px;
          box-shadow: 0 1px 2px rgba(59, 130, 246, 0.2);
        }
      }
    }

    .matrix-row {
      grid-template-columns: 120px repeat(auto-fit, minmax(70px, 1fr));
      
      .indicator-cell {
        padding: 0.375rem;
        font-size: 0.75rem;
        
        .font-medium {
          font-size: 0.75rem;
        }
        
        .text-xs {
          font-size: 0.625rem;
        }
      }
      
      .checkbox-cell {
        padding: 0.375rem;
      }
    }

    .custom-checkbox {
      width: 1rem;
      height: 1rem;
      
      .check-icon {
        font-size: 0.75rem;
      }
    }

    .secondary-indicator,
    .unavailable-indicator {
      padding: 0.25rem;
      gap: 0.125rem;
      width: 50px;
      
      span {
        font-size: 0.625rem;
      }
    }

    .matrix-note {
      margin-top: 0.75rem;
      padding: 0.5rem;
      font-size: 0.75rem;
    }
  }
}
</style>
