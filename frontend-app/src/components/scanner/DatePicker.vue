<template>
  <div class="date-picker-container w-full">
    <el-tooltip :content="tooltipText" placement="top" :show-after="500">
      <div class="date-picker-wrapper w-full">
        <el-date-picker
          v-model="selectedDate"
          type="date"
          :placeholder="placeholder"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :disabled="disabled"
          :clearable="true"
          :disabledDate="disabledDate"
          size="large"
          @change="handleDateChange"
          class="custom-date-picker w-full"
        >
          <template #prefix>
            <Icon name="calendar" />
          </template>
        </el-date-picker>
      </div>
    </el-tooltip>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import { ElDatePicker, ElTooltip } from "element-plus";
import Icon from "@/components/common/Icon.vue";
import { getLatestTradingDate } from "@/services/api/trading";

const props = defineProps({
  modelValue: {
    type: String,
    default: null,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
    default: "选择回测日期",
  },
});

const emit = defineEmits(["update:modelValue"]);

// 响应式数据
const selectedDate = ref(props.modelValue);
const latestTradingDate = ref("");
const isLoading = ref(false);

// 计算属性
const tooltipText = computed(() => {
  if (selectedDate.value) {
    return `历史回测：基于 ${selectedDate.value} 及之前的数据进行分析`;
  }
  return "选择历史日期进行回测分析，默认使用最近交易日数据";
});

const hintText = computed(() => {
  if (!selectedDate.value && latestTradingDate.value) {
    return `默认：${latestTradingDate.value}`;
  }
  if (selectedDate.value) {
    return `回测截止：${selectedDate.value}`;
  }
  return "请选择日期";
});

// 禁用未来日期和非交易日（简化版，仅禁用未来日期）
const disabledDate = (date) => {
  const today = new Date();
  today.setHours(23, 59, 59, 999);
  return date > today;
};

// 方法
const handleDateChange = (value) => {
  selectedDate.value = value;
  emit("update:modelValue", value);
};

const loadLatestTradingDate = async () => {
  try {
    isLoading.value = true;
    const response = await getLatestTradingDate();
    latestTradingDate.value = response.date;

    // 如果没有选中日期，设置默认值为最近交易日
    if (!selectedDate.value) {
      selectedDate.value = response.date;
      emit("update:modelValue", response.date);
    }
  } catch (error) {
    console.error("获取最近交易日失败:", error);
  } finally {
    isLoading.value = false;
  }
};

// 监听props变化
watch(
  () => props.modelValue,
  (newValue) => {
    selectedDate.value = newValue;
  },
  { immediate: true }
);

// 生命周期
onMounted(() => {
  loadLatestTradingDate();
});
</script>

<style scoped lang="scss">
.date-picker-container {
  height: 100%;
  .date-picker-wrapper {
    position: relative;
    :deep(.el-date-editor) {
      width: 100%;
      height: 48px;
    }
  }

  .date-picker-wrapper {
    width: 100%;

    :deep(.el-input__wrapper) {
      background-color: var(--bg-secondary);
      border: 1px solid var(--border-color);
      transition: all 0.2s ease;
      border-radius: 8px;

      &:hover {
        border-color: var(--primary);
      }

      &.is-focus {
        border-color: var(--primary);
        box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
      }
    }

    :deep(.el-input__inner) {
      color: var(--text-primary);

      &::placeholder {
        color: var(--text-secondary);
      }
    }

    :deep(.el-input__prefix-inner) {
      color: var(--text-secondary);
    }
  }

  .date-picker-hint {
    margin-top: 0.25rem;
    text-align: center;
  }
}

// 暗色主题适配
[data-theme="dark"] {
  .custom-date-picker {
    :deep(.el-input__wrapper) {
      background-color: var(--bg-secondary);
      border-color: var(--border-color);
    }
  }
}
</style>
