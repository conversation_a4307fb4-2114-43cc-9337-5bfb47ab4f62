<template>
  <div class="navbar h-80px bg-bg-secondary border-b border-border-color px-6 z-9999 flex items-center">
    <div class="flex items-center justify-between w-full flex-1">
      <!-- 左侧：汉堡菜单 + 面包屑 -->
      <div class="flex items-center space-x-4">
        <!-- 汉堡菜单按钮 -->
        <button
          @click="uiStore.toggleSidebar"
          class="p-2 rounded-lg hover:bg-bg-tertiary transition-colors"
        >
          <AppIcon name="menu" class="text-xl" />
        </button>
        
        <!-- 面包屑导航 -->
        <nav class="breadcrumb">
          <ol class="flex items-center space-x-2 text-sm">
            <li v-for="(item, index) in breadcrumbs" :key="index">
              <router-link
                v-if="item.path && index < breadcrumbs.length - 1"
                :to="item.path"
                class="text-text-muted hover:text-text-primary transition-colors"
              >
                {{ item.label }}
              </router-link>
              <span v-else class="text-text-primary font-medium">
                {{ item.label }}
              </span>              <AppIcon
                v-if="index < breadcrumbs.length - 1"
                name="chevron-right"
                class="text-text-muted mx-2"
                :size="16"
              />
            </li>
          </ol>
        </nav>
      </div>
      
      <!-- 右侧：搜索框 + 用户操作 -->
      <div class="flex items-center space-x-4">
        <!-- 搜索框 -->
        <div class="relative search-container">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索股票..."
            class="search-input w-64 pl-10 pr-4 py-2"
            @keyup.enter="handleSearch"
            @input="debouncedSearch"
          >
          <AppIcon name="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-muted" :size="16" />
          
          <!-- 搜索结果下拉框 -->
          <div
            v-if="searchQuery.trim() && (isSearching || searchResults.length > 0)"
            class="absolute top-full left-0 right-0 mt-2 bg-bg-secondary border border-border-color rounded-lg shadow-xl z-9999"
          >
            <el-scrollbar max-height="240px">
              <!-- 加载状态 -->
              <div v-if="isSearching" class="px-4 py-3 text-center text-text-muted">
                <div class="flex items-center justify-center space-x-2">
                  <div class="animate-spin rounded-full h-4 w-4 border-2 border-accent-primary border-t-transparent"></div>
                  <span>搜索中...</span>
                </div>
              </div>
              
              <!-- 搜索结果 -->
              <div
                v-for="stock in searchResults"
                :key="stock.code"
                @click="navigateToStock(stock)"
                class="px-4 py-3 hover:bg-bg-tertiary cursor-pointer border-b border-border-color last:border-b-0"
              >
                <div class="flex justify-between items-center">
                  <div class="flex-1">
                    <div class="font-medium">{{ stock.name }}</div>
                    <div class="text-sm text-text-muted">
                      {{ stock.full_code || stock.code }}
                      <span v-if="stock.exchange" class="ml-2 text-xs bg-bg-tertiary px-2 py-0.5 rounded">
                        {{ stock.exchange }}
                      </span>
                    </div>
                  </div>
                  <div class="text-right ml-4">
                    <div v-if="stock.industry" class="text-xs text-text-muted">
                      {{ stock.industry }}
                    </div>
                    <div v-if="stock.match_type" class="text-xs text-accent-primary">
                      {{ stock.match_type === 'code' ? '代码匹配' : stock.match_type === 'name' ? '名称匹配' : '行业匹配' }}
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 无结果状态 -->
              <div v-if="!isSearching && searchResults.length === 0" class="px-4 py-3 text-center text-text-muted">
                <div class="flex items-center justify-center space-x-2">
                  <AppIcon name="search" :size="16" />
                  <span>未找到相关股票</span>
                </div>
              </div>
            </el-scrollbar>
          </div>
        </div>
        
        <!-- 通知按钮 -->
        <button
          class="relative p-2 rounded-lg hover:bg-bg-tertiary transition-colors"
          @click="toggleNotifications"
        >
          <i class="i-carbon-notification text-xl"></i>
          <span
            v-if="unreadNotifications > 0"
            class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex-center"
          >
            {{ unreadNotifications }}
          </span>
        </button>
        
        <!-- 设置按钮 -->
        <router-link
          to="/settings"
          class="p-2 rounded-lg hover:bg-bg-tertiary transition-colors"
        >
          <AppIcon name="settings" class="text-xl" />
        </router-link>
        
        <!-- 全屏按钮 -->
        <button
          @click="toggleFullscreen"
          class="p-2 rounded-lg hover:bg-bg-tertiary transition-colors"
        >
          <AppIcon :name="isFullscreen ? 'minimize' : 'maximize'" class="text-xl" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useFullscreen } from '@vueuse/core'
import { useUiStore } from '@/store'
import AppIcon from '@/components/common/Icon.vue'
import { api } from '@/services/api'

const route = useRoute()
const router = useRouter()
const uiStore = useUiStore()

// 全屏功能
const { isFullscreen, toggle: toggleFullscreen } = useFullscreen()

// 搜索相关
const searchQuery = ref('')
const searchResults = ref([])
const searchDebounceTimer = ref(null)
const isSearching = ref(false)

// 通知相关
const unreadNotifications = ref(0)

// 路由标题映射
const routeTitles = {
  '/': '仪表盘',
  '/stocks': '股票列表',
  '/watchlist': '自选股',
  '/analysis': '技术分析',
  '/strategies': '量化策略',
  '/settings': '设置'
}

// 面包屑导航
const breadcrumbs = computed(() => {
  const items = []
  
  if (route.path === '/') {
    items.push({ label: '仪表盘', path: '/' })
  } else if (route.path.startsWith('/stocks/')) {
    items.push({ label: '股票列表', path: '/stocks' })
    if (route.params.code) {
      items.push({ label: route.params.code })
    }
  } else if (route.path.startsWith('/analysis/')) {
    items.push({ label: '技术分析', path: '/analysis' })
    if (route.params.code) {
      items.push({ label: `${route.params.code} 分析` })
    }
  } else {
    const title = routeTitles[route.path]
    if (title) {
      items.push({ label: title, path: route.path })
    }
  }
  
  return items
})

// 搜索处理
const handleSearch = async () => {
  if (!searchQuery.value.trim()) {
    searchResults.value = []
    isSearching.value = false
    return
  }
  
  isSearching.value = true
  
  try {
    // 调用快速搜索API
    const response = await api.stockSearch.quickSearchStocks(searchQuery.value, 10)
    searchResults.value = response.data || []
  } catch (error) {
    console.error('搜索失败:', error)
    searchResults.value = []
  } finally {
    isSearching.value = false
  }
}

// 防抖搜索
const debouncedSearch = () => {
  if (searchDebounceTimer.value) {
    clearTimeout(searchDebounceTimer.value)
  }
  
  searchDebounceTimer.value = setTimeout(() => {
    handleSearch()
  }, 300) // 300ms 防抖
}

// 导航到股票分析界面
const navigateToStock = (stock) => {
  router.push({
    name: 'Analysis',
    query: { stock: stock.code, name: stock.name }
  })
  searchQuery.value = ''
  searchResults.value = []
  isSearching.value = false
}

// 切换通知面板
const toggleNotifications = () => {
  // 实现通知面板切换逻辑
  console.log('切换通知面板')
}

// 监听搜索输入变化
watch(searchQuery, (newValue) => {
  if (!newValue.trim()) {
    searchResults.value = []
    isSearching.value = false
    if (searchDebounceTimer.value) {
      clearTimeout(searchDebounceTimer.value)
    }
  } else {
    debouncedSearch()
  }
})

// 点击外部关闭搜索结果
const handleClickOutside = (event) => {
  const searchContainer = event.target.closest('.search-container')
  if (!searchContainer) {
    searchResults.value = []
    isSearching.value = false
  }
}

// 组件挂载时添加事件监听
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  if (searchDebounceTimer.value) {
    clearTimeout(searchDebounceTimer.value)
  }
})
</script>

<style scoped lang="scss">
.navbar {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  backdrop-filter: blur(10px);
}

.search-input {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 8px 16px 8px 40px;
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: var(--bg-secondary);
  }
  
  &::placeholder {
    color: var(--text-muted);
  }
}
</style>
