import { createRouter, createWebHistory } from 'vue-router'

// 路由配置
export const routes = [ // 将 routes 导出
  {
    path: '/',
    redirect: '/analysis', // 根路径重定向到 /analysis,
    meta: {
      hidden: true // 根路径通常不需要在菜单中显示
    }
  },
  {
    path: '/analysis',
    name: 'Analysis',
    component: () => import('@/pages/Analysis/index.vue'),
    meta: {
      title: '股票分析',
      requiresAuth: false,
      icon: 'chart-line' // 使用 Carbon 图标集中的图标
    }
  },
  {
    path: '/watchlist',
    name: 'Watchlist',
    component: () => import('@/pages/Watchlist/index.vue'),
    meta: {
      title: '自选股',
      requiresAuth: false,
      icon: 'star' // 使用 Carbon 图标集中的图标
    }
  },
  {
    path: '/data',
    name: 'DataManagement',
    component: () => import('@/pages/DataManagement/index.vue'),
    meta: {
      title: '数据管理',
      requiresAuth: false,
      icon: 'data-base' // 使用 Carbon 图标集中的图标
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/pages/Settings/index.vue'),
    meta: {
      title: '设置',
      requiresAuth: false,
      icon: 'settings' // 使用 Carbon 图标集中的图标
    }
  },
  {
    path: '/scanner',
    name: 'Scanner',
    component: () => import('@/pages/Scanner/index.vue'),
    meta: {
      title: '技术指标',
      requiresAuth: false,
      icon: 'scan-alt' // 使用 Carbon 图标集中的图标
    }
  },
  {
    path: '/technical-indicators',
    name: 'TechnicalIndicators',
    component: () => import('@/pages/TechnicalIndicators/index.vue'),
    meta: {
      title: '技术指标',
      requiresAuth: false,
      hidden: true,
      icon: 'chart-line' // 添加 icon 元数据
    }
  },
  // 404 页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/pages/NotFound/index.vue'),
    meta: {
      title: '页面未找到',
      hidden: true, // 通常 404 页面也应隐藏在菜单中
      icon: 'error' // 添加 icon 元数据
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 滚动行为
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 股票量化分析系统`
  }
  
  // 权限检查
  if (to.meta.requiresAuth) {
    const token = localStorage.getItem('access_token')
    if (!token) {
      // 需要登录但没有 token，跳转到登录页
      next('/login')
      return
    }
  }
  
  next()
})

// 全局后置钩子
router.afterEach((to, from) => {
  // 可以在这里添加页面访问统计等
  console.log(`导航到: ${to.path}`)
})

export default router
