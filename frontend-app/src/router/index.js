import { createRouter, createWebHistory } from 'vue-router'

// 路由配置
export const routes = [ // 将 routes 导出
  {
    path: '/',
    redirect: '/analysis', // 根路径重定向到 /analysis,
    meta: {
      hidden: true // 根路径通常不需要在菜单中显示
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/pages/Login/index.vue'),
    meta: {
      title: '用户登录',
      requiresAuth: false,
      hidden: true, // 登录页面不在菜单中显示
      icon: 'login'
    }
  },
  {
    path: '/analysis',
    name: 'Analysis',
    component: () => import('@/pages/Analysis/index.vue'),
    meta: {
      title: '股票分析',
      requiresAuth: true,
      icon: 'chart-line' // 使用 Carbon 图标集中的图标
    }
  },
  {
    path: '/watchlist',
    name: 'Watchlist',
    component: () => import('@/pages/Watchlist/index.vue'),
    meta: {
      title: '自选股',
      requiresAuth: true,
      icon: 'star' // 使用 Carbon 图标集中的图标
    }
  },
  {
    path: '/data',
    name: 'DataManagement',
    component: () => import('@/pages/DataManagement/index.vue'),
    meta: {
      title: '数据管理',
      requiresAuth: true,
      icon: 'data-base' // 使用 Carbon 图标集中的图标
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/pages/Settings/index.vue'),
    meta: {
      title: '设置',
      requiresAuth: true,
      icon: 'settings' // 使用 Carbon 图标集中的图标
    }
  },
  {
    path: '/scanner',
    name: 'Scanner',
    component: () => import('@/pages/Scanner/index.vue'),
    meta: {
      title: '技术指标',
      requiresAuth: true,
      icon: 'scan-alt' // 使用 Carbon 图标集中的图标
    }
  },
  {
    path: '/scheduled-tasks',
    name: 'ScheduledTasks',
    component: () => import('@/pages/ScheduledTasks/index.vue'),
    meta: {
      title: '定时任务',
      requiresAuth: true,
      icon: 'time' // 使用 Carbon 图标集中的图标
    }
  },
  {
    path: '/task-history',
    name: 'TaskHistory',
    component: () => import('@/pages/TaskHistory/index.vue'),
    meta: {
      title: '任务历史',
      requiresAuth: true,
      icon: 'task' // 使用 Carbon 图标集中的图标
    }
  },
  {
    path: '/technical-indicators',
    name: 'TechnicalIndicators',
    component: () => import('@/pages/TechnicalIndicators/index.vue'),
    meta: {
      title: '技术指标',
      requiresAuth: true,
      hidden: true,
      icon: 'chart-line' // 添加 icon 元数据
    }
  },
  {
    path: '/user-management',
    name: 'UserManagement',
    component: () => import('@/pages/UserManagement/index.vue'),
    meta: {
      title: '用户管理',
      requiresAuth: true,
      requiresAdmin: true, // 需要管理员权限
      icon: 'user-admin'
    }
  },
  // 404 页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/pages/NotFound/index.vue'),
    meta: {
      title: '页面未找到',
      hidden: true, // 通常 404 页面也应隐藏在菜单中
      icon: 'error' // 添加 icon 元数据
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 滚动行为
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 股票量化分析系统`
  }
  
  // 权限检查
  if (to.meta.requiresAuth) {
    // 动态导入auth store
    const { useAuthStore } = await import('@/store')
    const authStore = useAuthStore()
    
    // 等待认证初始化完成
    if (!authStore.isInitialized) {
      await authStore.initialize()
    }
    
    // 检查是否已经认证
    if (!authStore.isAuthenticated) {
      // 未登录，跳转到登录页，并记录重定向路径
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
    
    // 检查管理员权限
    if (to.meta.requiresAdmin && !authStore.isAdmin) {
      console.warn('权限不足：需要管理员权限')
      // 权限不足，跳转到首页或显示权限不足页面
      next({ path: '/' })
      return
    }
  }
  
  // 如果已登录用户访问登录页，重定向到首页
  if (to.path === '/login') {
    const { useAuthStore } = await import('@/store')
    const authStore = useAuthStore()
    
    // 等待认证初始化完成
    if (!authStore.isInitialized) {
      await authStore.initialize()
    }
    
    if (authStore.isAuthenticated) {
      next({ path: '/' })
      return
    }
  }
  
  next()
})

// 全局后置钩子
router.afterEach((to, from) => {
  // 可以在这里添加页面访问统计等
  console.log(`导航到: ${to.path}`)
})

export default router
