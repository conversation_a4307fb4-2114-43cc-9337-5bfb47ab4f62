import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { scannerApi } from '@/services/api/scanner'

export const useScannerStore = defineStore('scanner', () => {
  // 状态
  const currentTask = ref(null)
  const results = ref([])
  const totalCount = ref(0)
  const loadingResults = ref(false)
  
  // 进度轮询定时器
  let progressTimer = null
  
  // 计算属性
  const isScanning = computed(() => {
    return currentTask.value?.status === 'pending' || 
           currentTask.value?.status === 'running'
  })
  
  // 方法
  const initSession = async () => {
    try {
      // 获取当前用户的活跃扫描任务
      const activeTasks = await scannerApi.getActiveTasks()
      
      if (activeTasks && activeTasks.length > 0) {
        // 恢复最新的活跃任务状态
        const latestTask = activeTasks[0] // 取第一个（最新的）活跃任务
        currentTask.value = {
          id: latestTask.task_id,
          status: latestTask.status,
          created_at: latestTask.created_at,
          progress: {
            current: latestTask.progress?.current || 0,
            total: latestTask.progress?.total || 0,
            percentage: latestTask.progress?.percentage || 0,
            message: latestTask.progress?.message
          }
        }
        
        // 如果任务正在运行，开始轮询进度
        if (['pending', 'running'].includes(latestTask.status)) {
          startProgressPolling()
        }
        
        // 如果任务已完成，获取结果
        if (latestTask.status === 'completed') {
          await fetchResults()
        }
        
        return true
      }
      
      // 没有活跃任务
      return false
    } catch (error) {
      console.error('Failed to init session:', error)
      // 初始化失败不抛出错误，只是没有活跃任务
      return false
    }
  }
  
  const startScan = async (params) => {
    try {
      // 首先停止现有的轮询
      stopProgressPolling()
      
      const response = await scannerApi.startScan(params)
      currentTask.value = {
        id: response.task_id,
        status: response.status,
        created_at: response.created_at,
        progress: {
          current: 0,
          total: 0,
          percentage: 0,
          message: '准备中...'
        }
      }
      
      // 清空之前的结果
      results.value = []
      totalCount.value = 0
      
      console.log(`Starting new scan task: ${response.task_id}`)
      
      // 开始轮询新任务进度
      startProgressPolling()
      
      return response
    } catch (error) {
      console.error('Failed to start scan:', error)
      throw error
    }
  }
  
  const stopScan = async () => {
    try {
      if (!currentTask.value?.id) {
        throw new Error('没有正在运行的扫描任务')
      }
      
      await scannerApi.stopScan(currentTask.value.id)
      
      // 停止轮询
      stopProgressPolling()
      
      // 更新状态
      if (currentTask.value) {
        currentTask.value.status = 'cancelled'
        currentTask.value.progress.message = '已取消'
      }
    } catch (error) {
      console.error('Failed to stop scan:', error)
      throw error
    }
  }
  
  const fetchProgress = async () => {
    try {
      if (!currentTask.value?.id) return
      
      const response = await scannerApi.getProgress(currentTask.value.id)
      
      // 更新任务信息
      currentTask.value = {
        ...currentTask.value,
        status: response.status,
        progress: {
          current: response.current,
          total: response.total,
          percentage: response.percentage,
          message: response.message
        }
      }
      
      // 如果任务完成或失败，停止轮询并获取结果
      if (['completed', 'failed', 'cancelled'].includes(response.status)) {
        stopProgressPolling()
        
        // 如果任务成功完成，获取结果
        if (response.status === 'completed') {
          await fetchResults()
        }
      }
    } catch (error) {
      console.error('Failed to fetch progress:', error)
      // 如果获取进度失败，可能任务不存在了，尝试获取新的活跃任务
      if (error.response?.status === 404) {
        console.log('Current task not found, checking for new active tasks...')
        stopProgressPolling()
        
        try {
          // 尝试获取活跃任务
          const activeTasks = await scannerApi.getActiveTasks()
          if (activeTasks && activeTasks.length > 0) {
            const latestTask = activeTasks[0]
            // 如果发现新的活跃任务，切换到新任务
            if (latestTask.task_id !== currentTask.value?.id) {
              console.log(`Switching to new active task: ${latestTask.task_id}`)
              currentTask.value = {
                id: latestTask.task_id,
                status: latestTask.status,
                created_at: latestTask.created_at,
                progress: {
                  current: latestTask.progress?.current || 0,
                  total: latestTask.progress?.total || 0,
                  percentage: latestTask.progress?.percentage || 0,
                  message: latestTask.progress?.message
                }
              }
              
              // 如果新任务正在运行，重新开始轮询
              if (['pending', 'running'].includes(latestTask.status)) {
                startProgressPolling()
              } else if (latestTask.status === 'completed') {
                await fetchResults()
              }
            }
          } else {
            // 没有活跃任务，清空当前任务
            currentTask.value = null
          }
        } catch (activeError) {
          console.error('Failed to get active tasks:', activeError)
          // 获取活跃任务失败，标记当前任务为失败状态
          if (currentTask.value) {
            currentTask.value.status = 'failed'
            currentTask.value.progress.message = '任务不存在'
          }
        }
      }
    }
  }
  
  const fetchResults = async (page = 1, pageSize = 20) => {
    try {
      if (!currentTask.value?.id) return
      
      loadingResults.value = true
      const response = await scannerApi.getResults(
        currentTask.value.id,
        page,
        pageSize
      )
      console.log(`Fetched results for task ${currentTask.value.id}:`, response)

      results.value = response.results
      totalCount.value = response.total_count
    } catch (error) {
      console.error('Failed to fetch results:', error)
      results.value = []
      totalCount.value = 0
    } finally {
      loadingResults.value = false
    }
  }
  
  const startProgressPolling = () => {
    stopProgressPolling() // 确保没有重复的轮询
    
    // 立即获取一次
    fetchProgress()
    
    // 每2秒轮询一次
    progressTimer = setInterval(() => {
      fetchProgress()
    }, 2000)
  }
  
  const stopProgressPolling = () => {
    if (progressTimer) {
      clearInterval(progressTimer)
      progressTimer = null
    }
  }
  
  const clearTask = () => {
    stopProgressPolling()
    currentTask.value = null
    results.value = []
    totalCount.value = 0
  }
  
  const cleanup = () => {
    stopProgressPolling()
  }
  
  return {
    // 状态
    currentTask,
    results,
    totalCount,
    loadingResults,
    
    // 计算属性
    isScanning,
    
    // 方法
    initSession,
    startScan,
    stopScan,
    fetchProgress,
    fetchResults,
    clearTask,
    cleanup
  }
})