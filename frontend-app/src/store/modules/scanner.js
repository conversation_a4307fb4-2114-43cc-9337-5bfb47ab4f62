import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { scannerApi } from '@/services/api/scanner'

export const useScannerStore = defineStore('scanner', () => {
  // 状态
  const sessionId = ref(null)
  const currentTask = ref(null)
  const results = ref([])
  const totalCount = ref(0)
  const loadingResults = ref(false)
  
  // 进度轮询定时器
  let progressTimer = null
  
  // 计算属性
  const isScanning = computed(() => {
    return currentTask.value?.status === 'pending' || 
           currentTask.value?.status === 'running'
  })
  
  // 方法
  const initSession = async () => {
    try {
      // 首先尝试获取活跃任务
      const activeTask = await scannerApi.getActiveTask()
      
      if (activeTask && activeTask.task_id) {
        // 恢复之前的任务状态
        sessionId.value = activeTask.session_id
        currentTask.value = {
          id: activeTask.task_id,
          status: activeTask.status,
          created_at: activeTask.created_at,
          progress: {
            current: activeTask.progress?.current || 0,
            total: activeTask.progress?.total || 0,
            percentage: activeTask.progress?.percentage || 0,
            message: activeTask.progress?.message
          }
        }
        
        // 如果任务正在运行，开始轮询进度
        if (['pending', 'running'].includes(activeTask.status)) {
          startProgressPolling()
        }
        
        // 如果任务已完成，获取结果
        if (activeTask.status === 'completed') {
          fetchResults()
        }
        
        return sessionId.value
      }
      
      // 没有活跃任务，创建新会话
      const response = await scannerApi.createSession()
      sessionId.value = response.session_id
      return sessionId.value
    } catch (error) {
      console.error('Failed to init session:', error)
      // 如果获取活跃任务失败，尝试创建新会话
      try {
        const response = await scannerApi.createSession()
        sessionId.value = response.session_id
        return sessionId.value
      } catch (createError) {
        console.error('Failed to create new session:', createError)
        throw createError
      }
    }
  }
  
  const startScan = async (params) => {
    try {
      // 确保有会话
      if (!sessionId.value) {
        await initSession()
      }
      
      const response = await scannerApi.startScan(params)
      currentTask.value = {
        id: response.task_id,
        status: response.status,
        created_at: response.created_at,
        progress: {
          current: 0,
          total: 0,
          percentage: 0
        }
      }
      
      // 清空之前的结果
      results.value = []
      totalCount.value = 0
      
      // 开始轮询进度
      startProgressPolling()
      
      return response
    } catch (error) {
      console.error('Failed to start scan:', error)
      throw error
    }
  }
  
  const stopScan = async () => {
    try {
      if (!currentTask.value?.id) {
        throw new Error('没有正在运行的扫描任务')
      }
      
      await scannerApi.stopScan(currentTask.value.id)
      
      // 停止轮询
      stopProgressPolling()
      
      // 更新状态
      if (currentTask.value) {
        currentTask.value.status = 'cancelled'
      }
    } catch (error) {
      console.error('Failed to stop scan:', error)
      throw error
    }
  }
  
  const fetchProgress = async () => {
    try {
      if (!currentTask.value?.id) return
      
      const response = await scannerApi.getProgress(currentTask.value.id)
      
      // 更新任务信息
      currentTask.value = {
        ...currentTask.value,
        status: response.status,
        progress: {
          current: response.current,
          total: response.total,
          percentage: response.percentage,
          message: response.message
        }
      }
      
      // 如果任务完成或失败，停止轮询
      if (['completed', 'failed', 'cancelled'].includes(response.status)) {
        stopProgressPolling()
      }
    } catch (error) {
      console.error('Failed to fetch progress:', error)
    }
  }
  
  const fetchResults = async (page = 1, pageSize = 20) => {
    try {
      if (!currentTask.value?.id) return
      
      loadingResults.value = true
      const response = await scannerApi.getResults(
        currentTask.value.id,
        page,
        pageSize
      )
      console.log(`Fetched results for task ${currentTask.value.id}:`, response);

      results.value = response.results
      totalCount.value = response.total_count
    } catch (error) {
      console.error('Failed to fetch results:', error)
      results.value = []
      totalCount.value = 0
    } finally {
      loadingResults.value = false
    }
  }
  
  const startProgressPolling = () => {
    stopProgressPolling() // 确保没有重复的轮询
    
    // 立即获取一次
    fetchProgress()
    
    // 每2秒轮询一次
    progressTimer = setInterval(() => {
      fetchProgress()
    }, 2000)
  }
  
  const stopProgressPolling = () => {
    if (progressTimer) {
      clearInterval(progressTimer)
      progressTimer = null
    }
  }
  
  const cleanup = () => {
    stopProgressPolling()
    currentTask.value = null
    results.value = []
    totalCount.value = 0
  }
  
  return {
    // 状态
    sessionId,
    currentTask,
    results,
    totalCount,
    loadingResults,
    
    // 计算属性
    isScanning,
    
    // 方法
    initSession,
    startScan,
    stopScan,
    fetchProgress,
    fetchResults,
    cleanup
  }
})