import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import apiClient from '@/services/apiClient'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref(null)
  const token = ref(localStorage.getItem('access_token'))
  const isLoading = ref(false)
  const isInitialized = ref(false)  // 添加初始化状态标识

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.is_admin || false)

  // 自动刷新token的定时器
  let refreshTimer = null

  // 登录
  async function login(credentials) {
    isLoading.value = true
    try {
      const response = await apiClient.post('/auth/login', credentials)
      
      token.value = response.access_token
      user.value = response.user
      
      // 保存到localStorage
      localStorage.setItem('access_token', response.access_token)
      if (credentials.remember_me) {
        localStorage.setItem('remember_user', 'true')
      }
      
      // 启动token刷新定时器（在过期前1小时刷新）
      startTokenRefresh(response.expires_in)
      
      return { success: true }
    } catch (error) {
      return { 
        success: false, 
        message: error.message || '登录失败' 
      }
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  async function logout() {
    try {
      // 调用后端登出接口（如果token有效）
      if (token.value) {
        await apiClient.post('/auth/logout')
      }
    } catch (error) {
      console.error('登出API调用失败:', error)
    }
    
    // 清理本地状态
    token.value = null
    user.value = null
    localStorage.removeItem('access_token')
    localStorage.removeItem('remember_user')
    
    // 清理定时器
    if (refreshTimer) {
      clearTimeout(refreshTimer)
      refreshTimer = null
    }
  }

  // 刷新token
  async function refreshToken() {
    if (!token.value) return false
    
    try {
      const response = await apiClient.post('/auth/refresh', {
        token: token.value
      })
      
      token.value = response.access_token
      localStorage.setItem('access_token', response.access_token)
      
      // 重新启动定时器
      startTokenRefresh(response.expires_in)
      
      return true
    } catch (error) {
      console.error('Token刷新失败:', error)
      await logout() // 刷新失败则登出
      return false
    }
  }

  // 启动token刷新定时器
  function startTokenRefresh(expiresIn) {
    if (refreshTimer) {
      clearTimeout(refreshTimer)
    }
    
    // 在过期前1小时刷新token
    const refreshTime = (expiresIn - 3600) * 1000
    if (refreshTime > 0) {
      refreshTimer = setTimeout(() => {
        refreshToken()
      }, refreshTime)
    }
  }

  // 获取用户信息
  async function fetchUser() {
    if (!token.value) return
    
    try {
      const userData = await apiClient.get('/auth/me')
      user.value = userData
    } catch (error) {
      console.error('获取用户信息失败:', error)
      await logout()
    }
  }

  // 验证当前token是否有效
  async function validateToken() {
    if (!token.value) return false
    
    try {
      await fetchUser()
      return true
    } catch (error) {
      console.error('Token验证失败:', error)
      await logout()
      return false
    }
  }

  // 初始化
  async function initialize() {
    try {
      if (token.value) {
        const isValid = await validateToken()
        
        // 如果有记住用户的设置，启动token刷新
        if (isValid && localStorage.getItem('remember_user')) {
          startTokenRefresh(24 * 3600) // 默认24小时
        }
      }
    } catch (error) {
      console.error('认证初始化失败:', error)
    } finally {
      isInitialized.value = true
    }
  }

  // 强制清除认证状态的方法（用于401响应处理）
  function clearAuth() {
    token.value = null
    user.value = null
    localStorage.removeItem('access_token')
    localStorage.removeItem('remember_user')
    
    // 清理定时器
    if (refreshTimer) {
      clearTimeout(refreshTimer)
      refreshTimer = null
    }
  }

  return {
    user,
    token,
    isLoading,
    isInitialized,
    isAuthenticated,
    isAdmin,
    login,
    logout,
    refreshToken,
    fetchUser,
    validateToken,
    initialize,
    clearAuth
  }
})