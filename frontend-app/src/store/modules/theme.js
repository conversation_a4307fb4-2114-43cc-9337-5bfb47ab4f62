import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useThemeStore = defineStore('theme', () => {
  // 状态
  const isDark = ref(true) // 默认深色主题
  
  // 计算属性
  const theme = computed(() => isDark.value ? 'dark' : 'light')
  const themeClass = computed(() => isDark.value ? 'dark' : 'light')
  
  // 动作
  const toggleTheme = () => {
    isDark.value = !isDark.value
    updateBodyClass()
    // 保存到 localStorage
    localStorage.setItem('theme', theme.value)
  }
  
  const setTheme = (newTheme) => {
    isDark.value = newTheme === 'dark'
    updateBodyClass()
    localStorage.setItem('theme', newTheme)
  }
  
  const updateBodyClass = () => {
    const body = document.body
    if (isDark.value) {
      body.classList.add('dark')
      body.classList.remove('light')
    } else {
      body.classList.remove('dark')
      body.classList.add('light')
    }
  }
  
  const initTheme = () => {
    // 从 localStorage 读取主题设置
    const savedTheme = localStorage.getItem('theme')
    if (savedTheme) {
      setTheme(savedTheme)
    } else {
      // 检查系统主题偏好
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      setTheme(prefersDark ? 'dark' : 'light')
    }
  }
  
  return {
    // 状态
    isDark,
    // 计算属性
    theme,
    themeClass,
    // 动作
    toggleTheme,
    setTheme,
    initTheme
  }
})
