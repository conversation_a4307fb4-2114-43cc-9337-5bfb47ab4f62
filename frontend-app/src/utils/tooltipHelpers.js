/**
 * ECharts tooltip 配置和格式化函数
 */
import { formatVolume } from './chartHelpers';

// Tooltip 通用样式配置
export const getTooltipCommonConfig = () => ({
  trigger: "axis",
  show: true,
  backgroundColor: "rgba(0, 0, 0, 0.9)",
  borderColor: "#4a90e2",
  borderWidth: 1,
  borderRadius: 8,
  textStyle: { 
    color: "#fff", 
    fontSize: 12,
    lineHeight: 18
  },
  padding: [12, 16],
  extraCssText: 'max-width: 300px; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5); z-index: 9999;',
  confine: true,
  position: function (point, params, dom, rect, size) {
    const tooltipWidth = size.contentSize[0];
    const tooltipHeight = size.contentSize[1];
    const containerWidth = size.viewSize[0];
    const containerHeight = size.viewSize[1];
    
    let x = point[0] + 10;
    let y = point[1] - tooltipHeight - 10;
    
    if (x + tooltipWidth > containerWidth) {
      x = point[0] - tooltipWidth - 10;
    }
    if (y < 0) {
      y = point[1] + 10;
    }
    
    return [x, y];
  }
});

// K线图表 tooltip 格式化函数
export const createKlineTooltipFormatter = (klineData, values, ma5, ma10, ma20, bollinger, adaptivePeriods, chartOverlay) => {
  return function (params) {
    try {
      const data = params[0];
      const dataIndex = data.dataIndex;
      const kline = values[dataIndex];
      if (!kline) return "";

      const [open, close, low, high] = kline;
      
      // 计算涨跌额：当前收盘价 - 前一日收盘价
      let change = 0;
      if (dataIndex > 0) {
        const prevClose = values[dataIndex - 1][1]; // 前一日收盘价
        change = close - prevClose;
      } else {
        // 第一天数据，使用开盘到收盘的变化
        change = close - open;
      }
      
      // 优先使用后端提供的涨跌幅，如果没有则计算相对于前日收盘价的涨跌幅
      let changePercent = 0;
      const currentData = klineData[dataIndex];
      
      if (currentData && currentData.change_pct !== undefined && currentData.change_pct !== null) {
        // 使用后端计算的涨跌幅
        changePercent = parseFloat(currentData.change_pct).toFixed(2);
      } else if (dataIndex > 0) {
        // 计算相对于前一日收盘价的涨跌幅
        const prevClose = values[dataIndex - 1][1]; // 前一日收盘价
        changePercent = (((close - prevClose) / prevClose) * 100).toFixed(2);
      } else {
        // 第一天数据，使用开盘到收盘的涨跌幅
        changePercent = ((change / open) * 100).toFixed(2);
      }
      
      const isUp = parseFloat(changePercent) >= 0;
      
      const volumeValue = currentData?.volume || 0;
      
      let content = `
        <div style="border-bottom: 1px solid #333; padding-bottom: 8px; margin-bottom: 8px;">
          <div style="font-weight: bold; color: #4a90e2; font-size: 14px;">${data.axisValue}</div>
        </div>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
          <div>
            <div style="color: #999; font-size: 11px;">开盘价</div>
            <div style="color: #fff; font-weight: bold;">¥${open}</div>
          </div>
          <div>
            <div style="color: #999; font-size: 11px;">收盘价</div>
            <div style="color: ${isUp ? '#ef4444' : '#22c55e'}; font-weight: bold;">¥${close}</div>
          </div>
          <div>
            <div style="color: #999; font-size: 11px;">最高价</div>
            <div style="color: #fff; font-weight: bold;">¥${high}</div>
          </div>
          <div>
            <div style="color: #999; font-size: 11px;">最低价</div>
            <div style="color: #fff; font-weight: bold;">¥${low}</div>
          </div>
          <div>
            <div style="color: #999; font-size: 11px;">涨跌额</div>
            <div style="color: ${isUp ? '#ef4444' : '#22c55e'}; font-weight: bold;">
              ${isUp ? '+' : ''}${change.toFixed(2)}
            </div>
          </div>
          <div>
            <div style="color: #999; font-size: 11px;">涨跌幅</div>
            <div style="color: ${isUp ? '#ef4444' : '#22c55e'}; font-weight: bold;">
              ${isUp ? '+' : ''}${changePercent}%
            </div>
          </div>
        </div>
        <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px;">
          <div style="color: #999; font-size: 11px;">成交量</div>
          <div style="color: #4ecdc4; font-weight: bold;">${formatVolume(volumeValue)}</div>
        </div>
      `;

      // 如果有指标数据，显示指标信息
      if (chartOverlay === "ma") {
        const ma5Value = ma5[dataIndex];
        const ma10Value = ma10[dataIndex];
        const ma20Value = ma20[dataIndex];
        
        content += `
          <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px;">
            <div style="color: #999; font-size: 11px; margin-bottom: 4px;">移动平均线</div>
            <div style="display: flex; gap: 12px; flex-wrap: wrap;">
              ${ma5Value ? `<div><span style="color: #ff6b6b;">MA${adaptivePeriods.ma5}:</span> <span style="color: #fff;">¥${ma5Value}</span></div>` : ''}
              ${ma10Value ? `<div><span style="color: #4ecdc4;">MA${adaptivePeriods.ma10}:</span> <span style="color: #fff;">¥${ma10Value}</span></div>` : ''}
              ${ma20Value ? `<div><span style="color: #45b7d1;">MA${adaptivePeriods.ma20}:</span> <span style="color: #fff;">¥${ma20Value}</span></div>` : ''}
            </div>
          </div>
        `;
      }

      if (chartOverlay === "bollinger") {
        const upperValue = bollinger.upper[dataIndex];
        const middleValue = bollinger.middle[dataIndex];
        const lowerValue = bollinger.lower[dataIndex];
        
        content += `
          <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px;">
            <div style="color: #999; font-size: 11px; margin-bottom: 4px;">布林带</div>
            <div style="display: flex; gap: 12px; flex-wrap: wrap;">
              ${upperValue ? `<div><span style="color: #ff6b6b;">上轨:</span> <span style="color: #fff;">¥${upperValue}</span></div>` : ''}
              ${middleValue ? `<div><span style="color: #4ecdc4;">中轨:</span> <span style="color: #fff;">¥${middleValue}</span></div>` : ''}
              ${lowerValue ? `<div><span style="color: #22c55e;">下轨:</span> <span style="color: #fff;">¥${lowerValue}</span></div>` : ''}
            </div>
          </div>
        `;
      }

      return content;
    } catch (error) {
      console.error('K线图表 tooltip 格式化错误:', error);
      return `<div style="color: #fff;">数据加载中...</div>`;
    }
  };
};

// MACD 图表 tooltip 格式化函数
export const createMacdTooltipFormatter = (dates, dif, dea, histogram) => {
  return function (params) {
    try {
      if (!params || params.length === 0) return "";
      
      const date = params[0].axisValue;
      const dataIndex = params[0].dataIndex;
      
      const difValue = dif[dataIndex];
      const deaValue = dea[dataIndex];
      const histogramValue = histogram[dataIndex];
      
      const isPositive = histogramValue >= 0;
      
      let content = `
        <div style="border-bottom: 1px solid #333; padding-bottom: 8px; margin-bottom: 8px;">
          <div style="font-weight: bold; color: #4a90e2; font-size: 14px;">${date}</div>
          <div style="color: #999; font-size: 11px;">MACD 指标</div>
        </div>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 8px;">
          <div>
            <div style="color: #999; font-size: 11px;">DIF (快线)</div>
            <div style="color: #ff6b6b; font-weight: bold;">${difValue?.toFixed(4) || "0.0000"}</div>
          </div>
          <div>
            <div style="color: #999; font-size: 11px;">DEA (慢线)</div>
            <div style="color: #4ecdc4; font-weight: bold;">${deaValue?.toFixed(4) || "0.0000"}</div>
          </div>
        </div>
        <div style="border-top: 1px solid #333; padding-top: 8px;">
          <div style="color: #999; font-size: 11px;">HISTOGRAM (柱状线)</div>
          <div style="color: ${isPositive ? '#ef4444' : '#22c55e'}; font-weight: bold;">
            ${isPositive ? '+' : ''}${histogramValue?.toFixed(4) || "0.0000"}
          </div>
        </div>
      `;

      // 添加信号提示
      if (difValue && deaValue) {
        const signal = difValue > deaValue ? "多头信号" : "空头信号";
        const signalColor = difValue > deaValue ? "#ef4444" : "#22c55e";  // 多头红色，空头绿色
        
        content += `
          <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px;">
            <div style="color: #999; font-size: 11px;">趋势信号</div>
            <div style="color: ${signalColor}; font-weight: bold;">${signal}</div>
          </div>
        `;
      }

      return content;
    } catch (error) {
      console.error('MACD 图表 tooltip 格式化错误:', error);
      return `<div style="color: #fff;">数据加载中...</div>`;
    }
  };
};

// 成交量图表 tooltip 格式化函数
export const createVolumeTooltipFormatter = (volumeData) => {
  return function (params) {
    try {
      const data = params[0];
      const dataIndex = data.dataIndex;
      const currentData = volumeData[dataIndex];
      
      if (!currentData) return "";
      
      const prevData = dataIndex > 0 ? volumeData[dataIndex - 1] : null;
      const volumeChange = prevData ? currentData.volume - prevData.volume : 0;
      const volumeChangePercent = prevData ? ((volumeChange / prevData.volume) * 100) : 0;
      
      const priceChange = currentData.close - currentData.open;
      const isPriceUp = priceChange >= 0;
      
      const avgPrice = (currentData.high + currentData.low + currentData.close + currentData.open) / 4;
      const turnover = currentData.volume * avgPrice;
      
      let content = `
        <div style="border-bottom: 1px solid #333; padding-bottom: 8px; margin-bottom: 8px;">
          <div style="font-weight: bold; color: #4a90e2; font-size: 14px;">${data.axisValue}</div>
          <div style="color: #999; font-size: 11px;">成交量分析</div>
        </div>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 8px;">
          <div>
            <div style="color: #999; font-size: 11px;">成交量</div>
            <div style="color: #4ecdc4; font-weight: bold;">${formatVolume(currentData.volume)}</div>
          </div>
          <div>
            <div style="color: #999; font-size: 11px;">成交金额</div>
            <div style="color: #ff9f43; font-weight: bold;">${formatVolume(turnover)}元</div>
          </div>
        </div>
      `;

      // 如果有前一天的数据，显示变化情况
      if (prevData) {
        const isVolumeUp = volumeChange >= 0;
        content += `
          <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px;">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
              <div>
                <div style="color: #999; font-size: 11px;">量比变化</div>
                <div style="color: ${isVolumeUp ? '#ef4444' : '#22c55e'}; font-weight: bold;">
                  ${isVolumeUp ? '+' : ''}${formatVolume(volumeChange)}
                </div>
              </div>
              <div>
                <div style="color: #999; font-size: 11px;">量比幅度</div>
                <div style="color: ${isVolumeUp ? '#ef4444' : '#22c55e'}; font-weight: bold;">
                  ${isVolumeUp ? '+' : ''}${volumeChangePercent.toFixed(2)}%
                </div>
              </div>
            </div>
          </div>
        `;
      }

      // 添加量价关系分析
      const volumeLevel = currentData.volume > (prevData?.volume || 0) ? "放量" : "缩量";
      const priceLevel = isPriceUp ? "上涨" : "下跌";
      const relationship = `${volumeLevel}${priceLevel}`;
      
      let relationshipColor = "#4ecdc4";
      if (relationship === "放量上涨") relationshipColor = "#ef4444";      // 涨：红色
      else if (relationship === "放量下跌") relationshipColor = "#22c55e"; // 跌：绿色
      else if (relationship === "缩量上涨") relationshipColor = "#ff9f43";
      else if (relationship === "缩量下跌") relationshipColor = "#9b59b6";

      content += `
        <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px;">
          <div style="color: #999; font-size: 11px;">量价关系</div>
          <div style="color: ${relationshipColor}; font-weight: bold;">${relationship}</div>
        </div>
      `;

      return content;
    } catch (error) {
      console.error('成交量图表 tooltip 格式化错误:', error);
      return `<div style="color: #fff;">数据加载中...</div>`;
    }
  };
};
