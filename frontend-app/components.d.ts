/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ChartContainer: typeof import('./src/components/analysis/ChartContainer.vue')['default']
    CommonPagination: typeof import('./src/components/common/CommonPagination.vue')['default']
    CommonTable: typeof import('./src/components/common/CommonTable.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTag: typeof import('element-plus/es')['ElTag']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    Icon: typeof import('./src/components/common/Icon.vue')['default']
    IconButton: typeof import('./src/components/common/IconButton.vue')['default']
    IndicatorSettings: typeof import('./src/components/analysis/IndicatorSettings.vue')['default']
    LoadingOverlay: typeof import('./src/components/common/LoadingOverlay.vue')['default']
    Navbar: typeof import('./src/components/layout/Navbar.vue')['default']
    NotificationContainer: typeof import('./src/components/common/NotificationContainer.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScannerControl: typeof import('./src/components/scanner/ScannerControl.vue')['default']
    ScannerResults: typeof import('./src/components/scanner/ScannerResults.vue')['default']
    Sidebar: typeof import('./src/components/layout/Sidebar.vue')['default']
    StatCard: typeof import('./src/components/common/StatCard.vue')['default']
  }
  export interface GlobalDirectives {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
