/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ChartContainer: typeof import('./src/components/analysis/ChartContainer.vue')['default']
    CommonPagination: typeof import('./src/components/common/CommonPagination.vue')['default']
    CommonTable: typeof import('./src/components/common/CommonTable.vue')['default']
    DatePicker: typeof import('./src/components/scanner/DatePicker.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopconfirm: typeof import('element-plus/es')['ElPopconfirm']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    Icon: typeof import('./src/components/common/Icon.vue')['default']
    IconButton: typeof import('./src/components/common/IconButton.vue')['default']
    IndicatorSettings: typeof import('./src/components/analysis/IndicatorSettings.vue')['default']
    LoadingOverlay: typeof import('./src/components/common/LoadingOverlay.vue')['default']
    Navbar: typeof import('./src/components/layout/Navbar.vue')['default']
    NotificationContainer: typeof import('./src/components/common/NotificationContainer.vue')['default']
    ParameterSection: typeof import('./src/components/scanner/ParameterSection.vue')['default']
    PeriodIndicatorMatrix: typeof import('./src/components/scanner/PeriodIndicatorMatrix.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScanModeSelector: typeof import('./src/components/scanner/ScanModeSelector.vue')['default']
    Sidebar: typeof import('./src/components/layout/Sidebar.vue')['default']
    StatCard: typeof import('./src/components/common/StatCard.vue')['default']
  }
}
