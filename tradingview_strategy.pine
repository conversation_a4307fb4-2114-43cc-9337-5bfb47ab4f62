//@version=5
strategy("成交量压力与动量双驱策略 (Volume-Momentum Dual-Thrust)", 
         shorttitle="VMDT Strategy",
         overlay=false,
         initial_capital=100000,
         default_qty_type=strategy.percent_of_equity,
         default_qty_value=10,
         commission_type=strategy.commission.percent,
         commission_value=0.1)

// ============================================================================
// 策略说明 (Strategy Description)
// ============================================================================
//
// 本策略移植自专业量化交易系统，核心思想是通过双重验证机制：
// 1. 市场买卖情绪（成交量压力）
// 2. 价格动量（KDJ指标）
// 必须同时确认进入强势状态时，才产生买入信号
//
// 原系统数据适配说明：
// - 原系统设计支持真实内外盘数据，具备完整的内外盘计算函数
// - 系统会检查数据中是否存在 'internal_volume' 和 'external_volume' 字段
// - 如果有：使用精确公式 (外盘-内盘)/(总成交量) 然后EMA平滑
// - 如果没有：自动降级使用 volume.pct_change().ewm() 作为替代方案
// - 数据源支持情况：
//   * Mairui: ❌ 仅OHLCV基础数据
//   * AkShare: ✅ 实时快照接口包含外盘、内盘字段
//   * TuShare: ✅ get_day_all接口返回buying(外盘)/selling(内盘)
// - TradingView版本提供多种算法：原系统替代方案 + 增强智能算法
//
// 交易逻辑：
// - 买入信号：KDJ金叉 AND 成交量压力突破（双重确认）
// - 卖出信号：KDJ死叉 OR 成交量压力衰竭 OR 布林带止损（任一触发）
// ============================================================================

// ============================================================================
// 策略参数配置 (Strategy Parameters)
// ============================================================================

// KDJ 参数
kdj_n = input.int(9, title="KDJ 周期 (KDJ Period)", minval=5, maxval=50, group="KDJ 指标")
kdj_m1 = input.int(3, title="K 平滑周期 (K Smoothing)", minval=1, maxval=10, group="KDJ 指标") 
kdj_m2 = input.int(3, title="D 平滑周期 (D Smoothing)", minval=1, maxval=10, group="KDJ 指标")

// 成交量压力指标参数
volume_method = input.string("enhanced", title="成交量压力算法", options=["original_fallback", "enhanced"], group="成交量压力")
volume_ema_length = input.int(10, title="成交量压力 EMA 周期", minval=3, maxval=50, group="成交量压力")
volume_bb_length = input.int(20, title="成交量布林带周期", minval=5, maxval=50, group="成交量压力")
volume_bb_std = input.float(2.0, title="成交量布林带标准差", minval=0.5, maxval=5.0, group="成交量压力")

// 布林带参数
bb_length = input.int(20, title="价格布林带周期", minval=5, maxval=50, group="布林带")
bb_std = input.float(2.0, title="价格布林带标准差", minval=0.5, maxval=5.0, group="布林带")

// 策略开关
use_kdj = input.bool(true, title="启用 KDJ 指标", group="信号过滤")
use_volume_pressure = input.bool(true, title="启用成交量压力", group="信号过滤")
use_bollinger_stop = input.bool(true, title="启用布林带止损", group="信号过滤")

// 显示选项
show_indicators = input.bool(true, title="显示指标线", group="显示设置")
show_signals = input.bool(true, title="显示买卖信号", group="显示设置")

// ============================================================================
// 技术指标计算 (Technical Indicators)
// ============================================================================

// 1. KDJ 指标计算 (Standard KDJ Calculation)
calc_kdj(src_high, src_low, src_close, n, m1, m2) =>
    // 计算 RSV (Raw Stochastic Value)
    lowest_low = ta.lowest(src_low, n)
    highest_high = ta.highest(src_high, n)
    rsv = 100 * (src_close - lowest_low) / (highest_high - lowest_low)
    
    // 标准 KDJ 算法：K = 2/3 * K_prev + 1/3 * RSV
    var float k = 50.0  // 初始值
    var float d = 50.0  // 初始值
    
    k := na(rsv) ? k : (2.0/3.0) * nz(k[1], 50) + (1.0/3.0) * rsv
    d := (2.0/3.0) * nz(d[1], 50) + (1.0/3.0) * k
    j = 3 * k - 2 * d
    
    [k, d, j]

// 计算 KDJ 指标
[kdj_k, kdj_d, kdj_j] = calc_kdj(high, low, close, kdj_n, kdj_m1, kdj_m2)

// 2. 成交量压力指标计算 (Volume Pressure Indicator)
// 
// 原系统设计说明：
// - 原系统优先使用真实的内外盘数据：(外盘成交量 - 内盘成交量) / 总成交量，然后EMA平滑
// - 当无法获取内外盘数据时，才使用替代方案：成交量变化率的EMA平滑
// - TradingView无法获取内外盘数据，因此使用改进的替代算法
//
// 改进的替代算法说明：
// 1. 基于价格和成交量的关系推算买卖力量
// 2. 价格上涨+高成交量 = 强买盘，价格下跌+高成交量 = 强卖盘
// 3. 结合原系统的EMA平滑逻辑
calc_volume_pressure_original(internal_vol, external_vol, ema_len) =>
    // 原系统的真实内外盘计算方法（当有数据时）
    volume_diff = external_vol - internal_vol
    total_volume = internal_vol + external_vol
    normalized_diff = volume_diff / (total_volume + 1e-8)
    ta.ema(normalized_diff, ema_len)

calc_volume_pressure_fallback(vol, price_change, ema_len) =>
    // 原系统的替代方案：成交量变化率的EMA平滑
    volume_pct_change = ta.change(vol) / (vol[1] + 1e-8)
    ta.ema(volume_pct_change, ema_len)

calc_volume_pressure_enhanced(price_change, vol, close_change_pct, ema_len) =>
    // TradingView增强替代算法：结合价格变化和成交量
    // 更好地模拟内外盘力量对比
    
    // 计算价格强度指标
    price_strength = price_change / (close[1] + 1e-8)
    
    // 计算成交量相对强度
    vol_avg = ta.sma(vol, 20)
    vol_relative = (vol - vol_avg) / (vol_avg + 1e-8)
    
    // 结合价格方向和成交量强度
    // 上涨+放量 = 买盘强，下跌+放量 = 卖盘强
    buying_pressure = price_change > 0 ? vol_relative * math.abs(price_strength) : 0
    selling_pressure = price_change < 0 ? vol_relative * math.abs(price_strength) : 0
    
    // 计算净压力（类似外盘-内盘）
    net_pressure = buying_pressure - selling_pressure
    
    // EMA平滑处理（与原系统保持一致）
    ta.ema(net_pressure, ema_len)

// 根据用户选择使用相应的算法
price_change = close - close[1]
close_change_pct = (close - close[1]) / close[1] * 100

log_vol = switch volume_method
    "original_fallback" => calc_volume_pressure_fallback(volume, price_change, volume_ema_length)
    "enhanced" => calc_volume_pressure_enhanced(price_change, volume, close_change_pct, volume_ema_length)
    => calc_volume_pressure_enhanced(price_change, volume, close_change_pct, volume_ema_length)

// 3. 成交量布林带 (Volume Bollinger Bands)
[vol_bb_upper, vol_bb_middle, vol_bb_lower] = ta.bb(log_vol, volume_bb_length, volume_bb_std)

// 4. 价格布林带 (Price Bollinger Bands)
[price_bb_upper, price_bb_middle, price_bb_lower] = ta.bb(close, bb_length, bb_std)

// ============================================================================
// 交易信号逻辑 (Trading Signal Logic)
// ============================================================================
//
// 原系统信号逻辑完全还原：
// 1. 买入信号：严格的AND逻辑 - 所有条件必须同时满足
// 2. 卖出信号：宽松的OR逻辑 - 任一条件满足即可退出
// 3. 布林带止损：最高优先级，强制止损保护
// ============================================================================

// KDJ 金叉和死叉检测（原系统标准）
kdj_golden_cross = ta.crossover(kdj_k, kdj_d) and use_kdj
kdj_death_cross = ta.crossunder(kdj_k, kdj_d) and use_kdj

// 成交量压力突破和衰竭检测（原系统标准）
// 突破：成交量压力指标向上穿过其布林带中轨
// 衰竭：成交量压力指标向下穿过其布林带中轨
volume_breakout = ta.crossover(log_vol, vol_bb_middle) and use_volume_pressure
volume_exhaustion = ta.crossunder(log_vol, vol_bb_middle) and use_volume_pressure

// 布林带止损条件（原系统标准）
// 收盘价跌破价格布林带下轨即触发止损
bollinger_stop_loss = close < price_bb_lower and use_bollinger_stop

// 买入条件构建（严格AND逻辑）
buy_condition_kdj = not use_kdj or kdj_golden_cross
buy_condition_volume = not use_volume_pressure or volume_breakout

// 完整买入信号：必须同时满足所有启用的条件
// 这确保了高精度的买入时机选择
buy_signal = buy_condition_kdj and buy_condition_volume

// 卖出条件构建（宽松OR逻辑）  
sell_condition_kdj = use_kdj and kdj_death_cross
sell_condition_volume = use_volume_pressure and volume_exhaustion
sell_condition_stop = use_bollinger_stop and bollinger_stop_loss

// 完整卖出信号：任一条件满足即可卖出
// 这确保了及时的风险控制和利润保护
sell_signal = sell_condition_kdj or sell_condition_volume or sell_condition_stop

// ============================================================================
// 策略执行 (Strategy Execution)
// ============================================================================

// 执行买入
if buy_signal
    strategy.entry("Long", strategy.long, comment="买入信号")

// 执行卖出
if sell_signal
    strategy.close("Long", comment="卖出信号")

// ============================================================================
// 图表显示 (Chart Display)
// ============================================================================

if show_indicators
    // 绘制 KDJ 指标
    plot(kdj_k, title="KDJ K", color=color.blue, linewidth=2)
    plot(kdj_d, title="KDJ D", color=color.red, linewidth=2)
    plot(kdj_j, title="KDJ J", color=color.orange, linewidth=1)
    
    // 绘制成交量压力指标
    plot(log_vol, title="成交量压力", color=color.purple, linewidth=2)
    plot(vol_bb_middle, title="成交量中轨", color=color.gray, linewidth=1)
    plot(vol_bb_upper, title="成交量上轨", color=color.gray, linewidth=1, linestyle=line.style_dotted)
    plot(vol_bb_lower, title="成交量下轨", color=color.gray, linewidth=1, linestyle=line.style_dotted)
    
    // 添加水平参考线
    hline(80, title="超买线", color=color.red, linestyle=hline.style_dashed)
    hline(20, title="超卖线", color=color.green, linestyle=hline.style_dashed)
    hline(50, title="中性线", color=color.gray, linestyle=hline.style_dotted)

// 信号标记
if show_signals
    plotshape(buy_signal, title="买入信号", location=location.bottom, 
              color=color.new(color.green, 0), style=shape.labelup, 
              text="BUY", textcolor=color.white, size=size.small)
    
    plotshape(sell_signal, title="卖出信号", location=location.top, 
              color=color.new(color.red, 0), style=shape.labeldown, 
              text="SELL", textcolor=color.white, size=size.small)

// ============================================================================
// 策略信息显示 (Strategy Information)
// ============================================================================

// 创建信息表格
if barstate.islast
    var table info_table = table.new(position.top_right, 2, 10, 
                                   bgcolor=color.new(color.white, 80), 
                                   border_width=1)
    
    table.cell(info_table, 0, 0, "策略名称", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1, 0, "成交量压力与动量双驱", text_color=color.black, text_size=size.small)
    
    table.cell(info_table, 0, 1, "数据源", text_color=color.black, text_size=size.small)
    data_source_text = volume_method == "original_fallback" ? "TradingView (原系统替代)" : "TradingView (智能增强)"
    table.cell(info_table, 1, 1, data_source_text, text_color=color.blue, text_size=size.small)
    
    table.cell(info_table, 0, 2, "KDJ K值", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1, 2, str.tostring(kdj_k, "#.##"), text_color=color.black, text_size=size.small)
    
    table.cell(info_table, 0, 3, "KDJ D值", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1, 3, str.tostring(kdj_d, "#.##"), text_color=color.black, text_size=size.small)
    
    table.cell(info_table, 0, 4, "成交量压力", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1, 4, str.tostring(log_vol, "#.####"), text_color=color.black, text_size=size.small)
    
    table.cell(info_table, 0, 5, "压力中轨", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1, 5, str.tostring(vol_bb_middle, "#.####"), text_color=color.black, text_size=size.small)
    
    table.cell(info_table, 0, 6, "当前价格", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1, 6, str.tostring(close, "#.##"), text_color=color.black, text_size=size.small)
    
    table.cell(info_table, 0, 7, "布林下轨", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1, 7, str.tostring(price_bb_lower, "#.##"), text_color=color.black, text_size=size.small)
    
    table.cell(info_table, 0, 8, "信号状态", text_color=color.black, text_size=size.small)
    signal_text = buy_signal ? "买入" : sell_signal ? "卖出" : "观望"
    signal_color = buy_signal ? color.green : sell_signal ? color.red : color.gray
    table.cell(info_table, 1, 8, signal_text, text_color=signal_color, text_size=size.small)
    
    table.cell(info_table, 0, 9, "算法版本", text_color=color.black, text_size=size.small)
    algorithm_text = volume_method == "original_fallback" ? "原系统替代算法" : "TradingView增强算法"
    algorithm_color = volume_method == "original_fallback" ? color.orange : color.purple
    table.cell(info_table, 1, 9, algorithm_text, text_color=algorithm_color, text_size=size.small)