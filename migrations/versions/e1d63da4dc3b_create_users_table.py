"""create users table

Revision ID: e1d63da4dc3b
Revises: 4f529e8152f5
Create Date: 2025-08-13 22:12:56.554202

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e1d63da4dc3b'
down_revision: Union[str, None] = '4f529e8152f5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('users',
    sa.Column('username', sa.String(length=50), nullable=False, comment='用户名'),
    sa.Column('password_hash', sa.String(length=128), nullable=False, comment='密码哈希'),
    sa.Column('email', sa.String(length=100), nullable=True, comment='邮箱'),
    sa.<PERSON>umn('is_active', sa.<PERSON>(), nullable=True, comment='是否激活'),
    sa.Column('is_admin', sa.<PERSON>(), nullable=True, comment='是否是管理员'),
    sa.Column('last_login', sa.DateTime(), nullable=True, comment='最后登录时间'),
    sa.Column('token_version', sa.Integer(), nullable=True, comment='JWT token版本号，用于强制过期'),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_users'))
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_table('stock_monthly',
    sa.Column('stock_code', sa.String(length=10), nullable=False),
    sa.Column('trade_date', sa.Date(), nullable=False),
    sa.Column('open', sa.Float(), nullable=False),
    sa.Column('high', sa.Float(), nullable=False),
    sa.Column('low', sa.Float(), nullable=False),
    sa.Column('close', sa.Float(), nullable=False),
    sa.Column('volume', sa.BigInteger(), nullable=False),
    sa.Column('amount', sa.BigInteger(), nullable=True),
    sa.Column('change_pct', sa.Float(), nullable=True),
    sa.Column('turnover_rate', sa.Float(), nullable=True),
    sa.Column('limit_status', sa.String(length=1), nullable=True),
    sa.Column('is_st', sa.Boolean(), nullable=False),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['stock_code'], ['stock_info.code'], name=op.f('fk_stock_monthly_stock_code_stock_info')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_stock_monthly')),
    sa.UniqueConstraint('stock_code', 'trade_date', name='uix_stock_monthly_code_date')
    )
    op.create_index('ix_stock_monthly_code_date', 'stock_monthly', ['stock_code', 'trade_date'], unique=False)
    op.create_index('ix_stock_monthly_date', 'stock_monthly', ['trade_date'], unique=False)
    op.create_index(op.f('ix_stock_monthly_stock_code'), 'stock_monthly', ['stock_code'], unique=False)
    op.create_table('stock_weekly',
    sa.Column('stock_code', sa.String(length=10), nullable=False),
    sa.Column('trade_date', sa.Date(), nullable=False),
    sa.Column('open', sa.Float(), nullable=False),
    sa.Column('high', sa.Float(), nullable=False),
    sa.Column('low', sa.Float(), nullable=False),
    sa.Column('close', sa.Float(), nullable=False),
    sa.Column('volume', sa.BigInteger(), nullable=False),
    sa.Column('amount', sa.BigInteger(), nullable=True),
    sa.Column('change_pct', sa.Float(), nullable=True),
    sa.Column('turnover_rate', sa.Float(), nullable=True),
    sa.Column('limit_status', sa.String(length=1), nullable=True),
    sa.Column('is_st', sa.Boolean(), nullable=False),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['stock_code'], ['stock_info.code'], name=op.f('fk_stock_weekly_stock_code_stock_info')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_stock_weekly')),
    sa.UniqueConstraint('stock_code', 'trade_date', name='uix_stock_weekly_code_date')
    )
    op.create_index('ix_stock_weekly_code_date', 'stock_weekly', ['stock_code', 'trade_date'], unique=False)
    op.create_index('ix_stock_weekly_date', 'stock_weekly', ['trade_date'], unique=False)
    op.create_index(op.f('ix_stock_weekly_stock_code'), 'stock_weekly', ['stock_code'], unique=False)
    op.create_table('user_scheduled_tasks',
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False, comment='任务名称'),
    sa.Column('task_type', sa.String(length=50), nullable=False, comment='任务类型'),
    sa.Column('cron_expression', sa.String(length=100), nullable=False, comment='Cron表达式'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否启用'),
    sa.Column('max_executions', sa.Integer(), nullable=True, comment='最大执行次数'),
    sa.Column('current_executions', sa.Integer(), nullable=False, comment='当前已执行次数'),
    sa.Column('task_config', sa.Text(), nullable=False, comment='任务配置JSON'),
    sa.Column('description', sa.String(length=500), nullable=True, comment='任务描述'),
    sa.Column('last_execution', sa.DateTime(), nullable=True, comment='最后执行时间'),
    sa.Column('next_execution', sa.DateTime(), nullable=True, comment='下次执行时间'),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('fk_user_scheduled_tasks_user_id_users')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_user_scheduled_tasks'))
    )
    op.create_index(op.f('ix_user_scheduled_tasks_is_active'), 'user_scheduled_tasks', ['is_active'], unique=False)
    op.create_index(op.f('ix_user_scheduled_tasks_next_execution'), 'user_scheduled_tasks', ['next_execution'], unique=False)
    op.create_index(op.f('ix_user_scheduled_tasks_user_id'), 'user_scheduled_tasks', ['user_id'], unique=False)
    op.create_table('watchlist_items',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
    sa.Column('stock_code', sa.String(length=10), nullable=False, comment='股票代码'),
    sa.Column('added_at', sa.DateTime(), nullable=True, comment='添加时间'),
    sa.Column('sort_order', sa.Integer(), nullable=True, comment='排序顺序'),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('fk_watchlist_items_user_id_users')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_watchlist_items')),
    sa.UniqueConstraint('user_id', 'stock_code', name='uix_user_stock')
    )
    op.create_index(op.f('ix_watchlist_items_id'), 'watchlist_items', ['id'], unique=False)
    op.create_index(op.f('ix_watchlist_items_stock_code'), 'watchlist_items', ['stock_code'], unique=False)
    op.create_index(op.f('ix_watchlist_items_user_id'), 'watchlist_items', ['user_id'], unique=False)
    op.create_table('task_executions',
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('scheduled_task_id', sa.Integer(), nullable=True),
    sa.Column('trigger_type', sa.String(length=20), nullable=False, comment='触发类型'),
    sa.Column('task_type', sa.String(length=50), nullable=False, comment='任务类型'),
    sa.Column('task_config', sa.Text(), nullable=False, comment='任务配置JSON'),
    sa.Column('status', sa.String(length=20), nullable=False, comment='执行状态'),
    sa.Column('start_time', sa.DateTime(), nullable=True, comment='开始时间'),
    sa.Column('end_time', sa.DateTime(), nullable=True, comment='结束时间'),
    sa.Column('duration_seconds', sa.Integer(), nullable=True, comment='执行时长'),
    sa.Column('results_count', sa.Integer(), nullable=False, comment='结果数量'),
    sa.Column('progress_current', sa.Integer(), nullable=False, comment='当前进度'),
    sa.Column('progress_total', sa.Integer(), nullable=False, comment='总进度'),
    sa.Column('progress_percentage', sa.Float(), nullable=False, comment='进度百分比'),
    sa.Column('progress_message', sa.String(length=200), nullable=True, comment='进度消息'),
    sa.Column('error_message', sa.Text(), nullable=True, comment='错误信息'),
    sa.Column('results_data', sa.Text(), nullable=True, comment='执行结果JSON'),
    sa.Column('task_metadata', sa.Text(), nullable=True, comment='扩展元数据JSON'),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['scheduled_task_id'], ['user_scheduled_tasks.id'], name=op.f('fk_task_executions_scheduled_task_id_user_scheduled_tasks')),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('fk_task_executions_user_id_users')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_task_executions'))
    )
    op.create_index(op.f('ix_task_executions_scheduled_task_id'), 'task_executions', ['scheduled_task_id'], unique=False)
    op.create_index(op.f('ix_task_executions_start_time'), 'task_executions', ['start_time'], unique=False)
    op.create_index(op.f('ix_task_executions_status'), 'task_executions', ['status'], unique=False)
    op.create_index(op.f('ix_task_executions_trigger_type'), 'task_executions', ['trigger_type'], unique=False)
    op.create_index(op.f('ix_task_executions_user_id'), 'task_executions', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_task_executions_user_id'), table_name='task_executions')
    op.drop_index(op.f('ix_task_executions_trigger_type'), table_name='task_executions')
    op.drop_index(op.f('ix_task_executions_status'), table_name='task_executions')
    op.drop_index(op.f('ix_task_executions_start_time'), table_name='task_executions')
    op.drop_index(op.f('ix_task_executions_scheduled_task_id'), table_name='task_executions')
    op.drop_table('task_executions')
    op.drop_index(op.f('ix_watchlist_items_user_id'), table_name='watchlist_items')
    op.drop_index(op.f('ix_watchlist_items_stock_code'), table_name='watchlist_items')
    op.drop_index(op.f('ix_watchlist_items_id'), table_name='watchlist_items')
    op.drop_table('watchlist_items')
    op.drop_index(op.f('ix_user_scheduled_tasks_user_id'), table_name='user_scheduled_tasks')
    op.drop_index(op.f('ix_user_scheduled_tasks_next_execution'), table_name='user_scheduled_tasks')
    op.drop_index(op.f('ix_user_scheduled_tasks_is_active'), table_name='user_scheduled_tasks')
    op.drop_table('user_scheduled_tasks')
    op.drop_index(op.f('ix_stock_weekly_stock_code'), table_name='stock_weekly')
    op.drop_index('ix_stock_weekly_date', table_name='stock_weekly')
    op.drop_index('ix_stock_weekly_code_date', table_name='stock_weekly')
    op.drop_table('stock_weekly')
    op.drop_index(op.f('ix_stock_monthly_stock_code'), table_name='stock_monthly')
    op.drop_index('ix_stock_monthly_date', table_name='stock_monthly')
    op.drop_index('ix_stock_monthly_code_date', table_name='stock_monthly')
    op.drop_table('stock_monthly')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    # ### end Alembic commands ###
