"""Add user scheduled tasks and task execution tables

Revision ID: fa8234895555
Revises: aada10839d05
Create Date: 2025-08-06 17:18:15.533528

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'fa8234895555'
down_revision: Union[str, None] = 'aada10839d05'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_scheduled_tasks',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False, comment='任务名称'),
    sa.Column('task_type', sa.String(length=50), nullable=False, comment='任务类型'),
    sa.Column('cron_expression', sa.String(length=100), nullable=False, comment='Cron表达式'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否启用'),
    sa.Column('max_executions', sa.Integer(), nullable=True, comment='最大执行次数'),
    sa.Column('current_executions', sa.Integer(), nullable=False, comment='当前已执行次数'),
    sa.Column('task_config', sa.Text(), nullable=False, comment='任务配置JSON'),
    sa.Column('description', sa.String(length=500), nullable=True, comment='任务描述'),
    sa.Column('last_execution', sa.DateTime(), nullable=True, comment='最后执行时间'),
    sa.Column('next_execution', sa.DateTime(), nullable=True, comment='下次执行时间'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_scheduled_tasks_is_active'), 'user_scheduled_tasks', ['is_active'], unique=False)
    op.create_index(op.f('ix_user_scheduled_tasks_next_execution'), 'user_scheduled_tasks', ['next_execution'], unique=False)
    op.create_index(op.f('ix_user_scheduled_tasks_user_id'), 'user_scheduled_tasks', ['user_id'], unique=False)
    
    op.create_table('task_executions',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('scheduled_task_id', sa.Integer(), nullable=True),
    sa.Column('trigger_type', sa.String(length=20), nullable=False, comment='触发类型'),
    sa.Column('task_type', sa.String(length=50), nullable=False, comment='任务类型'),
    sa.Column('task_config', sa.Text(), nullable=False, comment='任务配置JSON'),
    sa.Column('status', sa.String(length=20), nullable=False, comment='执行状态'),
    sa.Column('start_time', sa.DateTime(), nullable=True, comment='开始时间'),
    sa.Column('end_time', sa.DateTime(), nullable=True, comment='结束时间'),
    sa.Column('duration_seconds', sa.Integer(), nullable=True, comment='执行时长'),
    sa.Column('results_count', sa.Integer(), nullable=False, comment='结果数量'),
    sa.Column('error_message', sa.Text(), nullable=True, comment='错误信息'),
    sa.Column('results_data', sa.Text(), nullable=True, comment='执行结果JSON'),
    sa.Column('task_metadata', sa.Text(), nullable=True, comment='扩展元数据JSON'),
    sa.ForeignKeyConstraint(['scheduled_task_id'], ['user_scheduled_tasks.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_task_executions_scheduled_task_id'), 'task_executions', ['scheduled_task_id'], unique=False)
    op.create_index(op.f('ix_task_executions_start_time'), 'task_executions', ['start_time'], unique=False)
    op.create_index(op.f('ix_task_executions_status'), 'task_executions', ['status'], unique=False)
    op.create_index(op.f('ix_task_executions_trigger_type'), 'task_executions', ['trigger_type'], unique=False)
    op.create_index(op.f('ix_task_executions_user_id'), 'task_executions', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_task_executions_user_id'), table_name='task_executions')
    op.drop_index(op.f('ix_task_executions_trigger_type'), table_name='task_executions')
    op.drop_index(op.f('ix_task_executions_status'), table_name='task_executions')
    op.drop_index(op.f('ix_task_executions_start_time'), table_name='task_executions')
    op.drop_index(op.f('ix_task_executions_scheduled_task_id'), table_name='task_executions')
    op.drop_table('task_executions')
    op.drop_index(op.f('ix_user_scheduled_tasks_user_id'), table_name='user_scheduled_tasks')
    op.drop_index(op.f('ix_user_scheduled_tasks_next_execution'), table_name='user_scheduled_tasks')
    op.drop_index(op.f('ix_user_scheduled_tasks_is_active'), table_name='user_scheduled_tasks')
    op.drop_table('user_scheduled_tasks')
    # ### end Alembic commands ###
