"""remove_partitioning_add_unified_stock_daily

Revision ID: 17d7f17a61c1
Revises: 10389f6b60c5
Create Date: 2025-07-30 17:02:13.628210

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '17d7f17a61c1'
down_revision: Union[str, None] = '10389f6b60c5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema: 移除分区表，创建统一的stock_daily表."""
    
    # 安全删除已存在的表（确保迁移的幂等性）
    try:
        op.drop_table('stock_daily')
        print("删除了已存在的stock_daily表")
    except Exception:
        # 表不存在，继续创建
        print("stock_daily表不存在，准备创建")
    
    # 创建统一的股票日线数据表，直接包含所有约束
    op.create_table(
        'stock_daily',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('stock_code', sa.String(length=10), nullable=False),
        sa.Column('trade_date', sa.Date(), nullable=False),
        sa.Column('open', sa.Float(), nullable=False),
        sa.Column('high', sa.Float(), nullable=False),
        sa.Column('low', sa.Float(), nullable=False),
        sa.Column('close', sa.Float(), nullable=False),
        sa.Column('volume', sa.BigInteger(), nullable=False),
        sa.Column('amount', sa.BigInteger(), nullable=True),
        sa.Column('change_pct', sa.Float(), nullable=True),
        sa.Column('turnover_rate', sa.Float(), nullable=True),
        sa.Column('limit_status', sa.String(length=1), nullable=True),
        sa.Column('is_st', sa.Boolean(), nullable=False, server_default='0'),
        # 外键约束
        sa.ForeignKeyConstraint(['stock_code'], ['stock_info.code'], name=op.f('fk_stock_daily_stock_code_stock_info')),
        # 主键约束
        sa.PrimaryKeyConstraint('id', name=op.f('pk_stock_daily')),
        # 唯一约束：防止同一股票同一日期的重复数据
        sa.UniqueConstraint('stock_code', 'trade_date', name='uix_stock_daily_code_date')
    )
    
    # 创建优化的索引
    # 主联合索引：优化按股票代码和时间范围查询
    op.create_index('ix_stock_daily_code_date', 'stock_daily', ['stock_code', 'trade_date'])
    
    # 辅助索引：优化按日期查询全市场数据
    op.create_index('ix_stock_daily_date', 'stock_daily', ['trade_date'])
    
    # 基础索引：优化按股票代码查询
    op.create_index(op.f('ix_stock_daily_stock_code'), 'stock_daily', ['stock_code'])
    
    # 注意: 如果存在分区表数据需要迁移，可以在这里添加数据迁移逻辑
    # 目前由于分区表是动态创建的，我们暂时不处理数据迁移
    # 在生产环境中，应该先将分区表数据合并到unified表中
    
    print("已创建统一的stock_daily表，移除了分区表策略")


def downgrade() -> None:
    """Downgrade schema: 删除统一表，恢复分区表策略."""
    # 安全删除统一的股票日线数据表
    try:
        op.drop_table('stock_daily')
        print("已删除统一的stock_daily表")
    except Exception as e:
        print(f"删除stock_daily表时出错（可能已不存在）: {e}")
    
    # 注意：此downgrade不会重新创建分区表
    # 如需完全回滚，请手动创建分区表或使用备份数据
