"""Add progress tracking fields to TaskExecution

Revision ID: 4f529e8152f5
Revises: fa8234895555
Create Date: 2025-08-07 02:06:25.348072

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4f529e8152f5'
down_revision: Union[str, None] = 'fa8234895555'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('task_executions', sa.Column('progress_current', sa.Integer(), nullable=False, server_default='0', comment='当前进度'))
    op.add_column('task_executions', sa.Column('progress_total', sa.Integer(), nullable=False, server_default='0', comment='总进度'))
    op.add_column('task_executions', sa.Column('progress_percentage', sa.Float(), nullable=False, server_default='0.0', comment='进度百分比'))
    op.add_column('task_executions', sa.Column('progress_message', sa.String(length=200), nullable=True, comment='进度消息'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('task_executions', 'progress_message')
    op.drop_column('task_executions', 'progress_percentage')
    op.drop_column('task_executions', 'progress_total')
    op.drop_column('task_executions', 'progress_current')
    # ### end Alembic commands ###
