"""add token_version to users table for JWT authentication

Revision ID: aada10839d05
Revises: d91b7ce25f01
Create Date: 2025-08-06 14:04:20.922879

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'aada10839d05'
down_revision: Union[str, None] = 'd91b7ce25f01'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # 添加 token_version 字段到 users 表，默认值为0，不能为空
    op.add_column('users', sa.Column('token_version', sa.Integer(), nullable=False, default=0, comment='JWT token版本号，用于强制过期'))


def downgrade() -> None:
    """Downgrade schema."""
    # 删除 token_version 字段
    op.drop_column('users', 'token_version')