"""添加指数数据表结构

Revision ID: d91b7ce25f01
Revises: 9607a975fefd
Create Date: 2025-07-31 17:04:09.405690

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd91b7ce25f01'
down_revision: Union[str, None] = '9607a975fefd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('index_info',
    sa.Column('code', sa.String(length=20), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('exchange', sa.String(length=10), nullable=False),
    sa.Column('is_active', sa.<PERSON>(), nullable=False),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_index_info'))
    )
    op.create_index(op.f('ix_index_info_code'), 'index_info', ['code'], unique=True)
    op.create_table('index_daily',
    sa.Column('index_code', sa.String(length=20), nullable=False),
    sa.Column('trade_date', sa.Date(), nullable=False),
    sa.Column('open', sa.Float(), nullable=False),
    sa.Column('high', sa.Float(), nullable=False),
    sa.Column('low', sa.Float(), nullable=False),
    sa.Column('close', sa.Float(), nullable=False),
    sa.Column('volume', sa.BigInteger(), nullable=False),
    sa.Column('amount', sa.BigInteger(), nullable=True),
    sa.Column('prev_close', sa.Float(), nullable=True),
    sa.Column('change_pct', sa.Float(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['index_code'], ['index_info.code'], name=op.f('fk_index_daily_index_code_index_info')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_index_daily')),
    sa.UniqueConstraint('index_code', 'trade_date', name='uix_index_daily_code_date')
    )
    op.create_index('ix_index_daily_code_date', 'index_daily', ['index_code', 'trade_date'], unique=False)
    op.create_index('ix_index_daily_date', 'index_daily', ['trade_date'], unique=False)
    op.create_index(op.f('ix_index_daily_index_code'), 'index_daily', ['index_code'], unique=False)
    op.create_table('index_monthly',
    sa.Column('index_code', sa.String(length=20), nullable=False),
    sa.Column('trade_date', sa.Date(), nullable=False),
    sa.Column('open', sa.Float(), nullable=False),
    sa.Column('high', sa.Float(), nullable=False),
    sa.Column('low', sa.Float(), nullable=False),
    sa.Column('close', sa.Float(), nullable=False),
    sa.Column('volume', sa.BigInteger(), nullable=False),
    sa.Column('amount', sa.BigInteger(), nullable=True),
    sa.Column('prev_close', sa.Float(), nullable=True),
    sa.Column('change_pct', sa.Float(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['index_code'], ['index_info.code'], name=op.f('fk_index_monthly_index_code_index_info')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_index_monthly')),
    sa.UniqueConstraint('index_code', 'trade_date', name='uix_index_monthly_code_date')
    )
    op.create_index('ix_index_monthly_code_date', 'index_monthly', ['index_code', 'trade_date'], unique=False)
    op.create_index('ix_index_monthly_date', 'index_monthly', ['trade_date'], unique=False)
    op.create_index(op.f('ix_index_monthly_index_code'), 'index_monthly', ['index_code'], unique=False)
    op.create_table('index_weekly',
    sa.Column('index_code', sa.String(length=20), nullable=False),
    sa.Column('trade_date', sa.Date(), nullable=False),
    sa.Column('open', sa.Float(), nullable=False),
    sa.Column('high', sa.Float(), nullable=False),
    sa.Column('low', sa.Float(), nullable=False),
    sa.Column('close', sa.Float(), nullable=False),
    sa.Column('volume', sa.BigInteger(), nullable=False),
    sa.Column('amount', sa.BigInteger(), nullable=True),
    sa.Column('prev_close', sa.Float(), nullable=True),
    sa.Column('change_pct', sa.Float(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['index_code'], ['index_info.code'], name=op.f('fk_index_weekly_index_code_index_info')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_index_weekly')),
    sa.UniqueConstraint('index_code', 'trade_date', name='uix_index_weekly_code_date')
    )
    op.create_index('ix_index_weekly_code_date', 'index_weekly', ['index_code', 'trade_date'], unique=False)
    op.create_index('ix_index_weekly_date', 'index_weekly', ['trade_date'], unique=False)
    op.create_index(op.f('ix_index_weekly_index_code'), 'index_weekly', ['index_code'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_index_weekly_index_code'), table_name='index_weekly')
    op.drop_index('ix_index_weekly_date', table_name='index_weekly')
    op.drop_index('ix_index_weekly_code_date', table_name='index_weekly')
    op.drop_table('index_weekly')
    op.drop_index(op.f('ix_index_monthly_index_code'), table_name='index_monthly')
    op.drop_index('ix_index_monthly_date', table_name='index_monthly')
    op.drop_index('ix_index_monthly_code_date', table_name='index_monthly')
    op.drop_table('index_monthly')
    op.drop_index(op.f('ix_index_daily_index_code'), table_name='index_daily')
    op.drop_index('ix_index_daily_date', table_name='index_daily')
    op.drop_index('ix_index_daily_code_date', table_name='index_daily')
    op.drop_table('index_daily')
    op.drop_index(op.f('ix_index_info_code'), table_name='index_info')
    op.drop_table('index_info')
    # ### end Alembic commands ###
