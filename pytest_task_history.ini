# 统一任务结果展示功能 - 全面测试配置

[pytest]
# 基本配置
minversion = 6.0
testpaths = 
    tests/unit/services/tasks
    tests/unit/frontend
    tests/integration/api/test_scheduled_tasks_api.py
    tests/integration/api/test_task_executions_api.py
    tests/integration/api/test_task_executions_admin_api.py
    tests/integration/test_scheduled_tasks_e2e.py
    tests/integration/test_task_history_e2e.py
    tests/performance/test_task_execution_performance.py

# 测试标记定义
markers =
    unit: 单元测试 - 测试独立的功能模块
    integration: 集成测试 - 测试模块间交互
    e2e: 端到端测试 - 测试完整业务流程
    performance: 性能测试 - 测试系统性能和负载能力
    frontend: 前端测试 - 测试前端逻辑和交互
    admin: 管理员功能测试 - 测试管理员相关功能
    api: API接口测试 - 测试REST API端点
    database: 数据库测试 - 测试数据持久化
    auth: 权限测试 - 测试认证和授权
    error: 错误处理测试 - 测试异常场景
    slow: 运行时间较长的测试
    asyncio: 异步测试

# 异步测试支持
asyncio_mode = auto

# 测试发现模式
python_files = test_*.py *_test.py
python_classes = Test* *Test *Tests
python_functions = test_*

# 输出和报告配置
addopts = 
    --strict-markers
    --strict-config
    --tb=short
    --disable-warnings
    -ra
    --maxfail=50
    --durations=10
    --cov=app
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-fail-under=75

# 并发测试配置
# 注意：需要安装 pytest-xdist
# addopts += -n auto

# 测试数据目录
testmon_datafile = .testmondata

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:pydantic.*
    ignore::pytest.PytestUnraisableExceptionWarning
    ignore::sqlalchemy.exc.SAWarning

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 环境变量
env = 
    TESTING = true
    DATABASE_URL = sqlite:///./test.db
    LOG_LEVEL = DEBUG
    API_HOST = 127.0.0.1
    API_PORT = 8888