# 统一数据管理系统 API 规范

## API 概述

统一数据管理系统提供RESTful API接口，支持多种数据访问模式：查询、订阅、推送、流式和批量处理。API设计遵循OpenAPI 3.0规范，提供完整的类型安全和自动文档生成。

### API 设计原则

1. **统一性**: 所有数据类型使用相同的接口模式
2. **一致性**: 统一的请求/响应格式和错误处理
3. **可扩展性**: 支持新数据类型和功能扩展
4. **向后兼容**: 版本化API确保平滑升级
5. **开发者友好**: 清晰的文档和SDK支持

## API 基础信息

```yaml
openapi: 3.0.3
info:
  title: 统一数据管理系统 API
  version: 2.0.0
  description: |
    高性能、可扩展的统一数据管理系统API
    
    ## 功能特性
    - 统一数据接口：一个方法解决所有数据获取需求
    - 多级缓存：L1内存→L2Redis→数据库→API智能路由  
    - 实时数据：WebSocket推送 + Server-Sent Events
    - 批量处理：高效的批量数据获取和处理
    - 插件扩展：零代码修改接入新数据源
    
    ## 性能指标
    - 响应时间: < 50ms (缓存命中)
    - 吞吐量: 1000+ QPS
    - 可用性: 99.9%+
    
  contact:
    name: 开发团队
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.udm.example.com/v2
    description: 生产环境
  - url: https://staging-api.udm.example.com/v2  
    description: 测试环境
  - url: http://localhost:8000/api/v2
    description: 开发环境

security:
  - ApiKeyAuth: []
  - BearerAuth: []
```

## 核心数据接口

### 1. 统一数据获取接口

这是系统的核心接口，提供统一的数据访问入口。

```yaml
paths:
  /data/{data_type}:
    get:
      summary: 统一数据获取
      description: |
        通过单一接口获取所有类型的数据，支持智能缓存和故障转移。
        
        ## 支持的数据类型
        - `stock_basic`: 股票基本信息
        - `daily_data`: 日线数据  
        - `realtime_data`: 实时行情
        - `indicators`: 技术指标
        - `financial_data`: 财务数据
        - `news_data`: 新闻资讯
        
        ## 缓存策略
        系统自动选择最优数据源：L1内存 → L2Redis → 数据库 → 外部API
        
      operationId: getData
      tags:
        - 核心数据接口
      parameters:
        - name: data_type
          in: path
          required: true
          description: 数据类型标识符
          schema:
            type: string
            enum: [stock_basic, daily_data, realtime_data, indicators, financial_data, news_data]
            example: "daily_data"
            
        - name: symbol
          in: query
          required: true
          description: 股票代码（支持多种格式）
          schema:
            type: string
            pattern: '^[A-Z0-9]{6}$|^[A-Z0-9]{6}\.[A-Z]{2}$'
            example: "000001.SZ"
            
        - name: start_date
          in: query
          description: 开始日期 (YYYY-MM-DD)
          schema:
            type: string
            format: date
            example: "2024-01-01"
            
        - name: end_date
          in: query  
          description: 结束日期 (YYYY-MM-DD)
          schema:
            type: string
            format: date
            example: "2024-12-31"
            
        - name: period
          in: query
          description: 数据周期
          schema:
            type: string
            enum: [1m, 5m, 15m, 30m, 1h, 4h, 1d, 1w, 1M]
            default: "1d"
            example: "1d"
            
        - name: fields
          in: query
          description: 指定返回字段（逗号分隔）
          schema:
            type: string
            example: "open,high,low,close,volume"
            
        - name: limit
          in: query
          description: 返回记录数量限制
          schema:
            type: integer
            minimum: 1
            maximum: 10000
            default: 1000
            example: 100
            
        - name: access_mode
          in: query
          description: 数据访问模式
          schema:
            type: string
            enum: [auto, cache_only, database_only, api_only, realtime]
            default: "auto"
            example: "auto"
            
        - name: format
          in: query
          description: 响应数据格式
          schema:
            type: string
            enum: [json, csv, parquet]
            default: "json"
            
      responses:
        200:
          description: 数据获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DataResponse'
              examples:
                daily_data:
                  summary: 日线数据示例
                  value:
                    success: true
                    data:
                      symbol: "000001.SZ"
                      data_type: "daily_data"
                      records: 
                        - trade_date: "2024-01-02"
                          open: 10.50
                          high: 10.80
                          low: 10.30
                          close: 10.75
                          volume: 1000000
                          amount: 10750000.00
                        - trade_date: "2024-01-03"
                          open: 10.75
                          high: 11.00
                          low: 10.60
                          close: 10.90
                          volume: 1200000
                          amount: 13080000.00
                    metadata:
                      total_count: 2
                      cache_hit: true
                      cache_level: "L1"
                      data_source: "cache"
                      query_time_ms: 5
                    message: "数据获取成功"
                    
        400:
          $ref: '#/components/responses/BadRequest'
        401:
          $ref: '#/components/responses/Unauthorized'  
        429:
          $ref: '#/components/responses/RateLimited'
        500:
          $ref: '#/components/responses/InternalError'
```

### 2. 批量数据获取接口

```yaml
  /data/batch:
    post:
      summary: 批量数据获取
      description: |
        一次请求获取多种数据类型或多个股票的数据，提高查询效率。
        支持并行处理和智能缓存复用。
        
      operationId: getBatchData
      tags:
        - 核心数据接口
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BatchDataRequest'
            examples:
              multi_symbol:
                summary: 多股票相同指标
                value:
                  requests:
                    - data_type: "daily_data"
                      symbol: "000001.SZ"
                      params:
                        start_date: "2024-01-01"
                        end_date: "2024-01-31"
                    - data_type: "daily_data"  
                      symbol: "000002.SZ"
                      params:
                        start_date: "2024-01-01"
                        end_date: "2024-01-31"
                  options:
                    parallel: true
                    max_concurrency: 5
                    timeout: 30
                    
              multi_data_type:
                summary: 单股票多指标
                value:
                  requests:
                    - data_type: "stock_basic"
                      symbol: "000001.SZ"
                    - data_type: "daily_data"
                      symbol: "000001.SZ"
                      params:
                        start_date: "2024-01-01"
                        limit: 100
                    - data_type: "indicators"
                      symbol: "000001.SZ"
                      params:
                        indicator_types: ["MACD", "RSI", "KDJ"]
                        start_date: "2024-01-01"
                  options:
                    parallel: true
                    fail_fast: false
                    
      responses:
        200:
          description: 批量数据获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BatchDataResponse'
                
        400:
          $ref: '#/components/responses/BadRequest'
        413:
          description: 请求过大
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
```

### 3. 数据订阅接口

```yaml
  /subscriptions:
    post:
      summary: 创建数据订阅
      description: |
        创建数据订阅，当数据更新时通过WebSocket或webhook推送。
        支持多种过滤条件和推送方式。
        
      operationId: createSubscription
      tags:
        - 实时数据
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubscriptionRequest'
              
      responses:
        201:
          description: 订阅创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionResponse'
                
    get:
      summary: 获取订阅列表
      description: 获取当前用户的所有数据订阅
      operationId: getSubscriptions
      tags:
        - 实时数据
      parameters:
        - name: status
          in: query
          description: 订阅状态过滤
          schema:
            type: string
            enum: [active, paused, cancelled]
            
      responses:
        200:
          description: 订阅列表获取成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Subscription'
                      
  /subscriptions/{subscription_id}:
    get:
      summary: 获取订阅详情
      operationId: getSubscription
      tags:
        - 实时数据
      parameters:
        - name: subscription_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
            
      responses:
        200:
          description: 订阅详情
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionResponse'
                
    patch:
      summary: 更新订阅配置
      operationId: updateSubscription  
      tags:
        - 实时数据
      parameters:
        - name: subscription_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubscriptionUpdate'
              
      responses:
        200:
          description: 订阅更新成功
          
    delete:
      summary: 取消订阅
      operationId: cancelSubscription
      tags:
        - 实时数据
      parameters:
        - name: subscription_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
            
      responses:
        204:
          description: 订阅已取消
```

### 4. 流式数据接口

```yaml
  /stream/{data_type}:
    get:
      summary: 流式数据获取
      description: |
        通过Server-Sent Events (SSE) 获取实时数据流。
        适用于需要持续接收数据更新的场景。
        
      operationId: getDataStream
      tags:
        - 实时数据
      parameters:
        - name: data_type
          in: path
          required: true
          schema:
            type: string
            enum: [realtime_quotes, trade_ticks, market_depth]
            
        - name: symbols
          in: query
          required: true
          description: 股票代码列表（逗号分隔）
          schema:
            type: string
            example: "000001.SZ,000002.SZ,600000.SH"
            
        - name: fields
          in: query
          description: 数据字段过滤
          schema:
            type: string
            example: "symbol,price,volume,timestamp"
            
      responses:
        200:
          description: 数据流建立成功
          content:
            text/event-stream:
              schema:
                type: string
              examples:
                realtime_quote:
                  summary: 实时行情流
                  value: |
                    data: {"symbol":"000001.SZ","price":10.75,"change":0.05,"change_pct":0.47,"volume":1000,"timestamp":"2024-01-02T09:30:01Z"}
                    
                    data: {"symbol":"000002.SZ","price":8.20,"change":-0.10,"change_pct":-1.20,"volume":2000,"timestamp":"2024-01-02T09:30:02Z"}
```

## 数据模型定义

### 核心数据模型

```yaml
components:
  schemas:
    # 统一数据响应格式
    DataResponse:
      type: object
      required: [success, data, metadata, message]
      properties:
        success:
          type: boolean
          description: 请求是否成功
          example: true
        data:
          type: object
          description: 响应数据内容
          properties:
            symbol:
              type: string
              description: 股票代码
              example: "000001.SZ"
            data_type:
              type: string
              description: 数据类型
              example: "daily_data"
            records:
              type: array
              description: 数据记录列表
              items:
                type: object
        metadata:
          $ref: '#/components/schemas/ResponseMetadata'
        message:
          type: string
          description: 响应消息
          example: "数据获取成功"
        error:
          type: string
          nullable: true
          description: 错误信息（仅在失败时返回）
          
    # 响应元数据
    ResponseMetadata:
      type: object
      properties:
        total_count:
          type: integer
          description: 总记录数
          example: 100
        returned_count:
          type: integer
          description: 本次返回记录数
          example: 50
        cache_hit:
          type: boolean
          description: 是否命中缓存
          example: true
        cache_level:
          type: string
          enum: [L1, L2, none]
          description: 缓存级别
          example: "L1"
        data_source:
          type: string
          description: 数据来源
          example: "cache"
        query_time_ms:
          type: integer
          description: 查询耗时（毫秒）
          example: 5
        api_rate_limit:
          type: object
          description: API速率限制信息
          properties:
            limit:
              type: integer
              example: 1000
            remaining:
              type: integer
              example: 950
            reset_time:
              type: string
              format: date-time
              
    # 批量请求模型
    BatchDataRequest:
      type: object
      required: [requests]
      properties:
        requests:
          type: array
          description: 数据请求列表
          minItems: 1
          maxItems: 100
          items:
            $ref: '#/components/schemas/SingleDataRequest'
        options:
          type: object
          description: 批量处理选项
          properties:
            parallel:
              type: boolean
              default: true
              description: 是否并行处理
            max_concurrency:
              type: integer
              minimum: 1
              maximum: 10
              default: 5
              description: 最大并发数
            timeout:
              type: integer
              minimum: 1
              maximum: 300
              default: 30
              description: 超时时间（秒）
            fail_fast:
              type: boolean
              default: false
              description: 是否快速失败
              
    # 单个数据请求
    SingleDataRequest:
      type: object
      required: [data_type, symbol]
      properties:
        data_type:
          type: string
          enum: [stock_basic, daily_data, realtime_data, indicators, financial_data]
          description: 数据类型
        symbol:
          type: string
          description: 股票代码
          pattern: '^[A-Z0-9]{6}$|^[A-Z0-9]{6}\.[A-Z]{2}$'
        params:
          type: object
          description: 查询参数
          additionalProperties: true
          
    # 批量响应模型
    BatchDataResponse:
      type: object
      required: [success, results, summary]
      properties:
        success:
          type: boolean
          description: 批量请求整体是否成功
        results:
          type: array
          description: 各个请求的结果
          items:
            type: object
            properties:
              request_index:
                type: integer
                description: 请求在批量中的索引
              success:
                type: boolean
                description: 单个请求是否成功
              data:
                type: object
                description: 响应数据
              error:
                type: string
                nullable: true
                description: 错误信息
        summary:
          type: object
          description: 批量处理摘要
          properties:
            total_requests:
              type: integer
            successful_requests:
              type: integer
            failed_requests:
              type: integer
            total_time_ms:
              type: integer
            average_time_ms:
              type: number
              
    # 订阅请求模型
    SubscriptionRequest:
      type: object
      required: [data_type, symbols, delivery_method]
      properties:
        data_type:
          type: string
          enum: [realtime_quotes, daily_data, indicators, news]
          description: 订阅数据类型
        symbols:
          type: array
          description: 股票代码列表
          items:
            type: string
            pattern: '^[A-Z0-9]{6}$|^[A-Z0-9]{6}\.[A-Z]{2}$'
          maxItems: 100
        delivery_method:
          type: object
          description: 数据推送方式
          required: [type]
          properties:
            type:
              type: string
              enum: [websocket, webhook, sse]
            config:
              type: object
              description: 推送配置
              additionalProperties: true
        filters:
          type: object
          description: 数据过滤条件
          properties:
            price_change_threshold:
              type: number
              description: 价格变动阈值
            volume_threshold:
              type: integer
              description: 成交量阈值
            fields:
              type: array
              description: 字段过滤
              items:
                type: string
        schedule:
          type: object
          description: 推送时间安排
          properties:
            timezone:
              type: string
              default: "Asia/Shanghai"
            trading_hours_only:
              type: boolean
              default: true
            frequency:
              type: string
              enum: [realtime, 1s, 5s, 30s, 1m, 5m]
              default: "realtime"
              
    # 订阅响应模型
    SubscriptionResponse:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/Subscription'
        message:
          type: string
          
    # 订阅详情模型
    Subscription:
      type: object
      properties:
        subscription_id:
          type: string
          format: uuid
          description: 订阅唯一标识
        user_id:
          type: string
          description: 用户ID
        data_type:
          type: string
          description: 数据类型
        symbols:
          type: array
          items:
            type: string
          description: 订阅的股票代码
        status:
          type: string
          enum: [active, paused, cancelled]
          description: 订阅状态
        delivery_method:
          type: object
          description: 推送方式配置
        filters:
          type: object
          description: 过滤条件
        statistics:
          type: object
          description: 订阅统计信息
          properties:
            total_deliveries:
              type: integer
            successful_deliveries:
              type: integer
            failed_deliveries:
              type: integer
            last_delivery_time:
              type: string
              format: date-time
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
          
    # 订阅更新模型
    SubscriptionUpdate:
      type: object
      properties:
        status:
          type: string
          enum: [active, paused]
        symbols:
          type: array
          items:
            type: string
        filters:
          type: object
        delivery_method:
          type: object
          
    # 错误响应模型
    ErrorResponse:
      type: object
      required: [success, error, message]
      properties:
        success:
          type: boolean
          example: false
        error:
          type: string
          description: 错误代码
          example: "INVALID_SYMBOL"
        message:
          type: string
          description: 错误描述
          example: "股票代码格式不正确"
        details:
          type: object
          description: 错误详细信息
          additionalProperties: true
        request_id:
          type: string
          description: 请求追踪ID
          example: "req_123456789"
        timestamp:
          type: string
          format: date-time
          description: 错误发生时间
```

## 专用数据接口

### 1. 股票基础数据接口

```yaml
  /stocks:
    get:
      summary: 获取股票列表
      description: 获取股票基本信息列表，支持多种过滤条件
      operationId: getStockList
      tags:
        - 股票数据
      parameters:
        - name: market
          in: query
          description: 市场代码
          schema:
            type: string
            enum: [SZ, SH, BJ]
        - name: industry
          in: query
          description: 行业分类
          schema:
            type: string
        - name: is_active
          in: query
          description: 是否在交易
          schema:
            type: boolean
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: page_size
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 1000
            default: 100
            
      responses:
        200:
          description: 股票列表获取成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: object
                    properties:
                      stocks:
                        type: array
                        items:
                          $ref: '#/components/schemas/StockBasicInfo'
                      pagination:
                        $ref: '#/components/schemas/PaginationInfo'
                        
  /stocks/{symbol}:
    get:
      summary: 获取股票详细信息
      operationId: getStockDetail
      tags:
        - 股票数据
      parameters:
        - name: symbol
          in: path
          required: true
          schema:
            type: string
            pattern: '^[A-Z0-9]{6}$|^[A-Z0-9]{6}\.[A-Z]{2}$'
            
      responses:
        200:
          description: 股票信息获取成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    $ref: '#/components/schemas/StockDetailInfo'
```

### 2. 技术指标接口

```yaml
  /indicators/{indicator_type}:
    get:
      summary: 获取技术指标数据
      description: |
        获取指定类型的技术指标数据。
        
        ## 支持的指标类型
        - `MACD`: 移动平均收敛散度
        - `RSI`: 相对强弱指数
        - `KDJ`: 随机指标
        - `BOLL`: 布林带
        - `MA`: 移动平均线
        - `VOL`: 成交量指标
        
      operationId: getIndicator
      tags:
        - 技术指标
      parameters:
        - name: indicator_type
          in: path
          required: true
          schema:
            type: string
            enum: [MACD, RSI, KDJ, BOLL, MA, VOL]
        - name: symbol
          in: query
          required: true
          schema:
            type: string
        - name: period
          in: query
          description: 计算周期
          schema:
            type: string
            enum: [1d, 1w, 1M]
            default: "1d"
        - name: start_date
          in: query
          schema:
            type: string
            format: date
        - name: end_date
          in: query
          schema:
            type: string
            format: date
        - name: params
          in: query
          description: 指标参数（JSON格式）
          schema:
            type: string
            example: '{"fast_period":12,"slow_period":26,"signal_period":9}'
            
      responses:
        200:
          description: 技术指标数据获取成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: object
                    properties:
                      symbol:
                        type: string
                      indicator_type:
                        type: string
                      period:
                        type: string
                      params:
                        type: object
                      records:
                        type: array
                        items:
                          type: object
                          properties:
                            trade_date:
                              type: string
                              format: date
                            values:
                              type: object
                              additionalProperties:
                                type: number
```

## WebSocket API

### 实时数据推送

```yaml
# WebSocket连接示例
ws://localhost:8000/ws/realtime/{data_type}?symbols=000001.SZ,000002.SZ&token={auth_token}

# 消息格式规范
Message Format:
{
  "type": "data|error|heartbeat|subscribe|unsubscribe",
  "data": {
    "symbol": "000001.SZ",
    "timestamp": "2024-01-02T09:30:01Z",
    "price": 10.75,
    "volume": 1000,
    "change": 0.05,
    "change_pct": 0.47
  },
  "metadata": {
    "data_type": "realtime_quotes",
    "source": "mairui",
    "latency_ms": 50
  }
}

# 客户端订阅消息
{
  "type": "subscribe",
  "data": {
    "symbols": ["000001.SZ", "000002.SZ"],
    "data_type": "realtime_quotes"
  }
}

# 服务端确认消息
{
  "type": "subscribe_ack",
  "data": {
    "subscription_id": "sub_123456",
    "symbols": ["000001.SZ", "000002.SZ"],
    "status": "active"
  }
}
```

## 错误处理和状态码

### HTTP状态码规范

| 状态码 | 含义 | 使用场景 |
|--------|------|----------|
| 200 | OK | 请求成功 |
| 201 | Created | 资源创建成功 |
| 204 | No Content | 删除成功 |
| 400 | Bad Request | 请求参数错误 |
| 401 | Unauthorized | 认证失败 |
| 403 | Forbidden | 权限不足 |
| 404 | Not Found | 资源不存在 |
| 409 | Conflict | 资源冲突 |
| 429 | Too Many Requests | 请求频率过高 |
| 500 | Internal Server Error | 服务器内部错误 |
| 502 | Bad Gateway | 上游服务错误 |
| 503 | Service Unavailable | 服务不可用 |

### 错误代码规范

```yaml
# 标准错误响应
components:
  responses:
    BadRequest:
      description: 请求参数错误
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ErrorResponse'
            example:
              success: false
              error: "INVALID_PARAMETER"
              message: "参数'symbol'格式不正确"
              details:
                parameter: "symbol"
                provided_value: "invalid_code"
                expected_format: "XXXXXX.XX"
              request_id: "req_123456789"
              timestamp: "2024-01-02T09:30:01Z"
              
    Unauthorized:
      description: 认证失败
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error: "AUTHENTICATION_FAILED"
            message: "API密钥无效或已过期"
            
    RateLimited:
      description: 请求频率过高
      content:
        application/json:
          schema:
            allOf:
              - $ref: '#/components/schemas/ErrorResponse'
            example:
              success: false
              error: "RATE_LIMIT_EXCEEDED"
              message: "请求频率过高，请稍后再试"
              details:
                limit: 1000
                window: "1h"
                retry_after: 60
              
    InternalError:
      description: 服务器内部错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            success: false
            error: "INTERNAL_SERVER_ERROR"
            message: "服务器内部错误，请稍后重试"
```

### 业务错误代码

| 错误代码 | HTTP状态码 | 描述 |
|----------|------------|------|
| INVALID_SYMBOL | 400 | 股票代码格式不正确 |
| SYMBOL_NOT_FOUND | 404 | 股票代码不存在 |
| INVALID_DATE_RANGE | 400 | 日期范围无效 |
| DATA_NOT_AVAILABLE | 404 | 请求的数据不可用 |
| INDICATOR_CALCULATION_FAILED | 500 | 技术指标计算失败 |
| DATA_SOURCE_UNAVAILABLE | 502 | 数据源不可用 |
| CACHE_ERROR | 500 | 缓存系统错误 |
| SUBSCRIPTION_LIMIT_EXCEEDED | 400 | 订阅数量超限 |
| INVALID_SUBSCRIPTION_CONFIG | 400 | 订阅配置无效 |

## API使用示例

### Python SDK示例

```python
from udm_client import UnifiedDataManager

# 初始化客户端
udm = UnifiedDataManager(
    api_key="your_api_key",
    base_url="https://api.udm.example.com/v2"
)

# 1. 获取日线数据
daily_data = await udm.get_data(
    data_type="daily_data",
    symbol="000001.SZ",
    start_date="2024-01-01",
    end_date="2024-01-31"
)

# 2. 批量获取多股票数据
batch_request = [
    {"data_type": "daily_data", "symbol": "000001.SZ", "params": {"limit": 100}},
    {"data_type": "daily_data", "symbol": "000002.SZ", "params": {"limit": 100}},
]
batch_results = await udm.get_batch_data(batch_request)

# 3. 创建实时数据订阅
subscription = await udm.create_subscription(
    data_type="realtime_quotes",
    symbols=["000001.SZ", "000002.SZ"],
    delivery_method={
        "type": "websocket",
        "config": {"auto_reconnect": True}
    }
)

# 4. WebSocket实时数据接收
async def handle_realtime_data(data):
    print(f"收到实时数据: {data}")

await udm.subscribe_realtime(
    data_type="realtime_quotes",
    symbols=["000001.SZ"],
    callback=handle_realtime_data
)
```

### JavaScript SDK示例

```javascript
import { UnifiedDataManager } from '@udm/client';

// 初始化客户端
const udm = new UnifiedDataManager({
  apiKey: 'your_api_key',
  baseUrl: 'https://api.udm.example.com/v2'
});

// 1. 获取股票基本信息
const stockInfo = await udm.getData({
  dataType: 'stock_basic',
  symbol: '000001.SZ'
});

// 2. 获取技术指标
const macdData = await udm.getData({
  dataType: 'indicators',
  symbol: '000001.SZ',
  params: {
    indicator_type: 'MACD',
    start_date: '2024-01-01',
    params: { fast_period: 12, slow_period: 26, signal_period: 9 }
  }
});

// 3. Server-Sent Events实时数据
const eventSource = udm.createEventStream({
  dataType: 'realtime_quotes',
  symbols: ['000001.SZ', '000002.SZ']
});

eventSource.onmessage = (event) => {
  const data = JSON.parse(event.data);
  console.log('实时数据:', data);
};
```

### cURL示例

```bash
# 1. 获取日线数据
curl -X GET "https://api.udm.example.com/v2/data/daily_data?symbol=000001.SZ&start_date=2024-01-01&end_date=2024-01-31" \
  -H "Authorization: Bearer your_api_key" \
  -H "Content-Type: application/json"

# 2. 批量数据获取
curl -X POST "https://api.udm.example.com/v2/data/batch" \
  -H "Authorization: Bearer your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "requests": [
      {
        "data_type": "daily_data",
        "symbol": "000001.SZ",
        "params": {"start_date": "2024-01-01", "limit": 100}
      },
      {
        "data_type": "indicators",
        "symbol": "000001.SZ",
        "params": {"indicator_type": "MACD"}
      }
    ],
    "options": {
      "parallel": true,
      "max_concurrency": 5
    }
  }'

# 3. 创建数据订阅
curl -X POST "https://api.udm.example.com/v2/subscriptions" \
  -H "Authorization: Bearer your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "data_type": "realtime_quotes",
    "symbols": ["000001.SZ", "000002.SZ"],
    "delivery_method": {
      "type": "webhook",
      "config": {
        "url": "https://your-app.com/webhook/quotes",
        "method": "POST",
        "headers": {"X-Webhook-Secret": "your_secret"}
      }
    },
    "filters": {
      "price_change_threshold": 0.01
    }
  }'
```

## API版本管理

### 版本策略

1. **语义化版本**: 使用MAJOR.MINOR.PATCH格式
2. **向后兼容**: MINOR版本保持向后兼容
3. **废弃政策**: 提前6个月通知废弃API
4. **并行支持**: 同时支持多个MAJOR版本

### 版本路径规范

- v1: `/api/v1/` - 当前稳定版本（维护模式）
- v2: `/api/v2/` - 新功能版本（活跃开发）
- v3: `/api/v3/` - 未来版本（规划中）

### 客户端版本检测

```yaml
  /version:
    get:
      summary: 获取API版本信息
      operationId: getVersionInfo
      tags:
        - 系统信息
      responses:
        200:
          description: 版本信息
          content:
            application/json:
              schema:
                type: object
                properties:
                  current_version:
                    type: string
                    example: "2.1.0"
                  supported_versions:
                    type: array
                    items:
                      type: string
                    example: ["1.0.0", "2.0.0", "2.1.0"]
                  deprecated_versions:
                    type: array
                    items:
                      type: object
                      properties:
                        version:
                          type: string
                        deprecation_date:
                          type: string
                          format: date
                        sunset_date:
                          type: string
                          format: date
```

## 安全和认证

### 认证方式

```yaml
components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: API密钥认证
      
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token认证
      
    OAuth2:
      type: oauth2
      flows:
        authorizationCode:
          authorizationUrl: https://auth.udm.example.com/oauth/authorize
          tokenUrl: https://auth.udm.example.com/oauth/token
          scopes:
            read: 读取数据权限
            write: 写入数据权限
            admin: 管理员权限
```

### 速率限制

- **基础限制**: 1000 requests/hour
- **高级用户**: 10000 requests/hour  
- **企业用户**: 100000 requests/hour
- **实时数据**: 单独计算，基于订阅套餐

### 数据使用条款

1. **数据所有权**: 数据提供方保留所有权
2. **使用限制**: 仅限授权用途使用
3. **重分发限制**: 禁止未经授权的数据重分发
4. **缓存策略**: 客户端缓存不超过24小时

这个API规范为统一数据管理系统提供了完整的接口定义，支持多种数据访问模式和扩展需求，确保系统的可用性、性能和开发者体验。