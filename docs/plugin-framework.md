# 插件化框架设计

## 框架概述

统一数据管理系统的插件化框架基于"约定优于配置"的原则，提供强大而灵活的扩展机制。框架支持三大类插件：数据源插件、数据处理器插件和中间件插件，实现零代码修改接入新功能。

### 设计目标

1. **零侵入扩展**: 无需修改核心代码即可添加新功能
2. **热插拔支持**: 运行时动态加载和卸载插件
3. **类型安全**: 基于类型注解的插件接口定义
4. **依赖管理**: 自动解析和管理插件间依赖关系
5. **性能优化**: 最小化插件调用开销
6. **错误隔离**: 插件异常不影响系统核心功能

### 核心特性

- **声明式配置**: YAML配置驱动的插件管理
- **接口标准化**: 统一的插件接口规范
- **生命周期管理**: 完整的插件生命周期控制
- **事件驱动**: 基于事件的插件间通信
- **版本控制**: 插件版本兼容性管理
- **监控集成**: 插件性能和健康状态监控

## 插件类型和架构

### 插件分类

```mermaid
graph TB
    subgraph "插件生态系统"
        A[数据源插件] --> A1[API Provider插件]
        A --> A2[数据库插件]
        A --> A3[文件数据插件]
        A --> A4[消息队列插件]
        
        B[处理器插件] --> B1[数据转换插件]
        B --> B2[指标计算插件]
        B --> B3[数据验证插件]
        B --> B4[数据清洗插件]
        
        C[中间件插件] --> C1[认证插件]
        C --> C2[缓存插件]
        C --> C3[限流插件]
        C --> C4[监控插件]
        
        D[扩展插件] --> D1[通知插件]
        D --> D2[存储插件]
        D --> D3[调度插件]
        D --> D4[报告插件]
    end
```

### 插件架构层次

```mermaid
graph TB
    subgraph "应用层"
        A[统一数据管理器]
        B[API网关]
        C[业务服务]
    end
    
    subgraph "插件框架层"
        D[PluginManager<br/>插件管理器]
        E[PluginRegistry<br/>插件注册表]
        F[DependencyResolver<br/>依赖解析器]
        G[LifecycleManager<br/>生命周期管理]
    end
    
    subgraph "插件接口层"
        H[DataSourcePlugin<br/>数据源接口]
        I[ProcessorPlugin<br/>处理器接口]
        J[MiddlewarePlugin<br/>中间件接口]
        K[ExtensionPlugin<br/>扩展接口]
    end
    
    subgraph "插件实现层"
        L[Tushare插件]
        M[AKShare插件]
        N[迈睿插件]
        O[MACD计算插件]
        P[数据验证插件]
        Q[Redis缓存插件]
    end
    
    A --> D
    B --> D
    C --> D
    
    D --> E
    D --> F
    D --> G
    
    E --> H
    E --> I
    E --> J
    E --> K
    
    H --> L
    H --> M
    H --> N
    I --> O
    I --> P
    J --> Q
```

## 插件接口规范

### 1. 基础插件接口

```python
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Type, Union
from pydantic import BaseModel
from enum import Enum
import asyncio

class PluginStatus(Enum):
    """插件状态枚举"""
    UNLOADED = "unloaded"
    LOADING = "loading"
    LOADED = "loaded"
    ACTIVE = "active"
    ERROR = "error"
    DISABLED = "disabled"

class PluginMetadata(BaseModel):
    """插件元数据"""
    name: str
    version: str
    description: str
    author: str
    author_email: Optional[str] = None
    homepage: Optional[str] = None
    license: str = "MIT"
    tags: List[str] = []
    
    # 依赖信息
    dependencies: List[str] = []
    python_requires: str = ">=3.8"
    framework_version: str = ">=2.0.0"
    
    # 配置信息
    config_schema: Optional[Dict[str, Any]] = None
    default_config: Optional[Dict[str, Any]] = None

class BasePlugin(ABC):
    """插件基类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.status = PluginStatus.UNLOADED
        self.metadata: Optional[PluginMetadata] = None
        self.logger = None
        self._event_bus = None
        
    @abstractmethod
    async def initialize(self) -> bool:
        """初始化插件"""
        pass
        
    @abstractmethod
    async def destroy(self) -> bool:
        """销毁插件"""
        pass
        
    async def configure(self, config: Dict[str, Any]) -> bool:
        """配置插件"""
        self.config.update(config)
        return True
        
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return {
            "status": self.status.value,
            "healthy": self.status == PluginStatus.ACTIVE,
            "last_check": datetime.utcnow().isoformat()
        }
        
    def get_metadata(self) -> PluginMetadata:
        """获取插件元数据"""
        if not self.metadata:
            raise ValueError(f"Plugin {self.__class__.__name__} metadata not set")
        return self.metadata
        
    def set_event_bus(self, event_bus):
        """设置事件总线"""
        self._event_bus = event_bus
        
    async def emit_event(self, event_type: str, data: Any):
        """发送事件"""
        if self._event_bus:
            await self._event_bus.emit(event_type, data, source=self.metadata.name)
```

### 2. 数据源插件接口

```python
class DataSourcePlugin(BasePlugin):
    """数据源插件基类"""
    
    @abstractmethod
    async def get_data(
        self, 
        data_type: str, 
        params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """获取数据"""
        pass
        
    @abstractmethod
    async def validate_params(
        self, 
        data_type: str, 
        params: Dict[str, Any]
    ) -> bool:
        """验证参数"""
        pass
        
    @abstractmethod
    def get_supported_data_types(self) -> List[str]:
        """获取支持的数据类型"""
        pass
        
    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            # 子类可以重写此方法进行实际连接测试
            return True
        except Exception:
            return False
            
    async def get_rate_limit_info(self) -> Dict[str, Any]:
        """获取速率限制信息"""
        return {
            "requests_per_second": None,
            "requests_per_minute": None,
            "requests_per_hour": None,
            "concurrent_requests": None
        }

# 具体数据源插件示例
class TusharePlugin(DataSourcePlugin):
    """Tushare数据源插件"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        self.metadata = PluginMetadata(
            name="tushare",
            version="1.0.0",
            description="Tushare数据源插件",
            author="UDM Team",
            dependencies=["tushare>=1.2.0"],
            config_schema={
                "type": "object",
                "properties": {
                    "token": {"type": "string", "minLength": 1},
                    "timeout": {"type": "integer", "default": 30}
                },
                "required": ["token"]
            }
        )
        self._client = None
        
    async def initialize(self) -> bool:
        """初始化Tushare客户端"""
        try:
            import tushare as ts
            token = self.config.get("token")
            if not token:
                raise ValueError("Tushare token is required")
                
            ts.set_token(token)
            self._client = ts.pro_api()
            self.status = PluginStatus.ACTIVE
            return True
        except Exception as e:
            self.status = PluginStatus.ERROR
            if self.logger:
                self.logger.error(f"Failed to initialize Tushare plugin: {e}")
            return False
            
    async def get_data(self, data_type: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """获取Tushare数据"""
        if not self._client:
            raise RuntimeError("Tushare client not initialized")
            
        if data_type == "stock_basic":
            return await self._get_stock_basic(params)
        elif data_type == "daily_data":
            return await self._get_daily_data(params)
        else:
            raise ValueError(f"Unsupported data type: {data_type}")
            
    def get_supported_data_types(self) -> List[str]:
        return ["stock_basic", "daily_data", "financial_data"]
        
    async def _get_stock_basic(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """获取股票基本信息"""
        # 实现具体的数据获取逻辑
        pass
        
    async def _get_daily_data(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """获取日线数据"""
        # 实现具体的数据获取逻辑
        pass
```

### 3. 数据处理器插件接口

```python
class ProcessorPlugin(BasePlugin):
    """数据处理器插件基类"""
    
    @abstractmethod
    async def process(
        self, 
        data: Any, 
        context: Dict[str, Any]
    ) -> Any:
        """处理数据"""
        pass
        
    @abstractmethod
    def get_input_schema(self) -> Dict[str, Any]:
        """获取输入数据模式"""
        pass
        
    @abstractmethod
    def get_output_schema(self) -> Dict[str, Any]:
        """获取输出数据模式"""
        pass
        
    async def validate_input(self, data: Any) -> bool:
        """验证输入数据"""
        # 基于schema验证数据
        return True
        
    async def validate_output(self, data: Any) -> bool:
        """验证输出数据"""
        # 基于schema验证数据
        return True

# 具体处理器插件示例
class MACDCalculatorPlugin(ProcessorPlugin):
    """MACD指标计算插件"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        self.metadata = PluginMetadata(
            name="macd_calculator",
            version="1.0.0",
            description="MACD技术指标计算插件",
            author="UDM Team",
            dependencies=["pandas>=1.3.0", "numpy>=1.20.0"],
            config_schema={
                "type": "object",
                "properties": {
                    "fast_period": {"type": "integer", "default": 12},
                    "slow_period": {"type": "integer", "default": 26},
                    "signal_period": {"type": "integer", "default": 9}
                }
            }
        )
        
    async def process(self, data: Any, context: Dict[str, Any]) -> Any:
        """计算MACD指标"""
        import pandas as pd
        import numpy as np
        
        if not isinstance(data, pd.DataFrame):
            raise ValueError("Input data must be a pandas DataFrame")
            
        if 'close' not in data.columns:
            raise ValueError("DataFrame must contain 'close' column")
            
        # MACD计算逻辑
        close_prices = data['close']
        fast_period = self.config.get('fast_period', 12)
        slow_period = self.config.get('slow_period', 26)
        signal_period = self.config.get('signal_period', 9)
        
        # 计算EMA
        ema_fast = close_prices.ewm(span=fast_period).mean()
        ema_slow = close_prices.ewm(span=slow_period).mean()
        
        # 计算MACD线和信号线
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=signal_period).mean()
        histogram = macd_line - signal_line
        
        result = data.copy()
        result['macd'] = macd_line
        result['macd_signal'] = signal_line
        result['macd_histogram'] = histogram
        
        return result
        
    def get_input_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "description": "股票价格数据",
            "required": ["close"],
            "properties": {
                "close": {"type": "array", "items": {"type": "number"}}
            }
        }
        
    def get_output_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "description": "包含MACD指标的数据",
            "properties": {
                "macd": {"type": "array", "items": {"type": "number"}},
                "dif": {"type": "array", "items": {"type": "number"}},
                "dea": {"type": "array", "items": {"type": "number"}}
            }
        }
```

### 4. 中间件插件接口

```python
class MiddlewarePlugin(BasePlugin):
    """中间件插件基类"""
    
    @abstractmethod
    async def before_request(
        self, 
        request: Any, 
        context: Dict[str, Any]
    ) -> Optional[Any]:
        """请求前处理"""
        pass
        
    @abstractmethod
    async def after_request(
        self, 
        request: Any, 
        response: Any, 
        context: Dict[str, Any]
    ) -> Optional[Any]:
        """请求后处理"""
        pass
        
    async def on_error(
        self, 
        request: Any, 
        error: Exception, 
        context: Dict[str, Any]
    ) -> Optional[Any]:
        """错误处理"""
        return None
        
    def get_priority(self) -> int:
        """获取中间件优先级（数字越小优先级越高）"""
        return 100

# 具体中间件插件示例
class RateLimitPlugin(MiddlewarePlugin):
    """速率限制中间件插件"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        self.metadata = PluginMetadata(
            name="rate_limiter",
            version="1.0.0",
            description="API速率限制中间件",
            author="UDM Team",
            dependencies=["redis>=4.0.0"],
            config_schema={
                "type": "object",
                "properties": {
                    "requests_per_minute": {"type": "integer", "default": 60},
                    "requests_per_hour": {"type": "integer", "default": 1000},
                    "redis_url": {"type": "string"}
                }
            }
        )
        self._redis = None
        
    async def initialize(self) -> bool:
        try:
            import redis.asyncio as redis
            redis_url = self.config.get("redis_url", "redis://localhost:6379/0")
            self._redis = redis.from_url(redis_url)
            self.status = PluginStatus.ACTIVE
            return True
        except Exception as e:
            self.status = PluginStatus.ERROR
            return False
            
    async def before_request(self, request: Any, context: Dict[str, Any]) -> Optional[Any]:
        """检查速率限制"""
        user_id = context.get("user_id", "anonymous")
        current_time = int(time.time())
        
        # 检查每分钟限制
        minute_key = f"rate_limit:{user_id}:minute:{current_time // 60}"
        minute_count = await self._redis.incr(minute_key)
        await self._redis.expire(minute_key, 60)
        
        if minute_count > self.config.get("requests_per_minute", 60):
            return {
                "error": "RATE_LIMIT_EXCEEDED",
                "message": "请求频率过高，请稍后再试",
                "retry_after": 60
            }
            
        # 检查每小时限制
        hour_key = f"rate_limit:{user_id}:hour:{current_time // 3600}"
        hour_count = await self._redis.incr(hour_key)
        await self._redis.expire(hour_key, 3600)
        
        if hour_count > self.config.get("requests_per_hour", 1000):
            return {
                "error": "RATE_LIMIT_EXCEEDED",
                "message": "小时请求量超限",
                "retry_after": 3600
            }
            
        return None
        
    async def after_request(self, request: Any, response: Any, context: Dict[str, Any]) -> Optional[Any]:
        """添加速率限制头信息"""
        if isinstance(response, dict):
            user_id = context.get("user_id", "anonymous")
            current_time = int(time.time())
            
            minute_key = f"rate_limit:{user_id}:minute:{current_time // 60}"
            hour_key = f"rate_limit:{user_id}:hour:{current_time // 3600}"
            
            minute_count = await self._redis.get(minute_key) or 0
            hour_count = await self._redis.get(hour_key) or 0
            
            response["rate_limit"] = {
                "requests_per_minute": {
                    "limit": self.config.get("requests_per_minute", 60),
                    "remaining": max(0, self.config.get("requests_per_minute", 60) - int(minute_count))
                },
                "requests_per_hour": {
                    "limit": self.config.get("requests_per_hour", 1000),
                    "remaining": max(0, self.config.get("requests_per_hour", 1000) - int(hour_count))
                }
            }
            
        return response
        
    def get_priority(self) -> int:
        return 10  # 高优先级，在其他中间件之前执行
```

## 插件管理系统

### 插件管理器

```python
import importlib
import inspect
from pathlib import Path
from typing import Dict, List, Optional, Type, Any
import yaml
import asyncio
from concurrent.futures import ThreadPoolExecutor

class PluginManager:
    """插件管理器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.plugins: Dict[str, BasePlugin] = {}
        self.plugin_configs: Dict[str, Dict[str, Any]] = {}
        self.dependency_graph: Dict[str, List[str]] = {}
        self.load_order: List[str] = []
        self.event_bus = None
        self.logger = None
        
    async def initialize(self, config_path: Optional[str] = None):
        """初始化插件管理器"""
        if config_path:
            await self.load_config(config_path)
            
        # 创建事件总线
        from .event_bus import EventBus
        self.event_bus = EventBus()
        await self.event_bus.initialize()
        
    async def load_config(self, config_path: str):
        """加载插件配置"""
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            
        self.plugin_configs = config.get('plugins', {})
        
    async def discover_plugins(self, plugin_dirs: List[str]):
        """发现插件"""
        discovered_plugins = {}
        
        for plugin_dir in plugin_dirs:
            plugin_path = Path(plugin_dir)
            if not plugin_path.exists():
                continue
                
            for plugin_file in plugin_path.glob("**/*.py"):
                if plugin_file.name.startswith("__"):
                    continue
                    
                try:
                    module_name = self._path_to_module_name(plugin_file, plugin_path)
                    module = importlib.import_module(module_name)
                    
                    # 查找插件类
                    for name, obj in inspect.getmembers(module, inspect.isclass):
                        if (issubclass(obj, BasePlugin) and 
                            obj != BasePlugin and 
                            not inspect.isabstract(obj)):
                            
                            plugin_instance = obj()
                            metadata = plugin_instance.get_metadata()
                            discovered_plugins[metadata.name] = {
                                'class': obj,
                                'module': module_name,
                                'metadata': metadata
                            }
                            
                except Exception as e:
                    if self.logger:
                        self.logger.warning(f"Failed to load plugin from {plugin_file}: {e}")
                        
        return discovered_plugins
        
    async def load_plugin(self, plugin_name: str, plugin_info: Dict[str, Any]):
        """加载插件"""
        try:
            plugin_class = plugin_info['class']
            metadata = plugin_info['metadata']
            
            # 获取配置
            config = self.plugin_configs.get(plugin_name, {})
            
            # 创建插件实例
            plugin = plugin_class(config)
            plugin.set_event_bus(self.event_bus)
            
            # 初始化插件
            if await plugin.initialize():
                self.plugins[plugin_name] = plugin
                await self.emit_event("plugin_loaded", {
                    "plugin_name": plugin_name,
                    "metadata": metadata.dict()
                })
                return True
            else:
                if self.logger:
                    self.logger.error(f"Failed to initialize plugin: {plugin_name}")
                return False
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to load plugin {plugin_name}: {e}")
            return False
            
    async def unload_plugin(self, plugin_name: str):
        """卸载插件"""
        if plugin_name not in self.plugins:
            return False
            
        try:
            plugin = self.plugins[plugin_name]
            await plugin.destroy()
            del self.plugins[plugin_name]
            
            await self.emit_event("plugin_unloaded", {
                "plugin_name": plugin_name
            })
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to unload plugin {plugin_name}: {e}")
            return False
            
    async def reload_plugin(self, plugin_name: str):
        """重新加载插件"""
        if plugin_name in self.plugins:
            await self.unload_plugin(plugin_name)
            
        # 重新发现和加载插件
        # 这里需要重新导入模块
        return await self.load_plugin(plugin_name)
        
    def get_plugin(self, plugin_name: str) -> Optional[BasePlugin]:
        """获取插件实例"""
        return self.plugins.get(plugin_name)
        
    def get_plugins_by_type(self, plugin_type: Type[BasePlugin]) -> List[BasePlugin]:
        """根据类型获取插件"""
        return [plugin for plugin in self.plugins.values() 
                if isinstance(plugin, plugin_type)]
                
    async def emit_event(self, event_type: str, data: Any):
        """发送事件"""
        if self.event_bus:
            await self.event_bus.emit(event_type, data, source="plugin_manager")
            
    def _path_to_module_name(self, file_path: Path, base_path: Path) -> str:
        """将文件路径转换为模块名"""
        relative_path = file_path.relative_to(base_path)
        module_parts = list(relative_path.parts[:-1]) + [relative_path.stem]
        return '.'.join(module_parts)
        
    async def resolve_dependencies(self):
        """解析插件依赖"""
        # 构建依赖图
        for plugin_name, plugin in self.plugins.items():
            metadata = plugin.get_metadata()
            self.dependency_graph[plugin_name] = metadata.dependencies
            
        # 拓扑排序确定加载顺序
        self.load_order = self._topological_sort(self.dependency_graph)
        
    def _topological_sort(self, graph: Dict[str, List[str]]) -> List[str]:
        """拓扑排序"""
        in_degree = {node: 0 for node in graph}
        for node in graph:
            for neighbor in graph[node]:
                if neighbor in in_degree:
                    in_degree[neighbor] += 1
                    
        queue = [node for node in in_degree if in_degree[node] == 0]
        result = []
        
        while queue:
            node = queue.pop(0)
            result.append(node)
            
            for neighbor in graph.get(node, []):
                if neighbor in in_degree:
                    in_degree[neighbor] -= 1
                    if in_degree[neighbor] == 0:
                        queue.append(neighbor)
                        
        return result
```

### 插件注册表

```python
class PluginRegistry:
    """插件注册表"""
    
    def __init__(self):
        self.data_source_plugins: Dict[str, Type[DataSourcePlugin]] = {}
        self.processor_plugins: Dict[str, Type[ProcessorPlugin]] = {}
        self.middleware_plugins: Dict[str, Type[MiddlewarePlugin]] = {}
        self.extension_plugins: Dict[str, Type[BasePlugin]] = {}
        
    def register_data_source(self, name: str, plugin_class: Type[DataSourcePlugin]):
        """注册数据源插件"""
        self.data_source_plugins[name] = plugin_class
        
    def register_processor(self, name: str, plugin_class: Type[ProcessorPlugin]):
        """注册处理器插件"""
        self.processor_plugins[name] = plugin_class
        
    def register_middleware(self, name: str, plugin_class: Type[MiddlewarePlugin]):
        """注册中间件插件"""
        self.middleware_plugins[name] = plugin_class
        
    def register_extension(self, name: str, plugin_class: Type[BasePlugin]):
        """注册扩展插件"""
        self.extension_plugins[name] = plugin_class
        
    def get_data_source_plugin(self, name: str) -> Optional[Type[DataSourcePlugin]]:
        """获取数据源插件类"""
        return self.data_source_plugins.get(name)
        
    def get_processor_plugin(self, name: str) -> Optional[Type[ProcessorPlugin]]:
        """获取处理器插件类"""
        return self.processor_plugins.get(name)
        
    def get_middleware_plugin(self, name: str) -> Optional[Type[MiddlewarePlugin]]:
        """获取中间件插件类"""
        return self.middleware_plugins.get(name)
        
    def list_plugins_by_type(self, plugin_type: str) -> List[str]:
        """按类型列出插件"""
        if plugin_type == "data_source":
            return list(self.data_source_plugins.keys())
        elif plugin_type == "processor":
            return list(self.processor_plugins.keys())
        elif plugin_type == "middleware":
            return list(self.middleware_plugins.keys())
        elif plugin_type == "extension":
            return list(self.extension_plugins.keys())
        else:
            return []
```

## 插件配置管理

### 配置文件格式

```yaml
# plugins.yml - 插件配置文件
version: "2.0"

# 全局插件设置
global:
  plugin_directories:
    - "./plugins"
    - "./custom_plugins"
    - "/opt/udm/plugins"
  
  auto_discover: true
  hot_reload: true
  
  # 插件加载超时设置
  load_timeout: 30
  
  # 并发加载设置
  concurrent_loading: true
  max_concurrent_loads: 5

# 数据源插件配置
data_sources:
  tushare:
    enabled: true
    config:
      token: "${TUSHARE_TOKEN}"
      timeout: 30
      retry_count: 3
      retry_delay: 1.0
      
  akshare:
    enabled: true
    config:
      timeout: 30
      
  mairui:
    enabled: true
    config:
      token: "${MAIRUI_TOKEN}"
      timeout: 30
      endpoint: "https://api.mairui.club"

# 处理器插件配置
processors:
  macd_calculator:
    enabled: true
    config:
      fast_period: 12
      slow_period: 26
      signal_period: 9
      
  rsi_calculator:
    enabled: true
    config:
      period: 14
      
  data_validator:
    enabled: true
    config:
      strict_mode: false
      allow_null_values: false

# 中间件插件配置
middleware:
  rate_limiter:
    enabled: true
    priority: 10
    config:
      redis_url: "${REDIS_URL}"
      requests_per_minute: 60
      requests_per_hour: 1000
      
  cache_middleware:
    enabled: true
    priority: 20
    config:
      cache_ttl: 3600
      cache_key_prefix: "udm:cache:"
      
  auth_middleware:
    enabled: true
    priority: 5
    config:
      jwt_secret: "${JWT_SECRET}"
      token_expiry: 86400

# 扩展插件配置
extensions:
  notification_service:
    enabled: true
    config:
      email_smtp: "smtp.example.com"
      email_port: 587
      webhook_timeout: 10
      
  report_generator:
    enabled: false
    config:
      output_format: "pdf"
      template_dir: "./templates"

# 插件依赖配置
dependencies:
  resolution_strategy: "strict"  # strict, lenient
  auto_install: false
  
# 监控配置
monitoring:
  health_check_interval: 60
  performance_tracking: true
  error_reporting: true
```

### 配置管理器

```python
class PluginConfigManager:
    """插件配置管理器"""
    
    def __init__(self, config_path: str = "plugins.yml"):
        self.config_path = config_path
        self.config: Dict[str, Any] = {}
        self.watchers: List[Callable] = []
        
    async def load_config(self):
        """加载配置文件"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            raw_config = yaml.safe_load(f)
            
        # 环境变量替换
        self.config = self._resolve_env_vars(raw_config)
        
    def _resolve_env_vars(self, config: Any) -> Any:
        """解析环境变量"""
        if isinstance(config, dict):
            return {k: self._resolve_env_vars(v) for k, v in config.items()}
        elif isinstance(config, list):
            return [self._resolve_env_vars(item) for item in config]
        elif isinstance(config, str):
            if config.startswith("${") and config.endswith("}"):
                env_var = config[2:-1]
                return os.getenv(env_var, config)
            return config
        else:
            return config
            
    def get_plugin_config(self, plugin_type: str, plugin_name: str) -> Dict[str, Any]:
        """获取插件配置"""
        type_config = self.config.get(plugin_type, {})
        plugin_config = type_config.get(plugin_name, {})
        return plugin_config.get('config', {})
        
    def is_plugin_enabled(self, plugin_type: str, plugin_name: str) -> bool:
        """检查插件是否启用"""
        type_config = self.config.get(plugin_type, {})
        plugin_config = type_config.get(plugin_name, {})
        return plugin_config.get('enabled', False)
        
    def get_global_config(self) -> Dict[str, Any]:
        """获取全局配置"""
        return self.config.get('global', {})
        
    async def watch_config_changes(self, callback: Callable):
        """监控配置文件变化"""
        self.watchers.append(callback)
        
        # 实现文件监控逻辑
        import watchdog
        # ... 实现文件监控
        
    async def reload_config(self):
        """重新加载配置"""
        await self.load_config()
        
        # 通知所有监控器
        for watcher in self.watchers:
            await watcher(self.config)
```

## 插件开发指南

### 开发流程

1. **创建插件项目结构**
```
my_plugin/
├── __init__.py
├── plugin.py          # 插件主文件
├── metadata.yml       # 插件元数据
├── requirements.txt   # 依赖列表
├── config_schema.json # 配置模式
├── tests/            # 测试文件
└── docs/             # 文档
```

2. **定义插件元数据**

```yaml
# metadata.yml
name: "my_custom_plugin"
version: "1.0.0"
description: "自定义数据源插件"
author: "Your Name"
author_email: "<EMAIL>"
homepage: "https://github.com/yourusername/my-plugin"
license: "MIT"
tags: ["data-source", "financial"]

dependencies:
  - "requests>=2.25.0"
  - "pandas>=1.3.0"

python_requires: ">=3.8"
framework_version: ">=2.0.0"

# 插件类型和入口点
plugin_type: "data_source"
entry_point: "plugin.MyCustomPlugin"

# 支持的数据类型
supported_data_types:
  - "custom_data"
  - "special_indicators"

# 配置要求
config_required:
  - "api_key"
  - "endpoint_url"

config_optional:
  - "timeout"
  - "retry_count"
```

3. **实现插件类**

```python
# plugin.py
from typing import Any, Dict, List, Optional
from udm_framework import DataSourcePlugin, PluginMetadata

class MyCustomPlugin(DataSourcePlugin):
    """自定义数据源插件"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        
        # 设置插件元数据
        self.metadata = PluginMetadata(
            name="my_custom_plugin",
            version="1.0.0",
            description="自定义数据源插件",
            author="Your Name",
            dependencies=["requests>=2.25.0"],
            config_schema={
                "type": "object",
                "properties": {
                    "api_key": {"type": "string", "minLength": 1},
                    "endpoint_url": {"type": "string", "format": "uri"},
                    "timeout": {"type": "integer", "default": 30}
                },
                "required": ["api_key", "endpoint_url"]
            }
        )
        
        self._client = None
        
    async def initialize(self) -> bool:
        """初始化插件"""
        try:
            import requests
            
            # 验证配置
            if not self.config.get("api_key"):
                raise ValueError("api_key is required")
                
            if not self.config.get("endpoint_url"):
                raise ValueError("endpoint_url is required")
                
            # 初始化HTTP客户端
            self._client = requests.Session()
            self._client.headers.update({
                'Authorization': f'Bearer {self.config["api_key"]}',
                'User-Agent': 'UDM-CustomPlugin/1.0.0'
            })
            
            # 测试连接
            if not await self.test_connection():
                raise RuntimeError("Connection test failed")
                
            self.status = PluginStatus.ACTIVE
            return True
            
        except Exception as e:
            self.status = PluginStatus.ERROR
            if self.logger:
                self.logger.error(f"Failed to initialize plugin: {e}")
            return False
            
    async def destroy(self) -> bool:
        """销毁插件"""
        if self._client:
            self._client.close()
            self._client = None
            
        self.status = PluginStatus.UNLOADED
        return True
        
    async def get_data(self, data_type: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """获取数据"""
        if not self._client:
            raise RuntimeError("Plugin not initialized")
            
        if data_type not in self.get_supported_data_types():
            raise ValueError(f"Unsupported data type: {data_type}")
            
        # 构建API请求
        url = f"{self.config['endpoint_url']}/{data_type}"
        timeout = self.config.get('timeout', 30)
        
        try:
            response = self._client.get(url, params=params, timeout=timeout)
            response.raise_for_status()
            
            data = response.json()
            
            # 标准化数据格式
            return {
                'data': data,
                'metadata': {
                    'source': self.metadata.name,
                    'data_type': data_type,
                    'timestamp': datetime.utcnow().isoformat(),
                    'params': params
                }
            }
            
        except Exception as e:
            # 发送错误事件
            await self.emit_event('data_fetch_error', {
                'plugin': self.metadata.name,
                'data_type': data_type,
                'error': str(e),
                'params': params
            })
            raise
            
    async def validate_params(self, data_type: str, params: Dict[str, Any]) -> bool:
        """验证参数"""
        if data_type == "custom_data":
            required_params = ['symbol', 'start_date']
            for param in required_params:
                if param not in params:
                    return False
        return True
        
    def get_supported_data_types(self) -> List[str]:
        """获取支持的数据类型"""
        return ["custom_data", "special_indicators"]
        
    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            url = f"{self.config['endpoint_url']}/health"
            response = self._client.get(url, timeout=10)
            return response.status_code == 200
        except:
            return False
            
    async def get_rate_limit_info(self) -> Dict[str, Any]:
        """获取速率限制信息"""
        return {
            "requests_per_second": 10,
            "requests_per_minute": 600,
            "requests_per_hour": 10000,
            "concurrent_requests": 5
        }
```

4. **编写测试用例**

```python
# tests/test_plugin.py
import pytest
from unittest.mock import Mock, patch
from plugin import MyCustomPlugin

@pytest.fixture
def plugin_config():
    return {
        "api_key": "test_key",
        "endpoint_url": "https://api.example.com",
        "timeout": 30
    }

@pytest.fixture
def plugin(plugin_config):
    return MyCustomPlugin(plugin_config)

@pytest.mark.asyncio
async def test_plugin_initialization(plugin):
    """测试插件初始化"""
    with patch('requests.Session') as mock_session:
        mock_client = Mock()
        mock_session.return_value = mock_client
        
        # 模拟成功的连接测试
        plugin.test_connection = Mock(return_value=True)
        
        result = await plugin.initialize()
        assert result is True
        assert plugin.status == PluginStatus.ACTIVE

@pytest.mark.asyncio
async def test_get_data(plugin):
    """测试数据获取"""
    # 先初始化插件
    plugin._client = Mock()
    plugin.status = PluginStatus.ACTIVE
    
    # 模拟API响应
    mock_response = Mock()
    mock_response.json.return_value = {"test": "data"}
    mock_response.raise_for_status.return_value = None
    plugin._client.get.return_value = mock_response
    
    result = await plugin.get_data("custom_data", {"symbol": "TEST"})
    
    assert "data" in result
    assert "metadata" in result
    assert result["data"]["test"] == "data"

@pytest.mark.asyncio
async def test_validate_params(plugin):
    """测试参数验证"""
    # 有效参数
    valid_params = {"symbol": "TEST", "start_date": "2024-01-01"}
    assert await plugin.validate_params("custom_data", valid_params) is True
    
    # 无效参数
    invalid_params = {"symbol": "TEST"}  # 缺少start_date
    assert await plugin.validate_params("custom_data", invalid_params) is False
```

5. **插件打包和分发**

```python
# setup.py
from setuptools import setup, find_packages

setup(
    name="udm-custom-plugin",
    version="1.0.0",
    description="UDM自定义数据源插件",
    author="Your Name",
    author_email="<EMAIL>",
    packages=find_packages(),
    install_requires=[
        "requests>=2.25.0",
        "pandas>=1.3.0"
    ],
    entry_points={
        'udm.plugins': [
            'my_custom_plugin = plugin:MyCustomPlugin',
        ],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
    ],
)
```

### 插件最佳实践

1. **错误处理**
   - 使用适当的异常类型
   - 提供详细的错误信息
   - 实现优雅的降级机制

2. **性能优化**
   - 使用连接池和会话复用
   - 实现适当的缓存机制
   - 避免阻塞操作

3. **日志记录**
   - 使用结构化日志
   - 记录关键操作和错误
   - 避免记录敏感信息

4. **配置管理**
   - 提供合理的默认值
   - 支持环境变量配置
   - 验证配置参数

5. **测试覆盖**
   - 编写全面的单元测试
   - 进行集成测试
   - 测试错误情况

## 插件生态系统

### 官方插件库

```
udm-plugins/
├── data-sources/
│   ├── tushare-plugin/
│   ├── akshare-plugin/
│   ├── mairui-plugin/
│   ├── yahoo-finance-plugin/
│   └── alpha-vantage-plugin/
├── processors/
│   ├── technical-indicators/
│   ├── data-validators/
│   ├── format-converters/
│   └── statistical-analysis/
├── middleware/
│   ├── authentication/
│   ├── rate-limiting/
│   ├── caching/
│   └── monitoring/
└── extensions/
    ├── notification-services/
    ├── report-generators/
    ├── data-exporters/
    └── workflow-automation/
```

### 社区插件

- **第三方数据源插件**: 支持更多金融数据提供商
- **自定义指标插件**: 实现专有技术指标算法
- **行业特定插件**: 针对特定行业的数据处理
- **集成插件**: 与其他系统和工具的集成

### 插件管理工具

```bash
# UDM插件CLI工具
udm-plugin --help

# 搜索插件
udm-plugin search tushare

# 安装插件
udm-plugin install udm-tushare-plugin

# 列出已安装插件
udm-plugin list

# 更新插件
udm-plugin update udm-tushare-plugin

# 卸载插件
udm-plugin uninstall udm-tushare-plugin

# 开发工具
udm-plugin create my-plugin --type data-source
udm-plugin validate ./my-plugin
udm-plugin package ./my-plugin
udm-plugin publish ./my-plugin
```

这个插件化框架设计为统一数据管理系统提供了强大的扩展能力，确保系统能够灵活适应各种业务需求和技术变化，同时保持核心系统的稳定性和性能。