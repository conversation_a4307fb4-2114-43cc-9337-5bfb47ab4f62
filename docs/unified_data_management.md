# 统一数据管理系统

基于现有量化分析系统的可扩展统一数据管理系统，提供智能缓存、多级路由、插件化架构和事件驱动机制。

## 核心特性

### 🚀 性能优化
- **多级智能缓存**: L1内存 → L2Redis → L3数据库 → L4外部API
- **响应时间**: < 50ms (缓存命中)
- **并发支持**: 1000+ 并发请求
- **API调用减少**: 70%+
- **缓存命中率**: > 80%

### 🔧 架构特性
- **统一数据接口**: `UnifiedDataManager` 提供一致的API
- **智能数据路由**: `SmartDataRouter` 实现多级缓存决策
- **插件化架构**: 数据源、处理器、中间件插件
- **事件驱动**: `DataEventBus` 处理系统事件
- **配置驱动**: YAML配置管理所有策略

### 🔄 向后兼容
- **现有API保持不变**: 无缝集成到现有代码
- **渐进式迁移**: 可选择性启用新功能
- **混合模式**: 新旧系统并存

## 快速开始

### 1. 基本使用

```python
from app.core.data import get_data_manager, DataType

# 获取数据管理器
data_manager = get_data_manager()

# 获取股票信息
result = await data_manager.get_stock_info("000001")
if result.is_success:
    print(f"股票信息: {result.data}")
    print(f"缓存命中: {result.cache_hit}")
    print(f"响应时间: {result.response_time}s")

# 获取股票日线数据
result = await data_manager.get_stock_daily(
    stock_code="000001",
    start_date="2024-01-01",
    end_date="2024-01-31"
)
```

### 2. 智能缓存装饰器

```python
from app.utils.decorators import smart_data_cache, cache_stock_info
from app.core.data.models import DataType, CacheStrategy

# 使用预定义装饰器
@cache_stock_info(cache_ttl=1800)  # 30分钟缓存
async def get_stock_basic_info(stock_code: str):
    # 你的数据获取逻辑
    return {"code": stock_code, "name": "股票名称"}

# 使用智能缓存装饰器
@smart_data_cache(
    data_type=DataType.STOCK_DAILY,
    cache_strategy=CacheStrategy.CACHE_THROUGH,
    cache_ttl=3600
)
async def get_daily_data(stock_code: str, start_date: str, end_date: str):
    # 你的数据获取逻辑
    return [{"date": start_date, "close": 10.0}]
```

### 3. 向后兼容使用

```python
from app.core.data.compatibility import CompatibilityDataFetcher

# 替代现有的DataFetcher，保持接口不变
fetcher = CompatibilityDataFetcher(enable_unified_management=True)

# 现有代码无需修改
stock_list = await fetcher.get_stock_list()
daily_data = await fetcher.get_daily_data("000001", start_date, end_date)
realtime_data = await fetcher.get_realtime_quotes(["000001", "000002"])
```

## 核心组件

### UnifiedDataManager
统一数据管理器，提供主要的数据访问接口。

```python
# 初始化
data_manager = await init_data_manager()

# 基本数据获取
result = await data_manager.get_data(
    data_type=DataType.STOCK_INFO,
    params={"stock_code": "000001"},
    cache_strategy=CacheStrategy.CACHE_FIRST,
    routing_strategy=RoutingStrategy.FASTEST
)

# 批量数据获取
requests = [
    {"data_type": DataType.STOCK_INFO, "params": {"stock_code": "000001"}},
    {"data_type": DataType.STOCK_INFO, "params": {"stock_code": "000002"}}
]
results = await data_manager.batch_get_data(requests)
```

### SmartDataRouter
智能数据路由器，实现多级缓存和数据源路由。

```python
# 注册自定义数据源
async def custom_data_source(request):
    return DataResult(
        request_id=request.request_id,
        success=True,
        data={"custom": "data"}
    )

data_manager.register_data_source(
    name="custom_source",
    handler=custom_data_source,
    priority=100
)
```

### 事件系统
事件驱动架构支持。

```python
from app.core.data.events import get_event_bus

event_bus = get_event_bus()

# 订阅事件
@event_bus.on("request.completed")
async def on_request_completed(event):
    print(f"请求完成: {event.data}")

# 发送事件
await event_bus.emit("custom.event", {"message": "Hello"})
```

## 配置管理

系统配置通过 `config/data_management.yaml` 文件管理：

```yaml
# 基本配置
data_management:
  enabled: true
  debug: false

# 缓存配置
cache:
  enabled: true
  strategy: "cache_first"
  l1_memory:
    enabled: true
    max_size: 500
    ttl: 300
  l2_redis:
    enabled: true
    ttl: 3600

# 路由配置
routing:
  strategy: "fastest"
  timeout: 30.0
  max_retries: 3
```

## 监控和统计

```python
# 获取系统统计
stats = data_manager.get_stats()
print(f"总请求数: {stats['manager_stats']['total_requests']}")
print(f"缓存命中率: {stats['routing_stats']['cache_hit_rate']}")

# 健康检查
health = await data_manager.health_check()
print(f"系统健康状态: {health}")

# 缓存管理
await data_manager.invalidate_cache(
    data_type=DataType.STOCK_INFO,
    params={"stock_code": "000001"}
)
```

## 插件开发

### 数据源插件

```python
from app.core.data.plugins import DataSourcePlugin

class CustomDataSource(DataSourcePlugin):
    def __init__(self):
        super().__init__(
            name="custom_source",
            supported_data_types=[DataType.STOCK_INFO],
            priority=100
        )
    
    async def initialize(self):
        # 初始化逻辑
        pass
    
    async def fetch_data(self, request):
        # 数据获取逻辑
        return DataResult(
            request_id=request.request_id,
            success=True,
            data={"custom": "data"}
        )
```

### 中间件插件

```python
from app.core.data.plugins import MiddlewarePlugin

class LoggingMiddleware(MiddlewarePlugin):
    def __init__(self):
        super().__init__(name="logging", middleware_type="request")
    
    async def process_request(self, request):
        print(f"处理请求: {request.request_id}")
        return request
```

## 性能测试

运行性能测试：

```bash
# 运行所有测试
pytest tests/unit/core/data/ -v

# 运行特定测试
pytest tests/unit/core/data/test_manager.py -v

# 运行性能基准测试
python tests/benchmark/data_management_benchmark.py
```

## 使用示例

完整的使用示例请参考：
- `examples/unified_data_management_usage.py` - 基本使用示例
- `tests/unit/core/data/` - 单元测试示例

## 迁移指南

### 从现有DataFetcher迁移

1. **渐进式迁移**（推荐）：
```python
# 启用统一数据管理系统
from app.core.data.compatibility import enable_unified_data_management
enable_unified_data_management()

# 现有代码无需修改，自动使用新系统
```

2. **直接迁移**：
```python
# 旧代码
fetcher = DataFetcherFactory.create_fetcher("mairui")
data = await fetcher.get_stock_list()

# 新代码
data_manager = get_data_manager()
result = await data_manager.get_stock_info()
data = result.data if result.is_success else []
```

## 故障排除

### 常见问题

1. **缓存未命中**：
   - 检查缓存配置
   - 验证缓存键生成
   - 确认TTL设置

2. **数据源连接失败**：
   - 检查网络连接
   - 验证API密钥
   - 查看健康检查状态

3. **性能问题**：
   - 调整并发请求数
   - 优化缓存策略
   - 检查数据源响应时间

### 日志配置

```yaml
logging:
  enabled: true
  level: "INFO"
  file:
    enabled: true
    path: "logs/data_management.log"
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。