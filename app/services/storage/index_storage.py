"""
指数存储服务模块

提供指数数据的数据库操作功能。
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import date
from sqlalchemy import select, delete, and_, or_, desc, asc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.models.index import IndexInfo, IndexDaily, IndexWeekly, IndexMonthly
from app.services.data_fetcher.provider.mairui_provider import MairuiAdapter

logger = logging.getLogger(__name__)

class IndexStorageService:
    """指数存储服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.adapter = MairuiAdapter()
    
    async def get_index_list(
        self,
        skip: int = 0,
        limit: int = 100,
        exchange: Optional[str] = None,
        is_active: Optional[bool] = None,
        search: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取指数列表，支持分页和筛选
        
        Args:
            skip: 跳过的记录数
            limit: 返回的最大记录数
            exchange: 按交易所筛选
            is_active: 按是否活跃筛选
            search: 搜索关键词（指数代码或名称）
            
        Returns:
            Dict[str, Any]: 包含指数列表和总数的字典
        """
        # 构建查询条件
        query = select(IndexInfo)
        conditions = []
        
        if exchange:
            conditions.append(IndexInfo.exchange == exchange.upper())
        
        # 默认只返回活跃的指数，除非明确指定
        if is_active is not None:
            conditions.append(IndexInfo.is_active == is_active)
        else:
            conditions.append(IndexInfo.is_active == True)
        
        if search:
            search_condition = or_(
                IndexInfo.code.ilike(f"%{search}%"),
                IndexInfo.name.ilike(f"%{search}%")
            )
            conditions.append(search_condition)
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 计算总数
        from sqlalchemy import func
        count_query = select(func.count()).select_from(
            select(IndexInfo).where(and_(*conditions)) if conditions else select(IndexInfo)
        )
        count_result = await self.db.execute(count_query)
        total = count_result.scalar()
        
        # 分页查询
        query = query.offset(skip).limit(limit).order_by(IndexInfo.code)
        result = await self.db.execute(query)
        indices = result.scalars().all()
        
        # 转换为字典列表
        index_list = []
        for index in indices:
            index_dict = {
                "code": index.code,
                "name": index.name,
                "exchange": index.exchange,
                "is_active": index.is_active
            }
            index_list.append(index_dict)
        
        return {
            "indices": index_list,
            "total": total,
            "skip": skip,
            "limit": limit
        }
    
    async def get_index_info(self, index_code: str) -> Optional[Dict[str, Any]]:
        """
        根据指数代码获取单个指数信息
        
        Args:
            index_code: 指数代码
            
        Returns:
            Optional[Dict[str, Any]]: 指数信息字典，如果不存在或不活跃返回None
        """
        result = await self.db.execute(
            select(IndexInfo).where(
                and_(IndexInfo.code == index_code, IndexInfo.is_active == True)
            )
        )
        index = result.scalar_one_or_none()
        
        if index:
            return {
                "code": index.code,
                "name": index.name,
                "exchange": index.exchange,
                "is_active": index.is_active
            }
        return None
    
    async def save_index_info(self, index_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        保存指数信息到数据库
        
        Args:
            index_data: 指数信息字典
            
        Returns:
            Dict[str, Any]: 保存后的指数信息
        """
        index = IndexInfo(**index_data)
        self.db.add(index)
        await self.db.commit()
        await self.db.refresh(index)
        
        return self.adapter.normalize_index_info(index_data)
    
    async def replace_all_index_info(self, index_list: List[Dict[str, Any]]) -> Dict[str, int]:
        """
        替换数据库中的所有指数信息，使用假删除模式以避免外键约束问题
        
        Args:
            index_list: 指数信息列表
            
        Returns:
            Dict[str, int]: 包含删除和插入数量的字典
        """
        from sqlalchemy import update
        
        # 1. 先将所有现有指数设为非活跃状态（假删除）
        update_stmt = update(IndexInfo).values(is_active=False)
        await self.db.execute(update_stmt)
        
        # 2. 获取新列表中的所有代码
        new_codes = set()
        for info in index_list:
            if info.get('code'):
                new_codes.add(info['code'])
        
        # 3. 处理新增和修改
        inserted_count = 0
        updated_count = 0
        processed_codes = set()
        
        for index_data in index_list:
            try:
                if not index_data or not index_data.get('code'):
                    continue
                    
                code = index_data['code']
                processed_codes.add(code)
                
                # 查找是否已存在该指数信息
                stmt = select(IndexInfo).where(IndexInfo.code == code)
                existing = (await self.db.execute(stmt)).scalar_one_or_none()
                
                if existing:
                    # 更新已存在的记录
                    for key, value in index_data.items():
                        setattr(existing, key, value)
                    # 确保激活状态
                    existing.is_active = True
                    updated_count += 1
                else:
                    # 创建新记录
                    index_data['is_active'] = True  # 新增的记录默认激活
                    new_index = IndexInfo(**index_data)
                    self.db.add(new_index)
                    inserted_count += 1
                    
            except Exception as e:
                logger.error(f"插入指数信息失败 {index_data}: {e}")
                continue
        
        await self.db.commit()
        
        # 4. 计算被假删除的指数数量
        all_existing_stmt = select(IndexInfo).where(IndexInfo.is_active == False)
        all_existing = (await self.db.execute(all_existing_stmt)).scalars().all()
        deactivated_count = len(all_existing)
        
        logger.info(f"成功替换指数信息：更新{updated_count}条，插入{inserted_count}条，假删除{deactivated_count}条")
        
        return {
            "deleted": deactivated_count,
            "inserted": inserted_count,
            "updated": updated_count
        }
    
    async def save_index_daily_data(
        self,
        index_code: str,
        daily_data_list: List[Dict[str, Any]]
    ) -> int:
        """
        批量保存指数日线数据
        
        Args:
            index_code: 指数代码
            daily_data_list: 日线数据列表
            
        Returns:
            int: 实际插入的记录数
        """
        inserted_count = 0
        
        for raw_data in daily_data_list:
            try:
                # 标准化日线数据
                normalized_data = self.adapter.normalize_index_daily_data(raw_data)
                if not normalized_data:
                    continue
                
                # 添加指数代码
                normalized_data["index_code"] = index_code
                
                # 检查是否已存在该日期的数据
                existing = await self.db.execute(
                    select(IndexDaily).where(
                        and_(
                            IndexDaily.index_code == index_code,
                            IndexDaily.trade_date == normalized_data["trade_date"]
                        )
                    )
                )
                
                if not existing.scalar_one_or_none():
                    daily_record = IndexDaily(**normalized_data)
                    self.db.add(daily_record)
                    inserted_count += 1
                    
            except Exception as e:
                logger.error(f"保存指数日线数据失败 {index_code} {raw_data}: {e}")
                continue
        
        await self.db.commit()
        return inserted_count
    
    async def get_index_daily_data(
        self,
        index_code: str,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        获取指数日线数据
        
        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 日线数据列表
        """
        query = select(IndexDaily).where(IndexDaily.index_code == index_code)
        
        # 添加日期过滤条件
        if start_date:
            query = query.where(IndexDaily.trade_date >= start_date)
        if end_date:
            query = query.where(IndexDaily.trade_date <= end_date)
        
        # 按日期降序排列
        query = query.order_by(desc(IndexDaily.trade_date))
        
        # 限制返回数量
        if limit:
            query = query.limit(limit)
        
        result = await self.db.execute(query)
        daily_records = result.scalars().all()
        
        # 转换为字典列表
        daily_data = []
        for record in daily_records:
            daily_dict = {
                "index_code": record.index_code,
                "trade_date": record.trade_date,
                "open": record.open,
                "high": record.high,
                "low": record.low,
                "close": record.close,
                "volume": record.volume,
                "amount": record.amount,
                "prev_close": record.prev_close,
                "change_pct": record.change_pct
            }
            daily_data.append(daily_dict)
        
        return daily_data
    
    async def get_index_suggestions(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        根据输入提供指数代码或名称建议
        
        Args:
            query: 搜索查询
            limit: 返回的最大建议数
            
        Returns:
            List[Dict[str, Any]]: 指数建议列表
        """
        search_condition = or_(
            IndexInfo.code.ilike(f"%{query}%"),
            IndexInfo.name.ilike(f"%{query}%")
        )
        
        result = await self.db.execute(
            select(IndexInfo)
            .where(and_(search_condition, IndexInfo.is_active == True))
            .order_by(IndexInfo.code)
            .limit(limit)
        )
        
        indices = result.scalars().all()
        
        suggestions = []
        for index in indices:
            suggestion = {
                "code": index.code,
                "name": index.name,
                "exchange": index.exchange,
                "is_active": index.is_active
            }
            suggestions.append(suggestion)
        
        return suggestions
    
    async def batch_save_index_daily_data(self, daily_data_list: List[Dict[str, Any]]) -> int:
        """
        批量保存指数日线数据
        
        Args:
            daily_data_list: 指数日线数据列表
            
        Returns:
            int: 成功插入的记录数
        """
        if not daily_data_list:
            return 0
            
        inserted_count = 0
        
        for raw_data in daily_data_list:
            try:
                # 标准化数据格式
                normalized_data = self.adapter.normalize_index_daily_data(raw_data)
                
                # 从原始数据中提取index_code，或者从已标准化的数据中获取
                index_code = raw_data.get("index_code") or normalized_data.get("index_code")
                
                if not index_code:
                    logger.warning(f"跳过无效的指数日线数据（缺少index_code）: {raw_data}")
                    continue
                
                # 添加index_code到标准化数据中
                normalized_data["index_code"] = index_code
                
                # 检查记录是否已存在
                existing = await self.db.execute(
                    select(IndexDaily).where(
                        and_(
                            IndexDaily.index_code == index_code,
                            IndexDaily.trade_date == normalized_data["trade_date"]
                        )
                    )
                )
                
                if not existing.scalar_one_or_none():
                    daily_record = IndexDaily(**normalized_data)
                    self.db.add(daily_record)
                    inserted_count += 1
                    
            except Exception as e:
                logger.error(f"批量保存指数日线数据失败 {raw_data}: {e}")
                continue
        
        await self.db.commit()
        logger.info(f"批量保存指数日线数据完成，插入 {inserted_count} 条记录")
        return inserted_count