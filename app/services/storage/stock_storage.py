"""
股票数据存储服务实现
"""
from app.core.logging import logging
from app.core.config import settings
from typing import List, Optional, Dict, Any, Union, Type, cast, Sequence
from datetime import datetime
import inspect

from sqlalchemy import select, delete, update, and_, between
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.stock import (
    StockInfo, StockDaily, StockWeekly, StockMonthly, IndicatorVersion,
    StockIndicator
)
from app.services.storage.base import BaseStorageService
from app.core.exceptions import DatabaseException, QueueException

# 队列相关导入（条件导入，避免Redis未安装时报错）
if settings.QUEUE_ENABLED:
    try:
        from app.services.queue import get_batch_queue, TaskType, TaskPriority
        QUEUE_AVAILABLE = True
        logging.getLogger(__name__).info("Redis队列系统已启用")
    except ImportError as e:
        QUEUE_AVAILABLE = False
        logging.getLogger(__name__).warning(f"Redis队列导入失败 (ImportError): {e}")
    except Exception as e:
        QUEUE_AVAILABLE = False
        logging.getLogger(__name__).warning(f"Redis队列导入失败 (其他错误): {e}")
else:
    QUEUE_AVAILABLE = False
    logging.getLogger(__name__).info("Redis队列功能已禁用")

class StockStorageService(BaseStorageService):
    """基于SQLAlchemy的股票数据存储服务实现"""
    
    logger = logging.getLogger(__name__ + ".StockStorageService")

    def __init__(self, db: AsyncSession):
        super().__init__(db)
        self._cache: Dict[str, Any] = {}  # 简单的内存缓存

    async def save_stock_info(self, stock_info: Dict[str, Any], use_queue: bool = True) -> StockInfo:
        """保存股票基本信息
        
        Args:
            stock_info: 股票信息数据
            use_queue: 是否使用Redis队列（默认True）
            
        Returns:
            StockInfo: 股票信息对象
        """
        # 如果启用队列且队列可用，则使用队列处理
        if use_queue and QUEUE_AVAILABLE and settings.QUEUE_ENABLED:
            # 将单条数据转换为列表进行批量处理
            task_result = await self._enqueue_batch_save(
                TaskType.BATCH_SAVE_STOCK_INFO,
                None,  # 股票信息保存不需要单一股票代码
                [stock_info],
                TaskPriority.HIGH
            )
            # 队列模式下返回一个空的对象，实际保存由消费者处理
            self.logger.info(f"股票信息已加入队列: task_id={task_result.get('task_id')}")
            # 返回一个模拟对象用于兼容性
            return StockInfo(**stock_info)
        
        # 否则使用直接数据库操作
        return await self._save_stock_info_direct(stock_info)
    
    async def _save_stock_info_direct(self, stock_info: Dict[str, Any]) -> StockInfo:
        """直接保存股票基本信息到数据库（原有逻辑）"""
        try:
            info = StockInfo(**stock_info)
            self.db.add(info)
            await self.db.commit()
            return info
        except Exception as e:
            raise DatabaseException(f"保存股票信息失败: {str(e)}")

    async def save_stock_daily(
        self,
        stock_code: str,
        daily_data: Dict[str, Any],
        use_queue: bool = True
    ) -> StockDaily:
        """保存股票日线数据到统一表
        
        Args:
            stock_code: 股票代码
            daily_data: 日线数据
            use_queue: 是否使用Redis队列（默认True）
            
        Returns:
            StockDaily: 股票日线数据对象
        """
        # 如果启用队列且队列可用，则使用队列处理
        if use_queue and QUEUE_AVAILABLE and settings.QUEUE_ENABLED:
            # 将单条数据转换为列表进行批量处理
            task_result = await self._enqueue_batch_save(
                TaskType.BATCH_SAVE_DAILY,
                stock_code,
                [daily_data]
            )
            # 队列模式下返回一个空的对象，实际保存由消费者处理
            self.logger.info(f"日线数据已加入队列: task_id={task_result.get('task_id')}")
            # 返回一个模拟对象用于兼容性
            data = daily_data.copy()
            data['stock_code'] = stock_code
            return StockDaily(**data)
        
        # 否则使用直接数据库操作
        return await self._save_stock_daily_direct(stock_code, daily_data)
    
    async def _save_stock_daily_direct(
        self,
        stock_code: str,
        daily_data: Dict[str, Any]
    ) -> StockDaily:
        """直接保存股票日线数据到数据库（原有逻辑）"""
        try:
            # 创建并保存数据
            data = daily_data.copy()
            data['stock_code'] = stock_code
            daily = StockDaily(**data)
            self.db.add(daily)
            await self.db.commit()
            return daily
        except Exception as e:
            raise DatabaseException(f"保存日线数据失败: {str(e)}")

    async def batch_save_stock_info(
        self,
        stock_info_list: List[Dict[str, Any]],
        use_queue: bool = True
    ) -> List[StockInfo]:
        """批量保存股票基本信息,以新的list为标准，没有的新增，已有的修改，存在数据库但是新的list没有的使用假删除
        
        Args:
            stock_info_list: 股票信息列表
            use_queue: 是否使用Redis队列（默认True）
            
        Returns:
            保存的股票信息对象列表
        """
        if not stock_info_list:
            return []
        
        # 如果启用队列且队列可用，则使用队列处理
        if use_queue and QUEUE_AVAILABLE and settings.QUEUE_ENABLED:
            task_id = await self._enqueue_batch_save(
                TaskType.BATCH_SAVE_STOCK_INFO,
                None,  # 股票信息保存不需要单一股票代码
                stock_info_list,
                TaskPriority.HIGH
            )
            # 队列模式下返回空列表，实际保存由消费者处理
            self.logger.info(f"股票信息已加入队列: {task_id}")
            return []
        
        # 否则使用直接数据库操作
        return await self._batch_save_stock_info_direct(stock_info_list)

    async def _batch_save_stock_info_direct(
        self,
        stock_info_list: List[Dict[str, Any]]
    ) -> List[StockInfo]:
        try:
            result = []
            
            # 获取新列表中的所有full_code
            new_full_codes = set()
            for info in stock_info_list:
                info_copy = info.copy()
                if 'code' in info_copy and info_copy['code']:
                    # 如果股票代码包含点号，只保留点号前面的部分
                    info_copy['code'] = info_copy['code'].split('.')[0]
                new_full_codes.add(info_copy['full_code'])
            
            # 处理新增和修改
            for info in stock_info_list:
                # 处理股票代码，提取基础代码部分（去掉后缀）
                info_copy = info.copy()
                if 'code' in info_copy and info_copy['code']:
                    # 如果股票代码包含点号，只保留点号前面的部分
                    info_copy['code'] = info_copy['code'].split('.')[0]
                
                # 使用抽取的私有方法转换trade_date或listing_date
                if 'listing_date' in info_copy:
                    # 临时将listing_date重命名为trade_date进行转换，然后改回来
                    if info_copy['listing_date'] is not None:
                        temp_data = {'trade_date': info_copy['listing_date']}
                        self._convert_trade_date_to_date(temp_data, info_copy.get('code', ''))
                        info_copy['listing_date'] = temp_data['trade_date']
                
                # 查找是否已存在该股票信息
                stmt = select(StockInfo).where(StockInfo.code == info_copy['code']).where(StockInfo.exchange == info_copy['exchange'])
                existing = (await self.db.execute(stmt)).scalar_one_or_none()
                
                if existing:
                    # 更新已存在的记录
                    for key, value in info_copy.items():
                        setattr(existing, key, value)
                    # 确保激活状态
                    existing.is_active = True
                    result.append(existing)
                else:
                    # 创建新记录
                    info_copy['is_active'] = True  # 新增的记录默认激活
                    new_stock = StockInfo(**info_copy)
                    self.db.add(new_stock)
                    result.append(new_stock)
            
            # 处理假删除：将数据库中存在但新列表中不存在的记录标记为非激活
            all_existing_stmt = select(StockInfo).where(StockInfo.is_active == True)
            all_existing = (await self.db.execute(all_existing_stmt)).scalars().all()
            
            deactivated_count = 0
            for existing_stock in all_existing:
                if existing_stock.full_code not in new_full_codes:
                    existing_stock.is_active = False
                    deactivated_count += 1
            
            await self.db.commit()
            self.logger.info(f"成功保存{len(result)}条股票信息，假删除{deactivated_count}条股票信息")
            return result
        except Exception as e:
            await self.db.rollback()
            raise DatabaseException(f"批量保存股票信息失败: {str(e)}")

    async def batch_save_stock_daily(
        self,
        stock_code: str,
        daily_data_list: List[Dict[str, Any]],
        use_queue: bool = True
    ) -> Dict[str, int]:
        """批量保存股票日线数据到统一表（支持UPSERT）
        
        Args:
            stock_code: 股票代码
            daily_data_list: 日线数据列表
            use_queue: 是否使用Redis队列（默认True）
            
        Returns:
            保存统计信息 {'inserted': 新增数量, 'updated': 更新数量, 'skipped': 跳过数量}
        """
        if not daily_data_list:
            return {'inserted': 0, 'updated': 0, 'skipped': 0}
        
        # 如果启用队列且队列可用，则使用队列处理
        if use_queue and QUEUE_AVAILABLE and settings.QUEUE_ENABLED:
            return await self._enqueue_batch_save(
                TaskType.BATCH_SAVE_DAILY,
                stock_code,
                daily_data_list
            )
        
        # 否则使用直接数据库操作
        return await self._batch_save_stock_daily_direct(stock_code, daily_data_list)

    async def _enqueue_batch_save(
        self,
        task_type: 'TaskType',
        stock_code: str,
        data_list: List[Dict[str, Any]],
        priority: 'TaskPriority' = None
    ) -> Dict[str, int]:
        """将批量保存任务加入Redis队列
        
        Args:
            task_type: 任务类型
            stock_code: 股票代码
            data_list: 数据列表
            priority: 任务优先级
            
        Returns:
            队列任务统计信息
        """
        try:
            if not QUEUE_AVAILABLE:
                raise QueueException("Redis队列不可用")
            
            batch_queue = get_batch_queue()
            priority = priority or TaskPriority.MEDIUM
            
            # 根据任务类型调用相应的队列方法
            if task_type == TaskType.BATCH_SAVE_DAILY:
                task_id = await batch_queue.enqueue_daily_data(
                    data_list, stock_code, priority
                )
            elif task_type == TaskType.BATCH_SAVE_WEEKLY:
                task_id = await batch_queue.enqueue_weekly_data(
                    data_list, stock_code, priority
                )
            elif task_type == TaskType.BATCH_SAVE_MONTHLY:
                task_id = await batch_queue.enqueue_monthly_data(
                    data_list, stock_code, priority
                )
            elif task_type == TaskType.BATCH_SAVE_STOCK_INFO:
                task_id = await batch_queue.enqueue_stock_info(
                    data_list, priority
                )
            elif task_type == TaskType.BATCH_SAVE_INDICATORS:
                task_id = await batch_queue.enqueue_indicators(
                    data_list, stock_code, priority
                )
            else:
                raise QueueException(f"不支持的任务类型: {task_type}")
            
            # self.logger.info(f"任务已加入队列: {task_id}, 类型: {task_type}, 数据量: {len(data_list)}")
            
            # 返回队列统计信息
            return {
                'queued': len(data_list),
                'task_id': task_id,
                'inserted': 0,  # 队列模式下不立即执行
                'updated': 0,
                'skipped': 0
            }
            
        except Exception as e:
            self.logger.error(f"加入队列失败: {str(e)}")
            
            # 如果配置了降级，则使用直接数据库操作
            if settings.QUEUE_FALLBACK_TO_DIRECT:
                self.logger.warning("队列失败，降级为直接数据库操作")
                if task_type == TaskType.BATCH_SAVE_DAILY:
                    return await self._batch_save_stock_daily_direct(stock_code, data_list)
                elif task_type == TaskType.BATCH_SAVE_WEEKLY:
                    return await self._batch_save_weekly_data_direct(data_list)
                elif task_type == TaskType.BATCH_SAVE_MONTHLY:
                    return await self._batch_save_monthly_data_direct(data_list)
                elif task_type == TaskType.BATCH_SAVE_STOCK_INFO:
                    saved_stocks = await self.batch_save_stock_info(data_list)
                    return {'inserted': len(saved_stocks), 'updated': 0, 'skipped': 0}
                elif task_type == TaskType.BATCH_SAVE_INDICATORS:
                    indicators = await self.batch_save_indicators(stock_code, data_list)
                    return {'inserted': len(indicators), 'updated': 0, 'skipped': 0}
            
            raise QueueException(f"队列操作失败: {str(e)}")

    def _convert_trade_date_to_date(self, data: Dict[str, Any], stock_code: str = '') -> None:
        """转换trade_date字段从字符串到Python date对象
        
        Args:
            data: 包含trade_date字段的数据字典
            stock_code: 股票代码（用于日志）
        """
        if isinstance(data.get('trade_date'), str):
            from datetime import datetime
            try:
                # 尝试不同的日期格式
                trade_date_str = data['trade_date']
                if len(trade_date_str) == 8:  # YYYYMMDD
                    data['trade_date'] = datetime.strptime(trade_date_str, '%Y%m%d').date()
                elif len(trade_date_str) == 10:  # YYYY-MM-DD
                    data['trade_date'] = datetime.strptime(trade_date_str, '%Y-%m-%d').date()
                else:
                    data['trade_date'] = datetime.fromisoformat(trade_date_str).date()
            except (ValueError, TypeError) as e:
                self.logger.warning(f"日期格式转换失败 {stock_code} {data.get('trade_date', '')}: {str(e)}")

    async def _batch_save_stock_daily_direct(
        self,
        stock_code: str,
        daily_data_list: List[Dict[str, Any]]
    ) -> Dict[str, int]:
        """直接批量保存股票日线数据到数据库（简化版本）"""
        try:
            if not daily_data_list:
                return {'inserted': 0, 'updated': 0, 'skipped': 0}
            
            # 数据预处理
            processed_data = await self._prepare_daily_data(stock_code, daily_data_list)
            if not processed_data:
                return {'inserted': 0, 'updated': 0, 'skipped': len(daily_data_list)}
            
            # 查询现有数据
            existing_dates = await self._get_existing_dates(stock_code, processed_data)
            
            # 分类处理数据
            new_records, latest_data = self._classify_daily_data(processed_data, existing_dates)
            
            # 执行数据库操作
            stats = await self._execute_daily_operations(stock_code, new_records, latest_data, existing_dates)
            
            await self.db.commit()
            self._log_daily_stats(stock_code, stats)
            return stats
            
        except Exception as e:
            await self.db.rollback()
            self.logger.error(f"批量保存日线数据失败 {stock_code}: {str(e)}")
            raise DatabaseException(f"批量保存日线数据失败: {str(e)}")
    
    async def _prepare_daily_data(self, stock_code: str, daily_data_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """预处理日线数据：去重、转换日期、排序"""
        # 去重
        unique_data = {}
        for data in daily_data_list:
            trade_date = data.get('trade_date')
            if trade_date:
                unique_data[trade_date] = data.copy()
        
        # 转换日期格式
        processed_data = []
        for data in unique_data.values():
            data['stock_code'] = stock_code
            self._convert_trade_date_to_date(data, stock_code)
            if 'trade_date' in data and data['trade_date']:
                processed_data.append(data)
        
        # 按日期排序
        processed_data.sort(key=lambda x: x['trade_date'])
        return processed_data
    
    async def _get_existing_dates(self, stock_code: str, processed_data: List[Dict[str, Any]]) -> set:
        """查询现有的交易日期"""
        if not processed_data:
            return set()
        
        all_dates = [data['trade_date'] for data in processed_data]
        existing_stmt = select(StockDaily.trade_date).where(
            and_(
                StockDaily.stock_code == stock_code,
                StockDaily.trade_date.in_(all_dates)
            )
        )
        result = await self.db.execute(existing_stmt)
        return set(result.scalars().all())
    
    def _classify_daily_data(self, processed_data: List[Dict[str, Any]], existing_dates: set) -> tuple:
        """分类处理数据：新记录 vs 最新记录"""
        if not processed_data:
            return [], None
        
        latest_date = max(data['trade_date'] for data in processed_data)
        new_records = []
        latest_data = None
        
        for data in processed_data:
            trade_date = data['trade_date']
            if trade_date == latest_date:
                latest_data = data
            elif trade_date not in existing_dates:
                new_records.append(data)
        
        return new_records, latest_data
    
    async def _execute_daily_operations(
        self, 
        stock_code: str, 
        new_records: List[Dict[str, Any]], 
        latest_data: Dict[str, Any], 
        existing_dates: set
    ) -> Dict[str, int]:
        """执行数据库操作"""
        stats = {'inserted': 0, 'updated': 0, 'skipped': 0}
        
        # 批量插入新记录
        if new_records:
            stats['inserted'] = await self._insert_new_records(stock_code, new_records)
        
        # 处理最新数据
        if latest_data:
            latest_date = latest_data['trade_date']
            if latest_date in existing_dates:
                stats['updated'] = await self._update_latest_record(stock_code, latest_data)
            else:
                stats['inserted'] += await self._insert_new_records(stock_code, [latest_data])
        
        # 计算跳过的记录数
        total_processed = len(new_records) + (1 if latest_data else 0)
        for data in []:  # 这里处理被跳过的历史数据
            pass  # 历史数据直接跳过，不需要特殊处理
        
        return stats
    
    async def _insert_new_records(self, stock_code: str, records: List[Dict[str, Any]]) -> int:
        """批量插入新记录"""
        if not records:
            return 0
        
        try:
            daily_objects = [StockDaily(**data) for data in records]
            self.db.add_all(daily_objects)
            return len(records)
        except Exception as e:
            self.logger.warning(f"批量插入失败，降级为逐条插入 {stock_code}: {str(e)}")
            # 降级为逐条插入
            inserted = 0
            for data in records:
                try:
                    self.db.add(StockDaily(**data))
                    inserted += 1
                except Exception as e2:
                    self.logger.warning(f"插入记录失败 {stock_code} {data.get('trade_date')}: {str(e2)}")
            return inserted
    
    async def _update_latest_record(self, stock_code: str, latest_data: Dict[str, Any]) -> int:
        """更新最新记录"""
        from sqlalchemy import update
        
        update_data = {k: v for k, v in latest_data.items() if k not in ['stock_code', 'trade_date']}
        if not update_data:
            return 0
        
        try:
            update_stmt = update(StockDaily).where(
                and_(
                    StockDaily.stock_code == stock_code,
                    StockDaily.trade_date == latest_data['trade_date']
                )
            ).values(**update_data)
            
            result = await self.db.execute(update_stmt)
            return 1 if result.rowcount > 0 else 0
        except Exception as e:
            self.logger.warning(f"更新最新记录失败 {stock_code}: {str(e)}")
            return 0
    
    def _log_daily_stats(self, stock_code: str, stats: Dict[str, int]) -> None:
        """记录统计日志"""
        total = stats['inserted'] + stats['updated'] + stats['skipped']
        self.logger.debug(
            f"批量保存日线数据完成 {stock_code}: "
            f"总计{total}条, 新增{stats['inserted']}条, "
            f"更新{stats['updated']}条, 跳过{stats['skipped']}条"
        )

    async def get_stock_info(self, stock_code: str) -> Optional[StockInfo]:
        """获取股票基本信息"""
        # 先查询缓存（仅在启用缓存时）
        cache_key = f"stock_info:{stock_code}"
        if settings.CACHE_ENABLED and cache_key in self._cache:
            return self._cache[cache_key]

        try:
            result = await self.db.execute(
                select(StockInfo).where(
                    and_(StockInfo.code == stock_code, StockInfo.is_active == True)
                )
            )
            info = result.scalar_one_or_none()
            
            if not info:
                # 此处不应抛出异常，而是返回None，由API层处理404
                return None
            
            if inspect.iscoroutine(info):
                info = await info
            
            # 缓存结果（仅在启用缓存时）
            if info and settings.CACHE_ENABLED:
                self._cache[cache_key] = info
            return info
        except Exception as e:
            raise DatabaseException(f"获取股票信息失败: {str(e)}")

    async def get_stock_suggestions(
        self,
        query: str,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        根据查询字符串获取股票代码或名称建议
        """
        try:
            # 使用 or_ 进行模糊查询
            from sqlalchemy import or_
            
            search_query = f"%{query}%"
            
            stmt = (
                select(
                    StockInfo.code,
                    StockInfo.name,
                    StockInfo.exchange,
                    StockInfo.full_code,
                    StockInfo.industry,
                    StockInfo.listing_date
                )
                .where(
                    or_(
                        StockInfo.code.ilike(search_query),
                        StockInfo.name.ilike(search_query)
                    )
                )
                .limit(limit)
            )
            
            result = await self.db.execute(stmt)
            # 使用 .mappings().all() 直接获取字典列表
            suggestions = result.mappings().all()
            
            # 将结果转换为字典列表, 并处理日期格式
            return [dict(row) for row in suggestions]

        except Exception as e:
            self.logger.error(f"获取股票建议失败: {e}", exc_info=True)
            raise DatabaseException(f"获取股票建议失败: {str(e)}")

    async def get_stock_daily(
        self,
        stock_code: str,
        start_date: Optional[Union[datetime, str]] = None,
        end_date: Optional[Union[datetime, str]] = None
    ) -> List[StockDaily]:
        """获取股票日线数据"""
        try:
            if isinstance(start_date, str):
                start_date = datetime.strptime(start_date, "%Y-%m-%d")
            if isinstance(end_date, str):
                end_date = datetime.strptime(end_date, "%Y-%m-%d")

            # 构建查询条件
            conditions = [StockDaily.stock_code == stock_code]
            if start_date:
                conditions.append(StockDaily.trade_date >= start_date)
            if end_date:
                conditions.append(StockDaily.trade_date <= end_date)
            
            result = await self.db.execute(
                select(StockDaily).where(and_(*conditions)).order_by(StockDaily.trade_date)
            )
            return list(result.scalars().all())
        except Exception as e:
            raise DatabaseException(f"获取日线数据失败: {str(e)}")

    async def update_stock_info(
        self,
        stock_code: str,
        update_data: Dict[str, Any]
    ) -> StockInfo:
        """更新股票基本信息"""
        try:
            result = await self.db.execute(
                select(StockInfo).where(StockInfo.code == stock_code)
            )
            info = result.scalar_one_or_none()
            
            if not info:
                # 此处不应抛出异常，而是返回None，由API层处理404
                return None
            
            if inspect.iscoroutine(info):
                info = await info
            
            for key, value in update_data.items():
                setattr(info, key, value)
            
            await self.db.commit()
            await self.db.refresh(info) # 刷新以获取最新数据
            
            # 更新缓存（仅在启用缓存时）
            cache_key = f"stock_info:{stock_code}"
            if settings.CACHE_ENABLED:
                self._cache[cache_key] = info
            
            return info
        except Exception as e:
            await self.db.rollback()
            raise DatabaseException(f"更新股票信息失败: {str(e)}")

    async def delete_stock_info(self, stock_code: str) -> bool:
        """删除股票基本信息"""
        try:
            result = await self.db.execute(
                delete(StockInfo).where(StockInfo.code == stock_code)
            )
            
            # 删除缓存
            cache_key = f"stock_info:{stock_code}"
            self._cache.pop(cache_key, None)
            
            return result.rowcount > 0
        except Exception as e:
            raise DatabaseException(f"删除股票信息失败: {str(e)}")

    async def delete_stock_daily(
        self,
        stock_code: str,
        start_date: Optional[Union[datetime, str]] = None,
        end_date: Optional[Union[datetime, str]] = None
    ) -> bool:
        """删除股票日线数据"""
        try:
            if isinstance(start_date, str):
                start_date = datetime.strptime(start_date, "%Y-%m-%d")
            if isinstance(end_date, str):
                end_date = datetime.strptime(end_date, "%Y-%m-%d")

            conditions = [StockDaily.stock_code == stock_code]
            if start_date:
                conditions.append(StockDaily.trade_date >= start_date)
            if end_date:
                conditions.append(StockDaily.trade_date <= end_date)
            
            result = await self.db.execute(
                delete(StockDaily).where(and_(*conditions))
            )
            
            return result.rowcount > 0
        except Exception as e:
            raise DatabaseException(f"删除日线数据失败: {str(e)}")

    async def save_indicator(
        self,
        stock_code: str,
        indicator_type: str,
        values: Dict[str, Any],
        trade_date: datetime,
        data_frequency: str = 'D1',
        use_queue: bool = True
    ) -> StockIndicator:
        """保存技术指标数据
        
        Args:
            stock_code: 股票代码
            indicator_type: 指标类型
            values: 指标值
            trade_date: 交易日期
            data_frequency: 数据频率
            use_queue: 是否使用Redis队列（默认True）
            
        Returns:
            StockIndicator: 指标对象
        """
        # 如果启用队列且队列可用，则使用队列处理
        if use_queue and QUEUE_AVAILABLE and settings.QUEUE_ENABLED:
            # 构建指标数据
            indicator_data = {
                'indicator_type': indicator_type,
                'values': values,
                'trade_date': trade_date,
                'data_frequency': data_frequency
            }
            
            # 将单条数据转换为列表进行批量处理
            task_result = await self._enqueue_batch_save(
                TaskType.BATCH_SAVE_INDICATORS,
                stock_code,
                [indicator_data]
            )
            # 队列模式下返回一个空的对象，实际保存由消费者处理
            self.logger.info(f"指标数据已加入队列: task_id={task_result.get('task_id')}")
            
            # 返回一个模拟对象用于兼容性（需要版本信息）
            version = await self.get_current_indicator_version(indicator_type)
            if not version:
                raise DatabaseException(f"指标 {indicator_type} 没有有效的版本信息")
                
            return StockIndicator(
                stock_code=stock_code,
                indicator_type=indicator_type,
                version_hash=version.version_hash,
                values=values,
                trade_date=trade_date,
                data_frequency=data_frequency
            )
        
        # 否则使用直接数据库操作
        return await self._save_indicator_direct(stock_code, indicator_type, values, trade_date, data_frequency)
    
    async def _save_indicator_direct(
        self,
        stock_code: str,
        indicator_type: str,
        values: Dict[str, Any],
        trade_date: datetime,
        data_frequency: str = 'D1'
    ) -> StockIndicator:
        """直接保存技术指标数据到数据库（原有逻辑）"""
        try:
            # 获取当前指标版本
            version = await self.get_current_indicator_version(indicator_type)
            if not version:
                raise DatabaseException(f"指标 {indicator_type} 没有有效的版本信息")

            indicator = StockIndicator(
                stock_code=stock_code,
                indicator_type=indicator_type,
                version_hash=version.version_hash,
                values=values,
                trade_date=trade_date,
                data_frequency=data_frequency
            )
            self.db.add(indicator)
            await self.db.commit()
            return indicator
        except Exception as e:
            raise DatabaseException(f"保存指标数据失败: {str(e)}")

    async def batch_save_indicators(
        self,
        stock_code: str,
        indicators_data: List[Dict[str, Any]],
        use_queue: bool = True
    ) -> List[StockIndicator]:
        """批量保存技术指标数据
        
        Args:
            stock_code: 股票代码
            indicators_data: 指标数据列表
            use_queue: 是否使用Redis队列（默认True）
            
        Returns:
            保存的指标对象列表
        """
        if not indicators_data:
            return []
        
        # 如果启用队列且队列可用，则使用队列处理
        if use_queue and QUEUE_AVAILABLE and settings.QUEUE_ENABLED:
            task_id = await self._enqueue_batch_save(
                TaskType.BATCH_SAVE_INDICATORS,
                stock_code,
                indicators_data
            )
            # 队列模式下返回空列表，实际保存由消费者处理
            self.logger.info(f"指标数据已加入队列: {task_id}")
            return []
        
        # 否则使用直接数据库操作
        return await self._batch_save_indicators_direct(stock_code, indicators_data)

    async def _batch_save_indicators_direct(
        self,
        stock_code: str,
        indicators_data: List[Dict[str, Any]]
    ) -> List[StockIndicator]:
        """批量保存技术指标数据"""
        try:
            # 获取所有涉及的指标类型的当前版本
            indicator_types = {data['indicator_type'] for data in indicators_data}
            versions = {}
            for indicator_type in indicator_types:
                version = await self.get_current_indicator_version(indicator_type)
                if not version:
                    raise DatabaseException(f"指标 {indicator_type} 没有有效的版本信息")
                versions[indicator_type] = version

            # 创建所有指标对象
            indicators = []
            for data in indicators_data:
                data = data.copy()
                indicator_type = data['indicator_type']
                data['stock_code'] = stock_code
                data['version_hash'] = versions[indicator_type].version_hash
                
                # 使用抽取的私有方法转换trade_date
                self._convert_trade_date_to_date(data, stock_code)
                
                indicator = StockIndicator(**data)
                indicators.append(indicator)

            # 使用 add_all 批量添加
            self.db.add_all(indicators)
            await self.db.commit()
            return indicators
        except Exception as e:
            raise DatabaseException(f"批量保存指标数据失败: {str(e)}")

    async def get_indicators(
        self,
        stock_code: str,
        indicator_type: str,
        start_date: Optional[Union[datetime, str]] = None,
        end_date: Optional[Union[datetime, str]] = None,
        data_frequency: str = 'D1'
    ) -> List[StockIndicator]:
        """获取技术指标数据"""
        try:
            if isinstance(start_date, str):
                start_date = datetime.strptime(start_date, "%Y-%m-%d")
            if isinstance(end_date, str):
                end_date = datetime.strptime(end_date, "%Y-%m-%d")

            # 构建查询条件
            conditions = [
                StockIndicator.stock_code == stock_code,
                StockIndicator.indicator_type == indicator_type,
                StockIndicator.data_frequency == data_frequency
            ]
            
            if start_date and end_date:
                conditions.append(
                    between(StockIndicator.trade_date, start_date, end_date)
                )
            elif start_date:
                conditions.append(StockIndicator.trade_date >= start_date)
            elif end_date:
                conditions.append(StockIndicator.trade_date <= end_date)

            result = await self.db.execute(
                select(StockIndicator).where(and_(*conditions))
            )
            return list(result.scalars().all())
        except Exception as e:
            raise DatabaseException(f"获取指标数据失败: {str(e)}")

    async def save_indicator_version(
        self,
        version_data: Dict[str, Any]
    ) -> IndicatorVersion:
        """保存指标版本信息"""
        try:
            # 如果设置为当前版本，需要将其他版本设置为非当前版本
            if version_data.get('is_current', False):
                result = await self.db.execute(
                    select(IndicatorVersion).where(
                        IndicatorVersion.indicator_type == version_data['indicator_type'],
                        IndicatorVersion.is_current == True
                    )
                )
                current_version = result.scalar_one_or_none()
                if inspect.iscoroutine(current_version):
                    current_version = await current_version
                if current_version:
                    # 使用update语句更新当前版本的状态
                    await self.db.execute(
                        update(IndicatorVersion)
                        .where(IndicatorVersion.version_hash == current_version.version_hash)
                        .values(is_current=False)
                    )

            version = IndicatorVersion(**version_data)
            self.db.add(version)
            await self.db.commit()
            return version
        except Exception as e:
            raise DatabaseException(f"保存指标版本信息失败: {str(e)}")

    async def get_current_indicator_version(
        self,
        indicator_type: str
    ) -> Optional[IndicatorVersion]:
        """获取指标的当前版本信息"""
        try:
            result = await self.db.execute(
                select(IndicatorVersion).where(
                    IndicatorVersion.indicator_type == indicator_type,
                    IndicatorVersion.is_current == True
                ))
            version = result.scalar_one_or_none()
            if inspect.iscoroutine(version):
                version = await version
            return version
        except Exception as e:
            raise DatabaseException(f"获取指标版本信息失败: {str(e)}")

    async def get_all_stock_codes(self) -> List[str]:        
        """获取所有股票代码列表"""
        try:
            query = select(StockInfo.code).where(StockInfo.is_active == True)
            result = await self.db.execute(query)
            codes = result.scalars().all()
            return list(codes)
        except Exception as e:
            raise DatabaseException(f"获取所有股票代码失败: {str(e)}")

    async def get_stock_list(
        self,
        skip: int = 0,
        limit: int = 100,
        industry: Optional[str] = None,
        is_active: Optional[bool] = None,
        search: Optional[str] = None,
        exchange: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取股票列表，支持分页和筛选"""
        try:
            # 构建查询条件
            conditions = []
            if industry:
                conditions.append(StockInfo.industry == industry)
            # 默认只返回活跃的股票，除非明确指定
            if is_active is not None:
                conditions.append(StockInfo.is_active == is_active)
            else:
                conditions.append(StockInfo.is_active == True)
            if exchange:
                conditions.append(StockInfo.exchange == exchange)
            if search:
                # 支持按股票代码或名称搜索
                from sqlalchemy import or_
                search_pattern = f"%{search}%"
                conditions.append(
                    or_(
                        StockInfo.code.ilike(search_pattern),
                        StockInfo.name.ilike(search_pattern)
                    )
                )
            
            # 构建查询
            query = select(StockInfo)
            if conditions:
                query = query.where(and_(*conditions))
            
            # 先获取总数
            count_query = select(StockInfo)
            if conditions:
                count_query = count_query.where(and_(*conditions))
            
            from sqlalchemy import func
            count_result = await self.db.execute(select(func.count()).select_from(count_query.subquery()))
            total = count_result.scalar()
            
            # 添加分页和排序
            query = query.order_by(StockInfo.code).offset(skip).limit(limit)
              # 执行查询
            result = await self.db.execute(query)
            stocks = result.scalars().all()
            
            # 将 ORM 对象转换为字典，只包含必要的字段
            stocks_data = []
            for stock in stocks:
                stock_dict = {
                    "code": stock.code,
                    "name": stock.name,
                    "exchange": stock.exchange,
                    "full_code": stock.full_code,
                    "industry": stock.industry,
                    "sector": stock.sector,
                    "listing_date": stock.listing_date.isoformat() if stock.listing_date else None,
                    "total_shares": stock.total_shares,
                    "circulating_shares": stock.circulating_shares,
                    "company_profile": stock.company_profile,
                    "is_active": stock.is_active,
                    "market_cap": stock.market_cap
                }
                stocks_data.append(stock_dict)
            
            return {
                "stocks": stocks_data,
                "total": total,
                "skip": skip,
                "limit": limit
            }
        except Exception as e:
            raise DatabaseException(f"获取股票列表失败: {str(e)}")

    async def update_stock_daily(
        self,
        stock_code: str,
        trade_date: Union[datetime, str],
        update_data: Dict[str, Any]
    ) -> Any:
        """更新股票日线数据"""
        try:
            if isinstance(trade_date, str):
                trade_date = datetime.strptime(trade_date, "%Y-%m-%d")
            
            # 构建查询条件
            conditions = [
                StockDaily.stock_code == stock_code,
                StockDaily.trade_date == trade_date
            ]
            
            # 查找记录
            query = select(StockDaily).where(and_(*conditions))
            result = await self.db.execute(query)
            daily_data = result.scalar_one_or_none()
            
            if not daily_data:
                raise DatabaseException(f"未找到股票 {stock_code} 在 {trade_date.date()} 的日线数据")
            
            # 更新记录
            for key, value in update_data.items():
                setattr(daily_data, key, value)
            
            await self.db.commit()
            return daily_data
        except Exception as e:
            await self.db.rollback()
            raise DatabaseException(f"更新股票日线数据失败: {str(e)}")

    async def search_stocks(
        self,
        query: str,
        limit: int = 20,
        exchange: Optional[str] = None,
        industry: Optional[str] = None,
        sort_by: str = "relevance"
    ) -> List[Dict[str, Any]]:
        """
        搜索股票，支持多种搜索方式和排序
        
        Args:
            query: 搜索关键词
            limit: 返回结果数量限制
            exchange: 交易所筛选 (SH/SZ)
            industry: 行业筛选
            sort_by: 排序方式 (relevance/code/name/market_cap)
        
        Returns:
            List[Dict[str, Any]]: 搜索结果列表
        """
        try:
            from sqlalchemy import or_, func, case, desc, asc
            
            search_query = f"%{query.lower()}%"
            
            # 构建基础查询条件
            conditions = [
                StockInfo.is_active == True,  # 只查询活跃的股票
                or_(
                    func.lower(StockInfo.code).like(search_query),
                    func.lower(StockInfo.name).like(search_query),
                    func.lower(StockInfo.industry).like(search_query) if StockInfo.industry.isnot(None) else False
                )
            ]
            
            # 添加筛选条件
            if exchange:
                conditions.append(StockInfo.exchange == exchange.upper())
            if industry:
                conditions.append(StockInfo.industry == industry)
            
            # 构建排序条件
            order_by = []
            if sort_by == "relevance":
                # 按相关性排序：代码完全匹配 > 代码前缀匹配 > 名称匹配 > 行业匹配
                relevance_score = case(
                    (func.lower(StockInfo.code) == query.lower(), 4),
                    (func.lower(StockInfo.code).like(f"{query.lower()}%"), 3),
                    (func.lower(StockInfo.name).like(search_query), 2),
                    (func.lower(StockInfo.industry).like(search_query), 1),                    else_=0
                )
                order_by.append(desc(relevance_score))
                order_by.append(asc(StockInfo.code))
            elif sort_by == "code":
                order_by.append(asc(StockInfo.code))
            elif sort_by == "name":
                order_by.append(asc(StockInfo.name))
            elif sort_by == "market_cap":
                # SQLite不支持NULLS LAST，使用CASE语句处理
                order_by.append(
                    case(
                        (StockInfo.market_cap.is_(None), 1),  # NULL值排在后面
                        else_=0
                    )
                )
                order_by.append(desc(StockInfo.market_cap))
            
            # 构建并执行查询
            query_stmt = (
                select(StockInfo)
                .where(and_(*conditions))
                .order_by(*order_by)
                .limit(limit)
            )
            
            result = await self.db.execute(query_stmt)
            stocks = result.scalars().all()
            
            # 转换为搜索结果格式
            search_results = []
            for stock in stocks:
                # 确定匹配类型
                match_type = "industry"
                if query.lower() in stock.code.lower():
                    match_type = "code"
                elif query.lower() in stock.name.lower():
                    match_type = "name"
                
                result_dict = {
                    "code": stock.code,
                    "name": stock.name,
                    "exchange": stock.exchange,
                    "industry": stock.industry,
                    "sector": stock.sector,
                    "full_code": stock.full_code,
                    "market_cap": stock.market_cap,
                    "listing_date": stock.listing_date.isoformat() if stock.listing_date else None,
                    "match_type": match_type
                }
                search_results.append(result_dict)
            
            return search_results
            
        except Exception as e:
            self.logger.error(f"股票搜索失败: {e}", exc_info=True)
            raise DatabaseException(f"股票搜索失败: {str(e)}")
    
    async def quick_search_stocks(
        self,
        query: str,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        快速搜索股票，优化的搜索接口用于实时搜索建议
        
        Args:
            query: 搜索关键词
            limit: 返回结果数量限制
        
        Returns:
            List[Dict[str, Any]]: 搜索结果列表
        """
        try:
            from sqlalchemy import or_, func, case, desc, asc, select, and_
            
            search_query = f"%{query.lower()}%"
            
            # 简化逻辑，直接使用原始代码进行搜索
            # 优先匹配代码，然后名称
            conditions = [
                or_(
                    func.lower(StockInfo.code).like(search_query),
                    func.lower(StockInfo.name).like(search_query)
                )
            ]
            
            # 按相关性排序（代码匹配优先级更高）
            relevance_score = case(
                (func.lower(StockInfo.code) == query.lower(), 10),
                (func.lower(StockInfo.code).like(f"{query.lower()}%"), 8),
                (func.lower(StockInfo.code).like(search_query), 6),
                (func.lower(StockInfo.name).like(f"{query.lower()}%"), 4),
                (func.lower(StockInfo.name).like(search_query), 2),
                else_=1
            ).label("relevance_score")
            
            # 构建并执行查询
            query_stmt = (
                select(StockInfo)
                .where(and_(*conditions))
                .order_by(desc(relevance_score), asc(StockInfo.code))
                .limit(limit)
            )
            
            result = await self.db.execute(query_stmt)
            stocks = result.scalars().all()
            
            # 转换为简化的搜索结果格式
            search_results = []
            for stock in stocks:
                # 从代码中提取基础代码（移除后缀）
                base_code = stock.code.split('.')[0] if '.' in stock.code else stock.code
                
                # 确定匹配类型
                match_type = "name"  # 默认为名称匹配
                if query.lower() in stock.code.lower():
                    match_type = "code"
                elif query.lower() in stock.name.lower():
                    match_type = "name"
                
                result_dict = {
                    "code": base_code,
                    "name": stock.name,
                    "exchange": stock.exchange,
                    "industry": stock.industry,
                    "full_code": stock.full_code,
                    "listing_date": stock.listing_date.isoformat() if stock.listing_date else None,
                    "match_type": match_type
                }
                search_results.append(result_dict)
            
            return search_results
            
        except Exception as e:
            self.logger.error(f"快速搜索股票失败: {e}", exc_info=True)
            # 在生产环境中，可能需要更通用的错误消息
            raise DatabaseException(f"快速搜索服务异常: {str(e)}")

    async def get_stock_basic_info(self, code: str) -> Optional[Dict[str, Any]]:
        """获取股票基本信息"""
        # 先查询缓存（仅在启用缓存时）
        cache_key = f"stock_info:{code}"
        if settings.CACHE_ENABLED and cache_key in self._cache:
            return self._cache[cache_key]

        try:
            result = await self.db.execute(
                select(StockInfo).where(
                    and_(StockInfo.code == code, StockInfo.is_active == True)
                )
            )
            info = result.scalar_one_or_none()
            
            if not info:
                # 此处不应抛出异常，而是返回None，由API层处理404
                return None
            
            if inspect.iscoroutine(info):
                info = await info
            
            # 缓存结果（仅在启用缓存时）
            if info and settings.CACHE_ENABLED:
                self._cache[cache_key] = info
            return info
        except Exception as e:
            raise DatabaseException(f"获取股票信息失败: {str(e)}")

    # @smart_data_cache(
    #     data_type=DataType.STOCK_INFO,
    #     cache_strategy=CacheStrategy.CACHE_FIRST,
    #     cache_ttl=1800  # 30分钟缓存
    # )
    async def get_popular_stocks(self, limit: int = 10, exchange: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取热门股票列表
        
        Args:
            limit: 返回数量限制
            exchange: 交易所筛选（可选）
            
        Returns:
            List[Dict[str, Any]]: 热门股票列表
        """
        try:
            # 构建查询条件
            conditions = [StockInfo.is_active == True]  # 只查询活跃的股票
            
            # 如果指定了交易所，添加过滤条件
            if exchange:
                conditions.append(StockInfo.exchange == exchange)
            
            # 构建查询语句，按市值排序（假设有market_cap字段）
            # 如果没有市值字段，可以按照其他字段排序，比如按代码排序
            query_stmt = select(StockInfo)
            
            if conditions:
                query_stmt = query_stmt.where(and_(*conditions))
            
            # 按股票代码排序（作为默认排序方式）
            # 如果未来有更好的排序字段（如市值、成交量等），可以替换
            query_stmt = query_stmt.order_by(StockInfo.code).limit(limit)
            
            result = await self.db.execute(query_stmt)
            stocks = result.scalars().all()
            
            # 转换为返回格式
            popular_stocks = []
            for stock in stocks:
                # 从代码中提取基础代码（移除后缀）
                base_code = stock.code.split('.')[0] if '.' in stock.code else stock.code
                
                stock_dict = {
                    "code": base_code,
                    "name": stock.name,
                    "exchange": stock.exchange,
                    "industry": stock.industry or "未分类",
                    "full_code": stock.full_code,
                    "listing_date": stock.listing_date.isoformat() if stock.listing_date else None,
                    "match_type": "popular"  # 标识为热门股票
                }
                popular_stocks.append(stock_dict)
            
            self.logger.info(f"获取热门股票成功，返回{len(popular_stocks)}条记录")
            return popular_stocks
            
        except Exception as e:
            self.logger.error(f"获取热门股票失败: {e}", exc_info=True)
            raise DatabaseException(f"获取热门股票失败: {str(e)}")

    async def clear_all_stock_info(self) -> int:
        """
        清空所有股票基本信息
        
        Returns:
            删除的记录数量
        """
        try:
            # 获取要软删除的记录数量（当前活跃的记录）
            count_stmt = select(StockInfo).where(StockInfo.is_active == True)
            count_result = await self.db.execute(count_stmt)
            soft_delete_count = len(count_result.scalars().all())
            
            # 软删除：将所有记录设为非活跃状态
            update_stmt = update(StockInfo).values(is_active=False)
            await self.db.execute(update_stmt)
            await self.db.commit()
            
            self.logger.info(f"已软删除股票信息表，设置{soft_delete_count}条记录为非活跃状态")
            return soft_delete_count
            
        except Exception as e:
            await self.db.rollback()
            self.logger.error(f"清空股票信息失败: {e}", exc_info=True)
            raise DatabaseException(f"清空股票信息失败: {str(e)}")

    async def replace_all_stock_info(self, stock_info_list: List[Dict[str, Any]]) -> Dict[str, int]:
        """
        替换所有股票信息（清空后重新插入）
        
        Args:
            stock_info_list: 新的股票信息列表
            
        Returns:
            操作统计信息 {'deleted': 删除数量, 'inserted': 插入数量}
        """
        try:
            # 1. 先将所有现有股票设为非活跃状态
            update_stmt = update(StockInfo).values(is_active=False)
            await self.db.execute(update_stmt)
            
            # 2. 直接处理每个股票：标准化数据并插入/更新
            inserted_count = 0
            updated_count = 0
            processed_codes = set()  # 跟踪已处理的股票代码
            
            for info in stock_info_list:
                try:
                    info_copy = info.copy()
                    if 'code' in info_copy and info_copy['code']:
                        # 如果股票代码包含点号，只保留点号前面的部分
                        info_copy['code'] = info_copy['code'].split('.')[0]
                    
                    # 根据股票代码确定交易所
                    code = info_copy.get('code', '').strip()
                    if not code:
                        continue
                        
                    if code.startswith('6'):
                        exchange = 'SH'
                    elif code.startswith(('000', '001', '002', '003')):
                        exchange = 'SZ'  
                    elif code.startswith('30'):
                        exchange = 'SZ'  # 创业板
                    elif code.startswith('8'):
                        exchange = 'BJ'  # 北交所
                    else:
                        exchange = 'SZ'  # 默认深交所
                    
                    # 构建完整股票代码
                    full_code = f"{exchange}{code}"
                    
                    # 处理上市日期
                    list_date = info_copy.get('list_date', '')
                    if list_date and len(str(list_date)) == 8:
                        try:
                            list_date_str = str(list_date)
                            formatted_date = f"{list_date_str[:4]}-{list_date_str[4:6]}-{list_date_str[6:8]}"
                            listing_date = datetime.strptime(formatted_date, '%Y-%m-%d').date()
                        except (ValueError, TypeError):
                            listing_date = None
                    else:
                        listing_date = None
                    
                    stock_data = {
                        'code': code,
                        'name': info_copy.get('name', '').strip(),
                        'exchange': exchange,
                        'full_code': full_code,
                        'industry': info_copy.get('industry', '').strip() or None,
                        'sector': info_copy.get('market', '').strip() or None,
                        'listing_date': listing_date,
                        'is_active': True,
                        'total_shares': None,
                        'circulating_shares': None,
                        'market_cap': None,
                        'company_profile': None
                    }
                    
                    # 检查股票是否已存在
                    existing_stmt = select(StockInfo).where(StockInfo.code == code)
                    result = await self.db.execute(existing_stmt)
                    existing_stock = result.scalar_one_or_none()
                    
                    if existing_stock:
                        # 更新现有股票信息并重新激活
                        update_stmt = update(StockInfo).where(StockInfo.code == code).values(**stock_data)
                        await self.db.execute(update_stmt)
                        updated_count += 1
                    else:
                        # 插入新股票
                        new_stock = StockInfo(**stock_data)
                        self.db.add(new_stock)
                        inserted_count += 1
                    
                    processed_codes.add(code)
                    
                except Exception as e:
                    self.logger.warning(f"跳过无效股票数据 {info}: {str(e)}")
                    continue
            
            await self.db.commit()
            
            # 计算被软删除的股票数量（未在新列表中出现的股票）
            total_processed = len(processed_codes)
            soft_deleted_count = await self._count_inactive_stocks()
            
            self.logger.info(f"成功替换股票信息：处理{total_processed}条，更新{updated_count}条，插入{inserted_count}条，软删除{soft_deleted_count}条")
            
            return {
                'processed': total_processed,
                'updated': updated_count, 
                'inserted': inserted_count,
                'deleted': soft_deleted_count  # 修改键名以匹配API期望
            }
            
        except Exception as e:
            await self.db.rollback()
            self.logger.error(f"替换股票信息失败: {e}", exc_info=True)
            raise DatabaseException(f"替换股票信息失败: {str(e)}")

    async def _count_inactive_stocks(self) -> int:
        """计算非活跃股票数量"""
        try:
            from sqlalchemy import func
            result = await self.db.execute(
                select(func.count(StockInfo.id)).where(StockInfo.is_active == False)
            )
            return result.scalar() or 0
        except Exception:
            return 0

    # ==== 周线数据相关方法 ====
    
    async def get_weekly_data(
        self, 
        stock_code: str, 
        start_date=None, 
        end_date=None
    ) -> List[Dict[str, Any]]:
        """获取股票周线数据"""
        try:
            stmt = select(StockWeekly).where(StockWeekly.stock_code == stock_code)
            
            if start_date:
                stmt = stmt.where(StockWeekly.trade_date >= start_date)
            if end_date:
                stmt = stmt.where(StockWeekly.trade_date <= end_date)
                
            stmt = stmt.order_by(StockWeekly.trade_date)
            
            result = await self.db.execute(stmt)
            weekly_data = result.scalars().all()
            
            # 转换为字典格式
            return [
                {
                    'stock_code': item.stock_code,
                    'trade_date': item.trade_date,
                    'open': item.open,
                    'high': item.high,
                    'low': item.low,
                    'close': item.close,
                    'volume': item.volume,
                    'amount': item.amount,
                    'change_pct': item.change_pct,
                    'turnover_rate': item.turnover_rate,
                    'limit_status': item.limit_status,
                    'is_st': item.is_st
                }
                for item in weekly_data
            ]
            
        except Exception as e:
            self.logger.error(f"获取周线数据失败: {e}")
            raise DatabaseException(f"获取周线数据失败: {str(e)}")
    
    async def batch_save_weekly_data(
        self, 
        weekly_data_list: List[Dict[str, Any]], 
        use_queue: bool = True
    ) -> Dict[str, int]:
        """批量保存周线数据（支持UPSERT）
        
        Args:
            weekly_data_list: 周线数据列表
            use_queue: 是否使用Redis队列（默认True）
            
        Returns:
            保存统计信息
        """
        if not weekly_data_list:
            return {'inserted': 0, 'updated': 0, 'skipped': 0}
        
        # 如果启用队列且队列可用，则使用队列处理
        if use_queue and QUEUE_AVAILABLE and settings.QUEUE_ENABLED:
            # 从数据中提取股票代码（假设所有数据是同一股票）
            stock_code = weekly_data_list[0].get('stock_code', '')
            return await self._enqueue_batch_save(
                TaskType.BATCH_SAVE_WEEKLY,
                stock_code,
                weekly_data_list
            )
        
        # 否则使用直接数据库操作
        return await self._batch_save_weekly_data_direct(weekly_data_list)

    async def _batch_save_weekly_data_direct(self, weekly_data_list: List[Dict[str, Any]]) -> Dict[str, int]:
        """批量保存周线数据（支持UPSERT）"""
        try:
            if not weekly_data_list:
                return {'inserted': 0, 'updated': 0, 'skipped': 0}
            
            # 统计信息
            stats = {'inserted': 0, 'updated': 0, 'skipped': 0}
            
            # 按股票代码和交易日期去重
            unique_data = {}
            for data in weekly_data_list:
                stock_code = data.get('stock_code')
                trade_date = data.get('trade_date')
                
                if not stock_code or not trade_date:
                    stats['skipped'] += 1
                    continue
                
                # 使用抽取的私有方法转换trade_date
                self._convert_trade_date_to_date(data, stock_code)
                
                key = f"{stock_code}_{data['trade_date']}"
                unique_data[key] = data.copy()
            
            # 处理每条记录
            for key, data in unique_data.items():
                stock_code = data['stock_code']
                trade_date = data['trade_date']
                
                try:
                    # 查询是否已存在记录
                    existing_stmt = select(StockWeekly).where(
                        and_(
                            StockWeekly.stock_code == stock_code,
                            StockWeekly.trade_date == trade_date
                        )
                    )
                    result = await self.db.execute(existing_stmt)
                    existing_record = result.scalar_one_or_none()
                    
                    if existing_record:
                        # 更新现有记录
                        for key, value in data.items():
                            if key not in ['stock_code', 'trade_date']:
                                setattr(existing_record, key, value)
                        stats['updated'] += 1
                    else:
                        # 创建新记录
                        weekly = StockWeekly(**data)
                        self.db.add(weekly)
                        stats['inserted'] += 1
                        
                except Exception as e:
                    self.logger.warning(f"保存单条周线数据失败 {stock_code} {trade_date}: {str(e)}")
                    stats['skipped'] += 1
                    continue
            
            await self.db.commit()
            
            total_processed = stats['inserted'] + stats['updated'] + stats['skipped']
            self.logger.debug(f"批量保存周线数据完成: "
                            f"总计{total_processed}条, "
                            f"新增{stats['inserted']}条, "
                            f"更新{stats['updated']}条, "
                            f"跳过{stats['skipped']}条")
            
            return stats
            
        except Exception as e:
            await self.db.rollback()
            self.logger.error(f"批量保存周线数据失败: {e}")
            raise DatabaseException(f"批量保存周线数据失败: {str(e)}")
    
    # ==== 月线数据相关方法 ====
    
    async def get_monthly_data(
        self, 
        stock_code: str, 
        start_date=None, 
        end_date=None
    ) -> List[Dict[str, Any]]:
        """获取股票月线数据"""
        try:
            stmt = select(StockMonthly).where(StockMonthly.stock_code == stock_code)
            
            if start_date:
                stmt = stmt.where(StockMonthly.trade_date >= start_date)
            if end_date:
                stmt = stmt.where(StockMonthly.trade_date <= end_date)
                
            stmt = stmt.order_by(StockMonthly.trade_date)
            
            result = await self.db.execute(stmt)
            monthly_data = result.scalars().all()
            
            # 转换为字典格式
            return [
                {
                    'stock_code': item.stock_code,
                    'trade_date': item.trade_date,
                    'open': item.open,
                    'high': item.high,
                    'low': item.low,
                    'close': item.close,
                    'volume': item.volume,
                    'amount': item.amount,
                    'change_pct': item.change_pct,
                    'turnover_rate': item.turnover_rate,
                    'limit_status': item.limit_status,
                    'is_st': item.is_st
                }
                for item in monthly_data
            ]
            
        except Exception as e:
            self.logger.error(f"获取月线数据失败: {e}")
            raise DatabaseException(f"获取月线数据失败: {str(e)}")
    
    async def batch_save_monthly_data(
        self, 
        monthly_data_list: List[Dict[str, Any]], 
        use_queue: bool = True
    ) -> Dict[str, int]:
        """批量保存月线数据（支持UPSERT）
        
        Args:
            monthly_data_list: 月线数据列表
            use_queue: 是否使用Redis队列（默认True）
            
        Returns:
            保存统计信息
        """
        if not monthly_data_list:
            return {'inserted': 0, 'updated': 0, 'skipped': 0}
        
        # 如果启用队列且队列可用，则使用队列处理
        if use_queue and QUEUE_AVAILABLE and settings.QUEUE_ENABLED:
            # 从数据中提取股票代码（假设所有数据是同一股票）
            stock_code = monthly_data_list[0].get('stock_code', '')
            return await self._enqueue_batch_save(
                TaskType.BATCH_SAVE_MONTHLY,
                stock_code,
                monthly_data_list
            )
        
        # 否则使用直接数据库操作
        return await self._batch_save_monthly_data_direct(monthly_data_list)

    async def _batch_save_monthly_data_direct(self, monthly_data_list: List[Dict[str, Any]]) -> Dict[str, int]:
        """批量保存月线数据（支持UPSERT）"""
        try:
            if not monthly_data_list:
                return {'inserted': 0, 'updated': 0, 'skipped': 0}
            
            # 统计信息
            stats = {'inserted': 0, 'updated': 0, 'skipped': 0}
            
            # 按股票代码和交易日期去重
            unique_data = {}
            for data in monthly_data_list:
                stock_code = data.get('stock_code')
                trade_date = data.get('trade_date')
                
                if not stock_code or not trade_date:
                    stats['skipped'] += 1
                    continue
                
                # 使用抽取的私有方法转换trade_date
                self._convert_trade_date_to_date(data, stock_code)
                
                key = f"{stock_code}_{data['trade_date']}"
                unique_data[key] = data.copy()
            
            # 处理每条记录
            for key, data in unique_data.items():
                stock_code = data['stock_code']
                trade_date = data['trade_date']
                
                try:
                    # 查询是否已存在记录
                    existing_stmt = select(StockMonthly).where(
                        and_(
                            StockMonthly.stock_code == stock_code,
                            StockMonthly.trade_date == trade_date
                        )
                    )
                    result = await self.db.execute(existing_stmt)
                    existing_record = result.scalar_one_or_none()
                    
                    if existing_record:
                        # 更新现有记录
                        for key, value in data.items():
                            if key not in ['stock_code', 'trade_date']:
                                setattr(existing_record, key, value)
                        stats['updated'] += 1
                    else:
                        # 创建新记录
                        monthly = StockMonthly(**data)
                        self.db.add(monthly)
                        stats['inserted'] += 1
                        
                except Exception as e:
                    self.logger.warning(f"保存单条月线数据失败 {stock_code} {trade_date}: {str(e)}")
                    stats['skipped'] += 1
                    continue
            
            await self.db.commit()
            
            total_processed = stats['inserted'] + stats['updated'] + stats['skipped']
            self.logger.debug(f"批量保存月线数据完成: "
                            f"总计{total_processed}条, "
                            f"新增{stats['inserted']}条, "
                            f"更新{stats['updated']}条, "
                            f"跳过{stats['skipped']}条")
            
            return stats
            
        except Exception as e:
            await self.db.rollback()
            self.logger.error(f"批量保存月线数据失败: {e}")
            raise DatabaseException(f"批量保存月线数据失败: {str(e)}")
    
    # ==== 通用批量保存方法 ====
    
    async def save_period_data(
        self, 
        stock_code: str, 
        period: str, 
        data_list: List[Dict[str, Any]]
    ) -> Dict[str, int]:
        """
        根据周期类型保存股票数据到对应的数据库表
        
        Args:
            stock_code: 股票代码
            period: 数据周期 (d/w/m)
            data_list: 数据列表
            
        Returns:
            保存统计信息 {'inserted': 新增数量, 'updated': 更新数量, 'skipped': 跳过数量}
        """
        if not data_list:
            return {'inserted': 0, 'updated': 0, 'skipped': 0}
            
        try:
            if period == "d":
                # 日线数据
                stats = await self.batch_save_stock_daily(stock_code, data_list)
                return stats
            elif period == "w":
                # 周线数据
                # 为每条记录添加stock_code字段
                weekly_data = []
                for data in data_list:
                    data_with_code = data.copy()
                    data_with_code['stock_code'] = stock_code
                    weekly_data.append(data_with_code)
                stats = await self.batch_save_weekly_data(weekly_data)
                return stats
            elif period == "m":
                # 月线数据
                # 为每条记录添加stock_code字段
                monthly_data = []
                for data in data_list:
                    data_with_code = data.copy()
                    data_with_code['stock_code'] = stock_code
                    monthly_data.append(data_with_code)
                stats = await self.batch_save_monthly_data(monthly_data)
                return stats
            else:
                self.logger.warning(f"不支持的周期类型: {period}")
                return {'inserted': 0, 'updated': 0, 'skipped': len(data_list)}
                
        except Exception as e:
            self.logger.error(f"保存周期数据失败 - 股票: {stock_code}, 周期: {period}, 错误: {str(e)}")
            raise DatabaseException(f"保存周期数据失败: {str(e)}")
    
    async def batch_save_daily_data(self, daily_data_list: List[Dict[str, Any]]) -> Dict[str, int]:
        """通用的批量保存日线数据方法（用于处理器调用，支持UPSERT）"""
        try:
            if not daily_data_list:
                return {'inserted': 0, 'updated': 0, 'skipped': 0}
            
            # 统计信息
            stats = {'inserted': 0, 'updated': 0, 'skipped': 0}
            
            # 按股票代码分组去重
            stock_data_map = {}
            for data in daily_data_list:
                stock_code = data.get('stock_code')
                trade_date = data.get('trade_date')
                
                if not stock_code or not trade_date:
                    stats['skipped'] += 1
                    continue
                
                # 使用抽取的私有方法转换trade_date
                self._convert_trade_date_to_date(data, stock_code)
                
                if stock_code not in stock_data_map:
                    stock_data_map[stock_code] = {}
                
                stock_data_map[stock_code][data['trade_date']] = data.copy()
            
            # 处理每只股票的数据
            for stock_code, date_data_map in stock_data_map.items():
                for trade_date, data in date_data_map.items():
                    try:
                        # 查询是否已存在记录
                        existing_stmt = select(StockDaily).where(
                            and_(
                                StockDaily.stock_code == stock_code,
                                StockDaily.trade_date == trade_date
                            )
                        )
                        result = await self.db.execute(existing_stmt)
                        existing_record = result.scalar_one_or_none()
                        
                        if existing_record:
                            # 更新现有记录
                            for key, value in data.items():
                                if key not in ['stock_code', 'trade_date']:
                                    setattr(existing_record, key, value)
                            stats['updated'] += 1
                        else:
                            # 创建新记录
                            new_record = StockDaily(**data)
                            self.db.add(new_record)
                            stats['inserted'] += 1
                            
                    except Exception as e:
                        self.logger.warning(f"保存单条日线数据失败 {stock_code} {trade_date}: {str(e)}")
                        stats['skipped'] += 1
                        continue
            
            await self.db.commit()
            
            total_processed = stats['inserted'] + stats['updated'] + stats['skipped']
            self.logger.debug(f"通用批量保存日线数据完成: "
                            f"总计{total_processed}条, "
                            f"新增{stats['inserted']}条, "
                            f"更新{stats['updated']}条, "
                            f"跳过{stats['skipped']}条")
            
            return stats
            
        except Exception as e:
            await self.db.rollback()
            self.logger.error(f"通用批量保存日线数据失败: {e}")
            raise DatabaseException(f"批量保存日线数据失败: {str(e)}")
    
    async def get_daily_data(
        self, 
        stock_code: str, 
        start_date=None, 
        end_date=None
    ) -> List[Dict[str, Any]]:
        """获取股票日线数据（用于处理器调用）"""
        try:
            stmt = select(StockDaily).where(StockDaily.stock_code == stock_code)
            
            if start_date:
                stmt = stmt.where(StockDaily.trade_date >= start_date)
            if end_date:
                stmt = stmt.where(StockDaily.trade_date <= end_date)
                
            stmt = stmt.order_by(StockDaily.trade_date)
            
            result = await self.db.execute(stmt)
            daily_data = result.scalars().all()
            
            # 转换为字典格式
            return [
                {
                    'stock_code': item.stock_code,
                    'trade_date': item.trade_date,
                    'open': item.open,
                    'high': item.high,
                    'low': item.low,
                    'close': item.close,
                    'volume': item.volume,
                    'amount': item.amount,
                    'change_pct': item.change_pct,
                    'turnover_rate': item.turnover_rate,
                    'limit_status': item.limit_status,
                    'is_st': item.is_st
                }
                for item in daily_data
            ]
            
        except Exception as e:
            self.logger.error(f"获取日线数据失败: {e}")
            raise DatabaseException(f"获取日线数据失败: {str(e)}")
