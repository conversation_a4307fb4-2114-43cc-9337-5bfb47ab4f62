"""
股票数据存储服务实现
"""
from app.core.logging import logging
from app.core.config import settings
from typing import List, Optional, Dict, Any, Union, Type, cast, Sequence
from datetime import datetime
import inspect

from sqlalchemy import select, delete, update, and_, between
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.stock import (
    StockInfo, StockDailyBase, IndicatorVersion,
    StockIndicator
)
from app.services.storage.base import BaseStorageService
from app.core.exceptions import DatabaseException

class StockStorageService(BaseStorageService):
    """基于SQLAlchemy的股票数据存储服务实现"""
    
    logger = logging.getLogger(__name__ + ".StockStorageService")

    def __init__(self, db: AsyncSession):
        super().__init__(db)
        self._cache: Dict[str, Any] = {}  # 简单的内存缓存

    async def save_stock_info(self, stock_info: Dict[str, Any]) -> StockInfo:
        """保存股票基本信息"""
        try:
            info = StockInfo(**stock_info)
            self.db.add(info)
            await self.db.commit()
            return info
        except Exception as e:
            raise DatabaseException(f"保存股票信息失败: {str(e)}")

    async def save_stock_daily(
        self,
        stock_code: str,
        daily_data: Dict[str, Any],
        partition_date: datetime
    ) -> StockDailyBase:
        """保存股票日线数据到指定分区"""
        try:
            # 获取对应日期的分区表类
            partition_cls = cast(Type[StockDailyBase], StockDailyBase.get_partition(partition_date))
            
            # 创建分区表（如果不存在）
            await self.db.run_sync(lambda session: partition_cls.create_table(session.bind))
            
            # 创建并保存数据
            data = daily_data.copy()
            data['stock_code'] = stock_code
            daily = partition_cls(**data)
            self.db.add(daily)
            await self.db.commit()
            return daily
        except Exception as e:
            raise DatabaseException(f"保存日线数据失败: {str(e)}")

    async def batch_save_stock_info(
        self,
        stock_info_list: List[Dict[str, Any]]
    ) -> List[StockInfo]:
        """批量保存股票基本信息,以新的list为标准，没有的新增，已有的修改，存在数据库但是新的list没有的使用假删除"""
        try:
            result = []
            
            # 获取新列表中的所有full_code
            new_full_codes = set()
            for info in stock_info_list:
                info_copy = info.copy()
                if 'code' in info_copy and info_copy['code']:
                    # 如果股票代码包含点号，只保留点号前面的部分
                    info_copy['code'] = info_copy['code'].split('.')[0]
                new_full_codes.add(info_copy['full_code'])
            
            # 处理新增和修改
            for info in stock_info_list:
                # 处理股票代码，提取基础代码部分（去掉后缀）
                info_copy = info.copy()
                if 'code' in info_copy and info_copy['code']:
                    # 如果股票代码包含点号，只保留点号前面的部分
                    info_copy['code'] = info_copy['code'].split('.')[0]
                
                # 查找是否已存在该股票信息
                stmt = select(StockInfo).where(StockInfo.full_code == info_copy['full_code'])
                existing = (await self.db.execute(stmt)).scalar_one_or_none()
                
                if existing:
                    # 更新已存在的记录
                    for key, value in info_copy.items():
                        setattr(existing, key, value)
                    # 确保激活状态
                    existing.is_active = True
                    result.append(existing)
                else:
                    # 创建新记录
                    info_copy['is_active'] = True  # 新增的记录默认激活
                    new_stock = StockInfo(**info_copy)
                    self.db.add(new_stock)
                    result.append(new_stock)
            
            # 处理假删除：将数据库中存在但新列表中不存在的记录标记为非激活
            all_existing_stmt = select(StockInfo).where(StockInfo.is_active == True)
            all_existing = (await self.db.execute(all_existing_stmt)).scalars().all()
            
            deactivated_count = 0
            for existing_stock in all_existing:
                if existing_stock.full_code not in new_full_codes:
                    existing_stock.is_active = False
                    deactivated_count += 1
            
            await self.db.commit()
            self.logger.info(f"成功保存{len(result)}条股票信息，假删除{deactivated_count}条股票信息")
            return result
        except Exception as e:
            await self.db.rollback()
            raise DatabaseException(f"批量保存股票信息失败: {str(e)}")

    async def batch_save_stock_daily(
        self,
        stock_code: str,
        daily_data_list: List[Dict[str, Any]],
        partition_date: datetime
    ) -> List[StockDailyBase]:
        """批量保存股票日线数据到指定分区"""
        try:
            partition_cls = cast(Type[StockDailyBase], StockDailyBase.get_partition(partition_date))
            await self.db.run_sync(lambda session: partition_cls.create_table(session.bind))
            
            daily_objects = []
            for data in daily_data_list:
                data = data.copy()
                data['stock_code'] = stock_code
                daily = partition_cls(**data)
                daily_objects.append(daily)
            
            # 使用merge而不是add_all来处理重复数据
            for daily in daily_objects:
                self.db.merge(daily)
            
            await self.db.commit()
            return daily_objects
        except Exception as e:
            await self.db.rollback()  # 确保rollback
            raise DatabaseException(f"批量保存日线数据失败: {str(e)}")

    async def get_stock_info(self, stock_code: str) -> Optional[StockInfo]:
        """获取股票基本信息"""
        # 先查询缓存（仅在启用缓存时）
        cache_key = f"stock_info:{stock_code}"
        if settings.CACHE_ENABLED and cache_key in self._cache:
            return self._cache[cache_key]

        try:
            result = await self.db.execute(
                select(StockInfo).where(StockInfo.code == stock_code)
            )
            info = result.scalar_one_or_none()
            
            if not info:
                # 此处不应抛出异常，而是返回None，由API层处理404
                return None
            
            if inspect.iscoroutine(info):
                info = await info
            
            # 缓存结果（仅在启用缓存时）
            if info and settings.CACHE_ENABLED:
                self._cache[cache_key] = info
            return info
        except Exception as e:
            raise DatabaseException(f"获取股票信息失败: {str(e)}")

    async def get_stock_suggestions(
        self,
        query: str,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        根据查询字符串获取股票代码或名称建议
        """
        try:
            # 使用 or_ 进行模糊查询
            from sqlalchemy import or_
            
            search_query = f"%{query}%"
            
            stmt = (
                select(
                    StockInfo.code,
                    StockInfo.name,
                    StockInfo.exchange,
                    StockInfo.full_code,
                    StockInfo.industry,
                    StockInfo.listing_date
                )
                .where(
                    or_(
                        StockInfo.code.ilike(search_query),
                        StockInfo.name.ilike(search_query)
                    )
                )
                .limit(limit)
            )
            
            result = await self.db.execute(stmt)
            # 使用 .mappings().all() 直接获取字典列表
            suggestions = result.mappings().all()
            
            # 将结果转换为字典列表, 并处理日期格式
            return [dict(row) for row in suggestions]

        except Exception as e:
            self.logger.error(f"获取股票建议失败: {e}", exc_info=True)
            raise DatabaseException(f"获取股票建议失败: {str(e)}")

    async def get_stock_daily(
        self,
        stock_code: str,
        start_date: Optional[Union[datetime, str]] = None,
        end_date: Optional[Union[datetime, str]] = None
    ) -> List[StockDailyBase]:
        """获取股票日线数据"""
        try:
            if isinstance(start_date, str):
                start_date = datetime.strptime(start_date, "%Y-%m-%d")
            if isinstance(end_date, str):
                end_date = datetime.strptime(end_date, "%Y-%m-%d")

            # 获取需要查询的分区表
            from app.models.partition import PartitionManager
            raw_partitions = PartitionManager.get_partitions_between("StockDailyBase", start_date, end_date)
            partitions = [cast(Type[StockDailyBase], p) for p in raw_partitions]
            
            results = []
            for partition_cls in partitions:
                # 构建查询条件
                conditions = [partition_cls.stock_code == stock_code]
                if start_date:
                    conditions.append(partition_cls.trade_date >= start_date)
                if end_date:
                    conditions.append(partition_cls.trade_date <= end_date)
                
                result = await self.db.execute(
                    select(partition_cls).where(and_(*conditions))
                )
                results.extend(result.scalars().all())
            
            return results
        except Exception as e:
            raise DatabaseException(f"获取日线数据失败: {str(e)}")

    async def update_stock_info(
        self,
        stock_code: str,
        update_data: Dict[str, Any]
    ) -> StockInfo:
        """更新股票基本信息"""
        try:
            result = await self.db.execute(
                select(StockInfo).where(StockInfo.code == stock_code)
            )
            info = result.scalar_one_or_none()
            
            if not info:
                # 此处不应抛出异常，而是返回None，由API层处理404
                return None
            
            if inspect.iscoroutine(info):
                info = await info
            
            for key, value in update_data.items():
                setattr(info, key, value)
            
            await self.db.commit()
            await self.db.refresh(info) # 刷新以获取最新数据
            
            # 更新缓存（仅在启用缓存时）
            cache_key = f"stock_info:{stock_code}"
            if settings.CACHE_ENABLED:
                self._cache[cache_key] = info
            
            return info
        except Exception as e:
            await self.db.rollback()
            raise DatabaseException(f"更新股票信息失败: {str(e)}")

    async def delete_stock_info(self, stock_code: str) -> bool:
        """删除股票基本信息"""
        try:
            result = await self.db.execute(
                delete(StockInfo).where(StockInfo.code == stock_code)
            )
            
            # 删除缓存
            cache_key = f"stock_info:{stock_code}"
            self._cache.pop(cache_key, None)
            
            return result.rowcount > 0
        except Exception as e:
            raise DatabaseException(f"删除股票信息失败: {str(e)}")

    async def delete_stock_daily(
        self,
        stock_code: str,
        start_date: Optional[Union[datetime, str]] = None,
        end_date: Optional[Union[datetime, str]] = None
    ) -> bool:
        """删除股票日线数据"""
        try:
            if isinstance(start_date, str):
                start_date = datetime.strptime(start_date, "%Y-%m-%d")
            if isinstance(end_date, str):
                end_date = datetime.strptime(end_date, "%Y-%m-%d")

            from app.models.partition import PartitionManager
            raw_partitions = PartitionManager.get_partitions_between("StockDailyBase", start_date, end_date)
            partitions = [cast(Type[StockDailyBase], p) for p in raw_partitions]
            total_deleted = 0
            
            for partition_cls in partitions:
                conditions = [partition_cls.stock_code == stock_code]
                if start_date:
                    conditions.append(partition_cls.trade_date >= start_date)
                if end_date:
                    conditions.append(partition_cls.trade_date <= end_date)
                
                result = await self.db.execute(
                    delete(partition_cls).where(and_(*conditions))
                )
                total_deleted += result.rowcount
            
            return total_deleted > 0
        except Exception as e:
            raise DatabaseException(f"删除日线数据失败: {str(e)}")

    async def save_indicator(
        self,
        stock_code: str,
        indicator_type: str,
        values: Dict[str, Any],
        trade_date: datetime,
        data_frequency: str = 'D1'
    ) -> StockIndicator:
        """保存技术指标数据"""
        try:
            # 获取当前指标版本
            version = await self.get_current_indicator_version(indicator_type)
            if not version:
                raise DatabaseException(f"指标 {indicator_type} 没有有效的版本信息")

            indicator = StockIndicator(
                stock_code=stock_code,
                indicator_type=indicator_type,
                version_hash=version.version_hash,
                values=values,
                trade_date=trade_date,
                data_frequency=data_frequency
            )
            self.db.add(indicator)
            await self.db.commit()
            return indicator
        except Exception as e:
            raise DatabaseException(f"保存指标数据失败: {str(e)}")

    async def batch_save_indicators(
        self,
        stock_code: str,
        indicators_data: List[Dict[str, Any]]
    ) -> List[StockIndicator]:
        """批量保存技术指标数据"""
        try:
            # 获取所有涉及的指标类型的当前版本
            indicator_types = {data['indicator_type'] for data in indicators_data}
            versions = {}
            for indicator_type in indicator_types:
                version = await self.get_current_indicator_version(indicator_type)
                if not version:
                    raise DatabaseException(f"指标 {indicator_type} 没有有效的版本信息")
                versions[indicator_type] = version

            # 创建所有指标对象
            indicators = []
            for data in indicators_data:
                data = data.copy()
                indicator_type = data['indicator_type']
                data['stock_code'] = stock_code
                data['version_hash'] = versions[indicator_type].version_hash
                indicator = StockIndicator(**data)
                indicators.append(indicator)

            # 使用 add_all 批量添加
            self.db.add_all(indicators)
            await self.db.commit()
            return indicators
        except Exception as e:
            raise DatabaseException(f"批量保存指标数据失败: {str(e)}")

    async def get_indicators(
        self,
        stock_code: str,
        indicator_type: str,
        start_date: Optional[Union[datetime, str]] = None,
        end_date: Optional[Union[datetime, str]] = None,
        data_frequency: str = 'D1'
    ) -> List[StockIndicator]:
        """获取技术指标数据"""
        try:
            if isinstance(start_date, str):
                start_date = datetime.strptime(start_date, "%Y-%m-%d")
            if isinstance(end_date, str):
                end_date = datetime.strptime(end_date, "%Y-%m-%d")

            # 构建查询条件
            conditions = [
                StockIndicator.stock_code == stock_code,
                StockIndicator.indicator_type == indicator_type,
                StockIndicator.data_frequency == data_frequency
            ]
            
            if start_date and end_date:
                conditions.append(
                    between(StockIndicator.trade_date, start_date, end_date)
                )
            elif start_date:
                conditions.append(StockIndicator.trade_date >= start_date)
            elif end_date:
                conditions.append(StockIndicator.trade_date <= end_date)

            result = await self.db.execute(
                select(StockIndicator).where(and_(*conditions))
            )
            return list(result.scalars().all())
        except Exception as e:
            raise DatabaseException(f"获取指标数据失败: {str(e)}")

    async def save_indicator_version(
        self,
        version_data: Dict[str, Any]
    ) -> IndicatorVersion:
        """保存指标版本信息"""
        try:
            # 如果设置为当前版本，需要将其他版本设置为非当前版本
            if version_data.get('is_current', False):
                result = await self.db.execute(
                    select(IndicatorVersion).where(
                        IndicatorVersion.indicator_type == version_data['indicator_type'],
                        IndicatorVersion.is_current == True
                    )
                )
                current_version = result.scalar_one_or_none()
                if inspect.iscoroutine(current_version):
                    current_version = await current_version
                if current_version:
                    # 使用update语句更新当前版本的状态
                    await self.db.execute(
                        update(IndicatorVersion)
                        .where(IndicatorVersion.version_hash == current_version.version_hash)
                        .values(is_current=False)
                    )

            version = IndicatorVersion(**version_data)
            self.db.add(version)
            await self.db.commit()
            return version
        except Exception as e:
            raise DatabaseException(f"保存指标版本信息失败: {str(e)}")

    async def get_current_indicator_version(
        self,
        indicator_type: str
    ) -> Optional[IndicatorVersion]:
        """获取指标的当前版本信息"""
        try:
            result = await self.db.execute(
                select(IndicatorVersion).where(
                    IndicatorVersion.indicator_type == indicator_type,
                    IndicatorVersion.is_current == True
                ))
            version = result.scalar_one_or_none()
            if inspect.iscoroutine(version):
                version = await version
            return version
        except Exception as e:
            raise DatabaseException(f"获取指标版本信息失败: {str(e)}")

    async def get_all_stock_codes(self) -> List[str]:        
        """获取所有股票代码列表"""
        try:
            query = select(StockInfo.code)
            result = await self.db.execute(query)
            codes = result.scalars().all()
            return list(codes)
        except Exception as e:
            raise DatabaseException(f"获取所有股票代码失败: {str(e)}")

    async def get_stock_list(
        self,
        skip: int = 0,
        limit: int = 100,
        industry: Optional[str] = None,
        is_active: Optional[bool] = None,
        search: Optional[str] = None,
        exchange: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取股票列表，支持分页和筛选"""
        try:
            # 构建查询条件
            conditions = []
            if industry:
                conditions.append(StockInfo.industry == industry)
            if is_active is not None:
                conditions.append(StockInfo.is_active == is_active)
            if exchange:
                conditions.append(StockInfo.exchange == exchange)
            if search:
                # 支持按股票代码或名称搜索
                from sqlalchemy import or_
                search_pattern = f"%{search}%"
                conditions.append(
                    or_(
                        StockInfo.code.ilike(search_pattern),
                        StockInfo.name.ilike(search_pattern)
                    )
                )
            
            # 构建查询
            query = select(StockInfo)
            if conditions:
                query = query.where(and_(*conditions))
            
            # 先获取总数
            count_query = select(StockInfo)
            if conditions:
                count_query = count_query.where(and_(*conditions))
            
            from sqlalchemy import func
            count_result = await self.db.execute(select(func.count()).select_from(count_query.subquery()))
            total = count_result.scalar()
            
            # 添加分页和排序
            query = query.order_by(StockInfo.code).offset(skip).limit(limit)
              # 执行查询
            result = await self.db.execute(query)
            stocks = result.scalars().all()
            
            # 将 ORM 对象转换为字典，只包含必要的字段
            stocks_data = []
            for stock in stocks:
                stock_dict = {
                    "code": stock.code,
                    "name": stock.name,
                    "exchange": stock.exchange,
                    "full_code": stock.full_code,
                    "industry": stock.industry,
                    "sector": stock.sector,
                    "listing_date": stock.listing_date.isoformat() if stock.listing_date else None,
                    "total_shares": stock.total_shares,
                    "circulating_shares": stock.circulating_shares,
                    "company_profile": stock.company_profile,
                    "is_active": stock.is_active,
                    "market_cap": stock.market_cap
                }
                stocks_data.append(stock_dict)
            
            return {
                "stocks": stocks_data,
                "total": total,
                "skip": skip,
                "limit": limit
            }
        except Exception as e:
            raise DatabaseException(f"获取股票列表失败: {str(e)}")

    async def update_stock_daily(
        self,
        stock_code: str,
        trade_date: Union[datetime, str],
        update_data: Dict[str, Any]
    ) -> Any:
        """更新股票日线数据"""
        try:
            if isinstance(trade_date, str):
                trade_date = datetime.strptime(trade_date, "%Y-%m-%d")
            
            # 获取对应日期的分区表类
            from app.models.partition import PartitionManager
            partition_cls = PartitionManager.get_partition_by_date("StockDailyBase", trade_date)
            
            if not partition_cls:
                raise DatabaseException(f"找不到日期 {trade_date.date()} 对应的分区表")
            
            # 构建查询条件
            conditions = [
                partition_cls.stock_code == stock_code,
                partition_cls.trade_date == trade_date
            ]
            
            # 查找记录
            query = select(partition_cls).where(and_(*conditions))
            result = await self.db.execute(query)
            daily_data = result.scalar_one_or_none()
            
            if not daily_data:
                raise DatabaseException(f"未找到股票 {stock_code} 在 {trade_date.date()} 的日线数据")
            
            # 更新记录
            for key, value in update_data.items():
                setattr(daily_data, key, value)
            
            await self.db.commit()
            return daily_data
        except Exception as e:
            await self.db.rollback()
            raise DatabaseException(f"更新股票日线数据失败: {str(e)}")

    async def search_stocks(
        self,
        query: str,
        limit: int = 20,
        exchange: Optional[str] = None,
        industry: Optional[str] = None,
        sort_by: str = "relevance"
    ) -> List[Dict[str, Any]]:
        """
        搜索股票，支持多种搜索方式和排序
        
        Args:
            query: 搜索关键词
            limit: 返回结果数量限制
            exchange: 交易所筛选 (SH/SZ)
            industry: 行业筛选
            sort_by: 排序方式 (relevance/code/name/market_cap)
        
        Returns:
            List[Dict[str, Any]]: 搜索结果列表
        """
        try:
            from sqlalchemy import or_, func, case, desc, asc
            
            search_query = f"%{query.lower()}%"
            
            # 构建基础查询条件
            conditions = [
                or_(
                    func.lower(StockInfo.code).like(search_query),
                    func.lower(StockInfo.name).like(search_query),
                    func.lower(StockInfo.industry).like(search_query) if StockInfo.industry.isnot(None) else False
                )
            ]
            
            # 添加筛选条件
            if exchange:
                conditions.append(StockInfo.exchange == exchange.upper())
            if industry:
                conditions.append(StockInfo.industry == industry)
            
            # 构建排序条件
            order_by = []
            if sort_by == "relevance":
                # 按相关性排序：代码完全匹配 > 代码前缀匹配 > 名称匹配 > 行业匹配
                relevance_score = case(
                    (func.lower(StockInfo.code) == query.lower(), 4),
                    (func.lower(StockInfo.code).like(f"{query.lower()}%"), 3),
                    (func.lower(StockInfo.name).like(search_query), 2),
                    (func.lower(StockInfo.industry).like(search_query), 1),                    else_=0
                )
                order_by.append(desc(relevance_score))
                order_by.append(asc(StockInfo.code))
            elif sort_by == "code":
                order_by.append(asc(StockInfo.code))
            elif sort_by == "name":
                order_by.append(asc(StockInfo.name))
            elif sort_by == "market_cap":
                # SQLite不支持NULLS LAST，使用CASE语句处理
                order_by.append(
                    case(
                        (StockInfo.market_cap.is_(None), 1),  # NULL值排在后面
                        else_=0
                    )
                )
                order_by.append(desc(StockInfo.market_cap))
            
            # 构建并执行查询
            query_stmt = (
                select(StockInfo)
                .where(and_(*conditions))
                .order_by(*order_by)
                .limit(limit)
            )
            
            result = await self.db.execute(query_stmt)
            stocks = result.scalars().all()
            
            # 转换为搜索结果格式
            search_results = []
            for stock in stocks:
                # 确定匹配类型
                match_type = "industry"
                if query.lower() in stock.code.lower():
                    match_type = "code"
                elif query.lower() in stock.name.lower():
                    match_type = "name"
                
                result_dict = {
                    "code": stock.code,
                    "name": stock.name,
                    "exchange": stock.exchange,
                    "industry": stock.industry,
                    "sector": stock.sector,
                    "full_code": stock.full_code,
                    "market_cap": stock.market_cap,
                    "listing_date": stock.listing_date.isoformat() if stock.listing_date else None,
                    "match_type": match_type
                }
                search_results.append(result_dict)
            
            return search_results
            
        except Exception as e:
            self.logger.error(f"股票搜索失败: {e}", exc_info=True)
            raise DatabaseException(f"股票搜索失败: {str(e)}")
    
    async def quick_search_stocks(
        self,
        query: str,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        快速搜索股票，优化的搜索接口用于实时搜索建议
        
        Args:
            query: 搜索关键词
            limit: 返回结果数量限制
        
        Returns:
            List[Dict[str, Any]]: 搜索结果列表
        """
        try:
            from sqlalchemy import or_, func, case, desc, asc, select, and_
            
            search_query = f"%{query.lower()}%"
            
            # 简化逻辑，直接使用原始代码进行搜索
            # 优先匹配代码，然后名称
            conditions = [
                or_(
                    func.lower(StockInfo.code).like(search_query),
                    func.lower(StockInfo.name).like(search_query)
                )
            ]
            
            # 按相关性排序（代码匹配优先级更高）
            relevance_score = case(
                (func.lower(StockInfo.code) == query.lower(), 10),
                (func.lower(StockInfo.code).like(f"{query.lower()}%"), 8),
                (func.lower(StockInfo.code).like(search_query), 6),
                (func.lower(StockInfo.name).like(f"{query.lower()}%"), 4),
                (func.lower(StockInfo.name).like(search_query), 2),
                else_=1
            ).label("relevance_score")
            
            # 构建并执行查询
            query_stmt = (
                select(StockInfo)
                .where(and_(*conditions))
                .order_by(desc(relevance_score), asc(StockInfo.code))
                .limit(limit)
            )
            
            result = await self.db.execute(query_stmt)
            stocks = result.scalars().all()
            
            # 转换为简化的搜索结果格式
            search_results = []
            for stock in stocks:
                # 从代码中提取基础代码（移除后缀）
                base_code = stock.code.split('.')[0] if '.' in stock.code else stock.code
                
                # 确定匹配类型
                match_type = "name"  # 默认为名称匹配
                if query.lower() in stock.code.lower():
                    match_type = "code"
                elif query.lower() in stock.name.lower():
                    match_type = "name"
                
                result_dict = {
                    "code": base_code,
                    "name": stock.name,
                    "exchange": stock.exchange,
                    "industry": stock.industry,
                    "full_code": stock.full_code,
                    "listing_date": stock.listing_date.isoformat() if stock.listing_date else None,
                    "match_type": match_type
                }
                search_results.append(result_dict)
            
            return search_results
            
        except Exception as e:
            self.logger.error(f"快速搜索股票失败: {e}", exc_info=True)
            # 在生产环境中，可能需要更通用的错误消息
            raise DatabaseException(f"快速搜索服务异常: {str(e)}")

    async def get_stock_basic_info(self, code: str) -> Optional[Dict[str, Any]]:
        """获取股票基本信息"""
        # 先查询缓存（仅在启用缓存时）
        cache_key = f"stock_info:{code}"
        if settings.CACHE_ENABLED and cache_key in self._cache:
            return self._cache[cache_key]

        try:
            result = await self.db.execute(
                select(StockInfo).where(StockInfo.code == code)
            )
            info = result.scalar_one_or_none()
            
            if not info:
                # 此处不应抛出异常，而是返回None，由API层处理404
                return None
            
            if inspect.iscoroutine(info):
                info = await info
            
            # 缓存结果（仅在启用缓存时）
            if info and settings.CACHE_ENABLED:
                self._cache[cache_key] = info
            return info
        except Exception as e:
            raise DatabaseException(f"获取股票信息失败: {str(e)}")

    async def get_popular_stocks(self, limit: int = 10, exchange: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取热门股票列表
        
        Args:
            limit: 返回数量限制
            exchange: 交易所筛选（可选）
            
        Returns:
            List[Dict[str, Any]]: 热门股票列表
        """
        try:
            # 构建查询条件
            conditions = []
            
            # 如果指定了交易所，添加过滤条件
            if exchange:
                conditions.append(StockInfo.exchange == exchange)
            
            # 构建查询语句，按市值排序（假设有market_cap字段）
            # 如果没有市值字段，可以按照其他字段排序，比如按代码排序
            query_stmt = select(StockInfo)
            
            if conditions:
                query_stmt = query_stmt.where(and_(*conditions))
            
            # 按股票代码排序（作为默认排序方式）
            # 如果未来有更好的排序字段（如市值、成交量等），可以替换
            query_stmt = query_stmt.order_by(StockInfo.code).limit(limit)
            
            result = await self.db.execute(query_stmt)
            stocks = result.scalars().all()
            
            # 转换为返回格式
            popular_stocks = []
            for stock in stocks:
                # 从代码中提取基础代码（移除后缀）
                base_code = stock.code.split('.')[0] if '.' in stock.code else stock.code
                
                stock_dict = {
                    "code": base_code,
                    "name": stock.name,
                    "exchange": stock.exchange,
                    "industry": stock.industry or "未分类",
                    "full_code": stock.full_code,
                    "listing_date": stock.listing_date.isoformat() if stock.listing_date else None,
                    "match_type": "popular"  # 标识为热门股票
                }
                popular_stocks.append(stock_dict)
            
            self.logger.info(f"获取热门股票成功，返回{len(popular_stocks)}条记录")
            return popular_stocks
            
        except Exception as e:
            self.logger.error(f"获取热门股票失败: {e}", exc_info=True)
            raise DatabaseException(f"获取热门股票失败: {str(e)}")

    async def clear_all_stock_info(self) -> int:
        """
        清空所有股票基本信息
        
        Returns:
            删除的记录数量
        """
        try:
            # 获取删除前的记录数量
            count_stmt = select(StockInfo)
            count_result = await self.db.execute(count_stmt)
            deleted_count = len(count_result.scalars().all())
            
            # 删除所有记录
            delete_stmt = delete(StockInfo)
            await self.db.execute(delete_stmt)
            await self.db.commit()
            
            self.logger.info(f"已清空股票信息表，删除了{deleted_count}条记录")
            return deleted_count
            
        except Exception as e:
            await self.db.rollback()
            self.logger.error(f"清空股票信息失败: {e}", exc_info=True)
            raise DatabaseException(f"清空股票信息失败: {str(e)}")

    async def replace_all_stock_info(self, stock_info_list: List[Dict[str, Any]]) -> Dict[str, int]:
        """
        替换所有股票信息（清空后重新插入）
        
        Args:
            stock_info_list: 新的股票信息列表
            
        Returns:
            操作统计信息 {'deleted': 删除数量, 'inserted': 插入数量}
        """
        try:
            # 1. 清空现有数据
            deleted_count = await self.clear_all_stock_info()
            
            # 2. 标准化新数据
            normalized_stocks = []
            for info in stock_info_list:
                try:
                    info_copy = info.copy()
                    if 'code' in info_copy and info_copy['code']:
                        # 如果股票代码包含点号，只保留点号前面的部分
                        info_copy['code'] = info_copy['code'].split('.')[0]
                    
                    # 根据股票代码确定交易所
                    code = info_copy.get('code', '').strip()
                    if not code:
                        continue
                        
                    if code.startswith('6'):
                        exchange = 'SH'
                    elif code.startswith(('000', '001', '002', '003')):
                        exchange = 'SZ'  
                    elif code.startswith('30'):
                        exchange = 'SZ'  # 创业板
                    elif code.startswith('8'):
                        exchange = 'BJ'  # 北交所
                    else:
                        exchange = 'SZ'  # 默认深交所
                    
                    # 构建完整股票代码
                    full_code = f"{exchange}{code}"
                    
                    # 处理上市日期
                    list_date = info_copy.get('list_date', '')
                    if list_date and len(str(list_date)) == 8:
                        try:
                            list_date_str = str(list_date)
                            formatted_date = f"{list_date_str[:4]}-{list_date_str[4:6]}-{list_date_str[6:8]}"
                            listing_date = datetime.strptime(formatted_date, '%Y-%m-%d').date()
                        except (ValueError, TypeError):
                            listing_date = None
                    else:
                        listing_date = None
                    
                    normalized_stock = {
                        'code': code,
                        'name': info_copy.get('name', '').strip(),
                        'exchange': exchange,
                        'full_code': full_code,
                        'industry': info_copy.get('industry', '').strip() or None,
                        'sector': info_copy.get('market', '').strip() or None,
                        'listing_date': listing_date,
                        'is_active': True,
                        'total_shares': None,
                        'circulating_shares': None,
                        'market_cap': None,
                        'company_profile': None
                    }
                    
                    normalized_stocks.append(normalized_stock)
                    
                except Exception as e:
                    self.logger.warning(f"跳过无效股票数据 {info}: {str(e)}")
                    continue
            
            # 3. 批量插入新数据
            new_stocks = []
            for stock_data in normalized_stocks:
                new_stock = StockInfo(**stock_data)
                new_stocks.append(new_stock)
            
            self.db.add_all(new_stocks)
            await self.db.commit()
            
            inserted_count = len(new_stocks)
            self.logger.info(f"成功替换股票信息：删除{deleted_count}条，插入{inserted_count}条")
            
            return {
                'deleted': deleted_count,
                'inserted': inserted_count
            }
            
        except Exception as e:
            await self.db.rollback()
            self.logger.error(f"替换股票信息失败: {e}", exc_info=True)
            raise DatabaseException(f"替换股票信息失败: {str(e)}")
