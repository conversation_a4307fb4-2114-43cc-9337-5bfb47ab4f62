"""任务执行器"""

import json
import logging
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
from sqlalchemy import select

from app.core.database import db_session
from app.models.task import TaskExecution
from app.services.scan.manager import get_memory_scanner
from app.services.scan.manager import get_session_manager
from app.schemas.scheduled_task import IndicatorScanConfig, TaskStatus
from app.services.queue.redis_client import get_redis_client

logger = logging.getLogger(__name__)

class TaskExecutor:
    """任务执行器"""
    
    def __init__(self):
        self._active_executions: Dict[int, asyncio.Task] = {}
    
    async def execute_task(self, execution_id: int):
        """执行任务"""
        # 检查是否已经在执行
        if execution_id in self._active_executions:
            logger.warning(f"任务 {execution_id} 已在执行中")
            return
        
        # 创建执行任务
        execution_task = asyncio.create_task(self._run_task_execution(execution_id))
        self._active_executions[execution_id] = execution_task
        
        # 任务完成后清理
        execution_task.add_done_callback(
            lambda _: self._active_executions.pop(execution_id, None)
        )
        
        return execution_task

    async def _run_task_execution(self, execution_id: int):
        """运行任务执行"""
        async with db_session() as db:
            exec_stmt = select(TaskExecution).where(TaskExecution.id == execution_id)
            exec_result = await db.execute(exec_stmt)
            execution = exec_result.scalar_one_or_none()
            if not execution:
                logger.error(f"任务执行记录不存在: {execution_id}")
                return
            
            try:
                # 更新状态为运行中
                execution.status = TaskStatus.RUNNING
                execution.start_time = datetime.now()
                await db.commit()
                
                # 根据任务类型执行
                if execution.task_type == "indicator_scan":
                    await self._execute_indicator_scan(execution)
                else:
                    raise ValueError(f"不支持的任务类型: {execution.task_type}")
                
                # 更新完成状态
                execution.status = TaskStatus.COMPLETED
                execution.end_time = datetime.now()
                execution.duration_seconds = int(
                    (execution.end_time - execution.start_time).total_seconds()
                )
                
                logger.info(f"任务执行完成: {execution_id}, 结果数量: {execution.results_count}")
                
            except Exception as e:
                logger.error(f"任务执行失败 {execution_id}: {e}")
                execution.status = TaskStatus.FAILED
                execution.error_message = str(e)
                execution.end_time = datetime.now()
                if execution.start_time:
                    execution.duration_seconds = int(
                        (execution.end_time - execution.start_time).total_seconds()
                    )
            finally:
                # 安全提交最终状态，避免数据库锁冲突
                try:
                    await db.commit()
                except Exception as e:
                    logger.warning(f"最终状态提交失败: {e}")
                    try:
                        await db.rollback()
                    except Exception:
                        pass  # 忽略回滚异常
    
    async def _execute_indicator_scan(self, execution: TaskExecution):
        """执行指标扫描任务"""
        # 解析任务配置
        config_data = json.loads(execution.task_config)
        config = IndicatorScanConfig(**config_data)
        
        # 获取扫描器实例
        memory_scanner = get_memory_scanner()
        session_manager = get_session_manager(memory_scanner)
        session_id = f"immediate_indicator_scan_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        # 创建会话
        session_manager.create_session(session_id, user_id=str(execution.user_id))


        try:
            # 启动扫描
            task_id = await memory_scanner.start_scan(
                session_id=session_id,
                indicators=config.indicators,
                stock_codes=config.stock_codes,
                parameters=config.parameters,
                scan_mode=config.scan_mode,
                periods=config.periods,
                adjust=config.adjust,
                end_date=config.end_date
            )
            
            # 等待扫描完成
            scan_timeout = 3600  # 1小时超时
            start_time = datetime.now()
            
            while True:
                task = session_manager.get_scan_task(session_id, task_id)
                if not task:
                    raise Exception("扫描任务丢失")
                
                # 使用Redis存储进度，完全避免数据库操作
                progress_data = {
                    'progress_current': task.progress.current,
                    'progress_total': task.progress.total,
                    'progress_percentage': task.progress.percentage,
                    'updated_at': datetime.now().isoformat()
                }
                
                # 根据任务状态设置进度消息
                if task.status.value == "running":
                    progress_data['progress_message'] = "扫描中..."
                elif task.status.value == "completed":
                    progress_data['progress_message'] = "扫描完成"
                elif task.status.value == "cancelled":
                    progress_data['progress_message'] = "已取消"
                elif task.status.value == "failed":
                    progress_data['progress_message'] = "扫描失败"
                else:
                    progress_data['progress_message'] = f"状态: {task.status.value}"
                
                # 异步更新Redis进度，不阻塞主流程，无数据库依赖
                asyncio.create_task(self._update_progress_to_redis(execution.id, progress_data))
                
                if task.status.value in ["completed", "failed", "cancelled"]:
                    break
                
                # 检查超时
                if (datetime.now() - start_time).total_seconds() > scan_timeout:
                    await memory_scanner.stop_scan(session_id, task_id)
                    raise Exception("扫描任务超时")
                
                await asyncio.sleep(2)  # 2秒检查一次，减少数据库写冲突
            
            # 获取扫描结果
            if task and task.status.value == "completed":
                try:
                    results = []
                    for result in task.results:
                        # 使用Pydantic的model_dump方法确保正确序列化
                        result_dict = result.model_dump(mode='json')
                        results.append(result_dict)
                    
                    execution.results_data = json.dumps(results, ensure_ascii=False)
                    execution.results_count = len(results)
                    logger.info(f"扫描任务完成: execution={execution.id}, task={task_id}, 结果数量: {len(results)}")
                except Exception as e:
                    logger.error(f"序列化扫描结果失败: {e}")
                    execution.results_data = "[]"
                    execution.results_count = 0
            else:
                error_msg = task.error_message if task else "未知错误"
                raise Exception(f"扫描失败: {error_msg}")
            
        finally:
            # 清理临时会话
            try:
                if session_id is not None:
                    session_manager.remove_session(session_id)
            except Exception as e:
                logger.warning(f"清理会话失败: {e}")

    async def _update_progress_to_redis(self, execution_id: int, progress_data: dict):
        """将任务进度存储到Redis，完全避免数据库锁冲突"""
        try:
            redis_client = get_redis_client()
            redis_key = f"task_progress:{execution_id}"
            
            # 存储到Redis，设置过期时间24小时
            async with redis_client.get_redis() as redis:
                await redis.setex(
                    redis_key,
                    24 * 3600,  # 24小时过期
                    json.dumps(progress_data, ensure_ascii=False)
                )
            
            logger.debug(f"Redis进度更新成功: 任务{execution_id}, 进度{progress_data.get('progress_percentage', 0):.1f}%")
                    
        except Exception as e:
            logger.warning(f"Redis进度更新失败 {execution_id}: {e}")
            # 进度更新失败不影响主任务继续执行
    
    async def get_task_progress(self, execution_id: int) -> Optional[Dict[str, Any]]:
        """从Redis获取任务进度"""
        try:
            redis_client = get_redis_client()
            redis_key = f"task_progress:{execution_id}"
            
            async with redis_client.get_redis() as redis:
                progress_json = await redis.get(redis_key)
                
            if progress_json:
                return json.loads(progress_json)
            return None
                    
        except Exception as e:
            logger.warning(f"获取Redis进度失败 {execution_id}: {e}")
            return None

    def cancel_execution(self, execution_id: int) -> bool:
        """取消任务执行"""
        if execution_id in self._active_executions:
            self._active_executions[execution_id].cancel()
            return True
        return False

    def get_active_executions(self) -> list:
        """获取活跃的执行任务ID列表"""
        return list(self._active_executions.keys())

# 全局执行器实例
_executor: Optional[TaskExecutor] = None

def get_task_executor() -> TaskExecutor:
    """获取任务执行器实例"""
    global _executor
    if _executor is None:
        _executor = TaskExecutor()
    return _executor