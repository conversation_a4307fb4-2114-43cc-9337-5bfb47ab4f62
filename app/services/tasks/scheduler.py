"""定时任务调度器"""

import asyncio
import logging
import json
from typing import Dict, List, Optional, Set
from datetime import datetime, timedelta
from croniter import croniter
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError
from sqlalchemy import select

from app.core.database import db_session  # 使用异步数据库会话
from app.models.task import UserScheduledTask, TaskExecution
from app.schemas.scheduled_task import TaskStatus

logger = logging.getLogger(__name__)

class TaskScheduler:
    """定时任务调度器"""
    
    def __init__(self):
        self.running = False
        self._scheduler_task: Optional[asyncio.Task] = None
        self._active_executions: Dict[int, asyncio.Task] = {}
        self._running_task_ids: Set[int] = set()  # 防止重复执行的任务ID集合
        self._scheduler_lock = asyncio.Lock()  # 调度器锁
        self._max_concurrent_tasks = 10  # 最大并发任务数
        self._retry_attempts = 3  # 失败重试次数
        self._scheduler_interval = 60  # 默认调度间隔(秒)
    
    async def start(self):
        """启动调度器"""
        if self.running:
            return
        
        self.running = True
        self._scheduler_task = asyncio.create_task(self._scheduler_loop())
        logger.info("定时任务调度器已启动")
    
    async def stop(self):
        """停止调度器"""
        self.running = False
        if self._scheduler_task:
            self._scheduler_task.cancel()
            try:
                await self._scheduler_task
            except asyncio.CancelledError:
                pass
        
        # 停止所有正在执行的任务
        for task in self._active_executions.values():
            task.cancel()
        
        if self._active_executions:
            await asyncio.gather(*self._active_executions.values(), return_exceptions=True)
        
        logger.info("定时任务调度器已停止")
    
    async def _scheduler_loop(self):
        """调度器主循环"""
        consecutive_errors = 0
        max_errors = 5
        
        while self.running:
            try:
                async with self._scheduler_lock:
                    await self._check_and_execute_tasks()
                
                consecutive_errors = 0
                # 智能调度间隔：根据任务数量动态调整
                interval = self._calculate_scheduler_interval()
                await asyncio.sleep(interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                consecutive_errors += 1
                logger.error(f"调度器执行错误 ({consecutive_errors}/{max_errors}): {e}")
                
                if consecutive_errors >= max_errors:
                    logger.critical("调度器连续错误过多，暂停运行")
                    await asyncio.sleep(300)  # 暂停5分钟
                    consecutive_errors = 0
                else:
                    await asyncio.sleep(min(60 * consecutive_errors, 300))  # 指数退避
    
    async def _check_and_execute_tasks(self):
        """检查并执行到期的任务"""
        if len(self._active_executions) >= self._max_concurrent_tasks:
            logger.warning(f"当前并发任务数已达上限 {self._max_concurrent_tasks}，跳过本次调度")
            return
            
        now = datetime.now()
        
        # 使用异步数据库会话
        async with db_session() as db:
            # 获取需要执行的任务（排除已在运行的任务）
            tasks_stmt = select(UserScheduledTask).where(
                UserScheduledTask.is_active == True,
                UserScheduledTask.next_execution <= now
            )
            
            if self._running_task_ids:
                tasks_stmt = tasks_stmt.where(
                    ~UserScheduledTask.id.in_(self._running_task_ids)
                )
            
            tasks_stmt = tasks_stmt.limit(
                self._max_concurrent_tasks - len(self._active_executions)
            )
            
            tasks_result = await db.execute(tasks_stmt)
            tasks = tasks_result.scalars().all()
            
            for task in tasks:
                # 防重复执行检查
                if task.id in self._running_task_ids:
                    logger.debug(f"任务 {task.id} 已在执行中，跳过")
                    continue
                
                # 检查是否已达最大执行次数
                if (task.max_executions is not None and 
                    task.current_executions >= task.max_executions):
                    task.is_active = False
                    await db.commit()
                    logger.info(f"任务 {task.id} 已达最大执行次数，已停用")
                    continue
                
                # 检查用户任务数量限制
                count_stmt = select(TaskExecution).where(
                    TaskExecution.user_id == task.user_id,
                    TaskExecution.status.in_(["pending", "running"])
                )
                count_result = await db.execute(count_stmt)
                user_active_tasks = len(count_result.scalars().all())
                
                if user_active_tasks >= 5:  # 每用户最多5个并发任务
                    logger.warning(f"用户 {task.user_id} 并发任务数已达上限，跳过任务 {task.id}")
                    continue
                
                # 标记任务正在运行
                self._running_task_ids.add(task.id)
                
                try:
                    # 创建执行记录
                    execution = TaskExecution(
                        user_id=task.user_id,
                        scheduled_task_id=task.id,
                        trigger_type="scheduled",
                        task_type=task.task_type,
                        task_config=task.task_config,
                        status="pending"
                    )
                    db.add(execution)
                    
                    # 更新任务状态和下次执行时间
                    cron = croniter(task.cron_expression, now)
                    task.next_execution = cron.get_next(datetime)
                    task.current_executions += 1
                    task.last_execution = now
                    
                    await db.commit()
                    await db.refresh(execution)
                    
                    logger.info(f"任务 {task.id} 已调度执行，执行ID: {execution.id}")
                    
                    # 创建异步任务执行
                    task_coroutine = self._execute_task_with_retry(execution.id, task.id)
                    execution_task = asyncio.create_task(task_coroutine)
                    self._active_executions[execution.id] = execution_task
                    
                    # 任务完成后的清理回调
                    def cleanup_callback(future):
                        if execution.id in self._active_executions:
                            del self._active_executions[execution.id]
                        if task.id in self._running_task_ids:
                            self._running_task_ids.remove(task.id)
                    
                    execution_task.add_done_callback(cleanup_callback)
                    
                except Exception as e:
                    await db.rollback()
                    self._running_task_ids.discard(task.id)
                    logger.error(f"调度任务 {task.id} 时发生错误: {e}")
                    continue

    def _cleanup_task(self, execution_id: int, task_id: int):
        """清理任务"""
        self._active_executions.pop(execution_id, None)
        self._running_task_ids.discard(task_id)
        
    def _calculate_scheduler_interval(self) -> int:
        """计算智能调度间隔"""
        # 根据当前活跃任务数动态调整调度间隔
        active_count = len(self._active_executions)
        if active_count == 0:
            return 60  # 无活跃任务时，每分钟检查一次
        elif active_count < 3:
            return 30  # 少量任务时，30秒检查一次
        else:
            return 15  # 高负载时，15秒检查一次
    
    async def _execute_task_with_retry(self, execution_id: int, task_id: int):
        """带重试机制的任务执行"""
        for attempt in range(self._retry_attempts):
            try:
                await self._execute_task(execution_id)
                return  # 成功执行，退出重试循环
            except Exception as e:
                logger.error(f"任务执行第 {attempt + 1} 次尝试失败 (execution_id={execution_id}): {e}")
                if attempt == self._retry_attempts - 1:
                    # 最后一次尝试失败，记录最终失败状态
                    async with db_session() as db:
                        exec_stmt = select(TaskExecution).where(TaskExecution.id == execution_id)
                        exec_result = await db.execute(exec_stmt)
                        execution = exec_result.scalar_one_or_none()
                        if execution:
                            execution.status = TaskStatus.FAILED
                            execution.error_message = f"重试 {self._retry_attempts} 次后仍然失败: {str(e)}"
                            execution.end_time = datetime.now()
                            if execution.start_time:
                                execution.duration_seconds = int(
                                    (execution.end_time - execution.start_time).total_seconds()
                                )
                            await db.commit()
                else:
                    # 等待一段时间后重试
                    await asyncio.sleep(min(2 ** attempt, 30))  # 指数退避，最大30秒
    
    async def _execute_task(self, execution_id: int):
        """执行任务"""
        async with db_session() as db:
            exec_stmt = select(TaskExecution).where(TaskExecution.id == execution_id)
            exec_result = await db.execute(exec_stmt)
            execution = exec_result.scalar_one_or_none()
            if not execution:
                logger.error(f"任务执行记录不存在: {execution_id}")
                return
            
            try:
                # 更新状态为运行中
                execution.status = TaskStatus.RUNNING
                execution.start_time = datetime.now()
                await db.commit()
                
                # 根据任务类型执行
                if execution.task_type == "indicator_scan":
                    await self._execute_indicator_scan(execution, db)
                else:
                    raise ValueError(f"不支持的任务类型: {execution.task_type}")
                
                # 更新完成状态
                execution.status = TaskStatus.COMPLETED
                execution.end_time = datetime.now()
                execution.duration_seconds = int(
                    (execution.end_time - execution.start_time).total_seconds()
                )
                await db.commit()
                
                logger.info(f"任务执行完成 (execution_id={execution_id})")
                
            except Exception as e:
                await db.rollback()
                # 更新失败状态
                execution.status = TaskStatus.FAILED
                execution.error_message = str(e)
                execution.end_time = datetime.now()
                if execution.start_time:
                    execution.duration_seconds = int(
                        (execution.end_time - execution.start_time).total_seconds()
                    )
                await db.commit()
                raise

    async def _execute_indicator_scan(self, execution: TaskExecution, db: AsyncSession):
        """执行指标扫描任务"""
        from app.services.scan.manager import get_memory_scanner, get_session_manager
        from app.schemas.scheduled_task import IndicatorScanConfig
        
        # 解析任务配置
        config_data = json.loads(execution.task_config)
        config = IndicatorScanConfig(**config_data)
        
        # 获取扫描器实例
        memory_scanner = get_memory_scanner()
        session_manager = get_session_manager(memory_scanner)
        # 生成会话ID
        session_id = f"scheduled_indicator_scan_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        session_manager.create_session(session_id, user_id=str(execution.user_id))
        
        try:
            # 创建会话
            task_id = await memory_scanner.start_scan(
                session_id=session_id,
                indicators=config.indicators,
                stock_codes=config.stock_codes,
                parameters=config.parameters,
                scan_mode=config.scan_mode,
                periods=config.periods,
                adjust=config.adjust
            )
            
            # 等待扫描完成
            scan_timeout = 3600  # 1小时超时
            start_time = datetime.now()
            
            while True:
                task = session_manager.get_scan_task(session_id, task_id)
                if not task:
                    raise Exception("扫描任务丢失")
                
                if task.status.value in ["completed", "failed", "cancelled"]:
                    break
                
                # 检查超时
                if (datetime.now() - start_time).total_seconds() > scan_timeout:
                    await memory_scanner.stop_scan(session_id, task_id)
                    raise Exception("扫描任务超时")
                
                await asyncio.sleep(5)
            
            # 获取扫描结果
            if task and task.status.value == "completed":
                try:
                    results = []
                    for result in task.results:
                        # 使用Pydantic的model_dump方法确保正确序列化
                        result_dict = result.model_dump(mode='json')
                        results.append(result_dict)
                    
                    execution.results_data = json.dumps(results, ensure_ascii=False)
                    execution.results_count = len(results)
                    logger.info(f"定时任务扫描完成: {execution.id}, 结果数量: {len(results)}")
                except Exception as e:
                    logger.error(f"序列化扫描结果失败: {e}")
                    execution.results_data = "[]"
                    execution.results_count = 0
            else:
                error_msg = task.error_message if task else "未知错误"
                raise Exception(f"扫描失败: {error_msg}")
            
        finally:
            # 清理临时会话
            try:
                if session_id is not None:
                    session_manager.remove_session(session_id)
            except Exception as e:
                logger.warning(f"清理会话失败: {e}")

# 全局调度器实例
_scheduler: Optional[TaskScheduler] = None

def get_task_scheduler() -> TaskScheduler:
    """获取任务调度器实例"""
    global _scheduler
    if _scheduler is None:
        _scheduler = TaskScheduler()
    return _scheduler

async def start_scheduler():
    """启动调度器"""
    scheduler = get_task_scheduler()
    await scheduler.start()

async def stop_scheduler():
    """停止调度器"""
    scheduler = get_task_scheduler()
    await scheduler.stop()