"""麦睿数据提供者模块"""
from csv import Error
from datetime import date, datetime
from typing import Any, Dict, List, Optional
import aiohttp
from anyio import sleep
from app.core import logging
import asyncio

from app.core.config import settings

from ..base import DataFetcher
from ..adapter import BaseDataAdapter

logger = logging.getLogger(__name__)

    
class MairuiDataFetcher(DataFetcher):
    """麦睿数据获取器实现"""
    
    BASE_URL = "https://y.mairuiapi.com"
    
    def __init__(self, licence: str = settings.MAIRUI_TOKEN or ""):
        """
        初始化麦睿数据获取器。
        
        Args:
            licence: API许可证密钥
        """
        self.licence = licence
        self._session: Optional[aiohttp.ClientSession] = None
        self._adapter = MairuiAdapter()
        
    async def _ensure_session(self) -> aiohttp.ClientSession:
        """确保HTTP会话已创建"""
        if self._session is None:
            self._session = aiohttp.ClientSession()
        return self._session
    
    async def close(self):
        """关闭HTTP会话"""
        if self._session:
            await self._session.close()
            self._session = None
    
    async def _request(self, url: str) -> Any:
        """
        发送HTTP请求并返回JSON响应。
        
        Args:
            url: API端点URL
            
        Returns:
            Any: 解析后的JSON响应
            
        Raises:
            RuntimeError: 如果请求失败
        """
        session = await self._ensure_session()
        try:
            async with session.get(url) as response:
                response.raise_for_status()
                result = await response.json()
                
                # 检查API返回的错误
                if isinstance(result, dict) and "error" in result:
                    error_msg = result["error"]
                    logger.error(f"API返回错误: {error_msg}")
                    raise RuntimeError(f"API返回错误: {error_msg}")
                
                return result
        except Exception as e:
            logger.error(f"请求失败 {url}: {str(e)}")
            raise RuntimeError(f"API请求失败: {str(e)}")
    
    def _format_stock_code(self, code: str) -> str:
        """
        格式化股票代码，确保包含交易所后缀。
        
        Args:
            code: 股票代码，可能带有或不带有交易所后缀
            
        Returns:
            str: 格式化后的股票代码，包含交易所后缀（如：600000.SH）
        """
        # 如果代码已经包含交易所后缀（包含点号），则直接返回
        if "." in code:
            return code
            
        # 根据股票代码前缀判断交易所
        if code.startswith("6"):
            return f"{code}.SH"  # 上海交易所
        elif code.startswith("0") or code.startswith("3"):
            return f"{code}.SZ"  # 深圳交易所
        elif code.startswith("8") or code.startswith("4"):
            return f"{code}.BJ"  # 北京交易所
        else:
            logger.warning(f"无法确定股票 {code} 的交易所，默认使用原始代码")
            return code
    
    async def get_stock_list(self) -> List[Dict[str, Any]]:
        """获取股票列表"""
        url = f"{self.BASE_URL}/hslt/list/{self.licence}"
        return await self._request(url)
    
    async def get_period_data(
        self,
        code: str,
        period: str = "d",  # d=日线, w=周线, m=月线, y=年线
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        adjust: str = "n"  # n=不复权, f=前复权, b=后复权, fr=等比前复权, br=等比后复权
    ) -> List[Dict[str, Any]]:
        """
        获取指定周期的K线数据
        
        Args:
            code: 股票代码
            period: 数据周期 - d(日线), w(周线), m(月线), y(年线)
            start_date: 开始日期
            end_date: 结束日期
            adjust: 复权方式 - n(不复权), f(前复权), b(后复权)
            
        Returns:
            List[Dict[str, Any]]: K线数据列表
        """
        # 格式化股票代码，确保包含交易所后缀
        formatted_code = self._format_stock_code(code)
        
        # 验证周期参数
        valid_periods = ["d", "w", "m", "y"]
        if period not in valid_periods:
            raise ValueError(f"无效的周期参数: {period}. 有效值为: {valid_periods}")
        
        # 构建URL
        if start_date and end_date:
            url = (
                f"{self.BASE_URL}/hsstock/history/{formatted_code}/{period}/{adjust}/"
                f"{self.licence}?"
                f"st={start_date.strftime('%Y%m%d')}&"
                f"et={end_date.strftime('%Y%m%d')}"
            )
        else:
            url = f"{self.BASE_URL}/hsstock/history/{formatted_code}/{period}/{adjust}/{self.licence}"
        
        period_name = {"d": "日线", "w": "周线", "m": "月线", "y": "年线"}.get(period, period)
        logger.info(f"请求{period_name}数据: {formatted_code}")
        result = await self._request(url)
        
        # 使用配置的请求延迟
        from app.core.config import settings
        await sleep(settings.SCANNER_REQUEST_DELAY)
        return result
    
    async def get_daily_data(
        self,
        code: str,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> List[Dict[str, Any]]:
        """获取日线数据（向后兼容）"""
        return await self.get_period_data(code, "d", start_date, end_date)
    
    async def get_realtime_quotes(self, codes: List[str]) -> List[Dict[str, Any]]:
        """获取实时行情数据"""
        results = []
        for code in codes:
            try:
                # 使用新的API端点
                url = f"{self.BASE_URL}/hsstock/real/time/{code}/{self.licence}"
                logger.info(f"请求实时行情数据: {url}")
                # 发送请求
                data = await self._request(url)
                # 确保返回的数据是列表格式
                if isinstance(data, dict):
                    # 添加股票代码到数据中，便于后续处理
                    data['code'] = code
                    data = [data]
                # 将数据添加到结果列表中
                results.extend(data)
                # 使用配置的实时行情请求延迟
                from app.core.config import settings
                await sleep(settings.SCANNER_REALTIME_REQUEST_DELAY)
            except Exception as e:
                logger.error(f"获取实时行情失败 {code}: {str(e)}")
                
        return results
    
    async def get_index_components(self, index_code: str) -> List[str]:
        """获取指数成分股"""
        raise NotImplementedError("麦睿数据源不支持该接口")
    
    async def get_index_list(self) -> List[Dict[str, Any]]:
        """获取指数列表"""
        url = f"{self.BASE_URL}/hsindex/list/{self.licence}"
        logger.info(f"请求指数列表: {url}")
        return await self._request(url)
    
    async def get_index_realtime_quotes(self, codes: List[str]) -> List[Dict[str, Any]]:
        """获取指数实时行情数据"""
        results = []
        for code in codes:
            try:
                url = f"{self.BASE_URL}/hsindex/real/time/{code}/{self.licence}"
                logger.info(f"请求指数实时行情数据: {url}")
                data = await self._request(url)
                
                # 确保返回的数据是列表格式
                if isinstance(data, dict):
                    # 添加指数代码到数据中，便于后续处理
                    data['code'] = code
                    data = [data]
                # 将数据添加到结果列表中
                results.extend(data)
                # 使用配置的实时行情请求延迟
                from app.core.config import settings
                await sleep(settings.SCANNER_REALTIME_REQUEST_DELAY)
            except Exception as e:
                logger.error(f"获取指数实时行情失败 {code}: {str(e)}")
                
        return results
    
    async def get_index_period_data(
        self,
        code: str,
        period: str = "d",  # d=日线, w=周线, m=月线, y=年线
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> List[Dict[str, Any]]:
        """
        获取指数指定周期的K线数据
        
        Args:
            code: 指数代码
            period: 数据周期 - d(日线), w(周线), m(月线), y(年线)
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[Dict[str, Any]]: K线数据列表
        """
        # 验证周期参数
        valid_periods = ["d", "w", "m", "y"]
        if period not in valid_periods:
            raise ValueError(f"无效的周期参数: {period}. 有效值为: {valid_periods}")
        
        # 构建URL
        if start_date and end_date:
            url = (
                f"{self.BASE_URL}/hsindex/history/{code}/{period}/"
                f"{self.licence}?"
                f"st={start_date.strftime('%Y%m%d')}&"
                f"et={end_date.strftime('%Y%m%d')}"
            )
        else:
            url = f"{self.BASE_URL}/hsindex/history/{code}/{period}/{self.licence}"
        
        period_name = {"d": "日线", "w": "周线", "m": "月线", "y": "年线"}.get(period, period)
        logger.info(f"请求指数{period_name}数据: {code}")
        result = await self._request(url)
        
        # 使用配置的请求延迟
        from app.core.config import settings
        await sleep(settings.SCANNER_REQUEST_DELAY)
        return result
    
    async def get_financial_data(
        self,
        code: str,
        report_type: str = 'annual'
    ) -> List[Dict[str, Any]]:
        """获取财务数据"""
        raise NotImplementedError("麦睿数据源不支持该接口")

class MairuiAdapter(BaseDataAdapter):
    """麦睿数据适配器实现"""
    
    def _parse_datetime(self, date_str: str) -> datetime:
        """
        解析日期字符串，支持两种格式：
        1. YYYY-MM-DD HH:MM:SS
        2. YYYY-MM-DD HH:MM
        
        Args:
            date_str: 日期字符串
            
        Returns:
            datetime: 解析后的datetime对象
            
        Raises:
            ValueError: 如果日期格式无法解析
        """
        if not date_str:
            raise ValueError("日期字符串为空")
            
        try:
            # 首先尝试完整格式 YYYY-MM-DD HH:MM:SS
            return datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            try:
                # 然后尝试简短格式 YYYY-MM-DD HH:MM
                return datetime.strptime(date_str, "%Y-%m-%d %H:%M")
            except ValueError as e:
                logger.error(f"无法解析日期格式: {date_str}")
                raise ValueError(f"不支持的日期格式: {date_str}") from e
    
    def normalize_stock_info(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """标准化股票信息"""
        code = raw_data.get("dm", "")
        code = code.split('.')[0] or code  # 确保只取代码部分
        exchange = raw_data.get("jys", "").upper()
        full_code = f"{code}.{exchange}"
        return {
            "code": code,
            "name": raw_data.get("mc", ""),
            "exchange": exchange,
            "full_code": full_code,  # 生成完整股票代码
            "industry": "",  # 麦睿API不提供行业信息
            "listing_date": None  # 麦睿API不提供上市日期
        }
    
    def normalize_daily_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """标准化日线数据，直接转换为符合 StockDailyBase 模型的格式"""
        try:
            # 检查输入数据是否有效
            if not raw_data or not isinstance(raw_data, dict):
                logger.warning(f"无效的日线数据: {raw_data}")
                return {}
            
            # 转换日期 - 使用't'字段而非'd'字段
            date_str = raw_data.get("t", "")
            trade_date = None
            if date_str:
                # 尝试提取日期部分（忽略时间部分）
                date_parts = date_str.split(" ")[0] if " " in date_str else date_str
                trade_date = datetime.strptime(date_parts, "%Y-%m-%d").date()
            
            # 如果没有有效的日期，返回空数据
            if not trade_date:
                logger.warning(f"无效的日期数据: {date_str}")
                return {}
            
            # 计算涨跌幅（如果API未提供）
            change_pct = raw_data.get("zd", None)
            if change_pct is None and "pc" in raw_data and "c" in raw_data:
                prev_close = float(raw_data.get("pc", 0))
                if prev_close > 0:
                    curr_close = float(raw_data.get("c", 0))
                    change_pct = (curr_close - prev_close) / prev_close * 100
                else:
                    change_pct = 0
            
            return {
                "trade_date": trade_date,
                "open": float(raw_data.get("o", 0)),
                "high": float(raw_data.get("h", 0)),
                "low": float(raw_data.get("l", 0)),
                "close": float(raw_data.get("c", 0)),
                "volume": int(float(raw_data.get("v", 0)) * 100),  # 转换手数为股数并转为整数
                "amount": int(float(raw_data.get("a", 0))),  # 使用'a'字段存储成交额
                "turnover_rate": float(raw_data.get("hs", 0)),  # 保留原字段，但该字段可能不存在
                "change_pct": float(change_pct if change_pct is not None else 0)
            }
        except (ValueError, TypeError) as e:
            logger.error(f"日线数据格式化失败: {raw_data}, 错误: {e}")
            return {}
    
    def normalize_realtime_quote(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """标准化实时行情数据"""
        try:
            # 根据新的API字段结构处理数据
            result = {
                "code": raw_data.get("code", ""),  # 股票代码
                "price": float(raw_data.get("p", 0)),  # 最新价
                "open": float(raw_data.get("o", 0)),  # 开盘价
                "high": float(raw_data.get("h", 0)),  # 最高价
                "low": float(raw_data.get("l", 0)),  # 最低价
                "prev_close": float(raw_data.get("yc", 0)),  # 前收盘价
                "change": float(raw_data.get("ud", 0)),  # 涨跌额
                "change_pct": float(raw_data.get("pc", 0)),  # 涨跌幅
                "volume": float(raw_data.get("v", 0)),  # 成交总量
                "amount": float(raw_data.get("cje", 0)),  # 成交总额
                "amplitude": float(raw_data.get("zf", 0)),  # 振幅
                "time": raw_data.get("t", ""),  # 更新时间
                "pv": float(raw_data.get("pv", 0)),  # 原始成交总量
            }
            
            # 尝试解析时间字段
            if result["time"]:
                try:
                    result["time"] = self._parse_datetime(result["time"])
                except ValueError:
                    # 如果时间解析失败，使用当前时间
                    result["time"] = datetime.now()
            else:
                result["time"] = datetime.now()
            
            return result
        except (ValueError, TypeError) as e:
            logger.error(f"实时行情数据格式化失败: {raw_data}, 错误: {e}")
            return {}
    
    def normalize_index_info(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """标准化指数信息"""
        code = raw_data.get("dm", "").strip()
        exchange = raw_data.get("jys", "").strip().upper()
        name = raw_data.get("mc", "").strip()
        
        # 如果关键字段为空，返回空字典
        if not code or not name or not exchange:
            logger.warning(f"指数数据不完整，跳过: {raw_data}")
            return {}
        
        return {
            "code": code,
            "name": name,
            "exchange": exchange,
            "is_active": True
        }
    
    def normalize_index_daily_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """标准化指数日线数据，直接转换为符合 IndexDaily 模型的格式"""
        try:
            # 检查输入数据是否有效
            if not raw_data or not isinstance(raw_data, dict):
                logger.warning(f"无效的指数日线数据: {raw_data}")
                return {}
            
            # 转换日期 - 使用't'字段而非'd'字段
            date_str = raw_data.get("t", "")
            if not date_str:
                date_str = raw_data.get("trade_date", "")
            trade_date = None
            if date_str and isinstance(date_str, str):
                try:
                    # 尝试提取日期部分（忽略时间部分）
                    date_parts = date_str.split(" ")[0] if " " in date_str else date_str
                    trade_date = datetime.strptime(date_parts, "%Y-%m-%d").date()
                except ValueError as e:
                    logger.warning(f"日期格式解析失败: {date_str}, 错误: {e}")
                    trade_date = None
            
            # 如果没有有效的日期，返回空数据
            if not trade_date:
                logger.warning(f"无效的日期数据: {date_str}")
                return {}
            
            # 计算涨跌幅
            prev_close = float(raw_data.get("pc", 0))
            curr_close = float(raw_data.get("c", 0))
            
            if prev_close > 0:
                change_pct = (curr_close - prev_close) / prev_close * 100
            else:
                change_pct = 0
            
            return {
                "trade_date": trade_date,
                "open": float(raw_data.get("o", 0)),
                "high": float(raw_data.get("h", 0)),
                "low": float(raw_data.get("l", 0)),
                "close": float(raw_data.get("c", 0)),
                "volume": int(float(raw_data.get("v", 0))),  # 转为整数
                "amount": int(float(raw_data.get("a", 0))),  # 使用'a'字段存储成交额并转为整数
                "prev_close": prev_close,  # 前收盘价
                "change_pct": change_pct
            }
        except (ValueError, TypeError) as e:
            logger.error(f"指数日线数据格式化失败: {raw_data}, 错误: {e}")
            return {}
    
    def normalize_index_realtime_quote(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """标准化指数实时行情数据"""
        try:
            # 根据新的API字段结构处理数据
            result = {
                "code": raw_data.get("code", ""),  # 指数代码
                "price": float(raw_data.get("p", 0)),  # 最新价
                "open": float(raw_data.get("o", 0)),  # 开盘价
                "high": float(raw_data.get("h", 0)),  # 最高价
                "low": float(raw_data.get("l", 0)),  # 最低价
                "prev_close": float(raw_data.get("yc", 0)),  # 前收盘价
                "change": float(raw_data.get("ud", 0)),  # 涨跌额
                "change_pct": float(raw_data.get("pc", 0)),  # 涨跌幅
                "volume": float(raw_data.get("v", 0)),  # 成交总量
                "amount": float(raw_data.get("cje", 0)),  # 成交总额
                "amplitude": float(raw_data.get("zf", 0)),  # 振幅
                "time": raw_data.get("t", ""),  # 更新时间
                "pv": float(raw_data.get("pv", 0)),  # 原始成交总量
            }
            
            # 尝试解析时间字段
            if result["time"]:
                try:
                    result["time"] = self._parse_datetime(result["time"])
                except ValueError:
                    # 如果时间解析失败，使用当前时间
                    result["time"] = datetime.now()
            else:
                result["time"] = datetime.now()
            
            return result
        except (ValueError, TypeError) as e:
            logger.error(f"指数实时行情数据格式化失败: {raw_data}, 错误: {e}")
            return {}
