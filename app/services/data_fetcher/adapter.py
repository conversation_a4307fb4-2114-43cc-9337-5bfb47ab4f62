from datetime import datetime
from typing import Any, Dict, List, Optional, Callable
from app.core import logging
from functools import wraps
import asyncio
from abc import ABC, abstractmethod

from .base import DataFetcher

logger = logging.getLogger(__name__)

def retry_on_error(max_retries: int = 3, delay: float = 1.0):
    """
    装饰器: 在发生错误时进行重试。
    
    Args:
        max_retries: 最大重试次数
        delay: 重试之间的延迟时间(秒)
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_error = None
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_error = e
                    logger.warning(
                        f"请求失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}"
                    )
                    if attempt < max_retries - 1:
                        await asyncio.sleep(delay * (attempt + 1))
            if last_error is not None:
                raise last_error
            raise RuntimeError("执行函数失败，但没有捕获到具体错误")
        return wrapper
    return decorator

class DataAdapter(ABC):
    """数据适配器基类，定义数据转换方法"""
    
    @abstractmethod
    def normalize_stock_info(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """标准化股票基本信息"""
        pass
    
    @abstractmethod
    def normalize_daily_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """标准化日线数据"""
        pass
    
    @abstractmethod
    def normalize_realtime_quote(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """标准化实时行情数据"""
        pass

class BaseDataAdapter(DataAdapter):
    """基础数据适配器实现"""
    
    def normalize_stock_info(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        标准化股票基本信息。
        
        Args:
            raw_data: 原始数据

        Returns:
            Dict[str, Any]: 标准化后的数据，包含以下字段:
                - code: str, 股票代码
                - name: str, 股票名称
                - industry: str, 所属行业
                - market: str, 交易市场
                - list_date: str, 上市日期
        """
        return {
            "code": str(raw_data.get("code", "")),
            "name": str(raw_data.get("name", "")),
            "industry": str(raw_data.get("industry", "")),
            "market": str(raw_data.get("market", "")),
            "list_date": str(raw_data.get("list_date", ""))
        }

    def normalize_daily_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        标准化日线数据。
        
        Args:
            raw_data: 原始数据

        Returns:
            Dict[str, Any]: 标准化后的数据，包含统一格式的日线信息
        """
        return {
            "date": raw_data.get("date"),
            "open": float(raw_data.get("open", 0)),
            "high": float(raw_data.get("high", 0)),
            "low": float(raw_data.get("low", 0)),
            "close": float(raw_data.get("close", 0)),
            "volume": float(raw_data.get("volume", 0)),
            "amount": float(raw_data.get("amount", 0)),
            "turnover": float(raw_data.get("turnover", 0)),
            "change_pct": float(raw_data.get("change_pct", 0))
        }

    def normalize_realtime_quote(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        标准化实时行情数据。
        
        Args:
            raw_data: 原始数据

        Returns:
            Dict[str, Any]: 标准化后的数据，包含统一格式的实时行情
        """
        return {
            "code": str(raw_data.get("code", "")),
            "price": float(raw_data.get("price", 0)),
            "change": float(raw_data.get("change", 0)),
            "change_pct": float(raw_data.get("change_pct", 0)),
            "volume": float(raw_data.get("volume", 0)),
            "amount": float(raw_data.get("amount", 0)),
            "time": raw_data.get("time", datetime.now())
        }

class AdaptedDataFetcher(DataFetcher):
    """
    适配后的数据获取器，实现了统一的数据获取接口。
    """

    def __init__(self, data_fetcher: Any, adapter: DataAdapter):
        """
        初始化适配后的数据获取器。

        Args:
            data_fetcher: 原始数据获取器实例
            adapter: 数据适配器实例
        """
        self._fetcher = data_fetcher
        self._adapter = adapter

    @retry_on_error()
    async def get_stock_list(self) -> List[Dict[str, Any]]:
        """获取标准化的股票列表"""
        raw_data = await self._fetcher.get_stock_list()
        return [
            self._adapter.normalize_stock_info(item)
            for item in raw_data
        ]

    @retry_on_error()
    async def get_daily_data(
        self,
        code: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """获取标准化的日线数据"""
        raw_data = await self._fetcher.get_daily_data(code, start_date, end_date)
        
        # 检查返回的数据格式
        if not isinstance(raw_data, list):
            logger.error(f"API返回的数据格式不正确: {raw_data}")
            return []
        
        result = []
        for item in raw_data:
            normalized = self._adapter.normalize_daily_data(item)
            # 只有当标准化成功且有有效数据时才添加到结果中
            if normalized:
                result.append(normalized)
        
        return result

    @retry_on_error()
    async def get_realtime_quotes(self, codes: List[str]) -> List[Dict[str, Any]]:
        """获取标准化的实时行情数据"""
        raw_data = await self._fetcher.get_realtime_quotes(codes)
        return [
            self._adapter.normalize_realtime_quote(item)
            for item in raw_data
        ]

    @retry_on_error()
    async def get_index_components(self, index_code: str) -> List[str]:
        """获取指数成分股列表"""
        return await self._fetcher.get_index_components(index_code)

    @retry_on_error()
    async def get_financial_data(
        self,
        code: str,
        report_type: str = 'annual'
    ) -> List[Dict[str, Any]]:
        """获取财务数据"""
        return await self._fetcher.get_financial_data(code, report_type)
