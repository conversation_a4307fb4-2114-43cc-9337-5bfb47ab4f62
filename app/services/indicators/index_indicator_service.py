"""
指数技术指标计算服务
提供各种技术指标（MACD/KDJ/ARBR/RSI等）的计算功能，专门针对指数数据
"""
import pandas as pd
import numpy as np
import pandas_ta as ta
from typing import List, Dict, Any, Optional, Union, Tuple
from datetime import datetime, timedelta
from app.services.storage.index_storage import IndexStorageService
from app.core.exceptions import BaseAppException, ValidationException
from app.core.logging import logging
from app.utils.indicators import TechnicalIndicators
from app.utils.index_data_fetcher import IndexDataFetcher

logger = logging.getLogger(__name__)

class IndexIndicatorService:
    """指数技术指标计算服务"""
    
    def __init__(self, storage_service: IndexStorageService):
        """初始化指标计算服务
        
        Args:
            storage_service: 指数数据存储服务
        """
        self.storage = storage_service
        # 初始化指数数据获取工具
        self.data_fetcher = IndexDataFetcher(storage_service)
    
    async def calculate_macd(
        self, 
        index_code: str,
        start_date: Union[str, datetime],
        end_date: Union[str, datetime],
        fast_period: int = 12,
        slow_period: int = 26,
        signal_period: int = 9,
        freq: str = 'D'
    ) -> Dict[str, Any]:
        """计算指数MACD指标
        
        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期
            fast_period: 快线周期
            slow_period: 慢线周期
            signal_period: 信号线周期
            freq: 数据周期
            
        Returns:
            Dict[str, Any]: MACD指标数据
        """
        try:
            # 获取指数数据
            df = await self._get_index_dataframe(index_code, start_date, end_date, freq)
            
            if df.empty:
                return {"date": [], "diff": [], "dea": [], "macd": []}
            
            # 使用统一指标工具计算MACD
            dif, dea, macd = TechnicalIndicators.calculate_macd(
                df['close'], fast_period, slow_period, signal_period
            )
            
            # 格式化输出
            return {
                "date": df['trade_date'].dt.strftime('%Y-%m-%d').tolist(),
                "diff": self._handle_nan_values(dif.tolist()),
                "dea": self._handle_nan_values(dea.tolist()),
                "macd": self._handle_nan_values(macd.tolist())
            }
            
        except Exception as e:
            logger.error(f"计算指数MACD失败: {e}", exc_info=True)
            raise BaseAppException(f"计算指数MACD失败: {str(e)}")
    
    async def calculate_kdj(
        self,
        index_code: str,
        start_date: Union[str, datetime],
        end_date: Union[str, datetime],
        window: int = 9,
        signal_period: int = 3,
        freq: str = 'D'
    ) -> Dict[str, Any]:
        """计算指数KDJ指标
        
        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期
            window: 计算窗口
            signal_period: 信号线周期
            freq: 数据周期
            
        Returns:
            Dict[str, Any]: KDJ指标数据
        """
        try:
            # 获取指数数据
            df = await self._get_index_dataframe(index_code, start_date, end_date, freq)
            
            if df.empty:
                return {"date": [], "k": [], "d": [], "j": []}
            
            # 使用统一指标工具计算KDJ
            k, d, j = TechnicalIndicators.calculate_kdj(
                df['high'], df['low'], df['close'], window, signal_period
            )
            
            # 格式化输出
            return {
                "date": df['trade_date'].dt.strftime('%Y-%m-%d').tolist(),
                "k": self._handle_nan_values(k.tolist()),
                "d": self._handle_nan_values(d.tolist()),
                "j": self._handle_nan_values(j.tolist())
            }
            
        except Exception as e:
            logger.error(f"计算指数KDJ失败: {e}", exc_info=True)
            raise BaseAppException(f"计算指数KDJ失败: {str(e)}")
    
    async def calculate_rsi(
        self,
        index_code: str,
        start_date: Union[str, datetime],
        end_date: Union[str, datetime],
        periods: List[int] = [6, 12, 24],
        freq: str = 'D'
    ) -> Dict[str, Any]:
        """计算指数RSI指标
        
        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期
            periods: RSI周期列表
            freq: 数据周期
            
        Returns:
            Dict[str, Any]: RSI指标数据
        """
        try:
            # 获取指数数据
            df = await self._get_index_dataframe(index_code, start_date, end_date, freq)
            
            if df.empty:
                return {"date": [], "rsi1": [], "rsi2": [], "rsi3": []}
            
            # 计算多个周期的RSI
            rsi_results = {}
            for i, period in enumerate(periods):
                rsi_key = f"rsi{i+1}"
                rsi_data = TechnicalIndicators.calculate_rsi(df['close'], period)
                rsi_results[rsi_key] = self._handle_nan_values(rsi_data.tolist())
            
            # 格式化输出
            result = {"date": df['trade_date'].dt.strftime('%Y-%m-%d').tolist()}
            result.update(rsi_results)
            
            return result
            
        except Exception as e:
            logger.error(f"计算指数RSI失败: {e}", exc_info=True)
            raise BaseAppException(f"计算指数RSI失败: {str(e)}")
    
    async def calculate_volume_analysis(
        self,
        index_code: str,
        start_date: Union[str, datetime],
        end_date: Union[str, datetime],
        ma_periods: List[int] = [5, 10, 20],
        freq: str = 'D'
    ) -> Dict[str, Any]:
        """计算指数成交量分析
        
        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期
            ma_periods: 移动平均周期列表
            freq: 数据周期
            
        Returns:
            Dict[str, Any]: 成交量分析数据
        """
        try:
            # 获取指数数据
            df = await self._get_index_dataframe(index_code, start_date, end_date, freq)
            
            if df.empty:
                return {"date": [], "volume": [], "volume_ma": {}}
            
            # 计算成交量移动平均
            volume_ma = {}
            for period in ma_periods:
                ma_key = f"ma{period}"
                volume_ma[ma_key] = self._handle_nan_values(
                    df['volume'].rolling(window=period).mean().tolist()
                )
            
            # 格式化输出
            return {
                "date": df['trade_date'].dt.strftime('%Y-%m-%d').tolist(),
                "volume": df['volume'].tolist(),
                "volume_ma": volume_ma
            }
            
        except Exception as e:
            logger.error(f"计算指数成交量分析失败: {e}", exc_info=True)
            raise BaseAppException(f"计算指数成交量分析失败: {str(e)}")
    
    async def _get_index_dataframe(
        self,
        index_code: str,
        start_date: Union[str, datetime],
        end_date: Union[str, datetime],
        freq: str = 'D'
    ) -> pd.DataFrame:
        """获取指数数据并转换为DataFrame
        
        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期
            freq: 数据周期
            
        Returns:
            pd.DataFrame: 指数数据DataFrame
        """
        # 使用IndexDataFetcher获取数据，支持API补充
        df = await self.data_fetcher.fetch_index_data(
            index_code, 
            start_date, 
            end_date,
            use_provider_fallback=True  # 允许在数据库查询失败时使用数据提供者
        )
        
        if df.empty:
            return pd.DataFrame()
        
        # 根据频率处理数据
        if freq == 'W':
            df = self.data_fetcher.resample_kline(df, 'W')
        elif freq == 'M':
            df = self.data_fetcher.resample_kline(df, 'M')
        
        return df
    
    
    def _handle_nan_values(self, values: List[float]) -> List[float]:
        """处理NaN值，转换为0.0"""
        return [0.0 if pd.isna(v) else float(v) for v in values]