"""
技术指标计算服务
提供各种技术指标（MACD/KDJ/ARBR/RSI等）的计算功能
以及日K/周K/月K数据转换逻辑
"""
import pandas as pd
import numpy as np
import pandas_ta as ta
from typing import List, Dict, Any, Optional, Union, Tuple
from datetime import datetime, timedelta
from app.services.storage.stock_storage import StockStorageService
from app.core.exceptions import BaseAppException, ValidationException  # 修改为使用已有的异常类
from app.core.logging import logging
from app.utils.data_fetcher import StockDataFetcher  # 导入数据获取工具类
from app.utils.indicators import TechnicalIndicators  # 导入统一指标工具

logger = logging.getLogger(__name__)

class IndicatorService:
    """股票技术指标计算服务"""
    
    def __init__(self, storage_service: StockStorageService):
        """初始化指标计算服务
        
        Args:
            storage_service: 股票数据存储服务
        """
        self.storage = storage_service
        # 创建数据获取工具实例
        self.data_fetcher = StockDataFetcher(storage_service)
    
    async def calculate_macd(
        self, 
        stock_code: str,
        start_date: Union[str, datetime],
        end_date: Union[str, datetime],
        fast_period: int = 12,
        slow_period: int = 26,
        signal_period: int = 9,
        freq: str = 'D'
    ) -> Dict[str, Any]:
        """计算MACD指标
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            fast_period: 快线周期
            slow_period: 慢线周期
            signal_period: 信号线周期
            freq: 数据频率，'D'日线，'W'周线，'M'月线
            
        Returns:
            Dict: 包含MACD计算结果的字典
        """
        try:
            # 获取数据 - 确保有足够的历史数据用于计算
            min_periods = max(fast_period, slow_period) * 3  # 至少需要3倍慢线周期的数据
            df = await self.data_fetcher.fetch_stock_data(
                stock_code, start_date, end_date, min_periods=min_periods
            )
            
            # 转换周期
            if freq in ['W', 'M']:
                df = self.data_fetcher.resample_kline(df, freq)
            
            if df.empty:
                return {"error": "没有可用的数据"}
            
            if len(df) < min_periods:
                logger.warning(f"MACD计算数据不足, 仅有 {len(df)} 条记录, 少于建议的 {min_periods} 条")
            
            # 默认先使用统一指标工具计算MACD
            logger.info(f"使用统一指标工具计算MACD")
            dif, dea, macd = TechnicalIndicators.calculate_macd(
                df['close'], fast_period, slow_period, signal_period
            )
            
            # 格式化输出
            result = {
                "date": df.index.strftime('%Y-%m-%d').tolist(),
                "diff": self._handle_nan_values(dif.tolist()),
                "dea": self._handle_nan_values(dea.tolist()),
                "macd": self._handle_nan_values(macd.tolist())
            }
            
            return result
            
        except ValidationException as e:
            return {"error": str(e)}
        except Exception as e:
            logger.error(f"计算MACD失败: {str(e)}")
            return {"error": f"计算MACD时发生错误: {str(e)}"}
            
    async def calculate_kdj(
        self, 
        stock_code: str,
        start_date: Union[str, datetime],
        end_date: Union[str, datetime],
        window: int = 9,
        signal_period: int = 3,
        freq: str = 'D'
    ) -> Dict[str, Any]:
        """计算KDJ指标
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            window: 计算窗口
            signal_period: 信号线周期
            freq: 数据频率，'D'日线，'W'周线，'M'月线
            
        Returns:
            Dict: 包含KDJ计算结果的字典
        """
        try:
            # 获取数据 - 确保有足够的历史数据用于计算
            min_periods = window * 3  # 至少需要3倍窗口期的数据
            df = await self.data_fetcher.fetch_stock_data(
                stock_code, start_date, end_date, min_periods=min_periods
            )
            
            # 转换周期
            if freq in ['W', 'M']:
                df = self.data_fetcher.resample_kline(df, freq)
            
            if df.empty:
                return {"error": "没有可用的数据"}
            
            if len(df) < min_periods:
                logger.warning(f"KDJ计算数据不足, 仅有 {len(df)} 条记录, 少于建议的 {min_periods} 条")
            
            # 默认先使用统一指标工具计算KDJ
            logger.info(f"使用统一指标工具计算KDJ")
            k, d, j = TechnicalIndicators.calculate_kdj(
                df['high'], df['low'], df['close'], window, signal_period, signal_period
            )
            
            # 创建结果DataFrame
            kdj_result = pd.DataFrame({
                'K': k,
                'D': d, 
                'J': j
            }, index=df.index)
            
            # 如果统一工具计算失败，尝试使用pandas_ta作为备选
            if kdj_result.empty or kdj_result.isna().all().all():
                logger.info(f"统一工具计算KDJ失败，切换到pandas_ta计算")
                # 使用pandas_ta计算KDJ (使用stoch函数)
                kdj_result = ta.stoch(
                    df['high'], 
                    df['low'], 
                    df['close'], 
                    k=window, 
                    d=signal_period, 
                    smooth_k=signal_period
                )
                
                # 检查pandas_ta计算结果
                if kdj_result is None or kdj_result.empty:
                    return {"error": "计算KDJ指标失败，数据可能不足"}
                    
                # 确保列名符合预期
                k_col = f'STOCHk_{window}_{signal_period}_{signal_period}'
                d_col = f'STOCHd_{window}_{signal_period}_{signal_period}'
                
                # 重命名列或创建新列
                if k_col in kdj_result.columns and d_col in kdj_result.columns:
                    # 计算J值
                    kdj_result['J'] = 3 * kdj_result[k_col] - 2 * kdj_result[d_col]
                    # 重命名列
                    column_mapping = {k_col: 'K', d_col: 'D'}
                    kdj_result = kdj_result.rename(columns=column_mapping)
                else:
                    return {"error": f"KDJ计算结果中缺少必要的列"}
            
            # 合并结果
            result_df = pd.concat([df, kdj_result], axis=1)
            result_df = result_df.fillna(0)  # 保证无NaN，防止json序列化报错
            # 准备返回数据
            result = {
                "stock_code": stock_code,
                "indicator": "kdj",
                "params": {
                    "window": window,
                    "signal_period": signal_period,
                    "freq": freq
                },
                "data": result_df.reset_index().to_dict('records')
            }
            
            return result
        except ValidationException as e:
            return {"error": str(e)}
        except Exception as e:
            logger.error(f"计算KDJ失败: {str(e)}")
            return {"error": f"计算KDJ时发生错误: {str(e)}"}
            
    async def calculate_rsi(
        self, 
        stock_code: str,
        start_date: Union[str, datetime],
        end_date: Union[str, datetime],
        periods: List[int] = [6, 12, 24],
        freq: str = 'D'
    ) -> Dict[str, Any]:
        """计算RSI指标
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            periods: RSI周期列表
            freq: 数据频率，'D'日线，'W'周线，'M'月线
            
        Returns:
            Dict: 包含RSI计算结果的字典
        """
        try:
            # 获取数据 - 确保有足够的历史数据用于计算
            min_periods = max(periods) * 3  # 至少需要3倍最大RSI周期的数据
            df = await self.data_fetcher.fetch_stock_data(
                stock_code, start_date, end_date, min_periods=min_periods
            )
            
            # 转换周期
            if freq in ['W', 'M']:
                df = self.data_fetcher.resample_kline(df, freq)
            
            if df.empty:
                return {"error": "没有可用的数据"}
            
            if len(df) < min_periods:
                logger.warning(f"RSI计算数据不足, 仅有 {len(df)} 条记录, 少于建议的 {min_periods} 条")
            
            # 计算多周期RSI
            rsi_dfs = []
            has_pandas_ta_fallback = False
            
            for period in periods:
                # 先使用统一指标工具计算RSI
                logger.info(f"使用统一指标工具计算周期为{period}的RSI")
                rsi = TechnicalIndicators.calculate_rsi(df['close'], period)
                
                # 如果统一工具计算失败，使用pandas_ta作为备选
                if rsi is None or rsi.empty or rsi.isna().all():
                    logger.info(f"统一工具计算周期为{period}的RSI失败，切换到pandas_ta计算")
                    has_pandas_ta_fallback = True
                    # 使用pandas_ta计算RSI
                    rsi = ta.rsi(df['close'], length=period)
                    
                    # 检查pandas_ta计算结果
                    if rsi is None or rsi.isna().all():
                        logger.warning(f"使用pandas_ta计算周期为{period}的RSI也失败了")
                        continue
                
                # 设置名称
                if isinstance(rsi, pd.Series) and not rsi.empty and not rsi.isna().all():
                    rsi.name = f'RSI_{period}'
                    rsi_dfs.append(rsi)
            
            if not rsi_dfs:
                return {"error": "计算RSI指标失败，数据可能不足"}
            
            # 合并所有RSI结果
            rsi_df = pd.concat(rsi_dfs, axis=1)
            # 清理可能存在的NaN值
            rsi_df = rsi_df.fillna(50)  # 用50（中值）填充NaN
            # 合并结果
            result_df = pd.concat([df, rsi_df], axis=1)
            result_df = result_df.fillna(0)  # 保证无NaN，防止json序列化报错
            # 准备返回数据
            result = {
                "stock_code": stock_code,
                "indicator": "rsi",
                "params": {
                    "periods": periods,
                    "freq": freq
                },
                "data": result_df.reset_index().to_dict('records')
            }
            
            return result
        except ValidationException as e:
            return {"error": str(e)}
        except Exception as e:
            logger.error(f"计算RSI失败: {str(e)}")
            return {"error": f"计算RSI时发生错误: {str(e)}"}
            
    async def calculate_arbr(
        self, 
        stock_code: str,
        start_date: Union[str, datetime],
        end_date: Union[str, datetime],
        window: int = 26,
        freq: str = 'D'
    ) -> Dict[str, Any]:
        """计算AR/BR指标
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            window: 计算窗口
            freq: 数据频率，'D'日线，'W'周线，'M'月线
            
        Returns:
            Dict: 包含AR/BR计算结果的字典
        """
        try:
            # 获取更多历史数据，以确保计算准确性
            min_periods = window * 2
            df = await self.data_fetcher.fetch_stock_data(
                stock_code, start_date, end_date, min_periods=min_periods
            )
            
            # 转换周期
            if freq in ['W', 'M']:
                df = self.data_fetcher.resample_kline(df, freq)
            
            if df.empty:
                return {"error": "没有可用的数据"}
            
            if len(df) < window + 1:  # 至少需要窗口期+1的数据（计算前一日收盘价）
                return {"error": f"数据不足，无法计算ARBR指标，需要至少 {window + 1} 条记录"}
            
            # 先使用自己的实现计算ARBR
            logger.info(f"使用手动方法计算ARBR")
            arbr_result = self._calculate_arbr_manually(df, window)
            
            # 如果手动计算失败，则使用传统方法计算
            if arbr_result is None or arbr_result.empty:
                logger.info(f"手动计算ARBR失败，切换到传统方法计算")
                # 添加前一交易日收盘价
                df['prev_close'] = df['close'].shift(1)
                df = df.dropna().copy()  # 确保后续赋值不会触发SettingWithCopyWarning
                
                if df.empty:
                    return {"error": "处理数据后没有有效记录"}
                
                # 安全计算AR/BR，捕获所有可能的错误
                try:
                    # 计算AR: ∑(H-O) / ∑(O-L) × 100
                    df['h_o'] = df['high'] - df['open']
                    df['o_l'] = df['open'] - df['low']
                    df['h_o_sum'] = df['h_o'].rolling(window=window).sum()
                    df['o_l_sum'] = df['o_l'].rolling(window=window).sum()
                    
                    # 避免除以零 - 使用apply和lambda函数处理每一行
                    df['AR'] = df.apply(
                        lambda x: 100 * x['h_o_sum'] / x['o_l_sum'] if x['o_l_sum'] > 0 else 0, 
                        axis=1
                    )
                    
                    # 计算BR: ∑(H-PC) / ∑(PC-L) × 100
                    df['h_pc'] = df['high'] - df['prev_close']
                    df['pc_l'] = df['prev_close'] - df['low']
                    df['h_pc_sum'] = df['h_pc'].rolling(window=window).sum()
                    df['pc_l_sum'] = df['pc_l'].rolling(window=window).sum()
                    
                    # 避免除以零
                    df['BR'] = df.apply(
                        lambda x: 100 * x['h_pc_sum'] / x['pc_l_sum'] if x['pc_l_sum'] > 0 else 0, 
                        axis=1
                    )
                    
                    # 限制极端值，避免图表展示问题
                    df['AR'] = df['AR'].clip(0, 1000)  # AR一般不超过1000
                    df['BR'] = df['BR'].clip(0, 1000)  # BR一般不超过1000
                    
                    # 结果保存到arbr_result
                    arbr_result = df[['AR', 'BR']].copy()
                    
                except Exception as e:
                    logger.error(f"计算AR/BR指标时发生错误: {str(e)}")
                    return {"error": f"计算过程中出错: {str(e)}"}
            
            # 合并结果
            result_df = pd.concat([df[['open', 'high', 'low', 'close', 'volume', 'amount']], arbr_result], axis=1)
            result_df = result_df.fillna(0)  # 保证无NaN，防止json序列化报错
            # 准备返回数据
            result = {
                "stock_code": stock_code,
                "indicator": "arbr",
                "params": {
                    "window": window,
                    "freq": freq
                },
                "data": result_df.reset_index().to_dict('records')
            }
            
            return result
        except ValidationException as e:
            return {"error": str(e)}
        except Exception as e:
            logger.error(f"计算ARBR失败: {str(e)}")
            return {"error": f"计算ARBR时发生错误: {str(e)}"}
            
    def _calculate_arbr_manually(
        self,
        df: pd.DataFrame,
        window: int = 26
    ) -> pd.DataFrame:
        """手动计算AR/BR指标
        
        Args:
            df: 包含行情数据的DataFrame
            window: 计算窗口
            
        Returns:
            DataFrame: 包含AR/BR计算结果的DataFrame
        """
        try:
            # 检查输入数据
            required_columns = ['open', 'high', 'low', 'close']
            if any(col not in df.columns for col in required_columns) or len(df) < window + 1:
                logger.error("手动计算ARBR: 输入数据不足或缺少必要列")
                return pd.DataFrame()
            
            # 创建副本以避免对原始数据进行修改
            result = pd.DataFrame(index=df.index)
            
            # 添加前一交易日收盘价
            prev_close = df['close'].shift(1)
            
            # 计算AR指标部分数据
            h_o = df['high'] - df['open']  # 高价减开盘价
            o_l = df['open'] - df['low']   # 开盘价减低价
            
            # 计算BR指标部分数据
            h_pc = df['high'] - prev_close   # 高价减前收盘价
            pc_l = prev_close - df['low']    # 前收盘价减低价
            
            # 计算移动窗口的和
            h_o_sum = h_o.rolling(window=window).sum()
            o_l_sum = o_l.rolling(window=window).sum()
            h_pc_sum = h_pc.rolling(window=window).sum()
            pc_l_sum = pc_l.rolling(window=window).sum()
            
            # 计算AR: ∑(H-O) / ∑(O-L) × 100
            ar = pd.Series(np.zeros(len(df)), index=df.index)
            # 使用小容差避免浮点数精度问题
            valid_ar_indices = o_l_sum > 1e-9
            ar[valid_ar_indices] = 100 * h_o_sum[valid_ar_indices] / o_l_sum[valid_ar_indices]
            
            # 计算BR: ∑(H-PC) / ∑(PC-L) × 100
            br = pd.Series(np.zeros(len(df)), index=df.index)
            # 使用小容差避免浮点数精度问题
            valid_br_indices = pc_l_sum > 1e-9
            br[valid_br_indices] = 100 * h_pc_sum[valid_br_indices] / pc_l_sum[valid_br_indices]
            
            # 限制极端值，避免图表展示问题
            ar = ar.clip(0, 1000)  # AR一般不超过1000
            br = br.clip(0, 1000)  # BR一般不超过1000
            
            # 创建结果DataFrame
            result['AR'] = ar
            result['BR'] = br
            
            return result
            
        except Exception as e:
            logger.error(f"手动计算ARBR失败: {str(e)}")
            return pd.DataFrame()  # 返回空DataFrame
        
    async def get_volume_analysis(
        self, 
        stock_code: str,
        start_date: Union[str, datetime],
        end_date: Union[str, datetime],
        window: int = 20,
        freq: str = 'D'
    ) -> Dict[str, Any]:
        """获取成交量分析数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            window: 计算窗口（默认20日）
            freq: 数据频率，'D'日线，'W'周线，'M'月线
            
        Returns:
            Dict: 包含成交量分析结果的字典
        """
        try:
            # 获取数据 - 确保有足够的历史数据用于计算
            min_periods = window * 2  # 至少需要2倍窗口期的数据
            df = await self.data_fetcher.fetch_stock_data(
                stock_code, start_date, end_date, min_periods=min_periods
            )
            
            # 转换周期
            if freq in ['W', 'M']:
                df = self.data_fetcher.resample_kline(df, freq)
            
            if df.empty:
                return {"error": "没有可用的数据"}
            
            if len(df) < window + 1:
                return {"error": f"数据不足，无法计算成交量分析指标，需要至少 {window + 1} 条记录"}
            
            # 先使用自己的实现计算成交量分析指标
            logger.info(f"使用手动方法计算成交量分析指标")
            volume_result = self._calculate_volume_analysis_manually(df, window)
            
            # 如果手动计算失败，则使用备选方法计算
            if volume_result is None or volume_result.empty:
                logger.info(f"手动计算成交量分析失败，切换到传统方法计算")
                # 计算成交量相关指标
                try:
                    # 计算成交量移动平均线(volume MA)
                    df['VMA20'] = df['volume'].rolling(window=window).mean()
                    
                    # 计算相对前一天的成交量变化比例
                    df['VOL_CHANGE'] = df['volume'].pct_change() * 100
                    
                    # 计算成交量强度指标（当日成交量除以N日平均成交量）
                    df['VOL_RATIO'] = df['volume'] / df['VMA20'] * 100
                    
                    # 净额比（amount/volume，用于反映交易均价高低）
                    df['PRICE_PER_VOL'] = (df['amount'] / df['volume']).fillna(0)
                    
                    # 保持接口一致性
                    volume_result = df[['VMA20', 'VOL_CHANGE', 'VOL_RATIO', 'PRICE_PER_VOL']].copy()
                    
                except Exception as e:
                    logger.error(f"计算成交量指标时发生错误: {str(e)}")
                    return {"error": f"计算过程中出错: {str(e)}"}
            
            # 合并结果
            result_df = pd.concat([df[['open', 'high', 'low', 'close', 'volume', 'amount']], volume_result], axis=1)
            result_df = result_df.fillna(0)  # 保证无NaN，防止json序列化报错
            # 准备返回数据
            result = {
                "stock_code": stock_code,
                "indicator": "volume",
                "params": {
                    "window": window,
                    "freq": freq
                },
                "data": result_df.reset_index().to_dict('records')
            }
            
            return result
        except ValidationException as e:
            return {"error": str(e)}
        except Exception as e:
            logger.error(f"获取成交量分析失败: {str(e)}")
            return {"error": f"获取成交量分析时发生错误: {str(e)}"}
            
    def _calculate_volume_analysis_manually(
        self,
        df: pd.DataFrame,
        window: int = 20
    ) -> pd.DataFrame:
        """手动计算成交量分析指标
        
        Args:
            df: 包含行情数据的DataFrame
            window: 计算窗口
            
        Returns:
            DataFrame: 包含成交量分析指标的DataFrame
        """
        try:
            # 检查输入数据
            required_columns = ['volume', 'amount']
            if any(col not in df.columns for col in required_columns) or len(df) < window:
                logger.error("手动计算成交量分析: 输入数据不足或缺少必要列")
                return pd.DataFrame()
            
            # 创建结果DataFrame
            result = pd.DataFrame(index=df.index)
            
            # 1. 计算成交量移动平均线(volume MA)
            result['VMA20'] = df['volume'].rolling(window=window).mean()
            
            # 2. 计算相对前一天的成交量变化比例
            result['VOL_CHANGE'] = df['volume'].pct_change() * 100
            
            # 3. 计算成交量强度指标（当日成交量除以N日平均成交量）
            result['VOL_RATIO'] = (df['volume'] / result['VMA20']) * 100
            
            # 4. 净额比（amount/volume，用于反映交易均价高低）
            # 防止除以0导致的infinity或NaN
            valid_indices = df['volume'] > 0
            result['PRICE_PER_VOL'] = pd.Series(0, index=df.index)
            result.loc[valid_indices, 'PRICE_PER_VOL'] = df.loc[valid_indices, 'amount'] / df.loc[valid_indices, 'volume']
            
            # 5. 放量标志：当日成交量大于N日均量的1.5倍
            result['VOLUME_INCREASE'] = result['VOL_RATIO'] > 150
            
            # 6. 缩量标志：当日成交量小于N日均量的0.7倍
            result['VOLUME_DECREASE'] = result['VOL_RATIO'] < 70
            
            # 处理可能的极端值
            result = result.replace([np.inf, -np.inf], 0)
            
            return result
            
        except Exception as e:
            logger.error(f"手动计算成交量分析失败: {str(e)}")
            return pd.DataFrame()  # 返回空DataFrame
    def _handle_nan_values(self, values: List[float]) -> List[float]:
        """处理NaN值，转换为0.0"""
        return [0.0 if pd.isna(v) else float(v) for v in values]