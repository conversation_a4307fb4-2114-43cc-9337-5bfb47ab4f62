"""
缓存预热服务

在交易日休盘期间预热股票数据缓存，提高扫描性能
"""

from app.core import logging
from app.core.scheduler import ScheduledTask
from app.core.config import settings
from app.services.scan.session_manager import SessionManager
from app.services.scan.memory_scanner import MemoryScanner

logger = logging.getLogger(__name__)


class CacheWarmupService:
    """缓存预热服务"""

    def __init__(self):
        self.session_manager = SessionManager()
        self.scanner = MemoryScanner(self.session_manager)

    @ScheduledTask(cron=settings.CACHE_WARMUP_CRON, task_id="cache_warmup_task")
    async def cache_warmup_task(self, *args):
        """定时缓存预热任务
        
        在配置的cron时间（默认为交易日下午3点）执行缓存预热
        """
        try:
            # 检查预热功能是否启用
            if not settings.CACHE_WARMUP_ENABLED:
                logger.info("缓存预热功能已禁用，跳过预热任务")
                return
                
            logger.info("开始执行定时缓存预热任务...")
            
            # 启动缓存预热
            result = await self.scanner.start_cache_warmup()
            logger.info(f"定时缓存预热任务启动: {result}")
            
        except Exception as e:
            logger.error(f"定时缓存预热任务失败: {str(e)}")
            raise


# 创建服务实例以触发装饰器注册
cache_warmup_service = CacheWarmupService()