"""
自选股服务模块

提供自选股相关的业务逻辑功能，如添加、删除、获取自选股等。
这是同步版本的watchlist服务，配合同步的SQLAlchemy模型使用。
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, func
from sqlalchemy.exc import IntegrityError

from app.models.watchlist import WatchlistItem
from app.models.stock import StockInfo
from app.services.realtime_data_service import get_realtime_service

class WatchlistService:
    """自选股服务"""
    
    @staticmethod
    def add_stock_to_watchlist(
        db: Session, 
        user_id: int, 
        stock_code: str,
        sort_order: int = 0
    ) -> WatchlistItem:
        """
        添加股票到自选股列表
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            stock_code: 股票代码
            sort_order: 排序顺序
            
        Returns:
            WatchlistItem: 添加的自选股项目
            
        Raises:
            ValueError: 无效的股票代码或用户ID
            IntegrityError: 股票已在自选列表中
        """
        # 可以在这里添加股票代码验证逻辑
        
        try:            # 创建自选股项目
            watchlist_item = WatchlistItem(
                user_id=user_id,
                stock_code=stock_code,
                sort_order=sort_order
            )
            db.add(watchlist_item)
            db.commit()
            db.refresh(watchlist_item)
            return watchlist_item
        except IntegrityError:
            db.rollback()
            raise ValueError(f"股票 {stock_code} 已在自选列表中")
    
    @staticmethod
    async def get_watchlist_items(db: Session, user_id: int, include_stock_info: bool = True) -> List[Dict[str, Any]]:
        """
        获取用户自选股列表
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            include_stock_info: 是否包含股票详细信息
            
        Returns:
            List[Dict[str, Any]]: 自选股项目列表，包含股票详细信息
        """
        # 获取自选股项目
        watchlist_items = db.query(WatchlistItem).filter(
            WatchlistItem.user_id == user_id
        ).order_by(
            WatchlistItem.sort_order.asc(),
            WatchlistItem.added_at.desc()
        ).all()
        
        if not watchlist_items:
            return []
        
        if not include_stock_info:
            # 如果不需要股票信息，直接返回
            return [
                {
                    "id": item.id,
                    "user_id": item.user_id,
                    "stock_code": item.stock_code,
                    "added_at": item.added_at.isoformat() if item.added_at else None,
                    "sort_order": item.sort_order,
                    "stock_info": None
                }
                for item in watchlist_items
            ]
        
        # 获取所有股票代码
        stock_codes = [item.stock_code for item in watchlist_items]
        
        # 批量查询股票信息
        stock_infos = db.query(StockInfo).filter(
            StockInfo.code.in_(stock_codes)
        ).all()
        
        # 创建股票信息映射
        stock_info_map = {stock.code: stock for stock in stock_infos}
        
        # 获取实时行情数据
        realtime_service = get_realtime_service()
        try:
            realtime_quotes = await realtime_service.get_realtime_quotes(stock_codes)
        except Exception as e:
            # 如果获取实时数据失败，记录错误并使用默认值
            from app.core.logging import logging
            logger = logging.getLogger(__name__)
            logger.error(f"获取实时数据失败: {str(e)}")
            realtime_quotes = {}
        
        # 构建返回数据
        result = []
        for item in watchlist_items:
            stock_info = stock_info_map.get(item.stock_code)
            realtime_quote = realtime_quotes.get(item.stock_code, {})
            
            # 构建股票信息字典
            stock_info_dict = None
            if stock_info:
                stock_info_dict = {
                    "code": stock_info.code,
                    "name": stock_info.name,
                    "exchange": stock_info.exchange,
                    "full_code": stock_info.full_code,
                    "industry": stock_info.industry,
                    "sector": stock_info.sector,
                    "listing_date": stock_info.listing_date.isoformat() if stock_info.listing_date else None,
                    # 使用实时数据或默认值
                    "price": realtime_quote.get('price', 0.0),
                    "change_percent": realtime_quote.get('change_percent', 0.0),
                    "volume": realtime_quote.get('volume', 0),
                }
            
            item_dict = {
                "id": item.id,
                "user_id": item.user_id,
                "stock_code": item.stock_code,
                "added_at": item.added_at.isoformat() if item.added_at else None,
                "sort_order": item.sort_order,
                "stock_info": stock_info_dict
            }
            result.append(item_dict)
        
        return result
    
    @staticmethod
    def remove_stock_from_watchlist(db: Session, user_id: int, stock_code: str) -> bool:
        """
        从自选股列表移除股票
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            stock_code: 股票代码
            
        Returns:
            bool: 是否成功删除
        """
        item = db.query(WatchlistItem).filter(
            and_(
                WatchlistItem.user_id == user_id,
                WatchlistItem.stock_code == stock_code
            )
        ).first()
        
        if item:
            db.delete(item)
            db.commit()
            return True
        return False
    
    @staticmethod
    def update_watchlist_item(
        db: Session, 
        user_id: int, 
        stock_code: str, 
        sort_order: Optional[int] = None
    ) -> Optional[WatchlistItem]:
        """
        更新自选股项目信息
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            stock_code: 股票代码
            sort_order: 排序顺序
            
        Returns:
            Optional[WatchlistItem]: 更新后的自选股项目，如果未找到则返回None
        """
        item = db.query(WatchlistItem).filter(
            and_(
                WatchlistItem.user_id == user_id,
                WatchlistItem.stock_code == stock_code
            )
        ).first()
        
        if item and sort_order is not None:
            item.sort_order = sort_order
            db.commit()
            db.refresh(item)
            return item
        return None
    
    @staticmethod
    def bulk_update_watchlist(
        db: Session, 
        user_id: int, 
        stock_codes: List[str]
    ) -> List[WatchlistItem]:
        """
        批量更新自选股列表
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            stock_codes: 股票代码列表，按顺序排列
            
        Returns:
            List[WatchlistItem]: 更新后的自选股项目列表
        """
        # 获取现有自选股
        existing_items = db.query(WatchlistItem).filter(
            WatchlistItem.user_id == user_id
        ).all()
        existing_codes = {item.stock_code: item for item in existing_items}
        
        # 处理新的排序和添加新的股票
        result_items = []
        for idx, code in enumerate(stock_codes):
            if code in existing_codes:
                # 更新排序
                item = existing_codes[code]
                item.sort_order = idx
                result_items.append(item)
                del existing_codes[code]
            else:
                # 添加新股票
                try:
                    new_item = WatchlistItem(
                        user_id=user_id,
                        stock_code=code,
                        sort_order=idx
                    )
                    db.add(new_item)
                    result_items.append(new_item)
                except Exception:
                    # 跳过添加失败的股票
                    pass
        
        # 删除不在新列表中的股票
        for item in existing_codes.values():
            db.delete(item)
        
        db.commit()
        
        # 刷新所有项目以获取最新状态
        for item in result_items:
            db.refresh(item)
            
        return result_items
