"""
K线周期统计和分析服务

提供日K/周K/月K数据转换和统计分析功能
"""
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta, date
import pandas as pd
import numpy as np
from app.core import logging
from app.services.storage.stock_storage import StockStorageService
from app.services.indicators.indicator_service import IndicatorService
from app.core.exceptions import ValidationException, BaseAppException  # 使用已有的异常类
from app.utils.data_fetcher import StockDataFetcher
from app.utils.decorators import smart_data_cache
from app.core.data.models import DataType, CacheStrategy

logger = logging.getLogger(__name__)

def convert_numpy_types(data: Any) -> Any:
    """Recursively convert numpy types in a data structure to native Python types."""
    if isinstance(data, dict):
        return {k: convert_numpy_types(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [convert_numpy_types(i) for i in data]
    elif isinstance(data, np.integer):
        return int(data)
    elif isinstance(data, np.floating):
        return float(data)
    elif isinstance(data, np.bool_):
        return bool(data)
    elif isinstance(data, pd.Timestamp):
        return data.isoformat()
    # Add other numpy types if needed, e.g. np.datetime64, np.timedelta64
    return data

class KlineAnalysisService:
    """K线周期统计和分析服务"""
    
    def __init__(self, storage_service: StockStorageService):
        """初始化K线分析服务
        
        Args:
            storage_service: 股票数据存储服务
        """
        self.storage = storage_service
        self.indicator_service = IndicatorService(storage_service)
        # 初始化数据获取工具
        self.data_fetcher = StockDataFetcher(storage_service)
    
    @smart_data_cache(
        data_type=DataType.STOCK_DAILY,
        cache_strategy=CacheStrategy.CACHE_FIRST,
        cache_ttl=1800  # 30分钟缓存
    )
    async def get_kline_data(
        self,
        stock_code: str,
        freq: str = 'D',
        start_date: Optional[Union[str, datetime]] = None,
        end_date: Optional[Union[str, datetime]] = None,
        with_indicators: bool = False,
        indicators: Optional[List[str]] = None  # 修复类型注解
    ) -> Dict[str, Any]:
        """获取指定周期的K线数据
        
        Args:
            stock_code: 股票代码
            freq: 周期，'D'日线，'W'周线，'M'月线
            start_date: 开始日期
            end_date: 结束日期
            with_indicators: 是否包含技术指标
            indicators: 需要包含的指标列表，如['macd', 'kdj', 'rsi']
            
        Returns:
            Dict: K线数据和统计信息
        """
        try:
            # 设置默认日期（如果未提供）
            if end_date is None:
                end_date = datetime.now()
            
            if start_date is None:
                # 根据频率设置默认起始日期
                if freq == 'W':
                    # 默认获取一年的周K数据
                    start_date = (end_date - timedelta(days=365)) if isinstance(end_date, datetime) else datetime.now() - timedelta(days=365)
                elif freq == 'M':
                    # 默认获取两年的月K数据
                    start_date = (end_date - timedelta(days=730)) if isinstance(end_date, datetime) else datetime.now() - timedelta(days=730)
                else:
                    # 默认获取100天的日K数据
                    start_date = (end_date - timedelta(days=100)) if isinstance(end_date, datetime) else datetime.now() - timedelta(days=100)
            
            # 获取原始数据（默认为日K）（缓存由装饰器自动处理）
            df = await self._get_raw_stock_data(stock_code, start_date, end_date)
            
            # 根据需要转换周期
            if freq in ['W', 'M']:
                df = self.data_fetcher.resample_kline(df, freq)
            
            # 计算基础统计信息
            stats = await self._calculate_stats(df)
            
            # 如果需要，添加技术指标
            indicators_data = {}
            if with_indicators and indicators:
                indicators_data = await self._add_indicators(
                    stock_code, df, indicators, start_date, end_date, freq
                )
                indicators_data = convert_numpy_types(indicators_data) # Convert numpy types in indicators
            
            # 准备返回数据
            # Ensure DataFrame dtypes are native Python types before to_dict
            # For simplicity, convert after to_dict, but for performance, consider astype before.
            kline_data_raw = df.reset_index().to_dict('records')
            kline_data = convert_numpy_types(kline_data_raw)
            
            result = {
                "stock_code": stock_code,
                "freq": freq,
                "start_date": kline_data[0]['date'] if kline_data and 'date' in kline_data[0] else (start_date.isoformat() if isinstance(start_date, (date, datetime)) else start_date),
                "end_date": kline_data[-1]['date'] if kline_data and 'date' in kline_data[-1] else (end_date.isoformat() if isinstance(end_date, (date, datetime)) else end_date),
                "data_count": len(kline_data),
                "statistics": stats, # stats are already converted in _calculate_stats
                "kline_data": kline_data
            }
            
            if indicators_data:
                result["indicators"] = indicators_data
            
            return result
        except Exception as e:
            logger.error(f"获取K线数据失败: {str(e)}")
            raise ValidationException(f"获取{freq}线数据失败: {str(e)}")
    
    async def _get_raw_stock_data(
        self,
        stock_code: str,
        start_date: Union[str, datetime],
        end_date: Union[str, datetime]
    ) -> pd.DataFrame:
        """获取原始股票数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
            Returns:
            DataFrame: 股票日线数据
        """
        # 解析股票代码，去除市场后缀 (e.g., "000001.SZ" -> "000001")
        code_only = stock_code.split('.')[0]
        
        try:
            # 使用增强的数据获取工具，支持从数据提供者获取数据
            df = await self.data_fetcher.fetch_stock_data(
                code_only, 
                start_date, 
                end_date,
                use_provider_fallback=True  # 允许在数据库查询失败时使用数据提供者
            )
            
            if df.empty:
                raise ValidationException(f"未找到股票 {stock_code} 在指定时间范围内的数据")
            
            return df
            
        except ValidationException:
            raise  # 直接将已处理的异常传递出去
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 原始数据时出错: {str(e)}")
            raise ValidationException(f"处理股票数据失败: {str(e)}")
        
    async def _calculate_stats(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算K线数据的统计信息
        
        Args:
            df: K线数据DataFrame
            
        Returns:
            Dict: 统计信息
        """
        if df.empty:
            return {}
        
        # Ensure date column is string for JSON serialization if it's the index
        df_copy = df.copy()
        if isinstance(df_copy.index, pd.DatetimeIndex):
            df_copy.index = df_copy.index.strftime('%Y-%m-%d')

        stats = {
            "avg_volume": df_copy['volume'].mean(),
            "max_volume": df_copy['volume'].max(),
            "min_volume": df_copy['volume'].min(),
            "avg_price": df_copy['close'].mean(),
            "max_price": df_copy['high'].max(),
            "min_price": df_copy['low'].min(),
            "latest_price": df_copy['close'].iloc[-1] if not df_copy.empty else None,
            "price_range": (df_copy['high'].max() - df_copy['low'].min()) / df_copy['low'].min() * 100 if df_copy['low'].min() > 0 else 0,
        }
        
        # 添加涨跌统计
        if 'change_pct' in df_copy.columns:
            stats.update({
                "up_days": (df_copy['change_pct'] > 0).sum(),
                "down_days": (df_copy['change_pct'] < 0).sum(),
                "flat_days": (df_copy['change_pct'] == 0).sum(),
                "max_up_pct": df_copy['change_pct'].max(),
                "max_down_pct": df_copy['change_pct'].min(),
            })
        
        # 添加成交量统计
        if 'volume' in df_copy.columns:
            stats["volume_std"] = df_copy['volume'].std()
            
            df_copy['vol_ma5'] = df_copy['volume'].rolling(window=5, min_periods=1).mean()
            df_copy['vol_ma10'] = df_copy['volume'].rolling(window=10, min_periods=1).mean()
            df_copy['vol_ma20'] = df_copy['volume'].rolling(window=20, min_periods=1).mean()
            
            if not df_copy.empty:
                stats["vol_ma5"] = df_copy['vol_ma5'].iloc[-1] if not df_copy.empty and len(df_copy['vol_ma5']) > 0 else None
                stats["vol_ma10"] = df_copy['vol_ma10'].iloc[-1] if not df_copy.empty and len(df_copy['vol_ma10']) > 0 else None
                stats["vol_ma20"] = df_copy['vol_ma20'].iloc[-1] if not df_copy.empty and len(df_copy['vol_ma20']) > 0 else None
                if stats.get("vol_ma5") and stats["vol_ma5"] > 0 : # Check if vol_ma5 is not None and greater than 0
                    stats["vol_ratio"] = df_copy['volume'].iloc[-1] / stats["vol_ma5"]
                else:
                    stats["vol_ratio"] = 0
        
        return convert_numpy_types(stats) # Convert all numpy types at the end
    
    async def _add_indicators(
        self,
        stock_code: str,
        df: pd.DataFrame,
        indicators: List[str],
        start_date: Optional[Union[str, datetime]],
        end_date: Optional[Union[str, datetime]],
        freq: str
    ) -> Dict[str, Any]:
        """添加技术指标数据
        
        Args:
            stock_code: 股票代码
            df: K线数据DataFrame
            indicators: 需要添加的指标列表
            start_date: 开始日期
            end_date: 结束日期
            freq: 数据周期
            
        Returns:
            Dict: 技术指标数据
        """
        result = {}
        
        # 确保日期不为None
        actual_start_date = start_date if start_date is not None else datetime.now() - timedelta(days=100)
        actual_end_date = end_date if end_date is not None else datetime.now()
        
        for indicator in indicators:
            try:
                if indicator.lower() == 'macd':
                    # 计算MACD
                    macd_data = await self.indicator_service.calculate_macd(
                        stock_code, actual_start_date, actual_end_date, freq=freq
                    )
                    result['macd'] = macd_data
                elif indicator.lower() == 'kdj':
                    # 计算KDJ
                    kdj_data = await self.indicator_service.calculate_kdj(
                        stock_code, actual_start_date, actual_end_date, freq=freq
                    )
                    result['kdj'] = kdj_data
                elif indicator.lower() == 'rsi':
                    # 计算RSI
                    rsi_data = await self.indicator_service.calculate_rsi(
                        stock_code, actual_start_date, actual_end_date, freq=freq
                    )
                    result['rsi'] = rsi_data
                elif indicator.lower() == 'arbr':
                    # 计算AR/BR
                    arbr_data = await self.indicator_service.calculate_arbr(
                        stock_code, actual_start_date, actual_end_date, freq=freq
                    )
                    result['arbr'] = arbr_data
                elif indicator.lower() == 'volume':
                    # 获取成交量分析
                    volume_data = await self.indicator_service.get_volume_analysis(
                        stock_code, actual_start_date, actual_end_date, freq=freq
                    )
                    result['volume'] = volume_data
            except Exception as e:
                logger.error(f"计算指标 {indicator} 失败: {str(e)}")
                # 继续计算其他指标
                continue
                
        return result
