"""
指数K线周期统计和分析服务

提供指数日K/周K/月K数据转换和统计分析功能，模仿股票K线分析服务
"""
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta, date
import pandas as pd
import numpy as np
from app.core import logging
from app.services.storage.index_storage import IndexStorageService
from app.services.indicators.index_indicator_service import IndexIndicatorService
from app.core.exceptions import ValidationException, BaseAppException
from app.utils.decorators import smart_data_cache
from app.core.data.models import DataType, CacheStrategy
from app.utils.index_data_fetcher import IndexDataFetcher

logger = logging.getLogger(__name__)

def convert_numpy_types(data: Any) -> Any:
    """Recursively convert numpy types in a data structure to native Python types."""
    if isinstance(data, dict):
        return {k: convert_numpy_types(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [convert_numpy_types(i) for i in data]
    elif isinstance(data, np.integer):
        return int(data)
    elif isinstance(data, np.floating):
        return float(data)
    elif isinstance(data, np.bool_):
        return bool(data)
    elif isinstance(data, pd.Timestamp):
        return data.isoformat()
    return data

class IndexKlineAnalysisService:
    """指数K线周期统计和分析服务"""
    
    def __init__(self, storage_service: IndexStorageService):
        """初始化指数K线分析服务
        
        Args:
            storage_service: 指数数据存储服务
        """
        self.storage = storage_service
        self.indicator_service = IndexIndicatorService(storage_service)
        # 初始化指数数据获取工具
        self.data_fetcher = IndexDataFetcher(storage_service)
    
    async def get_kline_data(
        self,
        index_code: str,
        freq: str = "D",
        start_date: str = None,
        end_date: str = None,
        with_indicators: bool = False,
        indicators: List[str] = None
    ) -> Dict[str, Any]:
        """
        获取指数K线数据，可选包含技术指标
        
        Args:
            index_code: 指数代码
            freq: 数据周期 D/W/M
            start_date: 开始日期
            end_date: 结束日期
            with_indicators: 是否包含技术指标
            indicators: 技术指标列表
            
        Returns:
            Dict[str, Any]: 包含K线数据和指标的字典
        """
        try:
            logger.info(f"获取指数K线数据: {index_code}, 周期: {freq}")
            
            # 验证指数代码
            index_info = await self.storage.get_index_info(index_code)
            if not index_info:
                raise ValidationException(f"指数 {index_code} 不存在")
            
            # 设置默认日期（如果未提供）
            if end_date is None:
                end_date = datetime.now()
            else:
                end_date = datetime.fromisoformat(end_date)
            
            if start_date is None:
                # 根据频率设置默认起始日期
                if freq == 'W':
                    # 默认获取一年的周K数据
                    start_date = end_date - timedelta(days=365)
                elif freq == 'M':
                    # 默认获取两年的月K数据
                    start_date = end_date - timedelta(days=730)
                else:
                    # 默认获取100天的日K数据
                    start_date = end_date - timedelta(days=100)
            else:
                start_date = datetime.fromisoformat(start_date)
            
            # 使用增强的数据获取工具，支持从数据提供者获取数据
            df = await self.data_fetcher.fetch_index_data(
                index_code, 
                start_date, 
                end_date,
                use_provider_fallback=True  # 允许在数据库查询失败时使用数据提供者
            )
            
            if df.empty:
                logger.warning(f"指数 {index_code} 无数据")
                return {
                    "index_info": index_info,
                    "kline_data": [],
                    "indicators": {}
                }
            
            # 根据频率转换数据
            if freq in ['W', 'M']:
                df = self.data_fetcher.resample_kline(df, freq)
            
            # 转换为字典列表
            kline_data = []
            for _, row in df.iterrows():
                kline_data.append({
                    'trade_date': row['trade_date'].date() if hasattr(row['trade_date'], 'date') else row['trade_date'],
                    'open': float(row['open']),
                    'high': float(row['high']),
                    'low': float(row['low']),
                    'close': float(row['close']),
                    'volume': int(row['volume']),
                    'amount': int(row.get('amount', 0)),
                    'prev_close': float(row.get('prev_close', 0)),
                    'change_pct': float(row.get('change_pct', 0))
                })
            
            result = {
                "index_info": index_info,
                "kline_data": kline_data,
                "indicators": {}
            }
            
            # 如果需要技术指标
            if with_indicators and indicators:
                for indicator in indicators:
                    try:
                        indicator_data = await self._get_indicator_data(
                            index_code, indicator, start_date.strftime("%Y-%m-%d"), end_date.strftime("%Y-%m-%d"), freq
                        )
                        if indicator_data:
                            result["indicators"][indicator] = indicator_data
                    except Exception as e:
                        logger.warning(f"获取指数指标 {indicator} 失败: {e}")
                        result["indicators"][indicator] = None
            
            return convert_numpy_types(result)
            
        except Exception as e:
            logger.error(f"获取指数K线数据失败: {e}", exc_info=True)
            raise BaseAppException(f"获取指数K线数据失败: {str(e)}")
    
    async def _get_indicator_data(
        self,
        index_code: str,
        indicator: str,
        start_date: str = None,
        end_date: str = None,
        freq: str = "D"
    ) -> Optional[Dict[str, Any]]:
        """获取技术指标数据"""
        try:
            if indicator.lower() == 'macd':
                return await self.indicator_service.calculate_macd(
                    index_code, start_date, end_date, freq=freq
                )
            elif indicator.lower() == 'kdj':
                return await self.indicator_service.calculate_kdj(
                    index_code, start_date, end_date, freq=freq
                )
            elif indicator.lower() == 'rsi':
                return await self.indicator_service.calculate_rsi(
                    index_code, start_date, end_date, freq=freq
                )
            elif indicator.lower() == 'volume':
                return await self.indicator_service.calculate_volume_analysis(
                    index_code, start_date, end_date, freq=freq
                )
            else:
                logger.warning(f"不支持的指标: {indicator}")
                return None
        except Exception as e:
            logger.error(f"计算指标 {indicator} 失败: {e}")
            return None