"""
用户服务模块

提供用户相关的服务功能，如创建用户、验证用户等。
"""

from typing import Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from sqlalchemy.sql import func
from fastapi import Header, HTTPException, Depends

from app.models.user import User
from app.utils.auth import get_password_hash, verify_password
from app.utils.jwt_auth import generate_access_token, verify_user_token
from app.utils.decorators import smart_data_cache
from app.core.data.models import DataType, CacheStrategy
from app.core.database import db_session


class UserService:
    """用户服务"""
    
    @staticmethod
    async def create_user(username: str, password: str, email: Optional[str] = None, 
                         is_admin: bool = False, is_active: bool = True) -> User:
        """
        创建新用户
        
        Args:
            username: 用户名
            password: 密码
            email: 邮箱
            is_admin: 是否为管理员
            is_active: 是否激活
            
        Returns:
            User: 创建的用户对象
        """
        hashed_password = get_password_hash(password)
        
        async with db_session() as db:
            db_user = User(
                username=username,
                password_hash=hashed_password,
                email=email,
                is_admin=is_admin,
                is_active=is_active
            )
            db.add(db_user)
            await db.commit()
            await db.refresh(db_user)
            return db_user
    
    @staticmethod
    async def authenticate_user(username: str, password: str) -> Optional[User]:
        """
        验证用户
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            Optional[User]: 验证成功返回用户对象，失败返回None
        """
        async with db_session() as db:
            stmt = select(User).where(User.username == username)
            result = await db.execute(stmt)
            user = result.scalar_one_or_none()
            
            if not user or not verify_password(password, user.password_hash):
                return None
            return user
    
    @staticmethod
    async def login_user(username: str, password: str) -> Optional[Dict[str, Any]]:
        """用户登录，返回token和用户信息"""
        user = await UserService.authenticate_user(username, password)
        if not user:
            return None
            
        # 更新最后登录时间
        async with db_session() as db:
            stmt = update(User).where(User.id == user.id).values(last_login=func.now())
            await db.execute(stmt)
            await db.commit()
            
            # 重新获取用户信息以确保最新状态
            stmt = select(User).where(User.id == user.id)
            result = await db.execute(stmt)
            user = result.scalar_one()
        
        # 生成JWT token
        access_token = generate_access_token(user.id, user.username, user.is_admin, user.token_version)
        
        return {
            'access_token': access_token,
            'token_type': 'bearer',
            'expires_in': 24 * 3600,  # 24小时
            'user': {
                'id': user.id,
                'username': user.username,
                'is_admin': user.is_admin,
                'email': user.email
            }
        }
    
    @staticmethod
    async def get_current_user_from_token(token: str) -> Optional[User]:
        """从token获取当前用户"""
        return await verify_user_token(token)
    
    @staticmethod
    async def get_user_by_username(username: str) -> Optional[User]:
        """
        通过用户名获取用户
        
        Args:
            username: 用户名
            
        Returns:
            Optional[User]: 找到返回用户对象，未找到返回None
        """
        async with db_session() as db:
            stmt = select(User).where(User.username == username)
            result = await db.execute(stmt)
            return result.scalar_one_or_none()
    
    @staticmethod
    async def get_user_by_id(user_id: int) -> Optional[User]:
        """
        通过ID获取用户
        
        Args:
            user_id: 用户ID
            
        Returns:
            Optional[User]: 找到返回用户对象，未找到返回None
        """
        async with db_session() as db:
            stmt = select(User).where(User.id == user_id)
            result = await db.execute(stmt)
            return result.scalar_one_or_none()


# 以下为旧的session-based认证方法，保留用于向后兼容
# 假设的token到userId的映射，实际应用中应该从数据库或其他认证服务获取
# 这里为了演示，我们硬编码一些token和userId的对应关系
# 在实际部署时，这些token应该是动态生成和管理的
MOCK_TOKEN_USER_MAP = {
    "test_token_user1": "user_id_1",
    "test_token_admin": "admin_id_0",
}

@smart_data_cache(
    data_type=DataType.STOCK_INFO,  # 使用通用数据类型
    cache_strategy=CacheStrategy.CACHE_FIRST,
    cache_ttl=3600  # 1小时缓存
)
async def get_user_id_from_token(token: str) -> Optional[str]:
    """
    根据token从缓存或模拟数据中获取用户ID（缓存由装饰器自动处理）
    """
    # 从模拟数据中查找 (实际应用中可能是查询数据库)
    user_id = MOCK_TOKEN_USER_MAP.get(token)
    return user_id

async def get_current_user_id(token: Optional[str] = Header(None, alias="Authorization")) -> Optional[str]:
    """
    从请求头中获取Token并返回用户ID。
    如果token以 "Bearer " 开头，则去除此前缀。
    """
    if not token:
        return None

    # 兼容 "Bearer <token>" 格式
    if token.startswith("Bearer "):
        token = token.split("Bearer ")[1]

    user_id = await get_user_id_from_token(token)
    if not user_id:
        # 实际应用中，如果token无效且接口强制要求认证，应该在此处抛出HTTPException
        # 但为了逐步迁移，这里暂时返回None，由接口自行决定如何处理
        return None
    return user_id

async def require_user(user_id: Optional[str] = Depends(get_current_user_id)) -> str:
    """
    FastAPI依赖项，用于确保用户已认证。
    如果用户未认证（即user_id为None），则抛出401 HTTPException。
    """
    if user_id is None:
        raise HTTPException(status_code=401, detail="Not authenticated")
    return user_id
