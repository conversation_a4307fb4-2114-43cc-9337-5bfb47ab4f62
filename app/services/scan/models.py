"""
扫描服务数据模型
"""
from pydantic import BaseModel, Field
from enum import Enum
from datetime import datetime
from typing import List, Dict, Optional, Any
import uuid


class ScanStatus(str, Enum):
    """扫描状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class SignalType(str, Enum):
    """信号类型枚举"""
    BUY = "buy"
    SELL = "sell"
    STOP_LOSS = "stop_loss"


class ScanProgress(BaseModel):
    """扫描进度模型"""
    total: int = Field(0, description="总数")
    current: int = Field(0, description="当前进度")
    percentage: float = Field(0.0, description="完成百分比")
    message: Optional[str] = Field(None, description="进度消息")


class StockIndicatorData(BaseModel):
    """股票指标数据"""
    kdj_k: float = Field(..., description="KDJ-K值")
    kdj_d: Optional[float] = Field(None, description="KDJ-D值") 
    kdj_j: Optional[float] = Field(None, description="KDJ-J值")
    volume_pressure: float = Field(..., description="成交量压力指标")
    bollinger_upper: float = Field(..., description="布林带上轨")
    bollinger_middle: float = Field(..., description="布林带中轨")
    bollinger_lower: float = Field(..., description="布林带下轨")
    dif: float = Field(0.0, description="DIF差离值")
    dea: float = Field(0.0, description="DEA信号线")
    macd: float = Field(0.0, description="MACD柱状图")
    rsi: float = Field(0.0, description="RSI值")
    arbr_ar: float = Field(0.0, description="ARBR-AR值")
    arbr_br: float = Field(0.0, description="ARBR-BR值")
    
    # 新增字段用于增强显示
    prev_kdj_k: float = Field(0.0, description="前一日KDJ-K值")
    prev_kdj_d: Optional[float] = Field(None, description="前一日KDJ-D值")
    prev_macd: float = Field(0.0, description="前一日DIF差离值")
    prev_macd_signal: float = Field(0.0, description="前一日DEA信号线值")
    volume_pressure_avg: float = Field(..., description="成交量压力20日平均")
    close_price: float = Field(..., description="收盘价")
    bollinger_distance_pct: float = Field(0.0, description="收盘价距离下轨百分比")
    
    def is_kdj_golden_cross(self) -> bool:
        """判断是否为KDJ金叉"""
        if (self.prev_kdj_k is None or self.prev_kdj_d is None or 
            self.kdj_k is None or self.kdj_d is None):
            return False
        return self.prev_kdj_k < self.prev_kdj_d and self.kdj_k > self.kdj_d
        
    def is_kdj_death_cross(self) -> bool:
        """判断是否为KDJ死叉"""
        if (self.prev_kdj_k is None or self.prev_kdj_d is None or 
            self.kdj_k is None or self.kdj_d is None):
            return False
        return self.prev_kdj_k > self.prev_kdj_d and self.kdj_k < self.kdj_d
        
    def is_volume_breakout(self) -> bool:
        """判断是否为成交量突破"""
        return self.volume_pressure > self.volume_pressure_avg

    def is_macd_golden_cross(self) -> bool:
        """判断是否为MACD金叉（DIF上穿DEA）"""
        if (self.prev_macd is None or self.prev_macd_signal is None or 
            self.dif is None or self.dea is None):
            return False
        return self.prev_macd < self.prev_macd_signal and self.dif > self.dea
        
    def is_macd_death_cross(self) -> bool:
        """判断是否为MACD死叉（DIF下穿DEA）"""
        if (self.prev_macd is None or self.prev_macd_signal is None or 
            self.dif is None or self.dea is None):
            return False
        return self.prev_macd > self.prev_macd_signal and self.dif < self.dea


class ScanResult(BaseModel):
    """扫描结果"""
    stock_code: str = Field(..., description="股票代码")
    stock_name: str = Field(..., description="股票名称")
    signals: List[SignalType] = Field(..., description="信号列表")
    indicator_data: StockIndicatorData = Field(..., description="指标数据")
    price: float = Field(..., description="当前价格")
    change_percent: float = Field(..., description="涨跌幅")
    scan_time: datetime = Field(default_factory=datetime.now, description="扫描时间")
    period: str = Field("d", description="数据周期：d(日线)/w(周线)/m(月线)")


class ScanTask(BaseModel):
    """扫描任务"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="任务ID")
    session_id: str = Field(..., description="会话ID")
    indicators: List[str] = Field(..., description="扫描指标列表")
    stock_codes: List[str] = Field(default_factory=list, description="股票代码列表")
    parameters: Optional[Any] = Field(None, description="指标参数配置")
    status: ScanStatus = Field(ScanStatus.PENDING, description="任务状态")
    progress: ScanProgress = Field(default_factory=lambda: ScanProgress(), description="进度信息")
    results: List[ScanResult] = Field(default_factory=list, description="扫描结果")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    error_message: Optional[str] = Field(None, description="错误信息")
    # 多周期扫描相关字段
    scan_mode: str = Field("traditional", description="扫描模式：traditional/multi_period")
    scan_strategy: str = Field("parallel", description="多周期策略：parallel/cascade")
    periods: List[str] = Field(["d"], description="扫描周期列表")
    adjust: str = Field("n", description="复权方式")
    # 新增：多周期参数和指标配置
    period_parameters: Optional[Any] = Field(None, description="多周期参数配置")
    period_indicators: Optional[Dict[str, List[str]]] = Field(None, description="分周期指标配置")
    # 历史回测支持
    end_date: Optional[str] = Field(None, description="数据截止日期，格式：YYYY-MM-DD，用于历史回测")
    
    def __init__(self, **data) -> None:
        if "id" not in data:
            data["id"] = str(uuid.uuid4())
        super().__init__(**data)


class UserSession(BaseModel):
    """用户会话"""
    session_id: str = Field(..., description="会话ID")
    user_id: Optional[str] = Field(None, description="用户ID")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    last_active: datetime = Field(default_factory=datetime.now, description="最后活跃时间")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="会话元数据")


# 保留原有的类型定义，以便兼容
class IndicatorType(str, Enum):
    """指标类型"""
    VOLUME_PRESSURE = "volume_pressure"  # 成交量压力指标
    KDJ = "kdj"  # KDJ指标
    MACD = "macd"  # MACD指标
    RSI = "rsi"  # RSI指标
    BOLLINGER = "bollinger"  # 布林带指标


class ScanConfig(BaseModel):
    """扫描配置（兼容旧版本）"""
    indicator_types: List[IndicatorType]  # 选中的指标类型
    thresholds: Dict[str, Dict[str, Any]]  # 指标阈值配置
    stock_codes: Optional[List[str]] = None  # 指定股票代码列表，None表示全部
    market: Optional[str] = None  # 市场类型：A股、港股等