"""
多周期扫描策略
主周期完整计算，辅助周期只计算KDJ上升通道
"""
import asyncio
from typing import List, Optional, Dict, Any
import logging

from .base import ScanStrategy
from app.services.scan.models import ScanTask, ScanResult

logger = logging.getLogger(__name__)


class MultiPeriodStrategy(ScanStrategy):
    """多周期扫描策略"""
    
    def __init__(self, primary_period: str, secondary_periods: List[str], strategy_type: str = "parallel"):
        """
        初始化多周期策略
        
        Args:
            primary_period: 主周期（完整计算）
            secondary_periods: 辅助周期列表（只计算上升通道）
            strategy_type: 策略类型 (parallel/cascade)
        """
        self.primary_period = primary_period
        self.secondary_periods = secondary_periods
        self.strategy_type = strategy_type
        self.scanner = None  # 将在execute_scan时注入
    
    async def execute_scan(self, task: ScanTask, stock_codes: List[str]) -> None:
        """
        执行多周期扫描
        
        Args:
            task: 扫描任务对象
            stock_codes: 要扫描的股票代码列表
        """
        # logger.info(f"Starting multi-period scan for {len(stock_codes)} stocks")
        # logger.info(f"Primary period: {self.primary_period}, Secondary periods: {self.secondary_periods}")
        
        # 从task中获取scanner实例
        if not hasattr(task, '_scanner_instance'):
            raise RuntimeError("Scanner instance not injected into task")
        
        self.scanner = task._scanner_instance
        
        if self.strategy_type == "cascade":
            await self._execute_cascade_scan(task, stock_codes)
        else:
            await self._execute_parallel_scan(task, stock_codes)
    
    async def _execute_parallel_scan(self, task: ScanTask, stock_codes: List[str]) -> None:
        """
        并行扫描：同时扫描所有周期
        """
        # logger.info("Executing parallel multi-period scan")
        
        # 收集所有任务
        scan_tasks = []
        
        # 为每只股票创建所有周期的扫描任务
        for stock_code in stock_codes:
            # 主周期任务（完整计算）
            primary_task = self._create_primary_period_task(stock_code, task)
            scan_tasks.append(('primary', stock_code, self.primary_period, primary_task))
            
            # 辅助周期任务（只计算上升通道）
            for period in self.secondary_periods:
                secondary_task = self._create_secondary_period_task(stock_code, period, task)
                scan_tasks.append(('secondary', stock_code, period, secondary_task))
        
        # 并发执行所有任务
        semaphore = asyncio.Semaphore(10)
        async_tasks = []
        
        for task_type, code, period, coro_task in scan_tasks:
            async_task = self._execute_with_semaphore(semaphore, task_type, code, period, coro_task)
            async_tasks.append(async_task)
        
        results = await asyncio.gather(*async_tasks, return_exceptions=True)
        
        # 处理结果 - 按股票分组，只有所有周期都符合条件的股票才被选中
        stock_results = {}
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Task failed: {result}")
                continue
            
            task_type, stock_code, period, scan_result = result
            if scan_result and scan_result.signals:
                if stock_code not in stock_results:
                    stock_results[stock_code] = {}
                stock_results[stock_code][period] = scan_result
        
        # 筛选完全符合条件的股票（所有周期都有结果）
        all_periods = [self.primary_period] + self.secondary_periods
        for stock_code, period_results in stock_results.items():
            if len(period_results) == len(all_periods):
                # 使用主周期的结果作为最终结果
                final_result = period_results[self.primary_period]
                task.results.append(final_result)
                await self._trigger_result_callback(task, final_result)
        
        logger.info(f"Parallel scan completed. Found {len(task.results)} stocks matching all criteria.")
    
    async def _execute_cascade_scan(self, task: ScanTask, stock_codes: List[str]) -> None:
        """
        层级扫描：先扫描主周期，通过的再扫描辅助周期
        """
        # logger.info("Executing cascade multi-period scan")
        
        # 第一步：扫描主周期
        semaphore = asyncio.Semaphore(10)
        primary_tasks = []
        
        for stock_code in stock_codes:
            primary_task = self._create_primary_period_task(stock_code, task)
            async_task = self._execute_with_semaphore(semaphore, 'primary', stock_code, self.primary_period, primary_task)
            primary_tasks.append(async_task)
        
        primary_results = await asyncio.gather(*primary_tasks, return_exceptions=True)
        
        # 收集通过主周期筛选的股票
        passed_stocks = []
        primary_stock_results = {}
        
        for result in primary_results:
            if isinstance(result, Exception):
                continue
            
            task_type, stock_code, period, scan_result = result
            if scan_result and scan_result.signals:
                passed_stocks.append(stock_code)
                primary_stock_results[stock_code] = scan_result
        
        # logger.info(f"Primary period scan passed {len(passed_stocks)} stocks")
        
        if not passed_stocks or not self.secondary_periods:
            # 如果没有通过主周期的股票，或者没有辅助周期，直接返回主周期结果
            for stock_code, result in primary_stock_results.items():
                task.results.append(result)
                await self._trigger_result_callback(task, result)
            return
        
        # 第二步：对通过主周期的股票扫描辅助周期
        secondary_tasks = []
        
        for stock_code in passed_stocks:
            for period in self.secondary_periods:
                secondary_task = self._create_secondary_period_task(stock_code, period, task)
                async_task = self._execute_with_semaphore(semaphore, 'secondary', stock_code, period, secondary_task)
                secondary_tasks.append(async_task)
        
        secondary_results = await asyncio.gather(*secondary_tasks, return_exceptions=True)
        
        # 处理辅助周期结果
        secondary_stock_results = {}
        for result in secondary_results:
            if isinstance(result, Exception):
                continue
            
            task_type, stock_code, period, scan_result = result
            if scan_result and scan_result.signals:
                if stock_code not in secondary_stock_results:
                    secondary_stock_results[stock_code] = {}
                secondary_stock_results[stock_code][period] = scan_result
        
        # 筛选完全符合条件的股票（所有辅助周期都通过）
        for stock_code in passed_stocks:
            secondary_results_count = len(secondary_stock_results.get(stock_code, {}))
            if secondary_results_count == len(self.secondary_periods):
                # 使用主周期的结果作为最终结果
                final_result = primary_stock_results[stock_code]
                task.results.append(final_result)
                await self._trigger_result_callback(task, final_result)
        
        # logger.info(f"Cascade scan completed. Found {len(task.results)} stocks matching all criteria.")
    
    def _create_primary_period_task(self, stock_code: str, task: ScanTask):
        """创建主周期扫描任务（完整计算）"""
        return self._analyze_primary_period_stock(stock_code, self.primary_period, task)
    
    def _create_secondary_period_task(self, stock_code: str, period: str, task: ScanTask):
        """创建辅助周期扫描任务（只计算上升通道）"""
        return self._analyze_secondary_period_stock(stock_code, period, task)
    
    async def _execute_with_semaphore(self, semaphore, task_type, stock_code, period, coro_task):
        """使用信号量控制并发执行"""
        async with semaphore:
            result = await coro_task
            return (task_type, stock_code, period, result)
    
    async def _analyze_primary_period_stock(self, stock_code: str, period: str, task: ScanTask) -> Optional[ScanResult]:
        """
        分析主周期股票（完整指标计算）
        """
        try:
            # 获取主周期的指标和参数配置
            indicators, parameters = self._get_primary_period_config(task, period)
            
            if not indicators:
                return None
            
            # 获取股票数据
            df = await self.scanner._get_stock_period_data(stock_code, period, task.adjust, task.end_date)
            
            if df is None or df.empty:
                return None
            
            # 计算完整技术指标
            df_with_indicators = self.scanner.calculator.calculate_all_indicators(df, parameters=parameters)
            
            # 应用完整筛选条件
            condition_result = self.scanner.calculator.check_indicator_conditions(df_with_indicators, indicators)
            
            if not condition_result:
                return None
            
            # 构建扫描结果
            return self.scanner._build_scan_result(stock_code, df_with_indicators, indicators, period)
            
        except Exception as e:
            logger.error(f"Error analyzing primary period {period} for stock {stock_code}: {str(e)}")
            return None
    
    async def _analyze_secondary_period_stock(self, stock_code: str, period: str, task: ScanTask) -> Optional[ScanResult]:
        """
        分析辅助周期股票（只计算KDJ上升通道）
        """
        try:
            # 获取股票数据
            df = await self.scanner._get_stock_period_data(stock_code, period, task.adjust, task.end_date)
            
            if df is None or df.empty:
                return None
            
            # 只计算KDJ指标（简化计算）
            df_with_kdj = self.scanner.calculator.calculate_kdj_only(df)
            
            # 检查KDJ上升通道
            uptrend_result = self.scanner.calculator.check_kdj_uptrend(df_with_kdj)
            
            if not uptrend_result:
                return None
            
            # 构建简化的扫描结果
            return self.scanner._build_simplified_scan_result(stock_code, df_with_kdj, period)
            
        except Exception as e:
            logger.error(f"Error analyzing secondary period {period} for stock {stock_code}: {str(e)}")
            return None
    
    def _get_primary_period_config(self, task: ScanTask, period: str) -> tuple:
        """获取主周期的指标和参数配置"""
        if task.period_indicators and period in task.period_indicators:
            indicators = task.period_indicators[period]
        else:
            indicators = task.indicators
        
        if task.period_parameters and period in task.period_parameters:
            parameters = task.period_parameters[period]
        else:
            parameters = task.parameters
        
        return indicators, parameters
    
    async def _trigger_result_callback(self, task: ScanTask, result: ScanResult) -> None:
        """触发结果回调"""
        if hasattr(self.scanner, '_trigger_result_callback'):
            await self.scanner._trigger_result_callback(task, result)
    
    def get_strategy_name(self) -> str:
        """获取策略名称"""
        return f"multi_period_{self.strategy_type}_{self.primary_period}"
    
    def get_strategy_description(self) -> str:
        """获取策略描述"""
        period_names = {
            'd': '日线',
            'w': '周线', 
            'm': '月线',
            'y': '年线'
        }
        
        primary_name = period_names.get(self.primary_period, self.primary_period)
        secondary_names = [period_names.get(p, p) for p in self.secondary_periods]
        secondary_str = '、'.join(secondary_names)
        
        strategy_desc = "并行计算" if self.strategy_type == "parallel" else "层级筛选"
        
        return f"多周期{strategy_desc}：{primary_name}完整分析 + {secondary_str}上升通道确认"