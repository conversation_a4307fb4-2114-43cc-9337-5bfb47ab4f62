"""
扫描策略抽象基类
定义所有扫描策略必须实现的接口
"""
from abc import ABC, abstractmethod
from typing import List

from app.services.scan.models import ScanTask


class ScanStrategy(ABC):
    """扫描策略抽象基类"""
    
    @abstractmethod
    async def execute_scan(self, task: ScanTask, stock_codes: List[str]) -> None:
        """
        执行扫描策略
        
        Args:
            task: 扫描任务对象
            stock_codes: 要扫描的股票代码列表
        """
        pass
    
    @abstractmethod
    def get_strategy_name(self) -> str:
        """
        获取策略名称
        
        Returns:
            策略名称
        """
        pass
    
    @abstractmethod
    def get_strategy_description(self) -> str:
        """
        获取策略描述
        
        Returns:
            策略描述
        """
        pass