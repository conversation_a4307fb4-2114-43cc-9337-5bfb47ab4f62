"""
单周期扫描策略
对单个周期进行完整的技术指标计算和筛选
"""
import asyncio
from typing import List, Optional, Dict, Any
import logging

from .base import ScanStrategy
from app.services.scan.models import ScanTask, ScanResult

logger = logging.getLogger(__name__)


class SinglePeriodStrategy(ScanStrategy):
    """单周期扫描策略"""
    
    def __init__(self, period: str = "d"):
        """
        初始化单周期策略
        
        Args:
            period: 扫描周期 (d/w/m)
        """
        self.period = period
        self.scanner = None  # 将在execute_scan时注入
    
    async def execute_scan(self, task: ScanTask, stock_codes: List[str]) -> None:
        """
        执行单周期扫描
        
        Args:
            task: 扫描任务对象
            stock_codes: 要扫描的股票代码列表
        """
        # logger.info(f"Starting single period scan for {len(stock_codes)} stocks, period: {self.period}")
        
        # 从task中获取scanner实例（通过dependency injection）
        if not hasattr(task, '_scanner_instance'):
            raise RuntimeError("Scanner instance not injected into task")
        
        self.scanner = task._scanner_instance
        
        # 并发处理所有股票
        semaphore = asyncio.Semaphore(10)  # 控制并发数
        tasks = []
        
        for code in stock_codes:
            task_coroutine = self._analyze_single_stock(semaphore, code, task)
            tasks.append(task_coroutine)
        
        # 并发执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        successful_count = 0
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Failed to analyze stock {stock_codes[i]}: {result}")
            elif result and result.signals:
                task.results.append(result)
                successful_count += 1
                # 触发结果回调
                await self._trigger_result_callback(task, result)
        
        # logger.info(f"Single period scan completed. Found {successful_count} stocks matching criteria.")
    
    async def _analyze_single_stock(self, semaphore: asyncio.Semaphore, stock_code: str, task: ScanTask) -> Optional[ScanResult]:
        """
        分析单只股票
        
        Args:
            semaphore: 并发控制信号量
            stock_code: 股票代码
            task: 扫描任务
            
        Returns:
            扫描结果
        """
        async with semaphore:
            try:
                # 获取指标和参数配置
                indicators, parameters = self._get_indicators_and_parameters(task)
                
                if not indicators:
                    return None
                
                # 获取股票数据
                df = await self.scanner._get_stock_period_data(stock_code, self.period, task.adjust, task.end_date)
                
                if df is None or df.empty:
                    return None
                
                # 计算技术指标
                df_with_indicators = self.scanner.calculator.calculate_all_indicators(df, parameters=parameters)
                
                # 应用筛选条件
                condition_result = self.scanner.calculator.check_indicator_conditions(df_with_indicators, indicators)
                
                if not condition_result:
                    return None
                
                # 构建扫描结果
                return self.scanner._build_scan_result(stock_code, df_with_indicators, indicators, self.period)
                
            except Exception as e:
                logger.error(f"Error analyzing stock {stock_code}: {str(e)}")
                return None
    
    def _get_indicators_and_parameters(self, task: ScanTask) -> tuple:
        """
        获取指标列表和参数配置
        
        Args:
            task: 扫描任务
            
        Returns:
            (指标列表, 参数配置)
        """
        if task.scan_mode == "traditional":
            # 传统模式直接使用任务配置
            return task.indicators, task.parameters
        else:
            # 多周期模式但只有一个周期，使用对应周期的配置
            if task.period_indicators and self.period in task.period_indicators:
                indicators = task.period_indicators[self.period]
            else:
                indicators = task.indicators
            
            if task.period_parameters and self.period in task.period_parameters:
                parameters = task.period_parameters[self.period]
            else:
                parameters = task.parameters
            
            return indicators, parameters
    
    async def _trigger_result_callback(self, task: ScanTask, result: ScanResult) -> None:
        """
        触发结果回调
        
        Args:
            task: 扫描任务
            result: 扫描结果
        """
        if hasattr(self.scanner, '_trigger_result_callback'):
            await self.scanner._trigger_result_callback(task, result)
    
    def get_strategy_name(self) -> str:
        """获取策略名称"""
        return f"single_period_{self.period}"
    
    def get_strategy_description(self) -> str:
        """获取策略描述"""
        period_names = {
            'd': '日线',
            'w': '周线', 
            'm': '月线',
            'y': '年线'
        }
        period_name = period_names.get(self.period, self.period)
        return f"单周期{period_name}完整指标分析"