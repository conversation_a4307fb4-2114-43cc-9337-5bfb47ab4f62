"""
扫描策略工厂
根据扫描配置创建对应的策略实例
"""
from typing import List
import logging

from .base import ScanStrategy

logger = logging.getLogger(__name__)


class ScanStrategyFactory:
    """扫描策略工厂"""
    
    @staticmethod
    def create_strategy(scan_mode: str, periods: List[str], scan_strategy: str = "parallel") -> ScanStrategy:
        """
        根据扫描配置创建策略实例
        
        Args:
            scan_mode: 扫描模式 (traditional/multi_period)
            periods: 扫描周期列表
            scan_strategy: 多周期策略 (parallel/cascade)
            
        Returns:
            策略实例
            
        Raises:
            ValueError: 不支持的策略配置
        """
        # 导入具体策略类（避免循环导入）
        from .single_period import SinglePeriodStrategy
        from .multi_period import MultiPeriodStrategy
        
        if scan_mode == "traditional" or len(periods) == 1:
            # logger.info(f"Creating SinglePeriodStrategy for periods: {periods}")
            return SinglePeriodStrategy(periods[0] if periods else "d")
        elif scan_mode == "multi_period" and len(periods) > 1:
            # logger.info(f"Creating MultiPeriodStrategy for periods: {periods}, strategy: {scan_strategy}")
            return MultiPeriodStrategy(
                primary_period=periods[0],
                secondary_periods=periods[1:],
                strategy_type=scan_strategy
            )
        else:
            raise ValueError(f"Unsupported strategy configuration: mode={scan_mode}, periods={periods}")
    
    @staticmethod
    def get_available_strategies() -> List[str]:
        """
        获取可用的策略列表
        
        Returns:
            策略名称列表
        """
        return ["single_period", "multi_period"]