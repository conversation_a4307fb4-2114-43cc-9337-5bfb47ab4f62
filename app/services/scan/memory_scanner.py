import asyncio
from typing import List, Dict, Optional, Set, Callable, Any
from datetime import datetime, timedelta
import logging
import pandas as pd
import time

from app.services.scan.models import (
    ScanTask, ScanStatus, ScanProgress, ScanResult,
    StockIndicatorData, SignalType
)
from app.services.scan.session_manager import SessionManager
from app.services.indicators.scanner_calculator import StockIndicatorCalculator
from app.services.data_fetcher import DataFetcherFactory
from app.core.config import settings
from app.utils.decorators import smart_data_cache
from app.core.data.models import DataType, CacheStrategy
from app.services.scan.strategies import ScanStrategyFactory
from app.utils.trading_utils import get_last_trading_date

logger = logging.getLogger(__name__)


class MemoryScanner:
    """内存扫描器服务"""
    
    def __init__(self, session_manager: SessionManager):
        """
        初始化扫描器
        
        Args:
            session_manager: 会话管理器实例
        """
        self.session_manager = session_manager
        self.calculator = StockIndicatorCalculator()
        # 修复：DataFetcherFactory需要provider参数
        from app.core.config import settings
        self.data_fetcher = DataFetcherFactory.create_fetcher(
            provider=settings.DATA_API_TYPE
        )
        # self._executor = ThreadPoolExecutor(max_workers=4)  # 不再需要线程池
        self._active_scans: Dict[str, asyncio.Task] = {}
        self._scan_callbacks: Dict[str, List[Callable]] = {}
        # 缓存股票名称映射
        self._stock_name_cache: Dict[str, str] = {}
        # 使用统一缓存系统，通过装饰器实现缓存
        # 添加请求频率控制（考虑API供应商并发限制）
        self._request_semaphore = asyncio.Semaphore(settings.SCANNER_CONCURRENT_REQUESTS)
        
        # 缓存预热相关状态
        self._warmup_in_progress = False
        self._warmup_task: Optional[asyncio.Task] = None
        self._warmup_progress = {
            'total_stocks': 0,
            'completed_stocks': 0,
            'failed_stocks': 0,
            'current_stock': '',
            'start_time': None,
            'estimated_completion': None,
            # 数据库预热统计
            'db_hits': 0,
            'api_calls': 0,
            'cache_fallbacks': 0
        }
    
    async def start_scan(
        self,
        session_id: str,
        indicators: List[str],
        stock_codes: Optional[List[str]] = None,
        parameters: Optional[Any] = None,
        scan_mode: str = "traditional",
        scan_strategy: str = "parallel", 
        periods: List[str] = None,
        adjust: str = "n",
        period_parameters: Optional[Any] = None,
        period_indicators: Optional[Dict[str, List[str]]] = None,
        end_date: Optional[str] = None
    ) -> str:
        """
        启动扫描任务
        
        Args:
            session_id: 会话ID
            indicators: 要扫描的指标列表（传统模式使用）
            stock_codes: 要扫描的股票代码列表（None表示扫描所有股票）
            parameters: 指标参数配置（传统模式使用）
            scan_mode: 扫描模式（traditional/multi_period）
            scan_strategy: 多周期策略（parallel/cascade）
            periods: 扫描周期列表
            adjust: 复权方式
            period_parameters: 多周期参数配置
            period_indicators: 分周期指标配置
            end_date: 数据截止日期，用于历史回测（格式：YYYY-MM-DD）
            
        Returns:
            扫描任务ID
        """
        if periods is None:
            periods = ["d"]
            
        # 创建扫描任务
        task = ScanTask(
            session_id=session_id,
            indicators=indicators,
            stock_codes=stock_codes or [],
            parameters=parameters,
            status=ScanStatus.PENDING,
            scan_mode=scan_mode,
            scan_strategy=scan_strategy,
            periods=periods,
            adjust=adjust,
            period_parameters=period_parameters,
            period_indicators=period_indicators,
            end_date=end_date
        )
        
        # 记录扫描配置信息
        if end_date:
            logger.info(f"Starting scan with historical backtest date: {end_date}")
        else:
            logger.info(f"Starting scan with current market data")
        
        # 添加到会话
        self.session_manager.add_scan_task(session_id, task)
        
        # 创建异步扫描任务
        scan_coroutine = self._run_scan(task)
        scan_task = asyncio.create_task(scan_coroutine)
        self._active_scans[task.id] = scan_task
        
        # 任务完成后清理
        scan_task.add_done_callback(
            lambda t: self._active_scans.pop(task.id, None)
        )
        
        logger.info(f"Started scan task {task.id} for session {session_id}")
        return task.id
    
    async def stop_scan(self, session_id: str, task_id: str) -> bool:
        """
        停止扫描任务
        
        Args:
            session_id: 会话ID
            task_id: 任务ID
            
        Returns:
            是否成功停止
        """
        # 获取任务
        task = self.session_manager.get_scan_task(session_id, task_id)
        if not task:
            return False
        
        # 更新状态
        task.status = ScanStatus.CANCELLED
        task.end_time = datetime.now()
        
        # 取消异步任务
        if task_id in self._active_scans:
            self._active_scans[task_id].cancel()
            
        logger.info(f"Stopped scan task {task_id}")
        return True
    
    async def _run_scan(self, task: ScanTask):
        """
        执行扫描任务
        
        Args:
            task: 扫描任务
        """
        try:
            task.status = ScanStatus.RUNNING
            task.start_time = datetime.now()
            
            # 获取股票列表
            stock_codes = task.stock_codes

            if not stock_codes:
                # 获取所有股票
                stock_list = await self._get_all_stocks()
                stock_codes = [s['code'] for s in stock_list]
                
                # 构建股票名称缓存
                for stock in stock_list:
                    self._stock_name_cache[stock['code']] = stock.get('name', stock['code'])
            # 获取前600个
            # stock_codes = stock_codes[0:600]
            total_stocks = len(stock_codes)
            task.progress.total = total_stocks
            
            # 在循环外创建策略实例，避免重复创建
            strategy = ScanStrategyFactory.create_strategy(
                scan_mode=task.scan_mode,
                periods=task.periods,
                scan_strategy=task.scan_strategy
            )
            
            # 注入scanner实例到task中，供策略使用
            task._scanner_instance = self
            
            # 批量处理股票（因为使用真并发，可以适当增加批量大小）
            batch_size = settings.SCANNER_BATCH_SIZE
            for i in range(0, total_stocks, batch_size):
                if task.status == ScanStatus.CANCELLED:
                    break
                    
                batch = stock_codes[i:i + batch_size]
                await self._process_stock_batch(task, batch, strategy)
                
                # 更新进度
                task.progress.current = min(i + batch_size, total_stocks)
                task.progress.percentage = (task.progress.current / total_stocks) * 100
                
                # 触发进度回调
                await self._trigger_progress_callback(task)
                
            # 完成扫描
            if task.status != ScanStatus.CANCELLED:
                task.status = ScanStatus.COMPLETED
                task.end_time = datetime.now()
                
        except Exception as e:
            logger.error(f"Scan task {task.id} failed: {str(e)}")
            task.status = ScanStatus.FAILED
            task.end_time = datetime.now()
            task.error_message = str(e)
    
    async def _process_stock_batch(
        self,
        task: ScanTask,
        stock_codes: List[str],
        strategy
    ):
        """
        处理一批股票（使用策略模式）
        
        Args:
            task: 扫描任务
            stock_codes: 股票代码列表
            strategy: 预先创建的扫描策略实例
        """
        try:
            # logger.info(f"Using strategy: {strategy.get_strategy_name()} - {strategy.get_strategy_description()}")
            
            # 执行策略
            await strategy.execute_scan(task, stock_codes)
            
            logger.debug(f"Strategy completed. Found {len(task.results)} new results in this batch.")
            
        except Exception as e:
            logger.error(f"Error in strategy execution: {str(e)}")
            raise
    
    async def _process_traditional_scan(
        self,
        task: ScanTask,
        stock_codes: List[str]
    ):
        """
        传统扫描：只扫描日线数据
        """
        tasks = []
        for code in stock_codes:
            task_coroutine = asyncio.wait_for(
                self._analyze_stock_period(code, "d", [], task),  # indicators参数已废弃，传空列表
                timeout=60.0
            )
            tasks.append(task_coroutine)
        
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
        except Exception as e:
            logger.error(f"Traditional scan batch processing failed: {str(e)}")
            return
        
        # 处理结果
        for code, result in zip(stock_codes, results):
            if isinstance(result, asyncio.TimeoutError):
                logger.warning(f"Timeout analyzing stock {code}")
                continue
            elif isinstance(result, Exception):
                logger.error(f"Failed to analyze stock {code}: {result}")
                continue
                
            if result and result.signals:
                task.results.append(result)
                await self._trigger_result_callback(task, result)
    
    async def _process_parallel_scan(
        self,
        task: ScanTask,
        stock_codes: List[str]
    ):
        """
        并行扫描：同时扫描所有周期
        """
        tasks = []
        for code in stock_codes:
            for period in task.periods:
                task_coroutine = asyncio.wait_for(
                    self._analyze_stock_period(code, period, [], task),  # indicators参数已废弃，传空列表
                    timeout=60.0
                )
                tasks.append((code, period, task_coroutine))
        
        try:
            # 执行所有任务 - 修复：使用真正的并发执行
            stock_period_coros = [coro for code, period, coro in tasks]
            results_data = await asyncio.gather(*stock_period_coros, return_exceptions=True)
            
            # 重新组合结果
            results = []
            for i, (code, period, _) in enumerate(tasks):
                result = results_data[i]
                results.append((code, period, result))
                
        except Exception as e:
            logger.error(f"Parallel scan batch processing failed: {str(e)}")
            return
        
        # 处理结果
        for code, period, result in results:
            if isinstance(result, Exception):
                logger.error(f"Failed to analyze {code} {period}: {result}")
                continue
            if result and result.signals:
                task.results.append(result)
                await self._trigger_result_callback(task, result)
    
    async def _process_cascade_scan(
        self,
        task: ScanTask,
        stock_codes: List[str]
    ):
        """
        多层级复筛：先扫描日线，符合条件的再扫描周线、月线
        """
        # 第一步：扫描日线 - 修复：使用真正的并发执行
        daily_results = []
        tasks = []
        for code in stock_codes:
            task_coroutine = asyncio.wait_for(
                self._analyze_stock_period(code, "d", [], task),  # indicators参数已废弃，传空列表
                timeout=60.0
            )
            tasks.append((code, task_coroutine))
        
        # 执行日线扫描 - 使用真正并发
        passed_codes = []
        daily_coros = [coro for code, coro in tasks]
        daily_results_data = await asyncio.gather(*daily_coros, return_exceptions=True)
        
        for i, (code, _) in enumerate(tasks):
            result = daily_results_data[i]
            if isinstance(result, Exception):
                logger.error(f"Failed to analyze daily data for {code}: {result}")
                continue
            if result and result.signals:
                daily_results.append(result)
                passed_codes.append(code)
        
        # 第二步：对通过日线筛选的股票扫描其他周期 - 修复：使用真正的并发执行
        if passed_codes and len(task.periods) > 1:
            other_periods = [p for p in task.periods if p != "d"]
            other_period_tasks = []
            
            # 收集所有其他周期的任务
            for code in passed_codes:
                for period in other_periods:
                    task_coroutine = asyncio.wait_for(
                        self._analyze_stock_period(code, period, [], task),
                        timeout=60.0
                    )
                    other_period_tasks.append((code, period, task_coroutine))
            
            # 并发执行所有其他周期任务
            if other_period_tasks:
                other_coros = [coro for code, period, coro in other_period_tasks]
                other_results_data = await asyncio.gather(*other_coros, return_exceptions=True)
                
                for i, (code, period, _) in enumerate(other_period_tasks):
                    result = other_results_data[i]
                    if isinstance(result, Exception):
                        logger.error(f"Failed to analyze {period} data for {code}: {result}")
                        continue
                    if result and result.signals:
                        task.results.append(result)
                        await self._trigger_result_callback(task, result)
        
        # 添加日线结果
        for result in daily_results:
            task.results.append(result)
            await self._trigger_result_callback(task, result)

    def _get_period_indicators_and_parameters(self, task: ScanTask, period: str):
        """
        获取指定周期的指标列表和参数配置
        
        Args:
            task: 扫描任务
            period: 周期（d/w/m）
            
        Returns:
            tuple: (指标列表, 参数配置)
        """
        if task.scan_mode == "traditional":
            # 传统模式：使用task的indicators和parameters
            return task.indicators, task.parameters
        else:
            # 多周期模式：使用period_indicators和period_parameters
            if task.period_indicators and period in task.period_indicators:
                indicators = task.period_indicators[period]
            else:
                # 如果没有配置分周期指标，使用传统指标作为默认值
                indicators = task.indicators
            
            if task.period_parameters and period in task.period_parameters:
                parameters = task.period_parameters[period]
            else:
                # 如果没有配置分周期参数，使用传统参数作为默认值
                parameters = task.parameters
            
            return indicators, parameters

    async def _analyze_stock_period(
        self,
        stock_code: str,
        period: str,
        indicators: List[str],
        task: ScanTask
    ) -> Optional[ScanResult]:
        """
        分析单只股票的指定周期数据
        
        Args:
            stock_code: 股票代码
            period: 数据周期（d/w/m）
            indicators: 指标列表（已废弃，将从task中获取）
            task: 扫描任务（包含参数配置）
            
        Returns:
            扫描结果
        """
        try:
            # 获取当前周期的指标和参数配置
            period_indicators, period_parameters = self._get_period_indicators_and_parameters(task, period)
            
            # 如果当前周期没有指标，跳过
            if not period_indicators:
                return None
            
            # 获取股票数据（包含缓存逻辑和并发控制）
            df = await self._get_stock_period_data(stock_code, period, task.adjust, task.end_date)
            
            if df is None or df.empty:
                return None
            
            # 计算技术指标（传递当前周期的参数配置）
            df_with_indicators = self.calculator.calculate_all_indicators(df, parameters=period_parameters)
            
            # 应用筛选条件（使用当前周期的指标）
            condition_result = self.calculator.check_indicator_conditions(df_with_indicators, period_indicators)
            
            if not condition_result:
                return None  # 不满足条件，过滤掉
            
            # 构建满足条件的详细结果（使用当前周期的指标）
            return self._build_scan_result(stock_code, df_with_indicators, period_indicators, period)
            
        except Exception as e:
            logger.error(f"Error analyzing stock {stock_code}: {str(e)}")
            return None
    
    def _convert_to_dataframe(self, raw_data: List[Dict[str, Any]]) -> pd.DataFrame:
        """
        将原始数据转换为DataFrame
        
        Args:
            raw_data: 从数据源获取的原始数据列表
            
        Returns:
            DataFrame格式的数据
        """
        if not raw_data:
            return pd.DataFrame()
        
        try:
            df = pd.DataFrame(raw_data)
            
            # 确保必需的列存在
            required_columns = ['trade_date', 'open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in df.columns:
                    logger.warning(f"Missing required column: {col}")
                    return pd.DataFrame()
            
            # 转换数据类型
            df['trade_date'] = pd.to_datetime(df['trade_date'])
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 按日期排序
            df = df.sort_values('trade_date').reset_index(drop=True)
            
            # 过滤掉无效数据
            df = df.dropna(subset=['close', 'volume'])
            
            return df
            
        except Exception as e:
            logger.error(f"Error converting raw data to DataFrame: {str(e)}")
            return pd.DataFrame()

    def _clean_stock_code(self, stock_code: str) -> str:
        """
        清理股票代码，去除后缀
        
        Args:
            stock_code: 原始股票代码
            
        Returns:
            清理后的股票代码
        """
        # 截取点号前面的内容
        if '.' in stock_code:
            return stock_code.split('.')[0]
        return stock_code
    
    @smart_data_cache(
        data_type=DataType.STOCK_DAILY,
        cache_strategy=CacheStrategy.CACHE_FIRST,
        cache_ttl=3600  # 1小时缓存
    )
    async def _get_stock_period_data(self, stock_code: str, period: str = "d", adjust: str = "n", end_date: Optional[str] = None) -> Optional[pd.DataFrame]:
        """
        获取股票周期数据（数据库优先，API回退）
        
        Args:
            stock_code: 股票代码
            period: 数据周期（d/w/m）
            adjust: 复权方式
            end_date: 数据截止日期（YYYY-MM-DD），用于历史回测
            
        Returns:
            股票数据DataFrame
        """
        # 步骤1: 优先查询数据库
        # 确定截止日期：如果提供了end_date，使用它；否则使用默认的最近交易日
        if end_date:
            from datetime import datetime
            actual_end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            logger.debug(f"使用历史回测日期: {stock_code} {period} -> {actual_end_date}")
        else:
            actual_end_date = get_last_trading_date()
            logger.debug(f"使用默认最近交易日: {stock_code} {period} -> {actual_end_date}")
        
        start_date = actual_end_date - timedelta(days=365)
        
        try:
            from app.core.database import db_session
            from app.services.storage.stock_storage import StockStorageService
            
            async with db_session() as db:
                storage = StockStorageService(db)
                clean_code = self._clean_stock_code(stock_code)
                
                # 根据周期查询数据库
                if period == "d":
                    db_data = await storage.get_daily_data(clean_code, start_date, actual_end_date)
                elif period == "w":
                    db_data = await storage.get_weekly_data(clean_code, start_date, actual_end_date)
                elif period == "m":
                    db_data = await storage.get_monthly_data(clean_code, start_date, actual_end_date)
                else:
                    db_data = None
                
                if db_data and len(db_data) > 20:  # 如果有足够的数据
                    df = pd.DataFrame(db_data)
                    # 检查数据新鲜度 - 根据周期调整新鲜度要求
                    latest_date = pd.to_datetime(df['trade_date']).max().date()
                    first_date = pd.to_datetime(df['trade_date']).min().date()
                    days_threshold = 1 if period == "d" else (7 if period == "w" else 30)
                    
                    # 如果是历史回测，只检查数据是否覆盖指定日期范围
                    if end_date:
                        if latest_date >= actual_end_date and start_date - first_date < 30:
                            logger.debug(f"数据库命中（历史回测）: {stock_code} {period} - {len(df)}条记录")
                            return df
                    else:
                        # 实时数据，检查新鲜度
                        if (actual_end_date - latest_date).days < days_threshold:
                            logger.debug(f"数据库命中: {stock_code} {period} - {len(df)}条记录")
                            return df
        except Exception as e:
            logger.debug(f"数据库查询失败: {stock_code} {period} - {str(e)}")
        
        # 步骤2: 数据库查询失败，使用API
        # 使用信号量控制并发请求数
        async with self._request_semaphore:
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    # 计算日期范围
                    # 使用actual_end_date而不是get_last_trading_date()
                    start_date = actual_end_date - timedelta(days=365)  # 获取一年的数据
                    
                    # 调用MairuiDataFetcher的get_period_data方法
                    if hasattr(self.data_fetcher, 'get_period_data'):
                        raw_data = await self.data_fetcher.get_period_data(
                            stock_code, period, start_date, actual_end_date, adjust
                        )
                    else:
                        # 降级到get_daily_data
                        if period == "d":
                            raw_data = await self.data_fetcher.get_daily_data(stock_code, start_date, actual_end_date)
                        else:
                            logger.warning(f"Period {period} not supported for {stock_code}, falling back to daily data")
                            raw_data = await self.data_fetcher.get_daily_data(stock_code, start_date, actual_end_date)
                    
                    if not raw_data:
                        return None
                    
                    # 转换为DataFrame
                    df = self._convert_to_dataframe(raw_data)
                    
                    # 如果配置启用数据保存到数据库，则保存数据
                    if settings.SCAN_SAVE_DATA_TO_DB and df is not None and not df.empty:
                        try:
                            await self._save_period_data_to_db(stock_code, period, df)
                        except Exception as e:
                            logger.warning(f"保存股票数据到数据库失败 - 股票: {stock_code}, 周期: {period}, 错误: {str(e)}")
                            # 保存失败不影响扫描流程继续
                    
                    return df
                    
                except asyncio.TimeoutError:
                    if attempt == max_retries - 1:
                        logger.error(f"Timeout fetching {period} data for {stock_code} after {max_retries} attempts")
                        return None
                    await asyncio.sleep(settings.SCANNER_RETRY_TIMEOUT_DELAY)  # 使用配置的超时重试延迟
                except Exception as e:
                    if attempt == max_retries - 1:
                        logger.error(f"Error fetching {period} data for {stock_code}: {str(e)}")
                        return None
                    await asyncio.sleep(settings.SCANNER_RETRY_ERROR_DELAY)  # 使用配置的错误重试延迟

    async def _save_period_data_to_db(self, stock_code: str, period: str, df: pd.DataFrame) -> None:
        """
        将周期数据保存到数据库
        
        Args:
            stock_code: 股票代码
            period: 数据周期（d/w/m）
            df: 股票数据DataFrame
        """
        try:
            # 获取数据库会话
            from app.core.database import db_session
            from app.services.storage.stock_storage import StockStorageService
            
            async with db_session() as db:
                storage_service = StockStorageService(db)
                
                # 将DataFrame转换为字典列表
                data_list = []
                for _, row in df.iterrows():
                    # 转换数据类型，确保与数据库模型兼容
                    record = {
                        'trade_date': pd.to_datetime(row.get('trade_date', row.name)).date(),
                        'open': float(row.get('open', 0)),
                        'high': float(row.get('high', 0)),
                        'low': float(row.get('low', 0)),
                        'close': float(row.get('close', 0)),
                        'volume': int(row.get('volume', 0)),
                        'amount': int(row.get('amount', 0)) if row.get('amount') is not None else None,
                        'change_pct': float(row.get('change_pct', 0)) if row.get('change_pct') is not None else None,
                        'turnover_rate': float(row.get('turnover_rate', 0)) if row.get('turnover_rate') is not None else None,
                    }
                    data_list.append(record)
                
                # 根据周期调用相应的保存方法
                # 使用清理后的股票代码，确保与数据库外键匹配
                clean_stock_code = self._clean_stock_code(stock_code)
                # 使用队列保存数据，避免数据库死锁
                save_stats = await storage_service.save_period_data(clean_stock_code, period, data_list)
                
                if save_stats.get('inserted', 0) > 0 or save_stats.get('updated', 0) > 0:
                    logger.debug(f"成功保存{period}线数据到数据库 - 股票: {stock_code}, "
                               f"新增{save_stats.get('inserted', 0)}条, "
                               f"更新{save_stats.get('updated', 0)}条, "
                               f"跳过{save_stats.get('skipped', 0)}条")
                    
        except Exception as e:
            logger.warning(f"保存周期数据到数据库失败 - 股票: {stock_code}, 周期: {period}, 错误: {str(e)}")
            raise
    
    def _build_scan_result(self, stock_code: str, df: pd.DataFrame, indicators: List[str], period: str = "d") -> ScanResult:
        """
        构建扫描结果
        
        Args:
            stock_code: 股票代码
            df: 包含技术指标的DataFrame
            indicators: 选中的指标列表
            period: 数据周期
            
        Returns:
            扫描结果
        """
        # 获取最新数据和前一日数据
        latest = df.iloc[-1]
        prev_data = df.iloc[-2] if len(df) > 1 else df.iloc[-1]
        
        # 计算布林带距离百分比
        bollinger_distance_pct = 0.0
        try:
            close_price = float(latest.get('close', 0))
            lower_band = float(latest.get('dis_Lowr', 0))
            if lower_band > 0:
                bollinger_distance_pct = ((close_price - lower_band) / lower_band) * 100
        except (ValueError, ZeroDivisionError):
            bollinger_distance_pct = 0.0
        
        # 构建指标数据
        indicator_data = StockIndicatorData(
            kdj_k=float(latest.get('price_k', 0)),
            kdj_d=float(latest.get('price_d', 0)),
            kdj_j=float(latest.get('price_j', 0)),
            volume_pressure=float(latest.get('log_vol', 0)),
            bollinger_upper=float(latest.get('price_upper', 0)),
            bollinger_middle=float(latest.get('price_middle', 0)),
            bollinger_lower=float(latest.get('dis_Lowr', 0)),
            dif=float(latest.get('dif', 0)),  # 使用DIF差离值
            dea=float(latest.get('dea', 0)),  # 使用DEA信号线值
            macd=float(latest.get('macd', 0)),  # 使用MACD柱状图值
            rsi=0.0,  # 暂时设为0，后续可以添加RSI计算
            arbr_ar=0.0,  # 暂时设为0，后续可以添加ARBR计算
            arbr_br=0.0,
            # 新增字段
            prev_kdj_k=float(prev_data.get('price_k', 0)),
            prev_kdj_d=float(prev_data.get('price_d', 0)),
            prev_dif=float(prev_data.get('dif', 0)),
            prev_dea=float(prev_data.get('dea', 0)),
            volume_pressure_avg=float(latest.get('average_vol', 0)),
            close_price=float(latest.get('close', 0)),
            bollinger_distance_pct=bollinger_distance_pct
        )
        # logger.debug(f"Analyzing stock {stock_code} with indicators: {indicator_data}")
        # 根据满足的条件生成信号
        signals = []
        if 'kdj' in indicators and indicator_data.is_kdj_golden_cross():
            signals.append(SignalType.BUY)
        if 'volume_pressure' in indicators and indicator_data.is_volume_breakout():
            signals.append(SignalType.BUY)
        if 'bollinger' in indicators:
            signals.append(SignalType.BUY)  # 布林带机会信号
        if 'macd' in indicators and indicator_data.is_macd_golden_cross():
            signals.append(SignalType.BUY)  # MACD金叉买入信号
        
        # 创建扫描结果
        clean_code = self._clean_stock_code(stock_code)
        result = ScanResult(
            stock_code=clean_code,
            stock_name=self._get_stock_name(stock_code),
            signals=signals,
            indicator_data=indicator_data,
            price=float(latest.get('close', 0)),
            change_percent=float(df['close'].pct_change().iloc[-1] * 100) if len(df) > 1 else 0,
            period=period
        )
        
        return result
    
    def _build_simplified_scan_result(self, stock_code: str, df: pd.DataFrame, period: str = "w") -> ScanResult:
        """
        构建简化的扫描结果（用于辅助周期KDJ上升通道检查）
        
        Args:
            stock_code: 股票代码
            df: 包含KDJ指标的DataFrame
            period: 数据周期
            
        Returns:
            简化的扫描结果
        """
        # 获取最新数据和前一日数据
        latest = df.iloc[-1]
        prev_data = df.iloc[-2] if len(df) > 1 else df.iloc[-1]
        
        # 构建简化的指标数据（只包含KDJ相关）
        indicator_data = StockIndicatorData(
            kdj_k=float(latest.get('k', 0)),
            kdj_d=float(latest.get('d', 0)),
            kdj_j=float(latest.get('j', 0)),
            volume_pressure=0.0,  # 辅助周期不计算
            bollinger_upper=0.0,  # 辅助周期不计算
            bollinger_middle=0.0,  # 辅助周期不计算
            bollinger_lower=0.0,  # 辅助周期不计算
            macd=0.0,
            dif=0.0,
            dea=0.0,
            rsi=0.0,
            arbr_ar=0.0,
            arbr_br=0.0,
            # KDJ相关字段
            prev_kdj_k=float(prev_data.get('k', 0)),
            prev_kdj_d=float(prev_data.get('d', 0)),
            volume_pressure_avg=0.0,
            close_price=float(latest.get('close', 0)),
            bollinger_distance_pct=0.0
        )
        
        # 辅助周期只生成KDJ上升通道信号
        signals = [SignalType.BUY]  # 如果能到这里，说明满足上升通道条件
        
        # 创建简化的扫描结果
        clean_code = self._clean_stock_code(stock_code)
        result = ScanResult(
            stock_code=clean_code,
            stock_name=self._get_stock_name(stock_code),
            signals=signals,
            indicator_data=indicator_data,
            price=float(latest.get('close', 0)),
            change_percent=float(df['close'].pct_change().iloc[-1] * 100) if len(df) > 1 else 0,
            period=period
        )
        
        return result
    
    def _check_threshold_conditions(
        self,
        latest: pd.Series,
        indicators: List[str],
        thresholds: Dict[str, float]
    ) -> List[SignalType]:
        """
        检查阈值条件并生成信号
        
        Args:
            latest: 最新的数据行
            indicators: 指标列表
            thresholds: 阈值设置
            
        Returns:
            信号列表
        """
        signals = []
        
        # KDJ 指标检查
        if "kdj" in indicators:
            kdj_k = float(latest.get('price_k', 0))
            kdj_d = float(latest.get('price_d', 0))
            kdj_j = float(latest.get('price_j', 0))
            
            # KDJ 金叉买入信号
            if (thresholds.get('kdj_k_max', 80) >= kdj_k >= thresholds.get('kdj_k_min', 0) and
                thresholds.get('kdj_d_max', 80) >= kdj_d >= thresholds.get('kdj_d_min', 0)):
                if kdj_k > kdj_d and kdj_j > kdj_k:
                    signals.append(SignalType.BUY)
            
            # KDJ 死叉卖出信号
            if kdj_k > thresholds.get('kdj_k_max', 80) or kdj_d > thresholds.get('kdj_d_max', 80):
                if kdj_k < kdj_d and kdj_j < kdj_k:
                    signals.append(SignalType.SELL)
        
        # 成交量压力指标检查
        if "volume_pressure" in indicators:
            log_vol = float(latest.get('log_vol', 0))
            
            # 成交量压力突破买入信号
            if log_vol >= thresholds.get('volume_pressure_min', 1.5):
                signals.append(SignalType.BUY)
            
            # 成交量压力衰竭卖出信号
            if log_vol <= thresholds.get('volume_pressure_max', 0.5):
                signals.append(SignalType.SELL)
        
        # 布林带指标检查
        if "bollinger" in indicators:
            close = float(latest.get('close', 0))
            lower = float(latest.get('dis_Lowr', 0))
            
            # 价格跌破布林带下轨止损信号
            if close < lower * thresholds.get('bollinger_lower_ratio', 0.98):
                signals.append(SignalType.STOP_LOSS)
        
        return signals

    @smart_data_cache(
        data_type=DataType.STOCK_DAILY,
        cache_strategy=CacheStrategy.CACHE_FIRST,
        cache_ttl=3600  # 1小时缓存
    )
    async def _get_stock_data(self, stock_code: str) -> Optional[pd.DataFrame]:
        """
        获取股票历史数据（使用统一缓存系统）
        
        Args:
            stock_code: 股票代码
            
        Returns:
            股票历史数据DataFrame
        """
        try:
            # 使用信号量控制并发请求数
            async with self._request_semaphore:
                try:
                    end_date = datetime.now()
                    start_date = end_date - timedelta(days=120)
                    
                    daily_data = await asyncio.wait_for(
                        self.data_fetcher.get_daily_data(stock_code, start_date, end_date),
                        timeout=30.0  # 30秒超时
                    )
                    
                    if not daily_data:
                        return None
                    
                    logger.debug(f"Fetched daily_data for {stock_code}: {len(daily_data)} records")
                    
                except asyncio.TimeoutError:
                    logger.error(f"Timeout fetching daily data for {stock_code}")
                    return None
                except Exception as e:
                    logger.error(f"Error fetching daily data for {stock_code}: {str(e)}")
                    return None
            
            # 转换为DataFrame
            import pandas as pd
            df = pd.DataFrame(daily_data)
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to fetch data for {stock_code}: {str(e)}")
            return None
    
    
    def _get_stock_name(self, stock_code: str) -> str:
        """
        获取股票名称
        
        Args:
            stock_code: 股票代码
            
        Returns:
            股票名称
        """
        # 先从缓存中查找
        if stock_code in self._stock_name_cache:
            return self._stock_name_cache[stock_code]
        
        # 如果缓存中没有，返回股票代码
        return stock_code
    
    async def _get_all_stocks(self) -> List[Dict]:
        """
        获取所有股票列表
        
        Returns:
            股票列表
        """
        try:
            # 直接调用异步方法
            stock_list = await self.data_fetcher.get_stock_list()
            return stock_list
        except Exception as e:
            logger.error(f"Failed to get stock list: {str(e)}")
            return []
    
    def register_progress_callback(
        self,
        task_id: str,
        callback: Callable[[ScanTask], None]
    ) -> None:
        """
        注册进度回调
        
        Args:
            task_id: 任务ID
            callback: 回调函数
        """
        if task_id not in self._scan_callbacks:
            self._scan_callbacks[task_id] = []
        self._scan_callbacks[task_id].append(callback)
    
    def unregister_callbacks(self, task_id: str) -> None:
        """
        取消注册回调
        
        Args:
            task_id: 任务ID
        """
        self._scan_callbacks.pop(task_id, None)
    
    async def _trigger_progress_callback(self, task: ScanTask) -> None:
        """
        触发进度回调
        
        Args:
            task: 扫描任务
        """
        callbacks = self._scan_callbacks.get(task.id, [])
        for callback in callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(task)
                else:
                    callback(task)
            except Exception as e:
                logger.error(f"Progress callback error: {str(e)}")
    
    async def _trigger_result_callback(
        self,
        task: ScanTask,
        result: ScanResult
    ) -> None:
        """
        触发结果回调
        
        Args:
            task: 扫描任务
            result: 扫描结果
        """
        # 这里可以添加结果回调逻辑
        pass
    
    def get_active_scan_count(self) -> int:
        """
        获取活跃扫描任务数量
        
        Returns:
            活跃任务数量
        """
        return len(self._active_scans)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息（通过统一缓存系统）"""
        try:
            data_manager = get_data_manager()
            # 使用统一数据管理系统的健康检查获取统计信息
            import asyncio
            health_info = asyncio.create_task(data_manager.health_check())
            
            return {
                'cache_type': 'unified_data_management_system',
                'health_check_scheduled': True,
                'stock_name_cache_size': len(self._stock_name_cache)
            }
        except Exception as e:
            logger.error(f"获取缓存统计失败: {str(e)}")
            return {
                'cache_type': 'unified_cache_system',
                'error': str(e),
                'stock_name_cache_size': len(self._stock_name_cache)
            }
    
    def clear_cache(self) -> None:
        """清空股票名称缓存"""
        self._stock_name_cache.clear()
        logger.info("已清空股票名称缓存")
        # 注意：统一缓存系统的清理需要通过API调用
    
    async def cleanup(self) -> None:
        """清理资源"""
        # 取消所有活跃的扫描任务
        for task in self._active_scans.values():
            task.cancel()
        
        # 等待所有任务完成
        if self._active_scans:
            await asyncio.gather(
                *self._active_scans.values(),
                return_exceptions=True
            )
        
        # 关闭线程池
        # self._executor.shutdown(wait=True)  # 不再需要线程池
    
    # ==================== 缓存预热功能 ====================
    
    async def start_cache_warmup(self) -> str:
        """
        启动缓存预热任务
        
        Returns:
            任务状态信息
        """
        if not settings.CACHE_WARMUP_ENABLED:
            return "缓存预热功能已禁用"
        
        if self._warmup_in_progress:
            return "缓存预热任务已在进行中"
        
        # 启动预热任务
        self._warmup_task = asyncio.create_task(self._warmup_cache())
        logger.info("缓存预热任务已启动")
        return "缓存预热任务已启动"
    
    async def stop_cache_warmup(self) -> str:
        """
        停止缓存预热任务
        
        Returns:
            任务状态信息
        """
        if not self._warmup_in_progress:
            return "没有正在进行的预热任务"
        
        if self._warmup_task:
            self._warmup_task.cancel()
            try:
                await self._warmup_task
            except asyncio.CancelledError:
                pass
        
        self._warmup_in_progress = False
        logger.info("缓存预热任务已停止")
        return "缓存预热任务已停止"
    
    def get_warmup_status(self) -> Dict[str, Any]:
        """
        获取预热状态
        
        Returns:
            预热状态信息
        """
        return {
            'enabled': settings.CACHE_WARMUP_ENABLED,
            'in_progress': self._warmup_in_progress,
            'progress': self._warmup_progress.copy()
        }
    
    def is_warmup_in_progress(self) -> bool:
        """
        检查是否正在预热
        
        Returns:
            是否正在预热
        """
        return self._warmup_in_progress
    
    async def _warmup_cache(self):
        """
        执行缓存预热的主方法（使用统一缓存系统）
        """
        try:
            self._warmup_in_progress = True
            start_time = datetime.now()
            self._warmup_progress.update({
                'start_time': start_time,
                'completed_stocks': 0,
                'failed_stocks': 0,
                'current_stock': '',
                'estimated_completion': None,
                # 重置数据库预热统计
                'db_hits': 0,
                'api_calls': 0,
                'cache_fallbacks': 0
            })
            
            # 获取所有股票列表
            stock_list = await self._get_all_stocks()
            if not stock_list:
                logger.error("无法获取股票列表，预热失败")
                return
            
            stock_codes = [s['code'] for s in stock_list]
            total_stocks = len(stock_codes)
            
            self._warmup_progress['total_stocks'] = total_stocks
            logger.info(f"准备预热 {total_stocks} 只股票的缓存数据")
            
            # 检查并确保股票基本信息存在于数据库中
            await self._ensure_stock_info_exists(stock_list)
            
            # 使用专门的预热信号量控制并发
            warmup_semaphore = asyncio.Semaphore(settings.CACHE_WARMUP_CONCURRENT)
            
            # 分批处理股票
            batch_size = settings.CACHE_WARMUP_BATCH_SIZE
            for i in range(0, total_stocks, batch_size):
                if not self._warmup_in_progress:  # 检查是否被取消
                    break
                
                batch = stock_codes[i:i + batch_size]
                await self._warmup_stock_batch_unified(batch, warmup_semaphore)
                
                # 更新进度
                completed = min(i + batch_size, total_stocks)
                self._warmup_progress['completed_stocks'] = completed
                
                # 估算完成时间
                if completed > 0:
                    elapsed = (datetime.now() - start_time).total_seconds()
                    estimated_total_time = elapsed * total_stocks / completed
                    estimated_completion = start_time + timedelta(seconds=estimated_total_time)
                    self._warmup_progress['estimated_completion'] = estimated_completion
                
                # 记录进度
                progress_pct = (completed / total_stocks) * 100
                logger.info(f"缓存预热进度: {completed}/{total_stocks} ({progress_pct:.1f}%)")
                
                # 给其他任务让出执行机会
                await asyncio.sleep(0.1)
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.info(f"数据库预热完成! 耗时: {duration:.1f}秒")
            logger.info(f"统计信息 - 成功: {self._warmup_progress['completed_stocks']}, "
                       f"失败: {self._warmup_progress['failed_stocks']}")
            logger.info(f"预热来源 - 数据库: {self._warmup_progress['db_hits']}, "
                       f"API: {self._warmup_progress['api_calls']}, "
                       f"缓存回退: {self._warmup_progress['cache_fallbacks']}")
            
            # 计算数据库命中率
            total_requests = (self._warmup_progress['db_hits'] + 
                            self._warmup_progress['api_calls'] + 
                            self._warmup_progress['cache_fallbacks'])
            if total_requests > 0:
                db_hit_rate = (self._warmup_progress['db_hits'] / total_requests) * 100
                logger.info(f"数据库命中率: {db_hit_rate:.1f}%")
            
        except asyncio.CancelledError:
            logger.info("缓存预热任务被取消")
        except Exception as e:
            logger.error(f"缓存预热失败: {str(e)}")
        finally:
            self._warmup_in_progress = False
    
    async def _ensure_stock_info_exists(self, stock_list: List[Dict]):
        """
        确保股票基本信息存在于数据库中，避免外键约束错误
        
        Args:
            stock_list: 股票列表数据
        """
        try:
            from app.core.database import db_session
            from app.services.storage.stock_storage import StockStorageService
            
            async with db_session() as db:
                storage = StockStorageService(db)
                
                logger.info("检查数据库中的股票基本信息...")
                
                # 检查当前数据库中有多少股票信息
                existing_stocks = await storage.get_stock_list(limit=1)
                existing_count = existing_stocks.get('total', 0)
                
                logger.info(f"数据库中现有股票信息: {existing_count} 条")
                logger.info(f"API获取股票列表: {len(stock_list)} 条")

                if existing_count != len(stock_list):  # 如果数据库股票数量不等于API的数量
                    logger.info("数据库股票不匹配，使用现有服务批量保存股票基本信息...")
                    
                    # 直接使用现有的股票存储服务保存API获取的股票列表
                    # batch_save_stock_info方法已经包含了数据标准化和处理逻辑
                    saved_stocks = await storage.replace_all_stock_info(stock_list)
                    logger.info(f"成功保存 {len(saved_stocks)} 条股票基本信息到数据库")
                else:
                    logger.info("数据库股票信息充足，跳过保存步骤")
                    
        except Exception as e:
            logger.error(f"确保股票信息存在失败: {str(e)}")
            # 不抛出异常，让预热继续进行
    
    async def _warmup_stock_batch_unified(self, stock_codes: List[str], semaphore: asyncio.Semaphore):
        """
        批量预热股票缓存（使用统一缓存系统）
        
        Args:
            stock_codes: 股票代码列表
            semaphore: 并发控制信号量
        """
        tasks = []
        for code in stock_codes:
            task = asyncio.create_task(self._warmup_single_stock_unified(code, semaphore))
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        for code, result in zip(stock_codes, results):
            if isinstance(result, Exception):
                self._warmup_progress['failed_stocks'] += 1
                logger.debug(f"预热股票 {code} 失败: {result}")
    
    async def _warmup_single_stock_unified(self, stock_code: str, semaphore: asyncio.Semaphore):
        """
        预热单只股票的缓存数据（数据库优先策略）
        
        Args:
            stock_code: 股票代码
            semaphore: 并发控制信号量
        """
        async with semaphore:
            try:
                self._warmup_progress['current_stock'] = stock_code
                
                # 为配置的每个周期预热数据
                for period in settings.CACHE_WARMUP_PERIODS:
                    success = False
                    
                    # 策略1: 优先使用数据库预热（如果启用）
                    if settings.DB_WARMUP_ENABLED and settings.DB_WARMUP_PRIORITY:
                        success = await self._warmup_from_database(stock_code, period)
                        if success:
                            self._warmup_progress['db_hits'] += 1
                            logger.debug(f"数据库预热成功: {stock_code} {period}")
                            continue
                    
                    # 策略2: 数据库预热失败，回退到API（如果启用回退）
                    if not success and settings.DB_WARMUP_API_FALLBACK:
                        success = await self._warmup_from_api(stock_code, period)
                        if success:
                            self._warmup_progress['api_calls'] += 1
                            logger.debug(f"API预热成功: {stock_code} {period}")
                    
                    # 策略3: 如果都失败了，使用原有的缓存装饰器方法
                    if not success:
                        await self._get_stock_period_data(stock_code, period, "n")
                        self._warmup_progress['cache_fallbacks'] += 1
                        logger.debug(f"缓存装饰器预热: {stock_code} {period}")
                
                logger.debug(f"预热完成: {stock_code}")
                
            except Exception as e:
                logger.debug(f"预热股票 {stock_code} 失败: {str(e)}")
                raise
    
    async def _warmup_from_database(self, stock_code: str, period: str) -> bool:
        """
        从数据库预热数据
        
        Args:
            stock_code: 股票代码
            period: 数据周期
            
        Returns:
            是否成功从数据库获取到足够的数据
        """
        try:
            from app.core.database import db_session
            from app.services.storage.stock_storage import StockStorageService
            
            async with db_session() as db:
                storage = StockStorageService(db)
                
                # 计算查询日期范围
                end_date = get_last_trading_date()
                start_date = end_date - timedelta(days=settings.CACHE_WARMUP_DAYS)
                
                # 清理股票代码，确保与数据库格式一致
                clean_code = self._clean_stock_code(stock_code)
                
                # 根据周期选择合适的查询方法
                if period == "d":
                    data = await storage.get_daily_data(clean_code, start_date, end_date)
                elif period == "w":
                    data = await storage.get_weekly_data(clean_code, start_date, end_date)
                elif period == "m":
                    data = await storage.get_monthly_data(clean_code, start_date, end_date)
                else:
                    logger.warning(f"不支持的预热周期: {period}")
                    return False
                
                # 检查数据是否足够且新鲜
                if data and len(data) >= settings.DB_WARMUP_MIN_RECORDS:
                    # 转换为DataFrame并检查数据新鲜度
                    df = pd.DataFrame(data)
                    if not df.empty:
                        # 检查数据新鲜度 - 根据周期调整新鲜度要求
                        latest_date = pd.to_datetime(df['trade_date']).max().date()
                        current_date = get_last_trading_date()
                        days_threshold = 1 if period == "d" else (7 if period == "w" else 30)
                        
                        if (current_date - latest_date).days < days_threshold:
                            # 触发缓存系统存储（通过装饰器）
                            await self._get_stock_period_data(stock_code, period, "n")
                            logger.debug(f"数据库预热成功: {clean_code} -> {len(data)}条记录，数据新鲜度: {(current_date - latest_date).days}天")
                            return True
                        else:
                            logger.debug(f"数据库数据不够新鲜: {clean_code} {period} - 最新数据{(current_date - latest_date).days}天前 (要求{days_threshold}天内)")
                            return False
                
                logger.debug(f"数据库数据不足: {clean_code} {period} - {len(data) if data else 0} 条记录 (需要{settings.DB_WARMUP_MIN_RECORDS}条)")
                return False
                
        except Exception as e:
            logger.debug(f"数据库预热失败: {stock_code} {period} - {str(e)}")
            return False
    
    async def _warmup_from_api(self, stock_code: str, period: str) -> bool:
        """
        从API预热数据
        
        Args:
            stock_code: 股票代码
            period: 数据周期
            
        Returns:
            是否成功从API获取到数据
        """
        try:
            # 计算日期范围
            end_date = get_last_trading_date()
            start_date = end_date - timedelta(days=settings.CACHE_WARMUP_DAYS)
            
            # 从API获取数据
            if hasattr(self.data_fetcher, 'get_period_data'):
                raw_data = await self.data_fetcher.get_period_data(
                    stock_code, period, start_date, end_date, "n"
                )
            else:
                if period == "d":
                    raw_data = await self.data_fetcher.get_daily_data(stock_code, start_date, end_date)
                else:
                    logger.warning(f"数据源不支持周期 {period}，使用日线数据")
                    raw_data = await self.data_fetcher.get_daily_data(stock_code, start_date, end_date)
            
            if raw_data and len(raw_data) > 0:
                # 转换为DataFrame
                df = self._convert_to_dataframe(raw_data)
                
                if not df.empty:
                    # 如果配置了保存API数据到数据库
                    if settings.DB_WARMUP_SAVE_API_DATA:
                        try:
                            await self._save_api_data_to_db(stock_code, period, df)
                        except Exception as e:
                            logger.warning(f"保存API数据到数据库失败: {e}")
                    
                    # 触发缓存系统存储
                    await self._get_stock_period_data(stock_code, period, "n")
                    return True
            
            return False
            
        except Exception as e:
            logger.debug(f"API预热失败: {stock_code} {period} - {str(e)}")
            return False
    
    async def _save_api_data_to_db(self, stock_code: str, period: str, df: pd.DataFrame):
        """
        将API获取的数据保存到数据库
        
        Args:
            stock_code: 股票代码
            period: 数据周期
            df: 数据DataFrame
        """
        try:
            from app.core.database import db_session
            from app.services.storage.stock_storage import StockStorageService
            
            async with db_session() as db:
                storage = StockStorageService(db)
                
                # 转换DataFrame为字典列表
                data_list = []
                for _, row in df.iterrows():
                    record = {
                        'trade_date': pd.to_datetime(row.get('trade_date', row.name)).date(),
                        'open': float(row.get('open', 0)),
                        'high': float(row.get('high', 0)),
                        'low': float(row.get('low', 0)),
                        'close': float(row.get('close', 0)),
                        'volume': int(row.get('volume', 0)),
                        'amount': int(row.get('amount', 0)) if row.get('amount') is not None else None,
                        'change_pct': float(row.get('change_pct', 0)) if row.get('change_pct') is not None else None,
                        'turnover_rate': float(row.get('turnover_rate', 0)) if row.get('turnover_rate') is not None else None,
                    }
                    data_list.append(record)
                
                # 保存到数据库（使用队列避免死锁）
                clean_stock_code = self._clean_stock_code(stock_code)
                save_stats = await storage.save_period_data(clean_stock_code, period, data_list)
                
                if save_stats['inserted'] > 0 or save_stats['updated'] > 0:
                    logger.debug(f"API数据保存到数据库: {stock_code} {period} - "
                               f"新增{save_stats['inserted']}条, "
                               f"更新{save_stats['updated']}条, "
                               f"跳过{save_stats['skipped']}条")
                    
        except Exception as e:
            logger.warning(f"保存API数据到数据库失败: {stock_code} {period} - {str(e)}")
            raise