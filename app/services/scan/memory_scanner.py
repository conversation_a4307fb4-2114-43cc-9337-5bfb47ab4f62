"""
内存扫描器服务
实现股票技术指标的异步扫描功能
"""
import asyncio
from typing import List, Dict, Optional, Set, Callable, Any
from datetime import datetime, timedelta
import logging
# from concurrent.futures import ThreadPoolExecutor  # 不再需要线程池
import pandas as pd

from app.services.scan.models import (
    ScanTask, ScanStatus, ScanProgress, ScanResult,
    StockIndicatorData, SignalType
)
from app.services.scan.session_manager import SessionManager
from app.services.indicators.scanner_calculator import StockIndicatorCalculator
from app.services.data_fetcher import DataFetcherFactory
from app.core.config import settings

logger = logging.getLogger(__name__)


class MemoryScanner:
    """内存扫描器服务"""
    
    def __init__(self, session_manager: SessionManager):
        """
        初始化扫描器
        
        Args:
            session_manager: 会话管理器实例
        """
        self.session_manager = session_manager
        self.calculator = StockIndicatorCalculator()
        # 修复：DataFetcherFactory需要provider参数
        from app.core.config import settings
        self.data_fetcher = DataFetcherFactory.create_fetcher(
            provider=settings.DATA_API_TYPE
        )
        # self._executor = ThreadPoolExecutor(max_workers=4)  # 不再需要线程池
        self._active_scans: Dict[str, asyncio.Task] = {}
        self._scan_callbacks: Dict[str, List[Callable]] = {}
        # 缓存股票名称映射
        self._stock_name_cache: Dict[str, str] = {}
        # 简单的daily_data缓存
        self._daily_data_cache: Dict[str, List[Dict]] = {}
        # 添加请求频率控制（增加并发请求数）
        self._request_semaphore = asyncio.Semaphore(10)  # 增加到10个并发请求
    
    async def start_scan(
        self,
        session_id: str,
        indicators: List[str],
        stock_codes: Optional[List[str]] = None,
        parameters: Optional[Any] = None
    ) -> str:
        """
        启动扫描任务
        
        Args:
            session_id: 会话ID
            indicators: 要扫描的指标列表
            stock_codes: 要扫描的股票代码列表（None表示扫描所有股票）
            parameters: 指标参数配置
            
        Returns:
            扫描任务ID
        """
        # 创建扫描任务
        task = ScanTask(
            session_id=session_id,
            indicators=indicators,
            stock_codes=stock_codes or [],
            parameters=parameters,
            status=ScanStatus.PENDING
        )
        
        # 添加到会话
        self.session_manager.add_scan_task(session_id, task)
        
        # 创建异步扫描任务
        scan_coroutine = self._run_scan(task)
        scan_task = asyncio.create_task(scan_coroutine)
        self._active_scans[task.id] = scan_task
        
        # 任务完成后清理
        scan_task.add_done_callback(
            lambda t: self._active_scans.pop(task.id, None)
        )
        
        logger.info(f"Started scan task {task.id} for session {session_id}")
        return task.id
    
    async def stop_scan(self, session_id: str, task_id: str) -> bool:
        """
        停止扫描任务
        
        Args:
            session_id: 会话ID
            task_id: 任务ID
            
        Returns:
            是否成功停止
        """
        # 获取任务
        task = self.session_manager.get_scan_task(session_id, task_id)
        if not task:
            return False
        
        # 更新状态
        task.status = ScanStatus.CANCELLED
        task.end_time = datetime.now()
        
        # 取消异步任务
        if task_id in self._active_scans:
            self._active_scans[task_id].cancel()
            
        logger.info(f"Stopped scan task {task_id}")
        return True
    
    async def _run_scan(self, task: ScanTask):
        """
        执行扫描任务
        
        Args:
            task: 扫描任务
        """
        try:
            task.status = ScanStatus.RUNNING
            task.start_time = datetime.now()
            
            # 获取股票列表
            stock_codes = task.stock_codes

            if not stock_codes:
                # 获取所有股票
                stock_list = await self._get_all_stocks()
                stock_codes = [s['code'] for s in stock_list]
                
                # 构建股票名称缓存
                for stock in stock_list:
                    self._stock_name_cache[stock['code']] = stock.get('name', stock['code'])
            # 获取前600到1200个
            # stock_codes = stock_codes[600:1200]
            total_stocks = len(stock_codes)
            task.progress.total = total_stocks
            
            # 批量处理股票
            batch_size = 5
            for i in range(0, total_stocks, batch_size):
                if task.status == ScanStatus.CANCELLED:
                    break
                    
                batch = stock_codes[i:i + batch_size]
                await self._process_stock_batch(task, batch)
                
                # 更新进度
                task.progress.current = min(i + batch_size, total_stocks)
                task.progress.percentage = (task.progress.current / total_stocks) * 100
                
                # 触发进度回调
                await self._trigger_progress_callback(task)
                
            # 完成扫描
            if task.status != ScanStatus.CANCELLED:
                task.status = ScanStatus.COMPLETED
                task.end_time = datetime.now()
                
        except Exception as e:
            logger.error(f"Scan task {task.id} failed: {str(e)}")
            task.status = ScanStatus.FAILED
            task.end_time = datetime.now()
            task.error_message = str(e)
    
    async def _process_stock_batch(
        self,
        task: ScanTask,
        stock_codes: List[str]
    ):
        """
        处理一批股票
        
        Args:
            task: 扫描任务
            stock_codes: 股票代码列表
        """
        # 使用异步并发分析股票，增加超时控制
        tasks = []
        
        for code in stock_codes:
            task_coroutine = asyncio.wait_for(
                self._analyze_stock(code, task.indicators, task),
                timeout=60.0  # 每只股票60秒超时
            )
            tasks.append(task_coroutine)
        
        # 等待所有分析完成，使用gather处理异常
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
        except Exception as e:
            logger.error(f"Batch processing failed: {str(e)}")
            return
        
        # 处理结果
        for code, result in zip(stock_codes, results):
            if isinstance(result, asyncio.TimeoutError):
                logger.warning(f"Timeout analyzing stock {code}")
                continue
            elif isinstance(result, Exception):
                logger.error(f"Failed to analyze stock {code}: {result}")
                continue
                
            if result and result.signals:
                # 添加到结果集
                task.results.append(result)
                # 触发结果回调
                await self._trigger_result_callback(task, result)

    async def _analyze_stock(
        self,
        stock_code: str,
        indicators: List[str],
        task: ScanTask
    ) -> Optional[ScanResult]:
        """
        分析单只股票
        
        Args:
            stock_code: 股票代码
            indicators: 指标列表
            task: 扫描任务（包含参数配置）
            
        Returns:
            扫描结果
        """
        try:
            # 获取股票数据（包含缓存逻辑和并发控制）
            df = await self._get_stock_data(stock_code)
            
            if df is None or df.empty:
                return None
            
            # 计算技术指标（传递参数配置）
            df_with_indicators = self.calculator.calculate_all_indicators(df, parameters=task.parameters)
            
            # 应用筛选条件
            condition_result = self.calculator.check_indicator_conditions(df_with_indicators, indicators)
            
            if not condition_result:
                return None  # 不满足条件，过滤掉
            
            # 构建满足条件的详细结果
            return self._build_scan_result(stock_code, df_with_indicators, indicators)
            
        except Exception as e:
            logger.error(f"Error analyzing stock {stock_code}: {str(e)}")
            return None
    
    def _clean_stock_code(self, stock_code: str) -> str:
        """
        清理股票代码，去除后缀
        
        Args:
            stock_code: 原始股票代码
            
        Returns:
            清理后的股票代码
        """
        # 截取点号前面的内容
        if '.' in stock_code:
            return stock_code.split('.')[0]
        return stock_code
    
    def _build_scan_result(self, stock_code: str, df: pd.DataFrame, indicators: List[str]) -> ScanResult:
        """
        构建扫描结果
        
        Args:
            stock_code: 股票代码
            df: 包含技术指标的DataFrame
            indicators: 选中的指标列表
            
        Returns:
            扫描结果
        """
        # 获取最新数据和前一日数据
        latest = df.iloc[-1]
        prev_data = df.iloc[-2] if len(df) > 1 else df.iloc[-1]
        
        # 计算布林带距离百分比
        bollinger_distance_pct = 0.0
        try:
            close_price = float(latest.get('close', 0))
            lower_band = float(latest.get('dis_Lowr', 0))
            if lower_band > 0:
                bollinger_distance_pct = ((close_price - lower_band) / lower_band) * 100
        except (ValueError, ZeroDivisionError):
            bollinger_distance_pct = 0.0
        
        # 构建指标数据
        indicator_data = StockIndicatorData(
            kdj_k=float(latest.get('price_k', 0)),
            kdj_d=float(latest.get('price_d', 0)),
            kdj_j=float(latest.get('price_j', 0)),
            volume_pressure=float(latest.get('log_vol', 0)),
            bollinger_upper=float(latest.get('price_upper', 0)),
            bollinger_middle=float(latest.get('price_middle', 0)),
            bollinger_lower=float(latest.get('dis_Lowr', 0)),
            macd=0.0,  # 暂时设为0，后续可以添加MACD计算
            macd_signal=0.0,
            macd_histogram=0.0,
            rsi=0.0,  # 暂时设为0，后续可以添加RSI计算
            arbr_ar=0.0,  # 暂时设为0，后续可以添加ARBR计算
            arbr_br=0.0,
            # 新增字段
            prev_kdj_k=float(prev_data.get('price_k', 0)),
            prev_kdj_d=float(prev_data.get('price_d', 0)),
            volume_pressure_avg=float(latest.get('average_vol', 0)),
            close_price=float(latest.get('close', 0)),
            bollinger_distance_pct=bollinger_distance_pct
        )
        # logger.debug(f"Analyzing stock {stock_code} with indicators: {indicator_data}")
        # 根据满足的条件生成信号
        signals = []
        if 'kdj' in indicators and indicator_data.is_kdj_golden_cross():
            signals.append(SignalType.BUY)
        if 'volume_pressure' in indicators and indicator_data.is_volume_breakout():
            signals.append(SignalType.BUY)
        if 'bollinger' in indicators:
            signals.append(SignalType.BUY)  # 布林带机会信号
        
        # 创建扫描结果
        clean_code = self._clean_stock_code(stock_code)
        result = ScanResult(
            stock_code=clean_code,
            stock_name=self._get_stock_name(stock_code),
            signals=signals,
            indicator_data=indicator_data,
            price=float(latest.get('close', 0)),
            change_percent=float(df['close'].pct_change().iloc[-1] * 100) if len(df) > 1 else 0
        )
        
        return result
    
    def _check_threshold_conditions(
        self,
        latest: pd.Series,
        indicators: List[str],
        thresholds: Dict[str, float]
    ) -> List[SignalType]:
        """
        检查阈值条件并生成信号
        
        Args:
            latest: 最新的数据行
            indicators: 指标列表
            thresholds: 阈值设置
            
        Returns:
            信号列表
        """
        signals = []
        
        # KDJ 指标检查
        if "kdj" in indicators:
            kdj_k = float(latest.get('price_k', 0))
            kdj_d = float(latest.get('price_d', 0))
            kdj_j = float(latest.get('price_j', 0))
            
            # KDJ 金叉买入信号
            if (thresholds.get('kdj_k_max', 80) >= kdj_k >= thresholds.get('kdj_k_min', 0) and
                thresholds.get('kdj_d_max', 80) >= kdj_d >= thresholds.get('kdj_d_min', 0)):
                if kdj_k > kdj_d and kdj_j > kdj_k:
                    signals.append(SignalType.BUY)
            
            # KDJ 死叉卖出信号
            if kdj_k > thresholds.get('kdj_k_max', 80) or kdj_d > thresholds.get('kdj_d_max', 80):
                if kdj_k < kdj_d and kdj_j < kdj_k:
                    signals.append(SignalType.SELL)
        
        # 成交量压力指标检查
        if "volume_pressure" in indicators:
            log_vol = float(latest.get('log_vol', 0))
            
            # 成交量压力突破买入信号
            if log_vol >= thresholds.get('volume_pressure_min', 1.5):
                signals.append(SignalType.BUY)
            
            # 成交量压力衰竭卖出信号
            if log_vol <= thresholds.get('volume_pressure_max', 0.5):
                signals.append(SignalType.SELL)
        
        # 布林带指标检查
        if "bollinger" in indicators:
            close = float(latest.get('close', 0))
            lower = float(latest.get('dis_Lowr', 0))
            
            # 价格跌破布林带下轨止损信号
            if close < lower * thresholds.get('bollinger_lower_ratio', 0.98):
                signals.append(SignalType.STOP_LOSS)
        
        return signals

    async def _get_stock_data(self, stock_code: str) -> Optional[pd.DataFrame]:
        """
        获取股票历史数据（包含缓存逻辑）
        
        Args:
            stock_code: 股票代码
            
        Returns:
            股票历史数据DataFrame，可能包含内外盘数据
        """
        try:
            # 生成缓存key：股票代码 + 今日日期
            today = datetime.now().date()
            cache_key = f"{stock_code}_{today.strftime('%Y%m%d')}"
            
            # 1. 先检查缓存
            cached_daily_data = self._daily_data_cache.get(cache_key)
            if cached_daily_data is not None:
                # 缓存命中，直接使用
                daily_data = cached_daily_data
                # logger.debug(f"Cache hit for {cache_key}")
            else:
                # 2. 缓存未命中，需要获取历史数据（使用信号量控制）
                async with self._request_semaphore:
                    try:
                        # logger.debug(f"Cache miss for {cache_key}, fetching data")
                        end_date = datetime.now()
                        start_date = end_date - timedelta(days=120)
                        
                        daily_data = await asyncio.wait_for(
                            self.data_fetcher.get_daily_data(stock_code, start_date, end_date),
                            timeout=30.0  # 30秒超时
                        )
                        
                        if not daily_data:
                            return None
                        
                        # 3. 缓存daily_data
                        self._daily_data_cache[cache_key] = daily_data
                        logger.debug(f"Cached daily_data for {cache_key}: {len(daily_data)} records")
                        
                        # 添加延迟以控制API请求频率
                        # await asyncio.sleep(0.6)
                        
                    except asyncio.TimeoutError:
                        logger.error(f"Timeout fetching daily data for {stock_code}")
                        return None
                    except Exception as e:
                        logger.error(f"Error fetching daily data for {stock_code}: {str(e)}")
                        return None
            
            # 4. 转换为DataFrame
            import pandas as pd
            df = pd.DataFrame(daily_data)
            
            # 5. 尝试获取今日的实时数据（包含内外盘）来补充最新一行
            await self._enrich_with_realtime_data(df, stock_code)
            
            return df
            
        except Exception as e:
            logger.error(f"Failed to fetch data for {stock_code}: {str(e}}")
            return None
    
    async def _enrich_with_realtime_data(self, df: pd.DataFrame, stock_code: str) -> None:
        """
        用实时行情数据丰富历史数据，特别是添加内外盘数据
        
        Args:
            df: 历史数据DataFrame
            stock_code: 股票代码
        """
        try:
            # 只有当使用支持内外盘的数据源时才尝试获取实时数据
            from app.core.config import settings
            if settings.DATA_API_TYPE in ['akshare', 'tushare']:
                # 获取实时行情数据
                realtime_data = await self.data_fetcher.get_realtime_quotes([stock_code])
                
                if realtime_data and len(realtime_data) > 0:
                    realtime_quote = realtime_data[0]
                    
                    # 检查是否有内外盘数据
                    if 'external_volume' in realtime_quote and 'internal_volume' in realtime_quote:
                        # 将内外盘数据添加到最新一行（如果存在）
                        if not df.empty:
                            last_idx = df.index[-1]
                            df.loc[last_idx, 'external_volume'] = float(realtime_quote['external_volume'])
                            df.loc[last_idx, 'internal_volume'] = float(realtime_quote['internal_volume'])
                            
                            # 也可以更新其他实时数据
                            if 'price' in realtime_quote:
                                df.loc[last_idx, 'close'] = float(realtime_quote['price'])
                            if 'volume' in realtime_quote:
                                df.loc[last_idx, 'volume'] = float(realtime_quote['volume'])
                                
                            logger.debug(f"Enriched {stock_code} with realtime internal/external volume data")
                        
        except Exception as e:
            # 实时数据获取失败不应影响主流程，只记录debug日志
            logger.debug(f"Failed to enrich {stock_code} with realtime data: {str(e)}")
    
    def _get_stock_name(self, stock_code: str) -> str:
        """
        获取股票名称
        
        Args:
            stock_code: 股票代码
            
        Returns:
            股票名称
        """
        # 先从缓存中查找
        if stock_code in self._stock_name_cache:
            return self._stock_name_cache[stock_code]
        
        # 如果缓存中没有，返回股票代码
        return stock_code
    
    async def _get_all_stocks(self) -> List[Dict]:
        """
        获取所有股票列表
        
        Returns:
            股票列表
        """
        try:
            # 直接调用异步方法
            stock_list = await self.data_fetcher.get_stock_list()
            return stock_list
        except Exception as e:
            logger.error(f"Failed to get stock list: {str(e)}")
            return []
    
    def register_progress_callback(
        self,
        task_id: str,
        callback: Callable[[ScanTask], None]
    ) -> None:
        """
        注册进度回调
        
        Args:
            task_id: 任务ID
            callback: 回调函数
        """
        if task_id not in self._scan_callbacks:
            self._scan_callbacks[task_id] = []
        self._scan_callbacks[task_id].append(callback)
    
    def unregister_callbacks(self, task_id: str) -> None:
        """
        取消注册回调
        
        Args:
            task_id: 任务ID
        """
        self._scan_callbacks.pop(task_id, None)
    
    async def _trigger_progress_callback(self, task: ScanTask) -> None:
        """
        触发进度回调
        
        Args:
            task: 扫描任务
        """
        callbacks = self._scan_callbacks.get(task.id, [])
        for callback in callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(task)
                else:
                    callback(task)
            except Exception as e:
                logger.error(f"Progress callback error: {str(e)}")
    
    async def _trigger_result_callback(
        self,
        task: ScanTask,
        result: ScanResult
    ) -> None:
        """
        触发结果回调
        
        Args:
            task: 扫描任务
            result: 扫描结果
        """
        # 这里可以添加结果回调逻辑
        pass
    
    def get_active_scan_count(self) -> int:
        """
        获取活跃扫描任务数量
        
        Returns:
            活跃任务数量
        """
        return len(self._active_scans)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'cache_size': len(self._daily_data_cache),
            'cache_keys': list(self._daily_data_cache.keys())
        }
    
    def clear_cache(self) -> None:
        """清空缓存"""
        self._daily_data_cache.clear()
    
    async def cleanup(self) -> None:
        """清理资源"""
        # 取消所有活跃的扫描任务
        for task in self._active_scans.values():
            task.cancel()
        
        # 等待所有任务完成
        if self._active_scans:
            await asyncio.gather(
                *self._active_scans.values(),
                return_exceptions=True
            )
        
        # 关闭线程池
        # self._executor.shutdown(wait=True)  # 不再需要线程池