"""
会话管理器服务
管理用户会话和扫描任务的内存存储
"""
import logging
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import threading

from app.services.scan.models import UserSession, ScanTask, ScanStatus

logger = logging.getLogger(__name__)


class SessionManager:
    """会话管理器
    
    管理用户会话和扫描任务，使用内存存储
    - 支持多用户会话
    - 会话过期自动清理
    - 线程安全
    """
    
    def __init__(self, session_ttl_hours: int = 24, cleanup_interval_minutes: int = 30):
        """
        初始化会话管理器
        
        Args:
            session_ttl_hours: 会话存活时间（小时）
            cleanup_interval_minutes: 清理间隔（分钟）
        """
        self._sessions: Dict[str, UserSession] = {}
        self._session_tasks: Dict[str, List[ScanTask]] = {}
        self._lock = threading.RLock()
        self._session_ttl = timedelta(hours=session_ttl_hours)
        self._cleanup_interval = cleanup_interval_minutes
        self._running = True
        
        # 启动清理线程
        self._cleanup_thread = threading.Thread(
            target=self._cleanup_loop,
            daemon=True
        )
        self._cleanup_thread.start()
        
        logger.info(f"SessionManager initialized with TTL={session_ttl_hours}h")
    
    def create_session(
        self,
        session_id: str,
        user_id: Optional[str] = None,
        metadata: Optional[Dict] = None
    ) -> UserSession:
        """
        创建新会话
        
        Args:
            session_id: 会话ID
            user_id: 用户ID
            metadata: 会话元数据
            
        Returns:
            创建的会话
        """
        with self._lock:
            session = UserSession(
                session_id=session_id,
                user_id=user_id,
                metadata=metadata or {}
            )
            self._sessions[session_id] = session
            self._session_tasks[session_id] = []
            
            logger.info(f"Created session {session_id} for user {user_id}")
            return session
    
    def get_session(self, session_id: str) -> Optional[UserSession]:
        """
        获取会话
        
        Args:
            session_id: 会话ID
            
        Returns:
            会话对象，如果不存在或过期则返回None
        """
        with self._lock:
            session = self._sessions.get(session_id)
            if session and self._is_session_expired(session):
                self.remove_session(session_id)
                return None
            return session
    
    def update_session_activity(self, session_id: str) -> bool:
        """
        更新会话活跃时间
        
        Args:
            session_id: 会话ID
            
        Returns:
            是否更新成功
        """
        with self._lock:
            session = self._sessions.get(session_id)
            if session:
                session.last_active = datetime.now()
                return True
            return False
    
    def remove_session(self, session_id: str) -> bool:
        """
        删除会话及其所有任务
        
        Args:
            session_id: 会话ID
            
        Returns:
            是否删除成功
        """
        with self._lock:
            if session_id in self._sessions:
                # 取消所有运行中的任务
                tasks = self._session_tasks.get(session_id, [])
                for task in tasks:
                    if task.status in [ScanStatus.PENDING, ScanStatus.RUNNING]:
                        task.status = ScanStatus.CANCELLED
                        task.end_time = datetime.now()
                
                # 删除会话和任务
                del self._sessions[session_id]
                self._session_tasks.pop(session_id, None)
                
                logger.info(f"Removed session {session_id}")
                return True
            return False
    
    def add_scan_task(self, session_id: str, task: ScanTask) -> bool:
        """
        添加扫描任务到会话
        
        Args:
            session_id: 会话ID
            task: 扫描任务
            
        Returns:
            是否添加成功
        """
        with self._lock:
            if session_id not in self._sessions:
                return False
            
            self._session_tasks[session_id].append(task)
            self.update_session_activity(session_id)
            
            logger.info(f"Added task {task.id} to session {session_id}")
            return True
    
    def get_scan_task(self, session_id: str, task_id: str) -> Optional[ScanTask]:
        """
        获取扫描任务
        
        Args:
            session_id: 会话ID
            task_id: 任务ID
            
        Returns:
            扫描任务，如果不存在则返回None
        """
        with self._lock:
            tasks = self._session_tasks.get(session_id, [])
            for task in tasks:
                if task.id == task_id:
                    return task
            return None
    
    def get_session_tasks(self, session_id: str) -> List[ScanTask]:
        """
        获取会话的所有任务
        
        Args:
            session_id: 会话ID
            
        Returns:
            任务列表
        """
        with self._lock:
            return self._session_tasks.get(session_id, []).copy()
    
    def cleanup_expired_sessions(self) -> int:
        """
        清理过期会话
        
        Returns:
            清理的会话数量
        """
        with self._lock:
            expired_sessions = []
            
            for session_id, session in self._sessions.items():
                if self._is_session_expired(session):
                    expired_sessions.append(session_id)
            
            for session_id in expired_sessions:
                self.remove_session(session_id)
            
            if expired_sessions:
                logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
            
            return len(expired_sessions)
    
    def _is_session_expired(self, session: UserSession) -> bool:
        """
        检查会话是否过期
        
        Args:
            session: 会话对象
            
        Returns:
            是否过期
        """
        return datetime.now() - session.last_active > self._session_ttl
    
    def _cleanup_loop(self):
        """清理线程主循环"""
        import time
        
        while self._running:
            try:
                time.sleep(self._cleanup_interval * 60)
                self.cleanup_expired_sessions()
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
    
    def shutdown(self):
        """关闭会话管理器"""
        self._running = False
        logger.info("SessionManager shutting down")


# 全局会话管理器实例
_session_manager_instance = None
_session_manager_lock = threading.Lock()


def get_session_manager() -> SessionManager:
    """
    获取会话管理器单例实例
    
    Returns:
        SessionManager: 会话管理器实例
    """
    global _session_manager_instance
    
    if _session_manager_instance is None:
        with _session_manager_lock:
            if _session_manager_instance is None:
                _session_manager_instance = SessionManager()
                logger.info("Created SessionManager singleton instance")
    
    return _session_manager_instance