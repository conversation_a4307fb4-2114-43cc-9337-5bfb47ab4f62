"""
扫描服务实例管理器
提供全局的扫描服务实例
"""
from typing import Optional

from app.services.scan.session_manager import SessionManager
from app.services.scan.memory_scanner import MemoryScanner


class ScanServiceManager:
    """扫描服务管理器"""
    
    _session_manager: Optional[SessionManager] = None
    _memory_scanner: Optional[MemoryScanner] = None
    
    @classmethod
    def get_session_manager(cls) -> SessionManager:
        """
        获取会话管理器实例
        
        Returns:
            会话管理器实例
        """
        if cls._session_manager is None:
            cls._session_manager = SessionManager()
        return cls._session_manager
    
    @classmethod
    def get_memory_scanner(cls) -> MemoryScanner:
        """
        获取内存扫描器实例
        
        Returns:
            内存扫描器实例
        """
        if cls._memory_scanner is None:
            session_manager = cls.get_session_manager()
            cls._memory_scanner = MemoryScanner(session_manager)
        return cls._memory_scanner
    
    @classmethod
    def reset(cls) -> None:
        """重置所有服务实例（用于测试）"""
        cls._session_manager = None
        cls._memory_scanner = None


# 提供便捷的访问方法
def get_session_manager() -> SessionManager:
    """获取会话管理器实例"""
    return ScanServiceManager.get_session_manager()


def get_memory_scanner() -> MemoryScanner:
    """获取内存扫描器实例"""
    return ScanServiceManager.get_memory_scanner()