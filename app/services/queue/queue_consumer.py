"""
队列异步消费者
"""
import asyncio
import logging
from typing import Dict, Any, List, Optional, Callable, Awaitable
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession

from .batch_queue import get_batch_queue
from .models import QueueTask, TaskType, TaskResult, TaskStatus
from app.core.database import db_session
# 移除直接导入，改为延迟导入避免循环依赖
# from app.services.storage.stock_storage import StockStorageService
from app.core.config import settings
from app.core.exceptions import QueueException, DatabaseException

logger = logging.getLogger(__name__)


class QueueConsumer:
    """队列消费者"""
    
    def __init__(self):
        self.batch_queue = get_batch_queue()
        self.running = False
        self.consumer_tasks: List[asyncio.Task] = []
        self._handlers: Dict[TaskType, Callable] = {
            TaskType.BATCH_SAVE_DAILY: self._handle_daily_data,
            TaskType.BATCH_SAVE_WEEKLY: self._handle_weekly_data,
            TaskType.BATCH_SAVE_MONTHLY: self._handle_monthly_data,
            TaskType.BATCH_SAVE_STOCK_INFO: self._handle_stock_info,
            TaskType.BATCH_SAVE_INDICATORS: self._handle_indicators,
        }
        self._stats = {
            'processed_tasks': 0,
            'successful_tasks': 0,
            'failed_tasks': 0,
            'retry_tasks': 0,
            'start_time': None,
            'last_activity': None
        }
    
    async def start(self, concurrency: int = None) -> None:
        """启动消费者"""
        if self.running:
            logger.warning("消费者已在运行中")
            return
        
        concurrency = concurrency or settings.QUEUE_CONSUMER_CONCURRENCY
        self.running = True
        self._stats['start_time'] = datetime.now()
        
        logger.info(f"启动队列消费者，并发数: {concurrency}")
        
        # 为每种任务类型启动消费者（SQLite建议每种类型只启动一个消费者）
        for task_type in TaskType:
            task = asyncio.create_task(
                self._consume_loop(task_type),
                name=f"consumer-{task_type.value}"
            )
            self.consumer_tasks.append(task)
        
        logger.info(f"队列消费者启动完成，共启动 {len(self.consumer_tasks)} 个消费者任务")
    
    async def stop(self, timeout: int = 30) -> None:
        """停止消费者"""
        if not self.running:
            logger.warning("消费者未运行")
            return
        
        logger.info("正在停止队列消费者...")
        self.running = False
        
        # 取消所有消费者任务
        for task in self.consumer_tasks:
            task.cancel()
        
        # 等待任务完成或超时
        if self.consumer_tasks:
            try:
                await asyncio.wait_for(
                    asyncio.gather(*self.consumer_tasks, return_exceptions=True),
                    timeout=timeout
                )
            except asyncio.TimeoutError:
                logger.warning(f"消费者停止超时({timeout}秒)，强制结束")
        
        self.consumer_tasks.clear()
        logger.info("队列消费者已停止")
    
    async def _consume_loop(self, task_type: TaskType) -> None:
        """消费循环"""
        logger.info(f"启动 {task_type.value} 消费者")
        
        while self.running:
            try:
                # 检查队列是否暂停
                if await self.batch_queue.is_queue_paused(task_type):
                    await asyncio.sleep(5)
                    continue
                
                # 从队列获取任务
                task = await self.batch_queue.dequeue_task(
                    task_type, 
                    timeout=settings.QUEUE_CONSUMER_TIMEOUT
                )
                
                if task is None:
                    continue  # 超时，继续循环
                
                # 处理任务
                await self._process_task(task)
                
            except asyncio.CancelledError:
                logger.info(f"{task_type.value} 消费者被取消")
                break
            except Exception as e:
                logger.error(f"{task_type.value} 消费者异常: {str(e)}")
                await asyncio.sleep(1)  # 短暂暂停避免快速循环
    
    async def _process_task(self, task: QueueTask) -> None:
        """处理单个任务"""
        start_time = datetime.now()
        self._stats['processed_tasks'] += 1
        self._stats['last_activity'] = start_time
        
        try:
            logger.debug(f"开始处理任务: {task.task_id}, 类型: {task.task_type}")
            
            # 获取任务处理器
            handler = self._handlers.get(task.task_type)
            if not handler:
                raise QueueException(f"未找到任务类型 {task.task_type} 的处理器")
            
            # 执行任务处理
            result = await handler(task)
            
            # 记录成功
            await self.batch_queue.complete_task(task, result)
            self._stats['successful_tasks'] += 1
            
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"任务处理成功: {task.task_id}, 耗时: {processing_time:.2f}秒")
            
        except Exception as e:
            # 处理任务失败
            error_message = f"任务处理失败: {str(e)}"
            logger.error(f"{error_message}, 任务ID: {task.task_id}")
            
            # 尝试重试或移入死信队列
            if task.can_retry():
                await self.batch_queue.retry_task(task)
                self._stats['retry_tasks'] += 1
            else:
                await self.batch_queue.move_to_dead_letter(task, error_message)
                self._stats['failed_tasks'] += 1
    
    async def _handle_daily_data(self, task: QueueTask) -> TaskResult:
        """处理日线数据保存任务"""
        max_retries = 3
        retry_delay = 1.0  # 起始延迟时间（秒）
        
        for attempt in range(max_retries):
            try:
                async with db_session() as db:
                    # 延迟导入避免循环依赖
                    from app.services.storage.stock_storage import StockStorageService
                    storage = StockStorageService(db)
                    
                    # 使用原有的批量保存方法（直接数据库操作，不使用队列）
                    stats = await storage.batch_save_stock_daily(
                        task.stock_code, 
                        task.data,
                        use_queue=False  # 消费者内部不使用队列
                    )
                    
                    return TaskResult(
                        task_id=task.task_id,
                        success=True,
                        stats=stats,
                        processing_time=task.get_processing_time()
                    )
                    
            except Exception as e:
                error_msg = str(e)
                if "database is locked" in error_msg and attempt < max_retries - 1:
                    # 数据库锁定错误，等待后重试
                    wait_time = retry_delay * (2 ** attempt)  # 指数退避
                    logger.warning(f"数据库锁定，{wait_time:.1f}秒后重试 (尝试 {attempt + 1}/{max_retries}): {task.task_id}")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    # 其他错误或重试次数用完，抛出异常
                    logger.error(f"处理日线数据任务失败 {task.task_id}: {error_msg}")
                    raise
        
        # 不应该到达这里
        raise Exception("处理任务失败：重试次数用完")
    
    async def _handle_weekly_data(self, task: QueueTask) -> TaskResult:
        """处理周线数据保存任务"""
        max_retries = 3
        retry_delay = 1.0
        
        for attempt in range(max_retries):
            try:
                async with db_session() as db:
                    # 延迟导入避免循环依赖
                    from app.services.storage.stock_storage import StockStorageService
                    storage = StockStorageService(db)
                    
                    # 为每条记录添加stock_code
                    weekly_data = []
                    for data in task.data:
                        data_with_code = data.copy()
                        data_with_code['stock_code'] = task.stock_code
                        weekly_data.append(data_with_code)
                    
                    stats = await storage.batch_save_weekly_data(weekly_data, use_queue=False)
                    
                    return TaskResult(
                        task_id=task.task_id,
                        success=True,
                        stats=stats,
                        processing_time=task.get_processing_time()
                    )
                    
            except Exception as e:
                error_msg = str(e)
                if "database is locked" in error_msg and attempt < max_retries - 1:
                    wait_time = retry_delay * (2 ** attempt)
                    logger.warning(f"数据库锁定，{wait_time:.1f}秒后重试 (尝试 {attempt + 1}/{max_retries}): {task.task_id}")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    logger.error(f"处理周线数据任务失败 {task.task_id}: {error_msg}")
                    raise
        
        raise Exception("处理任务失败：重试次数用完")
    
    async def _handle_monthly_data(self, task: QueueTask) -> TaskResult:
        """处理月线数据保存任务"""
        max_retries = 3
        retry_delay = 1.0
        
        for attempt in range(max_retries):
            try:
                async with db_session() as db:
                    # 延迟导入避免循环依赖
                    from app.services.storage.stock_storage import StockStorageService
                    storage = StockStorageService(db)
                    
                    # 为每条记录添加stock_code
                    monthly_data = []
                    for data in task.data:
                        data_with_code = data.copy()
                        data_with_code['stock_code'] = task.stock_code
                        monthly_data.append(data_with_code)
                    
                    stats = await storage.batch_save_monthly_data(monthly_data, use_queue=False)
                    
                    return TaskResult(
                        task_id=task.task_id,
                        success=True,
                        stats=stats,
                        processing_time=task.get_processing_time()
                    )
                    
            except Exception as e:
                error_msg = str(e)
                if "database is locked" in error_msg and attempt < max_retries - 1:
                    wait_time = retry_delay * (2 ** attempt)
                    logger.warning(f"数据库锁定，{wait_time:.1f}秒后重试 (尝试 {attempt + 1}/{max_retries}): {task.task_id}")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    logger.error(f"处理月线数据任务失败 {task.task_id}: {error_msg}")
                    raise
        
        raise Exception("处理任务失败：重试次数用完")
    
    async def _handle_stock_info(self, task: QueueTask) -> TaskResult:
        """处理股票信息保存任务"""
        max_retries = 3
        retry_delay = 1.0
        
        for attempt in range(max_retries):
            try:
                async with db_session() as db:
                    # 延迟导入避免循环依赖
                    from app.services.storage.stock_storage import StockStorageService
                    storage = StockStorageService(db)
                    
                    # 使用原有的批量保存方法（直接数据库操作，不使用队列）
                    saved_stocks = await storage.batch_save_stock_info(task.data, use_queue=False)
                    
                    return TaskResult(
                        task_id=task.task_id,
                        success=True,
                        stats={'inserted': len(saved_stocks), 'updated': 0, 'skipped': 0},
                        processing_time=task.get_processing_time()
                    )
                    
            except Exception as e:
                error_msg = str(e)
                if "database is locked" in error_msg and attempt < max_retries - 1:
                    wait_time = retry_delay * (2 ** attempt)
                    logger.warning(f"数据库锁定，{wait_time:.1f}秒后重试 (尝试 {attempt + 1}/{max_retries}): {task.task_id}")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    logger.error(f"处理股票信息任务失败 {task.task_id}: {error_msg}")
                    raise
        
        raise Exception("处理任务失败：重试次数用完")
    
    async def _handle_indicators(self, task: QueueTask) -> TaskResult:
        """处理指标数据保存任务"""
        max_retries = 3
        retry_delay = 1.0
        
        for attempt in range(max_retries):
            try:
                async with db_session() as db:
                    # 延迟导入避免循环依赖
                    from app.services.storage.stock_storage import StockStorageService
                    storage = StockStorageService(db)
                    
                    # 使用原有的批量保存方法（直接数据库操作，不使用队列）
                    indicators = await storage.batch_save_indicators(
                        task.stock_code, 
                        task.data,
                        use_queue=False  # 消费者内部不使用队列
                    )
                    
                    return TaskResult(
                        task_id=task.task_id,
                        success=True,
                        stats={'inserted': len(indicators), 'updated': 0, 'skipped': 0},
                        processing_time=task.get_processing_time()
                    )
                    
            except Exception as e:
                error_msg = str(e)
                if "database is locked" in error_msg and attempt < max_retries - 1:
                    wait_time = retry_delay * (2 ** attempt)
                    logger.warning(f"数据库锁定，{wait_time:.1f}秒后重试 (尝试 {attempt + 1}/{max_retries}): {task.task_id}")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    logger.error(f"处理指标数据任务失败 {task.task_id}: {error_msg}")
                    raise
        
        raise Exception("处理任务失败：重试次数用完")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取消费者统计信息"""
        stats = self._stats.copy()
        
        if stats['start_time']:
            stats['uptime_seconds'] = (datetime.now() - stats['start_time']).total_seconds()
        
        stats['running'] = self.running
        stats['active_consumers'] = len(self.consumer_tasks)
        
        return stats
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            stats = self.get_stats()
            queue_stats = await self.batch_queue.get_queue_stats()
            
            # 检查是否有消费者在运行
            if not self.running or not self.consumer_tasks:
                return {
                    'status': 'unhealthy',
                    'reason': '消费者未运行',
                    'stats': stats
                }
            
            # 检查最近是否有活动
            if stats['last_activity']:
                inactive_time = (datetime.now() - stats['last_activity']).total_seconds()
                if inactive_time > 300:  # 5分钟无活动
                    return {
                        'status': 'warning',
                        'reason': f'消费者无活动时间: {inactive_time:.0f}秒',
                        'stats': stats,
                        'queue_stats': queue_stats
                    }
            
            return {
                'status': 'healthy',
                'stats': stats,
                'queue_stats': queue_stats
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'reason': str(e)
            }


# 全局消费者实例
_queue_consumer: Optional[QueueConsumer] = None


def get_queue_consumer() -> QueueConsumer:
    """获取全局队列消费者实例"""
    global _queue_consumer
    if _queue_consumer is None:
        _queue_consumer = QueueConsumer()
    return _queue_consumer


async def start_queue_consumer(concurrency: int = None) -> None:
    """启动队列消费者"""
    consumer = get_queue_consumer()
    await consumer.start(concurrency)


async def stop_queue_consumer(timeout: int = 30) -> None:
    """停止队列消费者"""
    global _queue_consumer
    if _queue_consumer:
        await _queue_consumer.stop(timeout)
        _queue_consumer = None