"""
队列任务数据模型
"""
from enum import Enum
from typing import Dict, Any, List, Optional
from datetime import datetime
from dataclasses import dataclass, asdict
import uuid


class TaskType(str, Enum):
    """任务类型枚举"""
    BATCH_SAVE_DAILY = "batch_save_daily"
    BATCH_SAVE_WEEKLY = "batch_save_weekly"
    BATCH_SAVE_MONTHLY = "batch_save_monthly"
    BATCH_SAVE_STOCK_INFO = "batch_save_stock_info"
    BATCH_SAVE_INDICATORS = "batch_save_indicators"


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRY = "retry"


class TaskPriority(int, Enum):
    """任务优先级枚举"""
    LOW = 1
    MEDIUM = 5
    HIGH = 10
    URGENT = 20


@dataclass
class QueueTask:
    """队列任务数据模型"""
    task_id: str
    task_type: TaskType
    data: List[Dict[str, Any]]
    stock_code: Optional[str] = None
    priority: TaskPriority = TaskPriority.MEDIUM
    status: TaskStatus = TaskStatus.PENDING
    created_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.task_id is None:
            self.task_id = str(uuid.uuid4())

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        # 转换datetime为ISO格式字符串
        for key, value in data.items():
            if isinstance(value, datetime):
                data[key] = value.isoformat() if value else None
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'QueueTask':
        """从字典创建实例"""
        # 转换datetime字段
        datetime_fields = ['created_at', 'started_at', 'completed_at']
        for field in datetime_fields:
            if data.get(field):
                data[field] = datetime.fromisoformat(data[field])
        
        # 转换枚举字段
        if 'task_type' in data:
            data['task_type'] = TaskType(data['task_type'])
        if 'status' in data:
            data['status'] = TaskStatus(data['status'])
        if 'priority' in data:
            data['priority'] = TaskPriority(data['priority'])
        
        return cls(**data)

    def mark_processing(self):
        """标记任务为处理中"""
        self.status = TaskStatus.PROCESSING
        self.started_at = datetime.now()

    def mark_completed(self):
        """标记任务为已完成"""
        self.status = TaskStatus.COMPLETED
        self.completed_at = datetime.now()

    def mark_failed(self, error_message: str):
        """标记任务为失败"""
        self.status = TaskStatus.FAILED
        self.error_message = error_message
        self.completed_at = datetime.now()

    def mark_retry(self):
        """标记任务为重试"""
        self.retry_count += 1
        self.status = TaskStatus.RETRY
        self.error_message = None

    def can_retry(self) -> bool:
        """判断是否可以重试"""
        return self.retry_count < self.max_retries

    def get_processing_time(self) -> Optional[float]:
        """获取处理时间（秒）"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None


@dataclass
class BatchSaveTaskData:
    """批量保存任务专用数据结构"""
    stock_code: Optional[str]
    period: Optional[str]  # d/w/m for daily/weekly/monthly
    data_list: List[Dict[str, Any]]
    operation_type: str = "upsert"  # upsert/insert/update

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'BatchSaveTaskData':
        return cls(**data)


@dataclass
class TaskResult:
    """任务执行结果"""
    task_id: str
    success: bool
    stats: Optional[Dict[str, int]] = None  # {'inserted': 0, 'updated': 0, 'skipped': 0}
    error_message: Optional[str] = None
    processing_time: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TaskResult':
        return cls(**data)


class QueueConfig:
    """队列配置常量"""
    
    # 队列名称
    DAILY_DATA_QUEUE = "stock_daily_data_queue"
    WEEKLY_DATA_QUEUE = "stock_weekly_data_queue"
    MONTHLY_DATA_QUEUE = "stock_monthly_data_queue"
    STOCK_INFO_QUEUE = "stock_info_queue"
    INDICATOR_QUEUE = "stock_indicator_queue"
    
    # 死信队列
    DEAD_LETTER_QUEUE = "dead_letter_queue"
    
    # 结果队列
    RESULT_QUEUE = "task_result_queue"
    
    # 队列相关配置
    DEFAULT_BATCH_SIZE = 100
    DEFAULT_TIMEOUT = 30
    DEFAULT_MAX_RETRIES = 3
    
    # 消费者配置
    CONSUMER_CONCURRENCY = 5
    CONSUMER_HEARTBEAT_INTERVAL = 30
    
    @classmethod
    def get_queue_name(cls, task_type: TaskType) -> str:
        """根据任务类型获取队列名称"""
        queue_mapping = {
            TaskType.BATCH_SAVE_DAILY: cls.DAILY_DATA_QUEUE,
            TaskType.BATCH_SAVE_WEEKLY: cls.WEEKLY_DATA_QUEUE,
            TaskType.BATCH_SAVE_MONTHLY: cls.MONTHLY_DATA_QUEUE,
            TaskType.BATCH_SAVE_STOCK_INFO: cls.STOCK_INFO_QUEUE,
            TaskType.BATCH_SAVE_INDICATORS: cls.INDICATOR_QUEUE,
        }
        return queue_mapping.get(task_type, cls.DAILY_DATA_QUEUE)

    @classmethod
    def get_all_queue_names(cls) -> List[str]:
        """获取所有队列名称"""
        return [
            cls.DAILY_DATA_QUEUE,
            cls.WEEKLY_DATA_QUEUE,
            cls.MONTHLY_DATA_QUEUE,
            cls.STOCK_INFO_QUEUE,
            cls.INDICATOR_QUEUE,
            cls.DEAD_LETTER_QUEUE,
            cls.RESULT_QUEUE
        ]