"""
批量保存队列封装
"""
import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from .redis_client import get_redis_client
from .models import (
    QueueTask, TaskType, TaskPriority, TaskStatus,
    BatchSaveTaskData, QueueConfig, TaskResult
)
from app.core.config import settings
from app.core.exceptions import QueueException

logger = logging.getLogger(__name__)


class BatchQueue:
    """批量保存队列管理器"""
    
    def __init__(self):
        self.redis_client = get_redis_client()
        self._stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'retry_tasks': 0
        }
    
    async def enqueue_batch_save(
        self,
        task_type: TaskType,
        data_list: List[Dict[str, Any]],
        stock_code: Optional[str] = None,
        period: Optional[str] = None,
        priority: TaskPriority = TaskPriority.MEDIUM,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        将批量保存任务加入队列
        
        Args:
            task_type: 任务类型
            data_list: 要保存的数据列表
            stock_code: 股票代码
            period: 数据周期
            priority: 任务优先级
            metadata: 元数据
            
        Returns:
            任务ID
        """
        try:
            # 创建任务数据
            batch_data = BatchSaveTaskData(
                stock_code=stock_code,
                period=period,
                data_list=data_list
            )
            
            # 创建队列任务
            task = QueueTask(
                task_id=None,  # 将自动生成
                task_type=task_type,
                data=batch_data.data_list,
                stock_code=stock_code,
                priority=priority,
                metadata=metadata or {}
            )
            
            # 获取对应的队列名称
            queue_name = QueueConfig.get_queue_name(task_type)
            
            # 推送到队列
            queue_length = await self.redis_client.push_to_queue(
                queue_name, 
                task.to_dict(),
                max_length=settings.QUEUE_MAX_LENGTH
            )
            
            # 更新统计
            self._stats['total_tasks'] += 1
            
            # logger.info(f"任务已加入队列: {task.task_id}, 类型: {task_type}, "
            #            f"数据量: {len(data_list)}, 队列长度: {queue_length}")
            
            return task.task_id
            
        except Exception as e:
            logger.error(f"加入队列失败: {str(e)}")
            raise QueueException(f"加入队列失败: {str(e)}")
    
    async def enqueue_daily_data(
        self,
        data_list: List[Dict[str, Any]],
        stock_code: str,
        priority: TaskPriority = TaskPriority.MEDIUM
    ) -> str:
        """加入日线数据保存任务"""
        return await self.enqueue_batch_save(
            TaskType.BATCH_SAVE_DAILY,
            data_list,
            stock_code=stock_code,
            period="d",
            priority=priority
        )
    
    async def enqueue_weekly_data(
        self,
        data_list: List[Dict[str, Any]],
        stock_code: str,
        priority: TaskPriority = TaskPriority.MEDIUM
    ) -> str:
        """加入周线数据保存任务"""
        return await self.enqueue_batch_save(
            TaskType.BATCH_SAVE_WEEKLY,
            data_list,
            stock_code=stock_code,
            period="w",
            priority=priority
        )
    
    async def enqueue_monthly_data(
        self,
        data_list: List[Dict[str, Any]],
        stock_code: str,
        priority: TaskPriority = TaskPriority.MEDIUM
    ) -> str:
        """加入月线数据保存任务"""
        return await self.enqueue_batch_save(
            TaskType.BATCH_SAVE_MONTHLY,
            data_list,
            stock_code=stock_code,
            period="m",
            priority=priority
        )
    
    async def enqueue_stock_info(
        self,
        data_list: List[Dict[str, Any]],
        priority: TaskPriority = TaskPriority.HIGH
    ) -> str:
        """加入股票信息保存任务"""
        return await self.enqueue_batch_save(
            TaskType.BATCH_SAVE_STOCK_INFO,
            data_list,
            priority=priority
        )
    
    async def enqueue_indicators(
        self,
        data_list: List[Dict[str, Any]],
        stock_code: str,
        priority: TaskPriority = TaskPriority.MEDIUM
    ) -> str:
        """加入指标数据保存任务"""
        return await self.enqueue_batch_save(
            TaskType.BATCH_SAVE_INDICATORS,
            data_list,
            stock_code=stock_code,
            priority=priority
        )
    
    async def dequeue_task(
        self, 
        task_type: TaskType,
        timeout: int = 30
    ) -> Optional[QueueTask]:
        """
        从队列取出任务
        
        Args:
            task_type: 任务类型
            timeout: 超时时间
            
        Returns:
            队列任务或None
        """
        try:
            queue_name = QueueConfig.get_queue_name(task_type)
            
            # 从队列弹出数据
            data_list = await self.redis_client.pop_from_queue(
                queue_name, 
                count=1, 
                timeout=timeout
            )
            
            if not data_list:
                return None
            
            # 反序列化任务
            task = QueueTask.from_dict(data_list[0])
            task.mark_processing()
            
            logger.debug(f"从队列取出任务: {task.task_id}, 类型: {task_type}")
            return task
            
        except Exception as e:
            logger.error(f"从队列取出任务失败: {str(e)}")
            raise QueueException(f"从队列取出任务失败: {str(e)}")
    
    async def move_to_dead_letter(self, task: QueueTask, error_message: str) -> None:
        """将失败任务移动到死信队列"""
        try:
            task.mark_failed(error_message)
            
            # 推送到死信队列
            await self.redis_client.push_to_queue(
                QueueConfig.DEAD_LETTER_QUEUE,
                task.to_dict(),
                max_length=settings.QUEUE_MAX_LENGTH
            )
            
            # 更新统计
            self._stats['failed_tasks'] += 1
            
            logger.error(f"任务移入死信队列: {task.task_id}, 错误: {error_message}")
            
        except Exception as e:
            logger.error(f"移动到死信队列失败: {str(e)}")
    
    async def retry_task(self, task: QueueTask) -> bool:
        """重试任务"""
        try:
            if not task.can_retry():
                await self.move_to_dead_letter(task, "超过最大重试次数")
                return False
            
            task.mark_retry()
            
            # 重新推送到队列
            queue_name = QueueConfig.get_queue_name(task.task_type)
            await self.redis_client.push_to_queue(
                queue_name,
                task.to_dict(),
                max_length=settings.QUEUE_MAX_LENGTH
            )
            
            # 更新统计
            self._stats['retry_tasks'] += 1
            
            logger.info(f"任务重试: {task.task_id}, 第{task.retry_count}次重试")
            return True
            
        except Exception as e:
            logger.error(f"任务重试失败: {str(e)}")
            await self.move_to_dead_letter(task, f"重试失败: {str(e)}")
            return False
    
    async def complete_task(self, task: QueueTask, result: TaskResult) -> None:
        """完成任务处理"""
        try:
            task.mark_completed()
            
            # 保存任务结果（可选）
            if settings.QUEUE_SAVE_RESULTS:
                await self.redis_client.push_to_queue(
                    QueueConfig.RESULT_QUEUE,
                    result.to_dict(),
                    max_length=settings.QUEUE_MAX_LENGTH
                )
            
            # 更新统计
            self._stats['completed_tasks'] += 1
            
            logger.info(f"任务完成: {task.task_id}, 处理时间: {task.get_processing_time():.2f}秒")
            
        except Exception as e:
            logger.error(f"完成任务处理失败: {str(e)}")
    
    async def get_queue_stats(self) -> Dict[str, Any]:
        """获取队列统计信息"""
        try:
            # 获取所有队列长度
            queue_names = QueueConfig.get_all_queue_names()
            queue_lengths = await self.redis_client.get_queue_stats(queue_names)
            
            # 获取Redis健康状态
            redis_health = await self.redis_client.health_check()
            
            return {
                'queue_lengths': queue_lengths,
                'task_stats': self._stats.copy(),
                'redis_health': redis_health,
                'config': {
                    'batch_size': QueueConfig.DEFAULT_BATCH_SIZE,
                    'max_retries': QueueConfig.DEFAULT_MAX_RETRIES,
                    'consumer_concurrency': QueueConfig.CONSUMER_CONCURRENCY
                }
            }
            
        except Exception as e:
            logger.error(f"获取队列统计失败: {str(e)}")
            return {
                'error': str(e),
                'task_stats': self._stats.copy()
            }
    
    async def clear_all_queues(self) -> Dict[str, bool]:
        """清空所有队列（仅用于测试和维护）"""
        results = {}
        queue_names = QueueConfig.get_all_queue_names()
        
        for queue_name in queue_names:
            try:
                success = await self.redis_client.clear_queue(queue_name)
                results[queue_name] = success
            except Exception as e:
                logger.error(f"清空队列失败 {queue_name}: {str(e)}")
                results[queue_name] = False
        
        # 重置统计
        self._stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'retry_tasks': 0
        }
        
        return results
    
    async def pause_queue(self, task_type: TaskType) -> bool:
        """暂停队列处理（通过设置标记）"""
        try:
            queue_name = QueueConfig.get_queue_name(task_type)
            pause_key = f"{queue_name}:paused"
            
            async with self.redis_client.get_redis() as redis:
                await redis.set(pause_key, "true", ex=3600)  # 1小时过期
            
            logger.info(f"队列已暂停: {queue_name}")
            return True
            
        except Exception as e:
            logger.error(f"暂停队列失败: {str(e)}")
            return False
    
    async def resume_queue(self, task_type: TaskType) -> bool:
        """恢复队列处理"""
        try:
            queue_name = QueueConfig.get_queue_name(task_type)
            pause_key = f"{queue_name}:paused"
            
            async with self.redis_client.get_redis() as redis:
                await redis.delete(pause_key)
            
            logger.info(f"队列已恢复: {queue_name}")
            return True
            
        except Exception as e:
            logger.error(f"恢复队列失败: {str(e)}")
            return False
    
    async def is_queue_paused(self, task_type: TaskType) -> bool:
        """检查队列是否暂停"""
        try:
            queue_name = QueueConfig.get_queue_name(task_type)
            pause_key = f"{queue_name}:paused"
            
            async with self.redis_client.get_redis() as redis:
                result = await redis.get(pause_key)
                return result == "true"
                
        except Exception as e:
            logger.error(f"检查队列状态失败: {str(e)}")
            return False


# 全局批量队列实例
_batch_queue: Optional[BatchQueue] = None


def get_batch_queue() -> BatchQueue:
    """获取全局批量队列实例"""
    global _batch_queue
    if _batch_queue is None:
        _batch_queue = BatchQueue()
    return _batch_queue