"""
Redis客户端连接管理
"""
import asyncio
import json
from typing import Optional, Any, Dict, List, Union
import redis.asyncio as redis
from contextlib import asynccontextmanager
import logging
from datetime import datetime, date, time
from decimal import Decimal

from app.core.config import settings
from app.core.exceptions import QueueException

logger = logging.getLogger(__name__)


class CustomJSONEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理datetime、date、Decimal、DataFrame等对象"""
    
    def default(self, obj):
        if isinstance(obj, (datetime, date)):
            return obj.isoformat()
        elif isinstance(obj, time):
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            return float(obj)
        elif hasattr(obj, 'to_dict'):
            # 处理pandas DataFrame/Series等对象
            try:
                return obj.to_dict('records')  # 转换为记录列表格式
            except:
                # 如果to_dict('records')失败，尝试普通to_dict()
                return obj.to_dict()
        elif hasattr(obj, '__dict__'):
            # 处理自定义对象
            return obj.__dict__
        return super().default(obj)


def custom_json_dumps(obj, **kwargs):
    """自定义JSON序列化函数（参考L2缓存的序列化方法）"""
    try:
        import pandas as pd
        
        def serialize_obj(data):
            if isinstance(data, pd.DataFrame):
                return {
                    '__type__': 'DataFrame',
                    '__data__': data.to_json(orient='records', date_format='iso')
                }
            elif isinstance(data, (datetime, date)):
                return data.isoformat()
            elif isinstance(data, time):
                return data.isoformat()
            elif isinstance(data, Decimal):
                return float(data)
            elif isinstance(data, dict):
                return {k: serialize_obj(v) for k, v in data.items()}
            elif isinstance(data, list):
                return [serialize_obj(item) for item in data]
            else:
                return data
        
        serialized = serialize_obj(obj)
        return json.dumps(serialized, ensure_ascii=False, **kwargs)
    
    except ImportError:
        # 如果没有pandas，使用原来的CustomJSONEncoder
        return json.dumps(obj, cls=CustomJSONEncoder, ensure_ascii=False, **kwargs)


def custom_json_loads(s, **kwargs):
    """自定义JSON反序列化函数（参考L2缓存的反序列化方法）"""
    try:
        import pandas as pd
        
        def deserialize_obj(obj):
            if isinstance(obj, dict):
                if obj.get('__type__') == 'DataFrame':
                    df_data = json.loads(obj['__data__'])
                    return pd.DataFrame(df_data)
                else:
                    return {k: deserialize_obj(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [deserialize_obj(item) for item in obj]
            else:
                return obj
        
        data = json.loads(s, **kwargs)
        return deserialize_obj(data)
    
    except ImportError:
        # 如果没有pandas，使用普通的json.loads
        return json.loads(s, **kwargs)


class RedisClient:
    """Redis异步客户端封装"""
    
    def __init__(self):
        self._pool: Optional[redis.ConnectionPool] = None
        self._redis: Optional[redis.Redis] = None
        self._lock = asyncio.Lock()
    
    async def connect(self) -> None:
        """建立Redis连接"""
        if self._redis is not None:
            return
            
        async with self._lock:
            if self._redis is not None:
                return
                
            try:
                # 创建连接池
                self._pool = redis.ConnectionPool.from_url(
                    settings.REDIS_URL,
                    max_connections=settings.REDIS_MAX_CONNECTIONS,
                    retry_on_timeout=True,
                    decode_responses=True
                )
                
                # 创建Redis客户端
                self._redis = redis.Redis(connection_pool=self._pool)
                
                # 测试连接
                await self._redis.ping()
                logger.info("Redis连接建立成功")
                
            except Exception as e:
                logger.error(f"Redis连接失败: {str(e)}")
                raise QueueException(f"Redis连接失败: {str(e)}")
    
    async def disconnect(self) -> None:
        """关闭Redis连接"""
        async with self._lock:
            if self._redis:
                await self._redis.close()
                self._redis = None
            
            if self._pool:
                await self._pool.aclose()
                self._pool = None
                
            logger.info("Redis连接已关闭")
    
    @asynccontextmanager
    async def get_redis(self):
        """获取Redis客户端上下文管理器"""
        if self._redis is None:
            await self.connect()
        
        try:
            yield self._redis
        except Exception as e:
            logger.error(f"Redis操作异常: {str(e)}")
            raise QueueException(f"Redis操作异常: {str(e)}")
    
    async def push_to_queue(
        self, 
        queue_name: str, 
        data: Union[Dict[str, Any], List[Dict[str, Any]]],
        max_length: int = 1000
    ) -> int:
        """推送数据到队列
        
        Args:
            queue_name: 队列名称
            data: 要推送的数据
            max_length: 队列最大长度，超出时丢弃旧数据
            
        Returns:
            队列长度
        """
        async with self.get_redis() as redis:
            try:
                # 序列化数据（使用自定义JSON编码器）
                if isinstance(data, list):
                    # 批量推送
                    serialized_data = [custom_json_dumps(item) for item in data]
                    if serialized_data:
                        # 使用pipeline进行原子操作
                        pipe = redis.pipeline()
                        pipe.lpush(queue_name, *serialized_data)
                        pipe.ltrim(queue_name, 0, max_length - 1)  # 保留最新的max_length个元素
                        results = await pipe.execute()
                        queue_length = results[0]  # lpush返回的长度
                    else:
                        queue_length = await redis.llen(queue_name)
                else:
                    # 单条推送
                    serialized_data = custom_json_dumps(data)
                    # 使用pipeline进行原子操作
                    pipe = redis.pipeline()
                    pipe.lpush(queue_name, serialized_data)
                    pipe.ltrim(queue_name, 0, max_length - 1)  # 保留最新的max_length个元素
                    results = await pipe.execute()
                    queue_length = results[0]  # lpush返回的长度
                
                # 实际队列长度不会超过max_length
                actual_length = min(queue_length, max_length)
                
                logger.debug(f"推送到队列 {queue_name}: {len(data) if isinstance(data, list) else 1} 条数据，队列长度: {actual_length}")
                return actual_length
                
            except Exception as e:
                logger.error(f"推送队列失败 {queue_name}: {str(e)}")
                raise QueueException(f"推送队列失败: {str(e)}")
    
    async def pop_from_queue(
        self, 
        queue_name: str, 
        count: int = 1,
        timeout: int = 0
    ) -> List[Dict[str, Any]]:
        """从队列弹出数据
        
        Args:
            queue_name: 队列名称
            count: 弹出数量
            timeout: 阻塞等待超时时间(秒)，0表示非阻塞
            
        Returns:
            弹出的数据列表
        """
        async with self.get_redis() as redis:
            try:
                if timeout > 0 and count == 1:
                    # 阻塞弹出单条
                    result = await redis.brpop(queue_name, timeout=timeout)
                    if result:
                        _, data = result
                        return [custom_json_loads(data)]
                    return []
                else:
                    # 非阻塞弹出多条
                    results = []
                    for _ in range(count):
                        data = await redis.rpop(queue_name)
                        if data is None:
                            break
                        results.append(custom_json_loads(data))
                    return results
                    
            except json.JSONDecodeError as e:
                logger.error(f"队列数据反序列化失败 {queue_name}: {str(e)}")
                raise QueueException(f"队列数据格式错误: {str(e)}")
            except Exception as e:
                logger.error(f"弹出队列失败 {queue_name}: {str(e)}")
                raise QueueException(f"弹出队列失败: {str(e)}")
    
    async def get_queue_length(self, queue_name: str) -> int:
        """获取队列长度"""
        async with self.get_redis() as redis:
            try:
                return await redis.llen(queue_name)
            except Exception as e:
                logger.error(f"获取队列长度失败 {queue_name}: {str(e)}")
                raise QueueException(f"获取队列长度失败: {str(e)}")
    
    async def clear_queue(self, queue_name: str) -> bool:
        """清空队列"""
        async with self.get_redis() as redis:
            try:
                await redis.delete(queue_name)
                logger.info(f"队列 {queue_name} 已清空")
                return True
            except Exception as e:
                logger.error(f"清空队列失败 {queue_name}: {str(e)}")
                raise QueueException(f"清空队列失败: {str(e)}")
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            async with self.get_redis() as redis:
                # 测试连接
                await redis.ping()
                
                # 获取Redis信息
                info = await redis.info()
                
                return {
                    'status': 'healthy',
                    'connected_clients': info.get('connected_clients', 0),
                    'used_memory': info.get('used_memory_human', '0B'),
                    'redis_version': info.get('redis_version', 'unknown')
                }
                
        except Exception as e:
            logger.error(f"Redis健康检查失败: {str(e)}")
            return {
                'status': 'unhealthy',
                'error': str(e)
            }
    
    async def get_queue_stats(self, queue_names: List[str]) -> Dict[str, int]:
        """获取多个队列的统计信息"""
        stats = {}
        async with self.get_redis() as redis:
            try:
                for queue_name in queue_names:
                    stats[queue_name] = await redis.llen(queue_name)
                return stats
            except Exception as e:
                logger.error(f"获取队列统计失败: {str(e)}")
                raise QueueException(f"获取队列统计失败: {str(e)}")


# 全局Redis客户端实例
_redis_client: Optional[RedisClient] = None


def get_redis_client() -> RedisClient:
    """获取全局Redis客户端实例"""
    global _redis_client
    if _redis_client is None:
        _redis_client = RedisClient()
    return _redis_client


async def init_redis():
    """初始化Redis连接"""
    client = get_redis_client()
    await client.connect()


async def close_redis():
    """关闭Redis连接"""
    global _redis_client
    if _redis_client:
        await _redis_client.disconnect()
        _redis_client = None