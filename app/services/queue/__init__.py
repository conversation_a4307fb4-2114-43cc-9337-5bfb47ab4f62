"""
队列服务模块初始化
"""
from .redis_client import get_redis_client, init_redis, close_redis
from .batch_queue import get_batch_queue
from .queue_consumer import get_queue_consumer, start_queue_consumer, stop_queue_consumer
from .models import TaskType, TaskPriority, TaskStatus, QueueConfig

__all__ = [
    # Redis客户端
    'get_redis_client',
    'init_redis', 
    'close_redis',
    
    # 批量队列
    'get_batch_queue',
    
    # 队列消费者
    'get_queue_consumer',
    'start_queue_consumer',
    'stop_queue_consumer',
    
    # 模型和配置
    'TaskType',
    'TaskPriority', 
    'TaskStatus',
    'QueueConfig'
]