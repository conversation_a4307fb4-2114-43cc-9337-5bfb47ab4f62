"""
技术指标计算工具类
统一管理所有技术指标的计算算法
"""
from typing import Tuple
import numpy as np
import pandas as pd
from app.core.logging import getLogger

logger = getLogger(__name__)


class TechnicalIndicators:
    """技术指标工具类"""
    
    @staticmethod
    def calculate_ema(data: pd.Series, period: int) -> pd.Series:
        """
        计算EMA指数移动平均
        
        EMA公式：
        - 第一个EMA值 = 第一个数据值
        - 之后的EMA = α × 当前值 + (1-α) × 前一日EMA
        - α = 2/(period+1)
        
        Args:
            data: 数据序列
            period: EMA周期
            
        Returns:
            EMA值序列
        """
        try:
            if data.empty or len(data) < 1:
                return pd.Series(dtype=float, index=data.index)
            
            # 计算平滑因子
            alpha = 2.0 / (period + 1)
            
            # 初始化结果序列
            ema = pd.Series(dtype=float, index=data.index)
            
            # 第一个EMA值等于第一个数据值
            ema.iloc[0] = data.iloc[0]
            
            # 计算后续EMA值
            for i in range(1, len(data)):
                ema.iloc[i] = alpha * data.iloc[i] + (1 - alpha) * ema.iloc[i-1]
            
            return ema
            
        except Exception as e:
            logger.error(f"计算EMA失败: {str(e)}")
            return pd.Series(dtype=float, index=data.index)
    
    @staticmethod
    def calculate_macd(
        close: pd.Series,
        fast_period: int = 12,
        slow_period: int = 26,
        signal_period: int = 9
    ) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        计算MACD指标
        
        MACD计算步骤：
        1. 计算12日EMA和26日EMA
        2. DIF = 12日EMA - 26日EMA  
        3. DEA = DIF的9日EMA
        4. MACD = DIF - DEA
        
        Args:
            close: 收盘价序列
            fast_period: 快线周期，默认12
            slow_period: 慢线周期，默认26
            signal_period: 信号线周期，默认9
            
        Returns:
            (DIF, DEA, MACD) 元组
        """
        try:
            if close.empty:
                empty_series = pd.Series(dtype=float, index=close.index)
                return empty_series, empty_series, empty_series
                
            # 计算快线和慢线的EMA
            ema_fast = TechnicalIndicators.calculate_ema(close, fast_period)
            ema_slow = TechnicalIndicators.calculate_ema(close, slow_period)
            
            # 计算差离值DIF
            dif = ema_fast - ema_slow
            
            # 计算信号线DEA（DIF的EMA）
            dea = TechnicalIndicators.calculate_ema(dif, signal_period)
            
            # 计算柱状图MACD（DIF - DEA）
            macd = (dif - dea) * 2  # MACD通常乘以2以便于可视化
            return dif, dea, macd
            
        except Exception as e:
            logger.error(f"计算MACD失败: {str(e)}")
            empty_series = pd.Series(dtype=float, index=close.index)
            return empty_series, empty_series, empty_series
    
    @staticmethod
    def calculate_kdj(
        high: pd.Series,
        low: pd.Series,
        close: pd.Series,
        n: int = 9,
        m1: int = 3,
        m2: int = 3
    ) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        计算KDJ指标（标准算法）
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            n: RSV周期，默认9
            m1: K值平滑周期，默认3
            m2: D值平滑周期，默认3
            
        Returns:
            (K线, D线, J线) 元组
        """
        try:
            if high.empty or low.empty or close.empty:
                empty_series = pd.Series(dtype=float, index=close.index)
                return empty_series, empty_series, empty_series
            
            # 计算RSV
            lowest_low = low.rolling(window=n).min()
            highest_high = high.rolling(window=n).max()
            rsv = 100 * (close - lowest_low) / (highest_high - lowest_low + 1e-8)
            
            # 使用标准KDJ算法
            k = pd.Series(index=close.index, dtype='float64')
            d = pd.Series(index=close.index, dtype='float64')
            
            # 找到第一个有效RSV值的位置
            first_valid_idx = rsv.first_valid_index()
            if first_valid_idx is None:
                # 如果没有有效RSV，返回全NaN序列
                k[:] = np.nan
                d[:] = np.nan
                j = pd.Series([np.nan] * len(close), index=close.index)
                return k, d, j
            
            # 设置初始值：K和D的初始值为50（行业标准）
            k.loc[first_valid_idx] = 50.0
            d.loc[first_valid_idx] = 50.0
            
            # 计算后续的K值和D值（使用标准KDJ公式）
            for i in range(close.index.get_loc(first_valid_idx), len(close)):
                idx = close.index[i]
                
                if i == close.index.get_loc(first_valid_idx):
                    # 第一个值特殊处理：K = RSV, D = K
                    if not pd.isna(rsv.loc[idx]):
                        k.loc[idx] = rsv.loc[idx]
                        d.loc[idx] = k.loc[idx]
                    continue
                
                prev_idx = close.index[i-1]
                
                if not pd.isna(rsv.loc[idx]):
                    # 标准KDJ公式:
                    # K = 2/3 × 前日K值 + 1/3 × 今日RSV
                    # D = 2/3 × 前日D值 + 1/3 × 今日K值
                    k.loc[idx] = (2.0/3.0) * k.loc[prev_idx] + (1.0/3.0) * rsv.loc[idx]
                    d.loc[idx] = (2.0/3.0) * d.loc[prev_idx] + (1.0/3.0) * k.loc[idx]
                else:
                    k.loc[idx] = k.loc[prev_idx]  # 保持前值
                    d.loc[idx] = d.loc[prev_idx]  # 保持前值
            
            # 计算J值（标准公式）
            j = 3 * k - 2 * d
            
            return k, d, j
            
        except Exception as e:
            logger.error(f"计算KDJ失败: {str(e)}")
            empty_series = pd.Series(dtype=float, index=close.index)
            return empty_series, empty_series, empty_series
    
    @staticmethod
    def calculate_rsi(close: pd.Series, period: int = 14) -> pd.Series:
        """
        计算RSI指标
        
        Args:
            close: 收盘价序列
            period: RSI周期，默认14
            
        Returns:
            RSI值序列
        """
        try:
            if close.empty or len(close) < period + 1:
                return pd.Series(dtype=float, index=close.index)
                
            # 计算价格变化
            delta = close.diff()
            
            # 分离上涨和下跌
            up = delta.copy()
            down = delta.copy()
            up[up < 0] = 0
            down[down > 0] = 0
            down = down.abs()
            
            # 计算均值
            avg_gain = up.rolling(window=period).mean()
            avg_loss = down.rolling(window=period).mean()
            
            # 计算相对强度RS
            rs = pd.Series(np.zeros(len(close)), index=close.index)
            valid_indices = avg_loss != 0
            rs[valid_indices] = avg_gain[valid_indices] / avg_loss[valid_indices]
            
            # 计算RSI
            rsi = 100 - (100 / (1 + rs))
            
            # 处理可能出现的极端值
            rsi = rsi.fillna(50)  # 用50填充NaN
            rsi = rsi.clip(0, 100)  # RSI只在0-100之间
            
            return rsi
            
        except Exception as e:
            logger.error(f"计算RSI失败: {str(e)}")
            return pd.Series(dtype=float, index=close.index)
    
    @staticmethod
    def calculate_bollinger_bands(
        close: pd.Series,
        window: int = 20,
        std_dev: float = 2.0
    ) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        计算布林带
        
        Args:
            close: 收盘价序列
            window: 计算窗口，默认20
            std_dev: 标准差倍数，默认2.0
            
        Returns:
            (上轨, 中轨, 下轨) 元组
        """
        try:
            if close.empty:
                empty_series = pd.Series(dtype=float, index=close.index)
                return empty_series, empty_series, empty_series
            
            # 计算移动平均（中轨）
            middle = close.rolling(window=window).mean()
            
            # 计算标准差
            std = close.rolling(window=window).std()
            
            # 计算上下轨
            upper = middle + (std_dev * std)
            lower = middle - (std_dev * std)
            
            return upper, middle, lower
            
        except Exception as e:
            logger.error(f"计算布林带失败: {str(e)}")
            empty_series = pd.Series(dtype=float, index=close.index)
            return empty_series, empty_series, empty_series