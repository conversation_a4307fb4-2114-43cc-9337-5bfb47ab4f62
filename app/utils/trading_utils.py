"""
交易日期处理工具函数
处理股票市场交易日期的相关逻辑
"""

from datetime import datetime, date, time, timedelta
from typing import Optional
import logging

logger = logging.getLogger(__name__)


def get_last_trading_date(now: Optional[datetime] = None) -> date:
    """
    获取最近的交易日期
    
    如果当前时间是交易日且已开盘，返回当前日期
    否则返回上一个交易日
    
    Args:
        now: 指定时间，默认为当前时间
        
    Returns:
        最近的交易日期
    """
    if now is None:
        now = datetime.now()
    
    current_date = now.date()
    current_time = now.time()
    
    # 如果是交易日且在开盘后，使用当前日期
    if is_trading_day(current_date) and is_after_market_open(current_time):
        return current_date
    
    # 否则找上一个交易日
    return get_previous_trading_date(current_date)


def is_trading_day(check_date: date) -> bool:
    """
    判断指定日期是否为交易日
    
    使用配置中定义的交易日，未来可扩展节假日逻辑
    
    Args:
        check_date: 要检查的日期
        
    Returns:
        是否为交易日
    """
    from app.core.config import settings
    
    # 检查是否在配置的交易日内 (weekday: 0-6, 0为周一)
    return check_date.weekday() in settings.TRADING_DAYS


def is_after_market_open(check_time: time) -> bool:
    """
    判断指定时间是否在开盘后
    
    使用配置中定义的开盘时间
    
    Args:
        check_time: 要检查的时间
        
    Returns:
        是否在开盘后
    """
    from app.core.config import settings
    
    # 解析配置的开盘时间
    hour, minute = map(int, settings.MARKET_OPEN_TIME.split(':'))
    market_open = time(hour, minute)
    return check_time >= market_open


def get_previous_trading_date(from_date: date) -> date:
    """
    获取指定日期之前的最近交易日
    
    Args:
        from_date: 起始日期
        
    Returns:
        前一个交易日
    """
    check_date = from_date - timedelta(days=1)
    
    # 最多往前查找10天，避免死循环
    for _ in range(10):
        if is_trading_day(check_date):
            return check_date
        check_date -= timedelta(days=1)
    
    # 如果10天内都没有交易日，返回10天前的日期（异常情况）
    logger.warning(f"10天内没有找到交易日，从日期: {from_date}")
    return from_date - timedelta(days=10)


def is_market_hours(now: Optional[datetime] = None) -> bool:
    """
    判断当前是否在交易时间内
    
    使用配置中定义的交易时间：
    - 上午：MARKET_MORNING_START - MARKET_MORNING_END
    - 下午：MARKET_AFTERNOON_START - MARKET_AFTERNOON_END
    
    Args:
        now: 指定时间，默认为当前时间
        
    Returns:
        是否在交易时间内
    """
    from app.core.config import settings
    
    if now is None:
        now = datetime.now()
    
    current_date = now.date()
    current_time = now.time()
    
    # 首先必须是交易日
    if not is_trading_day(current_date):
        return False
    
    # 解析配置的交易时间
    def parse_time(time_str: str) -> time:
        hour, minute = map(int, time_str.split(':'))
        return time(hour, minute)
    
    morning_start = parse_time(settings.MARKET_MORNING_START)
    morning_end = parse_time(settings.MARKET_MORNING_END)
    afternoon_start = parse_time(settings.MARKET_AFTERNOON_START)
    afternoon_end = parse_time(settings.MARKET_AFTERNOON_END)
    
    # 检查是否在交易时间段内
    return ((morning_start <= current_time <= morning_end) or 
            (afternoon_start <= current_time <= afternoon_end))


def format_trading_date(trading_date: date) -> str:
    """
    格式化交易日期为字符串
    
    Args:
        trading_date: 交易日期
        
    Returns:
        格式化的日期字符串 (YYYY-MM-DD)
    """
    return trading_date.strftime('%Y-%m-%d')


def get_trading_date_range(days: int, end_date: Optional[date] = None) -> tuple[date, date]:
    """
    获取交易日期范围
    
    Args:
        days: 天数范围
        end_date: 结束日期，默认为最近交易日
        
    Returns:
        (开始日期, 结束日期) 元组
    """
    if end_date is None:
        end_date = get_last_trading_date()
    
    start_date = end_date - timedelta(days=days)
    return start_date, end_date


# 为了兼容性，提供一些常用的便捷函数
def get_current_trading_date() -> date:
    """获取当前交易日期的便捷函数"""
    return get_last_trading_date()


def get_trading_end_date() -> date:
    """获取用于数据查询的结束日期（最近交易日）"""
    return get_last_trading_date()


def is_market_open(now: Optional[datetime] = None) -> bool:
    """判断市场是否开盘"""
    return is_market_hours(now)


def is_market_closed(now: Optional[datetime] = None) -> bool:
    """判断市场是否收盘"""
    return not is_market_hours(now)


def get_market_status(now: Optional[datetime] = None) -> str:
    """
    获取市场状态
    
    Args:
        now: 指定时间，默认为当前时间
        
    Returns:
        市场状态: 'open'(开盘), 'closed'(收盘), 'non_trading_day'(非交易日)
    """
    if now is None:
        now = datetime.now()
    
    if not is_trading_day(now.date()):
        return 'non_trading_day'
    
    if is_market_hours(now):
        return 'open'
    else:
        return 'closed'


def get_next_trading_session(now: Optional[datetime] = None) -> datetime:
    """
    获取下一个交易时段的开始时间
    
    Args:
        now: 指定时间，默认为当前时间
        
    Returns:
        下一个交易时段的开始时间
    """
    from app.core.config import settings
    
    if now is None:
        now = datetime.now()
    
    def parse_time(time_str: str) -> time:
        hour, minute = map(int, time_str.split(':'))
        return time(hour, minute)
    
    current_date = now.date()
    current_time = now.time()
    
    morning_start = parse_time(settings.MARKET_MORNING_START)
    afternoon_start = parse_time(settings.MARKET_AFTERNOON_START)
    
    # 如果是交易日
    if is_trading_day(current_date):
        # 如果还没到上午开盘时间
        if current_time < morning_start:
            return datetime.combine(current_date, morning_start)
        # 如果在午间休息时间
        elif current_time < afternoon_start:
            return datetime.combine(current_date, afternoon_start)
    
    # 否则找下一个交易日的上午开盘时间
    next_trading_date = get_next_trading_date(current_date)
    return datetime.combine(next_trading_date, morning_start)


def get_next_trading_date(from_date: date) -> date:
    """
    获取指定日期之后的下一个交易日
    
    Args:
        from_date: 起始日期
        
    Returns:
        下一个交易日
    """
    check_date = from_date + timedelta(days=1)
    
    # 最多往后查找10天，避免死循环
    for _ in range(10):
        if is_trading_day(check_date):
            return check_date
        check_date += timedelta(days=1)
    
    # 如果10天内都没有交易日，返回10天后的日期（异常情况）
    logger.warning(f"10天内没有找到交易日，从日期: {from_date}")
    return from_date + timedelta(days=10)