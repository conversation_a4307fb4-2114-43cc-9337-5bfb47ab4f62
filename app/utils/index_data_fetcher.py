"""
指数数据获取工具类
提供统一的指数数据获取和预处理功能，支持从数据库和API提供者获取数据
模仿StockDataFetcher的实现模式
"""
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
from app.services.storage.index_storage import IndexStorageService
from app.core.exceptions import ValidationException
from app.core.logging import logging
from app.core.config import settings

# 使用延迟导入避免循环引用问题
from functools import lru_cache

logger = logging.getLogger(__name__)

class IndexDataFetcher:
    """指数数据获取工具类"""
    
    def __init__(self, storage_service: IndexStorageService):
        """初始化指数数据获取工具
        
        Args:
            storage_service: 指数数据存储服务
        """
        self.storage = storage_service
        self._data_provider = None  # 延迟初始化
        
    async def _get_data_provider(self):
        """延迟初始化数据提供者"""
        if self._data_provider is None:
            from app.services.data_fetcher.factory import DataFetcherFactory
            provider_type = settings.DATA_API_TYPE
            if provider_type == "mairui" and settings.MAIRUI_TOKEN:
                self._data_provider = DataFetcherFactory.get_fetcher(
                    provider_type, licence=settings.MAIRUI_TOKEN
                )
            else:
                self._data_provider = DataFetcherFactory.get_fetcher(provider_type)
                
        return self._data_provider
    
    async def fetch_index_data(
        self,
        index_code: str,
        start_date: Union[str, datetime],
        end_date: Union[str, datetime],
        min_periods: Optional[int] = None,
        extend_days: int = 0,
        use_provider_fallback: bool = True
    ) -> pd.DataFrame:
        """获取指数日线数据并转换为DataFrame
        
        处理日期范围、数据类型转换、API补充等
        
        Args:
            index_code: 指数代码
            start_date: 开始日期
            end_date: 结束日期
            min_periods: 最小需要的数据周期数，如果不指定，则使用时间范围内的工作日数量
            extend_days: 向前扩展查询的天数，用于技术指标计算
            use_provider_fallback: 是否在数据库查询失败时使用数据提供者获取数据
            
        Returns:
            DataFrame: 指数日线数据，包含OHLCV等信息
        """
        try:
            # 1. 标准化日期格式
            start_date_dt, end_date_dt = self._normalize_dates(start_date, end_date)
            
            # 2. 计算扩展日期和预期数据量
            original_start_date, extended_start_date, expected_days, required_periods = self._calculate_extended_date(
                start_date_dt, end_date_dt, min_periods, extend_days
            )
            
            # 3. 准备查询参数
            extended_start = extended_start_date.date() if isinstance(extended_start_date, datetime) else extended_start_date
            end_date_obj = end_date_dt.date() if isinstance(end_date_dt, datetime) else end_date_dt
            
            # 4. 从数据库查询数据
            daily_data, db_query_failed, data_insufficient, data_in_range = await self._query_db_data(
                index_code, extended_start, end_date_obj, start_date_dt, end_date_dt, expected_days
            )
            
            # 5. 如果需要，从数据提供者获取补充数据
            if (not daily_data or db_query_failed or data_insufficient) and use_provider_fallback:
                daily_data = await self._fetch_from_provider(
                    index_code, extended_start_date, end_date_dt, 
                    daily_data, data_insufficient, expected_days, data_in_range
                )
            
            # 6. 检查最终数据
            if not daily_data:
                raise ValidationException(f"未能从数据库或数据提供者获取指数 {index_code} 在指定时间范围的数据")
            
            # 7. 转换为DataFrame
            df = pd.DataFrame(daily_data)
            df['trade_date'] = pd.to_datetime(df['trade_date'])
            df = df.sort_values('trade_date')
            
            # 8. 确保必要的列存在
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in df.columns:
                    logger.warning(f"指数数据缺少列: {col}")
                    df[col] = 0.0
            
            # 9. 数据类型转换
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0.0)
            
            return df
            
        except ValidationException:
            raise
        except Exception as e:
            logger.error(f"获取指数 {index_code} 数据时出错: {str(e)}")
            raise ValidationException(f"处理指数数据失败: {str(e)}")
    
    def _normalize_dates(self, start_date: Union[str, datetime], end_date: Union[str, datetime]):
        """标准化日期格式"""
        if isinstance(start_date, str):
            start_date_dt = datetime.fromisoformat(start_date)
        else:
            start_date_dt = start_date
            
        if isinstance(end_date, str):
            end_date_dt = datetime.fromisoformat(end_date) 
        else:
            end_date_dt = end_date
            
        return start_date_dt, end_date_dt
    
    def _calculate_extended_date(self, start_date_dt, end_date_dt, min_periods, extend_days):
        """计算扩展日期和预期数据量"""
        # 计算预期工作日数量
        expected_days = self._count_weekdays(start_date_dt, end_date_dt)
        
        # 如果指定了最小周期数，使用较大值
        if min_periods:
            required_periods = max(min_periods, expected_days)
        else:
            required_periods = expected_days
            
        # 扩展开始日期
        original_start_date = start_date_dt
        extended_start_date = start_date_dt - timedelta(days=extend_days)
        
        return original_start_date, extended_start_date, expected_days, required_periods
    
    def _count_weekdays(self, start_date: datetime, end_date: datetime) -> int:
        """计算两个日期之间的工作日数量"""
        delta = end_date - start_date
        weekdays = 0
        for i in range(delta.days + 1):
            day = start_date + timedelta(days=i)
            if day.weekday() < 5:  # 周一到周五
                weekdays += 1
        return weekdays
    
    async def _query_db_data(self, index_code, start_date, end_date, start_date_dt, end_date_dt, expected_days):
        """从数据库查询数据"""
        try:
            daily_data = await self.storage.get_index_daily_data(
                index_code, start_date, end_date
            )
            
            db_query_failed = False
            data_insufficient = False
            data_in_range = 0
            
            if daily_data:
                # 检查数据量是否充足
                data_in_range = len([
                    item for item in daily_data 
                    if start_date_dt.date() <= item['trade_date'] <= end_date_dt.date()
                ])
                
                # 数据量不足的阈值：期望数据量的70%
                min_required = max(1, int(expected_days * 0.7))
                data_insufficient = data_in_range < min_required
                
                if data_insufficient:
                    logger.info(f"指数 {index_code} 数据库数据不足: 获得 {data_in_range} 条，期望 {expected_days} 条")
            else:
                data_insufficient = True
                logger.info(f"指数 {index_code} 数据库无数据")
                
            return daily_data, db_query_failed, data_insufficient, data_in_range
            
        except Exception as e:
            logger.error(f"查询指数 {index_code} 数据库数据失败: {e}")
            return None, True, True, 0
    
    async def _fetch_from_provider(self, index_code, start_date_dt, end_date_dt, existing_data, data_insufficient, expected_days, data_in_range):
        """从数据提供者获取数据"""
        try:
            provider = await self._get_data_provider()
            if not provider:
                logger.warning("无可用的数据提供者")
                return existing_data
            
            logger.info(f"从数据提供者获取指数 {index_code} 数据: {start_date_dt.date()} 到 {end_date_dt.date()}")
            
            # 获取指数历史数据
            provider_data = await provider.get_index_period_data(
                index_code,
                "d",  # 日线数据
                start_date_dt.date(),
                end_date_dt.date()
            )
            
            if not provider_data:
                logger.warning(f"数据提供者无指数 {index_code} 数据")
                return existing_data
            
            # 转换数据格式
            formatted_data = []
            for item in provider_data:
                try:
                    formatted_item = {
                        'index_code': index_code,
                        'trade_date': datetime.strptime(str(item.get('date', item.get('trade_date', ''))), "%Y-%m-%d").date(),
                        'open': float(item.get('open', 0)),
                        'high': float(item.get('high', 0)),
                        'low': float(item.get('low', 0)),
                        'close': float(item.get('close', 0)),
                        'volume': int(item.get('volume', 0)),
                        'amount': float(item.get('amount', 0)),
                        'prev_close': float(item.get('prev_close', 0)),
                        'change_pct': float(item.get('change_pct', 0))
                    }
                    formatted_data.append(formatted_item)
                except (ValueError, TypeError) as e:
                    logger.warning(f"跳过无效的指数数据项: {item}, 错误: {e}")
                    continue
            
            if not formatted_data:
                logger.warning(f"数据提供者数据格式化后为空: {index_code}")
                return existing_data
            
            # 批量保存到数据库
            try:
                await self.storage.batch_save_index_daily_data(formatted_data)
                logger.info(f"成功保存 {len(formatted_data)} 条指数 {index_code} 数据到数据库")
            except Exception as e:
                logger.error(f"保存指数数据到数据库失败: {e}")
                # 即使保存失败，仍然返回数据供使用
            
            # 合并数据（优先使用新数据）
            if existing_data and not data_insufficient:
                # 如果已有数据且充足，合并去重
                existing_dates = {item['trade_date'] for item in existing_data}
                new_data = [item for item in formatted_data if item['trade_date'] not in existing_dates]
                combined_data = existing_data + new_data
            else:
                # 否则使用新数据
                combined_data = formatted_data
            
            logger.info(f"最终获得指数 {index_code} 数据 {len(combined_data)} 条")
            return combined_data
            
        except Exception as e:
            logger.error(f"从数据提供者获取指数 {index_code} 数据失败: {e}")
            return existing_data

    def resample_kline(self, df: pd.DataFrame, freq: str) -> pd.DataFrame:
        """重新采样K线数据到指定周期
        
        Args:
            df: 日线数据DataFrame
            freq: 目标周期，'W'周线，'M'月线
            
        Returns:
            DataFrame: 重采样后的数据
        """
        if df.empty:
            return df
        
        if freq not in ['W', 'M']:
            return df
        
        # 设置日期为索引
        df_resampled = df.set_index('trade_date').resample(freq).agg({
            'open': 'first',
            'high': 'max', 
            'low': 'min',
            'close': 'last',
            'volume': 'sum',
            'amount': 'sum' if 'amount' in df.columns else 'sum'
        }).dropna()
        
        # 重置索引
        df_resampled = df_resampled.reset_index()
        
        # 重新计算涨跌幅
        if len(df_resampled) > 1:
            df_resampled['prev_close'] = df_resampled['close'].shift(1)
            df_resampled['change_pct'] = (
                (df_resampled['close'] - df_resampled['prev_close']) / df_resampled['prev_close'] * 100
            ).fillna(0)
        else:
            df_resampled['prev_close'] = 0
            df_resampled['change_pct'] = 0
        
        return df_resampled