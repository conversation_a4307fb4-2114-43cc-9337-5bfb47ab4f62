"""JWT认证工具模块"""
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import jwt
import uuid
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.config import settings
from app.core.database import db_session
from app.models.user import User


def generate_access_token(user_id: int, username: str, is_admin: bool, token_version: int = 0) -> str:
    """生成访问token"""
    now = datetime.utcnow()
    payload = {
        'user_id': user_id,
        'username': username,
        'is_admin': is_admin,
        'token_version': token_version,  # 添加token版本号用于强制过期
        'exp': now + timedelta(hours=settings.JWT_EXPIRE_HOURS),
        'iat': now,
        'jti': str(uuid.uuid4())  # JWT ID用于标识唯一token
    }
    return jwt.encode(payload, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)


def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """验证并解析token"""
    try:
        payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.PyJWTError:  # 使用PyJWTError作为所有JWT异常的基类
        return None


async def refresh_token(old_token: str) -> Optional[str]:
    """刷新token（24小时内可续期）"""
    payload = verify_token(old_token)
    if not payload:
        return None
    
    async with db_session() as db:
        stmt = select(User).where(User.id == payload['user_id'])
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user or not user.is_active:
            return None
        
        # 验证token版本号
        if payload.get('token_version', 0) != user.token_version:
            return None
            
        # 生成新token
        return generate_access_token(user.id, user.username, user.is_admin, user.token_version)


async def revoke_user_tokens(user_id: int) -> bool:
    """撤销用户的所有token（通过增加token版本号）"""
    async with db_session() as db:
        stmt = select(User).where(User.id == user_id)
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user:
            return False
        
        user.token_version += 1
        await db.commit()
        return True


async def verify_user_token(token: str) -> Optional[User]:
    """验证token并返回用户对象"""
    payload = verify_token(token)
    if not payload:
        return None
    
    async with db_session() as db:
        stmt = select(User).where(User.id == payload['user_id'])
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user or not user.is_active:
            return None
        
        # 验证token版本号
        if payload.get('token_version', 0) != user.token_version:
            return None
        
        return user