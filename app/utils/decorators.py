"""
智能缓存装饰器

提供基于统一数据管理系统的智能缓存装饰器，支持多级缓存和灵活的缓存策略。
"""

import asyncio
import functools
import hashlib
import json
import time
from typing import Any, Callable, Dict, List, Optional, Union, TypeVar, Tuple
from datetime import datetime, timedelta
import inspect
import logging

from app.core.logging import getLogger
from app.core.data.models import (
    DataRequest,
    DataResult, 
    DataType,
    CacheStrategy,
    CacheLevel
)
from app.core.data.manager import get_data_manager

logger = getLogger(__name__)

T = TypeVar('T')


def _generate_cache_key(*args, **kwargs) -> str:
    """生成缓存键"""
    def _is_serializable(obj):
        """检查对象是否可以安全序列化"""
        return obj is None or isinstance(obj, (bool, int, float, str, list, tuple, dict))
    
    def _serialize_safe(obj):
        """安全序列化基本类型"""
        if obj is None or isinstance(obj, (bool, int, float, str)):
            return obj
        elif isinstance(obj, (list, tuple)):
            return [_serialize_safe(item) for item in obj if _is_serializable(item)]
        elif isinstance(obj, dict):
            return {
                str(k): _serialize_safe(v) 
                for k, v in sorted(obj.items()) 
                if _is_serializable(v)
            }
        else:
            # 跳过复杂对象
            return None
    
    # 只包含可序列化的参数
    serializable_args = [_serialize_safe(arg) for arg in args if _is_serializable(arg)]
    serializable_kwargs = {
        str(k): _serialize_safe(v) 
        for k, v in sorted(kwargs.items()) 
        if _is_serializable(v)
    }
    
    key_data = {
        'args': serializable_args,
        'kwargs': serializable_kwargs
    }
    
    # 序列化并生成哈希
    key_str = json.dumps(key_data, sort_keys=True, ensure_ascii=False)
    return hashlib.md5(key_str.encode('utf-8')).hexdigest()


def _extract_cache_params(func_args: Dict[str, Any]) -> Dict[str, Any]:
    """从函数参数中提取缓存相关参数"""
    cache_params = {}
    
    # 提取常见的缓存相关参数
    param_mapping = {
        'cache_strategy': 'cache_strategy',
        'cache_ttl': 'cache_ttl', 
        'force_refresh': 'force_refresh',
        'timeout': 'timeout'
    }
    
    for param_name, cache_key in param_mapping.items():
        if param_name in func_args:
            cache_params[cache_key] = func_args[param_name]
    
    return cache_params


class SmartCache:
    """智能缓存装饰器类"""
    
    def __init__(
        self,
        data_type: Optional[Union[DataType, str]] = None,
        cache_strategy: CacheStrategy = CacheStrategy.CACHE_FIRST,
        cache_ttl: int = 3600,
        key_generator: Optional[Callable] = None,
        enabled: bool = True,
        auto_invalidate: bool = False,
        invalidate_patterns: Optional[List[str]] = None
    ):
        self.data_type = DataType(data_type) if isinstance(data_type, str) else data_type
        self.cache_strategy = cache_strategy
        self.cache_ttl = cache_ttl
        self.key_generator = key_generator or _generate_cache_key
        self.enabled = enabled
        self.auto_invalidate = auto_invalidate
        self.invalidate_patterns = invalidate_patterns or []
        
        # 统计信息
        self.stats = {
            'total_calls': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'cache_errors': 0,
            'total_time': 0.0,
            'cache_time': 0.0
        }
    
    def __call__(self, func: Callable[..., T]) -> Callable[..., T]:
        """装饰器调用"""
        if not self.enabled:
            return func
        
        if asyncio.iscoroutinefunction(func):
            return self._wrap_async_function(func)
        else:
            return self._wrap_sync_function(func)
    
    def _wrap_async_function(self, func: Callable[..., T]) -> Callable[..., T]:
        """包装异步函数"""
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> T:
            return await self._cached_call_async(func, *args, **kwargs)
        return wrapper
    
    def _wrap_sync_function(self, func: Callable[..., T]) -> Callable[..., T]:
        """包装同步函数"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> T:
            # 对于同步函数，我们需要在异步环境中运行
            try:
                loop = asyncio.get_event_loop()
                return loop.run_until_complete(
                    self._cached_call_async(func, *args, **kwargs)
                )
            except RuntimeError:
                # 如果没有事件循环，直接调用原函数
                logger.warning("没有事件循环，跳过缓存")
                return func(*args, **kwargs)
        return wrapper
    
    async def _cached_call_async(self, func: Callable[..., T], *args, **kwargs) -> T:
        """缓存调用的异步实现"""
        start_time = time.time()
        self.stats['total_calls'] += 1
        
        try:
            # 生成缓存键
            cache_key = self._generate_cache_key(func, *args, **kwargs)
            
            # 检查是否强制刷新
            force_refresh = kwargs.get('force_refresh', False)
            
            # 尝试从缓存获取数据
            if not force_refresh:
                cached_result = await self._get_from_cache(cache_key)
                if cached_result is not None:
                    self.stats['cache_hits'] += 1
                    self.stats['cache_time'] += time.time() - start_time
                    logger.debug(f"缓存命中: {func.__name__}")
                    return cached_result
            
            # 缓存未命中，调用原函数
            self.stats['cache_misses'] += 1
            logger.debug(f"缓存未命中，调用原函数: {func.__name__}")
            
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                # 在线程池中运行同步函数
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(None, func, *args, **kwargs)
            
            # 将结果存入缓存
            await self._set_to_cache(cache_key, result, *args, **kwargs)
            
            self.stats['total_time'] += time.time() - start_time
            return result
            
        except Exception as e:
            self.stats['cache_errors'] += 1
            logger.error(f"缓存装饰器执行失败: {e}")
            
            # 如果缓存失败，直接调用原函数
            if asyncio.iscoroutinefunction(func):
                return await func(*args, **kwargs)
            else:
                return func(*args, **kwargs)
    
    def _generate_cache_key(self, func: Callable, *args, **kwargs) -> str:
        """生成缓存键"""
        # 移除缓存相关参数，避免影响缓存键生成
        filtered_kwargs = {
            k: v for k, v in kwargs.items() 
            if k not in ['cache_strategy', 'cache_ttl', 'force_refresh', 'timeout']
        }
        
        base_key = self.key_generator(*args, **filtered_kwargs)
        return f"smart_cache:{self.data_type}:{func.__name__}:{base_key}"

    async def _get_from_cache(self, cache_key: str) -> Optional[Any]:
        """从缓存获取数据"""
        try:
            data_manager = get_data_manager()
            cached_data = await data_manager.router.cache_manager.get(cache_key)
            
            if cached_data:
                # 检查缓存是否过期
                if self._is_cache_valid(cached_data):
                    return cached_data.get('result')
            
            return None
            
        except Exception as e:
            logger.error(f"缓存获取失败: {e}")
            return None
    
    async def _set_to_cache(self, cache_key: str, result: Any, *args, **kwargs) -> None:
        """设置缓存数据"""
        try:
            data_manager = get_data_manager()
            
            # 提取缓存参数
            func_args = dict(zip(
                inspect.signature(lambda *a, **k: None).parameters.keys(),
                args
            ))
            func_args.update(kwargs)
            cache_params = _extract_cache_params(func_args)
            
            cache_ttl = cache_params.get('cache_ttl', self.cache_ttl)
            
            cache_data = {
                'result': result,
                'timestamp': datetime.now().isoformat(),
                'ttl': cache_ttl,
                'metadata': {
                    'data_type': self.data_type.value if self.data_type else None,
                    'cache_strategy': self.cache_strategy.value
                }
            }
            
            await data_manager.router.cache_manager.set(
                cache_key,
                cache_data,
                ttl=cache_ttl
            )
            
            logger.debug(f"数据已缓存: {cache_key}")
            
        except Exception as e:
            logger.error(f"缓存设置失败: {e}")
    
    def _is_cache_valid(self, cached_data: Dict[str, Any]) -> bool:
        """检查缓存是否有效"""
        try:
            timestamp_str = cached_data.get('timestamp')
            if not timestamp_str:
                return False
            
            timestamp = datetime.fromisoformat(timestamp_str)
            ttl = cached_data.get('ttl', self.cache_ttl)
            
            return datetime.now() - timestamp < timedelta(seconds=ttl)
            
        except Exception as e:
            logger.error(f"缓存有效性检查失败: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_calls = self.stats['total_calls']
        if total_calls == 0:
            return self.stats
        
        hit_rate = (self.stats['cache_hits'] / total_calls) * 100
        avg_total_time = self.stats['total_time'] / total_calls
        avg_cache_time = self.stats['cache_time'] / max(self.stats['cache_hits'], 1)
        
        return {
            **self.stats,
            'hit_rate': f"{hit_rate:.2f}%",
            'avg_total_time': f"{avg_total_time:.4f}s",
            'avg_cache_time': f"{avg_cache_time:.4f}s"
        }
    
    async def invalidate(self, *args, **kwargs) -> None:
        """使缓存失效"""
        try:
            # 这里可以根据参数生成缓存键并删除
            cache_key = self._generate_cache_key(lambda: None, *args, **kwargs)
            data_manager = get_data_manager()
            await data_manager.router.cache_manager.delete(cache_key)
            logger.info(f"缓存已失效: {cache_key}")
        except Exception as e:
            logger.error(f"缓存失效失败: {e}")


# 装饰器工厂函数
def smart_data_cache(
    data_type: Optional[Union[DataType, str]] = None,
    cache_strategy: CacheStrategy = CacheStrategy.CACHE_FIRST,
    cache_ttl: int = 3600,
    key_generator: Optional[Callable] = None,
    enabled: bool = True,
    auto_invalidate: bool = False,
    invalidate_patterns: Optional[List[str]] = None
) -> Callable:
    """智能数据缓存装饰器
    
    Args:
        data_type: 数据类型
        cache_strategy: 缓存策略
        cache_ttl: 缓存存活时间（秒）
        key_generator: 自定义缓存键生成器
        enabled: 是否启用缓存
        auto_invalidate: 是否自动失效
        invalidate_patterns: 失效模式列表
    
    Returns:
        装饰器函数
    
    Examples:
        @smart_data_cache(data_type=DataType.STOCK_INFO, cache_ttl=1800)
        async def get_stock_info(stock_code: str):
            # 获取股票信息的实现
            pass
        
        @smart_data_cache(
            data_type=DataType.STOCK_DAILY,
            cache_strategy=CacheStrategy.CACHE_THROUGH,
            cache_ttl=3600
        )
        def get_stock_daily_data(stock_code: str, start_date: str, end_date: str):
            # 获取股票日线数据的实现
            pass
    """
    return SmartCache(
        data_type=data_type,
        cache_strategy=cache_strategy,
        cache_ttl=cache_ttl,
        key_generator=key_generator,
        enabled=enabled,
        auto_invalidate=auto_invalidate,
        invalidate_patterns=invalidate_patterns
    )


# 预定义的缓存装饰器
def cache_stock_info(cache_ttl: int = 3600, **kwargs):
    """股票信息缓存装饰器"""
    return smart_data_cache(
        data_type=DataType.STOCK_INFO,
        cache_ttl=cache_ttl,
        **kwargs
    )


def cache_stock_daily(cache_ttl: int = 1800, **kwargs):
    """股票日线数据缓存装饰器 - 调整为10分钟缓存确保交易时间内数据及时更新"""
    return smart_data_cache(
        data_type=DataType.STOCK_DAILY,
        cache_ttl=cache_ttl,
        **kwargs
    )


def cache_stock_realtime(cache_ttl: int = 30, **kwargs):
    """股票实时数据缓存装饰器"""
    return smart_data_cache(
        data_type=DataType.STOCK_REALTIME,
        cache_ttl=cache_ttl,
        cache_strategy=CacheStrategy.CACHE_ASIDE,
        **kwargs
    )


def cache_stock_indicators(cache_ttl: int = 7200, **kwargs):
    """股票技术指标缓存装饰器"""
    return smart_data_cache(
        data_type=DataType.STOCK_INDICATORS,
        cache_ttl=cache_ttl,
        **kwargs
    )


def cache_stock_weekly(cache_ttl: int = 3600*24, **kwargs):
    """股票周线数据缓存装饰器 - 调整为1小时缓存确保周线数据及时更新"""
    return smart_data_cache(
        data_type=DataType.STOCK_WEEKLY,
        cache_ttl=cache_ttl,
        **kwargs
    )


def cache_stock_monthly(cache_ttl: int = 3600*24*7, **kwargs):
    """股票月线数据缓存装饰器 - 调整为2小时缓存确保月线数据及时更新"""
    return smart_data_cache(
        data_type=DataType.STOCK_MONTHLY,
        cache_ttl=cache_ttl,
        **kwargs
    )


# 缓存管理工具类
class CacheManager:
    """缓存管理工具"""
    
    def __init__(self):
        self.cache_instances: Dict[str, SmartCache] = {}
    
    def register_cache(self, name: str, cache_instance: SmartCache) -> None:
        """注册缓存实例"""
        self.cache_instances[name] = cache_instance
    
    def get_cache_stats(self, cache_name: Optional[str] = None) -> Dict[str, Any]:
        """获取缓存统计信息"""
        if cache_name:
            cache_instance = self.cache_instances.get(cache_name)
            return cache_instance.get_stats() if cache_instance else {}
        
        # 返回所有缓存的统计信息
        all_stats = {}
        for name, cache_instance in self.cache_instances.items():
            all_stats[name] = cache_instance.get_stats()
        
        return all_stats
    
    async def invalidate_cache(
        self, 
        cache_name: Optional[str] = None,
        *args, 
        **kwargs
    ) -> None:
        """使缓存失效"""
        if cache_name:
            cache_instance = self.cache_instances.get(cache_name)
            if cache_instance:
                await cache_instance.invalidate(*args, **kwargs)
        else:
            # 失效所有缓存
            for cache_instance in self.cache_instances.values():
                try:
                    await cache_instance.invalidate(*args, **kwargs)
                except Exception as e:
                    logger.error(f"缓存失效失败: {e}")


# 全局缓存管理器实例
_cache_manager = CacheManager()


def get_cache_manager() -> CacheManager:
    """获取全局缓存管理器"""
    return _cache_manager


# ============ 周期数据装饰器 ============

def period_data(period: str):
    """
    周期数据装饰器
    
    简化不同周期数据的获取，自动根据period参数调用相应的数据管理器方法。
    
    Args:
        period: 数据周期 ('daily', 'weekly', 'monthly')
    
    Example:
        @period_data('weekly')
        async def get_stock_data(stock_code: str, start_date=None, end_date=None):
            # 装饰器会自动调用 data_manager.get_stock_weekly()
            pass
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 提取参数
            if args:
                stock_code = args[0]
            else:
                stock_code = kwargs.get('stock_code')
                
            if not stock_code:
                raise ValueError("stock_code parameter is required")
            
            start_date = kwargs.get('start_date')
            end_date = kwargs.get('end_date')
            
            # 获取数据管理器
            data_manager = get_data_manager()
            
            # 根据周期调用相应方法
            if period == 'daily':
                return await data_manager.get_stock_daily(
                    stock_code=stock_code,
                    start_date=start_date,
                    end_date=end_date,
                    **{k: v for k, v in kwargs.items() if k not in ['stock_code', 'start_date', 'end_date']}
                )
            elif period == 'weekly':
                return await data_manager.get_stock_weekly(
                    stock_code=stock_code,
                    start_date=start_date,
                    end_date=end_date,
                    **{k: v for k, v in kwargs.items() if k not in ['stock_code', 'start_date', 'end_date']}
                )
            elif period == 'monthly':
                return await data_manager.get_stock_monthly(
                    stock_code=stock_code,
                    start_date=start_date,
                    end_date=end_date,
                    **{k: v for k, v in kwargs.items() if k not in ['stock_code', 'start_date', 'end_date']}
                )
            else:
                raise ValueError(f"Unsupported period: {period}")
                
        return wrapper
    return decorator


def multi_period_data(periods: List[str] = None):
    """
    多周期数据装饰器
    
    允许函数同时获取多个周期的数据。
    
    Args:
        periods: 周期列表，默认为 ['daily', 'weekly', 'monthly']
    
    Example:
        @multi_period_data(['daily', 'weekly'])
        async def get_multi_data(stock_code: str, start_date=None, end_date=None):
            # 返回 {'daily': daily_result, 'weekly': weekly_result}
            pass
    """
    if periods is None:
        periods = ['daily', 'weekly', 'monthly']
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 提取参数
            if args:
                stock_code = args[0]
            else:
                stock_code = kwargs.get('stock_code')
                
            if not stock_code:
                raise ValueError("stock_code parameter is required")
            
            start_date = kwargs.get('start_date')
            end_date = kwargs.get('end_date')
            
            # 获取数据管理器
            data_manager = get_data_manager()
            
            # 并发获取多个周期的数据
            tasks = {}
            extra_kwargs = {k: v for k, v in kwargs.items() if k not in ['stock_code', 'start_date', 'end_date']}
            
            for period in periods:
                if period == 'daily':
                    tasks[period] = data_manager.get_stock_daily(
                        stock_code=stock_code,
                        start_date=start_date,
                        end_date=end_date,
                        **extra_kwargs
                    )
                elif period == 'weekly':
                    tasks[period] = data_manager.get_stock_weekly(
                        stock_code=stock_code, 
                        start_date=start_date,
                        end_date=end_date,
                        **extra_kwargs
                    )
                elif period == 'monthly':
                    tasks[period] = data_manager.get_stock_monthly(
                        stock_code=stock_code,
                        start_date=start_date,
                        end_date=end_date,
                        **extra_kwargs
                    )
            
            # 等待所有任务完成
            results = {}
            for period, task in tasks.items():
                try:
                    results[period] = await task
                except Exception as e:
                    logger.error(f"获取{period}数据失败: {e}")
                    results[period] = None
            
            return results
                
        return wrapper
    return decorator


def validate_stock_code(func: Callable) -> Callable:
    """
    股票代码验证装饰器
    
    验证股票代码格式的合法性
    """
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        # 提取股票代码
        stock_code = None
        if args:
            stock_code = args[0]
        elif 'stock_code' in kwargs:
            stock_code = kwargs['stock_code']
        
        if not stock_code:
            raise ValueError("stock_code parameter is required")
        
        # 基本格式验证
        if not isinstance(stock_code, str) or len(stock_code) < 6:
            raise ValueError(f"Invalid stock code format: {stock_code}")
        
        # 提取数字部分进行验证
        code_part = stock_code.split('.')[0] if '.' in stock_code else stock_code
        if not code_part.isdigit():
            raise ValueError(f"Stock code must contain only digits: {stock_code}")
        
        return await func(*args, **kwargs)
        
    return wrapper


def format_dates(func: Callable) -> Callable:
    """
    日期格式化装饰器
    
    自动格式化日期参数为标准格式
    """
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        # 格式化 start_date
        if 'start_date' in kwargs and kwargs['start_date']:
            kwargs['start_date'] = _format_date(kwargs['start_date'])
        
        # 格式化 end_date
        if 'end_date' in kwargs and kwargs['end_date']:
            kwargs['end_date'] = _format_date(kwargs['end_date'])
        
        return await func(*args, **kwargs)
        
    return wrapper


def _format_date(date_input: Union[str, datetime.date, datetime]) -> str:
    """格式化日期为字符串"""
    if isinstance(date_input, str):
        # 尝试解析常见的日期格式
        for fmt in ['%Y-%m-%d', '%Y%m%d', '%Y/%m/%d']:
            try:
                parsed_date = datetime.strptime(date_input, fmt)
                return parsed_date.strftime('%Y-%m-%d')
            except ValueError:
                continue
        raise ValueError(f"Unsupported date format: {date_input}")
    elif isinstance(date_input, datetime):
        return date_input.strftime('%Y-%m-%d')
    elif isinstance(date_input, datetime.date):
        return date_input.strftime('%Y-%m-%d')
    else:
        raise ValueError(f"Unsupported date type: {type(date_input)}")


# 组合装饰器
def robust_period_data(period: str, max_retries: int = 2, cache_ttl: int = 300):
    """
    鲁棒的周期数据装饰器
    
    结合了重试、缓存、日志记录和数据验证功能
    
    Args:
        period: 数据周期
        max_retries: 最大重试次数（预留参数，用于未来扩展）
        cache_ttl: 缓存时间
    """
    def decorator(func: Callable) -> Callable:
        # 应用多个装饰器
        decorated_func = func
        decorated_func = validate_stock_code(decorated_func)
        decorated_func = format_dates(decorated_func)
        decorated_func = period_data(period)(decorated_func)
        
        # 根据周期应用相应的缓存策略
        if period == 'daily':
            decorated_func = cache_stock_daily(cache_ttl)(decorated_func)
        elif period == 'weekly':
            decorated_func = cache_stock_weekly(cache_ttl)(decorated_func)
        elif period == 'monthly':
            decorated_func = cache_stock_monthly(cache_ttl)(decorated_func)
        
        # 注意：max_retries 参数预留用于未来的重试机制扩展
        logger.debug(f"装饰器配置: period={period}, cache_ttl={cache_ttl}, max_retries={max_retries}")
        
        return decorated_func
    return decorator