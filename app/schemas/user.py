"""
用户相关的模式验证模型
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, EmailStr, Field

class UserBase(BaseModel):
    """用户基础信息"""
    username: str = Field(..., min_length=3, max_length=50)
    email: Optional[EmailStr] = None
    is_active: bool = True
    is_admin: bool = False

class UserCreate(UserBase):
    """创建用户请求体"""
    password: str = Field(..., min_length=6)

class UserLogin(BaseModel):
    """用户登录请求体"""
    username: str
    password: str

class UserResponse(UserBase):
    """用户响应模型"""
    id: int
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime] = None

    class Config:
        from_attributes = True
