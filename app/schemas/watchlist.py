from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

class WatchlistItemBase(BaseModel):
    stock_code: str = Field(..., description="股票代码", example="600000")

class WatchlistItemCreate(WatchlistItemBase):
    # 不再需要用户手动提供user_id，将从认证上下文获取
    sort_order: Optional[int] = Field(0, description="排序顺序", example=0)
    # price_alert: Optional[str] = Field(None, description="价格提醒设置(JSON)")

class WatchlistItemUpdate(BaseModel):
    # 用于更新单个自选股项目，例如排序或提醒
    sort_order: Optional[int] = Field(None, description="排序顺序")
    # price_alert: Optional[str] = Field(None, description="价格提醒设置(JSON)")

class WatchlistBulkUpdate(BaseModel):
    stock_codes: List[str] = Field(..., description="股票代码列表，按顺序排列")

class WatchlistItemResponse(WatchlistItemBase):
    id: int
    user_id: int # 用户ID现在是整数类型
    added_at: datetime # 添加时间
    sort_order: int # 排序顺序
    # price_alert: Optional[Dict[str, Any]] = None # 如果 price_alert 是JSON，可以解析为字典
    stock_info: Optional[Dict[str, Any]] = Field(None, description="股票详情")

    class Config:
        from_attributes = True
