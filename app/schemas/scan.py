"""
扫描服务的请求和响应模型
"""
from typing import List, Dict, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum

from app.services.scan.models import ScanStatus, SignalType


class SessionCreateRequest(BaseModel):
    """会话创建请求"""
    user_id: Optional[str] = Field(None, description="用户ID")
    metadata: Optional[Dict[str, Any]] = Field(None, description="会话元数据")


class SessionCreateResponse(BaseModel):
    """会话创建响应"""
    session_id: str = Field(..., description="会话ID")
    user_id: Optional[str] = Field(None, description="用户ID")
    created_at: datetime = Field(..., description="创建时间")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="会话元数据")


class SessionInfoResponse(BaseModel):
    """会话信息响应"""
    session_id: str = Field(..., description="会话ID")
    user_id: Optional[str] = Field(None, description="用户ID")
    created_at: datetime = Field(..., description="创建时间")
    last_active: datetime = Field(..., description="最后活跃时间")
    scan_task_count: int = Field(..., description="扫描任务数量")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="会话元数据")


class KdjParameters(BaseModel):
    """KDJ指标参数"""
    n: int = Field(20, ge=5, le=100, description="KDJ周期")
    m1: int = Field(3, ge=1, le=20, description="K值平滑周期")
    m2: int = Field(3, ge=1, le=20, description="D值平滑周期")


class VolumeParameters(BaseModel):
    """成交量压力指标参数"""
    ema_period: int = Field(10, ge=3, le=50, description="EMA周期")


class BollingerParameters(BaseModel):
    """布林带指标参数"""
    window: int = Field(20, ge=5, le=100, description="布林带窗口大小")
    std_dev: float = Field(2.0, ge=0.5, le=5.0, description="标准差倍数")


class IndicatorParameters(BaseModel):
    """指标参数配置"""
    kdj: Optional[KdjParameters] = Field(None, description="KDJ指标参数")
    volume_pressure: Optional[VolumeParameters] = Field(None, description="成交量压力参数")
    bollinger: Optional[BollingerParameters] = Field(None, description="布林带参数")


class PeriodIndicatorParameters(BaseModel):
    """多周期指标参数配置"""
    d: Optional[IndicatorParameters] = Field(None, description="日线参数")
    w: Optional[IndicatorParameters] = Field(None, description="周线参数")
    m: Optional[IndicatorParameters] = Field(None, description="月线参数")
    y: Optional[IndicatorParameters] = Field(None, description="年线参数")


class ScanStartRequest(BaseModel):
    """扫描启动请求"""
    indicators: List[str] = Field(..., description="要扫描的指标列表")
    stock_codes: Optional[List[str]] = Field(None, description="要扫描的股票代码列表")
    parameters: Optional[IndicatorParameters] = Field(None, description="指标参数配置（传统模式使用）")
    # 多周期扫描相关字段
    scan_mode: str = Field("traditional", description="扫描模式：traditional(传统)/multi_period(多周期)")
    scan_strategy: str = Field("parallel", description="多周期策略：parallel(并行计算)/cascade(多层级复筛)")
    periods: List[str] = Field(["d"], description="扫描周期：d(日线)/w(周线)/m(月线)")
    adjust: str = Field("n", description="复权方式：n(不复权)/f(前复权)/b(后复权)")
    # 新增：多周期参数和指标配置
    period_parameters: Optional[PeriodIndicatorParameters] = Field(None, description="多周期参数配置")
    period_indicators: Optional[Dict[str, List[str]]] = Field(
        None, 
        description="分周期指标配置：{'d': ['kdj'], 'w': ['volume_pressure'], 'm': ['bollinger']}"
    )
    # 历史回测支持
    end_date: Optional[str] = Field(None, description="数据截止日期，格式：YYYY-MM-DD，默认为最近交易日")
    
    @validator('scan_mode')
    def validate_scan_mode(cls, v):
        """验证扫描模式"""
        valid_modes = ['traditional', 'multi_period']
        if v not in valid_modes:
            raise ValueError(f'Invalid scan_mode. Must be one of: {valid_modes}')
        return v
    
    @validator('scan_strategy')
    def validate_scan_strategy(cls, v):
        """验证扫描策略"""
        valid_strategies = ['parallel', 'cascade']
        if v not in valid_strategies:
            raise ValueError(f'Invalid scan_strategy. Must be one of: {valid_strategies}')
        return v
    
    @validator('periods')
    def validate_periods(cls, v, values):
        """验证周期配置"""
        valid_periods = ['d', 'w', 'm', 'y']
        if not v:
            raise ValueError('periods cannot be empty')
        
        for period in v:
            if period not in valid_periods:
                raise ValueError(f'Invalid period: {period}. Must be one of: {valid_periods}')
        
        # 检查传统模式的周期限制
        scan_mode = values.get('scan_mode', 'traditional')
        if scan_mode == 'traditional' and len(v) > 1:
            raise ValueError('Traditional scan mode only supports single period')
        
        # 移除重复周期
        unique_periods = list(dict.fromkeys(v))
        if len(unique_periods) != len(v):
            return unique_periods
        
        return v
    
    @validator('adjust')
    def validate_adjust(cls, v):
        """验证复权方式"""
        valid_adjusts = ['n', 'f', 'b', 'fr', 'br']
        if v not in valid_adjusts:
            raise ValueError(f'Invalid adjust. Must be one of: {valid_adjusts}')
        return v
    
    @validator('indicators')
    def validate_indicators(cls, v):
        """验证指标列表"""
        valid_indicators = ['kdj', 'volume_pressure', 'bollinger', 'macd', 'rsi', 'arbr']
        
        for indicator in v:
            if indicator not in valid_indicators:
                raise ValueError(f'Invalid indicator: {indicator}. Must be one of: {valid_indicators}')
        
        return v
    
    @validator('period_indicators')
    def validate_period_indicators(cls, v, values):
        """验证分周期指标配置"""
        if v is None:
            return v
            
        scan_mode = values.get('scan_mode', 'traditional')
        if scan_mode == 'traditional':
            # 传统模式不需要分周期指标配置
            return None
            
        valid_indicators = ['kdj', 'volume_pressure', 'bollinger', 'macd', 'rsi', 'arbr']
        valid_periods = ['d', 'w', 'm', 'y']
        
        for period, indicators in v.items():
            if period not in valid_periods:
                raise ValueError(f'Invalid period: {period}. Must be one of: {valid_periods}')
            
            if not isinstance(indicators, list):
                raise ValueError(f'Indicators for period {period} must be a list')
                
            for indicator in indicators:
                if indicator not in valid_indicators:
                    raise ValueError(f'Invalid indicator: {indicator}. Must be one of: {valid_indicators}')
        
        return v
    
    @validator('period_parameters')
    def validate_period_parameters(cls, v, values):
        """验证多周期参数配置"""
        if v is None:
            return v
            
        scan_mode = values.get('scan_mode', 'traditional')
        if scan_mode == 'traditional':
            # 传统模式不需要多周期参数配置
            return None
            
        return v
    
    @validator('end_date')
    def validate_end_date(cls, v):
        """验证数据截止日期格式"""
        if v is None:
            return v
        
        from datetime import datetime
        try:
            datetime.strptime(v, '%Y-%m-%d')
            return v
        except ValueError:
            raise ValueError('end_date must be in YYYY-MM-DD format')


class ScanStartResponse(BaseModel):
    """扫描启动响应"""
    task_id: str = Field(..., description="扫描任务ID")
    status: ScanStatus = Field(..., description="任务状态")
    created_at: datetime = Field(..., description="创建时间")


class ScanProgressResponse(BaseModel):
    """扫描进度响应"""
    task_id: str = Field(..., description="任务ID")
    status: ScanStatus = Field(..., description="任务状态")
    total: int = Field(..., description="总数")
    current: int = Field(..., description="当前进度")
    percentage: float = Field(..., description="完成百分比")
    message: Optional[str] = Field(None, description="进度消息")


class StockIndicatorDataResponse(BaseModel):
    """股票指标数据响应"""
    kdj_k: float = Field(..., description="KDJ-K值")
    kdj_d: Optional[float] = Field(None, description="KDJ-D值")
    kdj_j: Optional[float] = Field(None, description="KDJ-J值")
    volume_pressure: float = Field(..., description="成交量压力指标")
    bollinger_upper: float = Field(..., description="布林带上轨")
    bollinger_middle: float = Field(..., description="布林带中轨")
    bollinger_lower: float = Field(..., description="布林带下轨")
    dif: float = Field(..., description="DIF差离值")
    dea: float = Field(..., description="DEA信号线")
    macd: float = Field(..., description="MACD柱状图")
    rsi: float = Field(..., description="RSI值")
    arbr_ar: float = Field(..., description="ARBR-AR值")
    arbr_br: float = Field(..., description="ARBR-BR值")
    
    # 新增字段用于增强显示
    prev_kdj_k: float = Field(0.0, description="前一日KDJ-K值")
    prev_kdj_d: Optional[float] = Field(None, description="前一日KDJ-D值")
    volume_pressure_avg: float = Field(..., description="成交量压力20日平均")
    close_price: float = Field(..., description="收盘价")
    bollinger_distance_pct: float = Field(0.0, description="收盘价距离下轨百分比")


class ScanResultResponse(BaseModel):
    """扫描结果响应"""
    stock_code: str = Field(..., description="股票代码")
    stock_name: str = Field(..., description="股票名称")
    signals: List[SignalType] = Field(..., description="信号列表")
    indicator_data: StockIndicatorDataResponse = Field(..., description="指标数据")
    price: float = Field(..., description="当前价格")
    change_percent: float = Field(..., description="涨跌幅")
    scan_time: datetime = Field(..., description="扫描时间")
    period: str = Field("d", description="数据周期：d(日线)/w(周线)/m(月线)")
    end_date: Optional[str] = Field(None, description="数据截止日期")


class ScanTaskResponse(BaseModel):
    """扫描任务响应"""
    task_id: str = Field(..., description="任务ID")
    status: ScanStatus = Field(..., description="任务状态")
    indicators: List[str] = Field(..., description="扫描指标")
    progress: ScanProgressResponse = Field(..., description="进度信息")
    result_count: int = Field(..., description="结果数量")
    created_at: datetime = Field(..., description="创建时间")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    error_message: Optional[str] = Field(None, description="错误信息")
    end_date: Optional[str] = Field(None, description="数据截止日期")


class ScanResultsResponse(BaseModel):
    """扫描结果列表响应"""
    task_id: str = Field(..., description="任务ID")
    results: List[ScanResultResponse] = Field(..., description="结果列表")
    total_count: int = Field(..., description="总结果数")
    page: int = Field(1, description="当前页码")
    page_size: int = Field(20, description="每页大小")
    end_date: Optional[str] = Field(None, description="数据截止日期")