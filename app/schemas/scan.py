"""
扫描服务的请求和响应模型
"""
from typing import List, Dict, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum

from app.services.scan.models import ScanStatus, SignalType


class SessionCreateRequest(BaseModel):
    """会话创建请求"""
    user_id: Optional[str] = Field(None, description="用户ID")
    metadata: Optional[Dict[str, Any]] = Field(None, description="会话元数据")


class SessionCreateResponse(BaseModel):
    """会话创建响应"""
    session_id: str = Field(..., description="会话ID")
    user_id: Optional[str] = Field(None, description="用户ID")
    created_at: datetime = Field(..., description="创建时间")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="会话元数据")


class SessionInfoResponse(BaseModel):
    """会话信息响应"""
    session_id: str = Field(..., description="会话ID")
    user_id: Optional[str] = Field(None, description="用户ID")
    created_at: datetime = Field(..., description="创建时间")
    last_active: datetime = Field(..., description="最后活跃时间")
    scan_task_count: int = Field(..., description="扫描任务数量")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="会话元数据")


class KdjParameters(BaseModel):
    """KDJ指标参数"""
    n: int = Field(20, ge=5, le=100, description="KDJ周期")
    m1: int = Field(3, ge=1, le=20, description="K值平滑周期")
    m2: int = Field(3, ge=1, le=20, description="D值平滑周期")


class VolumeParameters(BaseModel):
    """成交量压力指标参数"""
    ema_period: int = Field(10, ge=3, le=50, description="EMA周期")


class BollingerParameters(BaseModel):
    """布林带指标参数"""
    window: int = Field(20, ge=5, le=100, description="布林带窗口大小")
    std_dev: float = Field(2.0, ge=0.5, le=5.0, description="标准差倍数")


class IndicatorParameters(BaseModel):
    """指标参数配置"""
    kdj: Optional[KdjParameters] = Field(None, description="KDJ指标参数")
    volume_pressure: Optional[VolumeParameters] = Field(None, description="成交量压力参数")
    bollinger: Optional[BollingerParameters] = Field(None, description="布林带参数")


class ScanStartRequest(BaseModel):
    """扫描启动请求"""
    indicators: List[str] = Field(..., description="要扫描的指标列表")
    stock_codes: Optional[List[str]] = Field(None, description="要扫描的股票代码列表")
    parameters: Optional[IndicatorParameters] = Field(None, description="指标参数配置")


class ScanStartResponse(BaseModel):
    """扫描启动响应"""
    task_id: str = Field(..., description="扫描任务ID")
    session_id: str = Field(..., description="会话ID")
    status: ScanStatus = Field(..., description="任务状态")
    created_at: datetime = Field(..., description="创建时间")


class ScanProgressResponse(BaseModel):
    """扫描进度响应"""
    task_id: str = Field(..., description="任务ID")
    status: ScanStatus = Field(..., description="任务状态")
    total: int = Field(..., description="总数")
    current: int = Field(..., description="当前进度")
    percentage: float = Field(..., description="完成百分比")
    message: Optional[str] = Field(None, description="进度消息")


class StockIndicatorDataResponse(BaseModel):
    """股票指标数据响应"""
    kdj_k: float = Field(..., description="KDJ-K值")
    kdj_d: Optional[float] = Field(None, description="KDJ-D值")
    kdj_j: Optional[float] = Field(None, description="KDJ-J值")
    volume_pressure: float = Field(..., description="成交量压力指标")
    bollinger_upper: float = Field(..., description="布林带上轨")
    bollinger_middle: float = Field(..., description="布林带中轨")
    bollinger_lower: float = Field(..., description="布林带下轨")
    macd: float = Field(..., description="MACD值")
    macd_signal: float = Field(..., description="MACD信号线")
    macd_histogram: float = Field(..., description="MACD柱状图")
    rsi: float = Field(..., description="RSI值")
    arbr_ar: float = Field(..., description="ARBR-AR值")
    arbr_br: float = Field(..., description="ARBR-BR值")
    
    # 新增字段用于增强显示
    prev_kdj_k: float = Field(0.0, description="前一日KDJ-K值")
    prev_kdj_d: Optional[float] = Field(None, description="前一日KDJ-D值")
    volume_pressure_avg: float = Field(..., description="成交量压力20日平均")
    close_price: float = Field(..., description="收盘价")
    bollinger_distance_pct: float = Field(0.0, description="收盘价距离下轨百分比")


class ScanResultResponse(BaseModel):
    """扫描结果响应"""
    stock_code: str = Field(..., description="股票代码")
    stock_name: str = Field(..., description="股票名称")
    signals: List[SignalType] = Field(..., description="信号列表")
    indicator_data: StockIndicatorDataResponse = Field(..., description="指标数据")
    price: float = Field(..., description="当前价格")
    change_percent: float = Field(..., description="涨跌幅")
    scan_time: datetime = Field(..., description="扫描时间")


class ScanTaskResponse(BaseModel):
    """扫描任务响应"""
    task_id: str = Field(..., description="任务ID")
    session_id: str = Field(..., description="会话ID")
    status: ScanStatus = Field(..., description="任务状态")
    indicators: List[str] = Field(..., description="扫描指标")
    progress: ScanProgressResponse = Field(..., description="进度信息")
    result_count: int = Field(..., description="结果数量")
    created_at: datetime = Field(..., description="创建时间")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    error_message: Optional[str] = Field(None, description="错误信息")


class ScanResultsResponse(BaseModel):
    """扫描结果列表响应"""
    task_id: str = Field(..., description="任务ID")
    results: List[ScanResultResponse] = Field(..., description="结果列表")
    total_count: int = Field(..., description="总结果数")
    page: int = Field(1, description="当前页码")
    page_size: int = Field(20, description="每页大小")