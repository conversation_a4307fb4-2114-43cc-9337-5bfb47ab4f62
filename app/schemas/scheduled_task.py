"""定时任务相关Schema"""

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class TaskType(str, Enum):
    """任务类型枚举"""
    INDICATOR_SCAN = "indicator_scan"
    AI_ANALYSIS = "ai_analysis"  # 预留

class TriggerType(str, Enum):
    """触发类型枚举"""
    SCHEDULED = "scheduled"
    MANUAL = "manual"

class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running" 
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class IndicatorScanConfig(BaseModel):
    """指标扫描任务配置"""
    indicators: List[str] = Field(..., description="扫描指标列表")
    stock_codes: Optional[List[str]] = Field(None, description="股票代码列表")
    parameters: Optional[Dict[str, Any]] = Field(None, description="指标参数")
    scan_mode: str = Field("traditional", description="扫描模式")
    periods: List[str] = Field(["d"], description="扫描周期")
    adjust: str = Field("n", description="复权方式")
    end_date: Optional[str] = Field(None, description="数据截止日期，用于历史回测（格式：YYYY-MM-DD）")

class FormattedTaskConfig(BaseModel):
    """格式化的任务配置显示"""
    config_type: str = Field(..., description="配置类型")
    display_name: str = Field(..., description="显示名称")
    summary: str = Field(..., description="配置摘要")
    details: Dict[str, Any] = Field(..., description="格式化的配置详情")
    raw_config: Optional[Dict[str, Any]] = Field(None, description="原始配置(可选)")
    
class TaskConfigFormatter:
    """任务配置格式化工具类"""
    
    @staticmethod
    def format_indicator_scan_config(config: Dict[str, Any]) -> FormattedTaskConfig:
        """格式化指标扫描配置"""
        indicators = config.get('indicators', [])
        periods = config.get('periods', ['d'])
        scan_mode = config.get('scan_mode', 'traditional')
        stock_codes = config.get('stock_codes')
        parameters = config.get('parameters', {})
        
        # 生成人性化的指标名称映射
        indicator_names = {
            'volume_pressure': '成交量压力',
            'kdj': 'KDJ随机指标',
            'bollinger': '布林带',
            'macd': 'MACD',
            'rsi': 'RSI相对强弱指标',
            'arbr': 'ARBR人气意愿指标'
        }
        
        # 生成周期名称映射
        period_names = {
            'd': '日线',
            'w': '周线', 
            'm': '月线'
        }
        
        # 生成扫描模式名称
        mode_names = {
            'traditional': '传统扫描',
            'multi_period': '多周期扫描'
        }
        
        # 复权方式名称
        adjust_names = {
            'n': '不复权',
            'qfq': '前复权',
            'hfq': '后复权'
        }
        
        # 构建显示内容
        indicator_display = [indicator_names.get(ind, ind) for ind in indicators]
        period_display = [period_names.get(p, p) for p in periods]
        
        summary_parts = [
            f"扫描指标: {', '.join(indicator_display)}",
            f"数据周期: {', '.join(period_display)}",
            f"扫描模式: {mode_names.get(scan_mode, scan_mode)}"
        ]
        
        if stock_codes:
            if len(stock_codes) <= 5:
                summary_parts.append(f"指定股票: {', '.join(stock_codes)}")
            else:
                summary_parts.append(f"指定股票: {len(stock_codes)}只 ({', '.join(stock_codes[:3])}, ...)")
        else:
            summary_parts.append("扫描范围: 全市场")
            
        summary = " | ".join(summary_parts)
        
        # 构建详细信息
        details = {
            "扫描指标": {
                "指标列表": indicator_display,
                "指标参数": parameters if parameters else "使用默认参数"
            },
            "扫描设置": {
                "数据周期": period_display,
                "扫描模式": mode_names.get(scan_mode, scan_mode),
                "复权方式": adjust_names.get(config.get('adjust', 'n'), '不复权')
            },
            "股票范围": {
                "类型": "指定股票" if stock_codes else "全市场扫描",
                "数量": len(stock_codes) if stock_codes else "全部",
                "股票列表": stock_codes[:10] if stock_codes else None  # 最多显示10只
            }
        }
        
        return FormattedTaskConfig(
            config_type="indicator_scan",
            display_name="指标扫描任务",
            summary=summary,
            details=details,
            raw_config=config
        )
    
    @classmethod
    def format_task_config(cls, task_type: str, config: Dict[str, Any]) -> FormattedTaskConfig:
        """根据任务类型格式化配置"""
        if task_type == "indicator_scan":
            return cls.format_indicator_scan_config(config)
        else:
            # 未知任务类型的默认处理
            return FormattedTaskConfig(
                config_type=task_type,
                display_name=f"{task_type}任务",
                summary=f"任务类型: {task_type}",
                details={"配置内容": config},
                raw_config=config
            )

class UserScheduledTaskCreate(BaseModel):
    """创建定时任务请求"""
    name: str = Field(..., max_length=100, description="任务名称")
    task_type: TaskType = Field(TaskType.INDICATOR_SCAN, description="任务类型")
    cron_expression: str = Field(..., max_length=100, description="Cron表达式")
    task_config: IndicatorScanConfig = Field(..., description="任务配置")
    description: Optional[str] = Field(None, max_length=500, description="任务描述")
    max_executions: Optional[int] = Field(None, description="最大执行次数")

class UserScheduledTaskUpdate(BaseModel):
    """更新定时任务请求"""
    name: Optional[str] = Field(None, max_length=100, description="任务名称")
    cron_expression: Optional[str] = Field(None, max_length=100, description="Cron表达式")
    task_config: Optional[IndicatorScanConfig] = Field(None, description="任务配置")
    description: Optional[str] = Field(None, max_length=500, description="任务描述")
    max_executions: Optional[int] = Field(None, description="最大执行次数")
    is_active: Optional[bool] = Field(None, description="是否启用")

class UserScheduledTaskResponse(BaseModel):
    """定时任务响应"""
    id: int
    name: str
    task_type: TaskType
    cron_expression: str
    is_active: bool
    max_executions: Optional[int]
    current_executions: int
    task_config: Dict[str, Any]
    description: Optional[str]
    last_execution: Optional[datetime]
    next_execution: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class TaskExecutionResponse(BaseModel):
    """任务执行记录响应"""
    id: int
    scheduled_task_id: Optional[int]
    trigger_type: TriggerType
    task_type: TaskType
    status: TaskStatus
    start_time: Optional[datetime]
    end_time: Optional[datetime]
    duration_seconds: Optional[int]
    results_count: int
    error_message: Optional[str]
    results_data: Optional[List[Dict[str, Any]]] = Field(None, description="任务执行结果数据")
    task_config: Optional[FormattedTaskConfig] = Field(None, description="格式化的任务配置")
    task_config_raw: Optional[Dict[str, Any]] = Field(None, description="原始任务配置")
    created_at: datetime
    
    # 关联任务信息
    scheduled_task_name: Optional[str] = Field(None, description="定时任务名称")
    
    # 管理员查看需要的用户信息
    user_email: Optional[str] = Field(None, description="执行用户邮箱")
    user_username: Optional[str] = Field(None, description="执行用户名")

    class Config:
        from_attributes = True

class TaskExecutionListResponse(BaseModel):
    """任务执行记录列表响应"""
    total: int
    items: List[TaskExecutionResponse]