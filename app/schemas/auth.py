"""认证相关的模式定义"""
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional
from datetime import datetime


class LoginRequest(BaseModel):
    """登录请求"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    password: str = Field(..., min_length=6, description="密码")
    remember_me: bool = Field(False, description="是否记住我")


class LoginResponse(BaseModel):
    """登录响应"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field("bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间(秒)")
    user: Dict[str, Any] = Field(..., description="用户信息")


class TokenRefreshRequest(BaseModel):
    """Token刷新请求"""
    token: str = Field(..., description="旧的访问令牌")


class UserProfileResponse(BaseModel):
    """用户个人信息响应"""
    id: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    email: Optional[str] = Field(None, description="邮箱")
    is_admin: bool = Field(..., description="是否是管理员")
    last_login: Optional[datetime] = Field(None, description="最后登录时间")
    
    class Config:
        from_attributes = True


class LogoutResponse(BaseModel):
    """登出响应"""
    message: str = Field(..., description="响应消息")