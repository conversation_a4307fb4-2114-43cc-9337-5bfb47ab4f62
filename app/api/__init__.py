"""API路由配置"""
from fastapi import APIRouter
from app.core.config import settings # Import settings
from app.api.endpoints import indicators
from app.api.endpoints import advanced_indicators
from app.api.endpoints.analytics import kline
from app.api.endpoints import sync_watchlist # 保留同步的 watchlist 路由
from app.api.endpoints import users # 导入用户路由
from app.api.endpoints import cache # 导入缓存管理路由
from app.api.endpoints import auth # 导入认证路由

# 创建主路由器, 使用  前缀
api_router = APIRouter(prefix=settings.API_V1_STR)

# 注册认证路由（不需要认证的公开端点）
api_router.include_router(auth.router, tags=["Authentication"])

# 注册各模块路由
api_router.include_router(indicators.router, prefix="/indicators", tags=["Indicators"]) # Add prefix and tags
api_router.include_router(advanced_indicators.router, prefix="/advanced-indicators", tags=["Advanced Indicators"]) # Add new advanced indicators
api_router.include_router(kline.router, prefix="/analytics", tags=["Analytics"]) # Add tags

# 注册同步版API路由
api_router.include_router(sync_watchlist.router, tags=["Watchlist"]) # 注册同步 watchlist 路由，并确保 tags 一致
api_router.include_router(users.router) # 注册用户路由

# 注册缓存管理路由
api_router.include_router(cache.router, tags=["Cache Management"])

# 只在开发环境下注册股票API路由(包括股票列表和股票数据)
# if settings.APP_ENV.lower() == "development":
#     from app.api.endpoints import stocks
#     api_router.include_router(stocks.router, prefix="/stocks", tags=["Stocks"])
#     print(f"股票API已启用 - 当前环境: {settings.APP_ENV}")
# else:
#     print(f"股票API已关闭 - 当前环境: {settings.APP_ENV}")

from app.api.endpoints import stocks
api_router.include_router(stocks.router, prefix="/stocks", tags=["Stocks"])

# 注册指数API路由
from app.api.endpoints import indices
api_router.include_router(indices.router, prefix="/indices", tags=["Indices"])

# 注册指数分析API路由
from app.api.endpoints.analytics import index_kline
from app.api.endpoints import index_indicators
api_router.include_router(index_kline.router, prefix="/analytics", tags=["Index Analytics"])
api_router.include_router(index_indicators.router, prefix="/index-indicators", tags=["Index Indicators"])

# 注册扫描服务路由
from app.api.endpoints import scan_session, scan
api_router.include_router(scan_session.router, prefix="/scan", tags=["Scan Session"])
api_router.include_router(scan.router, prefix="/scan", tags=["Scan"])

# 注册队列监控路由
from app.api.endpoints import queue
api_router.include_router(queue.router, prefix="/queue", tags=["Queue Management"])

# 注册定时任务路由
from app.api.endpoints import scheduled_tasks, task_executions
api_router.include_router(scheduled_tasks.router, prefix="/scheduled-tasks", tags=["Scheduled Tasks"])
api_router.include_router(task_executions.router, prefix="/task-executions", tags=["Task Executions"])

# 注册交易日期路由
from app.api.endpoints import trading
api_router.include_router(trading.router, prefix="/trading", tags=["Trading"])
