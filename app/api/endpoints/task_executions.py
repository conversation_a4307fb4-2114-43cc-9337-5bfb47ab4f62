"""任务执行记录API端点"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import desc, and_, or_, select, func
from sqlalchemy.orm import selectinload
import json

from app.core.database import get_db
from app.utils.dependencies import get_current_user, get_current_admin_user
from app.models.user import User
from app.models.task import TaskExecution
from app.schemas.common import CommonResponse
from app.schemas.scheduled_task import (
    TaskExecutionResponse,
    TaskExecutionListResponse,
    TriggerType,
    TaskStatus,
    TaskConfigFormatter
)

router = APIRouter()

def _build_execution_response(execution: TaskExecution, include_user_info: bool = False) -> dict:
    """构建任务执行响应的共享函数"""
    item_dict = execution.__dict__.copy()
    
    # 添加用户信息（管理员视图）
    if include_user_info and execution.user:
        item_dict["user_email"] = execution.user.email
        item_dict["user_username"] = execution.user.username
    else:
        item_dict["user_email"] = None
        item_dict["user_username"] = None
    
    # 获取关联任务名称
    if execution.scheduled_task_id and execution.scheduled_task:
        item_dict["scheduled_task_name"] = execution.scheduled_task.name
    else:
        item_dict["scheduled_task_name"] = None
        
    # 解析results_data
    if execution.results_data:
        try:
            item_dict["results_data"] = json.loads(execution.results_data)
        except:
            item_dict["results_data"] = None
    else:
        item_dict["results_data"] = None
    
    # 解析task_config
    if execution.task_config:
        try:
            raw_config = json.loads(execution.task_config)
            # 使用格式化工具格式化配置
            formatted_config = TaskConfigFormatter.format_task_config(execution.task_type, raw_config)
            item_dict["task_config"] = formatted_config
            item_dict["task_config_raw"] = raw_config
        except Exception as e:
            # 解析失败，提供原始配置
            item_dict["task_config"] = None
            item_dict["task_config_raw"] = None
    else:
        item_dict["task_config"] = None
        item_dict["task_config_raw"] = None
        
    return item_dict

@router.get("/", response_model=CommonResponse[TaskExecutionListResponse])
async def get_all_task_executions(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=500),
    trigger_type: Optional[TriggerType] = Query(None, description="按触发类型过滤"),
    status: Optional[TaskStatus] = Query(None, description="按状态过滤"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取用户所有任务执行记录"""
    # 构建查询语句
    query = select(TaskExecution).where(TaskExecution.user_id == current_user.id)
    
    # 应用过滤条件
    if trigger_type:
        query = query.where(TaskExecution.trigger_type == trigger_type)
    
    if status:
        query = query.where(TaskExecution.status == status)
    
    # 获取总数
    count_query = select(func.count(TaskExecution.id)).where(TaskExecution.user_id == current_user.id)
    if trigger_type:
        count_query = count_query.where(TaskExecution.trigger_type == trigger_type)
    if status:
        count_query = count_query.where(TaskExecution.status == status)
    
    count_result = await db.execute(count_query)
    total = count_result.scalar()
    
    # 获取分页数据，预加载关联数据
    query = query.options(
        selectinload(TaskExecution.scheduled_task)
    ).order_by(desc(TaskExecution.created_at)).offset(skip).limit(limit)
    result = await db.execute(query)
    executions = result.scalars().all()
    
    # 构造响应
    items = [
        TaskExecutionResponse(**_build_execution_response(execution))
        for execution in executions
    ]
    
    task_list_response = TaskExecutionListResponse(total=total, items=items)
    return CommonResponse(
        success=True,
        message="获取任务执行记录成功",
        data=task_list_response
    )

@router.get("/admin/all", response_model=CommonResponse[TaskExecutionListResponse])
async def get_all_task_executions_admin(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=500),
    trigger_type: Optional[TriggerType] = Query(None, description="按触发类型过滤"),
    status: Optional[TaskStatus] = Query(None, description="按状态过滤"),
    user_id: Optional[int] = Query(None, description="按用户ID过滤"),
    db: AsyncSession = Depends(get_db),
    current_admin: User = Depends(get_current_admin_user)
):
    """管理员获取所有用户的任务执行记录"""
    # 构建查询语句 - 管理员可以查看所有用户的记录
    query = select(TaskExecution)
    
    # 应用过滤条件
    if trigger_type:
        query = query.where(TaskExecution.trigger_type == trigger_type)
    
    if status:
        query = query.where(TaskExecution.status == status)
        
    if user_id:
        query = query.where(TaskExecution.user_id == user_id)
    
    # 获取总数
    count_query = select(func.count(TaskExecution.id))
    if trigger_type:
        count_query = count_query.where(TaskExecution.trigger_type == trigger_type)
    if status:
        count_query = count_query.where(TaskExecution.status == status)
    if user_id:
        count_query = count_query.where(TaskExecution.user_id == user_id)
    
    count_result = await db.execute(count_query)
    total = count_result.scalar()
    
    # 获取分页数据，加载关联数据
    query = query.options(
        selectinload(TaskExecution.user),
        selectinload(TaskExecution.scheduled_task)
    ).order_by(desc(TaskExecution.created_at)).offset(skip).limit(limit)
    result = await db.execute(query)
    executions = result.scalars().all()
    
    # 构造响应
    items = [
        TaskExecutionResponse(**_build_execution_response(execution, include_user_info=True))
        for execution in executions
    ]
    
    task_list_response = TaskExecutionListResponse(total=total, items=items)
    return CommonResponse(
        success=True,
        message="获取任务执行记录成功",
        data=task_list_response
    )

@router.get("/{execution_id}", response_model=TaskExecutionResponse)
async def get_task_execution_detail(
    execution_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取任务执行记录详情"""
    exec_stmt = select(TaskExecution).options(
        selectinload(TaskExecution.scheduled_task)
    ).where(
        and_(TaskExecution.id == execution_id, TaskExecution.user_id == current_user.id)
    )
    exec_result = await db.execute(exec_stmt)
    execution = exec_result.scalar_one_or_none()
    
    if not execution:
        raise HTTPException(status_code=404, detail="执行记录不存在")
    
    return TaskExecutionResponse(**_build_execution_response(execution))

@router.delete("/{execution_id}")
async def delete_task_execution(
    execution_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除任务执行记录"""
    exec_stmt = select(TaskExecution).where(
        and_(TaskExecution.id == execution_id, TaskExecution.user_id == current_user.id)
    )
    exec_result = await db.execute(exec_stmt)
    execution = exec_result.scalar_one_or_none()
    
    if not execution:
        raise HTTPException(status_code=404, detail="执行记录不存在")
    
    # 检查是否可以删除（不能删除正在执行的任务）
    if execution.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
        raise HTTPException(status_code=400, detail="不能删除正在执行的任务记录")
    
    try:
        await db.delete(execution)
        await db.commit()
        return {"message": "执行记录已删除"}
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"删除执行记录失败: {str(e)}")

@router.post("/{execution_id}/cancel")
async def cancel_task_execution(
    execution_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """取消正在执行的任务"""
    exec_stmt = select(TaskExecution).where(
        and_(TaskExecution.id == execution_id, TaskExecution.user_id == current_user.id)
    )
    exec_result = await db.execute(exec_stmt)
    execution = exec_result.scalar_one_or_none()
    
    if not execution:
        raise HTTPException(status_code=404, detail="执行记录不存在")
    
    if execution.status not in [TaskStatus.PENDING, TaskStatus.RUNNING]:
        raise HTTPException(status_code=400, detail="只能取消待执行或正在执行的任务")
    
    try:
        # 尝试取消执行器中的任务
        from app.services.tasks.executor import get_task_executor
        executor = get_task_executor()
        cancelled = executor.cancel_execution(execution_id)
        
        # 更新数据库状态
        execution.status = TaskStatus.CANCELLED
        if not execution.end_time:
            from datetime import datetime
            execution.end_time = datetime.now()
            if execution.start_time:
                execution.duration_seconds = int(
                    (execution.end_time - execution.start_time).total_seconds()
                )
        
        await db.commit()
        
        return {
            "message": "任务取消成功" if cancelled else "任务取消请求已提交",
            "cancelled": cancelled
        }
        
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")