"""
高级技术指标分析API接口

基于 new_feature_formatted.md 文档实现的新功能：
- 多周期Bollinger Bands计算
- 成交量内外盘分析
- 基于成交量的Bollinger指标
- 买卖点信号计算
"""
from typing import List, Optional, Dict, Any
from datetime import date, timedelta, datetime
from fastapi import APIRouter, Depends, Query, HTTPException, Path
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.services.storage.stock_storage import StockStorageService
from app.services.indicators import AdvancedIndicatorService, PeriodType
from app.utils.decorators import smart_data_cache
from app.core.data.models import DataType, CacheStrategy
from app.schemas.common import CommonResponse

router = APIRouter(
    tags=["Advanced Indicators"],
    responses={404: {"description": "指标数据不存在"}}
)

# 请求和响应模型定义
class BollingerBandsResponse(BaseModel):
    """Bollinger Bands响应模型"""
    middle: List[float] = Field(..., description="中轨数据")
    upper: List[float] = Field(..., description="上轨数据") 
    lower: List[float] = Field(..., description="下轨数据")

class VolumeAnalysisResponse(BaseModel):
    """成交量分析响应模型"""
    in_volume: List[float] = Field(..., description="内盘成交量")
    ex_volume: List[float] = Field(..., description="外盘成交量")
    volume_difference: List[float] = Field(..., description="成交量差异")

class VolumeBollingerResponse(BaseModel):
    """成交量Bollinger响应模型"""
    middle: List[float] = Field(..., description="成交量中轨")
    upper: List[float] = Field(..., description="成交量上轨")
    lower: List[float] = Field(..., description="成交量下轨")
    smoothed_volume: List[float] = Field(..., description="平滑后的成交量")

class KDJResponse(BaseModel):
    """KDJ指标响应模型"""
    K: List[float] = Field(..., description="K值")
    D: List[float] = Field(..., description="D值")
    J: List[float] = Field(..., description="J值")

class TradingSignalsResponse(BaseModel):
    """交易信号响应模型"""
    buy_signals: List[int] = Field(..., description="买入信号位置")
    sell_signals: List[int] = Field(..., description="卖出信号位置")

class ParametersResponse(BaseModel):
    """参数响应模型"""
    bollinger_window: int = Field(..., description="布林带窗口大小")
    lag: int = Field(..., description="移动平均滞后期")
    period_index: int = Field(..., description="周期索引")

class DateRangeResponse(BaseModel):
    """日期范围响应模型"""
    start: Optional[str] = Field(None, description="开始日期")
    end: Optional[str] = Field(None, description="结束日期")

class CompleteIndicatorsResponse(BaseModel):
    """完整技术指标响应模型"""
    period_type: str = Field(..., description="数据周期类型")
    data_points: int = Field(..., description="数据点数量")
    date_range: DateRangeResponse = Field(..., description="数据日期范围")
    price_bollinger: BollingerBandsResponse = Field(..., description="价格布林带")
    volume_analysis: VolumeAnalysisResponse = Field(..., description="成交量分析")
    volume_bollinger: VolumeBollingerResponse = Field(..., description="成交量布林带")
    kdj_indicators: KDJResponse = Field(..., description="KDJ指标")
    trading_signals: TradingSignalsResponse = Field(..., description="交易信号")
    parameters: ParametersResponse = Field(..., description="计算参数")

class PeriodDetail(BaseModel):
    """数据周期详情"""
    index: int
    code: str
    name: str
    description: str

class AvailablePeriodsResponse(BaseModel):
    """可用数据周期列表响应"""
    periods: List[PeriodDetail]


# 依赖注入：创建高级指标计算服务
async def get_advanced_indicator_service(db: AsyncSession = Depends(get_db)):
    """获取高级指标计算服务实例"""
    storage_service = StockStorageService(db)
    return AdvancedIndicatorService(storage_service)

# API端点定义

@router.get("/complete/{stock_code}", response_model=CommonResponse[CompleteIndicatorsResponse])
@smart_data_cache(
    data_type=DataType.STOCK_INDICATORS,
    cache_strategy=CacheStrategy.CACHE_FIRST,
    cache_ttl=1800  # 30分钟缓存
)
async def get_complete_indicators(
    stock_code: str = Path(..., description="股票代码"),
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    period_index: int = Query(1, ge=1, le=6, description="周期索引: 1=日线, 2=周线, 3=月线, 4=15分钟, 5=30分钟, 6=60分钟"),
    bollinger_window: int = Query(20, ge=5, le=100, description="布林带窗口大小"),
    lag: int = Query(2, ge=1, le=10, description="移动平均滞后期"),
    advanced_service: AdvancedIndicatorService = Depends(get_advanced_indicator_service)
):
    """
    获取股票的完整高级技术指标
    
    包括：
    - 价格布林带
    - 成交量内外盘分析
    - 成交量布林带
    - KDJ指标
    - 买卖点信号
    """
    try:
        # 设置默认日期范围
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')
        if not start_date:
            start_date = (datetime.now() - timedelta(days=100)).strftime('%Y-%m-%d')
        
        # 计算指标（缓存由装饰器自动处理）
        result = await advanced_service.calculate_complete_indicators(
            stock_code=stock_code,
            start_date=start_date,
            end_date=end_date,
            period_index=period_index,
            bollinger_window=bollinger_window,
            lag=lag
        )
        
        return CommonResponse(data=result)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"计算技术指标失败: {str(e)}")

@router.get("/bollinger/{stock_code}", response_model=CommonResponse[BollingerBandsResponse])
async def get_bollinger_bands(
    stock_code: str = Path(..., description="股票代码"),
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    window: int = Query(20, ge=5, le=100, description="布林带窗口大小"),
    std_dev: float = Query(2.0, ge=1.0, le=5.0, description="标准差倍数"),
    advanced_service: AdvancedIndicatorService = Depends(get_advanced_indicator_service)
):
    """
    获取股票的Bollinger Bands (布林带)指标
    
    返回中轨、上轨、下轨数据
    """
    try:
        # 设置默认日期范围
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')
        if not start_date:
            start_date = (datetime.now() - timedelta(days=100)).strftime('%Y-%m-%d')
        
        # 获取股票数据
        df = await advanced_service.get_stock_data(stock_code, start_date, end_date)
        
        # 计算Bollinger Bands
        bollinger_result = advanced_service.calculate_bollinger_bands(
            df, window=window, std_dev=std_dev
        )
        
        response_data = {
            'middle': bollinger_result['middle'].tolist(),
            'upper': bollinger_result['upper'].tolist(),
            'lower': bollinger_result['lower'].tolist()
        }
        return CommonResponse(data=response_data)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"计算Bollinger Bands失败: {str(e)}")

@router.get("/volume-analysis/{stock_code}", response_model=CommonResponse[VolumeAnalysisResponse])
async def get_volume_analysis(
    stock_code: str = Path(..., description="股票代码"),
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    advanced_service: AdvancedIndicatorService = Depends(get_advanced_indicator_service)
):
    """
    获取股票的成交量内外盘分析
    
    返回内盘、外盘成交量和差异数据
    """
    try:
        # 设置默认日期范围
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')
        if not start_date:
            start_date = (datetime.now() - timedelta(days=100)).strftime('%Y-%m-%d')
        
        # 获取股票数据
        df = await advanced_service.get_stock_data(stock_code, start_date, end_date)
        
        # 计算成交量分析
        volume_result = advanced_service.calculate_volume_inout_difference(df)
        
        response_data = {
            'in_volume': volume_result['in_volume'].tolist(),
            'ex_volume': volume_result['out_volume'].tolist(),
            'volume_difference': volume_result['volume_diff'].tolist()
        }
        return CommonResponse(data=response_data)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"计算成交量分析失败: {str(e)}")

@router.get("/volume-bollinger/{stock_code}", response_model=CommonResponse[VolumeBollingerResponse])
async def get_volume_bollinger(
    stock_code: str = Path(..., description="股票代码"),
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    window: int = Query(20, ge=5, le=100, description="布林带窗口大小"),
    lag: int = Query(2, ge=1, le=10, description="移动平均滞后期"),
    advanced_service: AdvancedIndicatorService = Depends(get_advanced_indicator_service)
):
    """
    获取基于成交量的Bollinger指标
    
    返回成交量布林带的中轨、上轨、下轨和平滑后的成交量数据
    """
    try:
        # 设置默认日期范围
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')
        if not start_date:
            start_date = (datetime.now() - timedelta(days=100)).strftime('%Y-%m-%d')
        
        # 获取股票数据并计算成交量差异
        df = await advanced_service.get_stock_data(stock_code, start_date, end_date)
        volume_diff = advanced_service.calculate_volume_inout_difference(df)
        
        # 计算成交量Bollinger
        volume_bollinger_result = advanced_service.calculate_volume_bollinger(
            volume_diff['volume_diff'], window=window, lag=lag
        )
        
        response_data = {
            'middle': volume_bollinger_result['volume_ma'].tolist(),
            'upper': volume_bollinger_result['volume_upper'].tolist(),
            'lower': volume_bollinger_result['volume_lower'].tolist(),
            'smoothed_volume': volume_bollinger_result['volume_bollinger'].tolist()
        }
        return CommonResponse(data=response_data)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"计算成交量Bollinger失败: {str(e)}")

@router.get("/kdj-enhanced/{stock_code}", response_model=CommonResponse[KDJResponse])
async def get_kdj_enhanced(
    stock_code: str = Path(..., description="股票代码"),
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    k_period: int = Query(9, ge=3, le=30, description="K值计算周期"),
    d_period: int = Query(3, ge=2, le=10, description="D值平滑周期"),
    j_period: int = Query(3, ge=2, le=10, description="J值计算周期"),
    advanced_service: AdvancedIndicatorService = Depends(get_advanced_indicator_service)
):
    """
    获取增强版KDJ指标
    
    支持自定义参数的KDJ计算
    """
    try:
        # 设置默认日期范围
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')
        if not start_date:
            start_date = (datetime.now() - timedelta(days=100)).strftime('%Y-%m-%d')
        
        # 获取股票数据
        df = await advanced_service.get_stock_data(stock_code, start_date, end_date)
        
        # 计算KDJ指标
        kdj_result = advanced_service.calculate_kdj(
            df, k_period=k_period, d_period=d_period, j_period=j_period
        )
        
        response_data = {
            'K': kdj_result['K'].tolist(),
            'D': kdj_result['D'].tolist(),
            'J': kdj_result['J'].tolist()
        }
        return CommonResponse(data=response_data)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"计算KDJ指标失败: {str(e)}")

@router.get("/trading-signals/{stock_code}", response_model=CommonResponse[TradingSignalsResponse])
async def get_trading_signals(
    stock_code: str = Path(..., description="股票代码"),
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    bollinger_window: int = Query(20, ge=5, le=100, description="布林带窗口大小"),
    lag: int = Query(2, ge=1, le=10, description="移动平均滞后期"),
    advanced_service: AdvancedIndicatorService = Depends(get_advanced_indicator_service)
):
    """
    获取买卖点交易信号
    
    基于成交量突破和KDJ金叉死叉计算买卖点
    """
    try:
        # 设置默认日期范围
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')
        if not start_date:
            start_date = (datetime.now() - timedelta(days=100)).strftime('%Y-%m-%d')
        
        # 获取股票数据
        df = await advanced_service.get_stock_data(stock_code, start_date, end_date)
        
        # 计算各种指标
        volume_diff_result = advanced_service.calculate_volume_inout_difference(df) # Renamed variable to avoid confusion
        volume_bollinger = advanced_service.calculate_volume_bollinger(
            volume_diff_result['volume_diff'], window=bollinger_window, lag=lag # Changed 'diff_vol' to 'volume_diff'
        )
        kdj_indicators = advanced_service.calculate_kdj(df)
        
        # 计算交易信号
        signals = advanced_service.calculate_buy_sell_signals(
            df, volume_bollinger, kdj_indicators
        )
        
        return CommonResponse(data=signals)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"计算交易信号失败: {str(e)}")

@router.get("/periods", response_model=CommonResponse[AvailablePeriodsResponse])
async def get_available_periods():
    """
    获取支持的数据周期列表
    """
    periods_data = {
        "periods": [
            {"index": 1, "code": "dh", "name": "日线", "description": "日K线数据"},
            {"index": 2, "code": "wh", "name": "周线", "description": "周K线数据"},
            {"index": 3, "code": "mh", "name": "月线", "description": "月K线数据"},
            {"index": 4, "code": "15m", "name": "15分钟线", "description": "15分钟K线数据"},
            {"index": 5, "code": "30m", "name": "30分钟线", "description": "30分钟K线数据"},
            {"index": 6, "code": "60m", "name": "60分钟线", "description": "60分钟K线数据"}
        ]
    }
    return CommonResponse(data=periods_data)
