"""
缓存管理API端点

提供缓存状态查询、清理等管理功能
"""
from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, Optional
from datetime import datetime
from app.core import logging
# 缓存清理功能由统一数据管理系统内部处理
from app.core.data.manager import get_data_manager
from app.schemas.common import CommonResponse

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/cache", tags=["cache"])


@router.get("/stats", response_model=CommonResponse[Dict[str, Any]])
async def get_cache_stats():
    """获取缓存统计信息
    
    返回缓存的基本统计数据，包括：
    - 缓存类型（Redis/Memory）
    - 缓存项数量（仅内存缓存）
    - 其他相关统计
    """
    try:
        data_manager = get_data_manager()
        health_info = await data_manager.health_check()
        
        stats = {
            "cache_type": "UnifiedDataManager",
            "enabled": True,
            "health": health_info
        }
        
        return CommonResponse(data=stats)
        
    except Exception as e:
        logger.error(f"Failed to get cache stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get cache statistics")


@router.delete("/clear", response_model=CommonResponse[Dict[str, str]])
async def clear_all_cache():
    """清空所有缓存
    
    警告：这将删除所有缓存数据！
    """
    try:
        # 统一数据管理系统的缓存清理
        data_manager = get_data_manager()
        health_info = await data_manager.health_check()
        
        return CommonResponse(data={
            "message": "统一数据管理系统缓存自动管理中",
            "system_status": health_info.get("status", "unknown")
        })
    except Exception as e:
        logger.error(f"Failed to clear cache: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to clear cache")


@router.delete("/prefix/{prefix}", response_model=CommonResponse[Dict[str, Any]])
async def clear_cache_by_prefix_endpoint(
    prefix: str
):
    """根据前缀清除缓存
    
    Args:
        prefix: 要清除的缓存键前缀
    """
    try:
        # 统一数据管理系统内部管理缓存，无需手动清理特定前缀
        data_manager = get_data_manager()
        health_info = await data_manager.health_check()
        
        return CommonResponse(data={
            "message": f"统一数据管理系统自动管理前缀 '{prefix}' 的缓存",
            "prefix": prefix,
            "system_status": health_info.get("status", "unknown")
        })
    except Exception as e:
        logger.error(f"Failed to clear cache by prefix: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to clear cache by prefix")


@router.delete("/l1-l2", response_model=CommonResponse[Dict[str, Any]])
async def clear_l1_l2_cache_endpoint():
    """手动清理L1和L2缓存
    
    立即清理L1内存缓存和L2 Redis缓存
    """
    try:
        data_manager = get_data_manager()
        result = await data_manager.clear_l1_l2_cache()
        
        return CommonResponse(data={
            "message": "L1和L2缓存清理完成",
            "l1_cleared": result["l1_cleared"],
            "l2_cleared": result["l2_cleared"], 
            "l1_count": result["l1_count"],
            "l2_count": result["l2_count"],
            "timestamp": result["timestamp"]
        })
    except Exception as e:
        logger.error(f"Failed to clear L1 and L2 cache: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to clear L1 and L2 cache: {str(e)}")


@router.post("/warm-up", response_model=CommonResponse[Dict[str, str]])
async def warm_up_cache():
    """启动扫描器缓存预热
    
    预加载所有股票的日K线数据到缓存中，提高扫描速度
    """
    try:
        from app.services.scan.session_manager import SessionManager
        from app.services.scan.memory_scanner import MemoryScanner
        
        # 创建会话管理器和扫描器实例
        session_manager = SessionManager()
        scanner = MemoryScanner(session_manager)
        
        # 启动缓存预热任务
        result = await scanner.start_cache_warmup()
        
        return CommonResponse(data={"message": result})
    except Exception as e:
        logger.error(f"Failed to warm up cache: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to warm up cache: {str(e)}")


@router.post("/warm-up/stop", response_model=CommonResponse[Dict[str, str]])
async def stop_warm_up_cache():
    """停止扫描器缓存预热
    
    停止正在进行的缓存预热任务
    """
    try:
        from app.services.scan.session_manager import SessionManager
        from app.services.scan.memory_scanner import MemoryScanner
        
        # 创建会话管理器和扫描器实例
        session_manager = SessionManager()
        scanner = MemoryScanner(session_manager)
        
        # 停止缓存预热任务
        result = await scanner.stop_cache_warmup()
        
        return CommonResponse(data={"message": result})
    except Exception as e:
        logger.error(f"Failed to stop warm up cache: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to stop warm up cache: {str(e)}")


@router.get("/warm-up/status", response_model=CommonResponse[Dict[str, Any]])
async def get_warm_up_status():
    """获取缓存预热状态
    
    返回当前缓存预热的进度和状态信息
    """
    try:
        from app.services.scan.session_manager import SessionManager
        from app.services.scan.memory_scanner import MemoryScanner
        
        # 创建会话管理器和扫描器实例
        session_manager = SessionManager()
        scanner = MemoryScanner(session_manager)
        
        # 获取预热状态
        status = scanner.get_warmup_status()
        
        return CommonResponse(data=status)
    except Exception as e:
        logger.error(f"Failed to get warm up status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get warm up status: {str(e)}")


@router.get("/health", response_model=CommonResponse[Dict[str, Any]])
async def check_cache_health():
    """检查缓存健康状态
    
    测试缓存的基本功能是否正常
    """
    try:
        test_key = "_health_check_test"
        test_value = {"status": "healthy", "timestamp": str(datetime.now())}
        
        # 测试设置缓存
        await cache_client.set(test_key, test_value, expire=60)
        
        # 测试获取缓存
        retrieved_value = await cache_client.get(test_key)
        
        # 测试删除缓存
        await cache_client.delete(test_key)
        
        # 验证结果
        if retrieved_value == test_value:
            return CommonResponse(data={
                "status": "healthy",
                "cache_type": cache_client.__class__.__name__,
                "test_passed": True
            })
        else:
            return CommonResponse(data={
                "status": "unhealthy",
                "cache_type": cache_client.__class__.__name__,
                "test_passed": False,
                "error": "Cache read/write test failed"
            })
            
    except Exception as e:
        logger.error(f"Cache health check failed: {str(e)}")
        return CommonResponse(data={
            "status": "unhealthy",
            "cache_type": cache_client.__class__.__name__,
            "test_passed": False,
            "error": str(e)
        })