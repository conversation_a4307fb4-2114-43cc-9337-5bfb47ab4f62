"""
缓存管理API端点

提供缓存状态查询、清理等管理功能
"""
from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, Optional
from datetime import datetime
from app.core import logging
from app.core.cache import cache_client, clear_cache, clear_cache_by_prefix
from app.schemas.common import CommonResponse

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/cache", tags=["cache"])


@router.get("/stats", response_model=CommonResponse[Dict[str, Any]])
async def get_cache_stats():
    """获取缓存统计信息
    
    返回缓存的基本统计数据，包括：
    - 缓存类型（Redis/Memory）
    - 缓存项数量（仅内存缓存）
    - 其他相关统计
    """
    try:
        stats = {
            "cache_type": cache_client.__class__.__name__,
            "enabled": True
        }
        
        # 如果是内存缓存，可以获取更多统计信息
        if hasattr(cache_client, '_cache'):
            stats.update({
                "total_items": len(cache_client._cache),
                "memory_usage_estimate_mb": len(cache_client._cache) * 0.001  # 粗略估算
            })
        
        # 如果是Redis缓存，尝试获取信息
        if cache_client.__class__.__name__ == 'RedisCache':
            try:
                client = await cache_client._get_client()
                info = await client.info()
                stats.update({
                    "redis_version": info.get('redis_version', 'unknown'),
                    "connected_clients": info.get('connected_clients', 0),
                    "used_memory_human": info.get('used_memory_human', 'unknown'),
                    "total_keys": await client.dbsize()
                })
            except Exception as e:
                logger.error(f"Failed to get Redis info: {str(e)}")
                stats["redis_status"] = "error"
                stats["redis_error"] = str(e)
        
        return CommonResponse(data=stats)
        
    except Exception as e:
        logger.error(f"Failed to get cache stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get cache statistics")


@router.delete("/clear", response_model=CommonResponse[Dict[str, str]])
async def clear_all_cache():
    """清空所有缓存
    
    警告：这将删除所有缓存数据！
    """
    try:
        await clear_cache()
        return CommonResponse(data={"message": "All cache cleared successfully"})
    except Exception as e:
        logger.error(f"Failed to clear cache: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to clear cache")


@router.delete("/prefix/{prefix}", response_model=CommonResponse[Dict[str, Any]])
async def clear_cache_by_prefix_endpoint(
    prefix: str
):
    """根据前缀清除缓存
    
    Args:
        prefix: 要清除的缓存键前缀
    """
    try:
        deleted_count = await clear_cache_by_prefix(prefix)
        return CommonResponse(data={
            "message": f"Cache cleared for prefix: {prefix}",
            "deleted_count": deleted_count
        })
    except Exception as e:
        logger.error(f"Failed to clear cache by prefix: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to clear cache by prefix")


@router.post("/warm-up", response_model=CommonResponse[Dict[str, str]])
async def warm_up_cache():
    """缓存预热
    
    预加载常用数据到缓存中，提高系统响应速度
    """
    try:
        # TODO: 实现缓存预热逻辑
        # 例如：预加载热门股票的最新数据、常用指标等
        
        return CommonResponse(data={"message": "Cache warm-up completed"})
    except Exception as e:
        logger.error(f"Failed to warm up cache: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to warm up cache")


@router.get("/health", response_model=CommonResponse[Dict[str, Any]])
async def check_cache_health():
    """检查缓存健康状态
    
    测试缓存的基本功能是否正常
    """
    try:
        test_key = "_health_check_test"
        test_value = {"status": "healthy", "timestamp": str(datetime.now())}
        
        # 测试设置缓存
        await cache_client.set(test_key, test_value, expire=60)
        
        # 测试获取缓存
        retrieved_value = await cache_client.get(test_key)
        
        # 测试删除缓存
        await cache_client.delete(test_key)
        
        # 验证结果
        if retrieved_value == test_value:
            return CommonResponse(data={
                "status": "healthy",
                "cache_type": cache_client.__class__.__name__,
                "test_passed": True
            })
        else:
            return CommonResponse(data={
                "status": "unhealthy",
                "cache_type": cache_client.__class__.__name__,
                "test_passed": False,
                "error": "Cache read/write test failed"
            })
            
    except Exception as e:
        logger.error(f"Cache health check failed: {str(e)}")
        return CommonResponse(data={
            "status": "unhealthy",
            "cache_type": cache_client.__class__.__name__,
            "test_passed": False,
            "error": str(e)
        })