"""
指数技术指标分析REST API接口
提供各种技术指标（MACD/KDJ/ARBR/RSI）的计算和查询接口，专门针对指数数据
"""
from typing import List, Optional, Dict, Any
from datetime import date, timedelta, datetime
from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.services.storage.index_storage import IndexStorageService
from app.services.indicators.index_indicator_service import IndexIndicatorService
from app.utils.decorators import smart_data_cache
from app.core.data.models import DataType, CacheStrategy
from app.schemas.common import CommonResponse

router = APIRouter(
    tags=["Index Indicators"],
    responses={404: {"description": "指数指标数据不存在"}}
)

# --- Pydantic Response Models ---

class IndicatorDataBase(BaseModel):
    date: List[str]

class MACDResponse(IndicatorDataBase):
    diff: List[float]
    dea: List[float]
    macd: List[float]

class KDJResponse(IndicatorDataBase):
    k: List[float]
    d: List[float]
    j: List[float]

class RSIResponse(IndicatorDataBase):
    rsi1: List[float]
    rsi2: List[float]
    rsi3: List[float]

class VolumeAnalysisResponse(IndicatorDataBase):
    volume: List[float]
    volume_ma: Dict[str, List[float]]

# 依赖注入：创建指数指标服务
async def get_index_indicator_service(db: AsyncSession = Depends(get_db)):
    """获取指数指标服务实例"""
    storage = IndexStorageService(db)
    return IndexIndicatorService(storage)

@router.get("/macd/{index_code}", response_model=CommonResponse[MACDResponse])
# @smart_data_cache(
#     data_type=DataType.STOCK_INDICATORS,  # 复用股票指标缓存类型
#     cache_strategy=CacheStrategy.CACHE_FIRST,
#     cache_ttl=3600
# )
async def get_index_macd(
    index_code: str,
    start_date: Optional[str] = Query(None, description="开始日期，格式：YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式：YYYY-MM-DD"),
    fast_period: int = Query(12, ge=1, le=100, description="快线周期"),
    slow_period: int = Query(26, ge=1, le=200, description="慢线周期"),
    signal_period: int = Query(9, ge=1, le=100, description="信号线周期"),
    freq: str = Query("D", regex="^[DWM]$", description="数据周期：D(日)、W(周)、M(月)"),
    indicator_service: IndexIndicatorService = Depends(get_index_indicator_service)
):
    """
    计算指数MACD指标
    
    MACD (Moving Average Convergence Divergence) 是一种趋势跟踪动量指标
    """
    # 设置默认日期范围
    if not end_date:
        end_date = date.today().isoformat()
    if not start_date:
        start_date = (date.fromisoformat(end_date) - timedelta(days=120)).isoformat()
    
    try:
        result = await indicator_service.calculate_macd(
            index_code, start_date, end_date, fast_period, slow_period, signal_period, freq
        )
        return CommonResponse(data=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"计算指数MACD失败: {str(e)}")

@router.get("/kdj/{index_code}", response_model=CommonResponse[KDJResponse])
@smart_data_cache(
    data_type=DataType.STOCK_INDICATORS,
    cache_strategy=CacheStrategy.CACHE_FIRST,
    cache_ttl=3600
)
async def get_index_kdj(
    index_code: str,
    start_date: Optional[str] = Query(None, description="开始日期，格式：YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式：YYYY-MM-DD"),
    window: int = Query(9, ge=1, le=100, description="计算窗口"),
    signal_period: int = Query(3, ge=1, le=50, description="信号线周期"),
    freq: str = Query("D", regex="^[DWM]$", description="数据周期：D(日)、W(周)、M(月)"),
    indicator_service: IndexIndicatorService = Depends(get_index_indicator_service)
):
    """
    计算指数KDJ指标
    
    KDJ是一种随机振荡器，用于分析超买超卖状态
    """
    # 设置默认日期范围
    if not end_date:
        end_date = date.today().isoformat()
    if not start_date:
        start_date = (date.fromisoformat(end_date) - timedelta(days=120)).isoformat()
    
    try:
        result = await indicator_service.calculate_kdj(
            index_code, start_date, end_date, window, signal_period, freq
        )
        return CommonResponse(data=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"计算指数KDJ失败: {str(e)}")

@router.get("/rsi/{index_code}", response_model=CommonResponse[RSIResponse])
@smart_data_cache(
    data_type=DataType.STOCK_INDICATORS,
    cache_strategy=CacheStrategy.CACHE_FIRST,
    cache_ttl=3600
)
async def get_index_rsi(
    index_code: str,
    start_date: Optional[str] = Query(None, description="开始日期，格式：YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式：YYYY-MM-DD"),
    periods: List[int] = Query([6, 12, 24], description="RSI周期列表"),
    freq: str = Query("D", regex="^[DWM]$", description="数据周期：D(日)、W(周)、M(月)"),
    indicator_service: IndexIndicatorService = Depends(get_index_indicator_service)
):
    """
    计算指数RSI指标
    
    RSI (Relative Strength Index) 相对强弱指标，用于衡量价格变动的速度和变化
    """
    # 设置默认日期范围
    if not end_date:
        end_date = date.today().isoformat()
    if not start_date:
        start_date = (date.fromisoformat(end_date) - timedelta(days=120)).isoformat()
    
    try:
        result = await indicator_service.calculate_rsi(
            index_code, start_date, end_date, periods, freq
        )
        return CommonResponse(data=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"计算指数RSI失败: {str(e)}")

@router.get("/volume/{index_code}", response_model=CommonResponse[VolumeAnalysisResponse])
@smart_data_cache(
    data_type=DataType.STOCK_INDICATORS,
    cache_strategy=CacheStrategy.CACHE_FIRST,
    cache_ttl=3600
)
async def get_index_volume_analysis(
    index_code: str,
    start_date: Optional[str] = Query(None, description="开始日期，格式：YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式：YYYY-MM-DD"),
    ma_periods: List[int] = Query([5, 10, 20], description="移动平均周期列表"),
    freq: str = Query("D", regex="^[DWM]$", description="数据周期：D(日)、W(周)、M(月)"),
    indicator_service: IndexIndicatorService = Depends(get_index_indicator_service)
):
    """
    获取指数成交量分析数据
    
    提供成交量及其移动平均线，用于分析成交量趋势
    """
    # 设置默认日期范围
    if not end_date:
        end_date = date.today().isoformat()
    if not start_date:
        start_date = (date.fromisoformat(end_date) - timedelta(days=120)).isoformat()
    
    try:
        result = await indicator_service.calculate_volume_analysis(
            index_code, start_date, end_date, ma_periods, freq
        )
        return CommonResponse(data=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取指数成交量分析失败: {str(e)}")