"""
指数REST API接口
提供指数基本信息和数据的CRUD操作
"""
import logging
from typing import List, Optional, Dict, Any
from datetime import date
from fastapi import APIRouter, Depends, Query, HTTPException, Path, Body, status
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field
from fastapi.responses import JSONResponse

from app.core.database import get_db
from app.services.storage.index_storage import IndexStorageService
from app.services.data_fetcher import DataFetcherFactory
from app.core.exceptions import DatabaseException
from app.utils.decorators import smart_data_cache
from app.core.data.models import DataType, CacheStrategy
from app.schemas.common import CommonResponse
from app.core.config import settings

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    tags=["Indices"],
    responses={
        404: {"description": "指数不存在"},
        500: {"description": "数据库操作失败"}
    }
)

# 定义请求和响应模型
class IndexInfoBase(BaseModel):
    """指数基本信息基础模型"""
    code: str = Field(..., description="指数代码，如：000001.SH")
    name: str = Field(..., description="指数名称，如：上证指数")
    exchange: str = Field(..., description="交易所代码，如：SH, SZ")
    is_active: bool = Field(True, description="是否在列表中显示")

class IndexInfoResponse(IndexInfoBase):
    """指数信息响应模型"""
    class Config:
        from_attributes = True

class IndexListResponse(BaseModel):
    """指数列表响应模型"""
    indices: List[IndexInfoResponse] = Field(..., description="指数列表")
    total: int = Field(..., description="总数量")
    skip: int = Field(..., description="跳过的记录数")
    limit: int = Field(..., description="返回的最大记录数")

    class Config:
        from_attributes = True

class IndexDailyResponse(BaseModel):
    """指数日线数据响应模型"""
    index_code: str = Field(..., description="指数代码")
    trade_date: date = Field(..., description="交易日期")
    open: float = Field(..., description="开盘价")
    high: float = Field(..., description="最高价")
    low: float = Field(..., description="最低价")
    close: float = Field(..., description="收盘价")
    volume: int = Field(..., description="交易量")
    amount: int = Field(..., description="交易额")
    prev_close: Optional[float] = Field(None, description="前收盘价")
    change_pct: Optional[float] = Field(None, description="涨跌幅")

    class Config:
        from_attributes = True

class RefreshRequest(BaseModel):
    """指数列表刷新请求模型"""
    data_source: Optional[str] = Field(None, description="数据源名称（tushare/akshare/mairui），为空时使用系统默认")

class RefreshResponse(BaseModel):
    """指数列表刷新响应模型"""
    deleted_count: int = Field(..., description="删除的指数数量")
    inserted_count: int = Field(..., description="新增的指数数量")
    total_fetched: int = Field(..., description="从数据源获取的指数数量")
    data_source: str = Field(..., description="使用的数据源")
    duration_seconds: float = Field(..., description="刷新耗时（秒）")

class MessageResponse(BaseModel):
    """通用消息响应模型"""
    message: str = Field(..., description="响应消息")

    class Config:
        from_attributes = True

# 依赖注入：创建指数存储服务
async def get_index_storage(db: AsyncSession = Depends(get_db)):
    """获取指数存储服务实例"""
    return IndexStorageService(db)

# 获取指数列表
@router.get("/", response_model=CommonResponse[IndexListResponse])
async def get_indices(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的最大记录数"),
    exchange: Optional[str] = Query(None, description="按交易所筛选"),
    is_active: Optional[bool] = Query(None, description="按是否活跃筛选"),
    search: Optional[str] = Query(None, description="搜索关键词（指数代码或名称）"),
    index_storage: IndexStorageService = Depends(get_index_storage)
):
    """
    获取指数列表，支持分页和筛选
    """
    try:
        result = await index_storage.get_index_list(
            skip=skip,
            limit=limit,
            exchange=exchange,
            is_active=is_active,
            search=search
        )
        
        response_data = {
            "indices": result["indices"],
            "total": result["total"],
            "skip": result["skip"],
            "limit": result["limit"]
        }
        
        return CommonResponse(data=response_data)
    except Exception as e:
        logger.error(f"获取指数列表失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取指数列表失败: {str(e)}")

# 刷新指数列表
@router.post("/refresh", response_model=CommonResponse[RefreshResponse])
async def refresh_index_list(
    refresh_request: RefreshRequest = Body(...),
    index_storage: IndexStorageService = Depends(get_index_storage)
):
    """
    刷新指数列表数据
    获取最新指数列表，清空数据库，然后重新插入
    """
    import time
    start_time = time.time()
    
    try:
        # 确定数据源
        data_source = refresh_request.data_source or settings.DATA_API_TYPE
        logger.info(f"开始刷新指数列表，使用数据源: {data_source}")
        
        # 创建数据获取器
        data_fetcher = DataFetcherFactory.create_fetcher(provider=data_source)
        
        # 获取指数列表
        logger.info("正在从数据源获取指数列表...")
        index_list = await data_fetcher.get_index_list()
        total_fetched = len(index_list)
        logger.info(f"从数据源获取到 {total_fetched} 个指数")
        
        # 替换数据库中的所有指数信息
        logger.info("正在清空数据库并插入新数据...")
        result = await index_storage.replace_all_index_info(index_list)
        
        # 计算耗时
        duration = time.time() - start_time
        
        logger.info(
            f"指数列表刷新完成: 删除 {result['deleted']} 条，"
            f"插入 {result['inserted']} 条，耗时 {duration:.2f} 秒"
        )
        
        return CommonResponse(
            data=RefreshResponse(
                deleted_count=result['deleted'],
                inserted_count=result['inserted'],
                total_fetched=total_fetched,
                data_source=data_source,
                duration_seconds=round(duration, 2)
            ),
            message=f"指数列表刷新成功：删除 {result['deleted']} 条，插入 {result['inserted']} 条"
        )
        
    except Exception as e:
        logger.error(f"刷新指数列表失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"刷新指数列表失败: {str(e)}")

# 获取单个指数信息
@router.get("/{index_code}", response_model=CommonResponse[IndexInfoResponse])  
async def get_index(
    index_code: str = Path(..., description="指数代码"),
    include_latest_data: bool = Query(False, description="是否包含最新行情数据"),
    index_storage: IndexStorageService = Depends(get_index_storage)
):
    """
    根据指数代码获取单个指数信息，可选择包含最新行情数据
    """
    try:
        index = await index_storage.get_index_info(index_code)
        if not index:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="指数不存在")
        
        # 如果需要包含最新数据，获取最新的日线数据
        if include_latest_data:
            try:
                latest_data = await index_storage.get_index_daily_data(index_code, limit=1)
                if latest_data:
                    latest = latest_data[0]
                    # 扩展指数信息
                    index.update({
                        "latest_price": latest.get("close"),
                        "change_pct": latest.get("change_pct"),
                        "prev_close": latest.get("prev_close"),
                        "high": latest.get("high"),
                        "low": latest.get("low"),
                        "volume": latest.get("volume"),
                        "amount": latest.get("amount"),
                        "trade_date": latest.get("trade_date").isoformat() if latest.get("trade_date") else None
                    })
            except Exception as e:
                logger.warning(f"获取指数 {index_code} 最新数据失败: {e}")
                # 即使获取最新数据失败，也返回基本信息
        
        return CommonResponse(data=index)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取指数 {index_code} 信息失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取指数 {index_code} 信息失败: {str(e)}")

# 获取指数日线数据
@router.get("/{index_code}/daily", response_model=CommonResponse[List[IndexDailyResponse]])
async def get_index_daily_data(
    index_code: str = Path(..., description="指数代码"),
    start_date: Optional[date] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[date] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    limit: Optional[int] = Query(250, ge=1, le=5000, description="返回记录数限制"),
    index_storage: IndexStorageService = Depends(get_index_storage)
):
    """
    获取指数日线数据
    """
    try:
        # 检查指数是否存在
        index = await index_storage.get_index_info(index_code)
        if not index:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="指数不存在")
        
        daily_data = await index_storage.get_index_daily_data(
            index_code=index_code,
            start_date=start_date,
            end_date=end_date,
            limit=limit
        )
        
        return CommonResponse(data=daily_data)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取指数 {index_code} 日线数据失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取指数 {index_code} 日线数据失败: {str(e)}")

# 获取指数代码建议
@router.get("/suggestions/", response_model=CommonResponse[List[IndexInfoResponse]])
async def get_index_suggestions(
    query: str = Query(..., min_length=2, description="搜索查询"),
    limit: int = Query(10, ge=1, le=50, description="返回的最大建议数"),
    index_storage: IndexStorageService = Depends(get_index_storage)
):
    """
    根据输入提供指数代码或名称建议
    """
    try:
        suggestions = await index_storage.get_index_suggestions(query, limit)
        return CommonResponse(data=suggestions)
    except Exception as e:
        logger.error(f"获取指数建议失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取指数建议失败: {str(e)}")

# 同步指数历史数据
@router.post("/{index_code}/sync-daily", response_model=CommonResponse[Dict[str, Any]])
async def sync_index_daily_data(
    index_code: str = Path(..., description="指数代码"),
    start_date: Optional[date] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[date] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    index_storage: IndexStorageService = Depends(get_index_storage)
):
    """
    同步指数历史日线数据
    """
    import time
    start_time = time.time()
    
    try:
        # 检查指数是否存在
        index = await index_storage.get_index_info(index_code)
        if not index:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="指数不存在")
        
        # 创建数据获取器
        data_fetcher = DataFetcherFactory.create_fetcher(provider=settings.DATA_API_TYPE)
        
        # 获取历史数据
        logger.info(f"开始同步指数 {index_code} 的历史日线数据...")
        daily_data = await data_fetcher.get_index_period_data(
            code=index_code,
            period="d",
            start_date=start_date,
            end_date=end_date
        )
        
        # 保存到数据库
        inserted_count = await index_storage.save_index_daily_data(index_code, daily_data)
        
        duration = time.time() - start_time
        
        logger.info(
            f"指数 {index_code} 历史数据同步完成: "
            f"获取 {len(daily_data)} 条，插入 {inserted_count} 条，耗时 {duration:.2f} 秒"
        )
        
        return CommonResponse(
            data={
                "index_code": index_code,
                "fetched_count": len(daily_data),
                "inserted_count": inserted_count,
                "duration_seconds": round(duration, 2)
            },
            message=f"指数 {index_code} 历史数据同步成功：获取 {len(daily_data)} 条，插入 {inserted_count} 条"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"同步指数 {index_code} 历史数据失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"同步指数 {index_code} 历史数据失败: {str(e)}")