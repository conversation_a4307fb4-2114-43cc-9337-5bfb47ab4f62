"""
自选股相关的API路由 - 同步版本
"""

from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.sync_database import get_db
from app.schemas.watchlist import WatchlistItemCreate, WatchlistItemUpdate, WatchlistItemResponse, WatchlistBulkUpdate
from app.services.sync_watchlist_service import WatchlistService
from app.schemas.common import CommonResponse
# 假设我们已经有了认证依赖
from app.api.deps import get_current_user_id

router = APIRouter(
    prefix="/watchlist",
    tags=["Watchlist"],
    responses={404: {"description": "Not found"}},
)

@router.post("/", response_model=CommonResponse[dict], status_code=status.HTTP_201_CREATED)
def create_watchlist_item(
    item: WatchlistItemCreate, 
    db: Session = Depends(get_db),
    current_user_id: int = Depends(get_current_user_id)
):
    """添加股票到自选股列表"""
    try:
        result = WatchlistService.add_stock_to_watchlist(
            db=db, 
            user_id=current_user_id, 
            stock_code=item.stock_code,
            sort_order=item.sort_order
        )
        # 将 WatchlistItem 对象序列化为字典
        result_dict = {
            "id": result.id,
            "user_id": result.user_id,
            "stock_code": result.stock_code,
            "added_at": result.added_at.isoformat() if result.added_at else None,
            "sort_order": result.sort_order
        }
        return CommonResponse(data=result_dict, message="添加自选股成功")
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.get("/", response_model=CommonResponse[List[Dict[str, Any]]])
async def get_watchlist_items(
    db: Session = Depends(get_db),
    current_user_id: int = Depends(get_current_user_id)
):
    """获取用户的自选股列表"""
    result = await WatchlistService.get_watchlist_items(
        db=db, 
        user_id=current_user_id, 
        include_stock_info=True
    )
    return CommonResponse(data=result, message="获取自选股列表成功")

@router.delete("/{stock_code}", response_model=CommonResponse[None], status_code=status.HTTP_200_OK)
def delete_watchlist_item(
    stock_code: str,
    db: Session = Depends(get_db),
    current_user_id: int = Depends(get_current_user_id)
):
    """从自选股列表移除股票"""
    result = WatchlistService.remove_stock_from_watchlist(db=db, user_id=current_user_id, stock_code=stock_code)    
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"自选股中未找到股票: {stock_code}"
        )
    return CommonResponse(data=None, message="删除自选股成功")

@router.put("/{stock_code}", response_model=CommonResponse[dict])
def update_watchlist_item(
    stock_code: str,
    item_update: WatchlistItemUpdate,
    db: Session = Depends(get_db),
    current_user_id: int = Depends(get_current_user_id)
):
    """更新自选股项目信息"""
    updated_item = WatchlistService.update_watchlist_item(
        db=db,
        user_id=current_user_id,
        stock_code=stock_code,
        sort_order=item_update.sort_order
    )
    if not updated_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"自选股中未找到股票: {stock_code}"
        )
    # 将 WatchlistItem 对象序列化为字典
    result_dict = {
        "id": updated_item.id,
        "user_id": updated_item.user_id,
        "stock_code": updated_item.stock_code,
        "added_at": updated_item.added_at.isoformat() if updated_item.added_at else None,
        "sort_order": updated_item.sort_order
    }
    return CommonResponse(data=result_dict, message="更新自选股成功")

@router.post("/bulk", response_model=CommonResponse[dict])
def bulk_update_watchlist(
    bulk_data: WatchlistBulkUpdate,
    db: Session = Depends(get_db),
    current_user_id: int = Depends(get_current_user_id)
):
    """批量更新自选股列表"""
    result = WatchlistService.bulk_update_watchlist(
        db=db,
        user_id=current_user_id,
        stock_codes=bulk_data.stock_codes
    )
    return CommonResponse(data=result, message="批量更新自选股成功")
