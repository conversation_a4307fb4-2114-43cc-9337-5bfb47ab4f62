"""
扫描操作API路由
处理扫描任务的启动、停止、查询等操作
"""
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Request, Query, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc
import logging
import json

from app.core.database import get_db
from app.utils.dependencies import get_current_user
from app.models.user import User
from app.models.task import TaskExecution
from app.services.tasks.executor import get_task_executor
from app.schemas.scheduled_task import TaskStatus
from app.schemas.scan import (
    ScanStartRequest,
    ScanStartResponse,
    ScanTaskResponse,
    ScanResultsResponse,
    ScanResultResponse,
    ScanProgressResponse,
    StockIndicatorDataResponse
)

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/start", response_model=ScanStartResponse)
async def start_scan(
    request: ScanStartRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> ScanStartResponse:
    """
    启动扫描任务
    
    Args:
        request: 扫描启动请求
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        扫描启动响应
    """
    try:
        # 构建任务配置
        task_config = {
            "indicators": request.indicators,
            "stock_codes": request.stock_codes,
            "parameters": request.parameters.model_dump() if request.parameters else {},
            "scan_mode": request.scan_mode,
            "scan_strategy": request.scan_strategy,
            "periods": request.periods or ["d"],
            "adjust": request.adjust,
            "period_parameters": request.period_parameters.model_dump() if request.period_parameters else {},
            "period_indicators": request.period_indicators,
            "end_date": request.end_date
        }
        
        # 创建TaskExecution记录
        execution = TaskExecution(
            user_id=current_user.id,
            scheduled_task_id=None,  # 手动触发
            trigger_type="manual",
            task_type="indicator_scan",
            task_config=json.dumps(task_config, ensure_ascii=False),
            status=TaskStatus.PENDING
        )
        db.add(execution)
        await db.commit()
        await db.refresh(execution)
        
        # 使用Executor执行任务
        executor = get_task_executor()
        await executor.execute_task(execution.id)
        
        logger.info(f"Started scan task {execution.id} for user {current_user.id}")
        
        return ScanStartResponse(
            task_id=str(execution.id),
            status=execution.status,
            created_at=execution.created_at
        )
        
    except Exception as e:
        logger.error(f"Failed to start scan: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to start scan: {str(e)}")


@router.post("/{task_id}/stop")
async def stop_scan(
    task_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    停止扫描任务
    
    Args:
        task_id: 任务ID（TaskExecution.id）
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        停止结果
    """
    try:
        # 查找任务执行记录
        exec_stmt = select(TaskExecution).where(
            TaskExecution.id == task_id,
            TaskExecution.user_id == current_user.id
        )
        exec_result = await db.execute(exec_stmt)
        execution = exec_result.scalar_one_or_none()
        
        if not execution:
            raise HTTPException(status_code=404, detail="Task not found")
        
        # 检查任务状态
        if execution.status not in [TaskStatus.PENDING, TaskStatus.RUNNING]:
            raise HTTPException(status_code=400, detail="Cannot cancel completed task")
        
        # 使用Executor取消任务
        executor = get_task_executor()
        cancelled = executor.cancel_execution(task_id)
        
        # 更新数据库状态
        if cancelled:
            execution.status = TaskStatus.CANCELLED
            if not execution.end_time:
                from datetime import datetime
                execution.end_time = datetime.now()
                if execution.start_time:
                    execution.duration_seconds = int(
                        (execution.end_time - execution.start_time).total_seconds()
                    )
            await db.commit()
        
        logger.info(f"Stopped scan task {task_id}")
        
        return {"message": f"Task {task_id} stopped successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to stop scan {task_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to stop scan")


@router.get("/active", response_model=List[ScanTaskResponse])
async def get_active_scans(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> List[ScanTaskResponse]:
    """
    获取当前用户的最新扫描任务（用于前端显示和跟踪）
    
    返回最新的扫描任务，不管其状态如何。这样前端可以：
    - 显示最新任务的状态和进度
    - 如果任务正在运行则继续轮询
    - 如果任务已完成则显示结果
    
    Args:
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        最新的扫描任务列表（通常只包含一个任务）
    """
    try:
        # 查询用户的扫描任务，按创建时间倒序
        exec_stmt = select(TaskExecution).where(
            TaskExecution.user_id == current_user.id,
            TaskExecution.task_type == "indicator_scan",
            TaskExecution.trigger_type == "manual"
        ).order_by(desc(TaskExecution.created_at))
        
        exec_result = await db.execute(exec_stmt)
        all_executions = exec_result.scalars().all()
        
        if not all_executions:
            return []
        
        # 返回最新的任务（按创建时间排序，取第一个）
        if all_executions:
            active_executions = [all_executions[0]]  # 最新的一个任务
        else:
            active_executions = []
        
        # 转换为响应格式
        responses = []
        for execution in active_executions:
            # 解析任务配置
            try:
                config_data = json.loads(execution.task_config) if execution.task_config else {}
                indicators = config_data.get("indicators", [])
                end_date = config_data.get("end_date")
            except:
                indicators = []
                end_date = None
            
            # 构建进度响应
            progress = ScanProgressResponse(
                task_id=str(execution.id),
                status=execution.status,
                total=execution.progress_total,
                current=execution.progress_current,
                percentage=execution.progress_percentage,
                message=execution.progress_message or "准备中..."
            )
            
            responses.append(ScanTaskResponse(
                task_id=str(execution.id),
                status=execution.status,
                indicators=indicators,
                progress=progress,
                result_count=execution.results_count or 0,
                created_at=execution.created_at,
                start_time=execution.start_time,
                end_time=execution.end_time,
                error_message=execution.error_message,
                end_date=end_date
            ))
        
        return responses
        
    except Exception as e:
        logger.error(f"Failed to get active scans: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get active scans")


@router.get("/{task_id}", response_model=ScanTaskResponse)
async def get_scan_task(
    task_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> ScanTaskResponse:
    """
    获取扫描任务信息
    
    Args:
        task_id: 任务ID（TaskExecution.id）
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        扫描任务信息
    """
    try:
        # 查找任务执行记录
        exec_stmt = select(TaskExecution).where(
            TaskExecution.id == task_id,
            TaskExecution.user_id == current_user.id,
            TaskExecution.task_type == "indicator_scan"
        )
        exec_result = await db.execute(exec_stmt)
        execution = exec_result.scalar_one_or_none()
        
        if not execution:
            raise HTTPException(status_code=404, detail="Task not found")
        
        # 解析任务配置获取indicators
        try:
            config_data = json.loads(execution.task_config) if execution.task_config else {}
            indicators = config_data.get("indicators", [])
            end_date = config_data.get("end_date")
        except:
            indicators = []
            end_date = None
        
        # 构建进度响应
        progress = ScanProgressResponse(
            task_id=str(task_id),
            status=execution.status,
            total=execution.progress_total,
            current=execution.progress_current,
            percentage=execution.progress_percentage,
            message=execution.progress_message or "准备中..."
        )
        
        return ScanTaskResponse(
            task_id=str(task_id),
            status=execution.status,
            indicators=indicators,
            progress=progress,
            result_count=execution.results_count or 0,
            created_at=execution.created_at,
            start_time=execution.start_time,
            end_time=execution.end_time,
            error_message=execution.error_message,
            end_date=end_date
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get scan task {task_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get task")


@router.get("/{task_id}/progress", response_model=ScanProgressResponse)
async def get_scan_progress(
    task_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> ScanProgressResponse:
    """
    获取扫描进度，优先从Redis获取实时进度，回退到数据库
    
    Args:
        task_id: 任务ID（TaskExecution.id）
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        扫描进度
    """
    try:
        # 优先从Redis获取实时进度（完全避免数据库查询）
        from app.services.tasks.executor import get_task_executor
        executor = get_task_executor()
        redis_progress = await executor.get_task_progress(task_id)
        
        if redis_progress:
            # Redis有数据，直接返回实时进度，完全不查数据库
            # 如果进度为100 说明任务完成
            status = "completed" if redis_progress.get("progress_percentage", 0) >= 100 else "running"
            return ScanProgressResponse(
                task_id=str(task_id),
                status=status,
                total=redis_progress.get("progress_total"),
                current=redis_progress.get("progress_current"),
                percentage=redis_progress.get("progress_percentage"),
                message=redis_progress.get("progress_message", "运行中...")
            )
        
        # Redis无数据，才查数据库验证任务并获取最终状态
        exec_stmt = select(TaskExecution).where(
            TaskExecution.id == task_id,
            TaskExecution.user_id == current_user.id,
            TaskExecution.task_type == "indicator_scan"
        )
        exec_result = await db.execute(exec_stmt)
        execution = exec_result.scalar_one_or_none()
        
        if not execution:
            raise HTTPException(status_code=404, detail="Task not found")
        
        # 使用数据库数据（任务已完成或失败）
        return ScanProgressResponse(
            task_id=str(task_id),
            status=execution.status,
            total=execution.progress_total,
            current=execution.progress_current,
            percentage=execution.progress_percentage,
            message=execution.progress_message or "已完成"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get scan progress for {task_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get progress")


@router.get("/{task_id}/results", response_model=ScanResultsResponse)
async def get_scan_results(
    task_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小")
) -> ScanResultsResponse:
    """
    获取扫描结果
    
    Args:
        task_id: 任务ID（TaskExecution.id）
        current_user: 当前用户
        db: 数据库会话
        page: 页码
        page_size: 每页大小
        
    Returns:
        扫描结果列表
    """
    try:
        # 查找任务执行记录
        exec_stmt = select(TaskExecution).where(
            TaskExecution.id == task_id,
            TaskExecution.user_id == current_user.id,
            TaskExecution.task_type == "indicator_scan"
        )
        exec_result = await db.execute(exec_stmt)
        execution = exec_result.scalar_one_or_none()
        
        if not execution:
            raise HTTPException(status_code=404, detail="Task not found")
        
        # 解析任务配置获取end_date
        try:
            config_data = json.loads(execution.task_config) if execution.task_config else {}
            end_date = config_data.get("end_date")
        except:
            end_date = None
        
        # 解析扫描结果
        results_data = []
        if execution.results_data:
            try:
                results_data = json.loads(execution.results_data)
                if not isinstance(results_data, list):
                    results_data = []
            except:
                logger.warning(f"Failed to parse results_data for task {task_id}")
                results_data = []
        
        # 分页处理
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        paged_results = results_data[start_idx:end_idx]
        
        # 转换为响应格式
        result_responses = []
        for result_dict in paged_results:
            try:
                # 构建指标数据响应
                indicator_data_dict = result_dict.get("indicator_data", {})
                indicator_data = StockIndicatorDataResponse(
                    kdj_k=indicator_data_dict.get("kdj_k"),
                    kdj_d=indicator_data_dict.get("kdj_d"),
                    kdj_j=indicator_data_dict.get("kdj_j"),
                    volume_pressure=indicator_data_dict.get("volume_pressure"),
                    bollinger_upper=indicator_data_dict.get("bollinger_upper"),
                    bollinger_middle=indicator_data_dict.get("bollinger_middle"),
                    bollinger_lower=indicator_data_dict.get("bollinger_lower"),
                    macd=indicator_data_dict.get("macd"),
                    dif=indicator_data_dict.get("dif"),
                    dea=indicator_data_dict.get("dea"),
                    rsi=indicator_data_dict.get("rsi"),
                    arbr_ar=indicator_data_dict.get("arbr_ar"),
                    arbr_br=indicator_data_dict.get("arbr_br"),
                    prev_kdj_k=indicator_data_dict.get("prev_kdj_k"),
                    prev_kdj_d=indicator_data_dict.get("prev_kdj_d"),
                    volume_pressure_avg=indicator_data_dict.get("volume_pressure_avg"),
                    close_price=indicator_data_dict.get("close_price"),
                    bollinger_distance_pct=indicator_data_dict.get("bollinger_distance_pct")
                )
                
                result_responses.append(ScanResultResponse(
                    stock_code=result_dict.get("stock_code", ""),
                    stock_name=result_dict.get("stock_name", ""),
                    signals=result_dict.get("signals", []),
                    indicator_data=indicator_data,
                    price=result_dict.get("price"),
                    change_percent=result_dict.get("change_percent"),
                    scan_time=result_dict.get("scan_time"),
                    end_date=end_date
                ))
            except Exception as e:
                logger.warning(f"Failed to parse result item: {e}")
                continue
        
        return ScanResultsResponse(
            task_id=str(task_id),
            results=result_responses,
            total_count=len(results_data),
            page=page,
            page_size=page_size,
            end_date=end_date
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get scan results for {task_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get results")

