"""
交易日API端点
提供交易日期相关的API服务
"""

from fastapi import APIRouter

router = APIRouter()


@router.get("/latest-trading-date")
async def get_latest_trading_date() -> dict:
    """
    获取最近交易日期
    
    Returns:
        dict: 包含最近交易日期信息
            - date: 格式化的日期字符串 (YYYY-MM-DD)
            - formatted: 友好的日期格式 (YYYY年MM月DD日)
    """
    from app.utils.trading_utils import get_last_trading_date, format_trading_date
    
    trading_date = get_last_trading_date()
    return {
        "date": format_trading_date(trading_date),
        "formatted": trading_date.strftime('%Y年%m月%d日')
    }