"""
用户相关的API路由
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from app.core.database import get_db
from app.schemas.common import CommonResponse, PaginatedResponse
from app.schemas.user import UserCreate, UserResponse
from app.services.user_service import UserService
from app.utils.dependencies import get_current_admin_user, get_current_user
from app.models.user import User

router = APIRouter(
    prefix="/users",
    tags=["Users"],
    responses={404: {"description": "Not found"}},
)

@router.post("/", response_model=dict, status_code=status.HTTP_201_CREATED)
async def create_user(
    user: UserCreate, 
    current_user: User = Depends(get_current_admin_user)  # 只有管理员可创建用户
):
    """创建新用户（仅管理员）"""
    db_user = await UserService.get_user_by_username(username=user.username)
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已被占用"
        )
    result = await UserService.create_user(**user.model_dump())
    return {
        "id": result.id,
        "username": result.username,
        "email": result.email,
        "is_admin": result.is_admin,
        "is_active": result.is_active
    }

@router.get("/list", response_model=dict)
async def list_users(
    current_user: User = Depends(get_current_admin_user)  # 只有管理员可查看用户列表
):
    """获取用户列表（仅管理员）"""
    from app.core.database import db_session
    
    async with db_session() as db:
        stmt = select(User)
        result = await db.execute(stmt)
        users = result.scalars().all()
        
        user_list = [
            {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "is_admin": user.is_admin,
                "is_active": user.is_active,
                "last_login": user.last_login,
                "created_at": user.created_at
            }
            for user in users
        ]
        
        return {
            "data": user_list,
            "total": len(user_list)
        }

@router.get("/me", response_model=dict)
def get_current_user_info(current_user: User = Depends(get_current_user)):
    """获取当前用户信息"""
    return {
        "id": current_user.id,
        "username": current_user.username,
        "email": current_user.email,
        "is_admin": current_user.is_admin,
        "is_active": current_user.is_active,
        "last_login": current_user.last_login
    }

@router.put("/{user_id}/status")
async def update_user_status(
    user_id: int,
    is_active: bool,
    current_user: User = Depends(get_current_admin_user)
):
    """更新用户状态（仅管理员）"""
    user = await UserService.get_user_by_id(user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    from app.core.database import db_session
    async with db_session() as db:
        stmt = update(User).where(User.id == user_id).values(is_active=is_active)
        await db.execute(stmt)
        await db.commit()
    
    return {"message": f"用户状态已更新为 {'激活' if is_active else '禁用'}"}
