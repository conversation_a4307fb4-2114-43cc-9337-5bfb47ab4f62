"""
技术指标分析REST API接口
提供各种技术指标（MACD/KDJ/ARBR/RSI）的计算和查询接口
以及日K/周K/月K数据的转换和查询
"""
from typing import List, Optional, Dict, Any
from datetime import date, timedelta, datetime
from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.services.storage.stock_storage import StockStorageService
from app.services.indicators.indicator_service import IndicatorService
from app.utils.decorators import smart_data_cache
from app.core.data.models import DataType, CacheStrategy
from app.schemas.common import CommonResponse

# Remove the redundant prefix, it's handled in app/api/__init__.py
router = APIRouter(
    tags=["Indicators"], # Use consistent capitalized tag
    responses={404: {"description": "指标数据不存在"}}
)

# --- Pydantic Response Models ---

class IndicatorDataBase(BaseModel):
    date: List[str]

class MACDResponse(IndicatorDataBase):
    diff: List[float]
    dea: List[float]
    macd: List[float]

class KDJResponse(IndicatorDataBase):
    k: List[float]
    d: List[float]
    j: List[float]

class RSIResponse(IndicatorDataBase):
    rsi1: List[float]
    rsi2: List[float]
    rsi3: List[float]

class ARBRResponse(IndicatorDataBase):
    ar: List[float]
    br: List[float]

class VolumeAnalysisResponse(IndicatorDataBase):
    volume: List[float]
    ma5: List[float]
    ma10: List[float]

# 依赖注入：创建指标计算服务
async def get_indicator_service(db: AsyncSession = Depends(get_db)):
    """获取指标计算服务实例"""
    storage = StockStorageService(db)
    return IndicatorService(storage)

# MACD指标API
@router.get("/macd/{stock_code}", response_model=CommonResponse[MACDResponse])
# @smart_data_cache(
#     data_type=DataType.STOCK_INDICATORS,
#     cache_strategy=CacheStrategy.CACHE_FIRST,
#     cache_ttl=3600
# )
async def get_macd(
    stock_code: str,
    start_date: Optional[str] = Query(None, description="开始日期，格式：YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式：YYYY-MM-DD"),
    fast_period: int = Query(12, ge=1, le=100, description="快线周期"),
    slow_period: int = Query(26, ge=1, le=200, description="慢线周期"),
    signal_period: int = Query(9, ge=1, le=100, description="信号线周期"),
    freq: str = Query("D", regex="^[DWM]$", description="数据周期：D(日)、W(周)、M(月)"),
    indicator_service: IndicatorService = Depends(get_indicator_service)
):
    """
    计算MACD指标
    
    MACD指标由三部分组成：差离值（DIF或DIFF）、信号线（DEA或MACD）和柱状图（BAR或HIST）。
    
    计算公式：
    - DIF = EMA(CLOSE, fast_period) - EMA(CLOSE, slow_period)
    - DEA = EMA(DIF, signal_period)
    - HIST = (DIF - DEA) * 2
    """
    # 设置默认日期
    if not end_date:
        end_date = date.today().isoformat()
    if not start_date:
        # 默认获取最近100天的数据，但指标计算服务会根据需要获取更多历史数据
        start_date = (date.fromisoformat(end_date) - timedelta(days=365)).isoformat() 
    
    try:
        # 计算指标
        result = await indicator_service.calculate_macd(
            stock_code, start_date, end_date, 
            fast_period, slow_period, signal_period, freq
        )
        return CommonResponse(data=result)
    except Exception as e:
        # logger.error(f"计算MACD指标失败 for {stock_code}: {str(e)}", exc_info=True) # 建议添加日志
        raise HTTPException(status_code=500, detail=f"计算MACD指标失败: {str(e)}")

# KDJ指标API
@router.get("/kdj/{stock_code}", response_model=CommonResponse[KDJResponse])
@smart_data_cache(
    data_type=DataType.STOCK_INDICATORS,
    cache_strategy=CacheStrategy.CACHE_FIRST,
    cache_ttl=3600
)
async def get_kdj(
    stock_code: str,
    start_date: Optional[str] = Query(None, description="开始日期，格式：YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式：YYYY-MM-DD"),
    window: int = Query(9, ge=1, le=100, description="计算窗口"),
    signal_period: int = Query(3, ge=1, le=50, description="信号线周期"),
    freq: str = Query("D", regex="^[DWM]$", description="数据周期：D(日)、W(周)、M(月)"),
    indicator_service: IndicatorService = Depends(get_indicator_service)
):
    """
    计算KDJ指标
    
    KDJ指标是一种超买超卖指标，由K线、D线和J线组成。
    
    计算公式：
    - K = 2/3 * 前一日K + 1/3 * RSV
    - D = 2/3 * 前一日D + 1/3 * K
    - J = 3 * K - 2 * D
    
    其中RSV = (C - L_n) / (H_n - L_n) * 100
    C为收盘价，L_n为n日内最低价，H_n为n日内最高价
    """
    # 设置默认日期
    if not end_date:
        end_date = date.today().isoformat()
    if not start_date:
        start_date = (date.fromisoformat(end_date) - timedelta(days=100)).isoformat()
    
    try:
        # 计算指标
        result = await indicator_service.calculate_kdj(
            stock_code, start_date, end_date, window, signal_period, freq
        )
        
        # 缓存结果
        # 缓存由装饰器自动处理
        
        return CommonResponse(data=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"计算KDJ指标失败: {str(e)}")

# RSI指标API
@router.get("/rsi/{stock_code}", response_model=CommonResponse[RSIResponse])
@smart_data_cache(
    data_type=DataType.STOCK_INDICATORS,
    cache_strategy=CacheStrategy.CACHE_FIRST,
    cache_ttl=3600
)
async def get_rsi(
    stock_code: str,
    start_date: Optional[str] = Query(None, description="开始日期，格式：YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式：YYYY-MM-DD"),
    periods: List[int] = Query([6, 12, 24], description="RSI周期列表"),
    freq: str = Query("D", regex="^[DWM]$", description="数据周期：D(日)、W(周)、M(月)"),
    indicator_service: IndicatorService = Depends(get_indicator_service)
):
    """
    计算RSI指标
    
    RSI(相对强弱指标)是一种动量指标，测量价格变动的速度和变化。
    
    计算公式：
    RSI = 100 - (100 / (1 + RS))
    其中RS = 平均上涨点数 / 平均下跌点数
    """
    # 设置默认日期
    if not end_date:
        end_date = date.today().isoformat()
    if not start_date:
        start_date = (date.fromisoformat(end_date) - timedelta(days=100)).isoformat()
    
    try:
        # 计算指标
        result = await indicator_service.calculate_rsi(
            stock_code, start_date, end_date, periods, freq
        )
        
        # 缓存结果
        # 缓存由装饰器自动处理
        
        return CommonResponse(data=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"计算RSI指标失败: {str(e)}")

# ARBR指标API
@router.get("/arbr/{stock_code}", response_model=CommonResponse[ARBRResponse])
@smart_data_cache(
    data_type=DataType.STOCK_INDICATORS,
    cache_strategy=CacheStrategy.CACHE_FIRST,
    cache_ttl=3600
)
async def get_arbr(
    stock_code: str,
    start_date: Optional[str] = Query(None, description="开始日期，格式：YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式：YYYY-MM-DD"),
    window: int = Query(26, ge=1, le=100, description="计算窗口"),
    freq: str = Query("D", regex="^[DWM]$", description="数据周期：D(日)、W(周)、M(月)"),
    indicator_service: IndicatorService = Depends(get_indicator_service)
):
    """
    计算ARBR指标
    
    AR指标和BR指标是反映市场人气和买卖力量对比的指标。
    
    计算公式：
    - AR = ∑(H - O) / ∑(O - L) × 100
    - BR = ∑(H - PC) / ∑(PC - L) × 100
    
    其中H为最高价，O为开盘价，L为最低价，PC为前收盘价
    """
    # 设置默认日期
    if not end_date:
        end_date = date.today().isoformat()
    if not start_date:
        start_date = (date.fromisoformat(end_date) - timedelta(days=100)).isoformat()
    
    try:
        # 计算指标
        result = await indicator_service.calculate_arbr(
            stock_code, start_date, end_date, window, freq
        )
        
        # 缓存结果
        # 缓存由装饰器自动处理
        
        return CommonResponse(data=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"计算ARBR指标失败: {str(e)}")

# 成交量分析API
@router.get("/volume/{stock_code}", response_model=CommonResponse[VolumeAnalysisResponse])
@smart_data_cache(
    data_type=DataType.STOCK_INDICATORS,
    cache_strategy=CacheStrategy.CACHE_FIRST,
    cache_ttl=3600
)
async def get_volume_analysis(
    stock_code: str,
    start_date: Optional[str] = Query(None, description="开始日期，格式：YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式：YYYY-MM-DD"),
    ma_periods: List[int] = Query([5, 10, 20], description="移动平均周期列表"),
    freq: str = Query("D", regex="^[DWM]$", description="数据周期：D(日)、W(周)、M(月)"),
    indicator_service: IndicatorService = Depends(get_indicator_service)
):
    """
    获取成交量分析数据
    
    提供成交量及其移动平均线、量比等信息。
    """
    # 设置默认日期
    if not end_date:
        end_date = date.today().isoformat()
    if not start_date:
        start_date = (date.fromisoformat(end_date) - timedelta(days=100)).isoformat()
    
    try:
        # 获取分析数据 - 传递计算窗口（使用最大的移动平均周期作为窗口）
        window = max(ma_periods) if ma_periods else 20
        result = await indicator_service.get_volume_analysis(
            stock_code, start_date, end_date, window, freq
        )
        
        # 缓存结果
        # 缓存由装饰器自动处理
        
        return CommonResponse(data=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取成交量分析数据失败: {str(e)}")
