"""
Redis队列监控API端点
"""
from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, Any, List
import logging

from app.services.queue import get_batch_queue, get_queue_consumer
from app.core.config import settings
# from app.api.deps import get_current_user  # 暂时注释掉认证依赖

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/queue/stats", response_model=Dict[str, Any])
async def get_queue_stats():
    """获取队列统计信息"""
    try:
        batch_queue = get_batch_queue()
        stats = await batch_queue.get_queue_stats()
        return {
            "success": True,
            "data": stats
        }
    except Exception as e:
        logger.error(f"获取队列统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取队列统计失败: {str(e)}")


@router.get("/queue/consumer/stats", response_model=Dict[str, Any])
async def get_consumer_stats():
    """获取消费者统计信息"""
    try:
        consumer = get_queue_consumer()
        stats = consumer.get_stats()
        return {
            "success": True,
            "data": stats
        }
    except Exception as e:
        logger.error(f"获取消费者统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取消费者统计失败: {str(e)}")


@router.get("/queue/health", response_model=Dict[str, Any])
async def queue_health_check():
    """队列系统健康检查"""
    try:
        consumer = get_queue_consumer()
        health = await consumer.health_check()
        
        status_code = 200
        if health.get('status') == 'unhealthy':
            status_code = 503
        elif health.get('status') == 'warning':
            status_code = 200  # 警告状态仍返回200
        
        return {
            "success": True,
            "data": health
        }
    except Exception as e:
        logger.error(f"队列健康检查失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"队列健康检查失败: {str(e)}")


@router.post("/queue/pause/{task_type}")
async def pause_queue(task_type: str):
    """暂停指定类型的队列处理"""
    try:
        from app.services.queue.models import TaskType
        
        # 验证任务类型
        try:
            task_type_enum = TaskType(task_type)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"无效的任务类型: {task_type}")
        
        batch_queue = get_batch_queue()
        success = await batch_queue.pause_queue(task_type_enum)
        
        if success:
            return {
                "success": True,
                "message": f"队列 {task_type} 已暂停"
            }
        else:
            raise HTTPException(status_code=500, detail="暂停队列失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"暂停队列失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"暂停队列失败: {str(e)}")


@router.post("/queue/resume/{task_type}")
async def resume_queue(task_type: str):
    """恢复指定类型的队列处理"""
    try:
        from app.services.queue.models import TaskType
        
        # 验证任务类型
        try:
            task_type_enum = TaskType(task_type)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"无效的任务类型: {task_type}")
        
        batch_queue = get_batch_queue()
        success = await batch_queue.resume_queue(task_type_enum)
        
        if success:
            return {
                "success": True,
                "message": f"队列 {task_type} 已恢复"
            }
        else:
            raise HTTPException(status_code=500, detail="恢复队列失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"恢复队列失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"恢复队列失败: {str(e)}")


@router.get("/queue/status/{task_type}")
async def get_queue_status(task_type: str):
    """获取指定队列的状态"""
    try:
        from app.services.queue.models import TaskType
        
        # 验证任务类型
        try:
            task_type_enum = TaskType(task_type)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"无效的任务类型: {task_type}")
        
        batch_queue = get_batch_queue()
        is_paused = await batch_queue.is_queue_paused(task_type_enum)
        
        return {
            "success": True,
            "data": {
                "task_type": task_type,
                "is_paused": is_paused,
                "status": "paused" if is_paused else "running"
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取队列状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取队列状态失败: {str(e)}")


@router.delete("/queue/clear")
async def clear_all_queues():
    """清空所有队列（危险操作，仅用于测试和维护）"""
    try:
        if settings.APP_ENV == "production":
            raise HTTPException(status_code=403, detail="生产环境禁止清空队列")
        
        batch_queue = get_batch_queue()
        results = await batch_queue.clear_all_queues()
        
        return {
            "success": True,
            "message": "所有队列已清空",
            "data": results
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清空队列失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清空队列失败: {str(e)}")


@router.get("/queue/config")
async def get_queue_config():
    """获取队列配置信息"""
    try:
        from app.services.queue.models import QueueConfig
        
        config_info = {
            "enabled": settings.QUEUE_ENABLED,
            "redis_url": settings.REDIS_URL,
            "consumer_concurrency": settings.QUEUE_CONSUMER_CONCURRENCY,
            "consumer_timeout": settings.QUEUE_CONSUMER_TIMEOUT,
            "fallback_to_direct": settings.QUEUE_FALLBACK_TO_DIRECT,
            "save_results": settings.QUEUE_SAVE_RESULTS,
            "queue_names": QueueConfig.get_all_queue_names(),
            "default_batch_size": QueueConfig.DEFAULT_BATCH_SIZE,
            "default_max_retries": QueueConfig.DEFAULT_MAX_RETRIES
        }
        
        return {
            "success": True,
            "data": config_info
        }
        
    except Exception as e:
        logger.error(f"获取队列配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取队列配置失败: {str(e)}")


@router.get("/queue/tasks/types")
async def get_task_types():
    """获取所有可用的任务类型"""
    try:
        from app.services.queue.models import TaskType
        
        task_types = [
            {
                "value": task_type.value,
                "name": task_type.value.replace("_", " ").title()
            }
            for task_type in TaskType
        ]
        
        return {
            "success": True,
            "data": task_types
        }
        
    except Exception as e:
        logger.error(f"获取任务类型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务类型失败: {str(e)}")


# 管理员权限的端点（需要认证）
@router.post("/queue/restart")
async def restart_queue_consumer():
    """重启队列消费者（需要管理员权限）"""
    try:
        from app.services.queue import stop_queue_consumer, start_queue_consumer
        
        # 停止现有消费者
        await stop_queue_consumer(timeout=30)
        
        # 启动新的消费者
        await start_queue_consumer(concurrency=settings.QUEUE_CONSUMER_CONCURRENCY)
        
        return {
            "success": True,
            "message": "队列消费者已重启"
        }
        
    except Exception as e:
        logger.error(f"重启队列消费者失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重启队列消费者失败: {str(e)}")