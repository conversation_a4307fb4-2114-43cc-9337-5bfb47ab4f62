"""定时任务API端点"""

import json
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import desc, and_, select, func
from croniter import croniter
from datetime import datetime

from app.core.database import get_db
from app.utils.dependencies import get_current_user
from app.models.user import User
from app.models.task import UserScheduledTask, TaskExecution
from app.schemas.scheduled_task import (
    UserScheduledTaskCreate,
    UserScheduledTaskUpdate,
    UserScheduledTaskResponse,
    TaskExecutionResponse,
    TaskExecutionListResponse
)
from app.services.tasks.executor import get_task_executor

router = APIRouter()

@router.post("/", response_model=UserScheduledTaskResponse)
async def create_scheduled_task(
    task_data: UserScheduledTaskCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建定时任务"""
    # 验证cron表达式
    try:
        cron = croniter(task_data.cron_expression)
        next_run = cron.get_next(datetime)
    except Exception:
        raise HTTPException(status_code=400, detail="无效的Cron表达式")
    
    # 检查用户任务数量限制
    stmt = select(func.count(UserScheduledTask.id)).where(
        UserScheduledTask.user_id == current_user.id
    )
    result = await db.execute(stmt)
    user_task_count = result.scalar()
    
    if user_task_count >= 20:  # 每用户最多20个任务
        raise HTTPException(status_code=400, detail="您的定时任务数量已达上限(20个)")
    
    try:
        # 序列化task_config
        if hasattr(task_data.task_config, 'json'):
            config_json = task_data.task_config.json()
        else:
            config_json = json.dumps(task_data.task_config)
        
        task = UserScheduledTask(
            user_id=current_user.id,
            name=task_data.name,
            task_type=task_data.task_type,
            cron_expression=task_data.cron_expression,
            task_config=config_json,
            description=task_data.description,
            max_executions=task_data.max_executions,
            next_execution=next_run
        )
        
        db.add(task)
        await db.commit()
        await db.refresh(task)
        
        # 转换task_config为字典
        response_data = {**task.__dict__}
        response_data['task_config'] = json.loads(task.task_config)
        
        return UserScheduledTaskResponse(**response_data)
        
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"创建任务失败: {str(e)}")

@router.get("/", response_model=List[UserScheduledTaskResponse])
async def list_scheduled_tasks(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取用户的定时任务列表"""
    stmt = select(UserScheduledTask).where(
        UserScheduledTask.user_id == current_user.id
    ).order_by(desc(UserScheduledTask.created_at)).offset(skip).limit(limit)
    result = await db.execute(stmt)
    tasks = result.scalars().all()
    
    # 转换结果
    results = []
    for task in tasks:
        task_data = {**task.__dict__}
        task_data['task_config'] = json.loads(task.task_config)
        results.append(UserScheduledTaskResponse(**task_data))
    
    return results

@router.get("/{task_id}", response_model=UserScheduledTaskResponse)
async def get_scheduled_task(
    task_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取特定定时任务详情"""
    stmt = select(UserScheduledTask).where(
        and_(UserScheduledTask.id == task_id, UserScheduledTask.user_id == current_user.id)
    )
    result = await db.execute(stmt)
    task = result.scalar_one_or_none()
    
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    # 转换task_config为字典
    response_data = {**task.__dict__}
    response_data['task_config'] = json.loads(task.task_config)
    
    return UserScheduledTaskResponse(**response_data)

@router.put("/{task_id}", response_model=UserScheduledTaskResponse)
async def update_scheduled_task(
    task_id: int,
    task_data: UserScheduledTaskUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新定时任务"""
    stmt = select(UserScheduledTask).where(
        and_(UserScheduledTask.id == task_id, UserScheduledTask.user_id == current_user.id)
    )
    result = await db.execute(stmt)
    task = result.scalar_one_or_none()
    
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    try:
        # 更新字段
        update_data = task_data.dict(exclude_unset=True)
        
        # 处理cron表达式更新
        if "cron_expression" in update_data:
            try:
                cron = croniter(update_data["cron_expression"])
                task.next_execution = cron.get_next(datetime)
            except Exception:
                raise HTTPException(status_code=400, detail="无效的Cron表达式")
        
        # 处理配置更新
        if "task_config" in update_data:
            # 如果task_config是IndicatorScanConfig对象，将其序列化为JSON字符串
            config_data = update_data["task_config"]
            if hasattr(config_data, 'json'):
                # 是Pydantic模型，调用json()方法
                update_data["task_config"] = config_data.json()
            elif isinstance(config_data, dict):
                # 已经是字典，需要转为JSON字符串
                import json
                update_data["task_config"] = json.dumps(config_data)
            else:
                # 其他情况，尝试JSON序列化
                import json
                update_data["task_config"] = json.dumps(config_data)
        
        for field, value in update_data.items():
            setattr(task, field, value)
        
        await db.commit()
        await db.refresh(task)
        
        # 转换task_config为字典
        response_data = {**task.__dict__}
        response_data['task_config'] = json.loads(task.task_config)
        
        return UserScheduledTaskResponse(**response_data)
        
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"更新任务失败: {str(e)}")

@router.delete("/{task_id}")
async def delete_scheduled_task(
    task_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除定时任务"""
    stmt = select(UserScheduledTask).where(
        and_(UserScheduledTask.id == task_id, UserScheduledTask.user_id == current_user.id)
    )
    result = await db.execute(stmt)
    task = result.scalar_one_or_none()
    
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    try:
        # 删除相关的执行记录
        delete_stmt = select(TaskExecution).where(
            TaskExecution.scheduled_task_id == task_id
        )
        executions_result = await db.execute(delete_stmt)
        executions = executions_result.scalars().all()
        for execution in executions:
            await db.delete(execution)
        
        # 删除任务
        await db.delete(task)
        await db.commit()
        
        return {"message": "任务已删除"}
        
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"删除任务失败: {str(e)}")

@router.post("/{task_id}/execute")
async def execute_task_manually(
    task_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """手动执行定时任务"""
    stmt = select(UserScheduledTask).where(
        and_(UserScheduledTask.id == task_id, UserScheduledTask.user_id == current_user.id)
    )
    result = await db.execute(stmt)
    task = result.scalar_one_or_none()
    
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    # 检查是否有正在执行的任务
    count_stmt = select(func.count(TaskExecution.id)).where(
        TaskExecution.user_id == current_user.id,
        TaskExecution.status.in_(["pending", "running"])
    )
    count_result = await db.execute(count_stmt)
    running_count = count_result.scalar()
    
    if running_count >= 3:  # 每用户最多3个并发任务
        raise HTTPException(status_code=400, detail="您有太多正在执行的任务，请稍后再试")
    
    try:
        # 创建执行记录
        execution = TaskExecution(
            user_id=current_user.id,
            scheduled_task_id=task.id,
            trigger_type="manual",
            task_type=task.task_type,
            task_config=task.task_config,
            status="pending"
        )
        
        db.add(execution)
        await db.commit()
        await db.refresh(execution)
        
        # 异步执行任务
        executor = get_task_executor()
        await executor.execute_task(execution.id)
        
        return {"message": "任务已提交执行", "execution_id": execution.id}
        
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"提交任务失败: {str(e)}")

@router.get("/{task_id}/executions", response_model=TaskExecutionListResponse)
async def get_task_executions(
    task_id: int,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=500),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取任务执行记录"""
    # 验证任务所有权
    task_stmt = select(UserScheduledTask).where(
        and_(UserScheduledTask.id == task_id, UserScheduledTask.user_id == current_user.id)
    )
    task_result = await db.execute(task_stmt)
    task = task_result.scalar_one_or_none()
    
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    # 获取执行记录
    # 先获取总数
    count_stmt = select(func.count(TaskExecution.id)).where(
        TaskExecution.scheduled_task_id == task_id
    )
    count_result = await db.execute(count_stmt)
    total = count_result.scalar()
    
    # 获取分页数据
    exec_stmt = select(TaskExecution).where(
        TaskExecution.scheduled_task_id == task_id
    ).order_by(desc(TaskExecution.created_at)).offset(skip).limit(limit)
    exec_result = await db.execute(exec_stmt)
    executions = exec_result.scalars().all()
    
    # 构造响应
    items = []
    for execution in executions:
        item_dict = execution.__dict__.copy()
        item_dict["scheduled_task_name"] = task.name
        # 解析results_data
        if execution.results_data:
            try:
                item_dict["results_data"] = json.loads(execution.results_data)
            except:
                item_dict["results_data"] = None
        else:
            item_dict["results_data"] = None
        
        # 解析task_config
        if execution.task_config:
            try:
                raw_config = json.loads(execution.task_config)
                # 使用格式化工具格式化配置
                from app.schemas.scheduled_task import TaskConfigFormatter
                formatted_config = TaskConfigFormatter.format_task_config(execution.task_type, raw_config)
                item_dict["task_config"] = formatted_config
                item_dict["task_config_raw"] = raw_config
            except Exception as e:
                # 解析失败，提供原始配置
                item_dict["task_config"] = None
                item_dict["task_config_raw"] = None
        else:
            item_dict["task_config"] = None
            item_dict["task_config_raw"] = None
            
        items.append(TaskExecutionResponse(**item_dict))
    
    return TaskExecutionListResponse(total=total, items=items)

@router.put("/{task_id}/toggle")
async def toggle_task_status(
    task_id: int,
    is_active: bool,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """切换任务启用/禁用状态"""
    stmt = select(UserScheduledTask).where(
        and_(UserScheduledTask.id == task_id, UserScheduledTask.user_id == current_user.id)
    )
    result = await db.execute(stmt)
    task = result.scalar_one_or_none()
    
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    try:
        task.is_active = is_active
        
        # 如果启用任务，重新计算下次执行时间
        if is_active:
            try:
                cron = croniter(task.cron_expression)
                task.next_execution = cron.get_next(datetime)
            except Exception:
                await db.rollback()
                raise HTTPException(status_code=400, detail="任务Cron表达式无效，无法启用")
        else:
            task.next_execution = None
        
        await db.commit()
        return {"message": f"任务已{'启用' if is_active else '禁用'}"}
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"更新任务状态失败: {str(e)}")