"""
指数K线数据分析API接口

提供指数日K/周K/月K数据获取和统计功能，支持同时获取相关技术指标
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import date, timedelta, datetime

from app.core.database import get_db
from app.services.storage.index_storage import IndexStorageService
from app.services.analytics.index_kline_service import IndexKlineAnalysisService
from app.utils.decorators import smart_data_cache
from app.core.data.models import DataType, CacheStrategy
from app.schemas.common import CommonResponse

router = APIRouter(
    prefix="/index-kline",
    responses={404: {"description": "指数数据不存在"}}
)

# 依赖注入：创建指数K线分析服务
async def get_index_kline_service(db: AsyncSession = Depends(get_db)):
    """获取指数K线分析服务实例"""
    storage = IndexStorageService(db)
    return IndexKlineAnalysisService(storage)

@router.get("/{index_code}", response_model=CommonResponse[Dict[str, Any]])
# @smart_data_cache(
#     data_type=DataType.STOCK_DAILY,  # 复用股票缓存类型
#     cache_strategy=CacheStrategy.CACHE_FIRST,
#     cache_ttl=1800  # 30分钟缓存
# )
async def get_index_kline_data(
    index_code: str,
    freq: str = Query("D", regex="^[DWM]$", description="数据周期：D(日线)、W(周线)、M(月线)"),
    start_date: Optional[str] = Query(None, description="开始日期，格式：YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式：YYYY-MM-DD"),
    with_indicators: bool = Query(False, description="是否包含技术指标"),
    indicators: List[str] = Query(None, description="技术指标列表，例如：macd,kdj,rsi,volume"),
    kline_service: IndexKlineAnalysisService = Depends(get_index_kline_service)
):
    """
    获取指数K线数据
    
    支持日K/周K/月K数据，可选是否包含技术指标
    """
    # 设置默认日期
    if not end_date:
        end_date = date.today().isoformat()
    if not start_date:
        # 根据周期设置不同的默认时间范围
        days_back = 100  # 日线默认100天
        if freq == 'W':
            days_back = 52 * 7  # 周线默认52周
        elif freq == 'M':
            days_back = 24 * 30  # 月线默认24个月
        start_date = (date.fromisoformat(end_date) - timedelta(days=days_back)).isoformat()
    
    # 处理指标列表
    indicator_list = []
    if with_indicators and indicators:
        indicator_list = [i.strip().lower() for i in indicators]
    
    try:
        # 获取数据（缓存由装饰器自动处理）
        result = await kline_service.get_kline_data(
            index_code, freq, start_date, end_date, with_indicators, indicator_list
        )
        
        return CommonResponse(data=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取指数K线数据失败: {str(e)}")