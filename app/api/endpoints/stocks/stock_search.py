"""
股票搜索API接口
提供股票搜索功能，支持多种搜索方式和排序
"""
import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.services.storage.stock_storage import StockStorageService
from app.utils.decorators import smart_data_cache
from app.core.data.models import DataType, CacheStrategy
from app.schemas.common import CommonResponse

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    tags=["StockSearch"],
    responses={
        404: {"description": "未找到匹配的股票"},
        500: {"description": "搜索服务失败"}
    }
)

# 定义响应模型
class StockSearchResult(BaseModel):
    """股票搜索结果模型"""
    code: str = Field(..., description="股票代码")
    name: str = Field(..., description="股票名称")
    exchange: str = Field(..., description="交易所代码")
    industry: Optional[str] = Field(None, description="行业分类")
    sector: Optional[str] = Field(None, description="板块")
    full_code: str = Field(..., description="完整股票代码")
    market_cap: Optional[int] = Field(None, description="市值")
    listing_date: Optional[str] = Field(None, description="上市日期")
    match_type: str = Field(..., description="匹配类型: code/name/industry")
    
    class Config:
        from_attributes = True

class StockSearchResponse(BaseModel):
    """股票搜索响应模型"""
    results: List[StockSearchResult] = Field(..., description="搜索结果列表")
    total: int = Field(..., description="匹配的总数量")
    query: str = Field(..., description="搜索查询字符串")
    
    class Config:
        from_attributes = True

# 依赖注入：创建股票存储服务
async def get_stock_storage(db: AsyncSession = Depends(get_db)):
    """获取股票存储服务实例"""
    return StockStorageService(db)

@router.get("/search", response_model=CommonResponse[StockSearchResponse])
@smart_data_cache(
    data_type=DataType.STOCK_INFO,
    cache_strategy=CacheStrategy.CACHE_FIRST,
    cache_ttl=600  # 10分钟缓存
)
async def search_stocks(
    q: str = Query(..., min_length=1, max_length=50, description="搜索关键词"),
    limit: int = Query(20, ge=1, le=100, description="返回结果数量限制"),
    exchange: Optional[str] = Query(None, description="交易所筛选 (SH/SZ)"),
    industry: Optional[str] = Query(None, description="行业筛选"),
    sort_by: str = Query("relevance", regex="^(relevance|code|name|market_cap)$", description="排序方式"),
    stock_storage: StockStorageService = Depends(get_stock_storage)
):
    """
    搜索股票
    
    支持以下搜索方式：
    - 股票代码模糊匹配
    - 股票名称模糊匹配  
    - 行业分类匹配
    - 可按交易所、行业筛选
    - 支持多种排序方式
    """
    try:
        # 执行搜索（缓存由装饰器自动处理）
        search_results = await stock_storage.search_stocks(
            query=q,
            limit=limit,
            exchange=exchange,
            industry=industry,
            sort_by=sort_by
        )
        
        # 构建响应数据
        response_data = StockSearchResponse(
            results=search_results,
            total=len(search_results),
            query=q
        )
        
        return CommonResponse(
            data=response_data,
            message=f"找到 {len(search_results)} 只匹配的股票"
        )
        
    except Exception as e:
        logger.error(f"股票搜索失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"搜索服务失败: {str(e)}")

@router.get("/quick-search", response_model=CommonResponse[List[StockSearchResult]])
@smart_data_cache(
    data_type=DataType.STOCK_INFO,
    cache_strategy=CacheStrategy.CACHE_FIRST,
    cache_ttl=300  # 5分钟缓存
)
async def quick_search_stocks(
    q: str = Query(..., min_length=1, max_length=20, description="快速搜索关键词"),
    limit: int = Query(10, ge=1, le=20, description="返回结果数量限制"),
    stock_storage: StockStorageService = Depends(get_stock_storage)
):
    """
    快速搜索股票 (用于搜索建议)
    
    优化的快速搜索接口，适用于实时搜索建议
    """
    try:
        # 执行快速搜索（缓存由装饰器自动处理）
        search_results = await stock_storage.quick_search_stocks(q, limit)
        
        return CommonResponse(
            data=search_results,
            message=f"快速搜索找到 {len(search_results)} 只股票"
        )
        
    except Exception as e:
        logger.error(f"快速搜索失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"快速搜索失败: {str(e)}")

@router.get("/popular", response_model=CommonResponse[List[StockSearchResult]])
async def get_popular_stocks(
    limit: int = Query(10, ge=1, le=50, description="返回数量限制"),
    exchange: Optional[str] = Query(None, description="交易所筛选"),
    stock_storage: StockStorageService = Depends(get_stock_storage)
):
    """
    获取热门股票列表
    
    返回按市值或活跃度排序的热门股票，用于搜索推荐
    """
    try:
        # 获取热门股票（缓存由装饰器自动处理）
        popular_stocks = await stock_storage.get_popular_stocks(limit, exchange)
        
        return CommonResponse(
            data=popular_stocks,
            message=f"获取到 {len(popular_stocks)} 只热门股票"
        )
        
    except Exception as e:
        logger.error(f"获取热门股票失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取热门股票失败: {str(e)}")
