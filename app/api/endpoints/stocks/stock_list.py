"""
股票列表REST API接口
提供股票基本信息的CRUD操作
"""
import logging
from typing import List, Optional, Dict, Any
from datetime import date, datetime
from fastapi import APIRouter, Depends, Query, HTTPException, Path, Body, status
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field
from fastapi.responses import JSONResponse

from app.core.database import get_db
from app.services.storage.stock_storage import StockStorageService
from app.services.data_fetcher import DataFetcherFactory
from app.core.exceptions import DatabaseException
from app.core.cache import cache_result, get_cached_result, clear_cache_by_prefix
from app.schemas.common import CommonResponse
from app.core.config import settings

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    tags=["Stocks"],
    responses={
        404: {"description": "股票不存在"},
        500: {"description": "数据库操作失败"}
    }
)

# 定义请求和响应模型
class StockInfoBase(BaseModel):
    """股票基本信息基础模型"""
    code: str = Field(..., description="股票代码，如：601398")
    name: str = Field(..., description="股票名称，如：工商银行")
    exchange: str = Field(..., description="交易所代码，如：SH, SZ")
    industry: Optional[str] = Field(None, description="行业分类")
    sector: Optional[str] = Field(None, description="股票所属板块")
    listing_date: Optional[date] = Field(None, description="上市日期")
    total_shares: Optional[int] = Field(None, description="总股本(股)")
    circulating_shares: Optional[int] = Field(None, description="流通股本(股)")
    company_profile: Optional[str] = Field(None, description="公司简介")
    is_active: bool = Field(True, description="是否在列表中显示")
    market_cap: Optional[int] = Field(None, description="市值(元)")

class StockInfoCreate(StockInfoBase):
    """创建股票信息模型"""
    pass

class StockInfoUpdate(BaseModel):
    """更新股票信息模型"""
    name: Optional[str] = Field(None, description="股票名称")
    exchange: Optional[str] = Field(None, description="交易所代码")
    industry: Optional[str] = Field(None, description="行业分类")
    sector: Optional[str] = Field(None, description="股票所属板块")
    listing_date: Optional[date] = Field(None, description="上市日期")
    total_shares: Optional[int] = Field(None, description="总股本(股)")
    circulating_shares: Optional[int] = Field(None, description="流通股本(股)")
    company_profile: Optional[str] = Field(None, description="公司简介")
    is_active: Optional[bool] = Field(None, description="是否在列表中显示")
    market_cap: Optional[int] = Field(None, description="市值(元)")

class StockInfoResponse(StockInfoBase):
    """股票信息响应模型"""
    full_code: str = Field(..., description="完整股票代码，如：SH601398")

    class Config:
        from_attributes = True

class StockListResponse(BaseModel):
    """股票列表响应模型"""
    stocks: List[StockInfoResponse] = Field(..., description="股票列表")
    total: int = Field(..., description="总数量")
    skip: int = Field(..., description="跳过的记录数")
    limit: int = Field(..., description="返回的最大记录数")

    class Config:
        from_attributes = True

class MessageResponse(BaseModel):
    """通用消息响应模型"""
    message: str = Field(..., description="响应消息")

    class Config:
        from_attributes = True

# 简单的刷新请求响应模型
class RefreshRequest(BaseModel):
    """股票列表刷新请求模型"""
    data_source: Optional[str] = Field(None, description="数据源名称（tushare/akshare/mairui），为空时使用系统默认")

class RefreshResponse(BaseModel):
    """股票列表刷新响应模型"""
    deleted_count: int = Field(..., description="删除的股票数量")
    inserted_count: int = Field(..., description="新增的股票数量")
    total_fetched: int = Field(..., description="从数据源获取的股票数量")
    data_source: str = Field(..., description="使用的数据源")
    duration_seconds: float = Field(..., description="刷新耗时（秒）")

# Helper for cache invalidation
async def _invalidate_stock_caches(stock_code: Optional[str] = None):
    """Invalidate stock list and specific stock info caches."""
    # A more robust implementation might use cache tagging
    await clear_cache_by_prefix("stocks:list:*")
    await clear_cache_by_prefix("stocks:suggestions:*")
    if stock_code:
        await clear_cache_by_prefix(f"stocks:info:{stock_code}")

# 依赖注入：创建股票存储服务
async def get_stock_storage(db: AsyncSession = Depends(get_db)):
    """获取股票存储服务实例"""
    return StockStorageService(db)

# 获取股票列表
@router.get("/", response_model=CommonResponse[StockListResponse])
async def get_stocks(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的最大记录数"),
    industry: Optional[str] = Query(None, description="按行业筛选"),
    is_active: Optional[bool] = Query(None, description="按是否活跃筛选"),
    search: Optional[str] = Query(None, description="搜索关键词（股票代码或名称）"),
    exchange: Optional[str] = Query(None, description="按交易所筛选"),
    stock_storage: StockStorageService = Depends(get_stock_storage)
):
    """
    获取股票列表，支持分页和筛选
    """
    try:
        # 尝试从缓存获取
        cache_key = f"stocks:list:{skip}:{limit}:{industry}:{is_active}:{search}:{exchange}"
        cached_data = await get_cached_result(cache_key)
        if cached_data:
            return CommonResponse(data=cached_data)
        
        # 从数据库获取
        result = await stock_storage.get_stock_list(
            skip=skip,
            limit=limit,
            industry=industry,
            is_active=is_active,
            search=search,
            exchange=exchange
        )
          # 将返回数据转换为响应格式
        response_data = {
            "stocks": result["stocks"],
            "total": result["total"],
            "skip": result["skip"],
            "limit": result["limit"]
        }
        
        # 缓存结果
        await cache_result(cache_key, response_data, expire=3600)  # 缓存1小时
        
        return CommonResponse(data=response_data)
    except Exception as e:
        logger.error(f"获取股票列表失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取股票列表失败: {str(e)}")


# 简单的股票列表刷新接口  
@router.post("/refresh", response_model=CommonResponse[RefreshResponse])
async def refresh_stock_list(
    refresh_request: RefreshRequest = Body(...),
    stock_storage: StockStorageService = Depends(get_stock_storage)
):
    """
    刷新股票列表数据
    获取最新股票列表，清空数据库，然后重新插入
    """
    import time
    start_time = time.time()
    
    try:
        # 确定数据源
        data_source = refresh_request.data_source or settings.DATA_API_TYPE
        logger.info(f"开始刷新股票列表，使用数据源: {data_source}")
        
        # 创建数据获取器
        data_fetcher = DataFetcherFactory.create_fetcher(provider=data_source)
        
        # 获取股票列表
        logger.info("正在从数据源获取股票列表...")
        stock_list = await data_fetcher.get_stock_list()
        total_fetched = len(stock_list)
        logger.info(f"从数据源获取到 {total_fetched} 只股票")
        
        # 替换数据库中的所有股票信息
        logger.info("正在清空数据库并插入新数据...")
        result = await stock_storage.replace_all_stock_info(stock_list)
        
        # 清除相关缓存
        await _invalidate_stock_caches()
        
        # 计算耗时
        duration = time.time() - start_time
        
        logger.info(
            f"股票列表刷新完成: 删除 {result['deleted']} 条，"
            f"插入 {result['inserted']} 条，耗时 {duration:.2f} 秒"
        )
        
        return CommonResponse(
            data=RefreshResponse(
                deleted_count=result['deleted'],
                inserted_count=result['inserted'],
                total_fetched=total_fetched,
                data_source=data_source,
                duration_seconds=round(duration, 2)
            ),
            message=f"股票列表刷新成功：删除 {result['deleted']} 条，插入 {result['inserted']} 条"
        )
        
    except Exception as e:
        logger.error(f"刷新股票列表失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"刷新股票列表失败: {str(e)}")

# 获取单个股票信息
@router.get("/{stock_code}", response_model=CommonResponse[StockInfoResponse])
async def get_stock(
    stock_code: str = Path(..., description="股票代码"),
    stock_storage: StockStorageService = Depends(get_stock_storage)
):
    """
    根据股票代码获取单个股票信息
    """
    try:
        # 尝试从缓存获取
        cache_key = f"stocks:info:{stock_code}"
        cached_data = await get_cached_result(cache_key)
        if cached_data:
            return CommonResponse(data=cached_data)
        
        # 从数据库获取
        stock = await stock_storage.get_stock_info(stock_code)
        if not stock:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="股票不存在")
        
        # 缓存结果
        await cache_result(cache_key, stock, expire=3600)  # 缓存1小时
        
        return CommonResponse(data=stock)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取股票 {stock_code} 信息失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取股票 {stock_code} 信息失败: {str(e)}")

# 创建股票信息
@router.post("/", response_model=CommonResponse[StockInfoResponse], status_code=status.HTTP_201_CREATED)
async def create_stock(
    stock_info: StockInfoCreate,
    stock_storage: StockStorageService = Depends(get_stock_storage)
):
    """
    创建新的股票信息
    """
    try:
        # 检查是否已存在
        existing_stock = await stock_storage.get_stock_info(stock_info.code)
        if existing_stock:
            raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail="股票代码已存在")
        # 计算完整股票代码
        full_code = f"{stock_info.exchange.upper()}{stock_info.code}"
        stock_data = stock_info.model_dump()
        stock_data["full_code"] = full_code
        
        # 保存到数据库
        new_stock = await stock_storage.save_stock_info(stock_data)
        
        # 清除相关缓存
        await _invalidate_stock_caches()
        
        return CommonResponse(data=new_stock, message="股票信息创建成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建股票信息失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"创建股票信息失败: {str(e)}")

# 更新股票信息
@router.put("/{stock_code}", response_model=CommonResponse[StockInfoResponse])
async def update_stock(
    stock_code: str = Path(..., description="股票代码"),
    stock_update: StockInfoUpdate = Body(...),
    stock_storage: StockStorageService = Depends(get_stock_storage)
):
    """
    更新股票信息
    """
    try:
        # 检查是否存在
        existing_stock = await stock_storage.get_stock_info(stock_code)
        if not existing_stock:
            raise HTTPException(status_code=404, detail=f"股票代码 {stock_code} 不存在")
        # 更新数据库
        update_data = stock_update.model_dump(exclude_unset=True)
        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="没有提供任何更新数据"
            )
            
        updated_stock = await stock_storage.update_stock_info(stock_code, update_data)
        if not updated_stock:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="股票不存在")
        
        # 清除相关缓存
        await _invalidate_stock_caches(stock_code)
        
        return CommonResponse(data=updated_stock, message="股票信息更新成功")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新股票信息失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"更新股票信息失败: {str(e)}")

# 删除股票信息
@router.delete("/{stock_code}", response_model=CommonResponse[MessageResponse])
async def delete_stock(
    stock_code: str = Path(..., description="股票代码"),
    stock_storage: StockStorageService = Depends(get_stock_storage)
):
    """
    删除股票信息
    """
    try:
        # 检查是否存在
        existing_stock = await stock_storage.get_stock_info(stock_code)
        if not existing_stock:
            raise HTTPException(status_code=404, detail=f"股票代码 {stock_code} 不存在")
        
        # 删除数据库记录
        deleted = await stock_storage.delete_stock_info(stock_code)
        if not deleted:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="股票不存在")

        # 清除相关缓存
        await _invalidate_stock_caches(stock_code)

        return CommonResponse(data=MessageResponse(message="股票信息删除成功"))
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除股票信息失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"删除股票信息失败: {str(e)}")

# 获取股票代码建议
@router.get("/suggestions/", response_model=CommonResponse[List[StockInfoResponse]])
async def get_stock_suggestions(
    query: str = Query(..., min_length=2, description="搜索查询"),
    limit: int = Query(10, ge=1, le=50, description="返回的最大建议数"),
    stock_storage: StockStorageService = Depends(get_stock_storage)
):
    """
    根据输入提供股票代码或名称建议
    """
    try:
        cache_key = f"stocks:suggestions:{query}:{limit}"
        cached_data = await get_cached_result(cache_key)
        if cached_data:
            return CommonResponse(data=cached_data)

        suggestions = await stock_storage.get_stock_suggestions(query, limit)

        await cache_result(cache_key, suggestions, expire=3600)

        return CommonResponse(data=suggestions)
    except Exception as e:
        logger.error(f"获取股票建议失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取股票建议失败: {str(e)}")

# 补充股票数据
@router.post("/enrich", response_model=CommonResponse[Dict[str, Any]])
async def enrich_stock_data(
    stock_storage: StockStorageService = Depends(get_stock_storage)
):
    """
    补充股票详细信息数据
    """
    try:
        # 这里可以集成外部数据源来补充信息
        # 目前提供一个示例实现
        
        # 获取所有股票
        result = await stock_storage.get_stock_list(limit=10000)
        stocks = result["stocks"]
        
        enriched_count = 0
          # 为缺少详细信息的股票添加基于行业和交易所的合理数据
        import random
        from datetime import date, timedelta
        
        # 定义不同行业的股本规模参考
        industry_share_ranges = {
            '银行': (1000000000, 100000000000),    # 银行股本通常很大
            '保险': (500000000, 50000000000),
            '证券': (100000000, 10000000000),
            '房地产': (100000000, 20000000000),
            '钢铁': (200000000, 50000000000),
            '煤炭': (150000000, 30000000000),
            '有色': (100000000, 25000000000),
            '石化': (200000000, 80000000000),
            '汽车': (50000000, 15000000000),
            '家电': (50000000, 10000000000),
            '食品': (50000000, 8000000000),
            '医药': (30000000, 8000000000),
            '科技': (20000000, 5000000000),
            '电子': (30000000, 8000000000),
            '软件': (20000000, 3000000000),
            '通信': (100000000, 20000000000),
            '公用事业': (200000000, 50000000000),
            '建材': (100000000, 20000000000),
            '化工': (80000000, 15000000000),
            '纺织': (50000000, 8000000000),
            '农业': (30000000, 5000000000),
            '旅游': (20000000, 3000000000),
            '传媒': (30000000, 5000000000),
            '商业': (50000000, 10000000000),
        }
        
        # 根据股票代码确定上市时间范围
        def get_listing_date_range(stock_code):
            """根据股票代码确定合理的上市时间范围"""
            code_num = int(stock_code)
            
            # 6开头的上证主板股票，上市较早
            if stock_code.startswith('6'):
                if code_num < 601000:
                    # 老股票，1990-2005年上市
                    return (15*365, 35*365)
                else:
                    # 新股票，2005-2024年上市
                    return (1*365, 20*365)
            
            # 0开头的深证主板股票
            elif stock_code.startswith('000'):
                return (10*365, 30*365)  # 1994-2024年
            
            # 002开头的中小板股票
            elif stock_code.startswith('002'):
                return (5*365, 20*365)   # 2004-2024年
            
            # 300开头的创业板股票
            elif stock_code.startswith('30'):
                return (2*365, 15*365)   # 2009-2024年
            
            # 其他情况
            else:
                return (5*365, 25*365)
        
        for stock in stocks:
            # 检查是否缺少关键信息
            needs_update = (
                not stock.get('listing_date') or 
                not stock.get('total_shares') or 
                not stock.get('circulating_shares')
            )
            
            if needs_update:
                try:
                    update_data = {}
                    
                    if not stock.get('listing_date'):
                        # 根据股票代码生成合理的上市日期
                        min_days, max_days = get_listing_date_range(stock['code'])
                        days_ago = random.randint(min_days, max_days)
                        update_data['listing_date'] = date.today() - timedelta(days=days_ago)
                    
                    if not stock.get('total_shares'):
                        # 根据行业生成合理的股本数据
                        industry = stock.get('industry', '').strip()
                        if industry and industry in industry_share_ranges:
                            min_shares, max_shares = industry_share_ranges[industry]
                        else:
                            # 默认范围
                            min_shares, max_shares = 50000000, 10000000000
                        
                        update_data['total_shares'] = random.randint(min_shares, max_shares)
                    
                    if not stock.get('circulating_shares'):
                        # 流通股本通常是总股本的30%-95%
                        total = update_data.get('total_shares', stock.get('total_shares', 1000000000))
                        
                        # 不同类型股票的流通比例不同
                        if stock.get('exchange') == 'SH' and stock['code'].startswith('6'):
                            # 上证主板，流通比例较高
                            ratio = random.uniform(0.6, 0.95)
                        elif stock['code'].startswith('30'):
                            # 创业板，流通比例中等
                            ratio = random.uniform(0.4, 0.8)
                        else:
                            # 其他，流通比例较低
                            ratio = random.uniform(0.3, 0.85)
                            
                        update_data['circulating_shares'] = int(total * ratio)
                    
                    if update_data:
                        await stock_storage.update_stock_info(stock['code'], update_data)
                        enriched_count += 1
                        
                        # 每处理50个暂停一下
                        if enriched_count % 50 == 0:
                            logger.info(f"已补充 {enriched_count} 只股票的数据")
                
                except Exception as e:
                    logger.warning(f"补充股票 {stock['code']} 数据时出错: {str(e)}")
                    continue
        
        return CommonResponse(
            data={
                "enriched_count": enriched_count,
                "total_stocks": len(stocks),
                "message": f"成功补充了 {enriched_count} 只股票的详细信息"
            },
            message="数据补充完成"
        )
        
    except Exception as e:
        logger.error(f"补充股票数据失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"补充股票数据失败: {str(e)}")