"""认证相关API端点"""
from fastapi import APIRouter, Depends, HTTPException, status

from app.schemas.auth import (
    LoginRequest, 
    LoginResponse, 
    TokenRefreshRequest,
    UserProfileResponse,
    LogoutResponse
)
from app.services.user_service import UserService
from app.utils.jwt_auth import refresh_token, revoke_user_tokens
from app.utils.dependencies import get_current_user
from app.models.user import User

router = APIRouter(prefix="/auth", tags=["认证"])


@router.post("/login", response_model=LoginResponse)
async def login(login_data: LoginRequest):
    """用户登录"""
    result = await UserService.login_user(login_data.username, login_data.password)
    if not result:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )
    return result


@router.post("/refresh")
async def refresh_access_token(
    request: TokenRefreshRequest
):
    """刷新访问token"""
    new_token = await refresh_token(request.token)
    if not new_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token刷新失败，请重新登录"
        )
    return {
        'access_token': new_token,
        'token_type': 'bearer',
        'expires_in': 24 * 3600
    }


@router.get("/me", response_model=UserProfileResponse)
async def get_profile(current_user: User = Depends(get_current_user)):
    """获取当前用户信息"""
    return UserProfileResponse(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        is_admin=current_user.is_admin,
        last_login=current_user.last_login
    )


@router.post("/logout", response_model=LogoutResponse)
async def logout(current_user: User = Depends(get_current_user)):
    """用户登出（撤销所有token）"""
    await revoke_user_tokens(current_user.id)
    return LogoutResponse(message="退出登录成功")


@router.post("/revoke-tokens")
async def revoke_all_tokens(current_user: User = Depends(get_current_user)):
    """撤销当前用户的所有token（强制重新登录）"""
    success = await revoke_user_tokens(current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="撤销token失败"
        )
    return {"message": "所有token已撤销，需要重新登录"}