"""
数据库连接模块 - 同步版本

管理数据库连接、会话和事务的同步版本。
"""

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from typing import Generator

from app.core.config import settings

# SQLite性能优化函数
def optimize_sqlite_connection(connection, connection_record):
    """SQLite连接优化回调函数"""
    cursor = connection.cursor()
    try:
        # 启用WAL模式以支持并发读写
        cursor.execute("PRAGMA journal_mode = WAL")
        
        # 设置同步模式为NORMAL以平衡性能和安全性
        cursor.execute("PRAGMA synchronous = NORMAL")
        
        # 增加缓存大小 (单位: 页，每页通常4KB，这里设置为64MB)
        cursor.execute("PRAGMA cache_size = -16384")
        
        # 启用外键约束
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # 设置临时存储为内存
        cursor.execute("PRAGMA temp_store = MEMORY")
        
        # 设置内存映射大小 (256MB)
        cursor.execute("PRAGMA mmap_size = 268435456")
        
        # 设置页面大小为4KB（默认值，但明确设置以确保一致性）
        cursor.execute("PRAGMA page_size = 4096")
    finally:
        cursor.close()

# 创建同步引擎
sync_engine = create_engine(
    settings.DATABASE_URL,
    pool_pre_ping=True,
    echo=settings.SQL_ECHO,
    pool_recycle=3600,
    # SQLite特定优化
    connect_args={
        "check_same_thread": False,  # 允许多线程访问
    },
)

# 为SQLite连接添加优化
from sqlalchemy import event
@event.listens_for(sync_engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """为每个新的SQLite连接设置PRAGMA"""
    optimize_sqlite_connection(dbapi_connection, connection_record)

# 创建同步会话工厂
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=sync_engine,
)

def get_db() -> Generator[Session, None, None]:
    """
    提供同步数据库会话
    
    用作API依赖项，自动管理会话的生命周期。
    
    Yields:
        Session: 数据库会话
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
