"""
数据库连接模块

管理数据库连接、会话和事务。
"""

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from typing import AsyncGenerator
from contextlib import asynccontextmanager

from app.core.config import settings

# 修改数据库URL以支持异步
db_url = settings.DATABASE_URL.replace('sqlite:///', 'sqlite+aiosqlite:///')

# SQLite性能优化函数
def optimize_sqlite_connection(connection, connection_record):
    """SQLite连接优化回调函数"""
    cursor = connection.cursor()
    try:
        # 启用WAL模式以支持并发读写
        cursor.execute("PRAGMA journal_mode = WAL")
        
        # 设置同步模式为NORMAL以平衡性能和安全性
        cursor.execute("PRAGMA synchronous = NORMAL")
        
        # 增加缓存大小 (单位: 页，每页通常4KB，这里设置为64MB)
        cursor.execute("PRAGMA cache_size = -16384")
        
        # 启用外键约束
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # 设置临时存储为内存
        cursor.execute("PRAGMA temp_store = MEMORY")
        
        # 设置内存映射大小 (256MB)
        cursor.execute("PRAGMA mmap_size = 268435456")
        
        # 设置页面大小为4KB（默认值，但明确设置以确保一致性）
        cursor.execute("PRAGMA page_size = 4096")
        
        # 设置繁忙超时为30秒，减少数据库锁冲突
        cursor.execute("PRAGMA busy_timeout = 30000")
        
        # 禁用自动检查点以减少写冲突
        cursor.execute("PRAGMA wal_autocheckpoint = 0")
    finally:
        cursor.close()

# 创建异步引擎
engine = create_async_engine(
    db_url,
    pool_pre_ping=True,
    echo=settings.SQL_ECHO,
    pool_recycle=3600,
    # 连接池配置 - 增加以支持更多并发任务和减少锁冲突
    pool_size=15,                   # 增加连接池大小从10到15
    max_overflow=25,                # 增加溢出连接数从20到25
    pool_timeout=60,                # 增加获取连接的超时时间从30到60秒
    # SQLite特定优化
    connect_args={
        "check_same_thread": False,  # 允许多线程访问
    },
)

# 为SQLite连接添加优化
from sqlalchemy import event
import logging

logger = logging.getLogger(__name__)

@event.listens_for(engine.sync_engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """为每个新的SQLite连接设置PRAGMA"""
    optimize_sqlite_connection(dbapi_connection, connection_record)
    logger.debug(f"新建SQLite连接: {id(dbapi_connection)}")

@event.listens_for(engine.sync_engine, "checkout")
def receive_checkout(dbapi_connection, connection_record, connection_proxy):
    """连接池签出事件"""
    logger.debug(f"连接签出: {id(dbapi_connection)}")

@event.listens_for(engine.sync_engine, "checkin")
def receive_checkin(dbapi_connection, connection_record):
    """连接池签入事件"""
    logger.debug(f"连接签入: {id(dbapi_connection)}")

# 创建异步会话工厂
AsyncSessionLocal = async_sessionmaker(
    engine,
    expire_on_commit=False,
    class_=AsyncSession,
    autoflush=True,
    autocommit=False,
)

# 基本声明类
Base = declarative_base()

async def get_db() -> AsyncSession:
    """
    提供异步数据库会话，用于FastAPI依赖项
    """
    session = AsyncSessionLocal()
    try:
        yield session
    finally:
        await session.close()

@asynccontextmanager
async def db_session():
    """
    异步上下文管理器提供数据库会话
    用法:
        async with db_session() as session:
            await session.execute(...)
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()  # 自动提交事务
        except Exception:
            await session.rollback()  # 出错时回滚
            raise
        finally:
            await session.close()
