"""
增强的缓存装饰器

提供灵活、强大的缓存装饰器，支持多种配置和高级功能。
"""
import functools
import hashlib
import json
import asyncio
from typing import Any, Callable, Optional, Union, Dict, List
from datetime import timedelta

from .manager import CacheManager
from .config import CacheProfile, CacheConfig
from .stats import global_cache_stats
from app.core import logging

logger = logging.getLogger(__name__)


def cache_key_builder(
    func_name: str, 
    args: tuple, 
    kwargs: dict,
    namespace: Optional[str] = None,
    version: int = 1,
    include_self: bool = False
) -> str:
    """
    构建缓存键
    
    Args:
        func_name: 函数名
        args: 位置参数
        kwargs: 关键字参数
        namespace: 命名空间前缀
        version: 版本号
        include_self: 是否包含self参数（用于类方法）
        
    Returns:
        缓存键字符串
    """
    key_parts = []
    
    # 添加命名空间
    if namespace:
        key_parts.append(namespace)
    
    # 添加版本
    key_parts.append(f"v{version}")
    
    # 添加函数名
    key_parts.append(func_name)
    
    # 处理位置参数
    start_index = 0 if include_self else 1  # 跳过self/cls参数
    
    if args and len(args) > start_index:
        for arg in args[start_index:]:
            if isinstance(arg, (str, int, float, bool)):
                key_parts.append(str(arg))
            elif arg is None:
                key_parts.append("None")
            else:
                # 对于复杂对象，使用其字符串表示的哈希值
                arg_str = json.dumps(arg, sort_keys=True, default=str) if hasattr(arg, '__dict__') else str(arg)
                key_parts.append(hashlib.md5(arg_str.encode()).hexdigest()[:8])
    
    # 处理关键字参数（按键排序保证一致性）
    if kwargs:
        for key in sorted(kwargs.keys()):
            value = kwargs[key]
            if isinstance(value, (str, int, float, bool)) or value is None:
                key_parts.append(f"{key}:{value}")
            else:
                value_str = json.dumps(value, sort_keys=True, default=str) if hasattr(value, '__dict__') else str(value)
                key_parts.append(f"{key}:{hashlib.md5(value_str.encode()).hexdigest()[:8]}")
    
    return ":".join(key_parts)


def cache(
    profile: Union[str, CacheProfile] = "default",
    expire: Optional[Union[int, timedelta]] = None,
    namespace: Optional[str] = None,
    key_func: Optional[Callable] = None,
    condition: Optional[Callable] = None,
    tags: Optional[List[str]] = None,
    version: int = 1,
    enabled: Optional[bool] = None,
    stats: bool = True
):
    """
    缓存装饰器
    
    Args:
        profile: 缓存配置文件名或CacheProfile实例
        expire: 过期时间，覆盖配置文件中的设置
        namespace: 命名空间，覆盖配置文件中的设置
        key_func: 自定义键生成函数
        condition: 缓存条件函数，返回True才缓存
        tags: 缓存标签列表
        version: 缓存版本
        enabled: 是否启用缓存
        stats: 是否记录统计信息
        
    使用示例:
        @cache(profile="indicators", expire=1800)
        async def calculate_macd(stock_code: str, start_date: str):
            # 复杂的计算逻辑
            return result
        
        @cache(
            expire=3600,
            condition=lambda result: result is not None,
            tags=["market_data"]
        )
        async def get_stock_data(code: str):
            return data
    """
    def decorator(func: Callable) -> Callable:
        # 获取缓存配置
        if isinstance(profile, str):
            cache_profile = CacheConfig.get_profile(profile)
        else:
            cache_profile = profile or CacheProfile()
        
        # 覆盖配置参数
        if expire is not None:
            cache_profile.expire = expire
        if namespace is not None:
            cache_profile.namespace = namespace
        if condition is not None:
            cache_profile.condition = condition
        if tags is not None:
            cache_profile.tags = tags
        if version != 1:
            cache_profile.version = version
        if enabled is not None:
            cache_profile.enabled = enabled
        
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            # 检查是否启用缓存
            if not cache_profile.enabled:
                return await func(*args, **kwargs)
            
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = cache_key_builder(
                    func.__name__, args, kwargs,
                    namespace=cache_profile.namespace,
                    version=cache_profile.version
                )
            
            # 从全局缓存管理器获取缓存（需要在应用启动时设置）
            cache_manager = getattr(cache, '_cache_manager', None)
            if not cache_manager:
                logger.warning("缓存管理器未初始化，直接执行函数")
                return await func(*args, **kwargs)
            
            # 尝试从缓存获取
            cached_value = await cache_manager.get(cache_key, cache_profile)
            if cached_value is not None:
                if stats:
                    global_cache_stats.record_hit(
                        cache_key, 0,  # 延迟在manager中记录
                        namespace=cache_profile.namespace
                    )
                logger.debug(f"缓存命中: {cache_key}")
                return cached_value
            
            # 执行函数
            try:
                result = await func(*args, **kwargs)
                
                # 检查缓存条件
                should_cache = True
                if cache_profile.condition:
                    should_cache = cache_profile.condition(result)
                
                # 缓存结果
                if should_cache and result is not None:
                    await cache_manager.set(cache_key, result, cache_profile)
                    logger.debug(f"缓存结果: {cache_key}")
                
                return result
                
            except Exception as e:
                if stats:
                    global_cache_stats.record_error(
                        str(type(e).__name__),
                        namespace=cache_profile.namespace
                    )
                raise
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            # 同步函数包装器
            return asyncio.create_task(async_wrapper(*args, **kwargs))
        
        # 根据函数类型选择包装器
        if asyncio.iscoroutinefunction(func):
            wrapper = async_wrapper
        else:
            wrapper = sync_wrapper
        
        # 添加缓存管理方法
        wrapper.cache_profile = cache_profile
        wrapper.clear_cache = lambda: clear_function_cache(func.__name__, cache_profile.namespace)
        wrapper.invalidate_cache = lambda **kw: invalidate_function_cache(func.__name__, cache_profile.namespace, **kw)
        
        return wrapper
    
    return decorator


async def clear_function_cache(func_name: str, namespace: Optional[str] = None) -> int:
    """
    清除特定函数的所有缓存
    
    Args:
        func_name: 函数名
        namespace: 命名空间前缀
        
    Returns:
        删除的缓存项数量
    """
    cache_manager = getattr(cache, '_cache_manager', None)
    if not cache_manager:
        return 0
    
    # 构建模式
    pattern_parts = []
    if namespace:
        pattern_parts.append(namespace)
    pattern_parts.append("*")  # 版本通配符
    pattern_parts.append(func_name)
    pattern_parts.append("*")  # 参数通配符
    
    pattern = ":".join(pattern_parts)
    
    # 获取匹配的键并删除
    deleted_count = 0
    for backend in cache_manager.backends:
        try:
            keys = await backend.keys(pattern)
            if keys:
                deleted_count += await backend.mdelete(keys)
        except Exception as e:
            logger.error(f"清除函数缓存失败: {e}")
    
    return deleted_count


async def invalidate_function_cache(func_name: str, namespace: Optional[str] = None, **kwargs) -> int:
    """
    根据参数失效特定函数的缓存
    
    Args:
        func_name: 函数名
        namespace: 命名空间
        **kwargs: 用于构建缓存键的参数
        
    Returns:
        失效的缓存数量
    """
    cache_manager = getattr(cache, '_cache_manager', None)
    if not cache_manager:
        return 0
    
    # 构建缓存键
    cache_key = cache_key_builder(func_name, (), kwargs, namespace)
    
    deleted_count = 0
    for backend in cache_manager.backends:
        try:
            if await backend.delete(cache_key):
                deleted_count += 1
        except Exception as e:
            logger.error(f"失效缓存失败: {e}")
    
    return deleted_count


# 预定义的缓存装饰器
stock_cache = functools.partial(cache, profile="stock_data")
indicator_cache = functools.partial(cache, profile="indicators")
scanner_cache = functools.partial(cache, profile="scanner")
kline_cache = functools.partial(cache, profile="kline")
user_cache = functools.partial(cache, profile="user")
short_cache = functools.partial(cache, profile="short")
long_cache = functools.partial(cache, profile="long")
api_cache = functools.partial(cache, profile="api_response")


# 特殊用途的装饰器
def cache_unless_none(profile: str = "default", **kwargs):
    """只有结果不为None时才缓存"""
    return cache(profile=profile, condition=lambda result: result is not None, **kwargs)


def cache_for_user(user_id: str, profile: str = "user", **kwargs):
    """为特定用户缓存"""
    def key_func(*args, **kw):
        base_key = cache_key_builder("user_specific", args, kw)
        return f"user:{user_id}:{base_key}"
    
    return cache(profile=profile, key_func=key_func, **kwargs)


def cache_with_lock(profile: str = "default", lock_timeout: int = 60, **kwargs):
    """带锁的缓存，防止缓存击穿"""
    def decorator(func):
        locks = {}
        
        @cache(profile=profile, **kwargs)
        @functools.wraps(func)
        async def wrapper(*args, **kw):
            # 生成锁键
            lock_key = cache_key_builder(func.__name__, args, kw)
            
            # 获取或创建锁
            if lock_key not in locks:
                locks[lock_key] = asyncio.Lock()
            
            async with locks[lock_key]:
                return await func(*args, **kw)
        
        return wrapper
    return decorator


def set_cache_manager(manager: CacheManager):
    """设置全局缓存管理器"""
    cache._cache_manager = manager