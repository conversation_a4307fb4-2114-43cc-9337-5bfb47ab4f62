"""
缓存配置模块

定义缓存配置文件和配置管理。
"""
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field
from datetime import timedelta
from app.core.config import settings
from .serializers import Serializer, auto_serializer


@dataclass
class CacheProfile:
    """缓存配置文件"""
    
    # 基本配置
    expire: Union[int, timedelta] = 3600  # 默认过期时间（秒）
    namespace: Optional[str] = None  # 命名空间
    enabled: bool = True  # 是否启用
    
    # 序列化配置
    serializer: Optional[Serializer] = None  # 默认使用auto_serializer
    compress: bool = False  # 是否压缩
    compression: str = 'gzip'  # 压缩算法
    
    # 行为配置
    prefix: str = ""  # 键前缀
    version: int = 1  # 版本号，用于缓存失效
    key_func: Optional[callable] = None  # 自定义键生成函数
    condition: Optional[callable] = None  # 缓存条件函数
    
    # 高级配置
    tags: list = field(default_factory=list)  # 默认标签
    ttl_variance: float = 0.1  # TTL随机偏差，避免缓存雪崩
    max_size: Optional[int] = None  # 最大缓存大小（字节）
    
    def __post_init__(self):
        """初始化后处理"""
        if self.serializer is None:
            if self.compress:
                from .serializers import CompressedSerializer
                self.serializer = CompressedSerializer(auto_serializer, self.compression)
            else:
                self.serializer = auto_serializer
        
        # 转换timedelta为秒数
        if isinstance(self.expire, timedelta):
            self.expire = int(self.expire.total_seconds())
    
    def get_expire_with_variance(self) -> int:
        """获取带有随机偏差的过期时间"""
        if self.ttl_variance > 0:
            import random
            variance = int(self.expire * self.ttl_variance)
            return self.expire + random.randint(-variance, variance)
        return self.expire
    
    def make_key(self, base_key: str) -> str:
        """生成最终的缓存键"""
        parts = []
        
        if self.prefix:
            parts.append(self.prefix)
        
        if self.namespace:
            parts.append(self.namespace)
        
        parts.append(f"v{self.version}")
        parts.append(base_key)
        
        return ":".join(parts)


class CacheConfig:
    """缓存配置管理器"""
    
    # 预定义的缓存配置
    profiles: Dict[str, CacheProfile] = {
        # 股票数据缓存（30分钟，启用压缩）
        "stock_data": CacheProfile(
            expire=1800,
            namespace="stock",
            compress=True,
            tags=["market_data"]
        ),
        
        # 技术指标缓存（1小时）
        "indicators": CacheProfile(
            expire=3600,
            namespace="indicator",
            tags=["technical_analysis"]
        ),
        
        # 扫描器缓存（6小时，大数据量）
        "scanner": CacheProfile(
            expire=21600,
            namespace="scanner",
            compress=True,
            tags=["scanner", "batch_data"]
        ),
        
        # K线数据缓存（30分钟）
        "kline": CacheProfile(
            expire=1800,
            namespace="kline",
            tags=["market_data", "chart"]
        ),
        
        # 用户数据缓存（5分钟）
        "user": CacheProfile(
            expire=300,
            namespace="user",
            tags=["user_data"]
        ),
        
        # 短期缓存（5分钟）
        "short": CacheProfile(
            expire=300,
            namespace="short"
        ),
        
        # 长期缓存（24小时）
        "long": CacheProfile(
            expire=86400,
            namespace="long",
            compress=True
        ),
        
        # API响应缓存
        "api_response": CacheProfile(
            expire=1800,
            namespace="api",
            condition=lambda response: response.get("success", False),
            tags=["api"]
        )
    }
    
    @classmethod
    def get_profile(cls, name: str) -> CacheProfile:
        """获取缓存配置"""
        if name in cls.profiles:
            profile = cls.profiles[name]
            # 检查是否全局禁用缓存
            if not settings.CACHE_ENABLED:
                profile.enabled = False
            return profile
        
        # 返回默认配置
        return CacheProfile(enabled=settings.CACHE_ENABLED)
    
    @classmethod
    def register_profile(cls, name: str, profile: CacheProfile):
        """注册新的缓存配置"""
        cls.profiles[name] = profile
    
    @classmethod
    def update_profile(cls, name: str, **kwargs):
        """更新缓存配置"""
        if name in cls.profiles:
            profile = cls.profiles[name]
            for key, value in kwargs.items():
                if hasattr(profile, key):
                    setattr(profile, key, value)
    
    @classmethod
    def get_backend_config(cls) -> Dict[str, Any]:
        """获取缓存后端配置"""
        config = {
            "memory": {
                "enabled": True,
                "max_size": 1000,  # 最大1000个键
                "max_memory_mb": 100,  # 最大100MB
                "eviction_policy": "lru"  # LRU淘汰策略
            },
            "redis": {
                "enabled": bool(settings.REDIS_URL),
                "url": settings.REDIS_URL,
                "max_connections": 50,
                "socket_timeout": 5,
                "retry_on_timeout": True,
                "health_check_interval": 30,
                "decode_responses": False
            },
            "multi_level": {
                "enabled": True,
                "levels": ["memory", "redis"] if settings.REDIS_URL else ["memory"],
                "write_through": True,  # 写穿透
                "read_through": True    # 读穿透
            }
        }
        
        return config


# 缓存配置快捷方式
def get_cache_config(profile_name: str = "default") -> CacheProfile:
    """获取缓存配置的快捷函数"""
    return CacheConfig.get_profile(profile_name)