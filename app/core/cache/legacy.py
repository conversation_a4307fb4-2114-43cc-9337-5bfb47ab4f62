"""
向后兼容层

保持与现有缓存API的兼容性，确保平滑迁移。
"""
import asyncio
from typing import Any, Optional, Dict, Union
from datetime import timedelta

from .manager import CacheManager
from .backends.memory import MemoryCacheBackend
from .backends.redis import RedisCacheBackend
from .config import CacheConfig
from .stats import global_cache_stats
from app.core.config import settings
from app.core import logging

logger = logging.getLogger(__name__)

# 全局缓存管理器实例
_global_cache_manager: Optional[CacheManager] = None


def _get_global_cache_manager() -> CacheManager:
    """获取全局缓存管理器（延迟初始化）"""
    global _global_cache_manager
    
    if _global_cache_manager is None:
        # 创建缓存后端
        backends = []
        
        # 内存缓存作为L1
        memory_backend = MemoryCacheBackend(
            max_size=1000,
            max_memory_mb=100,
            eviction_policy="lru"
        )
        backends.append(memory_backend)
        
        # Redis缓存作为L2（如果配置了）
        if settings.REDIS_URL:
            try:
                redis_backend = RedisCacheBackend(settings.REDIS_URL)
                backends.append(redis_backend)
                logger.info("启用多级缓存：内存 + Redis")
            except Exception as e:
                logger.warning(f"Redis缓存初始化失败，仅使用内存缓存: {e}")
        else:
            logger.info("启用单级缓存：仅内存缓存")
        
        _global_cache_manager = CacheManager(backends, global_cache_stats)
    
    return _global_cache_manager


# === 兼容旧API ===

class MemoryCache:
    """兼容旧的MemoryCache类"""
    
    @classmethod
    async def get(cls, key: str) -> Optional[Any]:
        """获取缓存值"""
        manager = _get_global_cache_manager()
        return await manager.get(key)
    
    @classmethod
    async def set(cls, key: str, value: Any, expire: int = 3600) -> None:
        """设置缓存值"""
        manager = _get_global_cache_manager()
        from .config import CacheProfile
        profile = CacheProfile(expire=expire)
        await manager.set(key, value, profile)
    
    @classmethod
    async def delete(cls, key: str) -> None:
        """删除缓存值"""
        manager = _get_global_cache_manager()
        await manager.delete(key)
    
    @classmethod
    async def clear(cls) -> None:
        """清空所有缓存"""
        manager = _get_global_cache_manager()
        await manager.clear()
    
    @classmethod
    async def clean_expired(cls) -> int:
        """清理过期的缓存项"""
        # 新系统自动处理过期，返回0
        return 0
    
    @classmethod
    async def clear_by_prefix(cls, prefix: str) -> int:
        """根据前缀删除缓存项"""
        manager = _get_global_cache_manager()
        deleted_count = 0
        
        for backend in manager.backends:
            try:
                keys = await backend.keys(f"{prefix}*")
                if keys:
                    deleted_count += await backend.mdelete(keys)
            except Exception as e:
                logger.error(f"按前缀删除缓存失败: {e}")
        
        return deleted_count


class RedisCache:
    """兼容旧的RedisCache类"""
    
    @classmethod
    async def get(cls, key: str) -> Optional[Any]:
        """获取缓存值"""
        manager = _get_global_cache_manager()
        return await manager.get(key)
    
    @classmethod
    async def set(cls, key: str, value: Any, expire: int = 3600) -> None:
        """设置缓存值"""
        manager = _get_global_cache_manager()
        from .config import CacheProfile
        profile = CacheProfile(expire=expire)
        await manager.set(key, value, profile)
    
    @classmethod
    async def delete(cls, key: str) -> None:
        """删除缓存值"""
        manager = _get_global_cache_manager()
        await manager.delete(key)
    
    @classmethod
    async def clear(cls) -> None:
        """清空所有缓存"""
        manager = _get_global_cache_manager()
        await manager.clear()
    
    @classmethod
    async def clean_expired(cls) -> int:
        """清理过期的缓存项"""
        return 0
    
    @classmethod
    async def clear_by_prefix(cls, prefix: str) -> int:
        """根据前缀删除缓存项"""
        return await MemoryCache.clear_by_prefix(prefix)
    
    @classmethod
    async def close(cls):
        """关闭Redis连接"""
        manager = _get_global_cache_manager()
        await manager.close()


def get_cache_client():
    """获取缓存客户端（兼容旧API）"""
    if settings.REDIS_URL:
        return RedisCache
    else:
        return MemoryCache


# 全局缓存客户端实例
cache_client = get_cache_client()


# === 公共API函数 ===

async def get_cached_result(key: str) -> Optional[Any]:
    """
    获取缓存结果（兼容旧API）
    
    Args:
        key: 缓存键
        
    Returns:
        缓存的值，如果不存在或缓存被禁用则返回None
    """
    if not settings.CACHE_ENABLED:
        return None
    
    try:
        manager = _get_global_cache_manager()
        return await manager.get(key)
    except Exception as e:
        logger.error(f"获取缓存失败: {str(e)}")
        return None


async def cache_result(key: str, value: Any, expire: int = 3600) -> None:
    """
    缓存结果（兼容旧API）
    
    Args:
        key: 缓存键
        value: 要缓存的值
        expire: 过期时间（秒），默认1小时
    """
    if not settings.CACHE_ENABLED:
        return
    
    try:
        manager = _get_global_cache_manager()
        from .config import CacheProfile
        profile = CacheProfile(expire=expire)
        await manager.set(key, value, profile)
    except Exception as e:
        logger.error(f"设置缓存失败: {str(e)}")


async def delete_cache(key: str) -> None:
    """
    删除缓存（兼容旧API）
    
    Args:
        key: 缓存键
    """
    try:
        manager = _get_global_cache_manager()
        await manager.delete(key)
    except Exception as e:
        logger.error(f"删除缓存失败: {str(e)}")


async def clear_cache() -> None:
    """清空所有缓存（兼容旧API）"""
    try:
        manager = _get_global_cache_manager()
        await manager.clear()
    except Exception as e:
        logger.error(f"清空缓存失败: {str(e)}")


async def clear_cache_by_prefix(prefix: str) -> int:
    """
    根据前缀删除缓存（兼容旧API）
    
    Args:
        prefix: 缓存键前缀
        
    Returns:
        int: 删除的缓存项数量
    """
    try:
        manager = _get_global_cache_manager()
        deleted_count = 0
        
        for backend in manager.backends:
            try:
                keys = await backend.keys(f"{prefix}*")
                if keys:
                    deleted_count += await backend.mdelete(keys)
            except Exception as e:
                logger.error(f"后端删除缓存失败: {e}")
        
        logger.info(f"缓存已根据前缀 '{prefix}' 清理, {deleted_count} 个条目已删除。")
        return deleted_count
        
    except Exception as e:
        logger.error(f"根据前缀删除缓存失败: {str(e)}")
        return 0


async def clean_expired_cache() -> int:
    """
    清理过期的缓存项（兼容旧API）
    
    Returns:
        int: 清理的缓存项数量
    """
    # 新系统自动处理过期，返回0
    return 0


# === 初始化和关闭函数 ===

async def initialize_cache_system():
    """初始化缓存系统"""
    try:
        manager = _get_global_cache_manager()
        
        # 启动健康检查
        await manager.start_health_check()
        
        # 启动统计清理任务
        await manager.stats.start_cleanup_task()
        
        # 设置装饰器的全局管理器
        from .decorators import set_cache_manager
        set_cache_manager(manager)
        
        logger.info("缓存系统初始化完成")
        
    except Exception as e:
        logger.error(f"缓存系统初始化失败: {e}")
        raise


async def shutdown_cache_system():
    """关闭缓存系统"""
    global _global_cache_manager
    
    try:
        if _global_cache_manager:
            await _global_cache_manager.close()
            _global_cache_manager = None
        
        logger.info("缓存系统已关闭")
        
    except Exception as e:
        logger.error(f"缓存系统关闭失败: {e}")


def get_cache_stats() -> Dict[str, Any]:
    """获取缓存统计信息"""
    try:
        manager = _get_global_cache_manager()
        return manager.get_stats()
    except Exception as e:
        logger.error(f"获取缓存统计失败: {e}")
        return {}