"""
序列化器模块

提供不同的序列化策略，支持JSON、Pickle等格式。
"""
import json
import pickle
from abc import ABC, abstractmethod
from typing import Any, Optional
import pandas as pd
import numpy as np
from datetime import datetime, date


class Serializer(ABC):
    """序列化器抽象基类"""
    
    @abstractmethod
    def serialize(self, value: Any) -> bytes:
        """序列化对象为字节"""
        pass
    
    @abstractmethod
    def deserialize(self, data: bytes) -> Any:
        """反序列化字节为对象"""
        pass


class JsonSerializer(Serializer):
    """JSON序列化器，适用于简单数据类型"""
    
    def __init__(self, ensure_ascii: bool = False):
        self.ensure_ascii = ensure_ascii
    
    def serialize(self, value: Any) -> bytes:
        """序列化为JSON字节"""
        # 自定义编码器处理特殊类型
        def default(obj):
            if isinstance(obj, (datetime, date)):
                return obj.isoformat()
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, pd.DataFrame):
                return obj.to_dict('records')
            elif isinstance(obj, pd.Series):
                return obj.to_dict()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, set):
                return list(obj)
            raise TypeError(f"Object of type {type(obj).__name__} is not JSON serializable")
        
        return json.dumps(value, default=default, ensure_ascii=self.ensure_ascii).encode('utf-8')
    
    def deserialize(self, data: bytes) -> Any:
        """从JSON字节反序列化"""
        return json.loads(data.decode('utf-8'))


class PickleSerializer(Serializer):
    """Pickle序列化器，支持所有Python对象"""
    
    def __init__(self, protocol: int = pickle.HIGHEST_PROTOCOL):
        self.protocol = protocol
    
    def serialize(self, value: Any) -> bytes:
        """序列化为Pickle字节"""
        return pickle.dumps(value, protocol=self.protocol)
    
    def deserialize(self, data: bytes) -> Any:
        """从Pickle字节反序列化"""
        return pickle.loads(data)


class CompressedSerializer(Serializer):
    """压缩序列化器，对其他序列化器的输出进行压缩"""
    
    def __init__(self, base_serializer: Serializer, compression: str = 'gzip'):
        """
        Args:
            base_serializer: 基础序列化器
            compression: 压缩算法 ('gzip', 'bz2', 'lzma')
        """
        self.base_serializer = base_serializer
        self.compression = compression
        
        if compression == 'gzip':
            import gzip
            self.compress = gzip.compress
            self.decompress = gzip.decompress
        elif compression == 'bz2':
            import bz2
            self.compress = bz2.compress
            self.decompress = bz2.decompress
        elif compression == 'lzma':
            import lzma
            self.compress = lzma.compress
            self.decompress = lzma.decompress
        else:
            raise ValueError(f"Unsupported compression: {compression}")
    
    def serialize(self, value: Any) -> bytes:
        """序列化并压缩"""
        data = self.base_serializer.serialize(value)
        return self.compress(data)
    
    def deserialize(self, data: bytes) -> Any:
        """解压并反序列化"""
        decompressed = self.decompress(data)
        return self.base_serializer.deserialize(decompressed)


class AutoSerializer(Serializer):
    """自动选择合适的序列化器"""
    
    def __init__(self):
        self.json_serializer = JsonSerializer()
        self.pickle_serializer = PickleSerializer()
    
    def serialize(self, value: Any) -> bytes:
        """根据数据类型自动选择序列化方式"""
        # 尝试使用JSON序列化（更快、更小）
        try:
            # 简单类型直接用JSON
            if isinstance(value, (str, int, float, bool, list, dict, type(None))):
                return b'J' + self.json_serializer.serialize(value)
        except:
            pass
        
        # 复杂类型使用Pickle
        return b'P' + self.pickle_serializer.serialize(value)
    
    def deserialize(self, data: bytes) -> Any:
        """自动识别并反序列化"""
        if not data:
            return None
        
        marker = data[0:1]
        content = data[1:]
        
        if marker == b'J':
            return self.json_serializer.deserialize(content)
        elif marker == b'P':
            return self.pickle_serializer.deserialize(content)
        else:
            # 兼容旧数据
            try:
                return self.json_serializer.deserialize(data)
            except:
                return self.pickle_serializer.deserialize(data)


# 默认序列化器实例
json_serializer = JsonSerializer()
pickle_serializer = PickleSerializer()
auto_serializer = AutoSerializer()

# 为pandas DataFrame提供特殊的序列化器
class DataFrameSerializer(Serializer):
    """专门用于pandas DataFrame的序列化器"""
    
    def __init__(self, compression: Optional[str] = 'gzip'):
        self.compression = compression
    
    def serialize(self, value: pd.DataFrame) -> bytes:
        """序列化DataFrame"""
        # 使用parquet格式，高效且保留类型信息
        import io
        buffer = io.BytesIO()
        value.to_parquet(buffer, compression=self.compression, engine='pyarrow')
        return buffer.getvalue()
    
    def deserialize(self, data: bytes) -> pd.DataFrame:
        """反序列化DataFrame"""
        import io
        buffer = io.BytesIO(data)
        return pd.read_parquet(buffer, engine='pyarrow')