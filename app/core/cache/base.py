"""
缓存后端抽象基类

定义缓存后端必须实现的接口，支持同步和异步操作。
"""
from abc import ABC, abstractmethod
from typing import Any, Optional, List, Dict, Set, Union
from datetime import timedelta


class CacheBackend(ABC):
    """缓存后端抽象接口"""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            缓存的值，如果不存在或已过期则返回None
        """
        pass
    
    @abstractmethod
    async def set(self, key: str, value: Any, expire: Optional[Union[int, timedelta]] = None) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 要缓存的值
            expire: 过期时间（秒数或timedelta），None表示永不过期
            
        Returns:
            是否设置成功
        """
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """
        删除缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            是否删除成功
        """
        pass
    
    @abstractmethod
    async def exists(self, key: str) -> bool:
        """
        检查缓存键是否存在
        
        Args:
            key: 缓存键
            
        Returns:
            是否存在
        """
        pass
    
    @abstractmethod
    async def expire(self, key: str, seconds: Union[int, timedelta]) -> bool:
        """
        设置键的过期时间
        
        Args:
            key: 缓存键
            seconds: 过期时间（秒数或timedelta）
            
        Returns:
            是否设置成功
        """
        pass
    
    @abstractmethod
    async def ttl(self, key: str) -> int:
        """
        获取键的剩余生存时间
        
        Args:
            key: 缓存键
            
        Returns:
            剩余秒数，-1表示永不过期，-2表示键不存在
        """
        pass
    
    # 批量操作
    @abstractmethod
    async def mget(self, keys: List[str]) -> List[Optional[Any]]:
        """
        批量获取缓存值
        
        Args:
            keys: 缓存键列表
            
        Returns:
            对应的值列表，不存在的键返回None
        """
        pass
    
    @abstractmethod
    async def mset(self, mapping: Dict[str, Any], expire: Optional[Union[int, timedelta]] = None) -> bool:
        """
        批量设置缓存值
        
        Args:
            mapping: 键值对字典
            expire: 统一的过期时间
            
        Returns:
            是否全部设置成功
        """
        pass
    
    @abstractmethod
    async def mdelete(self, keys: List[str]) -> int:
        """
        批量删除缓存值
        
        Args:
            keys: 缓存键列表
            
        Returns:
            实际删除的键数量
        """
        pass
    
    # 集合操作
    @abstractmethod
    async def keys(self, pattern: str = "*") -> List[str]:
        """
        获取匹配模式的所有键
        
        Args:
            pattern: 匹配模式，支持通配符
            
        Returns:
            匹配的键列表
        """
        pass
    
    @abstractmethod
    async def clear(self) -> bool:
        """
        清空所有缓存
        
        Returns:
            是否清空成功
        """
        pass
    
    @abstractmethod
    async def size(self) -> int:
        """
        获取缓存中的键数量
        
        Returns:
            键的数量
        """
        pass
    
    # 原子操作
    @abstractmethod
    async def incr(self, key: str, amount: int = 1) -> int:
        """
        原子递增操作
        
        Args:
            key: 缓存键
            amount: 递增量
            
        Returns:
            递增后的值
        """
        pass
    
    @abstractmethod
    async def decr(self, key: str, amount: int = 1) -> int:
        """
        原子递减操作
        
        Args:
            key: 缓存键
            amount: 递减量
            
        Returns:
            递减后的值
        """
        pass
    
    # 高级功能
    async def get_or_set(self, key: str, default: Any, expire: Optional[Union[int, timedelta]] = None) -> Any:
        """
        获取缓存值，如果不存在则设置默认值
        
        Args:
            key: 缓存键
            default: 默认值或返回默认值的可调用对象
            expire: 过期时间
            
        Returns:
            缓存值或默认值
        """
        value = await self.get(key)
        if value is None:
            if callable(default):
                value = await default() if asyncio.iscoroutinefunction(default) else default()
            else:
                value = default
            await self.set(key, value, expire)
        return value
    
    async def add(self, key: str, value: Any, expire: Optional[Union[int, timedelta]] = None) -> bool:
        """
        仅在键不存在时设置值
        
        Args:
            key: 缓存键
            value: 要缓存的值
            expire: 过期时间
            
        Returns:
            是否设置成功
        """
        if not await self.exists(key):
            return await self.set(key, value, expire)
        return False
    
    async def replace(self, key: str, value: Any, expire: Optional[Union[int, timedelta]] = None) -> bool:
        """
        仅在键存在时替换值
        
        Args:
            key: 缓存键
            value: 新值
            expire: 过期时间
            
        Returns:
            是否替换成功
        """
        if await self.exists(key):
            return await self.set(key, value, expire)
        return False
    
    # 标签支持
    async def tag(self, key: str, tags: Union[str, List[str]]) -> bool:
        """
        为缓存键添加标签
        
        Args:
            key: 缓存键
            tags: 标签或标签列表
            
        Returns:
            是否添加成功
        """
        # 默认实现：将标签作为额外的键存储
        if isinstance(tags, str):
            tags = [tags]
        for tag in tags:
            tag_key = f"_tag:{tag}"
            tag_set = await self.get(tag_key) or set()
            tag_set.add(key)
            await self.set(tag_key, tag_set)
        return True
    
    async def invalidate_tag(self, tag: str) -> int:
        """
        使带有指定标签的所有缓存失效
        
        Args:
            tag: 标签名
            
        Returns:
            失效的缓存数量
        """
        tag_key = f"_tag:{tag}"
        tagged_keys = await self.get(tag_key) or set()
        if tagged_keys:
            count = await self.mdelete(list(tagged_keys))
            await self.delete(tag_key)
            return count
        return 0


import asyncio