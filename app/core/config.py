"""
应用配置模块

提供应用配置项，支持从环境变量和.env文件加载配置。
"""

from venv import logger
from pydantic import Field, field_validator, ConfigDict, ValidationInfo
from pydantic_settings import BaseSettings, SettingsConfigDict
from typing import Optional, Dict, Any, List
import os

class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用信息
    APP_NAME: str = Field("Quantization", description="应用名称")
    APP_VERSION: str = Field("1.0.0", description="应用版本")
    APP_ENV: str = Field("development", description="运行环境: development, test, production")
    DEBUG: bool = Field(False, description="是否启用调试模式")
    
    # 数据库配置
    DATABASE_URL: str = Field(
        "sqlite:///./quantization.db", 
        description="数据库连接串，例如: sqlite:///./app.db 或 mysql+pymysql://user:pass@localhost/dbname"
    )
    DATABASE_TYPE: str = Field("sqlite", description="数据库类型")
    SQL_ECHO: bool = Field(False, description="是否打印SQL语句")
    
    # API配置
    API_PREFIX: str = Field("/api", description="API路由前缀")
    API_V1_STR: str = Field("/v1", description="API v1路由前缀")
    API_HOST: str = Field("0.0.0.0", description="API主机地址")
    API_PORT: str = Field("8000", description="API端口")
    
    # 跨域配置
    BACKEND_CORS_ORIGINS: List[str] = Field(
        ["*"], description="允许跨域的源列表，例如 ['http://localhost', 'https://example.com']"
    )
    
    # 数据源配置
    DATA_API_TYPE: str = Field("mairui", description="数据源类型: tushare, akshare, mairui")
    MAIRUI_TOKEN: Optional[str] = Field("test-token", description="迈睿数据API授权令牌")
    TUSHARE_TOKEN: Optional[str] = Field("test-token", description="TuShare数据API授权令牌")
    
    # 调度任务配置
    SCHEDULER_ENABLED: bool = Field(True, description="是否启用任务调度")
    DATA_UPDATE_CRON: str = Field("0 18 * * 1-5", description="数据更新cron表达式")
    STOCK_LIST_UPDATE_CRON: str = Field("0 12 * * 0", description="股票列表更新cron表达式")
    
    # 缓存配置
    CACHE_ENABLED: bool = Field(True, description="是否启用缓存")
    CACHE_EXPIRATION: int = Field(3600, description="缓存过期时间(秒)")
    REDIS_URL: str = Field("redis://localhost:6379/0", description="Redis连接串,例如: redis://localhost:6379/0")
    
    # Redis队列配置
    REDIS_MAX_CONNECTIONS: int = Field(20, description="Redis连接池最大连接数")
    QUEUE_ENABLED: bool = Field(True, description="是否启用Redis队列功能")
    QUEUE_SAVE_RESULTS: bool = Field(False, description="是否保存任务执行结果到队列")
    QUEUE_CONSUMER_CONCURRENCY: int = Field(1, description="队列消费者并发数（SQLite优化：设为1避免写锁冲突）")
    QUEUE_CONSUMER_TIMEOUT: int = Field(60, description="队列消费者超时时间(秒) - 增加以避免任务丢失")
    QUEUE_FALLBACK_TO_DIRECT: bool = Field(True, description="队列异常时是否降级为直接数据库操作")
    QUEUE_MAX_LENGTH: int = Field(1000, description="队列最大长度，超出时丢弃旧数据")
    
    # 扫描器性能配置
    SCANNER_CONCURRENT_REQUESTS: int = Field(10, description="扫描器并发请求数（考虑API供应商限制）")
    SCANNER_BATCH_SIZE: int = Field(20, description="扫描器批量处理股票数量")
    SCANNER_REQUEST_DELAY: float = Field(0.4, description="扫描器API请求间隔（秒）")
    SCANNER_REALTIME_REQUEST_DELAY: float = Field(0.1, description="扫描器实时行情请求间隔（秒）")
    SCANNER_RETRY_TIMEOUT_DELAY: float = Field(1, description="扫描器超时重试延迟（秒）")
    SCANNER_RETRY_ERROR_DELAY: float = Field(1, description="扫描器错误重试延迟（秒）")
    
    # 缓存预热配置
    CACHE_WARMUP_ENABLED: bool = Field(True, description="是否启用缓存预热功能")
    CACHE_WARMUP_ON_STARTUP: bool = Field(True, description="程序启动时是否自动预热缓存")
    CACHE_WARMUP_CRON: str = Field("0 21 * * 1-5", description="定时预热的cron表达式（默认交易日下午9点）")
    CACHE_WARMUP_DAYS: int = Field(365, description="预热数据的天数范围")
    CACHE_WARMUP_PERIODS: List[str] = Field(["d"], description="需要预热的周期列表，如 ['d', 'w', 'm']")
    CACHE_WARMUP_BATCH_SIZE: int = Field(50, description="预热时的批量处理股票数量")
    CACHE_WARMUP_CONCURRENT: int = Field(3, description="预热时的并发请求数（避免对API造成压力）")
    
    # 数据库预热配置
    DB_WARMUP_ENABLED: bool = Field(True, description="是否启用数据库预热功能")
    DB_WARMUP_PRIORITY: bool = Field(True, description="是否优先使用数据库进行预热（比API快18.7倍）")
    DB_WARMUP_API_FALLBACK: bool = Field(True, description="数据库数据不足时是否回退到API获取")
    DB_WARMUP_SAVE_API_DATA: bool = Field(True, description="是否将API获取的数据保存到数据库")
    DB_WARMUP_MIN_RECORDS: int = Field(20, description="数据库预热的最小记录数阈值")
    
    # 扫描数据保存配置
    SCAN_SAVE_DATA_TO_DB: bool = Field(True, description="扫描时是否将获取的股票数据保存到数据库（默认开启）")
    
    # 日志配置
    LOG_LEVEL: str = Field("INFO", description="日志级别")
    LOG_DIR: str = Field("logs", description="日志文件目录")
    
    # JWT认证配置
    JWT_SECRET_KEY: str = Field("your-super-secret-key-change-in-production", description="JWT密钥，生产环境必须修改")
    JWT_ALGORITHM: str = Field("HS256", description="JWT加密算法")
    JWT_EXPIRE_HOURS: int = Field(24, description="JWT token过期时间（小时）")
    
    # 认证配置
    AUTH_REQUIRED: bool = Field(True, description="是否要求认证访问")
    ALLOW_MULTI_DEVICE_LOGIN: bool = Field(True, description="是否允许多设备登录")
    
    # 交易时间配置
    MARKET_OPEN_TIME: str = Field("09:30", description="市场开盘时间(HH:MM格式)")
    MARKET_CLOSE_TIME: str = Field("15:00", description="市场收盘时间(HH:MM格式)")
    TRADING_DAYS: List[int] = Field([0, 1, 2, 3, 4], description="交易日(周一到周五，0=周一, 6=周日)")
    MARKET_MORNING_START: str = Field("09:30", description="上午交易开始时间")
    MARKET_MORNING_END: str = Field("11:30", description="上午交易结束时间")
    MARKET_AFTERNOON_START: str = Field("13:00", description="下午交易开始时间")
    MARKET_AFTERNOON_END: str = Field("15:00", description="下午交易结束时间")
    
    @field_validator("DATABASE_URL", mode="before")
    def validate_database_url(cls, v: str, info: ValidationInfo) -> str:
        """验证并规范化数据库连接URL"""
        if v.startswith("sqlite"):
            # 确保SQLite数据库文件目录存在
            db_path = v.replace("sqlite:///", "")
            if db_path != ":memory:":
                os.makedirs(os.path.dirname(os.path.abspath(db_path)), exist_ok=True)
        return v
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=True,
        extra="allow"  # 允许额外字段
    )
    

class SettingsManager:
    """配置管理器,支持重新加载配置"""
    _instance: Optional[Settings] = None
    
    @classmethod
    def get_settings(cls) -> Settings:
        """获取配置实例,如果不存在则创建"""
        if cls._instance is None:
            cls._instance = Settings()
            logger.info(f"读取配置: {cls._instance.model_dump_json()}")
        return cls._instance
    
    @classmethod
    def reload(cls) -> Settings:
        """重新加载配置"""
        cls._instance = Settings()
        logger.info(f"重新加载配置: {cls._instance.model_dump_json()}")
        return cls._instance

# 创建全局设置实例
settings = SettingsManager.get_settings()
