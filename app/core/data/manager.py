"""
统一数据管理器

提供统一的数据访问接口，整合智能路由、缓存管理、插件系统和事件驱动机制。
"""

import asyncio
import uuid
from typing import Dict, List, Optional, Any, Callable, Union
from datetime import datetime, date
import logging
import redis.asyncio as redis
from contextlib import asynccontextmanager

from app.core.logging import getLogger
from app.core.config import settings
from .models import (
    DataType,
    DataRequest,
    DataResult,
    CacheStrategy,
    RoutingStrategy,
    DataManagerConfig,
    CacheConfig,
    RoutingConfig
)
from .router import SmartDataRouter
from .events import DataEventBus
from .plugins import PluginManager

logger = getLogger(__name__)


class UnifiedDataManager:
    """统一数据管理器
    
    核心功能：
    1. 统一数据访问接口
    2. 智能数据路由和缓存
    3. 插件化数据源管理
    4. 事件驱动架构
    5. 性能监控和统计
    """
    
    def __init__(self, config: Optional[DataManagerConfig] = None, storage_service: Optional[Any] = None):
        self.config = config or DataManagerConfig()
        self.storage_service = storage_service
        
        # 初始化Redis客户端
        self.redis_client = None
        if settings.REDIS_URL:
            self.redis_client = redis.from_url(settings.REDIS_URL)
        
        # 初始化核心组件
        self.router = SmartDataRouter(
            cache_config=self.config.cache,
            routing_config=self.config.routing,
            redis_client=self.redis_client,
            storage_service=storage_service
        )
        
        self.event_bus = DataEventBus()
        self.plugin_manager = PluginManager(self.config.plugins)
        
        # 管理器状态
        self._running = False
        self._request_queue = asyncio.Queue(maxsize=self.config.request_queue_size)
        self._worker_tasks: List[asyncio.Task] = []
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'start_time': datetime.now()
        }
        
        logger.info("统一数据管理器初始化完成")
    
    async def start(self) -> None:
        """启动数据管理器"""
        if self._running:
            logger.warning("数据管理器已在运行")
            return
        
        logger.info("启动统一数据管理器...")
        
        try:
            # 初始化插件系统
            await self.plugin_manager.initialize()
            
            # 注册默认数据源
            await self._register_default_sources()
            
            # 启动工作线程
            await self._start_workers()
            
            # 启动事件监听
            await self.event_bus.start()
            
            self._running = True
            logger.info("统一数据管理器启动成功")
            
            # 发送启动事件
            await self.event_bus.emit('manager.started', {
                'timestamp': datetime.now().isoformat(),
                'config': self.config.dict()
            })
            
        except Exception as e:
            logger.error(f"数据管理器启动失败: {e}")
            raise
    
    async def stop(self) -> None:
        """停止数据管理器"""
        if not self._running:
            return
        
        logger.info("停止统一数据管理器...")
        
        try:
            self._running = False
            
            # 停止工作线程
            await self._stop_workers()
            
            # 停止事件总线
            await self.event_bus.stop()
            
            # 关闭插件系统
            await self.plugin_manager.cleanup()
            
            # 关闭Redis连接
            if self.redis_client:
                await self.redis_client.close()
            
            logger.info("统一数据管理器已停止")
            
        except Exception as e:
            logger.error(f"数据管理器停止时出错: {e}")
    
    async def get_data(
        self,
        data_type: Union[DataType, str],
        params: Optional[Dict[str, Any]] = None,
        cache_strategy: CacheStrategy = CacheStrategy.CACHE_FIRST,
        routing_strategy: RoutingStrategy = RoutingStrategy.FASTEST,
        timeout: float = 30.0,
        force_refresh: bool = False,
        **kwargs
    ) -> DataResult:
        """获取数据的主要接口
        
        Args:
            data_type: 数据类型
            params: 请求参数
            cache_strategy: 缓存策略
            routing_strategy: 路由策略
            timeout: 超时时间
            force_refresh: 强制刷新缓存
            **kwargs: 其他参数
        
        Returns:
            DataResult: 数据结果
        """
        # 创建数据请求
        request = DataRequest(
            request_id=str(uuid.uuid4()),
            data_type=DataType(data_type) if isinstance(data_type, str) else data_type,
            params=params or {},
            cache_strategy=cache_strategy,
            routing_strategy=routing_strategy,
            timeout=timeout,
            force_refresh=force_refresh,
            **kwargs
        )
        
        return await self.process_request(request)
    
    async def process_request(self, request: DataRequest) -> DataResult:
        """处理数据请求"""
        start_time = datetime.now()
        
        logger.info(f"处理数据请求: {request.request_id}, 类型: {request.data_type}")
        
        try:
            # 发送请求开始事件
            await self.event_bus.emit('request.started', {
                'request_id': request.request_id,
                'data_type': request.data_type.value,
                'timestamp': start_time.isoformat()
            })
            
            # 应用请求中间件
            request = await self._apply_request_middleware(request)
            
            # 路由请求
            result = await self.router.route_request(request)
            
            # 应用响应中间件
            result = await self._apply_response_middleware(result, request)
            
            # 更新统计
            self.stats['total_requests'] += 1
            if result.is_success:
                self.stats['successful_requests'] += 1
            else:
                self.stats['failed_requests'] += 1
            
            # 发送请求完成事件
            await self.event_bus.emit('request.completed', {
                'request_id': request.request_id,
                'success': result.is_success,
                'response_time': result.response_time,
                'cache_hit': result.cache_hit,
                'data_source': result.data_source,
                'timestamp': datetime.now().isoformat()
            })
            
            return result
            
        except Exception as e:
            error_result = DataResult(
                request_id=request.request_id,
                success=False,
                error_code="PROCESS_ERROR",
                error_message=str(e),
                response_time=(datetime.now() - start_time).total_seconds()
            )
            
            self.stats['total_requests'] += 1
            self.stats['failed_requests'] += 1
            
            # 发送错误事件
            await self.event_bus.emit('request.error', {
                'request_id': request.request_id,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            
            logger.error(f"请求处理失败: {request.request_id}, 错误: {e}")
            return error_result
    
    async def batch_get_data(
        self,
        requests: List[Dict[str, Any]],
        max_concurrency: Optional[int] = None
    ) -> List[DataResult]:
        """批量获取数据"""
        if not requests:
            return []
        
        # 限制并发数
        concurrency = min(
            max_concurrency or self.config.max_concurrent_requests,
            len(requests)
        )
        
        # 创建信号量控制并发
        semaphore = asyncio.Semaphore(concurrency)
        
        async def process_single_request(req_dict: Dict[str, Any]) -> DataResult:
            async with semaphore:
                return await self.get_data(**req_dict)
        
        # 并发执行所有请求
        tasks = [process_single_request(req) for req in requests]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append(DataResult(
                    request_id=f"batch_{i}",
                    success=False,
                    error_code="BATCH_PROCESS_ERROR",
                    error_message=str(result)
                ))
            else:
                processed_results.append(result)
        
        return processed_results
    
    # 便民方法 - 针对不同数据类型的快捷接口
    async def get_stock_info(
        self,
        stock_code: Optional[str] = None,
        **kwargs
    ) -> DataResult:
        """获取股票基本信息"""
        params = {'stock_code': stock_code} if stock_code else {}
        return await self.get_data(
            data_type=DataType.STOCK_INFO,
            params=params,
            **kwargs
        )
    
    async def get_stock_daily(
        self,
        stock_code: str,
        start_date: Optional[Union[datetime, date, str]] = None,
        end_date: Optional[Union[datetime, date, str]] = None,
        **kwargs
    ) -> DataResult:
        """获取股票日线数据"""
        params = {
            'stock_code': stock_code,
            'start_date': start_date,
            'end_date': end_date
        }
        return await self.get_data(
            data_type=DataType.STOCK_DAILY,
            params=params,
            **kwargs
        )
    
    async def get_stock_weekly(
        self,
        stock_code: str,
        start_date: Optional[Union[datetime, date, str]] = None,
        end_date: Optional[Union[datetime, date, str]] = None,
        **kwargs
    ) -> DataResult:
        """获取股票周线数据"""
        params = {
            'stock_code': stock_code,
            'start_date': start_date,
            'end_date': end_date
        }
        return await self.get_data(
            data_type=DataType.STOCK_WEEKLY,
            params=params,
            **kwargs
        )
    
    async def get_stock_monthly(
        self,
        stock_code: str,
        start_date: Optional[Union[datetime, date, str]] = None,
        end_date: Optional[Union[datetime, date, str]] = None,
        **kwargs
    ) -> DataResult:
        """获取股票月线数据"""
        params = {
            'stock_code': stock_code,
            'start_date': start_date,
            'end_date': end_date
        }
        return await self.get_data(
            data_type=DataType.STOCK_MONTHLY,
            params=params,
            **kwargs
        )
    
    async def get_stock_realtime(
        self,
        stock_codes: List[str],
        **kwargs
    ) -> DataResult:
        """获取股票实时行情"""
        params = {'stock_codes': stock_codes}
        return await self.get_data(
            data_type=DataType.STOCK_REALTIME,
            params=params,
            **kwargs
        )
    
    async def get_stock_indicators(
        self,
        stock_code: str,
        indicator_types: List[str],
        start_date: Optional[Union[datetime, date, str]] = None,
        end_date: Optional[Union[datetime, date, str]] = None,
        **kwargs
    ) -> DataResult:
        """获取股票技术指标"""
        params = {
            'stock_code': stock_code,
            'indicator_types': indicator_types,
            'start_date': start_date,
            'end_date': end_date
        }
        return await self.get_data(
            data_type=DataType.STOCK_INDICATORS,
            params=params,
            **kwargs
        )
    
    def register_data_source(
        self,
        name: str,
        handler: Callable[[DataRequest], DataResult],
        priority: int = 0,
        health_check: Optional[Callable] = None,
        data_types: Optional[List[DataType]] = None
    ) -> None:
        """注册数据源"""
        self.router.register_source(name, handler, priority, health_check)
        logger.info(f"数据源已注册: {name}, 支持类型: {data_types or '全部'}")
    
    def register_middleware(
        self,
        middleware: Callable,
        middleware_type: str = "request"
    ) -> None:
        """注册中间件"""
        self.plugin_manager.register_middleware(middleware, middleware_type)
    
    async def invalidate_cache(
        self,
        cache_key: Optional[str] = None,
        data_type: Optional[DataType] = None,
        params: Optional[Dict[str, Any]] = None
    ) -> None:
        """使缓存失效"""
        if cache_key:
            await self.router.cache_manager.delete(cache_key)
        elif data_type and params:
            # 根据数据类型和参数生成缓存键
            dummy_request = DataRequest(
                request_id="cache_invalidation",
                data_type=data_type,
                params=params
            )
            cache_key = dummy_request.get_cache_key()
            await self.router.cache_manager.delete(cache_key)
        
        logger.info(f"缓存已失效: {cache_key or f'{data_type}:{params}'}")
    
    async def clear_l1_l2_cache(self) -> Dict[str, Any]:
        """清理L1和L2缓存"""
        try:
            result = {
                'l1_cleared': False,
                'l2_cleared': False,
                'l1_count': 0,
                'l2_count': 0,
                'timestamp': datetime.now().isoformat()
            }
            
            # 清理L1内存缓存
            if self.router.cache_manager.l1_cache:
                result['l1_count'] = len(self.router.cache_manager.l1_cache)
                self.router.cache_manager.l1_cache.clear()
                result['l1_cleared'] = True
                logger.info("L1内存缓存已清理")
            
            # 清理L2 Redis缓存
            if self.router.cache_manager.redis_client:
                prefix = self.router.cache_manager.config.l2_key_prefix
                keys = await self.router.cache_manager.redis_client.keys(f"{prefix}:*")
                if keys:
                    await self.router.cache_manager.redis_client.delete(*keys)
                    result['l2_count'] = len(keys)
                    result['l2_cleared'] = True
                    logger.info(f"L2缓存已清理，删除了{len(keys)}个键")
            
            # 发送清理完成事件
            await self.event_bus.emit('cache.cleared', result)
            
            return result
            
        except Exception as e:
            logger.error(f"缓存清理失败: {str(e)}")
            error_result = {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
            await self.event_bus.emit('cache.clear_failed', error_result)
            raise
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        router_stats = self.router.get_stats()
        plugin_stats = self.plugin_manager.get_stats()
        
        uptime = (datetime.now() - self.stats['start_time']).total_seconds()
        
        return {
            'manager_stats': {
                **self.stats,
                'uptime_seconds': uptime,
                'requests_per_second': self.stats['total_requests'] / max(uptime, 1),
                'success_rate': (
                    self.stats['successful_requests'] / max(self.stats['total_requests'], 1) * 100
                )
            },
            'router_stats': router_stats,
            'plugin_stats': plugin_stats,
            'queue_size': self._request_queue.qsize() if self._request_queue else 0
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        checks = {
            'manager_running': self._running,
            'redis_connected': False,
            'plugins_loaded': len(self.plugin_manager.loaded_plugins),
            'data_sources': len(self.router.source_registry.sources)
        }
        
        # 检查Redis连接
        if self.redis_client:
            try:
                await self.redis_client.ping()
                checks['redis_connected'] = True
            except Exception:
                checks['redis_connected'] = False
        
        # 检查数据源健康状态
        source_health = {}
        for source_name in self.router.source_registry.sources:
            try:
                is_healthy = await self.router.source_registry.health_check(source_name)
                source_health[source_name] = is_healthy
            except Exception as e:
                source_health[source_name] = False
                logger.error(f"数据源健康检查失败 {source_name}: {e}")
        
        checks['source_health'] = source_health
        
        return checks
    
    # 私有方法
    async def _register_default_sources(self) -> None:
        """注册默认数据源"""
        # 这里可以注册现有的数据提供者作为数据源
        from app.services.data_fetcher.factory import DataFetcherFactory
        
        try:
            # 获取配置的数据提供者
            provider_name = settings.DATA_API_TYPE
            config = {}
            
            if provider_name == "tushare" and settings.TUSHARE_TOKEN:
                config['token'] = settings.TUSHARE_TOKEN
            elif provider_name == "mairui" and settings.MAIRUI_TOKEN:
                config['licence'] = settings.MAIRUI_TOKEN
            
            # 创建数据提供者
            fetcher = DataFetcherFactory.create_fetcher(provider_name, **config)
            
            # 创建适配器函数
            async def data_source_handler(request: DataRequest) -> DataResult:
                try:
                    data_type = request.data_type
                    params = request.params
                    
                    if data_type == DataType.STOCK_INFO:
                        if 'stock_code' in params:
                            # 获取单个股票信息 - 这里需要适配现有接口
                            data = await fetcher.get_stock_list()
                            # 过滤指定股票
                            filtered_data = [
                                item for item in data 
                                if item.get('code') == params['stock_code']
                            ]
                        else:
                            data = await fetcher.get_stock_list()
                            filtered_data = data
                        
                        return DataResult(
                            request_id=request.request_id,
                            success=True,
                            data=filtered_data,
                            count=len(filtered_data)
                        )
                    
                    elif data_type == DataType.STOCK_DAILY:
                        stock_code = params.get('stock_code')
                        start_date = params.get('start_date')
                        end_date = params.get('end_date')
                        
                        data = await fetcher.get_daily_data(
                            stock_code, start_date, end_date
                        )
                        
                        return DataResult(
                            request_id=request.request_id,
                            success=True,
                            data=data,
                            count=len(data)
                        )
                    
                    elif data_type == DataType.STOCK_REALTIME:
                        stock_codes = params.get('stock_codes', [])
                        data = await fetcher.get_realtime_quotes(stock_codes)
                        
                        return DataResult(
                            request_id=request.request_id,
                            success=True,
                            data=data,
                            count=len(data)
                        )
                    
                    else:
                        return DataResult(
                            request_id=request.request_id,
                            success=False,
                            error_code="UNSUPPORTED_DATA_TYPE",
                            error_message=f"不支持的数据类型: {data_type}"
                        )
                        
                except Exception as e:
                    return DataResult(
                        request_id=request.request_id,
                        success=False,
                        error_code="DATA_SOURCE_ERROR",
                        error_message=str(e)
                    )
            
            # 注册数据源
            self.register_data_source(
                name=f"default_{provider_name}",
                handler=data_source_handler,
                priority=100  # 高优先级
            )
            
            logger.info(f"默认数据源已注册: {provider_name}")
            
        except Exception as e:
            logger.error(f"注册默认数据源失败: {e}")
    
    async def _apply_request_middleware(self, request: DataRequest) -> DataRequest:
        """应用请求中间件"""
        return await self.plugin_manager.apply_request_middleware(request)
    
    async def _apply_response_middleware(
        self, 
        result: DataResult, 
        request: DataRequest
    ) -> DataResult:
        """应用响应中间件"""
        return await self.plugin_manager.apply_response_middleware(result, request)
    
    async def _start_workers(self) -> None:
        """启动工作线程"""
        # 这里可以启动后台工作任务，如缓存预热、健康检查等
        pass
    
    async def _stop_workers(self) -> None:
        """停止工作线程"""
        for task in self._worker_tasks:
            task.cancel()
        
        if self._worker_tasks:
            await asyncio.gather(*self._worker_tasks, return_exceptions=True)
        
        self._worker_tasks.clear()
    
    @asynccontextmanager
    async def session(self):
        """上下文管理器"""
        await self.start()
        try:
            yield self
        finally:
            await self.stop()


# 全局单例实例
_manager_instance: Optional[UnifiedDataManager] = None


def get_data_manager(storage_service: Optional[Any] = None) -> UnifiedDataManager:
    """获取数据管理器单例实例"""
    global _manager_instance
    if _manager_instance is None:
        _manager_instance = UnifiedDataManager(storage_service=storage_service)
    return _manager_instance


async def init_data_manager(
    config: Optional[DataManagerConfig] = None, 
    storage_service: Optional[Any] = None
) -> UnifiedDataManager:
    """初始化数据管理器"""
    global _manager_instance
    if _manager_instance is None:
        _manager_instance = UnifiedDataManager(config, storage_service)
        await _manager_instance.start()
    return _manager_instance


async def cleanup_data_manager() -> None:
    """清理数据管理器"""
    global _manager_instance
    if _manager_instance:
        await _manager_instance.stop()
        _manager_instance = None