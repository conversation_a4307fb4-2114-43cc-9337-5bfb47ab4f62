"""
统一数据管理系统的核心数据模型

定义了数据请求、响应、路由策略等核心数据结构。
"""

from enum import Enum, auto
from typing import Any, Dict, List, Optional, Union, Type, Callable
from dataclasses import dataclass, field
from datetime import datetime, date
from pydantic import BaseModel, Field, validator


class DataType(str, Enum):
    """数据类型枚举"""
    STOCK_INFO = "stock_info"
    STOCK_DAILY = "stock_daily"
    STOCK_WEEKLY = "stock_weekly"      # 新增：股票周线数据
    STOCK_MONTHLY = "stock_monthly"    # 新增：股票月线数据
    STOCK_REALTIME = "stock_realtime"
    STOCK_INDICATORS = "stock_indicators"
    INDEX_COMPONENTS = "index_components"
    FINANCIAL_DATA = "financial_data"
    CUSTOM = "custom"


class CacheLevel(str, Enum):
    """缓存级别枚举"""
    L1_MEMORY = "l1_memory"       # L1内存缓存
    L2_REDIS = "l2_redis"         # L2 Redis缓存
    L3_DATABASE = "l3_database"   # L3数据库
    L4_API = "l4_api"             # L4外部API


class CacheStrategy(str, Enum):
    """缓存策略枚举"""
    CACHE_FIRST = "cache_first"           # 优先从缓存获取
    API_FIRST = "api_first"               # 优先从API获取
    CACHE_THROUGH = "cache_through"       # 缓存穿透
    CACHE_ASIDE = "cache_aside"           # 缓存旁路
    WRITE_BEHIND = "write_behind"         # 写后模式
    WRITE_THROUGH = "write_through"       # 写穿模式


class RoutingStrategy(str, Enum):
    """路由策略枚举"""
    FASTEST = "fastest"           # 最快响应
    MOST_RELIABLE = "reliable"    # 最可靠
    COST_EFFECTIVE = "cost"       # 成本最优
    LOAD_BALANCED = "balanced"    # 负载均衡


class DataRequest(BaseModel):
    """统一数据请求模型"""
    
    # 基本信息
    request_id: str = Field(..., description="请求唯一标识")
    data_type: DataType = Field(..., description="数据类型")
    
    # 数据参数
    params: Dict[str, Any] = Field(default_factory=dict, description="请求参数")
    
    # 缓存策略
    cache_strategy: CacheStrategy = Field(
        default=CacheStrategy.CACHE_FIRST, 
        description="缓存策略"
    )
    cache_ttl: int = Field(default=3600, description="缓存存活时间(秒)")
    force_refresh: bool = Field(default=False, description="强制刷新缓存")
    
    # 路由策略
    routing_strategy: RoutingStrategy = Field(
        default=RoutingStrategy.FASTEST,
        description="路由策略"
    )
    
    # 时间范围
    start_date: Optional[Union[datetime, date, str]] = Field(
        default=None, 
        description="开始日期"
    )
    end_date: Optional[Union[datetime, date, str]] = Field(
        default=None, 
        description="结束日期"
    )
    
    # 性能要求
    timeout: float = Field(default=30.0, description="超时时间(秒)")
    max_retries: int = Field(default=3, description="最大重试次数")
    
    # 元数据
    created_at: datetime = Field(default_factory=datetime.now)
    priority: int = Field(default=0, description="请求优先级，数值越大优先级越高")
    tags: List[str] = Field(default_factory=list, description="请求标签")
    
    @validator('start_date', 'end_date', pre=True)
    def parse_dates(cls, v):
        """解析日期字符串"""
        if isinstance(v, str):
            try:
                return datetime.fromisoformat(v.replace('Z', '+00:00'))
            except ValueError:
                try:
                    return datetime.strptime(v, '%Y-%m-%d')
                except ValueError:
                    return v
        return v
    
    def get_cache_key(self) -> str:
        """生成缓存键"""
        key_parts = [
            str(self.data_type.value),
            str(sorted(self.params.items())),
            str(self.start_date) if self.start_date else "",
            str(self.end_date) if self.end_date else ""
        ]
        return f"unified_data:{':'.join(key_parts)}"


class DataResult(BaseModel):
    """统一数据响应模型"""
    
    # 基本信息
    request_id: str = Field(..., description="请求ID")
    success: bool = Field(..., description="请求是否成功")
    
    # 数据内容
    data: Optional[Any] = Field(default=None, description="返回数据")
    count: int = Field(default=0, description="数据条数")
    
    # 缓存信息
    cache_hit: bool = Field(default=False, description="是否命中缓存")
    cache_level: Optional[CacheLevel] = Field(default=None, description="命中的缓存级别")
    
    # 性能信息
    response_time: float = Field(default=0.0, description="响应时间(秒)")
    data_source: str = Field(default="", description="数据来源")
    
    # 错误信息
    error_code: Optional[str] = Field(default=None, description="错误代码")
    error_message: Optional[str] = Field(default=None, description="错误消息")
    
    # 元数据
    timestamp: datetime = Field(default_factory=datetime.now)
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    
    @property
    def is_success(self) -> bool:
        """检查结果是否成功"""
        return self.success and self.error_code is None
    
    @property
    def has_data(self) -> bool:
        """检查是否有数据"""
        return self.data is not None and self.count > 0


@dataclass
class CacheConfig:
    """缓存配置"""
    enabled: bool = True
    ttl: int = 3600
    max_size: int = 1000
    strategy: CacheStrategy = CacheStrategy.CACHE_FIRST
    
    # L1内存缓存配置
    l1_enabled: bool = True
    l1_max_size: int = 500
    l1_ttl: int = 300
    
    # L2 Redis缓存配置
    l2_enabled: bool = True
    l2_ttl: int = 3600
    l2_key_prefix: str = "unified_data"
    
    # 数据库缓存配置
    l3_enabled: bool = True
    l3_ttl: int = 86400  # 24小时


@dataclass
class RoutingConfig:
    """路由配置"""
    strategy: RoutingStrategy = RoutingStrategy.FASTEST
    timeout: float = 30.0
    max_retries: int = 3
    retry_delay: float = 1.0
    
    # 数据源优先级
    source_priorities: Dict[str, int] = field(default_factory=dict)
    
    # 负载均衡配置
    load_balance_enabled: bool = False
    health_check_enabled: bool = True
    health_check_interval: int = 60


@dataclass
class PluginConfig:
    """插件配置"""
    enabled: bool = True
    plugin_dirs: List[str] = field(default_factory=list)
    auto_discovery: bool = True
    
    # 插件优先级
    plugin_priorities: Dict[str, int] = field(default_factory=dict)
    
    # 中间件配置
    middleware_enabled: bool = True
    middleware_chain: List[str] = field(default_factory=list)


class DataManagerConfig(BaseModel):
    """数据管理器配置"""
    
    # 基本配置
    enabled: bool = Field(default=True, description="是否启用统一数据管理")
    debug: bool = Field(default=False, description="调试模式")
    
    # 缓存配置
    cache: CacheConfig = Field(default_factory=CacheConfig)
    
    # 路由配置
    routing: RoutingConfig = Field(default_factory=RoutingConfig)
    
    # 插件配置
    plugins: PluginConfig = Field(default_factory=PluginConfig)
    
    # 性能配置
    max_concurrent_requests: int = Field(default=100, description="最大并发请求数")
    request_queue_size: int = Field(default=1000, description="请求队列大小")
    
    # 监控配置
    metrics_enabled: bool = Field(default=True, description="启用指标收集")
    logging_enabled: bool = Field(default=True, description="启用日志记录")


# 类型别名
DataHandler = Callable[[DataRequest], DataResult]
CacheKeyGenerator = Callable[[DataRequest], str]
DataValidator = Callable[[Any], bool]