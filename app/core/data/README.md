# 统一数据管理系统 (Unified Data Management)

## 概述

统一数据管理系统是一个高性能、插件化的数据访问层，为量化交易系统提供统一的数据获取接口。系统采用多级缓存架构、智能路由和事件驱动设计，支持多数据源整合和插件扩展。

## 核心特性

- 🚀 **统一接口**: 为所有数据类型提供一致的访问接口
- 💾 **多级缓存**: L1内存 → L2Redis → L3数据库 → L4API 的智能缓存策略
- 🎯 **智能路由**: 支持最快响应、最可靠、成本最优等多种路由策略
- 🔌 **插件化架构**: 可扩展的数据源和处理器插件系统
- 📡 **事件驱动**: 完整的事件总线支持实时监控和响应
- 📊 **性能监控**: 内置统计和健康检查功能
- 🔒 **容错机制**: 超时控制、重试策略和降级处理

## 架构组件

### 核心模块

```
app/core/data/
├── __init__.py         # 模块导出
├── models.py          # 数据模型定义
├── manager.py         # 统一数据管理器
├── router.py          # 智能数据路由器
├── events.py          # 事件总线系统
├── plugins.py         # 插件管理器
├── compatibility.py   # 兼容性适配层
├── middleware/        # 中间件目录
├── processors/        # 数据处理器目录
└── sources/          # 数据源目录
```

### 数据模型 (`models.py`)

#### 数据类型枚举
- `STOCK_INFO`: 股票基本信息
- `STOCK_DAILY`: 股票日线数据
- `STOCK_REALTIME`: 实时行情数据
- `STOCK_INDICATORS`: 技术指标数据
- `INDEX_COMPONENTS`: 指数成分股
- `FINANCIAL_DATA`: 财务数据

#### 缓存策略
- `CACHE_FIRST`: 优先从缓存获取
- `API_FIRST`: 优先从API获取
- `CACHE_THROUGH`: 缓存穿透
- `CACHE_ASIDE`: 缓存旁路

#### 路由策略
- `FASTEST`: 最快响应
- `MOST_RELIABLE`: 最可靠
- `COST_EFFECTIVE`: 成本最优
- `LOAD_BALANCED`: 负载均衡

### 统一数据管理器 (`manager.py`)

主要功能：
- 统一数据访问接口
- 智能数据路由和缓存
- 插件化数据源管理
- 事件驱动架构
- 性能监控和统计

### 智能数据路由器 (`router.py`)

负责：
- 多级缓存管理 (L1-L4)
- 数据源健康检查
- 负载均衡和故障转移
- 路由决策和优化

## 快速开始

### 基本使用

```python
from app.core.data import UnifiedDataManager, DataType

# 创建数据管理器实例
manager = UnifiedDataManager()

# 启动管理器
await manager.start()

# 获取股票基本信息
result = await manager.get_stock_info(stock_code="000001")

# 获取股票日线数据
result = await manager.get_stock_daily(
    stock_code="000001",
    start_date="2024-01-01",
    end_date="2024-12-31"
)

# 获取实时行情
result = await manager.get_stock_realtime(
    stock_codes=["000001", "000002"]
)

# 关闭管理器
await manager.stop()
```

### 上下文管理器方式

```python
async with UnifiedDataManager().session() as manager:
    result = await manager.get_stock_info()
    print(f"获取到 {result.count} 条股票信息")
```

### 批量请求

```python
requests = [
    {"data_type": "stock_info", "params": {"stock_code": "000001"}},
    {"data_type": "stock_daily", "params": {"stock_code": "000002", "start_date": "2024-01-01"}},
]

results = await manager.batch_get_data(requests, max_concurrency=10)
```

### 自定义缓存和路由策略

```python
from app.core.data import CacheStrategy, RoutingStrategy

result = await manager.get_data(
    data_type=DataType.STOCK_DAILY,
    params={"stock_code": "000001"},
    cache_strategy=CacheStrategy.API_FIRST,
    routing_strategy=RoutingStrategy.MOST_RELIABLE,
    timeout=60.0,
    force_refresh=True
)
```

## 高级功能

### 数据源注册

```python
async def custom_data_source(request: DataRequest) -> DataResult:
    # 自定义数据获取逻辑
    return DataResult(
        request_id=request.request_id,
        success=True,
        data=custom_data,
        count=len(custom_data)
    )

# 注册自定义数据源
manager.register_data_source(
    name="custom_source",
    handler=custom_data_source,
    priority=50
)
```

### 中间件注册

```python
async def logging_middleware(request: DataRequest) -> DataRequest:
    logger.info(f"处理请求: {request.data_type}")
    return request

manager.register_middleware(logging_middleware, "request")
```

### 缓存管理

```python
# 使缓存失效
await manager.invalidate_cache(
    data_type=DataType.STOCK_INFO,
    params={"stock_code": "000001"}
)

# 使用特定缓存键
await manager.invalidate_cache(cache_key="specific_cache_key")
```

### 监控和统计

```python
# 获取系统统计信息
stats = manager.get_stats()
print(f"成功率: {stats['manager_stats']['success_rate']:.2f}%")
print(f"平均QPS: {stats['manager_stats']['requests_per_second']:.2f}")

# 健康检查
health = await manager.health_check()
print(f"Redis连接状态: {health['redis_connected']}")
print(f"数据源数量: {health['data_sources']}")
```

## 配置选项

### 数据管理器配置

```python
from app.core.data.models import DataManagerConfig, CacheConfig, RoutingConfig

config = DataManagerConfig(
    # 基本配置
    enabled=True,
    debug=False,
    
    # 缓存配置
    cache=CacheConfig(
        enabled=True,
        ttl=3600,
        max_size=1000,
        l1_enabled=True,
        l1_max_size=500,
        l1_ttl=300,
        l2_enabled=True,
        l2_ttl=3600
    ),
    
    # 路由配置
    routing=RoutingConfig(
        strategy=RoutingStrategy.FASTEST,
        timeout=30.0,
        max_retries=3,
        retry_delay=1.0
    ),
    
    # 性能配置
    max_concurrent_requests=100,
    request_queue_size=1000
)

manager = UnifiedDataManager(config)
```

## 环境变量配置

```env
# Redis配置
REDIS_URL=redis://localhost:6379/0

# 数据源配置
DATA_API_TYPE=tushare
TUSHARE_TOKEN=your_token_here
MAIRUI_TOKEN=your_token_here

# 日志配置
LOG_LEVEL=INFO
```

## 性能优化建议

### 缓存策略选择
- **高频查询数据**: 使用 `CACHE_FIRST` 策略
- **实时数据**: 使用 `API_FIRST` 策略
- **批量操作**: 考虑使用 `CACHE_THROUGH` 策略

### 并发控制
- 根据系统资源调整 `max_concurrent_requests`
- 使用批量请求接口减少网络开销
- 合理设置超时时间避免长时间等待

### 缓存优化
- L1缓存适合小量热点数据
- L2缓存适合中等规模数据
- 合理设置TTL避免数据过期

## 故障排除

### 常见问题

1. **Redis连接失败**
   - 检查 `REDIS_URL` 配置
   - 确认Redis服务运行状态

2. **数据源超时**
   - 增加 `timeout` 设置
   - 检查网络连接状况

3. **内存占用过高**
   - 减少L1缓存大小
   - 缩短缓存TTL时间

### 日志监控

系统提供详细的日志记录，可通过以下方式监控：

```python
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("app.core.data")
```

## 扩展开发

### 自定义数据源插件

创建继承 `DataSourcePlugin` 的自定义插件：

```python
from app.core.data.plugins import DataSourcePlugin

class CustomDataSourcePlugin(DataSourcePlugin):
    def __init__(self):
        super().__init__("custom_source", version="1.0.0")
    
    async def fetch_data(self, request: DataRequest) -> DataResult:
        # 实现自定义数据获取逻辑
        pass
```

### 自定义中间件

```python
async def custom_middleware(request: DataRequest) -> DataRequest:
    # 请求预处理逻辑
    return request

async def custom_response_middleware(result: DataResult, request: DataRequest) -> DataResult:
    # 响应后处理逻辑
    return result
```

## 版本历史

- **v1.0.0**: 初始版本，支持基本的统一数据访问
- **v1.1.0**: 添加多级缓存和智能路由
- **v1.2.0**: 完善插件系统和事件总线
- **v1.3.0**: 增强性能监控和健康检查

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 提交 Pull Request

## 许可证

本项目采用 MIT 许可证。