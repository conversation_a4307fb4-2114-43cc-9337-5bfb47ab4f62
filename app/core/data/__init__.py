"""
统一数据管理系统核心模块

提供统一的数据访问接口，智能数据路由，插件化架构和事件驱动机制。
"""

from .models import (
    DataType,
    DataRequest,
    DataResult,
    CacheLevel,
    CacheStrategy,
    RoutingStrategy
)
from .manager import UnifiedDataManager
from .router import SmartDataRouter
from .events import DataEventBus
from .plugins import (
    DataSourcePlugin,
    DataProcessorPlugin,
    PluginManager
)

__all__ = [
    # 核心模型
    "DataType",
    "DataRequest", 
    "DataResult",
    "CacheLevel",
    "CacheStrategy",
    "RoutingStrategy",
    
    # 核心组件
    "UnifiedDataManager",
    "SmartDataRouter",
    "DataEventBus",
    
    # 插件系统
    "DataSourcePlugin",
    "DataProcessorPlugin", 
    "PluginManager"
]