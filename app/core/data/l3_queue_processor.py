"""
L3缓存队列处理器

处理异步写入L3数据库缓存的队列消息。
"""

import asyncio
import json
import time
from typing import Optional, Dict, Any
from datetime import datetime, timedelta

from app.core.logging import getLogger
from app.core.data.models import DataType, DataRequest
from app.core.data.processors.factory import DataProcessorFactory

logger = getLogger(__name__)


class L3CacheQueueProcessor:
    """L3缓存队列处理器"""
    
    def __init__(self, redis_client, storage_service, queue_key: str = "unified_data:l3_queue"):
        self.redis_client = redis_client
        self.storage_service = storage_service
        self.queue_key = queue_key
        self.running = False
        self.stats = {
            'processed': 0,
            'success': 0,
            'failed': 0,
            'retries': 0
        }
        
    async def start_processing(self, batch_size: int = 10, process_interval: float = 5.0):
        """开始处理L3缓存队列"""
        self.running = True
        logger.info("L3缓存队列处理器启动")
        
        while self.running:
            try:
                # 批量从队列中获取消息
                messages = await self._fetch_batch_messages(batch_size)
                
                if messages:
                    await self._process_batch(messages)
                else:
                    # 没有消息时短暂休眠
                    await asyncio.sleep(process_interval)
                    
            except Exception as e:
                logger.error(f"L3缓存队列处理错误: {e}")
                await asyncio.sleep(1)  # 错误后短暂休眠
    
    async def stop_processing(self):
        """停止处理L3缓存队列"""
        self.running = False
        logger.info("L3缓存队列处理器停止")
    
    async def _fetch_batch_messages(self, batch_size: int) -> list:
        """批量获取队列消息"""
        messages = []
        
        try:
            for _ in range(batch_size):
                # 使用BRPOP阻塞式获取，超时时间1秒
                result = await self.redis_client.brpop(self.queue_key, timeout=1)
                if result:
                    queue_name, message_data = result
                    message = json.loads(message_data)
                    messages.append(message)
                else:
                    break  # 没有更多消息
                    
        except Exception as e:
            logger.error(f"获取队列消息失败: {e}")
            
        return messages
    
    async def _process_batch(self, messages: list):
        """批量处理队列消息"""
        logger.debug(f"开始处理 {len(messages)} 条L3缓存消息")
        
        for message in messages:
            try:
                await self._process_single_message(message)
                self.stats['processed'] += 1
                self.stats['success'] += 1
                
            except Exception as e:
                logger.error(f"处理L3缓存消息失败: {e}, 消息: {message}")
                self.stats['processed'] += 1
                self.stats['failed'] += 1
                
                # 重试逻辑
                await self._handle_retry(message, e)
    
    async def _process_single_message(self, message: Dict[str, Any]):
        """处理单条队列消息"""
        cache_key = message['key']
        cache_value = message['value']
        ttl = message.get('ttl', 86400)  # 默认24小时
        
        # 解析cache_key来确定数据类型和参数
        if not await self._parse_and_save_cache(cache_key, cache_value, ttl):
            raise ValueError(f"无法处理缓存键: {cache_key}")
    
    async def _parse_and_save_cache(self, cache_key: str, cache_value: Any, ttl: int) -> bool:
        """解析缓存键并保存到L3"""
        try:
            # cache_key格式: smart_cache:data_type:func_name:base_key 或 unified_data:data_type:params:start_date:end_date
            parts = cache_key.split(":")
            
            # 处理新格式的smart_cache键
            if len(parts) >= 3 and parts[0] == "smart_cache":
                data_type_str = parts[1]
            # 处理旧格式的unified_data键
            elif len(parts) >= 2 and parts[0] == "unified_data":
                data_type_str = parts[1]
            else:
                return False
            
            # 仅处理支持的数据类型（使用实际的枚举值）
            supported_data_types = [
                DataType.STOCK_DAILY.value,    # "stock_daily"
                DataType.STOCK_WEEKLY.value,   # "stock_weekly" 
                DataType.STOCK_MONTHLY.value   # "stock_monthly"
            ]
            
            if data_type_str not in supported_data_types:
                logger.debug(f"跳过不支持的数据类型: {data_type_str}")
                return True  # 不是错误，只是不需要L3缓存
            
            # 映射到枚举
            data_type_map = {
                DataType.STOCK_DAILY.value: DataType.STOCK_DAILY,
                DataType.STOCK_WEEKLY.value: DataType.STOCK_WEEKLY,
                DataType.STOCK_MONTHLY.value: DataType.STOCK_MONTHLY
            }
            
            data_type = data_type_map[data_type_str]
            
            # 获取处理器
            processor = DataProcessorFactory.get_processor(data_type)
            processor.storage_service = self.storage_service
            
            # 从cache_value中提取数据
            if isinstance(cache_value, dict) and 'data' in cache_value:
                actual_data = cache_value['data']
            else:
                actual_data = cache_value
            
            # 构建DataRequest（用于保存）
            # 这里需要从cache_key中解析更多参数，简化处理
            temp_request = DataRequest(
                request_id=f"l3_cache_save_{int(time.time())}",
                data_type=data_type,
                params={"stock_code": "placeholder"}  # 实际应该从key解析
            )
            
            # 保存到持久化层
            success = await processor.save_to_persistence(temp_request, actual_data)
            
            if success:
                logger.debug(f"L3缓存保存成功: {cache_key}")
            else:
                logger.warning(f"L3缓存保存失败: {cache_key}")
                
            return success
            
        except Exception as e:
            logger.error(f"L3缓存保存异常: {e}")
            return False
    
    async def _handle_retry(self, message: Dict[str, Any], error: Exception):
        """处理重试逻辑"""
        retry_count = message.get('retry_count', 0)
        max_retries = 3
        
        if retry_count < max_retries:
            # 增加重试计数
            message['retry_count'] = retry_count + 1
            message['last_error'] = str(error)
            message['retry_at'] = datetime.now().isoformat()
            
            # 延迟重试 - 重新放入队列
            try:
                await asyncio.sleep(retry_count + 1)  # 递增延迟
                await self.redis_client.lpush(
                    self.queue_key,
                    json.dumps(message, default=str)
                )
                self.stats['retries'] += 1
                logger.info(f"L3缓存消息重试 {retry_count + 1}/{max_retries}: {message['key']}")
                
            except Exception as e:
                logger.error(f"重试消息入队失败: {e}")
        else:
            logger.error(f"L3缓存消息达到最大重试次数，放弃处理: {message['key']}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return {
            'queue_processor': {
                'processed': self.stats['processed'],
                'success': self.stats['success'], 
                'failed': self.stats['failed'],
                'retries': self.stats['retries'],
                'success_rate': f"{(self.stats['success'] / max(1, self.stats['processed']) * 100):.2f}%"
            }
        }
