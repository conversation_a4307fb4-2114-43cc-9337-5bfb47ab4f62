"""
智能数据路由器

实现多级缓存路由决策，支持L1内存→L2Redis→数据库→API的智能路由。
"""

import asyncio
import time
from typing import Dict, List, Optional, Any, Callable, Tuple
from collections import defaultdict
from datetime import datetime, timedelta
import logging
import json
import redis.asyncio as redis
from cachetools import LRUCache, TTLCache

from app.core.logging import getLogger
from .models import (
    DataRequest, 
    DataResult, 
    CacheLevel, 
    CacheStrategy,
    RoutingStrategy,
    RoutingConfig,
    CacheConfig,
    DataType
)
from .processors.factory import DataProcessorFactory

logger = getLogger(__name__)


class CacheManager:
    """多级缓存管理器"""
    
    def __init__(self, config: CacheConfig, redis_client: Optional[redis.Redis] = None, storage_service=None):
        self.config = config
        self.redis_client = redis_client
        self.storage_service = storage_service  # 存储服务用于L3缓存
        
        # L1内存缓存 - 使用TTL缓存
        self.l1_cache = TTLCache(
            maxsize=config.l1_max_size,
            ttl=config.l1_ttl
        ) if config.l1_enabled else None
        
        # L3缓存队列（用于异步写入）
        self.l3_queue_key = f"{config.l2_key_prefix}:l3_queue"
        
        # 缓存统计
        self.stats = defaultdict(int)
        
    async def get(self, key: str, cache_level: Optional[CacheLevel] = None) -> Optional[Any]:
        """从缓存获取数据"""
        start_time = time.time()
        
        try:
            # L1内存缓存
            if cache_level in (None, CacheLevel.L1_MEMORY) and self.l1_cache is not None:
                if key in self.l1_cache:
                    self.stats['l1_hits'] += 1
                    logger.debug(f"L1缓存命中: {key}")
                    return self.l1_cache[key]
                self.stats['l1_misses'] += 1
                
                # 如果指定了L1缓存级别但未命中，直接返回
                if cache_level == CacheLevel.L1_MEMORY:
                    return None
            
            # L2 Redis缓存
            if cache_level in (None, CacheLevel.L2_REDIS) and self.redis_client:
                try:
                    redis_key = f"{self.config.l2_key_prefix}:{key}"
                    cached_data = await self.redis_client.get(redis_key)
                    if cached_data:
                        self.stats['l2_hits'] += 1
                        logger.debug(f"L2缓存命中: {key}")
                        
                        # 反序列化数据  
                        data = json.loads(cached_data)
                        
                        # 处理特殊数据类型的反序列化
                        data = self._deserialize_special_types(data)
                        
                        # 回填到L1缓存
                        if self.l1_cache is not None:
                            self.l1_cache[key] = data
                            
                        return data
                    self.stats['l2_misses'] += 1
                    
                except Exception as e:
                    logger.error(f"Redis缓存访问失败: {e}")
                    self.stats['l2_errors'] += 1
                    
                # 如果指定了L2缓存级别但未命中，直接返回
                if cache_level == CacheLevel.L2_REDIS:
                    return None
            
            # L3数据库缓存（通过DataProcessor）
            # if cache_level in (None, CacheLevel.L3_DATABASE):
            #     # 动态获取存储服务（如果没有提供）
            #     storage_service = self.storage_service
            #     if not storage_service:
            #         storage_service = await self._get_storage_service()
                
            #     if storage_service:
            #         try:
            #             # 从key中解析数据类型和参数来构建DataRequest
            #             cached_data = await self._get_from_l3_cache(key)
            #             if cached_data:
            #                 self.stats['l3_hits'] += 1
            #                 logger.debug(f"L3缓存命中: {key}")
                            
            #                 # 回填到L2和L1缓存
            #                 if self.redis_client:
            #                     try:
            #                         redis_key = f"{self.config.l2_key_prefix}:{key}"
            #                         serialized_data = self._serialize_special_types(cached_data)
            #                         await self.redis_client.setex(redis_key, self.config.l2_ttl, serialized_data)
            #                     except Exception as e:
            #                         logger.warning(f"L3->L2回填失败: {e}")
                            
            #                 if self.l1_cache is not None:
            #                     self.l1_cache[key] = cached_data
                                
            #                 return cached_data
            #             self.stats['l3_misses'] += 1
                        
            #         except Exception as e:
            #             logger.error(f"L3缓存访问失败: {e}")
            #             self.stats['l3_errors'] += 1
            
            return None
            
        finally:
            self.stats['get_time'] += time.time() - start_time
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None,
        cache_levels: Optional[List[CacheLevel]] = None
    ) -> None:
        """设置缓存数据"""
        if cache_levels is None:
            cache_levels = [CacheLevel.L1_MEMORY, CacheLevel.L2_REDIS, CacheLevel.L3_DATABASE]
            
        start_time = time.time()
        
        try:
            # L1内存缓存
            if CacheLevel.L1_MEMORY in cache_levels and self.l1_cache is not None:
                self.l1_cache[key] = value
                self.stats['l1_sets'] += 1
                logger.debug(f"数据写入L1缓存: {key}")
            
            # L2 Redis缓存
            if CacheLevel.L2_REDIS in cache_levels and self.redis_client:
                try:
                    redis_key = f"{self.config.l2_key_prefix}:{key}"
                    serialized_data = self._serialize_special_types(value)
                    
                    cache_ttl = ttl or self.config.l2_ttl
                    await self.redis_client.setex(redis_key, cache_ttl, serialized_data)
                    
                    self.stats['l2_sets'] += 1
                    logger.debug(f"数据写入L2缓存: {key}, TTL: {cache_ttl}")
                    
                except Exception as e:
                    logger.error(f"Redis缓存写入失败: {e}")
                    self.stats['l2_errors'] += 1
            
            # L3数据库缓存（异步队列写入）
            # if CacheLevel.L3_DATABASE in cache_levels and self.redis_client:
            #     # 动态获取存储服务（如果没有提供）
            #     storage_service = self.storage_service
            #     if not storage_service:
            #         storage_service = await self._get_storage_service()
                
            #     if storage_service:
            #         try:
            #             await self._queue_l3_write(key, value, ttl)
            #             self.stats['l3_queued'] += 1
            #             logger.debug(f"数据队列到L3缓存: {key}")
            #         except Exception as e:
            #             logger.error(f"L3缓存队列失败: {e}")
            #             self.stats['l3_errors'] += 1
            #     else:
            #         logger.debug("L3缓存: 无法获取存储服务，跳过写入")
                    
        finally:
            self.stats['set_time'] += time.time() - start_time
    
    async def delete(self, key: str) -> None:
        """删除缓存数据"""
        # L1内存缓存
        if self.l1_cache is not None and key in self.l1_cache:
            del self.l1_cache[key]
            self.stats['l1_deletes'] += 1
        
        # L2 Redis缓存
        if self.redis_client:
            try:
                redis_key = f"{self.config.l2_key_prefix}:{key}"
                await self.redis_client.delete(redis_key)
                self.stats['l2_deletes'] += 1
            except Exception as e:
                logger.error(f"Redis缓存删除失败: {e}")
        
        # L3缓存暂不支持单独删除，依赖数据过期机制
    
    async def _get_from_l3_cache(self, cache_key: str) -> Optional[Any]:
        """从L3缓存获取数据"""
        # 解析cache_key来获取数据类型和参数
        # cache_key格式: unified_data:data_type:params:start_date:end_date
        try:
            # 动态获取存储服务（如果没有提供）
            if not self.storage_service:
                storage_service = await self._get_storage_service()
                if not storage_service:
                    logger.debug("L3缓存: 无法获取存储服务")
                    return None
            else:
                storage_service = self.storage_service
            
            parts = cache_key.split(":")
            if len(parts) < 2 or parts[0] != "unified_data":
                return None
            
            data_type_str = parts[1]
            
            # 根据数据类型创建处理器
            if data_type_str in ["DataType.STOCK_DAILY", "DataType.STOCK_WEEKLY", "DataType.STOCK_MONTHLY"]:
                from .processors.factory import DataProcessorFactory
                from .models import DataType
                
                # 映射字符串到枚举
                data_type_map = {
                    "DataType.STOCK_DAILY": DataType.STOCK_DAILY,
                    "DataType.STOCK_WEEKLY": DataType.STOCK_WEEKLY, 
                    "DataType.STOCK_MONTHLY": DataType.STOCK_MONTHLY
                }
                
                data_type = data_type_map.get(data_type_str)
                if not data_type:
                    return None
                
                processor = DataProcessorFactory.get_processor(data_type)
                processor.storage_service = storage_service
                
                # 构建简化的DataRequest用于查询
                # 这里需要从cache_key中解析参数，简化处理
                from .models import DataRequest
                
                # 创建临时请求对象（仅用于L3查询）
                temp_request = DataRequest(
                    request_id="l3_cache_query",
                    data_type=data_type,
                    params={"stock_code": "000001"}  # 这里需要从key中解析
                )
                
                # 从持久化层获取数据
                return await processor.get_from_persistence(temp_request)
                
        except Exception as e:
            logger.error(f"L3缓存查询失败: {e}")
            return None
        
        return None
    
    def _serialize_special_types(self, data: Any) -> str:
        """序列化特殊数据类型"""
        try:
            import pandas as pd
            from app.schemas.common import CommonResponse
            
            def serialize_obj(obj):
                if isinstance(obj, pd.DataFrame):
                    return {
                        '__type__': 'DataFrame',
                        '__data__': obj.to_json(orient='records', date_format='iso')
                    }
                elif isinstance(obj, CommonResponse):
                    return {
                        '__type__': 'CommonResponse',
                        '__data__': obj.dict()
                    }
                elif isinstance(obj, dict):
                    return {k: serialize_obj(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [serialize_obj(item) for item in obj]
                else:
                    return obj
            
            serialized = serialize_obj(data)
            return json.dumps(serialized, default=str)
        except ImportError:
            return json.dumps(data, default=str)
    
    def _deserialize_special_types(self, data: Any) -> Any:
        """反序列化特殊数据类型"""
        try:
            import pandas as pd
            from app.schemas.common import CommonResponse
            
            def deserialize_obj(obj):
                if isinstance(obj, dict):
                    if obj.get('__type__') == 'DataFrame':
                        df_data = json.loads(obj['__data__'])
                        return pd.DataFrame(df_data)
                    elif obj.get('__type__') == 'CommonResponse':
                        response_data = obj['__data__']
                        return CommonResponse(**response_data)
                    else:
                        return {k: deserialize_obj(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [deserialize_obj(item) for item in obj]
                else:
                    return obj
            
            return deserialize_obj(data)
        except ImportError:
            return data
    
    async def _get_storage_service(self):
        """动态获取存储服务（警告：这是兜底方案）"""
        logger.warning("storage_service 未正确初始化，L3缓存功能受限")
        return None
    
    async def _queue_l3_write(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """将L3写入请求加入队列"""
        if not self.redis_client:
            return
        
        try:
            # 构建队列消息
            queue_message = {
                "key": key,
                "value": value,
                "ttl": ttl or self.config.l3_ttl,
                "timestamp": datetime.now().isoformat(),
                "retry_count": 0
            }
            
            # 推送到Redis队列
            await self.redis_client.lpush(
                self.l3_queue_key,
                self._serialize_special_types(queue_message)
            )
            
        except Exception as e:
            logger.error(f"L3队列写入失败: {e}")
            raise
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self.stats['l1_hits'] + self.stats['l1_misses']
        l1_hit_rate = (self.stats['l1_hits'] / total_requests * 100) if total_requests > 0 else 0
        
        total_l2_requests = self.stats['l2_hits'] + self.stats['l2_misses']
        l2_hit_rate = (self.stats['l2_hits'] / total_l2_requests * 100) if total_l2_requests > 0 else 0
        
        total_l3_requests = self.stats['l3_hits'] + self.stats['l3_misses']
        l3_hit_rate = (self.stats['l3_hits'] / total_l3_requests * 100) if total_l3_requests > 0 else 0
        
        return {
            'l1_hits': self.stats['l1_hits'],
            'l1_misses': self.stats['l1_misses'], 
            'l1_hit_rate': f"{l1_hit_rate:.2f}%",
            'l2_hits': self.stats['l2_hits'],
            'l2_misses': self.stats['l2_misses'],
            'l2_hit_rate': f"{l2_hit_rate:.2f}%",
            'l2_errors': self.stats['l2_errors'],
            'l3_hits': self.stats['l3_hits'],
            'l3_misses': self.stats['l3_misses'],
            'l3_hit_rate': f"{l3_hit_rate:.2f}%",
            'l3_queued': self.stats['l3_queued'],
            'l3_errors': self.stats['l3_errors'],
            'avg_get_time': self.stats['get_time'] / max(1, total_requests),
            'total_requests': total_requests
        }


class DataSourceRegistry:
    """数据源注册表"""
    
    def __init__(self):
        self.sources: Dict[str, Dict[str, Any]] = {}
        self.health_status: Dict[str, bool] = {}
        self.response_times: Dict[str, List[float]] = defaultdict(list)
        self.last_health_check: Dict[str, datetime] = {}
    
    def register_source(
        self, 
        name: str, 
        handler: Callable, 
        priority: int = 0,
        health_check: Optional[Callable] = None
    ) -> None:
        """注册数据源"""
        self.sources[name] = {
            'handler': handler,
            'priority': priority,
            'health_check': health_check,
            'registered_at': datetime.now()
        }
        self.health_status[name] = True
        logger.info(f"数据源已注册: {name}, 优先级: {priority}")
    
    def unregister_source(self, name: str) -> None:
        """注销数据源"""
        if name in self.sources:
            del self.sources[name] 
            del self.health_status[name]
            if name in self.response_times:
                del self.response_times[name]
            if name in self.last_health_check:
                del self.last_health_check[name]
            logger.info(f"数据源已注销: {name}")
    
    def get_available_sources(self, data_type: str = None) -> List[Tuple[str, Dict[str, Any]]]:
        """获取可用的数据源，按优先级排序"""
        available = []
        for name, info in self.sources.items():
            if self.health_status.get(name, False):
                available.append((name, info))
        
        # 按优先级排序
        available.sort(key=lambda x: x[1]['priority'], reverse=True)
        return available
    
    def record_response_time(self, source_name: str, response_time: float) -> None:
        """记录响应时间"""
        times = self.response_times[source_name]
        times.append(response_time)
        
        # 保持最近100次记录
        if len(times) > 100:
            times.pop(0)
    
    def get_avg_response_time(self, source_name: str) -> float:
        """获取平均响应时间"""
        times = self.response_times.get(source_name, [])
        return sum(times) / len(times) if times else float('inf')
    
    async def health_check(self, source_name: str) -> bool:
        """健康检查"""
        if source_name not in self.sources:
            return False
            
        source_info = self.sources[source_name]
        health_check_func = source_info.get('health_check')
        
        if health_check_func:
            try:
                is_healthy = await health_check_func()
                self.health_status[source_name] = is_healthy
                self.last_health_check[source_name] = datetime.now()
                return is_healthy
            except Exception as e:
                logger.error(f"数据源 {source_name} 健康检查失败: {e}")
                self.health_status[source_name] = False
                return False
        
        return True


class SmartDataRouter:
    """智能数据路由器"""
    
    def __init__(
        self, 
        cache_config: CacheConfig, 
        routing_config: RoutingConfig,
        redis_client: Optional[redis.Redis] = None,
        storage_service: Optional[Any] = None
    ):
        self.cache_config = cache_config
        self.routing_config = routing_config
        self.storage_service = storage_service
        
        # 初始化缓存管理器
        self.cache_manager = CacheManager(cache_config, redis_client, storage_service)
        
        # 初始化数据源注册表（保留兼容性）
        self.source_registry = DataSourceRegistry()
        
        # 路由统计
        self.stats = defaultdict(int)
        
    async def route_request(self, request: DataRequest) -> DataResult:
        """路由数据请求"""
        start_time = time.time()
        request_id = request.request_id
        
        logger.info(f"开始路由请求: {request_id}, 数据类型: {request.data_type}")
        
        try:
            # 生成缓存键
            cache_key = request.get_cache_key()
            
            # 1. 尝试从缓存获取数据
            if not request.force_refresh and request.cache_strategy in (
                CacheStrategy.CACHE_FIRST, 
                CacheStrategy.CACHE_THROUGH
            ):
                cached_result = await self._get_from_cache(cache_key, request)
                if cached_result:
                    cached_result.request_id = request_id
                    cached_result.response_time = time.time() - start_time
                    self.stats['cache_hits'] += 1
                    return cached_result
            
            # 2. 从数据源获取数据
            result = await self._get_from_source(request)
            
            # 3. 写入缓存
            if result.is_success and request.cache_strategy in (
                CacheStrategy.CACHE_FIRST,
                CacheStrategy.CACHE_THROUGH,
                CacheStrategy.WRITE_THROUGH
            ):
                await self._set_to_cache(cache_key, result, request)
            
            result.request_id = request_id
            result.response_time = time.time() - start_time
            self.stats['source_hits'] += 1
            
            return result
            
        except Exception as e:
            logger.error(f"路由请求失败: {request_id}, 错误: {e}")
            return DataResult(
                request_id=request_id,
                success=False,
                error_code="ROUTING_ERROR",
                error_message=str(e),
                response_time=time.time() - start_time
            )
    
    async def _get_from_cache(self, cache_key: str, request: DataRequest) -> Optional[DataResult]:
        """从缓存获取数据"""
        try:
            cached_data = await self.cache_manager.get(cache_key)
            if cached_data:
                # 检查缓存数据是否仍然有效
                if self._is_cache_valid(cached_data, request):
                    return DataResult(
                        request_id=request.request_id,
                        success=True,
                        data=cached_data.get('data'),
                        count=cached_data.get('count', 0),
                        cache_hit=True,
                        cache_level=CacheLevel.L1_MEMORY,  # 简化处理
                        data_source='cache'
                    )
        except Exception as e:
            logger.error(f"缓存访问失败: {e}")
        
        return None
    
    async def _get_from_source(self, request: DataRequest) -> DataResult:
        """从数据源获取数据（通过处理器工厂）"""
        start_time = time.time()
        
        try:
            # 首先尝试使用新的处理器工厂系统
            if request.data_type in [DataType.STOCK_DAILY, DataType.STOCK_WEEKLY, DataType.STOCK_MONTHLY, DataType.STOCK_INFO]:
                processor = DataProcessorFactory.get_processor(request.data_type)
                
                # 注入存储服务
                if self.storage_service:
                    processor.storage_service = self.storage_service
                
                # 验证请求
                if not await processor.validate_request(request):
                    return DataResult(
                        request_id=request.request_id,
                        success=False,
                        error_code="INVALID_REQUEST",
                        error_message="请求验证失败"
                    )
                
                # 尝试从持久化层获取数据
                if not request.force_refresh:
                    persisted_data = await processor.get_from_persistence(request)
                    if persisted_data and len(persisted_data) > 0:
                        logger.info(f"从持久化层获取数据成功: {len(persisted_data)} 条记录")
                        return DataResult(
                            request_id=request.request_id,
                            success=True,
                            data=persisted_data,
                            count=len(persisted_data),
                            data_source="persistence",
                            response_time=time.time() - start_time
                        )
                
                # 从API获取数据
                try:
                    api_data = await processor.fetch_from_api(request)
                    
                    if api_data:
                        # 格式化数据
                        formatted_data = processor.format_api_response(api_data, request)
                        
                        # 保存到持久化层
                        if formatted_data:
                            save_success = await processor.save_to_persistence(request, formatted_data)
                            logger.info(f"保存到持久化层: {'成功' if save_success else '失败'}")
                        
                        return DataResult(
                            request_id=request.request_id,
                            success=True,
                            data=formatted_data or api_data,
                            count=len(formatted_data) if formatted_data else (len(api_data) if isinstance(api_data, list) else 1),
                            data_source="api",
                            response_time=time.time() - start_time
                        )
                    else:
                        return DataResult(
                            request_id=request.request_id,
                            success=False,
                            error_code="NO_DATA",
                            error_message="API返回空数据",
                            data_source="api",
                            response_time=time.time() - start_time
                        )
                        
                except Exception as e:
                    logger.error(f"从API获取数据失败: {e}")
                    return DataResult(
                        request_id=request.request_id,
                        success=False,
                        error_code="API_ERROR",
                        error_message=str(e),
                        data_source="api",
                        response_time=time.time() - start_time
                    )
            
            # 兜底到原有的数据源注册表系统
            return await self._get_from_legacy_source(request)
            
        except Exception as e:
            logger.error(f"数据源路由失败: {e}")
            return DataResult(
                request_id=request.request_id,
                success=False,
                error_code="ROUTING_ERROR",
                error_message=str(e),
                response_time=time.time() - start_time
            )
    
    async def _get_from_legacy_source(self, request: DataRequest) -> DataResult:
        """从传统数据源获取数据（保留兼容性）"""
        # 获取可用的数据源
        available_sources = self.source_registry.get_available_sources(
            str(request.data_type)
        )
        
        if not available_sources:
            return DataResult(
                request_id=request.request_id,
                success=False,
                error_code="NO_AVAILABLE_SOURCE",
                error_message="没有可用的数据源"
            )
        
        # 根据路由策略选择数据源
        selected_source = await self._select_source(available_sources, request)
        
        if not selected_source:
            return DataResult(
                request_id=request.request_id,
                success=False,
                error_code="SOURCE_SELECTION_FAILED",
                error_message="数据源选择失败"
            )
        
        source_name, source_info = selected_source
        handler = source_info['handler']
        
        # 执行数据获取
        start_time = time.time()
        try:
            # 设置超时
            result = await asyncio.wait_for(
                handler(request),
                timeout=request.timeout
            )
            
            response_time = time.time() - start_time
            self.source_registry.record_response_time(source_name, response_time)
            
            result.data_source = source_name
            return result
            
        except asyncio.TimeoutError:
            logger.error(f"数据源 {source_name} 请求超时")
            return DataResult(
                request_id=request.request_id,
                success=False,
                error_code="REQUEST_TIMEOUT",
                error_message=f"数据源 {source_name} 请求超时",
                data_source=source_name
            )
        except Exception as e:
            logger.error(f"数据源 {source_name} 请求失败: {e}")
            return DataResult(
                request_id=request.request_id,
                success=False,
                error_code="SOURCE_REQUEST_FAILED",
                error_message=str(e),
                data_source=source_name
            )
    
    async def _select_source(
        self, 
        available_sources: List[Tuple[str, Dict[str, Any]]], 
        request: DataRequest
    ) -> Optional[Tuple[str, Dict[str, Any]]]:
        """根据路由策略选择数据源"""
        if not available_sources:
            return None
        
        strategy = request.routing_strategy
        
        if strategy == RoutingStrategy.FASTEST:
            # 选择平均响应时间最短的数据源
            best_source = min(
                available_sources,
                key=lambda x: self.source_registry.get_avg_response_time(x[0])
            )
            return best_source
            
        elif strategy == RoutingStrategy.MOST_RELIABLE:
            # 选择优先级最高的数据源
            return available_sources[0]  # 已按优先级排序
            
        elif strategy == RoutingStrategy.LOAD_BALANCED:
            # 简单轮询负载均衡
            source_count = len(available_sources)
            selected_index = self.stats['total_requests'] % source_count
            return available_sources[selected_index]
        
        else:
            # 默认返回第一个可用数据源
            return available_sources[0]
    
    async def _set_to_cache(
        self, 
        cache_key: str, 
        result: DataResult, 
        request: DataRequest
    ) -> None:
        """写入缓存"""
        if not result.is_success or not result.has_data:
            return
        
        cache_data = {
            'data': result.data,
            'count': result.count,
            'timestamp': datetime.now().isoformat(),
            'ttl': request.cache_ttl
        }
        
        await self.cache_manager.set(
            cache_key, 
            cache_data, 
            ttl=request.cache_ttl
        )
    
    def _is_cache_valid(self, cached_data: Dict[str, Any], request: DataRequest) -> bool:
        """检查缓存数据是否有效"""
        try:
            timestamp_str = cached_data.get('timestamp')
            if not timestamp_str:
                return False
            
            timestamp = datetime.fromisoformat(timestamp_str)
            ttl = cached_data.get('ttl', request.cache_ttl)
            
            return datetime.now() - timestamp < timedelta(seconds=ttl)
            
        except Exception as e:
            logger.error(f"缓存有效性检查失败: {e}")
            return False
    
    def register_source(
        self, 
        name: str, 
        handler: Callable[[DataRequest], DataResult], 
        priority: int = 0,
        health_check: Optional[Callable] = None
    ) -> None:
        """注册数据源"""
        self.source_registry.register_source(name, handler, priority, health_check)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取路由统计信息"""
        cache_stats = self.cache_manager.get_stats()
        
        total_requests = self.stats['cache_hits'] + self.stats['source_hits']
        cache_hit_rate = (self.stats['cache_hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'routing_stats': {
                'total_requests': total_requests,
                'cache_hits': self.stats['cache_hits'],
                'source_hits': self.stats['source_hits'],
                'cache_hit_rate': f"{cache_hit_rate:.2f}%"
            },
            'cache_stats': cache_stats,
            'source_stats': {
                'registered_sources': len(self.source_registry.sources),
                'healthy_sources': sum(1 for status in self.source_registry.health_status.values() if status)
            }
        }