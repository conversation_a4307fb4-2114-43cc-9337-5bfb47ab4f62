"""
数据事件总线

提供事件驱动架构支持，用于处理数据请求、缓存、监控等系统事件。
"""

import asyncio
from typing import Dict, List, Callable, Any, Optional, Union
from datetime import datetime
from collections import defaultdict
import logging
import json
from enum import Enum

from app.core.logging import getLogger

logger = getLogger(__name__)


class EventPriority(int, Enum):
    """事件优先级"""
    LOW = 0
    NORMAL = 1
    HIGH = 2
    CRITICAL = 3


class Event:
    """事件基类"""
    
    def __init__(
        self,
        event_type: str,
        data: Any = None,
        priority: EventPriority = EventPriority.NORMAL,
        source: Optional[str] = None,
        correlation_id: Optional[str] = None
    ):
        self.event_id = self._generate_event_id()
        self.event_type = event_type
        self.data = data
        self.priority = priority
        self.source = source or "unknown"
        self.correlation_id = correlation_id
        self.timestamp = datetime.now()
        self.metadata: Dict[str, Any] = {}
    
    def _generate_event_id(self) -> str:
        """生成事件ID"""
        import uuid
        return str(uuid.uuid4())
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'event_id': self.event_id,
            'event_type': self.event_type,
            'data': self.data,
            'priority': self.priority.value,
            'source': self.source,
            'correlation_id': self.correlation_id,
            'timestamp': self.timestamp.isoformat(),
            'metadata': self.metadata
        }
    
    def __str__(self) -> str:
        return f"Event(id={self.event_id}, type={self.event_type}, source={self.source})"


class EventHandler:
    """事件处理器"""
    
    def __init__(
        self,
        handler: Callable[[Event], Any],
        name: Optional[str] = None,
        priority: int = 0,
        async_handler: bool = True
    ):
        self.handler = handler
        self.name = name or handler.__name__
        self.priority = priority
        self.async_handler = async_handler
        self.created_at = datetime.now()
        self.call_count = 0
        self.error_count = 0
        self.last_called = None
        self.last_error = None
    
    async def handle(self, event: Event) -> Any:
        """处理事件"""
        try:
            self.call_count += 1
            self.last_called = datetime.now()
            
            if self.async_handler:
                if asyncio.iscoroutinefunction(self.handler):
                    return await self.handler(event)
                else:
                    # 在线程池中运行同步处理器
                    loop = asyncio.get_event_loop()
                    return await loop.run_in_executor(None, self.handler, event)
            else:
                return self.handler(event)
                
        except Exception as e:
            self.error_count += 1
            self.last_error = str(e)
            logger.error(f"事件处理器 {self.name} 处理失败: {e}")
            raise
    
    def get_stats(self) -> Dict[str, Any]:
        """获取处理器统计信息"""
        return {
            'name': self.name,
            'priority': self.priority,
            'call_count': self.call_count,
            'error_count': self.error_count,
            'error_rate': (self.error_count / max(self.call_count, 1)) * 100,
            'last_called': self.last_called.isoformat() if self.last_called else None,
            'last_error': self.last_error,
            'created_at': self.created_at.isoformat()
        }


class EventSubscription:
    """事件订阅"""
    
    def __init__(
        self,
        event_pattern: str,
        handler: EventHandler,
        filter_func: Optional[Callable[[Event], bool]] = None,
        once: bool = False
    ):
        self.subscription_id = self._generate_subscription_id()
        self.event_pattern = event_pattern
        self.handler = handler
        self.filter_func = filter_func
        self.once = once
        self.created_at = datetime.now()
        self.match_count = 0
        self.active = True
    
    def _generate_subscription_id(self) -> str:
        """生成订阅ID"""
        import uuid
        return str(uuid.uuid4())
    
    def matches(self, event: Event) -> bool:
        """检查事件是否匹配订阅"""
        if not self.active:
            return False
        
        # 检查事件类型模式匹配
        if not self._pattern_matches(self.event_pattern, event.event_type):
            return False
        
        # 应用过滤器
        if self.filter_func and not self.filter_func(event):
            return False
        
        self.match_count += 1
        
        # 如果是一次性订阅，匹配后取消激活
        if self.once:
            self.active = False
        
        return True
    
    def _pattern_matches(self, pattern: str, event_type: str) -> bool:
        """检查事件类型是否匹配模式"""
        # 支持通配符匹配
        if pattern == "*":
            return True
        
        if "*" in pattern:
            # 简单的通配符匹配
            import fnmatch
            return fnmatch.fnmatch(event_type, pattern)
        
        return pattern == event_type


class EventQueue:
    """事件队列"""
    
    def __init__(self, maxsize: int = 0):
        self._queue = asyncio.PriorityQueue(maxsize=maxsize)
        self._event_count = 0
    
    async def put(self, event: Event) -> None:
        """添加事件到队列"""
        # 使用负优先级确保高优先级事件先处理
        priority = -event.priority.value
        await self._queue.put((priority, self._event_count, event))
        self._event_count += 1
    
    async def get(self) -> Event:
        """从队列获取事件"""
        _, _, event = await self._queue.get()
        return event
    
    def qsize(self) -> int:
        """获取队列大小"""
        return self._queue.qsize()
    
    def empty(self) -> bool:
        """检查队列是否为空"""
        return self._queue.empty()


class DataEventBus:
    """数据事件总线"""
    
    def __init__(
        self,
        max_queue_size: int = 10000,
        max_workers: int = 2,
        enable_persistence: bool = False
    ):
        self.max_queue_size = max_queue_size
        self.max_workers = max_workers
        self.enable_persistence = enable_persistence
        
        # 事件队列
        self.event_queue = EventQueue(maxsize=max_queue_size)
        
        # 订阅管理
        self.subscriptions: List[EventSubscription] = []
        self.handlers_by_type: Dict[str, List[EventHandler]] = defaultdict(list)
        
        # 工作线程
        self.workers: List[asyncio.Task] = []
        self.running = False
        
        # 统计信息
        self.stats = {
            'events_published': 0,
            'events_processed': 0,
            'events_failed': 0,
            'subscriptions_count': 0,
            'handlers_count': 0,
            'start_time': None
        }
        
        # 事件历史（用于调试）
        self.event_history: List[Dict[str, Any]] = []
        self.max_history_size = 1000
    
    async def start(self) -> None:
        """启动事件总线"""
        if self.running:
            logger.warning("事件总线已在运行")
            return
        
        logger.info("启动数据事件总线...")
        
        self.running = True
        self.stats['start_time'] = datetime.now()
        
        # 启动工作线程
        for i in range(self.max_workers):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.workers.append(worker)
        
        logger.info(f"数据事件总线已启动，工作线程数: {self.max_workers}")
    
    async def stop(self) -> None:
        """停止事件总线"""
        if not self.running:
            return
        
        logger.info("停止数据事件总线...")
        
        self.running = False
        
        # 停止工作线程
        for worker in self.workers:
            worker.cancel()
        
        if self.workers:
            await asyncio.gather(*self.workers, return_exceptions=True)
        
        self.workers.clear()
        logger.info("数据事件总线已停止")
    
    async def emit(
        self,
        event_type: str,
        data: Any = None,
        priority: EventPriority = EventPriority.NORMAL,
        source: Optional[str] = None,
        correlation_id: Optional[str] = None,
        wait: bool = False
    ) -> Optional[Event]:
        """发送事件"""
        event = Event(
            event_type=event_type,
            data=data,
            priority=priority,
            source=source,
            correlation_id=correlation_id
        )
        
        try:
            if wait:
                # 同步处理事件
                await self._process_event_sync(event)
            else:
                # 异步处理事件
                await self.event_queue.put(event)
            
            self.stats['events_published'] += 1
            
            # 记录到历史
            self._add_to_history(event)
            
            logger.debug(f"事件已发送: {event}")
            return event
            
        except Exception as e:
            logger.error(f"事件发送失败: {e}")
            self.stats['events_failed'] += 1
            return None
    
    def subscribe(
        self,
        event_pattern: str,
        handler: Callable[[Event], Any],
        priority: int = 0,
        filter_func: Optional[Callable[[Event], bool]] = None,
        once: bool = False,
        async_handler: bool = True
    ) -> str:
        """订阅事件"""
        event_handler = EventHandler(
            handler=handler,
            priority=priority,
            async_handler=async_handler
        )
        
        subscription = EventSubscription(
            event_pattern=event_pattern,
            handler=event_handler,
            filter_func=filter_func,
            once=once
        )
        
        self.subscriptions.append(subscription)
        self.stats['subscriptions_count'] = len(self.subscriptions)
        self.stats['handlers_count'] = sum(len(handlers) for handlers in self.handlers_by_type.values())
        
        logger.info(f"事件订阅已创建: {event_pattern} -> {event_handler.name}")
        return subscription.subscription_id
    
    def unsubscribe(self, subscription_id: str) -> bool:
        """取消订阅"""
        for i, subscription in enumerate(self.subscriptions):
            if subscription.subscription_id == subscription_id:
                del self.subscriptions[i]
                self.stats['subscriptions_count'] = len(self.subscriptions)
                logger.info(f"事件订阅已取消: {subscription_id}")
                return True
        
        logger.warning(f"未找到订阅: {subscription_id}")
        return False
    
    def on(self, event_pattern: str, **kwargs):
        """装饰器版本的事件订阅"""
        def decorator(func: Callable[[Event], Any]) -> Callable[[Event], Any]:
            self.subscribe(event_pattern, func, **kwargs)
            return func
        return decorator
    
    def once(self, event_pattern: str, **kwargs):
        """装饰器版本的一次性事件订阅"""
        def decorator(func: Callable[[Event], Any]) -> Callable[[Event], Any]:
            self.subscribe(event_pattern, func, once=True, **kwargs)
            return func
        return decorator
    
    async def wait_for_event(
        self,
        event_pattern: str,
        timeout: Optional[float] = None,
        filter_func: Optional[Callable[[Event], bool]] = None
    ) -> Optional[Event]:
        """等待特定事件"""
        future = asyncio.Future()
        
        def handler(event: Event):
            if not future.done():
                future.set_result(event)
        
        subscription_id = self.subscribe(
            event_pattern,
            handler,
            filter_func=filter_func,
            once=True
        )
        
        try:
            if timeout:
                return await asyncio.wait_for(future, timeout=timeout)
            else:
                return await future
        except asyncio.TimeoutError:
            self.unsubscribe(subscription_id)
            return None
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        uptime = None
        if self.stats['start_time']:
            uptime = (datetime.now() - self.stats['start_time']).total_seconds()
        
        handler_stats = []
        for subscription in self.subscriptions:
            if subscription.active:
                handler_stats.append(subscription.handler.get_stats())
        
        return {
            **self.stats,
            'running': self.running,
            'queue_size': self.event_queue.qsize(),
            'uptime_seconds': uptime,
            'events_per_second': (
                self.stats['events_processed'] / max(uptime or 1, 1)
            ),
            'active_subscriptions': sum(1 for s in self.subscriptions if s.active),
            'handler_stats': handler_stats
        }
    
    def get_event_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取事件历史"""
        return self.event_history[-limit:]
    
    # 私有方法
    async def _worker(self, worker_name: str) -> None:
        """事件处理工作线程"""
        logger.info(f"事件处理工作线程启动: {worker_name}")
        
        while self.running:
            try:
                # 获取事件
                event = await self.event_queue.get()
                
                # 处理事件
                await self._process_event(event)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"工作线程 {worker_name} 处理事件失败: {e}")
        
        logger.info(f"事件处理工作线程停止: {worker_name}")
    
    async def _process_event(self, event: Event) -> None:
        """处理事件"""
        try:
            matching_subscriptions = [
                sub for sub in self.subscriptions if sub.matches(event)
            ]
            
            if not matching_subscriptions:
                logger.debug(f"没有匹配的订阅: {event}")
                return
            
            # 按优先级排序处理器
            matching_subscriptions.sort(key=lambda s: s.handler.priority, reverse=True)
            
            # 并发处理所有匹配的订阅
            tasks = []
            for subscription in matching_subscriptions:
                task = asyncio.create_task(subscription.handler.handle(event))
                tasks.append(task)
            
            # 等待所有处理器完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计处理结果
            success_count = sum(1 for r in results if not isinstance(r, Exception))
            error_count = len(results) - success_count
            
            self.stats['events_processed'] += 1
            if error_count > 0:
                self.stats['events_failed'] += 1
            
            logger.debug(f"事件处理完成: {event}, 成功: {success_count}, 失败: {error_count}")
            
        except Exception as e:
            logger.error(f"事件处理失败: {event}, 错误: {e}")
            self.stats['events_failed'] += 1
    
    async def _process_event_sync(self, event: Event) -> None:
        """同步处理事件（用于wait=True的情况）"""
        await self._process_event(event)
    
    def _add_to_history(self, event: Event) -> None:
        """添加事件到历史记录"""
        self.event_history.append(event.to_dict())
        
        # 限制历史记录大小
        if len(self.event_history) > self.max_history_size:
            self.event_history = self.event_history[-self.max_history_size:]


# 全局事件总线实例
_event_bus_instance: Optional[DataEventBus] = None


def get_event_bus() -> DataEventBus:
    """获取全局事件总线实例"""
    global _event_bus_instance
    if _event_bus_instance is None:
        _event_bus_instance = DataEventBus()
    return _event_bus_instance


# 便捷函数
async def emit_event(
    event_type: str,
    data: Any = None,
    **kwargs
) -> Optional[Event]:
    """发送事件的便捷函数"""
    event_bus = get_event_bus()
    return await event_bus.emit(event_type, data, **kwargs)


def on_event(event_pattern: str, **kwargs):
    """事件订阅装饰器"""
    event_bus = get_event_bus()
    return event_bus.on(event_pattern, **kwargs)