"""
股票信息数据处理器

处理股票基本信息的持久化和API获取。
"""

from typing import Optional, Any, Dict, List
from datetime import datetime

from app.core.logging import getLogger
from app.services.data_fetcher.factory import DataFetcherFactory
from ..models import DataRequest
from .base import DataProcessorStrategy

logger = getLogger(__name__)


class StockInfoProcessor(DataProcessorStrategy):
    """股票信息数据处理器"""
    
    async def get_from_persistence(self, request: DataRequest) -> Optional[Any]:
        """从数据库获取股票信息"""
        if not self.storage_service:
            logger.warning("存储服务未注入，无法从持久化层获取数据")
            return None
        
        try:
            stock_code = request.params.get('stock_code')
            
            if stock_code:
                # 获取单个股票信息
                stock_info = await self.storage_service.get_stock_info(stock_code)
                return [stock_info] if stock_info else None
            else:
                # 获取股票列表
                result = await self.storage_service.get_stock_list(
                    skip=request.params.get('skip', 0),
                    limit=request.params.get('limit', 100),
                    industry=request.params.get('industry'),
                    is_active=request.params.get('is_active'),
                    search=request.params.get('search'),
                    exchange=request.params.get('exchange')
                )
                return result.get('stocks', []) if result else None
                
        except Exception as e:
            logger.error(f"从持久化层获取股票信息失败: {e}")
            return None
    
    async def save_to_persistence(self, request: DataRequest, data: Any) -> bool:
        """保存股票信息到数据库"""
        if not self.storage_service:
            logger.warning("存储服务未注入，无法保存到持久化层")
            return False
        
        try:
            if isinstance(data, list) and len(data) > 0:
                # 批量保存股票信息（如刷新股票列表）
                result = await self.storage_service.replace_all_stock_info(data)
                return result.get('inserted', 0) > 0
            elif isinstance(data, dict):
                # 保存单个股票信息
                saved_stock = await self.storage_service.save_stock_info(data)
                return saved_stock is not None
            else:
                logger.warning(f"无效的股票信息数据格式: {type(data)}")
                return False
                
        except Exception as e:
            logger.error(f"保存股票信息到持久化层失败: {e}")
            return False
    
    async def fetch_from_api(self, request: DataRequest) -> Any:
        """从API获取股票信息"""
        try:
            # 使用现有的数据获取器工厂
            fetcher = DataFetcherFactory.create_fetcher()
            
            stock_code = request.params.get('stock_code')
            
            if stock_code:
                # 获取所有股票列表，然后过滤指定股票
                all_stocks = await fetcher.get_stock_list()
                filtered_stocks = [
                    stock for stock in all_stocks 
                    if stock.get('code') == stock_code
                ]
                return filtered_stocks
            else:
                # 获取完整股票列表
                return await fetcher.get_stock_list()
                
        except Exception as e:
            logger.error(f"从API获取股票信息失败: {e}")
            raise
    
    def get_cache_config(self) -> Dict[str, Any]:
        """获取股票信息缓存配置"""
        return {
            "ttl": 3600,        # 1小时缓存
            "compress": False,   # 股票信息数据量不大，不需要压缩
            "max_size": 1000    # 最多缓存1000个条目
        }
    
    def format_api_response(self, raw_data: Any, request: DataRequest) -> Any:
        """格式化API响应数据"""
        if not isinstance(raw_data, list):
            return []
        
        # 使用数据适配器标准化数据格式
        try:
            fetcher = DataFetcherFactory.create_fetcher()
            if hasattr(fetcher, '_adapter'):
                return [
                    fetcher._adapter.normalize_stock_info(item) 
                    for item in raw_data
                ]
        except Exception as e:
            logger.warning(f"数据格式化失败，使用原始数据: {e}")
        
        return raw_data