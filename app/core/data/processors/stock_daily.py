"""
股票日线数据处理器

处理股票日线数据的持久化和API获取，迁移现有逻辑。
"""

from typing import Optional, Any, Dict, List, Union
from datetime import datetime, date

from app.core.logging import getLogger
from app.services.data_fetcher.factory import DataFetcherFactory
from ..models import DataRequest
from .base import DataProcessorStrategy

logger = getLogger(__name__)


class StockDailyProcessor(DataProcessorStrategy):
    """股票日线数据处理器"""
    
    async def get_from_persistence(self, request: DataRequest) -> Optional[Any]:
        """从数据库获取日线数据"""
        if not self.storage_service:
            logger.warning("存储服务未注入，无法从持久化层获取数据")
            return None
        
        try:
            stock_code = request.params.get('stock_code')
            start_date = request.start_date
            end_date = request.end_date
            
            if not stock_code:
                logger.warning("获取日线数据缺少股票代码")
                return None
            
            # 使用现有的存储服务获取日线数据
            daily_data = await self.storage_service.get_daily_data(
                stock_code=stock_code,
                start_date=start_date,
                end_date=end_date
            )
            
            return daily_data
            
        except Exception as e:
            logger.error(f"从持久化层获取日线数据失败: {e}")
            return None
    
    async def save_to_persistence(self, request: DataRequest, data: Any) -> bool:
        """保存日线数据到数据库"""
        if not self.storage_service:
            logger.warning("存储服务未注入，无法保存到持久化层")
            return False
        
        try:
            stock_code = request.params.get('stock_code')
            
            if not stock_code:
                logger.warning("保存日线数据缺少股票代码")
                return False
            
            if not isinstance(data, list):
                logger.warning(f"无效的日线数据格式: {type(data)}")
                return False
            
            # 为每条数据添加股票代码
            formatted_data = []
            for item in data:
                if isinstance(item, dict):
                    item_with_code = item.copy()
                    item_with_code['stock_code'] = stock_code
                    formatted_data.append(item_with_code)
                else:
                    logger.warning(f"跳过无效的数据项: {item}")
            
            if not formatted_data:
                logger.warning("没有有效的日线数据需要保存")
                return False
            
            # 使用现有的批量保存方法
            success_count = await self.storage_service.batch_save_daily_data(formatted_data)
            
            logger.info(f"成功保存 {success_count} 条日线数据")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"保存日线数据到持久化层失败: {e}")
            return False
    
    async def fetch_from_api(self, request: DataRequest) -> Any:
        """从API获取日线数据"""
        try:
            stock_code = request.params.get('stock_code')
            start_date = request.start_date
            end_date = request.end_date
            
            if not stock_code:
                raise ValueError("获取日线数据缺少股票代码")
            
            # 使用现有的数据获取器工厂
            fetcher = DataFetcherFactory.create_fetcher()
            
            # 调用日线数据接口
            raw_data = await fetcher.get_daily_data(
                code=stock_code,
                start_date=start_date,
                end_date=end_date
            )
            
            return raw_data
            
        except Exception as e:
            logger.error(f"从API获取日线数据失败: {e}")
            raise
    
    def get_cache_config(self) -> Dict[str, Any]:
        """获取日线数据缓存配置"""
        return {
            "ttl": 1800,         # 30分钟缓存
            "compress": False,    # 日线数据访问频繁，不启用压缩以提高性能
            "max_size": 1000      # 最多缓存1000个条目
        }
    
    def format_api_response(self, raw_data: Any, request: DataRequest) -> Any:
        """格式化API响应数据"""
        if not isinstance(raw_data, list):
            logger.warning(f"API返回的日线数据格式异常: {type(raw_data)}")
            return []
        
        # 使用数据适配器标准化数据格式
        try:
            fetcher = DataFetcherFactory.create_fetcher()
            if hasattr(fetcher, '_adapter'):
                formatted_data = []
                for item in raw_data:
                    normalized_item = fetcher._adapter.normalize_daily_data(item)
                    if normalized_item:  # 只添加有效的数据项
                        formatted_data.append(normalized_item)
                return formatted_data
        except Exception as e:
            logger.warning(f"日线数据格式化失败，使用原始数据: {e}")
        
        return raw_data
    
    async def validate_request(self, request: DataRequest) -> bool:
        """验证日线数据请求的有效性"""
        if not await super().validate_request(request):
            return False
        
        # 日线数据特定的验证逻辑
        stock_code = request.params.get('stock_code')
        if not stock_code:
            logger.warning(f"日线数据请求缺少股票代码 - {request.request_id}")
            return False
        
        # 验证日期范围的合理性
        if request.start_date and request.end_date:
            if request.start_date > request.end_date:
                logger.warning(f"日线数据请求日期范围无效 - {request.request_id}")
                return False
        
        return True