"""
数据处理器策略基类

定义统一的数据处理接口，用于L3持久化和L4 API的适配工作。
"""

from abc import ABC, abstractmethod
from typing import Optional, Any, Dict
from datetime import datetime

from app.core.logging import getLogger
from ..models import DataRequest

logger = getLogger(__name__)


class DataProcessorStrategy(ABC):
    """数据处理策略基类"""
    
    def __init__(self):
        self.storage_service = None
        
    def set_storage_service(self, storage_service):
        """注入存储服务依赖"""
        self.storage_service = storage_service
    
    @abstractmethod
    async def get_from_persistence(self, request: DataRequest) -> Optional[Any]:
        """
        从L3持久化层获取数据
        
        Args:
            request: 数据请求对象
            
        Returns:
            Optional[Any]: 持久化数据，如果没有找到则返回None
        """
        pass
    
    @abstractmethod 
    async def save_to_persistence(self, request: DataRequest, data: Any) -> bool:
        """
        保存到L3持久化层
        
        Args:
            request: 数据请求对象
            data: 要保存的数据
            
        Returns:
            bool: 保存是否成功
        """
        pass
        
    @abstractmethod
    async def fetch_from_api(self, request: DataRequest) -> Any:
        """
        从L4 API获取数据
        
        Args:
            request: 数据请求对象
            
        Returns:
            Any: API返回的数据
            
        Raises:
            Exception: API调用失败时抛出异常
        """
        pass
        
    @abstractmethod
    def get_cache_config(self) -> Dict[str, Any]:
        """
        获取该数据类型的缓存配置
        
        Returns:
            Dict[str, Any]: 缓存配置字典，包含ttl、compress等参数
        """
        pass
    
    def get_data_type_name(self) -> str:
        """获取数据类型名称"""
        return self.__class__.__name__.replace('Processor', '').lower()
    
    async def validate_request(self, request: DataRequest) -> bool:
        """
        验证请求的有效性
        
        Args:
            request: 数据请求对象
            
        Returns:
            bool: 请求是否有效
        """
        # 基础验证逻辑
        if not request.params.get('stock_code'):
            logger.warning(f"无效请求：缺少股票代码 - {request.request_id}")
            return False
        
        return True
    
    def format_persistence_data(self, raw_data: Any, request: DataRequest) -> Any:
        """
        格式化数据用于持久化存储
        
        Args:
            raw_data: 原始数据
            request: 数据请求对象
            
        Returns:
            Any: 格式化后的数据
        """
        # 子类可以重写此方法来实现特定的数据格式化逻辑
        return raw_data
    
    def format_api_response(self, raw_data: Any, request: DataRequest) -> Any:
        """
        格式化API响应数据
        
        Args:
            raw_data: API原始响应数据
            request: 数据请求对象
            
        Returns:
            Any: 格式化后的数据
        """
        # 子类可以重写此方法来实现特定的响应格式化逻辑
        return raw_data