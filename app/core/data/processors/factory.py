"""
数据处理器工厂

基于数据类型创建对应的数据处理器实例。
"""

from typing import Dict, Type

from app.core.logging import getLogger
from ..models import DataType
from .base import DataProcessorStrategy

logger = getLogger(__name__)


class DataProcessorFactory:
    """数据处理器工厂"""
    
    _processors: Dict[DataType, Type[DataProcessorStrategy]] = {}
    
    @classmethod
    def register_processor(
        cls, 
        data_type: DataType, 
        processor_class: Type[DataProcessorStrategy]
    ) -> None:
        """
        注册数据处理器
        
        Args:
            data_type: 数据类型
            processor_class: 处理器类
        """
        cls._processors[data_type] = processor_class
        logger.info(f"注册数据处理器: {data_type.value} -> {processor_class.__name__}")
    
    @classmethod
    def get_processor(cls, data_type: DataType) -> DataProcessorStrategy:
        """
        获取数据处理器实例
        
        Args:
            data_type: 数据类型
            
        Returns:
            DataProcessorStrategy: 处理器实例
            
        Raises:
            ValueError: 不支持的数据类型
        """
        processor_class = cls._processors.get(data_type)
        if not processor_class:
            raise ValueError(f"不支持的数据类型: {data_type.value}")
        
        return processor_class()
    
    @classmethod
    def list_supported_types(cls) -> list:
        """获取支持的数据类型列表"""
        return list(cls._processors.keys())
    
    @classmethod
    def is_supported(cls, data_type: DataType) -> bool:
        """检查是否支持指定数据类型"""
        return data_type in cls._processors


# 延迟导入和注册处理器，避免循环导入
def _register_default_processors():
    """注册默认的数据处理器"""
    try:
        # 导入具体的处理器实现
        from .stock_daily import StockDailyProcessor
        from .stock_weekly import StockWeeklyProcessor
        from .stock_monthly import StockMonthlyProcessor
        from .stock_info import StockInfoProcessor
        
        # 注册处理器
        DataProcessorFactory.register_processor(DataType.STOCK_DAILY, StockDailyProcessor)
        DataProcessorFactory.register_processor(DataType.STOCK_WEEKLY, StockWeeklyProcessor)
        DataProcessorFactory.register_processor(DataType.STOCK_MONTHLY, StockMonthlyProcessor)
        DataProcessorFactory.register_processor(DataType.STOCK_INFO, StockInfoProcessor)
        
        logger.info(f"已注册 {len(DataProcessorFactory._processors)} 个数据处理器")
        
    except ImportError as e:
        logger.warning(f"部分数据处理器注册失败: {e}")


# 自动注册处理器
_register_default_processors()