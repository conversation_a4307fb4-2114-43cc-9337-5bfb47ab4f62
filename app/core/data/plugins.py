"""
插件系统基础架构

提供数据源插件、处理器插件和中间件插件的基础类和管理器。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Callable, Type, Union
import asyncio
import importlib
import inspect
import os
from pathlib import Path
from datetime import datetime
import logging

from app.core.logging import getLogger
from .models import DataRequest, DataResult, DataType, PluginConfig

logger = getLogger(__name__)


class BasePlugin(ABC):
    """插件基类"""
    
    def __init__(self, name: str, version: str = "1.0.0"):
        self.name = name
        self.version = version
        self.enabled = True
        self.created_at = datetime.now()
        self.metadata: Dict[str, Any] = {}
    
    @abstractmethod
    async def initialize(self) -> None:
        """初始化插件"""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """清理插件资源"""
        pass
    
    def get_info(self) -> Dict[str, Any]:
        """获取插件信息"""
        return {
            'name': self.name,
            'version': self.version,
            'enabled': self.enabled,
            'created_at': self.created_at.isoformat(),
            'metadata': self.metadata
        }


class DataSourcePlugin(BasePlugin):
    """数据源插件基类"""
    
    def __init__(
        self, 
        name: str, 
        version: str = "1.0.0",
        supported_data_types: Optional[List[DataType]] = None,
        priority: int = 0
    ):
        super().__init__(name, version)
        self.supported_data_types = supported_data_types or []
        self.priority = priority
    
    @abstractmethod
    async def fetch_data(self, request: DataRequest) -> DataResult:
        """获取数据"""
        pass
    
    async def health_check(self) -> bool:
        """健康检查"""
        return True
    
    def supports_data_type(self, data_type: DataType) -> bool:
        """检查是否支持指定数据类型"""
        return not self.supported_data_types or data_type in self.supported_data_types


class DataProcessorPlugin(BasePlugin):
    """数据处理器插件基类"""
    
    def __init__(
        self, 
        name: str, 
        version: str = "1.0.0",
        processing_types: Optional[List[str]] = None
    ):
        super().__init__(name, version)
        self.processing_types = processing_types or []
    
    @abstractmethod
    async def process_data(
        self, 
        data: Any, 
        request: DataRequest,
        processing_type: str = "default"
    ) -> Any:
        """处理数据"""
        pass
    
    def supports_processing_type(self, processing_type: str) -> bool:
        """检查是否支持指定处理类型"""
        return not self.processing_types or processing_type in self.processing_types


class MiddlewarePlugin(BasePlugin):
    """中间件插件基类"""
    
    def __init__(
        self, 
        name: str, 
        version: str = "1.0.0",
        middleware_type: str = "request"
    ):
        super().__init__(name, version)
        self.middleware_type = middleware_type  # "request" 或 "response"
    
    async def process_request(self, request: DataRequest) -> DataRequest:
        """处理请求（可选重写）"""
        return request
    
    async def process_response(
        self, 
        result: DataResult, 
        request: DataRequest
    ) -> DataResult:
        """处理响应（可选重写）"""
        return result


class PluginRegistry:
    """插件注册表"""
    
    def __init__(self):
        self.plugins: Dict[str, BasePlugin] = {}
        self.data_sources: Dict[str, DataSourcePlugin] = {}
        self.processors: Dict[str, DataProcessorPlugin] = {}
        self.middlewares: Dict[str, List[MiddlewarePlugin]] = {
            'request': [],
            'response': []
        }
    
    def register_plugin(self, plugin: BasePlugin) -> None:
        """注册插件"""
        plugin_name = plugin.name
        
        if plugin_name in self.plugins:
            logger.warning(f"插件 {plugin_name} 已存在，将被覆盖")
        
        self.plugins[plugin_name] = plugin
        
        # 根据插件类型分类注册
        if isinstance(plugin, DataSourcePlugin):
            self.data_sources[plugin_name] = plugin
        elif isinstance(plugin, DataProcessorPlugin):
            self.processors[plugin_name] = plugin
        elif isinstance(plugin, MiddlewarePlugin):
            self.middlewares[plugin.middleware_type].append(plugin)
        
        logger.info(f"插件已注册: {plugin_name} ({type(plugin).__name__})")
    
    def unregister_plugin(self, plugin_name: str) -> None:
        """注销插件"""
        if plugin_name not in self.plugins:
            logger.warning(f"插件 {plugin_name} 不存在")
            return
        
        plugin = self.plugins[plugin_name]
        
        # 从分类注册表中移除
        if isinstance(plugin, DataSourcePlugin):
            self.data_sources.pop(plugin_name, None)
        elif isinstance(plugin, DataProcessorPlugin):
            self.processors.pop(plugin_name, None)
        elif isinstance(plugin, MiddlewarePlugin):
            middleware_list = self.middlewares[plugin.middleware_type]
            self.middlewares[plugin.middleware_type] = [
                p for p in middleware_list if p.name != plugin_name
            ]
        
        # 从主注册表中移除
        del self.plugins[plugin_name]
        logger.info(f"插件已注销: {plugin_name}")
    
    def get_plugin(self, plugin_name: str) -> Optional[BasePlugin]:
        """获取插件"""
        return self.plugins.get(plugin_name)
    
    def get_data_sources(
        self, 
        data_type: Optional[DataType] = None,
        enabled_only: bool = True
    ) -> List[DataSourcePlugin]:
        """获取数据源插件"""
        sources = []
        for plugin in self.data_sources.values():
            if enabled_only and not plugin.enabled:
                continue
            if data_type and not plugin.supports_data_type(data_type):
                continue
            sources.append(plugin)
        
        # 按优先级排序
        sources.sort(key=lambda p: p.priority, reverse=True)
        return sources
    
    def get_processors(
        self, 
        processing_type: Optional[str] = None,
        enabled_only: bool = True
    ) -> List[DataProcessorPlugin]:
        """获取数据处理器插件"""
        processors = []
        for plugin in self.processors.values():
            if enabled_only and not plugin.enabled:
                continue
            if processing_type and not plugin.supports_processing_type(processing_type):
                continue
            processors.append(plugin)
        
        return processors
    
    def get_middlewares(
        self, 
        middleware_type: str,
        enabled_only: bool = True
    ) -> List[MiddlewarePlugin]:
        """获取中间件插件"""
        middlewares = self.middlewares.get(middleware_type, [])
        if enabled_only:
            middlewares = [p for p in middlewares if p.enabled]
        return middlewares
    
    def list_plugins(self, plugin_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """列出插件信息"""
        plugins = []
        
        for plugin in self.plugins.values():
            if plugin_type:
                if plugin_type == "data_source" and not isinstance(plugin, DataSourcePlugin):
                    continue
                elif plugin_type == "processor" and not isinstance(plugin, DataProcessorPlugin):
                    continue
                elif plugin_type == "middleware" and not isinstance(plugin, MiddlewarePlugin):
                    continue
            
            plugins.append(plugin.get_info())
        
        return plugins


class PluginLoader:
    """插件加载器"""
    
    def __init__(self, plugin_dirs: Optional[List[str]] = None):
        self.plugin_dirs = plugin_dirs or []
    
    async def load_plugins_from_directory(
        self, 
        directory: str,
        registry: PluginRegistry
    ) -> List[BasePlugin]:
        """从目录加载插件"""
        loaded_plugins = []
        plugin_path = Path(directory)
        
        if not plugin_path.exists() or not plugin_path.is_dir():
            logger.warning(f"插件目录不存在: {directory}")
            return loaded_plugins
        
        # 遍历插件文件
        for file_path in plugin_path.glob("*.py"):
            if file_path.name.startswith("__"):
                continue
            
            try:
                plugin = await self._load_plugin_from_file(file_path)
                if plugin:
                    registry.register_plugin(plugin)
                    loaded_plugins.append(plugin)
            except Exception as e:
                logger.error(f"加载插件失败 {file_path}: {e}")
        
        return loaded_plugins
    
    async def _load_plugin_from_file(self, file_path: Path) -> Optional[BasePlugin]:
        """从文件加载插件"""
        try:
            # 动态导入模块
            spec = importlib.util.spec_from_file_location(
                file_path.stem, 
                file_path
            )
            if not spec or not spec.loader:
                return None
            
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # 查找插件类
            plugin_classes = []
            for name, obj in inspect.getmembers(module, inspect.isclass):
                if (issubclass(obj, BasePlugin) and 
                    obj != BasePlugin and
                    not obj.__name__.startswith('_')):
                    plugin_classes.append(obj)
            
            if not plugin_classes:
                logger.warning(f"文件中未找到插件类: {file_path}")
                return None
            
            # 实例化插件（取第一个找到的插件类）
            plugin_class = plugin_classes[0]
            plugin = plugin_class()
            
            logger.info(f"插件加载成功: {plugin.name} from {file_path}")
            return plugin
            
        except Exception as e:
            logger.error(f"插件文件加载失败 {file_path}: {e}")
            return None


class PluginManager:
    """插件管理器"""
    
    def __init__(self, config: PluginConfig):
        self.config = config
        self.registry = PluginRegistry()
        self.loader = PluginLoader(config.plugin_dirs)
        self.loaded_plugins: List[BasePlugin] = []
        
        # 统计信息
        self.stats = {
            'loaded_plugins': 0,
            'active_plugins': 0,
            'failed_loads': 0,
            'last_load_time': None
        }
    
    async def initialize(self) -> None:
        """初始化插件管理器"""
        if not self.config.enabled:
            logger.info("插件系统已禁用")
            return
        
        logger.info("初始化插件管理器...")
        
        try:
            # 自动发现并加载插件
            if self.config.auto_discovery:
                await self._auto_discover_plugins()
            
            # 初始化所有加载的插件
            await self._initialize_plugins()
            
            self.stats['last_load_time'] = datetime.now()
            logger.info(f"插件管理器初始化完成，共加载 {len(self.loaded_plugins)} 个插件")
            
        except Exception as e:
            logger.error(f"插件管理器初始化失败: {e}")
            raise
    
    async def cleanup(self) -> None:
        """清理插件管理器"""
        logger.info("清理插件管理器...")
        
        # 清理所有插件
        for plugin in self.loaded_plugins:
            try:
                await plugin.cleanup()
            except Exception as e:
                logger.error(f"插件清理失败 {plugin.name}: {e}")
        
        self.loaded_plugins.clear()
        self.registry = PluginRegistry()
    
    async def register_plugin(self, plugin: BasePlugin) -> None:
        """注册插件"""
        self.registry.register_plugin(plugin) 
        
        if plugin not in self.loaded_plugins:
            self.loaded_plugins.append(plugin)
        
        # 初始化插件
        try:
            await plugin.initialize()
            self.stats['loaded_plugins'] += 1
            if plugin.enabled:
                self.stats['active_plugins'] += 1
        except Exception as e:
            logger.error(f"插件初始化失败 {plugin.name}: {e}")
            self.stats['failed_loads'] += 1
    
    def register_middleware(
        self, 
        middleware: Callable,
        middleware_type: str = "request"
    ) -> None:
        """注册中间件函数"""
        # 将函数包装为中间件插件
        class FunctionMiddleware(MiddlewarePlugin):
            def __init__(self):
                super().__init__(
                    name=f"func_{middleware.__name__}",
                    middleware_type=middleware_type
                )
                self.func = middleware
            
            async def initialize(self):
                pass
            
            async def cleanup(self):
                pass
            
            async def process_request(self, request: DataRequest) -> DataRequest:
                if middleware_type == "request":
                    if asyncio.iscoroutinefunction(self.func):
                        return await self.func(request)
                    else:
                        return self.func(request)
                return request
            
            async def process_response(
                self, 
                result: DataResult, 
                request: DataRequest
            ) -> DataResult:
                if middleware_type == "response":
                    if asyncio.iscoroutinefunction(self.func):
                        return await self.func(result, request)
                    else:
                        return self.func(result, request)
                return result
        
        middleware_plugin = FunctionMiddleware()
        self.registry.register_plugin(middleware_plugin)
    
    async def apply_request_middleware(self, request: DataRequest) -> DataRequest:
        """应用请求中间件"""
        if not self.config.middleware_enabled:
            return request
        
        middlewares = self.registry.get_middlewares("request")
        
        for middleware in middlewares:
            try:
                request = await middleware.process_request(request)
            except Exception as e:
                logger.error(f"请求中间件处理失败 {middleware.name}: {e}")
        
        return request
    
    async def apply_response_middleware(
        self, 
        result: DataResult, 
        request: DataRequest
    ) -> DataResult:
        """应用响应中间件"""
        if not self.config.middleware_enabled:
            return result
        
        middlewares = self.registry.get_middlewares("response")
        
        for middleware in middlewares:
            try:
                result = await middleware.process_response(result, request)
            except Exception as e:
                logger.error(f"响应中间件处理失败 {middleware.name}: {e}")
        
        return result
    
    def get_data_source_plugins(
        self, 
        data_type: Optional[DataType] = None
    ) -> List[DataSourcePlugin]:
        """获取数据源插件"""
        return self.registry.get_data_sources(data_type)
    
    def get_processor_plugins(
        self, 
        processing_type: Optional[str] = None
    ) -> List[DataProcessorPlugin]:
        """获取处理器插件"""
        return self.registry.get_processors(processing_type)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        active_count = sum(1 for p in self.loaded_plugins if p.enabled)
        
        return {
            **self.stats,
            'active_plugins': active_count,
            'plugin_types': {
                'data_sources': len(self.registry.data_sources),
                'processors': len(self.registry.processors),
                'request_middlewares': len(self.registry.middlewares['request']),
                'response_middlewares': len(self.registry.middlewares['response'])
            }
        }
    
    # 私有方法
    async def _auto_discover_plugins(self) -> None:
        """自动发现插件"""
        for plugin_dir in self.config.plugin_dirs:
            try:
                plugins = await self.loader.load_plugins_from_directory(
                    plugin_dir, 
                    self.registry
                )
                self.loaded_plugins.extend(plugins)
            except Exception as e:
                logger.error(f"自动发现插件失败 {plugin_dir}: {e}")
    
    async def _initialize_plugins(self) -> None:
        """初始化插件"""
        for plugin in self.loaded_plugins:
            try:
                await plugin.initialize()
                self.stats['loaded_plugins'] += 1
                if plugin.enabled:
                    self.stats['active_plugins'] += 1
            except Exception as e:
                logger.error(f"插件初始化失败 {plugin.name}: {e}")
                self.stats['failed_loads'] += 1