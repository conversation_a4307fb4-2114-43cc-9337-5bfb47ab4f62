"""
JSON序列化工具

提供自定义JSON编码器和序列化函数，用于处理特殊数据类型（如numpy、日期等）的序列化。
"""

import json
import numpy as np
import datetime
import decimal
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder

# 自定义JSON编码器，处理numpy和其他特殊类型
class CustomJSONEncoder(json.JSONEncoder):
    """
    自定义JSON编码器，处理以下特殊类型：
    - numpy整数和浮点数
    - numpy数组
    - decimal.Decimal类型
    - 日期和时间类型
    """
    def default(self, obj):
        # 处理numpy数值类型
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            # 处理NaN和无穷大
            if np.isnan(obj) or np.isinf(obj):
                return None
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        # 处理decimal类型
        elif isinstance(obj, decimal.Decimal):
            return float(obj)
        # 处理日期和时间类型
        elif isinstance(obj, (datetime.date, datetime.datetime)):
            return obj.isoformat()
        # 处理Python原生float中的NaN
        elif isinstance(obj, float) and (np.isnan(obj) or np.isinf(obj)):
            return None
        # 其他类型尝试转换为字符串
        try:
            return super().default(obj)
        except:
            return str(obj)  # 如果无法序列化，转为字符串

# 自定义FastAPI的JSONResponse，使用自定义编码器
class CustomJSONResponse(JSONResponse):
    """
    自定义FastAPI响应类，使用CustomJSONEncoder进行JSON序列化。
    用于处理响应中的特殊数据类型。
    """
    def render(self, content):
        # 预处理内容，将所有NaN值转换为None
        processed_content = self._process_nan_values(content)
        return json.dumps(
            processed_content,
            ensure_ascii=False,
            allow_nan=False,  # 禁止NaN，强制转换为null
            indent=None,
            separators=(",", ":"),
            cls=CustomJSONEncoder,
        ).encode("utf-8")
    
    def _process_nan_values(self, obj):
        """递归处理对象中的NaN值，将其转换为None"""
        if isinstance(obj, dict):
            return {k: self._process_nan_values(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._process_nan_values(item) for item in obj]
        elif isinstance(obj, float):
            if np.isnan(obj) or np.isinf(obj):
                return None
            return obj
        elif hasattr(obj, 'dtype') and np.issubdtype(obj.dtype, np.floating):
            if np.isnan(obj) or np.isinf(obj):
                return None
            return float(obj)
        else:
            return obj

# 重写FastAPI的jsonable_encoder函数，处理特殊类型
def custom_jsonable_encoder(obj, *args, **kwargs):
    """
    自定义jsonable_encoder函数，增强对特殊类型的处理能力。
    
    首先尝试处理numpy和其他特殊类型，如果失败则回退到原始的jsonable_encoder。
    如果原始encoder也失败，则使用自定义逻辑处理。
    
    Args:
        obj: 要序列化的对象
        *args, **kwargs: 传递给原始jsonable_encoder的参数
        
    Returns:
        序列化后的对象
    """
    # 预处理特殊类型
    if hasattr(obj, 'dtype') and hasattr(obj, 'item'):  # numpy标量类型
        try:
            return obj.item()
        except:
            pass
    
    # 使用原始编码器
    try:
        return jsonable_encoder(obj, *args, **kwargs)
    except Exception as e:
        # 如果原始编码器失败，使用自定义逻辑
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            # 处理NaN和无穷大
            if np.isnan(obj) or np.isinf(obj):
                return None
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, decimal.Decimal):
            return float(obj)
        elif isinstance(obj, (datetime.date, datetime.datetime)):
            return obj.isoformat()
        # 处理Python原生float中的NaN
        elif isinstance(obj, float) and (np.isnan(obj) or np.isinf(obj)):
            return None
        else:
            # 尝试转换为字符串
            return str(obj)

# 安装自定义编码器的函数
def install_custom_encoder():
    """
    安装自定义编码器，替换FastAPI默认的jsonable_encoder。
    在应用启动时调用此函数。
    """
    import fastapi.encoders
    original_jsonable_encoder = fastapi.encoders.jsonable_encoder
    fastapi.encoders.jsonable_encoder = custom_jsonable_encoder
    return original_jsonable_encoder