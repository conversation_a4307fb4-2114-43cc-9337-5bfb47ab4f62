"""定时任务相关数据模型"""

from sqlalchemy import Column, String, Integer, Boolean, DateTime, Text, ForeignKey, Float
from sqlalchemy.orm import relationship
from app.models.base import BaseModel

class UserScheduledTask(BaseModel):
    """用户定时任务模型"""
    __tablename__ = "user_scheduled_tasks"
    
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    name = Column(String(100), nullable=False, comment="任务名称")
    task_type = Column(String(50), nullable=False, default="indicator_scan", comment="任务类型")
    cron_expression = Column(String(100), nullable=False, comment="Cron表达式")
    is_active = Column(Boolean, nullable=False, default=True, comment="是否启用", index=True)
    max_executions = Column(Integer, nullable=True, comment="最大执行次数")
    current_executions = Column(Integer, nullable=False, default=0, comment="当前已执行次数")
    task_config = Column(Text, nullable=False, comment="任务配置JSON")
    description = Column(String(500), nullable=True, comment="任务描述")
    last_execution = Column(DateTime, nullable=True, comment="最后执行时间")
    next_execution = Column(DateTime, nullable=True, comment="下次执行时间", index=True)
    
    # 关联关系
    user = relationship("User", back_populates="scheduled_tasks")
    executions = relationship("TaskExecution", back_populates="scheduled_task", cascade="all, delete-orphan")

class TaskExecution(BaseModel):
    """任务执行记录模型"""
    __tablename__ = "task_executions"
    
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    scheduled_task_id = Column(Integer, ForeignKey("user_scheduled_tasks.id"), nullable=True, index=True)
    trigger_type = Column(String(20), nullable=False, comment="触发类型", index=True)  # scheduled/manual
    task_type = Column(String(50), nullable=False, default="indicator_scan", comment="任务类型")
    task_config = Column(Text, nullable=False, comment="任务配置JSON")
    status = Column(String(20), nullable=False, default="pending", comment="执行状态", index=True)
    start_time = Column(DateTime, nullable=True, comment="开始时间", index=True)
    end_time = Column(DateTime, nullable=True, comment="结束时间")
    duration_seconds = Column(Integer, nullable=True, comment="执行时长")
    results_count = Column(Integer, nullable=False, default=0, comment="结果数量")
    
    # 进度跟踪字段
    progress_current = Column(Integer, nullable=False, default=0, comment="当前进度")
    progress_total = Column(Integer, nullable=False, default=0, comment="总进度")
    progress_percentage = Column(Float, nullable=False, default=0.0, comment="进度百分比")
    progress_message = Column(String(200), nullable=True, comment="进度消息")
    
    error_message = Column(Text, nullable=True, comment="错误信息")
    results_data = Column(Text, nullable=True, comment="执行结果JSON")
    task_metadata = Column(Text, nullable=True, comment="扩展元数据JSON")
    
    # 关联关系
    user = relationship("User", back_populates="task_executions")
    scheduled_task = relationship("UserScheduledTask", back_populates="executions")