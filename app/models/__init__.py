"""
模型包初始化文件

导入所有模型类，方便其他模块访问。
"""

from app.models.base import Base, BaseModel
from app.models.stock import (
    StockInfo, 
    StockDaily, 
    StockIndicator,
    IndicatorVersion
)
from app.models.index import (
    IndexInfo,
    IndexDaily,
    IndexWeekly,
    IndexMonthly
)
from app.models.user import User
from app.models.watchlist import WatchlistItem
from app.models.task import UserScheduledTask, TaskExecution

# 导出所有模型类，方便其他模块使用
__all__ = [
    'Base', 
    'BaseModel',
    'StockInfo', 
    'StockDaily', 
    'StockIndicator',
    'IndicatorVersion',
    'IndexInfo',
    'IndexDaily',
    'IndexWeekly',
    'IndexMonthly',
    'User',
    'WatchlistItem',
    'UserScheduledTask',
    'TaskExecution'
]
