"""
股票模型模块

定义与股票数据相关的数据库模型。
"""

from sqlalchemy import (
    Column, String, Float, Date, ForeignKey, 
    UniqueConstraint, Index, BigInteger, JSON,
    Boolean
)
from sqlalchemy.orm import relationship, declared_attr
from typing import Optional, List, Dict
from datetime import datetime

from app.models.base import BaseModel

class StockInfo(BaseModel):
    """股票基本信息模型"""
    
    __tablename__ = "stock_info"
    
    # 股票代码（如：601398）
    code = Column(String(10), index=True, nullable=False, unique=True)
    
    # 股票名称（如：工商银行）
    name = Column(String(100), nullable=False)
    
    # 交易所代码（如：SH, SZ）
    exchange = Column(String(10), nullable=False)
    
    # 完整股票代码（如：SH601398）
    full_code = Column(String(20), index=True, nullable=False, unique=True)
    
    # 行业分类
    industry = Column(String(50), nullable=True)
    
    # 股票所属板块
    sector = Column(String(50), nullable=True)
    
    # 上市日期
    listing_date = Column(Date, nullable=True)
    
    # 总股本（股）
    total_shares = Column(BigInteger, nullable=True)
    
    # 流通股本（股）
    circulating_shares = Column(BigInteger, nullable=True)
    
    # 公司简介
    company_profile = Column(String(2000), nullable=True)
    
    # 是否在列表中显示
    is_active = Column(Boolean, default=True, nullable=False)
    
    # 市值（元）
    market_cap = Column(BigInteger, nullable=True)
    
    def __repr__(self) -> str:
        return f"<StockInfo(code='{self.code}', name='{self.name}', exchange='{self.exchange}')>"

class StockDaily(BaseModel):
    """股票日线数据模型（统一表）"""
    
    __tablename__ = "stock_daily"
    
    # 关联的股票代码（外键）
    stock_code = Column(String(10), ForeignKey('stock_info.code'), index=True, nullable=False)
    
    # 交易日期
    trade_date = Column(Date, nullable=False)
    
    # 开盘价
    open = Column(Float, nullable=False)
    
    # 最高价
    high = Column(Float, nullable=False)
    
    # 最低价
    low = Column(Float, nullable=False)
    
    # 收盘价
    close = Column(Float, nullable=False)
    
    # 交易量（股）
    volume = Column(BigInteger, nullable=False)
    
    # 交易额（元）
    amount = Column(BigInteger, nullable=True)
    
    # 涨跌幅（%）
    change_pct = Column(Float, nullable=True)
    
    # 换手率（%）
    turnover_rate = Column(Float, nullable=True)
    
    # 涨跌停状态（U:涨停, D:跌停, N:正常）
    limit_status = Column(String(1), nullable=True)
    
    # 是否ST股
    is_st = Column(Boolean, default=False, nullable=False)
    
    # 添加优化的索引和约束
    __table_args__ = (
        # 唯一约束：防止同一股票同一日期的重复数据
        UniqueConstraint('stock_code', 'trade_date', name='uix_stock_daily_code_date'),
        # 主联合索引：优化按股票代码和时间范围查询
        Index('ix_stock_daily_code_date', 'stock_code', 'trade_date'),
        # 辅助索引：优化按日期查询全市场数据
        Index('ix_stock_daily_date', 'trade_date'),
    )
    
    def __repr__(self) -> str:
        return f"<StockDaily(stock_code='{self.stock_code}', trade_date='{self.trade_date}', close={self.close})>"

class IndicatorVersion(BaseModel):
    """指标版本控制模型"""
    
    __tablename__ = "indicator_version"
    
    # 版本哈希值
    version_hash = Column(String(64), unique=True, nullable=False)
    
    # 指标类型（如：MACD, KDJ, RSI等）
    indicator_type = Column(String(20), nullable=False)
    
    # 计算公式
    formula = Column(String(2000), nullable=False)
    
    # 参数配置（JSON格式）
    parameters = Column(JSON, nullable=False)
    
    # 生效日期
    effective_date = Column(Date, nullable=False)
    
    # 是否为当前版本
    is_current = Column(Boolean, nullable=False, server_default='1')
    
    # 版本说明
    description = Column(String(500), nullable=True)
    
    __table_args__ = (
        Index('ix_indicator_version_type_current', 'indicator_type', 'is_current'),
    )
    
    def __repr__(self) -> str:
        return f"<IndicatorVersion(type='{self.indicator_type}', hash='{self.version_hash}')>"

class StockWeekly(BaseModel):
    """股票周线数据模型"""
    
    __tablename__ = "stock_weekly"
    
    # 关联的股票代码（外键）
    stock_code = Column(String(10), ForeignKey('stock_info.code'), index=True, nullable=False)
    
    # 交易日期（周的最后一个交易日）
    trade_date = Column(Date, nullable=False)
    
    # 开盘价
    open = Column(Float, nullable=False)
    
    # 最高价
    high = Column(Float, nullable=False)
    
    # 最低价
    low = Column(Float, nullable=False)
    
    # 收盘价
    close = Column(Float, nullable=False)
    
    # 交易量（股）
    volume = Column(BigInteger, nullable=False)
    
    # 交易额（元）
    amount = Column(BigInteger, nullable=True)
    
    # 涨跌幅（%）
    change_pct = Column(Float, nullable=True)
    
    # 换手率（%）
    turnover_rate = Column(Float, nullable=True)
    
    # 涨跌停状态（U:涨停, D:跌停, N:正常）
    limit_status = Column(String(1), nullable=True)
    
    # 是否ST股
    is_st = Column(Boolean, default=False, nullable=False)
    
    # 添加优化的索引和约束
    __table_args__ = (
        # 唯一约束：防止同一股票同一周期的重复数据
        UniqueConstraint('stock_code', 'trade_date', name='uix_stock_weekly_code_date'),
        # 主联合索引：优化按股票代码和时间范围查询
        Index('ix_stock_weekly_code_date', 'stock_code', 'trade_date'),
        # 辅助索引：优化按日期查询全市场数据
        Index('ix_stock_weekly_date', 'trade_date'),
    )
    
    def __repr__(self) -> str:
        return f"<StockWeekly(stock_code='{self.stock_code}', trade_date='{self.trade_date}', close={self.close})>"


class StockMonthly(BaseModel):
    """股票月线数据模型"""
    
    __tablename__ = "stock_monthly"
    
    # 关联的股票代码（外键）
    stock_code = Column(String(10), ForeignKey('stock_info.code'), index=True, nullable=False)
    
    # 交易日期（月的最后一个交易日）
    trade_date = Column(Date, nullable=False)
    
    # 开盘价
    open = Column(Float, nullable=False)
    
    # 最高价
    high = Column(Float, nullable=False)
    
    # 最低价
    low = Column(Float, nullable=False)
    
    # 收盘价
    close = Column(Float, nullable=False)
    
    # 交易量（股）
    volume = Column(BigInteger, nullable=False)
    
    # 交易额（元）
    amount = Column(BigInteger, nullable=True)
    
    # 涨跌幅（%）
    change_pct = Column(Float, nullable=True)
    
    # 换手率（%）
    turnover_rate = Column(Float, nullable=True)
    
    # 涨跌停状态（U:涨停, D:跌停, N:正常）
    limit_status = Column(String(1), nullable=True)
    
    # 是否ST股
    is_st = Column(Boolean, default=False, nullable=False)
    
    # 添加优化的索引和约束
    __table_args__ = (
        # 唯一约束：防止同一股票同一月期的重复数据
        UniqueConstraint('stock_code', 'trade_date', name='uix_stock_monthly_code_date'),
        # 主联合索引：优化按股票代码和时间范围查询
        Index('ix_stock_monthly_code_date', 'stock_code', 'trade_date'),
        # 辅助索引：优化按日期查询全市场数据
        Index('ix_stock_monthly_date', 'trade_date'),
    )
    
    def __repr__(self) -> str:
        return f"<StockMonthly(stock_code='{self.stock_code}', trade_date='{self.trade_date}', close={self.close})>"


class StockIndicator(BaseModel):
    """股票技术指标数据模型"""
    
    __tablename__ = "stock_indicator"
    
    # 关联的股票代码（外键）
    stock_code = Column(String(10), ForeignKey('stock_info.code'), index=True, nullable=False)
    
    # 交易日期
    trade_date = Column(Date, nullable=False)
    
    # 指标类型（如：MACD, KDJ, RSI等）
    indicator_type = Column(String(20), nullable=False)
    
    # 指标版本（关联版本控制表）
    version_hash = Column(String(64), ForeignKey('indicator_version.version_hash'), nullable=False)
    
    # 指标值（JSON格式，支持多周期）
    values = Column(JSON, nullable=False)
    
    # 数据频率（D1:日线, W1:周线, M1:月线）
    data_frequency = Column(String(2), nullable=False)
    
    # 关联到版本信息
    version = relationship("IndicatorVersion")
    
    # 复合唯一索引
    __table_args__ = (
        UniqueConstraint(
            'stock_code', 'trade_date', 'indicator_type', 'version_hash',
            name='uix_stock_indicator_code_date_type_version'
        ),
        Index('ix_stock_indicator_code_date_type', 'stock_code', 'trade_date', 'indicator_type'),
    )
    
    def __repr__(self) -> str:
        return f"<StockIndicator(stock_code='{self.stock_code}', trade_date='{self.trade_date}', type='{self.indicator_type}')>"
