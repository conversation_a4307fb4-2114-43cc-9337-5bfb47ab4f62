"""
指数模型模块

定义与指数数据相关的数据库模型。
"""

from sqlalchemy import (
    Column, String, Float, Date, ForeignKey, 
    UniqueConstraint, Index, BigInteger, JSON,
    Boolean
)
from sqlalchemy.orm import relationship, declared_attr
from typing import Optional, List, Dict
from datetime import datetime

from app.models.base import BaseModel

class IndexInfo(BaseModel):
    """指数基本信息模型"""
    
    __tablename__ = "index_info"
    
    # 指数代码（如：000001.SH）
    code = Column(String(20), index=True, nullable=False, unique=True)
    
    # 指数名称（如：上证指数）
    name = Column(String(100), nullable=False)
    
    # 交易所代码（如：SH, SZ）
    exchange = Column(String(10), nullable=False)
    
    # 是否在列表中显示
    is_active = Column(Boolean, default=True, nullable=False)
    
    def __repr__(self) -> str:
        return f"<IndexInfo(code='{self.code}', name='{self.name}', exchange='{self.exchange}')>"

class IndexDaily(BaseModel):
    """指数日线数据模型"""
    
    __tablename__ = "index_daily"
    
    # 关联的指数代码（外键）
    index_code = Column(String(20), ForeignKey('index_info.code'), index=True, nullable=False)
    
    # 交易日期
    trade_date = Column(Date, nullable=False)
    
    # 开盘价
    open = Column(Float, nullable=False)
    
    # 最高价
    high = Column(Float, nullable=False)
    
    # 最低价
    low = Column(Float, nullable=False)
    
    # 收盘价
    close = Column(Float, nullable=False)
    
    # 交易量
    volume = Column(BigInteger, nullable=False)
    
    # 交易额（元）
    amount = Column(BigInteger, nullable=True)
    
    # 前收盘价
    prev_close = Column(Float, nullable=True)
    
    # 涨跌幅（%）
    change_pct = Column(Float, nullable=True)
    
    # 添加优化的索引和约束
    __table_args__ = (
        # 唯一约束：防止同一指数同一日期的重复数据
        UniqueConstraint('index_code', 'trade_date', name='uix_index_daily_code_date'),
        # 主联合索引：优化按指数代码和时间范围查询
        Index('ix_index_daily_code_date', 'index_code', 'trade_date'),
        # 辅助索引：优化按日期查询全市场数据
        Index('ix_index_daily_date', 'trade_date'),
    )
    
    def __repr__(self) -> str:
        return f"<IndexDaily(index_code='{self.index_code}', trade_date='{self.trade_date}', close={self.close})>"

class IndexWeekly(BaseModel):
    """指数周线数据模型"""
    
    __tablename__ = "index_weekly"
    
    # 关联的指数代码（外键）
    index_code = Column(String(20), ForeignKey('index_info.code'), index=True, nullable=False)
    
    # 交易日期（周的最后一个交易日）
    trade_date = Column(Date, nullable=False)
    
    # 开盘价
    open = Column(Float, nullable=False)
    
    # 最高价
    high = Column(Float, nullable=False)
    
    # 最低价
    low = Column(Float, nullable=False)
    
    # 收盘价
    close = Column(Float, nullable=False)
    
    # 交易量
    volume = Column(BigInteger, nullable=False)
    
    # 交易额（元）
    amount = Column(BigInteger, nullable=True)
    
    # 前收盘价
    prev_close = Column(Float, nullable=True)
    
    # 涨跌幅（%）
    change_pct = Column(Float, nullable=True)
    
    # 添加优化的索引和约束
    __table_args__ = (
        # 唯一约束：防止同一指数同一周期的重复数据
        UniqueConstraint('index_code', 'trade_date', name='uix_index_weekly_code_date'),
        # 主联合索引：优化按指数代码和时间范围查询
        Index('ix_index_weekly_code_date', 'index_code', 'trade_date'),
        # 辅助索引：优化按日期查询全市场数据
        Index('ix_index_weekly_date', 'trade_date'),
    )
    
    def __repr__(self) -> str:
        return f"<IndexWeekly(index_code='{self.index_code}', trade_date='{self.trade_date}', close={self.close})>"

class IndexMonthly(BaseModel):
    """指数月线数据模型"""
    
    __tablename__ = "index_monthly"
    
    # 关联的指数代码（外键）
    index_code = Column(String(20), ForeignKey('index_info.code'), index=True, nullable=False)
    
    # 交易日期（月的最后一个交易日）
    trade_date = Column(Date, nullable=False)
    
    # 开盘价
    open = Column(Float, nullable=False)
    
    # 最高价
    high = Column(Float, nullable=False)
    
    # 最低价
    low = Column(Float, nullable=False)
    
    # 收盘价
    close = Column(Float, nullable=False)
    
    # 交易量
    volume = Column(BigInteger, nullable=False)
    
    # 交易额（元）
    amount = Column(BigInteger, nullable=True)
    
    # 前收盘价
    prev_close = Column(Float, nullable=True)
    
    # 涨跌幅（%）
    change_pct = Column(Float, nullable=True)
    
    # 添加优化的索引和约束
    __table_args__ = (
        # 唯一约束：防止同一指数同一月期的重复数据
        UniqueConstraint('index_code', 'trade_date', name='uix_index_monthly_code_date'),
        # 主联合索引：优化按指数代码和时间范围查询
        Index('ix_index_monthly_code_date', 'index_code', 'trade_date'),
        # 辅助索引：优化按日期查询全市场数据
        Index('ix_index_monthly_date', 'trade_date'),
    )
    
    def __repr__(self) -> str:
        return f"<IndexMonthly(index_code='{self.index_code}', trade_date='{self.trade_date}', close={self.close})>"