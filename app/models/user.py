"""
用户模型模块

定义用户相关的数据模型。
"""

from sqlalchemy import Column, String, Integer, Boolean, DateTime
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from app.models.base import BaseModel as Base

class User(Base):
    """用户模型"""
    __tablename__ = "users"
    
    username = Column(String(50), unique=True, index=True, nullable=False, comment="用户名")
    password_hash = Column(String(128), nullable=False, comment="密码哈希")
    email = Column(String(100), unique=True, index=True, nullable=True, comment="邮箱")
    is_active = Column(Boolean(), default=True, comment="是否激活")
    is_admin = Column(Boolean(), default=False, comment="是否是管理员")
    last_login = Column(DateTime, nullable=True, comment="最后登录时间")
    token_version = Column(Integer, default=0, comment="JWT token版本号，用于强制过期")
    
    # 关系: 一个用户可以有多个自选股项目（延迟导入，避免循环引用）
    watchlist_items = relationship("WatchlistItem", back_populates="user", cascade="all, delete-orphan", lazy="select")
    
    # 关系: 一个用户可以有多个定时任务
    scheduled_tasks = relationship("UserScheduledTask", back_populates="user", cascade="all, delete-orphan")
    task_executions = relationship("TaskExecution", back_populates="user", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<User(username='{self.username}', email='{self.email}')>"
