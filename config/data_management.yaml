# 统一数据管理系统配置文件
# 支持所有数据管理策略和插件配置

# 基本配置
data_management:
  enabled: true
  debug: false
  version: "1.0.0"
  
# 缓存配置
cache:
  enabled: true
  strategy: "cache_first"  # cache_first, api_first, cache_through, cache_aside, write_behind, write_through
  
  # L1内存缓存配置
  l1_memory:
    enabled: true
    max_size: 500
    ttl: 300  # 5分钟
    
  # L2 Redis缓存配置  
  l2_redis:
    enabled: true
    ttl: 3600  # 1小时
    key_prefix: "unified_data"
    compression: true
    serialization: "json"  # json, pickle, msgpack
    
  # L3数据库缓存配置
  l3_database:
    enabled: true
    ttl: 86400  # 24小时
    
  # 缓存预热配置
  warmup:
    enabled: true
    on_startup: false
    cron_schedule: "0 15 * * 1-5"  # 工作日下午3点
    batch_size: 50
    concurrent_requests: 3
    data_types:
      - "stock_info"
      - "stock_daily"
    warmup_days: 365

# 路由配置
routing:
  strategy: "fastest"  # fastest, reliable, cost, balanced
  timeout: 30.0
  max_retries: 3
  retry_delay: 1.0
  
  # 数据源优先级配置
  source_priorities:
    default_mairui: 100
    default_tushare: 90
    default_akshare: 80
    database: 70
    
  # 负载均衡配置
  load_balance:
    enabled: false
    algorithm: "round_robin"  # round_robin, weighted_round_robin, least_connections
    
  # 健康检查配置
  health_check:
    enabled: true
    interval: 60  # 秒
    timeout: 10   # 秒
    failure_threshold: 3
    recovery_threshold: 2

# 插件配置
plugins:
  enabled: true
  auto_discovery: true
  plugin_directories:
    - "app/core/data/sources"
    - "app/core/data/processors"
    - "app/core/data/middleware"
    
  # 插件优先级
  priorities:
    stock_data_validator: 100
    data_transformer: 90
    metrics_collector: 80
    
  # 中间件配置
  middleware:
    enabled: true
    chain:
      - "request_logger"
      - "data_validator" 
      - "metrics_collector"
      - "error_handler"

# 数据类型特定配置
data_types:
  stock_info:
    cache_ttl: 3600    # 1小时
    max_age: 86400     # 24小时
    validation: true
    
  stock_daily:
    cache_ttl: 1800    # 30分钟
    max_age: 7200      # 2小时
    validation: true
    compression: true
    
  stock_realtime:
    cache_ttl: 30      # 30秒
    max_age: 300       # 5分钟
    validation: false
    priority: "high"
    
  stock_indicators:
    cache_ttl: 7200    # 2小时
    max_age: 86400     # 24小时
    validation: true
    
  index_components:
    cache_ttl: 86400   # 24小时
    max_age: 259200    # 3天
    validation: true
    
  financial_data:
    cache_ttl: 43200   # 12小时
    max_age: 86400     # 24小时
    validation: true

# 性能配置
performance:
  max_concurrent_requests: 100
  request_queue_size: 1000
  worker_threads: 5
  connection_pool_size: 20
  
  # 限流配置
  rate_limiting:
    enabled: false
    requests_per_minute: 1000
    burst_size: 100
    
  # 超时配置
  timeouts:
    api_request: 30.0
    database_query: 15.0
    cache_operation: 5.0

# 监控配置
monitoring:
  enabled: true
  
  # 指标收集
  metrics:
    enabled: true
    collection_interval: 60  # 秒
    retention_days: 30
    
  # 告警配置
  alerts:
    enabled: true
    
    # 错误率告警
    error_rate:
      threshold: 5.0  # 百分比
      window: 300     # 5分钟窗口
      
    # 响应时间告警
    response_time:
      threshold: 5.0  # 秒
      window: 300     # 5分钟窗口
      
    # 缓存命中率告警
    cache_hit_rate:
      threshold: 70.0  # 百分比
      window: 300      # 5分钟窗口

# 日志配置
logging:
  enabled: true
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  
  # 日志文件配置
  file:
    enabled: true
    path: "logs/data_management.log"
    max_size: "100MB"
    backup_count: 5
    
  # 结构化日志
  structured:
    enabled: true
    format: "json"
    
  # 日志过滤
  filters:
    - "request_id"
    - "data_type" 
    - "cache_hit"
    - "response_time"

# 事件配置
events:
  enabled: true
  max_queue_size: 10000
  max_workers: 5
  
  # 事件持久化
  persistence:
    enabled: false
    storage: "redis"  # redis, database, file
    retention_days: 7
    
  # 事件类型配置
  event_types:
    request_started:
      enabled: true
      priority: "normal"
      
    request_completed:
      enabled: true
      priority: "normal"
      
    cache_hit:
      enabled: true
      priority: "low"
      
    cache_miss:
      enabled: true
      priority: "low"
      
    error_occurred:
      enabled: true
      priority: "high"

# 安全配置
security:
  # API访问控制
  access_control:
    enabled: false
    require_authentication: false
    
  # 数据加密
  encryption:
    enabled: false
    algorithm: "AES-256-GCM"
    
  # 敏感数据脱敏
  data_masking:
    enabled: false
    fields:
      - "token"
      - "password"
      - "secret"

# 开发和调试配置
development:
  # 模拟延迟
  simulate_latency:
    enabled: false
    min_delay: 0.1
    max_delay: 1.0
    
  # 模拟错误
  simulate_errors:
    enabled: false
    error_rate: 0.01  # 1%错误率
    
  # 数据Mock
  mock_data:
    enabled: false
    providers:
      - "stock_info_mock"
      - "stock_daily_mock"

# 环境特定配置
environments:
  development:
    cache:
      l1_memory:
        max_size: 100
      l2_redis:
        ttl: 300
    monitoring:
      enabled: false
    logging:
      level: "DEBUG"
      
  testing:
    cache:
      enabled: false
    events:
      max_workers: 1
    performance:
      max_concurrent_requests: 10
      
  production:
    cache:
      l1_memory:
        max_size: 1000
      l2_redis:
        ttl: 7200
    monitoring:
      enabled: true
    logging:
      level: "INFO"
      structured:
        enabled: true

# 实验性功能配置
experimental:
  # 预测性缓存
  predictive_caching:
    enabled: false
    algorithm: "lru_prediction"
    
  # 智能预取
  smart_prefetch:
    enabled: false
    lookahead_window: 3600  # 1小时
    
  # 自适应缓存TTL
  adaptive_ttl:
    enabled: false
    min_ttl: 60
    max_ttl: 86400
    
  # 分布式缓存
  distributed_cache:
    enabled: false
    nodes:
      - "redis://node1:6379"
      - "redis://node2:6379"
      - "redis://node3:6379"