# 股票量化分析系统

一个高性能的股票量化分析系统，支持多数据源获取、技术指标计算、用户认证管理和现代化Web界面。

## 功能特点

- **多数据源支持** - Tushare、Akshare、Mairui API等多种数据源
- **技术指标计算** - MACD、KDJ、RSI、ARBR等经典技术指标
- **多周期分析** - 支持日线、周线、月线数据分析
- **用户认证系统** - 基于JWT的用户登录、权限管理
- **角色权限控制** - 管理员和普通用户角色区分
- **现代化前端** - Vue 3 + Element Plus响应式界面
- **RESTful API** - 完整的API文档和访问接口
- **自动数据更新** - 定时任务自动更新股票数据
- **高性能计算** - 优化的数据存储和计算引擎

## 技术栈

### 后端
- **框架**: FastAPI
- **数据库**: SQLite (可扩展到PostgreSQL/MySQL)
- **ORM**: SQLAlchemy
- **认证**: JWT Token
- **数据迁移**: Alembic
- **包管理**: uv (推荐) / pip

### 前端  
- **框架**: Vue 3 + Composition API
- **构建工具**: Vite
- **UI组件**: Element Plus
- **状态管理**: Pinia
- **图标**: Carbon Icons (Iconify)
- **样式**: UnoCSS + SCSS
- **图表**: ECharts

## 项目结构

```
quantization/
├── alembic/                    # 数据库迁移
├── app/                        # 后端应用
│   ├── api/                    # API层
│   │   ├── endpoints/          # API端点
│   │   │   ├── auth.py         # 用户认证API
│   │   │   ├── stocks/         # 股票数据API
│   │   │   └── users.py        # 用户管理API
│   ├── core/                   # 核心配置
│   │   ├── config.py           # 应用配置
│   │   └── database.py         # 数据库配置
│   ├── models/                 # 数据库模型
│   │   ├── user.py             # 用户模型
│   │   └── stock.py            # 股票模型
│   ├── schemas/                # Pydantic模式
│   │   ├── auth.py             # 认证相关模式
│   │   └── stock.py            # 股票数据模式
│   ├── services/               # 业务服务
│   │   ├── data_fetcher/       # 数据获取服务
│   │   ├── indicators/         # 技术指标计算
│   │   ├── storage/            # 数据存储服务
│   │   └── user_service.py     # 用户服务
│   ├── utils/                  # 工具函数
│   │   ├── jwt_auth.py         # JWT认证工具
│   │   └── dependencies.py     # API依赖注入
│   └── main.py                 # 应用入口
├── frontend-app/               # 前端应用
│   ├── src/
│   │   ├── components/         # Vue组件
│   │   │   ├── common/         # 通用组件
│   │   │   └── layout/         # 布局组件
│   │   ├── pages/              # 页面组件
│   │   │   ├── Login/          # 登录页面
│   │   │   ├── UserManagement/ # 用户管理
│   │   │   ├── DataManagement/ # 数据管理
│   │   │   └── Analysis/       # 股票分析
│   │   ├── services/           # API服务
│   │   │   └── api/            # API客户端
│   │   ├── store/              # Pinia状态管理
│   │   │   └── modules/        # 状态模块
│   │   └── router/             # 路由配置
│   ├── public/                 # 静态资源
│   ├── package.json            # 前端依赖
│   └── vite.config.js          # Vite配置
├── tests/                      # 测试代码
├── migrations/                 # 数据库迁移文件
├── .env                        # 环境变量配置
├── pyproject.toml             # Python项目配置
└── README.md                  # 项目说明
```

## 快速开始

### 环境要求
- Python 3.8+
- Node.js 16+ 
- uv (推荐) 或 pip

### 安装步骤

1. **克隆仓库**:
```bash
git clone https://github.com/yourusername/quantization.git
cd quantization
```

2. **后端安装**:
```bash
# 使用 uv 同步依赖（推荐）
uv sync

# 或使用传统的 pip 方式
python -m venv venv
source venv/bin/activate  # Linux/Mac 或 venv\Scripts\activate (Windows)
pip install -r requirements.txt --pre
```

3. **前端安装**:
```bash
cd frontend-app
npm install
# 或
yarn install
```

4. **配置环境变量**:
```bash
cp .env.example .env
# 编辑 .env 文件，设置必要的配置参数：
# - DATABASE_URL: 数据库连接地址
# - JWT_SECRET_KEY: JWT密钥
# - TUSHARE_TOKEN: Tushare API令牌
# - MAIRUI_TOKEN: Mairui API令牌
```

## 使用方法

### 1. 数据库初始化
```bash
# 执行数据库迁移
alembic upgrade head

# 创建管理员用户（可选）
python scripts/create_admin.py
```

### 2. 启动服务

**方式一：同时启动前后端（推荐）**
```bash
python run_dev.py
```

**方式二：分别启动**
```bash
# 启动后端API服务
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 启动前端开发服务器
cd frontend-app
npm run dev
```

### 3. 访问应用
- **前端界面**: http://localhost:5173
- **API文档**: http://localhost:8000/docs  
- **管理后台**: http://localhost:5173/user-management (需要管理员权限)

### 4. 默认用户
如果创建了管理员用户，可以使用以下凭据登录：
- 用户名: admin
- 密码: admin123 (请在生产环境中修改)

## 用户认证系统

### 特性
- **JWT认证**: 基于JSON Web Token的无状态认证
- **角色权限**: 管理员和普通用户角色区分
- **记住登录**: 支持长期登录状态保持
- **多设备**: 支持多设备同时登录
- **自动刷新**: Token自动刷新机制

### API端点
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/logout` - 用户登出
- `POST /api/v1/auth/refresh` - 刷新Token
- `GET /api/v1/auth/me` - 获取当前用户信息
- `GET /api/v1/users` - 用户列表（管理员）
- `POST /api/v1/users` - 创建用户（管理员）

## 数据库管理

项目使用Alembic进行数据库版本控制，主要功能：

1. 数据库结构版本管理
- 支持自动生成迁移脚本
- 提供版本间迁移能力
- 支持迁移回滚操作

2. 迁移脚本位置
```
migrations/
├── versions/          # 迁移脚本目录
├── env.py            # 迁移环境配置
└── script.py.mako    # 迁移脚本模板
```

3. 常用操作
- 创建新迁移：当模型变更后，使用`alembic revision --autogenerate`生成迁移脚本
- 检查当前状态：使用`alembic current`查看当前数据库版本
- 版本管理：使用`alembic upgrade`和`alembic downgrade`在版本间切换

4. 注意事项
- 生产环境执行迁移前应先备份数据库
- 建议在测试环境验证迁移脚本
- 定期清理旧的迁移脚本

## Docker 构建与运行

本项目已内置 Dockerfile，可直接构建和运行 Docker 镜像。

### 构建镜像
```bash
docker build -t quantization:latest .
```

### 运行容器
```bash
docker run -d -p 8000:8000 --env-file .env --name quantization quantization:latest
```
- `--env-file .env` 可选，推荐用于加载环境变量。
- 访问接口文档：http://localhost:8000/docs

### 常见问题
- 如需自定义依赖或启动命令，请修改 Dockerfile。
- 若依赖私有源或特殊包，可在 Dockerfile 中添加相关 pip 配置。

## 开发指南

### 后端开发

#### 环境准备
```bash
# 创建并激活虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装开发依赖
pip install -e ".[dev]"
pip install -r requirements-test.txt

# 安装pre-commit hooks
pre-commit install
```

#### 代码质量
```bash
# 代码格式化
black .                    # 格式化代码
isort .                    # 排序导入
flake8                     # 代码检查
mypy app                   # 类型检查
```

#### 数据库操作
```bash
# 查看迁移历史
alembic history

# 生成新迁移（模型变更后）
alembic revision --autogenerate -m "迁移说明"

# 执行迁移
alembic upgrade head

# 回滚迁移
alembic downgrade -1      # 回滚一个版本
alembic downgrade base    # 回滚到初始状态
```

### 前端开发

#### 环境准备
```bash
cd frontend-app

# 安装依赖
npm install
# 或
yarn install

# 开发服务器
npm run dev
# 或
yarn dev
```

#### 项目结构
```
frontend-app/src/
├── components/          # 组件目录
│   ├── common/         # 通用组件 (IconButton, CommonTable等)
│   └── layout/         # 布局组件 (Sidebar, Navbar)
├── pages/              # 页面组件
├── services/           # API服务层
├── store/              # 状态管理
├── router/             # 路由配置
└── assets/             # 静态资源
```

#### 开发规范
- **组件命名**: 使用PascalCase (如 `UserManagement.vue`)
- **文件命名**: 使用kebab-case (如 `user-management.js`)  
- **图标库**: 使用Carbon图标集 (`<Icon name="user" />`)
- **状态管理**: 使用Pinia进行状态管理
- **API调用**: 统一通过`services/api`目录下的模块

#### 构建部署
```bash
# 构建生产版本
npm run build
# 或  
yarn build

# 预览构建结果
npm run preview
# 或
yarn preview
```

## 测试

### 后端测试

项目使用pytest进行测试，包含单元测试和集成测试：

```
tests/
├── unit/                      # 单元测试
│   └── services/
│       └── data_fetcher/     # 数据获取服务单元测试
├── integration/              # 集成测试
│   └── services/
│       └── data_fetcher/    # 数据获取服务集成测试
└── conftest.py             # 全局测试配置
```

#### 运行测试
```bash
# 运行所有测试
pytest

# 只运行单元测试
pytest tests/unit -v

# 只运行集成测试
pytest tests/integration -v

# 运行特定测试文件
pytest tests/unit/services/data_fetcher/test_adapter.py -v

# 排除耗时测试
pytest -m "not slow"
```

#### 测试覆盖率
```bash
# 生成HTML报告
pytest --cov=app --cov-report=html

# 生成简要控制台报告
pytest --cov=app --cov-report=term-missing
```

#### 集成测试配置
运行集成测试需要配置环境变量：
```bash
# Linux/Mac
export TUSHARE_TOKEN="your_token_here"

# Windows PowerShell
$env:TUSHARE_TOKEN="your_token_here"
```

### 前端测试
```bash
cd frontend-app

# 运行单元测试
npm run test

# 运行E2E测试
npm run test:e2e
```

## 部署

### 生产环境部署

#### 使用Docker
```bash
# 构建镜像
docker build -t quantization:latest .

# 运行容器
docker run -d -p 8000:8000 --env-file .env --name quantization quantization:latest
```

#### 手动部署
```bash
# 后端
uvicorn app.main:app --host 0.0.0.0 --port 8000

# 前端构建
cd frontend-app
npm run build
# 将dist目录部署到Web服务器
```

### 环境变量配置

生产环境需要配置以下关键环境变量：

```bash
# 数据库配置
DATABASE_URL=postgresql://user:password@localhost/dbname

# JWT配置
JWT_SECRET_KEY=your-super-secret-jwt-key
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_HOURS=24

# API令牌
TUSHARE_TOKEN=your_tushare_token
MAIRUI_TOKEN=your_mairui_token

# 服务配置
API_HOST=0.0.0.0
API_PORT=8000
LOG_LEVEL=INFO
```

## SQLite查询示例

### 使用Linux命令行sqlite3工具

连接数据库并执行查询：

```bash
# 连接到数据库
sqlite3 app.db

# 查询stock_daily表中stock_code=300013的最新30条记录
sqlite> SELECT * FROM stock_daily WHERE stock_code = '300013' ORDER BY updated_at DESC LIMIT 30;

# 或者直接在命令行执行
sqlite3 app.db "SELECT * FROM stock_daily WHERE stock_code = '300013' ORDER BY updated_at DESC LIMIT 30;"
```

### 常用SQLite查询命令

```sql
-- 查看所有表
.tables

-- 查看表结构
.schema stock_daily

-- 格式化输出
.mode column
.headers on

-- 退出sqlite3
.quit
```

## 许可证

本项目基于 MIT 许可证开源 - 详见 [LICENSE](LICENSE) 文件

## 支持

如果您在使用过程中遇到问题，请通过以下方式获取帮助：

- 创建 [GitHub Issue](https://github.com/yourusername/quantization/issues)
- 查看 [API文档](http://localhost:8000/docs)  
- 阅读项目 [Wiki](https://github.com/yourusername/quantization/wiki)
