#!/usr/bin/env python3
"""
JWT认证系统检查脚本

检查JWT认证系统是否正确配置
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import Session
from app.core.sync_database import sync_engine
from app.core.config import settings
from app.models.user import User
import jwt


def check_jwt_config():
    """检查JWT配置"""
    print("🔍 检查JWT配置...")
    
    checks = [
        ("JWT密钥", settings.JWT_SECRET_KEY != "your-super-secret-key-change-in-production"),
        ("JWT算法", settings.JWT_ALGORITHM == "HS256"),
        ("JWT过期时间", settings.JWT_EXPIRE_HOURS > 0),
    ]
    
    all_good = True
    for name, condition in checks:
        status = "✅" if condition else "❌"
        print(f"  {status} {name}: {getattr(settings, name.replace('JWT', 'JWT_').replace('密钥', 'SECRET_KEY').replace('算法', 'ALGORITHM').replace('过期时间', 'EXPIRE_HOURS'))}")
        if not condition:
            all_good = False
    
    if not all_good:
        print("⚠️  警告：请检查JWT配置")
    
    return all_good


def check_database():
    """检查数据库中的用户表"""
    print("\n🔍 检查数据库...")
    
    try:
        with Session(sync_engine) as db:
            # 检查用户表是否存在
            users = db.query(User).all()
            user_count = len(users)
            admin_count = len([u for u in users if u.is_admin])
            
            print(f"  ✅ 用户表: 存在")
            print(f"  📊 用户总数: {user_count}")
            print(f"  👑 管理员数量: {admin_count}")
            
            # 检查token_version字段
            if users:
                first_user = users[0]
                has_token_version = hasattr(first_user, 'token_version')
                print(f"  🔧 token_version字段: {'存在' if has_token_version else '缺失'}")
                return has_token_version
            else:
                print("  ⚠️  警告：没有用户，建议运行 scripts/create_admin.py 创建管理员")
                return True
                
    except Exception as e:
        print(f"  ❌ 数据库检查失败: {str(e)}")
        return False


def check_dependencies():
    """检查依赖包"""
    print("\n🔍 检查依赖包...")
    
    try:
        import jwt
        print(f"  ✅ PyJWT: {jwt.__version__}")
        
        from passlib.context import CryptContext
        print("  ✅ passlib: 可用")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ 依赖包缺失: {str(e)}")
        return False


def main():
    """主函数"""
    print("JWT认证系统检查工具")
    print("==================")
    
    checks = [
        check_dependencies(),
        check_jwt_config(),
        check_database()
    ]
    
    if all(checks):
        print("\n🎉 JWT认证系统检查通过!")
        print("\n💡 使用指南:")
        print("1. 运行 'python scripts/create_admin.py' 创建管理员用户")
        print("2. 运行 'uv run alembic upgrade head' 应用数据库迁移")
        print("3. 启动服务器: 'uv run uvicorn app.main:app --reload'")
        print("4. 访问 http://localhost:8000/api/docs 查看API文档")
        print("5. 使用 POST /api/v1/auth/login 登录获取JWT token")
    else:
        print("\n❌ JWT认证系统配置不完整，请检查以上问题")
        sys.exit(1)


if __name__ == "__main__":
    main()