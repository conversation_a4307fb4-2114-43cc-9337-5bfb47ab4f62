#!/bin/bash

# JWT认证系统快速设置脚本

echo "🚀 JWT认证系统快速设置"
echo "====================="

# 1. 复制环境变量文件
if [ ! -f .env ]; then
    echo "📋 复制环境变量模板..."
    cp .env.example .env
    echo "✅ .env 文件已创建，请根据需要修改配置"
else
    echo "📋 .env 文件已存在"
fi

# 2. 安装依赖
echo "📦 安装依赖..."
uv sync

# 3. 执行数据库迁移
echo "🗃️  应用数据库迁移..."
uv run alembic upgrade head

# 4. 检查系统状态
echo "🔍 检查JWT认证系统状态..."
uv run python scripts/check_jwt_system.py

echo ""
echo "🎯 下一步："
echo "1. 运行 'uv run python scripts/create_admin.py' 创建管理员用户"
echo "2. 启动服务器: 'uv run uvicorn app.main:app --reload'"
echo "3. 访问 http://localhost:8000/api/docs 测试API"