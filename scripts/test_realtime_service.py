#!/usr/bin/env python3
"""
测试实时数据服务
"""
import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.realtime_data_service import get_realtime_service

async def test_realtime_service():
    """测试实时数据服务"""
    print("开始测试实时数据服务...")
    
    # 获取服务实例
    service = get_realtime_service()
    
    # 测试股票代码 - 使用一些常见的股票代码
    test_codes = ["000001", "000002", "600000", "600036"]
    
    try:
        # 测试批量获取实时数据
        print(f"测试获取股票 {test_codes} 的实时数据...")
        quotes = await service.get_realtime_quotes(test_codes)
        
        if quotes:
            print(f"成功获取 {len(quotes)} 只股票的实时数据:")
            for code, quote in quotes.items():
                print(f"  {code}: 价格={quote.get('price', 0):.2f}, "
                      f"涨跌幅={quote.get('change_percent', 0):.2f}%, "
                      f"成交量={quote.get('volume', 0)}")
        else:
            print("未获取到实时数据")
            
        # 测试单个股票数据
        print("\n测试单个股票数据...")
        single_quote = await service.get_single_quote("000001")
        if single_quote:
            print(f"000001 单个查询结果: 价格={single_quote.get('price', 0):.2f}, "
                  f"涨跌幅={single_quote.get('change_percent', 0):.2f}%")
        else:
            print("未获取到单个股票数据")
            
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n测试完成!")

if __name__ == "__main__":
    asyncio.run(test_realtime_service())
