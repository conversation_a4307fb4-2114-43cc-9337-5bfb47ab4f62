#!/usr/bin/env python3
"""
数据库初始化脚本
用于解决Alembic版本不一致问题和数据库初始化
"""

import os
import sys
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def run_command(cmd, description):
    """运行命令并处理错误"""
    print(f"执行: {description}")
    print(f"命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: {e}")
        if e.stdout:
            print(f"输出: {e.stdout}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        return False

def check_database_exists():
    """检查数据库文件是否存在"""
    db_file = project_root / "quantization.db"
    return db_file.exists()

def init_alembic():
    """初始化Alembic版本管理"""
    print("=== 初始化Alembic版本管理 ===")
    
    # 检查当前版本
    if not run_command(["alembic", "current"], "检查当前版本"):
        print("Alembic版本检查失败")
        return False
    
    # 检查是否有表但版本不匹配的情况
    if check_database_exists():
        print("数据库文件存在，检查版本同步状态...")
        
        # 先标记为基础版本
        if not run_command(["alembic", "stamp", "base"], "标记为基础版本"):
            print("无法标记为基础版本")
            return False
        
        # 再标记为最新版本
        if not run_command(["alembic", "stamp", "head"], "标记为最新版本"):
            print("无法标记为最新版本")
            return False
    
    # 执行升级
    if not run_command(["alembic", "upgrade", "head"], "升级到最新版本"):
        print("升级失败")
        return False
    
    print("✅ Alembic初始化完成")
    return True

def main():
    """主函数"""
    print("=== 数据库初始化脚本 ===")
    
    # 切换到项目根目录
    os.chdir(project_root)
    
    # 初始化Alembic
    if not init_alembic():
        print("❌ 数据库初始化失败")
        sys.exit(1)
    
    print("✅ 数据库初始化成功")

if __name__ == "__main__":
    main()