#!/bin/bash
# 服务器数据库部署脚本
# 解决Alembic版本不一致问题

set -e  # 遇到错误立即退出

echo "=== 服务器数据库部署脚本 ==="

# 检查是否在项目根目录
if [ ! -f "alembic.ini" ]; then
    echo "错误: 请在项目根目录执行此脚本"
    exit 1
fi

echo "1. 检查当前Alembic状态..."
alembic current || echo "警告: Alembic状态检查失败，可能需要初始化"

echo "2. 检查数据库文件是否存在..."
if [ -f "quantization.db" ]; then
    echo "✅ 数据库文件存在"
    
    echo "3. 重新同步Alembic版本..."
    alembic stamp base
    alembic stamp head
    
    echo "4. 确认版本同步..."
    alembic current
else
    echo "❌ 数据库文件不存在，将创建新数据库"
fi

echo "5. 执行迁移..."
if alembic upgrade head; then
    echo "✅ 数据库迁移成功"
else
    echo "❌ 数据库迁移失败"
    echo "如果遇到表已存在错误，请手动执行:"
    echo "  alembic stamp head"
    echo "  alembic upgrade head"
    exit 1
fi

echo "6. 验证最终状态..."
alembic current

echo "✅ 服务器数据库部署完成"