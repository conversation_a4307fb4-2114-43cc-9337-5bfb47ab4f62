#!/usr/bin/env python3
"""
创建初始管理员用户的脚本 - 异步版本

用法: python scripts/create_admin.py
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.database import db_session
from app.models.user import User
from app.services.user_service import UserService
from app.utils.auth import get_password_hash
import getpass


async def create_admin_user():
    """创建初始管理员用户"""
    print("创建初始管理员用户")
    print("==================")
    
    # 获取用户输入
    username = input("请输入管理员用户名: ").strip()
    if not username:
        print("错误：用户名不能为空")
        return False
    
    email = input("请输入管理员邮箱 (可选): ").strip() or None
    
    # 安全地获取密码
    while True:
        password = getpass.getpass("请输入密码: ")
        if len(password) < 6:
            print("错误：密码长度至少6位")
            continue
        
        confirm_password = getpass.getpass("请确认密码: ")
        if password != confirm_password:
            print("错误：两次输入的密码不一致")
            continue
        break
    
    # 异步数据库操作
    try:
        # 检查用户名是否已存在
        existing_user = await UserService.get_user_by_username(username)
        if existing_user:
            print(f"警告：用户名 '{username}' 已存在，将进行覆盖")
            # 删除现有用户
            async with db_session() as db:
                # 重新查询用户以获取绑定到当前会话的对象
                from sqlalchemy import select
                stmt = select(User).where(User.username == username)
                result = await db.execute(stmt)
                user_to_delete = result.scalar_one_or_none()
                if user_to_delete:
                    await db.delete(user_to_delete)
                    await db.commit()
                    print(f"✅ 已删除现有用户")
        
        # 创建管理员用户
        admin_user = await UserService.create_user(
            username=username,
            password=password,
            email=email,
            is_admin=True
        )
        
        print(f"✅ 管理员用户创建成功!")
        print(f"   用户ID: {admin_user.id}")
        print(f"   用户名: {admin_user.username}")
        print(f"   邮箱: {admin_user.email or '未设置'}")
        print(f"   管理员权限: {'是' if admin_user.is_admin else '否'}")
        print(f"   账户状态: {'激活' if admin_user.is_active else '禁用'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建管理员用户失败: {str(e)}")
        return False


async def main():
    """主函数"""
    print("JWT认证系统 - 管理员用户创建工具")
    print("==================================")
    
    success = await create_admin_user()
    
    if success:
        print("\n✅ 管理员用户创建完成!")
        print("现在您可以使用这个账户登录系统。")
        print("登录API: POST /api/v1/auth/login")
    else:
        print("\n❌ 管理员用户创建失败!")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())