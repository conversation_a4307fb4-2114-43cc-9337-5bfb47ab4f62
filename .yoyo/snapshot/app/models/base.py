"""
基础模型模块

定义所有模型的基类和共同属性。
"""

from datetime import datetime
from sqlalchemy import Column, Integer, DateTime, MetaData
from sqlalchemy.orm import declared_attr
from sqlalchemy.ext.declarative import declarative_base, declared_attr

class Base(object):
    """声明性基类"""
    
    # 使用类名小写作为表名
    @declared_attr
    def __tablename__(cls):
        return cls.__name__.lower()
    
    # 主键使用普通自增ID，确保SQLite兼容性
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # 自动添加创建和更新时间
    created_at = Column(DateTime, nullable=False, default=datetime.now)
    updated_at = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now)

# 创建基类，允许表重复定义（用于测试）
BaseModel = declarative_base(cls=Base, metadata=MetaData(naming_convention={
    "ix": "ix_%(column_0_label)s",
    "uq": "uq_%(table_name)s_%(column_0_name)s",
    "ck": "ck_%(table_name)s_%(constraint_name)s",
    "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
    "pk": "pk_%(table_name)s"
}))
BaseModel.metadata.reflect = True
