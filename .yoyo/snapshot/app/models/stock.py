"""
股票模型模块

定义与股票数据相关的数据库模型。
"""

from sqlalchemy import (
    Column, String, Float, Date, ForeignKey, 
    UniqueConstraint, Index, BigInteger, JSON,
    Boolean
)
from sqlalchemy.orm import relationship, declared_attr
from typing import Optional, List, Dict
from datetime import datetime

from app.models.base import BaseModel
from app.models.partition import TimePartitionedMixin

class StockInfo(BaseModel):
    """股票基本信息模型"""
    
    __tablename__ = "stock_info"
    
    # 股票代码（如：601398）
    code = Column(String(10), index=True, nullable=False, unique=True)
    
    # 股票名称（如：工商银行）
    name = Column(String(100), nullable=False)
    
    # 交易所代码（如：SH, SZ）
    exchange = Column(String(10), nullable=False)
    
    # 完整股票代码（如：SH601398）
    full_code = Column(String(20), index=True, nullable=False, unique=True)
    
    # 行业分类
    industry = Column(String(50), nullable=True)
    
    # 股票所属板块
    sector = Column(String(50), nullable=True)
    
    # 上市日期
    listing_date = Column(Date, nullable=True)
    
    # 总股本（股）
    total_shares = Column(BigInteger, nullable=True)
    
    # 流通股本（股）
    circulating_shares = Column(BigInteger, nullable=True)
    
    # 公司简介
    company_profile = Column(String(2000), nullable=True)
    
    # 是否在列表中显示
    is_active = Column(Boolean, default=True, nullable=False)
    
    # 市值（元）
    market_cap = Column(BigInteger, nullable=True)
    
    def __repr__(self) -> str:
        return f"<StockInfo(code='{self.code}', name='{self.name}', exchange='{self.exchange}')>"

class StockDailyBase(BaseModel, TimePartitionedMixin):
    """股票日线数据基类（按月分表）"""
    
    __abstract__ = True
    _base_table = "stock_daily"
    _time_format = "%Y%m"  # 按月分表，格式如: stock_daily_202503
    
    # 关联的股票代码（外键）
    stock_code = Column(String(10), ForeignKey('stock_info.code'), index=True, nullable=False)
    
    # 交易日期
    trade_date = Column(Date, nullable=False)
    
    # 开盘价
    open = Column(Float, nullable=False)
    
    # 最高价
    high = Column(Float, nullable=False)
    
    # 最低价
    low = Column(Float, nullable=False)
    
    # 收盘价
    close = Column(Float, nullable=False)
    
    # 交易量（股）
    volume = Column(BigInteger, nullable=False)
    
    # 交易额（元）
    amount = Column(BigInteger, nullable=True)
    
    # 涨跌幅（%）
    change_pct = Column(Float, nullable=True)
    
    # 换手率（%）
    turnover_rate = Column(Float, nullable=True)
    
    # 涨跌停状态（U:涨停, D:跌停, N:正常）
    limit_status = Column(String(1), nullable=True)
    
    # 是否ST股
    is_st = Column(Boolean, default=False, nullable=False)
    
    # 定义索引为类属性，方便继承和修改
    _unique_constraints = [
        ('stock_code', 'trade_date', 'uix_stock_daily_code_date')
    ]
    
    _indexes = [
        ('ix_stock_daily_code_date', ['stock_code', 'trade_date'])
    ]
    
    @classmethod
    def __declare_last__(cls):
        """生成表约束和索引"""
        args = []
        # 添加唯一约束
        for columns in cls._unique_constraints:
            name = columns[-1]
            cols = columns[:-1]
            UniqueConstraint(*[getattr(cls, col) for col in cols], name=name)
        
        # 添加索引
        for idx in cls._indexes:
            name, columns = idx
            Index(name, *[getattr(cls, col) for col in columns])
    
    def __repr__(self) -> str:
        return f"<StockDaily(stock_code='{self.stock_code}', trade_date='{self.trade_date}', close={self.close})>"

    def __new__(cls, *args, **kwargs):
        """确保使用正确的分区表类"""
        if cls is StockDailyBase:
            # 如果直接使用基类，则使用当前日期的分区表类
            current_date = datetime.now()
            partition_class = cls.get_partition(current_date)
            return super(StockDailyBase, partition_class).__new__(partition_class)
        return super().__new__(cls)

class IndicatorVersion(BaseModel):
    """指标版本控制模型"""
    
    __tablename__ = "indicator_version"
    
    # 版本哈希值
    version_hash = Column(String(64), unique=True, nullable=False)
    
    # 指标类型（如：MACD, KDJ, RSI等）
    indicator_type = Column(String(20), nullable=False)
    
    # 计算公式
    formula = Column(String(2000), nullable=False)
    
    # 参数配置（JSON格式）
    parameters = Column(JSON, nullable=False)
    
    # 生效日期
    effective_date = Column(Date, nullable=False)
    
    # 是否为当前版本
    is_current = Column(Boolean, nullable=False, server_default='1')
    
    # 版本说明
    description = Column(String(500), nullable=True)
    
    __table_args__ = (
        Index('ix_indicator_version_type_current', 'indicator_type', 'is_current'),
    )
    
    def __repr__(self) -> str:
        return f"<IndicatorVersion(type='{self.indicator_type}', hash='{self.version_hash}')>"

class StockIndicator(BaseModel):
    """股票技术指标数据模型"""
    
    __tablename__ = "stock_indicator"
    
    # 关联的股票代码（外键）
    stock_code = Column(String(10), ForeignKey('stock_info.code'), index=True, nullable=False)
    
    # 交易日期
    trade_date = Column(Date, nullable=False)
    
    # 指标类型（如：MACD, KDJ, RSI等）
    indicator_type = Column(String(20), nullable=False)
    
    # 指标版本（关联版本控制表）
    version_hash = Column(String(64), ForeignKey('indicator_version.version_hash'), nullable=False)
    
    # 指标值（JSON格式，支持多周期）
    values = Column(JSON, nullable=False)
    
    # 数据频率（D1:日线, W1:周线, M1:月线）
    data_frequency = Column(String(2), nullable=False)
    
    # 关联到版本信息
    version = relationship("IndicatorVersion")
    
    # 复合唯一索引
    __table_args__ = (
        UniqueConstraint(
            'stock_code', 'trade_date', 'indicator_type', 'version_hash',
            name='uix_stock_indicator_code_date_type_version'
        ),
        Index('ix_stock_indicator_code_date_type', 'stock_code', 'trade_date', 'indicator_type'),
    )
    
    def __repr__(self) -> str:
        return f"<StockIndicator(stock_code='{self.stock_code}', trade_date='{self.trade_date}', type='{self.indicator_type}')>"
