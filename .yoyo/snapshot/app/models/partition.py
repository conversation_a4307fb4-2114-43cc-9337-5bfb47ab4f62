"""
分表基类模块

提供分表支持的基础类和工具函数。
"""

from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy import UniqueConstraint, Index, inspect, Table
from datetime import datetime, timedelta
from typing import Optional, Type, Dict, List, Tuple, Any, Union
from app.models.base import BaseModel

class TimePartitionedMixin:
    """
    时间分区Mixin类，用于SQLAlchemy模型的动态分区表管理。
    这个Mixin类提供了基于时间创建和管理数据库表分区的能力。它可以根据指定的日期动态生成表名和索引名，
    确保不同分区表之间的索引名不会冲突。这对于需要按时间分区的大型数据表特别有用，可以提高查询性能
    并简化数据管理。
    属性:
        _partition_classes (dict): 缓存已创建的分区表类。
        _partition_date (datetime, optional): 分区的日期。默认为None。
        _time_format (str): 用于格式化日期的字符串。默认为"%Y%m%d"。
        _base_table (str): 基础表名。默认为"base"。
        _indexes (list): 要创建的索引列表，每项为(索引名, 列名列表)的元组。默认为空列表。
    属性方法:
        __tablename__: 动态生成表名，格式为"{_base_table}_{日期格式化字符串}"
        __table_args__: 动态生成表参数，主要用于处理索引和约束的命名，避免不同分区间的冲突
    类方法:
        get_partition(dt): 获取或创建指定日期的分区表类
            参数:
                dt (datetime): 分区日期
            返回:
                Type["TimePartitionedMixin"]: 对应日期的分区表类
    生成的分区表类方法:
        create_table(bind, checkfirst=True): 在数据库中创建分区表
            参数:
                bind: SQLAlchemy引擎或连接
                checkfirst (bool): 如果为True，仅在表不存在时创建
    使用示例:
        class MyModel(BaseModel, TimePartitionedMixin):
            _base_table = "my_data"
            _time_format = "%Y%m"  # 按月分区
            # 定义列...
        # 获取今天的分区表
        today_partition = MyModel.get_partition(datetime.now())
        # 创建表（如果不存在）
        today_partition.create_table(engine)
        # 使用该分区表进行查询和插入
        session.add(today_partition(column1="value", ...))
    """
    """时间分区Mixin类"""
    
    # 分区表类缓存
    _partition_classes = {}
    
    # 默认属性
    _partition_date = None
    _time_format = "%Y%m%d"
    _base_table = "base"
    _indexes = []
    
    @property
    def __tablename__(self):
        """返回分区表名"""
        if hasattr(self, '_tablename'):
            return self._tablename
        return f"{self._base_table}"

    @classmethod
    def create_table(cls, bind, checkfirst=True):
        """创建分区表"""
        # 获取或创建表对象
        if hasattr(cls, '__table__'):
            table = cls.__table__
        else:
            table = Table(
                cls.__tablename__,
                BaseModel.metadata,
                *[c.copy() for c in cls.__table__.columns],
                extend_existing=True
            )
        
        # 检查表是否已存在
        if checkfirst and inspect(bind).has_table(cls.__tablename__):
            return
            
        # 创建表
        table.create(bind=bind, checkfirst=checkfirst)
    
    @property
    def __table_args__(cls) -> Any:
        """动态生成索引名称，避免分区表间的索引名冲突"""
        if hasattr(cls, '_partition_date') and cls._partition_date is not None:
            table_suffix = cls._partition_date.strftime(cls._time_format)
        else:
            table_suffix = datetime.now().strftime(cls._time_format)
        
        # 获取父类的 __table_args__
        parent_args = getattr(super(), '__table_args__', ())
        
        # 如果父类的 __table_args__ 是字典，则保留该字典
        if isinstance(parent_args, dict):
            return {'extend_existing': True, **parent_args}
        
        # 处理父类的 __table_args__ 是元组的情况
        constraints = list(parent_args)
        
        # 创建一个新列表来存储修改后的约束
        new_constraints = []
        
        # 修改每个约束和索引的名称，添加分区标识
        for i, constraint in enumerate(constraints):
            if hasattr(constraint, 'name') and constraint.name:
                # 为约束名称添加分区标识
                old_name = constraint.name
                if not old_name.endswith(f"_{table_suffix}"):
                    new_name = f"{old_name}_{table_suffix}"
                    
                    # 创建新的约束对象，复制原约束的所有参数，但使用新名称
                    if isinstance(constraint, UniqueConstraint):
                        new_constraints.append(UniqueConstraint(
                            *constraint.columns,
                            name=new_name
                        ))
                    elif isinstance(constraint, Index):
                        new_constraints.append(Index(
                            new_name,
                            *constraint.expressions,
                            unique=constraint.unique
                        ))
                else:
                    new_constraints.append(constraint)
            else:
                new_constraints.append(constraint)
                
        constraints = new_constraints
        
        # 添加任何必要的表选项
        dict_args = {'extend_existing': True}
        
        # 如果最后一个元素是字典，提取它并与我们的选项合并
        if constraints and isinstance(constraints[-1], dict):
            dict_args.update(constraints.pop())
        
        return tuple(constraints + [dict_args])

    @classmethod
    def get_partition(cls, dt: datetime) -> Type["TimePartitionedMixin"]:
        """获取指定时间对应的分区表类"""
        table_suffix = dt.strftime(cls._time_format)
        table_name = f"{cls._base_table}_{table_suffix}"
        
        # 检查缓存中是否已存在该分区表类
        cache_key = f"{cls.__name__}_{table_suffix}"
        if cache_key in cls._partition_classes:
            return cls._partition_classes[cache_key]
        
        # 修改索引名称，添加分区标识
        modified_indexes = []
        if hasattr(cls, '_indexes'):
            for idx in cls._indexes:
                name, columns = idx
                if not name.endswith(f"_{table_suffix}"):
                    modified_name = f"{name}_{table_suffix}"
                    modified_indexes.append((modified_name, columns))
                else:
                    modified_indexes.append(idx)
        
        # 获取父类的 __table_args__
        parent_args = getattr(cls, '__table_args__', {})
        if isinstance(parent_args, tuple):
            # 提取字典部分(如果有)
            table_args_dict = {}
            for item in parent_args:
                if isinstance(item, dict):
                    table_args_dict.update(item)
        elif isinstance(parent_args, dict):
            table_args_dict = parent_args.copy()
        else:
            table_args_dict = {}
        
        # 确保 extend_existing 设置为 True
        table_args_dict["extend_existing"] = True
        
        # 使用基类名作为类名前缀，确保是唯一的
        class_name = f"Partition_{cls.__name__}_{table_suffix}"

        # 创建新的分区类，确保正确设置表名
        attrs = {
            "_partition_date": dt,
            "_indexes": modified_indexes if modified_indexes else getattr(cls, '_indexes', []),
            "__table_args__": table_args_dict,
            "__module__": cls.__module__,
            "__qualname__": class_name,
            "__tablename__": table_name  # 直接设置__tablename__
        }
        
        partition_class = type(class_name, (cls,), attrs)
        
        # 设置分区日期和表名
        partition_class._partition_date = dt
        partition_class._tablename = table_name
        
        
        # 将创建的分区表类添加到缓存
        cls._partition_classes[cache_key] = partition_class
            
        return partition_class

class PartitionManager:
    """分区管理器"""
    
    _partition_tables: Dict[str, Type[TimePartitionedMixin]] = {}
    
    @classmethod
    def register_partition(cls, key: str, table: Type[TimePartitionedMixin]) -> None:
        """注册分区表"""
        cls._partition_tables[key] = table
        
    @classmethod
    def get_partition(cls, key: str, dt: datetime) -> Optional[Type[TimePartitionedMixin]]:
        """获取指定时间的分区表"""
        base_table = cls._partition_tables.get(key)
        if not base_table:
            return None
        return base_table.get_partition(dt)
        
    @classmethod
    def get_partitions_between(cls, key: str, start_date: Optional[datetime], end_date: Optional[datetime]) -> List[Type[TimePartitionedMixin]]:
        """获取指定时间范围内的所有分区表
        
        Args:
            key: 分区表键名
            start_date: 开始日期，如果为None则使用end_date
            end_date: 结束日期，如果为None则使用start_date
            
        Returns:
            List[Type[TimePartitionedMixin]]: 时间范围内的所有分区表类列表
        """
        if not start_date:
            start_date = end_date
        if not end_date:
            end_date = start_date
        if not start_date or not end_date:
            return []
            
        base_table = cls._partition_tables.get(key)
        if not base_table:
            return []
            
        # 获取时间格式
        time_format = getattr(base_table, '_time_format', '%Y%m%d')
        
        # 根据时间格式计算时间间隔
        if time_format == '%Y%m':  # 按月分区
            current_date = datetime(start_date.year, start_date.month, 1)
            end_month = datetime(end_date.year, end_date.month, 1)
        else:  # 按天分区
            current_date = start_date
            end_month = end_date
            
        partitions = []
        while current_date <= end_month:
            partition = cls.get_partition(key, current_date)
            if partition and partition not in partitions:
                partitions.append(partition)
                
            # 移动到下一个时间点
            if time_format == '%Y%m':
                # 移动到下个月
                if current_date.month == 12:
                    current_date = datetime(current_date.year + 1, 1, 1)
                else:
                    current_date = datetime(current_date.year, current_date.month + 1, 1)
            else:
                # 移动到下一天
                current_date = current_date + timedelta(days=1)
                
        return partitions
