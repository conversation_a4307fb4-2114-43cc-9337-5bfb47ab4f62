"""
通用模型和响应类型

提供全局通用的响应模型和工具类，用于标准化API响应格式。
"""
from pydantic import BaseModel, Field
from typing import Generic, TypeVar, Optional, List, Dict, Any, Union

# 定义泛型类型变量
T = TypeVar('T')

class CommonResponse(BaseModel, Generic[T]):
    """
    统一的API响应格式
    
    所有API响应都应使用此模型来确保一致的响应格式。
    泛型参数T代表实际的数据类型。
    
    示例:
    ```python
    @router.get("/users", response_model=CommonResponse[List[User]])
    async def get_users():
        users = await user_service.get_all_users()
        return CommonResponse(data=users)
    ```
    """
    success: bool = Field(True, description="操作是否成功")
    message: str = Field("操作成功", description="操作消息")
    data: Optional[T] = Field(None, description="响应数据")
    error: Optional[str] = Field(None, description="错误信息")
    
    class Config:
        """模型配置"""
        schema_extra = {
            "example": {
                "success": True,
                "message": "获取数据成功",
                "data": {},
                "error": None
            }
        }

class PaginationParams(BaseModel):
    """
    分页参数模型
    
    用于需要分页的API端点
    """
    page: int = Field(1, description="页码", ge=1)
    per_page: int = Field(20, description="每页数量", ge=1, le=100)
    
    class Config:
        """模型配置"""
        schema_extra = {
            "example": {
                "page": 1,
                "per_page": 20
            }
        }

class PaginatedResponse(BaseModel, Generic[T]):
    """
    分页响应模型
    
    用于返回分页数据的API响应
    """
    items: List[T] = Field(..., description="数据项列表")
    total: int = Field(..., description="总数据量")
    page: int = Field(..., description="当前页码")
    per_page: int = Field(..., description="每页数量")
    pages: int = Field(..., description="总页数")
    
    class Config:
        """模型配置"""
        schema_extra = {
            "example": {
                "items": [],
                "total": 100,
                "page": 1,
                "per_page": 20,
                "pages": 5
            }
        }

# 常用错误响应
class ErrorDetail(BaseModel):
    """
    错误详情模型
    
    用于详细描述错误信息
    """
    loc: List[Union[str, int]] = Field(..., description="错误位置")
    msg: str = Field(..., description="错误消息")
    type: str = Field(..., description="错误类型")

class ValidationErrorResponse(BaseModel):
    """
    验证错误响应模型
    
    用于返回表单验证错误
    """
    success: bool = Field(False, description="操作是否成功")
    message: str = Field("数据验证错误", description="操作消息")
    error: str = Field("请检查输入数据", description="简要错误信息")
    detail: List[ErrorDetail] = Field(..., description="错误详情")
