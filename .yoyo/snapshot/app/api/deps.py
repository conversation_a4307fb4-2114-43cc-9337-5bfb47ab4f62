"""
API路由依赖项

提供API路由所需的依赖项，如数据库会话、认证等。
"""

from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy.orm import Session

from app.core.sync_database import get_db
from app.services.user_service import UserService

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

def get_current_user_id() -> int:
    """
    获取当前用户ID
    
    目前简单返回默认管理员用户ID (1)
    实际项目应实现基于token的认证并从token中解析用户ID
    
    Returns:
        int: 用户ID
    """
    # TODO: 实现基于token的认证
    # 现在简化处理，直接返回管理员用户ID
    return 1

def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    获取当前认证用户
    
    Args:
        token: JWT令牌
        db: 数据库会话
        
    Returns:
        User: 用户对象
        
    Raises:
        HTTPException: 认证失败
    """
    # TODO: 实现JWT令牌验证
    # 现在简化处理，直接返回管理员用户
    user = UserService.get_user_by_id(db, 1)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的身份凭证",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return user

def get_admin_user(
    current_user = Depends(get_current_user)
):
    """
    确认当前用户是管理员
    
    Args:
        current_user: 当前用户对象
        
    Returns:
        User: 管理员用户对象
        
    Raises:
        HTTPException: 用户不是管理员
    """
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user
