"""API路由配置"""
from fastapi import APIRouter
from app.core.config import settings # Import settings
from app.api.endpoints import indicators
from app.api.endpoints import advanced_indicators
from app.api.endpoints.analytics import kline
from app.api.endpoints import sync_watchlist # 保留同步的 watchlist 路由
from app.api.endpoints import users # 导入用户路由
from app.api.endpoints import cache # 导入缓存管理路由

# 创建主路由器, 使用 /v1 前缀
api_router = APIRouter(prefix=settings.API_V1_STR)

# 注册各模块路由
api_router.include_router(indicators.router, prefix="/indicators", tags=["Indicators"]) # Add prefix and tags
api_router.include_router(advanced_indicators.router, prefix="/advanced-indicators", tags=["Advanced Indicators"]) # Add new advanced indicators
api_router.include_router(kline.router, prefix="/analytics", tags=["Analytics"]) # Add tags

# 注册同步版API路由
api_router.include_router(sync_watchlist.router, tags=["Watchlist"]) # 注册同步 watchlist 路由，并确保 tags 一致
api_router.include_router(users.router) # 注册用户路由

# 注册缓存管理路由
api_router.include_router(cache.router, tags=["Cache Management"])

# 只在开发环境下注册股票API路由(包括股票列表和股票数据)
# if settings.APP_ENV.lower() == "development":
#     from app.api.endpoints import stocks
#     api_router.include_router(stocks.router, prefix="/stocks", tags=["Stocks"])
#     print(f"股票API已启用 - 当前环境: {settings.APP_ENV}")
# else:
#     print(f"股票API已关闭 - 当前环境: {settings.APP_ENV}")

from app.api.endpoints import stocks
api_router.include_router(stocks.router, prefix="/stocks", tags=["Stocks"])

# 注册扫描服务路由
from app.api.endpoints import scan_session, scan
api_router.include_router(scan_session.router, prefix="/scan", tags=["Scan Session"])
api_router.include_router(scan.router, prefix="/scan", tags=["Scan"])
