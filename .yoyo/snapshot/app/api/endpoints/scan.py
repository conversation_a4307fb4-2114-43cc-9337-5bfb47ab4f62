"""
扫描操作API路由
处理扫描任务的启动、停止、查询等操作
"""
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Request, Query, Depends
import logging

from app.services.scan.manager import get_session_manager, get_memory_scanner
from app.services.scan.models import ScanStatus
from app.schemas.scan import (
    ScanStartRequest,
    ScanStartResponse,
    ScanTaskResponse,
    ScanResultsResponse,
    ScanResultResponse,
    ScanProgressResponse,
    StockIndicatorDataResponse
)

logger = logging.getLogger(__name__)

router = APIRouter()


async def get_session_id(request: Request) -> str:
    """
    从请求中获取会话ID
    
    Args:
        request: HTTP请求对象
        
    Returns:
        会话ID
        
    Raises:
        HTTPException: 如果没有会话ID
    """
    session_id = request.cookies.get("scan_session_id")
    if not session_id:
        # 尝试从header获取
        session_id = request.headers.get("X-Session-ID")
    
    if not session_id:
        raise HTTPException(status_code=401, detail="No session ID provided")
    
    # 验证会话存在
    session_manager = get_session_manager()
    if not session_manager.get_session(session_id):
        raise HTTPException(status_code=401, detail="Invalid or expired session")
    
    return session_id


@router.post("/start", response_model=ScanStartResponse)
async def start_scan(
    request: ScanStartRequest,
    session_id: str = Depends(get_session_id)
) -> ScanStartResponse:
    """
    启动扫描任务
    
    Args:
        request: 扫描启动请求
        session_id: 会话ID
        
    Returns:
        扫描启动响应
    """
    try:
        # 获取扫描器
        scanner = get_memory_scanner()
        
        # 启动扫描（传递参数配置）
        task_id = await scanner.start_scan(
            session_id=session_id,
            indicators=request.indicators,
            stock_codes=request.stock_codes,
            parameters=request.parameters
        )
        
        # 获取任务信息
        session_manager = get_session_manager()
        task = session_manager.get_scan_task(session_id, task_id)
        
        if not task:
            raise HTTPException(status_code=500, detail="Failed to create scan task")
        
        logger.info(f"Started scan task {task_id} for session {session_id}")
        
        return ScanStartResponse(
            task_id=task_id,
            session_id=session_id,
            status=task.status,
            created_at=task.created_at
        )
        
    except Exception as e:
        logger.error(f"Failed to start scan: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to start scan: {str(e)}")


@router.post("/{task_id}/stop")
async def stop_scan(
    task_id: str,
    session_id: str = Depends(get_session_id)
) -> dict:
    """
    停止扫描任务
    
    Args:
        task_id: 任务ID
        session_id: 会话ID
        
    Returns:
        停止结果
    """
    try:
        scanner = get_memory_scanner()
        success = await scanner.stop_scan(session_id, task_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="Task not found")
        
        logger.info(f"Stopped scan task {task_id}")
        
        return {"message": f"Task {task_id} stopped successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to stop scan {task_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to stop scan")


@router.get("/active", response_model=List[ScanTaskResponse])
async def get_active_scans(
    session_id: str = Depends(get_session_id)
) -> List[ScanTaskResponse]:
    """
    获取当前会话的活跃扫描任务（包括正在运行和最近完成的任务）
    
    Args:
        session_id: 会话ID
        
    Returns:
        活跃的扫描任务列表
    """
    try:
        session_manager = get_session_manager()
        tasks = session_manager.get_session_tasks(session_id)
        
        # 首先查找正在运行的任务
        running_tasks = [
            task for task in tasks
            if task.status in [ScanStatus.PENDING, ScanStatus.RUNNING]
        ]
        
        # 如果没有正在运行的任务，返回最近的一个任务（包括已完成的）
        if not running_tasks and tasks:
            # 按创建时间排序，获取最新的任务
            latest_task = max(tasks, key=lambda t: t.created_at)
            active_tasks = [latest_task]
        else:
            active_tasks = running_tasks
        
        # 转换为响应格式
        responses = []
        for task in active_tasks:
            progress = ScanProgressResponse(
                task_id=task.id,
                status=task.status,
                total=task.progress.total,
                current=task.progress.current,
                percentage=task.progress.percentage,
                message=task.progress.message
            )
            
            responses.append(ScanTaskResponse(
                task_id=task.id,
                session_id=session_id,
                status=task.status,
                indicators=task.indicators,
                progress=progress,
                result_count=len(task.results),
                created_at=task.created_at,
                start_time=task.start_time,
                end_time=task.end_time,
                error_message=task.error_message
            ))
        
        return responses
        
    except Exception as e:
        logger.error(f"Failed to get active scans: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get active scans")


@router.get("/{task_id}", response_model=ScanTaskResponse)
async def get_scan_task(
    task_id: str,
    session_id: str = Depends(get_session_id)
) -> ScanTaskResponse:
    """
    获取扫描任务信息
    
    Args:
        task_id: 任务ID
        session_id: 会话ID
        
    Returns:
        扫描任务信息
    """
    try:
        session_manager = get_session_manager()
        task = session_manager.get_scan_task(session_id, task_id)
        
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        # 构建进度响应
        progress = ScanProgressResponse(
            task_id=task_id,
            status=task.status,
            total=task.progress.total,
            current=task.progress.current,
            percentage=task.progress.percentage,
            message=task.progress.message
        )
        
        return ScanTaskResponse(
            task_id=task_id,
            session_id=session_id,
            status=task.status,
            indicators=task.indicators,
            progress=progress,
            result_count=len(task.results),
            created_at=task.created_at,
            start_time=task.start_time,
            end_time=task.end_time,
            error_message=task.error_message
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get scan task {task_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get task")


@router.get("/{task_id}/progress", response_model=ScanProgressResponse)
async def get_scan_progress(
    task_id: str,
    session_id: str = Depends(get_session_id)
) -> ScanProgressResponse:
    """
    获取扫描进度
    
    Args:
        task_id: 任务ID
        session_id: 会话ID
        
    Returns:
        扫描进度
    """
    try:
        session_manager = get_session_manager()
        task = session_manager.get_scan_task(session_id, task_id)
        
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        return ScanProgressResponse(
            task_id=task_id,
            status=task.status,
            total=task.progress.total,
            current=task.progress.current,
            percentage=task.progress.percentage,
            message=task.progress.message
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get scan progress for {task_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get progress")


@router.get("/{task_id}/results", response_model=ScanResultsResponse)
async def get_scan_results(
    task_id: str,
    session_id: str = Depends(get_session_id),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小")
) -> ScanResultsResponse:
    """
    获取扫描结果
    
    Args:
        task_id: 任务ID
        session_id: 会话ID
        page: 页码
        page_size: 每页大小
        
    Returns:
        扫描结果列表
    """
    try:
        session_manager = get_session_manager()
        task = session_manager.get_scan_task(session_id, task_id)
        
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        # 分页处理
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        
        # 获取分页结果
        results = task.results[start_idx:end_idx]
        
        # 转换为响应格式
        result_responses = []
        for result in results:
            indicator_data = StockIndicatorDataResponse(
                kdj_k=result.indicator_data.kdj_k,
                kdj_d=result.indicator_data.kdj_d,
                kdj_j=result.indicator_data.kdj_j,
                volume_pressure=result.indicator_data.volume_pressure,
                bollinger_upper=result.indicator_data.bollinger_upper,
                bollinger_middle=result.indicator_data.bollinger_middle,
                bollinger_lower=result.indicator_data.bollinger_lower,
                macd=result.indicator_data.macd,
                macd_signal=result.indicator_data.macd_signal,
                macd_histogram=result.indicator_data.macd_histogram,
                rsi=result.indicator_data.rsi,
                arbr_ar=result.indicator_data.arbr_ar,
                arbr_br=result.indicator_data.arbr_br,
                # 新增字段
                prev_kdj_k=result.indicator_data.prev_kdj_k,
                prev_kdj_d=result.indicator_data.prev_kdj_d,
                volume_pressure_avg=result.indicator_data.volume_pressure_avg,
                close_price=result.indicator_data.close_price,
                bollinger_distance_pct=result.indicator_data.bollinger_distance_pct
            )
            
            result_responses.append(ScanResultResponse(
                stock_code=result.stock_code,
                stock_name=result.stock_name,
                signals=result.signals,
                indicator_data=indicator_data,
                price=result.price,
                change_percent=result.change_percent,
                scan_time=result.scan_time
            ))
        
        return ScanResultsResponse(
            task_id=task_id,
            results=result_responses,
            total_count=len(task.results),
            page=page,
            page_size=page_size
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get scan results for {task_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get results")


@router.get("/cache/stats")
async def get_cache_stats() -> dict:
    """
    获取缓存统计信息
    
    Returns:
        缓存统计数据
    """
    try:
        scanner = get_memory_scanner()
        stats = scanner.get_cache_stats()
        
        return {
            "cache_stats": stats,
            "message": "Cache statistics retrieved successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to get cache stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get cache stats")


@router.delete("/cache")
async def clear_cache() -> dict:
    """
    清空所有缓存
    
    Returns:
        清空结果
    """
    try:
        scanner = get_memory_scanner()
        scanner.clear_cache()
        
        return {"message": "Cache cleared successfully"}
        
    except Exception as e:
        logger.error(f"Failed to clear cache: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to clear cache")