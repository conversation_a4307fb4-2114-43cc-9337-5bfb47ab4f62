"""
会话管理API路由
处理会话创建、获取、清理等操作
"""
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Response, Request
from fastapi.responses import JSONResponse
import uuid
import logging

from app.services.scan.manager import get_session_manager
from app.schemas.scan import (
    SessionCreateRequest,
    SessionCreateResponse,
    SessionInfoResponse
)

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/session/create", response_model=SessionCreateResponse)
async def create_session(
    request: SessionCreateRequest,
    response: Response
) -> SessionCreateResponse:
    """
    创建新的扫描会话
    
    Args:
        request: 会话创建请求
        response: HTTP响应对象
        
    Returns:
        会话创建响应
    """
    try:
        # 生成会话ID
        session_id = str(uuid.uuid4())
        
        # 获取会话管理器
        session_manager = get_session_manager()
        
        # 创建会话
        session = session_manager.create_session(
            session_id=session_id,
            user_id=request.user_id,
            metadata=request.metadata or {}
        )
        
        # 设置cookie
        response.set_cookie(
            key="scan_session_id",
            value=session_id,
            max_age=3600 * 24,  # 24小时
            httponly=True,
            samesite="lax"
        )
        
        logger.info(f"Created session {session_id} for user {request.user_id}")
        
        return SessionCreateResponse(
            session_id=session_id,
            user_id=session.user_id,
            created_at=session.created_at,
            metadata=session.metadata
        )
        
    except Exception as e:
        logger.error(f"Failed to create session: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to create session")


@router.get("/session/{session_id}", response_model=SessionInfoResponse)
async def get_session(session_id: str) -> SessionInfoResponse:
    """
    获取会话信息
    
    Args:
        session_id: 会话ID
        
    Returns:
        会话信息响应
    """
    try:
        session_manager = get_session_manager()
        session = session_manager.get_session(session_id)
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
        
        # 获取会话的扫描任务
        tasks = session_manager.get_session_tasks(session_id)
        
        return SessionInfoResponse(
            session_id=session_id,
            user_id=session.user_id,
            created_at=session.created_at,
            last_active=session.last_active,
            scan_task_count=len(tasks),
            metadata=session.metadata
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get session {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get session")


@router.get("/session/current")
async def get_current_session(request: Request) -> Dict[str, Any]:
    """
    获取当前会话信息（从cookie中）
    
    Args:
        request: HTTP请求对象
        
    Returns:
        当前会话信息
    """
    try:
        # 从cookie获取会话ID
        session_id = request.cookies.get("scan_session_id")
        
        if not session_id:
            return {"session_id": None, "message": "No active session"}
        
        # 获取会话信息
        session_manager = get_session_manager()
        session = session_manager.get_session(session_id)
        
        if not session:
            return {"session_id": None, "message": "Session expired or not found"}
        
        # 更新活跃时间
        session_manager.update_session_activity(session_id)
        
        # 获取任务信息
        tasks = session_manager.get_session_tasks(session_id)
        
        return {
            "session_id": session_id,
            "user_id": session.user_id,
            "created_at": session.created_at.isoformat(),
            "scan_task_count": len(tasks),
            "active_tasks": [
                task.id for task in tasks 
                if task.status in ["pending", "running"]
            ]
        }
        
    except Exception as e:
        logger.error(f"Failed to get current session: {str(e)}")
        return {"session_id": None, "error": str(e)}


@router.delete("/session/{session_id}")
async def delete_session(session_id: str) -> Dict[str, str]:
    """
    删除会话
    
    Args:
        session_id: 会话ID
        
    Returns:
        删除结果
    """
    try:
        session_manager = get_session_manager()
        
        if not session_manager.get_session(session_id):
            raise HTTPException(status_code=404, detail="Session not found")
        
        # 删除会话
        session_manager.remove_session(session_id)
        
        logger.info(f"Deleted session {session_id}")
        
        return {"message": f"Session {session_id} deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete session {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to delete session")


@router.post("/session/cleanup")
async def cleanup_sessions() -> Dict[str, Any]:
    """
    清理过期会话
    
    Returns:
        清理结果
    """
    try:
        session_manager = get_session_manager()
        
        # 获取清理前的会话数
        before_count = len(session_manager._sessions)
        
        # 执行清理
        session_manager.cleanup_expired_sessions()
        
        # 获取清理后的会话数
        after_count = len(session_manager._sessions)
        
        cleaned_count = before_count - after_count
        
        logger.info(f"Cleaned up {cleaned_count} expired sessions")
        
        return {
            "cleaned_sessions": cleaned_count,
            "remaining_sessions": after_count
        }
        
    except Exception as e:
        logger.error(f"Failed to cleanup sessions: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to cleanup sessions")