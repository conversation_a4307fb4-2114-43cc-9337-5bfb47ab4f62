"""
数据库连接模块 - 同步版本

管理数据库连接、会话和事务的同步版本。
"""

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from typing import Generator

from app.core.config import settings

# 创建同步引擎
sync_engine = create_engine(
    settings.DATABASE_URL,
    pool_pre_ping=True,
    echo=settings.SQL_ECHO,
    pool_recycle=3600,
)

# 创建同步会话工厂
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=sync_engine,
)

def get_db() -> Generator[Session, None, None]:
    """
    提供同步数据库会话
    
    用作API依赖项，自动管理会话的生命周期。
    
    Yields:
        Session: 数据库会话
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
