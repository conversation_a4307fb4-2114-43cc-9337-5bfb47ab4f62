"""
日志配置模块

配置应用日志记录系统。
"""

import logging
import sys
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime
from logging.handlers import TimedRotatingFileHandler # 导入 TimedRotatingFileHandler

from app.core.config import settings


def setup_logging(log_level: Optional[str] = None) -> None:
    """
    设置应用日志记录
    
    参数:
        log_level: 日志级别，默认使用配置中的值
    """
    level = log_level or (logging.DEBUG if settings.DEBUG else logging.INFO)
    
    # 创建日志格式器
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 清除现有处理器
    if root_logger.handlers:
        for handler in root_logger.handlers:
            root_logger.removeHandler(handler)
    
    # 添加控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.setLevel(level)
    root_logger.addHandler(console_handler)
    
    # 添加文件处理器（如果需要）
    if True or settings.APP_ENV != "development":
        logs_dir = Path(settings.LOG_DIR)
        logs_dir.mkdir(exist_ok=True)
        
        # 使用 TimedRotatingFileHandler 替换 FileHandler
        log_file_path = logs_dir / f"{settings.APP_NAME.lower().replace(' ', '_')}.log" # 基础日志文件名
        file_handler = TimedRotatingFileHandler(
            log_file_path,
            when="midnight",  # 每天午夜轮换
            interval=1,       # 每天轮换一次
            backupCount=7,    # 保留最近7天的日志
            encoding="utf-8",
            delay=True        # 延迟文件创建直到第一次记录日志
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(level)
        root_logger.addHandler(file_handler)
    
    # 配置某些模块的日志级别
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    
    # 调整日志级别，确保不记录太多详细信息
    for logger_name in ["uvicorn", "fastapi", "sqlalchemy", "aiosqlite"]:
        mod_logger = logging.getLogger(logger_name)
        mod_logger.setLevel(logging.INFO)

def getLogger(name: str) -> logging.Logger:
    """
    获取指定名称的日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        logging.Logger: 日志记录器实例
    """
    return logging.getLogger(name)
