"""
缓存模块 - 新版本

重构后的统一缓存模块，提供向后兼容的API，同时引入新的功能。

升级说明：
- 保持所有现有API的兼容性
- 新增多级缓存支持（内存 + Redis）
- 新增缓存标签、统计、监控等功能
- 提供更灵活的配置和装饰器

使用方式：
1. 旧的API继续正常工作
2. 新功能通过 app.core.cache.new 模块访问
3. 建议逐步迁移到新的装饰器和配置系统
"""

# 导入新的缓存系统，但保持向后兼容
from .cache.legacy import (
    # 旧的类和函数（保持兼容性）
    MemoryCache,
    RedisCache,
    get_cache_client,
    cache_client,
    get_cached_result,
    cache_result,
    delete_cache,
    clear_cache,
    clear_cache_by_prefix,
    clean_expired_cache,
    get_cache_stats,
    # 新的初始化函数
    initialize_cache_system,
    shutdown_cache_system
)

# 导入新的API（可选使用）
from .cache import (
    # 新的核心组件
    CacheManager,
    CacheBackend,
    CacheProfile,
    CacheConfig,
    CacheStats,
    # 新的后端实现
    MemoryCacheBackend,
    RedisCacheBackend,
    # 序列化器
    JsonSerializer,
    PickleSerializer,
    # 装饰器
    cache,
    cache_key_builder,
    # 预定义装饰器
    stock_cache,
    indicator_cache,
    scanner_cache,
    kline_cache,
    user_cache,
    short_cache,
    long_cache,
    api_cache,
    cache_unless_none,
    cache_for_user,
    cache_with_lock
)

# 为了完全向后兼容，保留原有的日志记录
from app.core import logging
logger = logging.getLogger(__name__)

# 兼容性提示
_MIGRATION_WARNING_SHOWN = False

def _show_migration_info():
    """显示迁移信息（只显示一次）"""
    global _MIGRATION_WARNING_SHOWN
    if not _MIGRATION_WARNING_SHOWN:
        logger.info("""
=== 缓存系统升级通知 ===
缓存系统已升级到新版本，具有以下新功能：
1. 多级缓存支持（内存 + Redis）
2. 缓存标签和批量失效
3. 详细的统计和监控
4. 更灵活的配置系统
5. 增强的装饰器

旧的API继续正常工作，建议逐步迁移到新的装饰器：
- 使用 @cache() 替代手动缓存管理
- 使用 @stock_cache, @indicator_cache 等预定义装饰器
- 配置缓存配置文件替代硬编码参数

详细文档请参考：docs/cache-migration.md
=== 升级通知结束 ===
        """)
        _MIGRATION_WARNING_SHOWN = True

# 初始化时显示信息
_show_migration_info()

# 导出所有符号以保持兼容性
__all__ = [
    # === 兼容旧API ===
    'MemoryCache',
    'RedisCache', 
    'get_cache_client',
    'cache_client',
    'get_cached_result',
    'cache_result',
    'delete_cache',
    'clear_cache',
    'clear_cache_by_prefix',
    'clean_expired_cache',
    'get_cache_stats',
    'initialize_cache_system',
    'shutdown_cache_system',
    
    # === 新API ===
    # 核心组件
    'CacheManager',
    'CacheBackend', 
    'CacheProfile',
    'CacheConfig',
    'CacheStats',
    
    # 后端实现
    'MemoryCacheBackend',
    'RedisCacheBackend',
    
    # 序列化器
    'JsonSerializer',
    'PickleSerializer',
    
    # 装饰器
    'cache',
    'cache_key_builder',
    
    # 预定义装饰器
    'stock_cache',
    'indicator_cache', 
    'scanner_cache',
    'kline_cache',
    'user_cache',
    'short_cache',
    'long_cache',
    'api_cache',
    'cache_unless_none',
    'cache_for_user',
    'cache_with_lock'
]