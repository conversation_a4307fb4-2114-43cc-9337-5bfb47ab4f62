"""
数据库连接模块

管理数据库连接、会话和事务。
"""

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from typing import AsyncGenerator
from contextlib import asynccontextmanager

from app.core.config import settings

# 修改数据库URL以支持异步
db_url = settings.DATABASE_URL.replace('sqlite:///', 'sqlite+aiosqlite:///')

# 创建异步引擎
engine = create_async_engine(
    db_url,
    pool_pre_ping=True,
    echo=settings.SQL_ECHO,
    pool_recycle=3600,
)

# 创建异步会话工厂
AsyncSessionLocal = async_sessionmaker(
    engine,
    expire_on_commit=False,
    class_=AsyncSession,
)

# 基本声明类
Base = declarative_base()

async def get_db() -> AsyncSession:
    """
    提供异步数据库会话
    """
    async with AsyncSessionLocal() as session:
        try:
            return session
        finally:
            await session.close()

@asynccontextmanager
async def db_session():
    """
    异步上下文管理器提供数据库会话
    用法:
        async with db_session() as session:
            await session.execute(...)
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()  # 自动提交事务
        except Exception:
            await session.rollback()  # 出错时回滚
            raise
        finally:
            await session.close()
