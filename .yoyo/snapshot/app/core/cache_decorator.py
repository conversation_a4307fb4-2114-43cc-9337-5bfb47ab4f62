"""
缓存装饰器

提供简单易用的缓存装饰器，支持异步函数的自动缓存。
"""
import functools
import hashlib
import json
from typing import Any, Callable, Optional, Union
from app.core import logging
from app.core.cache import cache_result, get_cached_result

logger = logging.getLogger(__name__)


def cache_key_builder(
    func_name: str, 
    args: tuple, 
    kwargs: dict,
    namespace: Optional[str] = None
) -> str:
    """构建缓存键
    
    Args:
        func_name: 函数名
        args: 位置参数
        kwargs: 关键字参数
        namespace: 命名空间前缀
        
    Returns:
        缓存键字符串
    """
    # 构建键的组成部分
    key_parts = []
    
    # 添加命名空间
    if namespace:
        key_parts.append(namespace)
    
    # 添加函数名
    key_parts.append(func_name)
    
    # 处理参数
    # 跳过第一个参数（通常是self或cls）
    if args and len(args) > 1:
        for arg in args[1:]:
            if isinstance(arg, (str, int, float, bool)):
                key_parts.append(str(arg))
            else:
                # 对于复杂对象，使用其字符串表示的哈希值
                key_parts.append(hashlib.md5(str(arg).encode()).hexdigest()[:8])
    
    # 处理关键字参数（按键排序以保证一致性）
    for key in sorted(kwargs.keys()):
        value = kwargs[key]
        if isinstance(value, (str, int, float, bool)):
            key_parts.append(f"{key}:{value}")
        else:
            key_parts.append(f"{key}:{hashlib.md5(str(value).encode()).hexdigest()[:8]}")
    
    return ":".join(key_parts)


def cached(
    expire: int = 3600,
    namespace: Optional[str] = None,
    key_builder: Optional[Callable] = None
):
    """缓存装饰器
    
    Args:
        expire: 缓存过期时间（秒），默认1小时
        namespace: 缓存键的命名空间前缀
        key_builder: 自定义的缓存键构建函数
        
    使用示例:
        @cached(expire=1800, namespace="indicators")
        async def calculate_macd(stock_code: str, start_date: str):
            # 复杂的计算逻辑
            return result
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 构建缓存键
            if key_builder:
                cache_key = key_builder(func.__name__, args, kwargs)
            else:
                cache_key = cache_key_builder(func.__name__, args, kwargs, namespace)
            
            # 尝试从缓存获取
            cached_value = await get_cached_result(cache_key)
            if cached_value is not None:
                logger.debug(f"Cache hit for {cache_key}")
                return cached_value
            
            # 执行函数
            result = await func(*args, **kwargs)
            
            # 缓存结果
            if result is not None:
                await cache_result(cache_key, result, expire)
                logger.debug(f"Cached result for {cache_key}")
            
            return result
        
        # 添加清除缓存的方法
        wrapper.clear_cache = lambda: clear_function_cache(func.__name__, namespace)
        
        return wrapper
    
    return decorator


async def clear_function_cache(func_name: str, namespace: Optional[str] = None) -> int:
    """清除特定函数的所有缓存
    
    Args:
        func_name: 函数名
        namespace: 命名空间前缀
        
    Returns:
        删除的缓存项数量
    """
    from app.core.cache import clear_cache_by_prefix
    
    prefix = f"{namespace}:{func_name}" if namespace else func_name
    return await clear_cache_by_prefix(prefix)


# 预定义的缓存装饰器，用于常见场景

# 股票数据缓存（30分钟）
stock_cache = functools.partial(cached, expire=1800, namespace="stock")

# 指标计算缓存（1小时）
indicator_cache = functools.partial(cached, expire=3600, namespace="indicator")

# K线数据缓存（30分钟）
kline_cache = functools.partial(cached, expire=1800, namespace="kline")

# 短期缓存（5分钟）
short_cache = functools.partial(cached, expire=300, namespace="short")

# 长期缓存（24小时）
long_cache = functools.partial(cached, expire=86400, namespace="long")