"""
缓存统计模块

收集和管理缓存的统计信息，包括命中率、延迟、内存使用等。
"""
import time
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import asyncio
from collections import defaultdict, deque
import statistics


@dataclass
class CacheMetrics:
    """缓存指标数据"""
    hits: int = 0
    misses: int = 0
    sets: int = 0
    deletes: int = 0
    errors: int = 0
    
    # 延迟统计（毫秒）
    get_latencies: deque = field(default_factory=lambda: deque(maxlen=1000))
    set_latencies: deque = field(default_factory=lambda: deque(maxlen=1000))
    
    # 大小统计
    total_size_bytes: int = 0
    key_count: int = 0
    
    # 时间戳
    created_at: datetime = field(default_factory=datetime.now)
    last_reset_at: datetime = field(default_factory=datetime.now)
    
    @property
    def hit_rate(self) -> float:
        """计算命中率"""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0
    
    @property
    def total_requests(self) -> int:
        """总请求数"""
        return self.hits + self.misses
    
    @property
    def avg_get_latency(self) -> float:
        """平均GET延迟（毫秒）"""
        return statistics.mean(self.get_latencies) if self.get_latencies else 0.0
    
    @property
    def avg_set_latency(self) -> float:
        """平均SET延迟（毫秒）"""
        return statistics.mean(self.set_latencies) if self.set_latencies else 0.0
    
    @property
    def p95_get_latency(self) -> float:
        """GET延迟的95分位数"""
        if not self.get_latencies:
            return 0.0
        sorted_latencies = sorted(self.get_latencies)
        index = int(len(sorted_latencies) * 0.95)
        return sorted_latencies[index] if index < len(sorted_latencies) else sorted_latencies[-1]
    
    @property
    def p95_set_latency(self) -> float:
        """SET延迟的95分位数"""
        if not self.set_latencies:
            return 0.0
        sorted_latencies = sorted(self.set_latencies)
        index = int(len(sorted_latencies) * 0.95)
        return sorted_latencies[index] if index < len(sorted_latencies) else sorted_latencies[-1]


class CacheStats:
    """缓存统计管理器"""
    
    def __init__(self):
        # 全局统计
        self.global_metrics = CacheMetrics()
        
        # 按命名空间统计
        self.namespace_metrics: Dict[str, CacheMetrics] = defaultdict(CacheMetrics)
        
        # 按后端统计
        self.backend_metrics: Dict[str, CacheMetrics] = defaultdict(CacheMetrics)
        
        # 热点键统计
        self.hot_keys: deque = deque(maxlen=100)
        self.key_access_count: Dict[str, int] = defaultdict(int)
        
        # 定期清理任务
        self._cleanup_task: Optional[asyncio.Task] = None
        
    def record_hit(self, key: str, latency_ms: float, namespace: Optional[str] = None, backend: Optional[str] = None):
        """记录缓存命中"""
        self.global_metrics.hits += 1
        self.global_metrics.get_latencies.append(latency_ms)
        
        if namespace:
            self.namespace_metrics[namespace].hits += 1
            self.namespace_metrics[namespace].get_latencies.append(latency_ms)
        
        if backend:
            self.backend_metrics[backend].hits += 1
            self.backend_metrics[backend].get_latencies.append(latency_ms)
        
        # 记录热点键
        self.key_access_count[key] += 1
        if self.key_access_count[key] > 10:  # 访问超过10次认为是热点
            if key not in self.hot_keys:
                self.hot_keys.append(key)
    
    def record_miss(self, key: str, latency_ms: float, namespace: Optional[str] = None, backend: Optional[str] = None):
        """记录缓存未命中"""
        self.global_metrics.misses += 1
        self.global_metrics.get_latencies.append(latency_ms)
        
        if namespace:
            self.namespace_metrics[namespace].misses += 1
            self.namespace_metrics[namespace].get_latencies.append(latency_ms)
        
        if backend:
            self.backend_metrics[backend].misses += 1
            self.backend_metrics[backend].get_latencies.append(latency_ms)
    
    def record_set(self, key: str, latency_ms: float, size_bytes: int = 0, namespace: Optional[str] = None, backend: Optional[str] = None):
        """记录缓存设置"""
        self.global_metrics.sets += 1
        self.global_metrics.set_latencies.append(latency_ms)
        self.global_metrics.total_size_bytes += size_bytes
        
        if namespace:
            self.namespace_metrics[namespace].sets += 1
            self.namespace_metrics[namespace].set_latencies.append(latency_ms)
            self.namespace_metrics[namespace].total_size_bytes += size_bytes
        
        if backend:
            self.backend_metrics[backend].sets += 1
            self.backend_metrics[backend].set_latencies.append(latency_ms)
            self.backend_metrics[backend].total_size_bytes += size_bytes
    
    def record_delete(self, key: str, namespace: Optional[str] = None, backend: Optional[str] = None):
        """记录缓存删除"""
        self.global_metrics.deletes += 1
        
        if namespace:
            self.namespace_metrics[namespace].deletes += 1
        
        if backend:
            self.backend_metrics[backend].deletes += 1
    
    def record_error(self, error_type: str, namespace: Optional[str] = None, backend: Optional[str] = None):
        """记录错误"""
        self.global_metrics.errors += 1
        
        if namespace:
            self.namespace_metrics[namespace].errors += 1
        
        if backend:
            self.backend_metrics[backend].errors += 1
    
    def get_summary(self) -> Dict[str, Any]:
        """获取统计摘要"""
        return {
            "global": self._metrics_to_dict(self.global_metrics),
            "namespaces": {ns: self._metrics_to_dict(metrics) for ns, metrics in self.namespace_metrics.items()},
            "backends": {backend: self._metrics_to_dict(metrics) for backend, metrics in self.backend_metrics.items()},
            "hot_keys": list(self.hot_keys)[-20:],  # 最近20个热点键
        }
    
    def _metrics_to_dict(self, metrics: CacheMetrics) -> Dict[str, Any]:
        """将指标转换为字典"""
        uptime = datetime.now() - metrics.created_at
        return {
            "hits": metrics.hits,
            "misses": metrics.misses,
            "hit_rate": f"{metrics.hit_rate:.2%}",
            "total_requests": metrics.total_requests,
            "sets": metrics.sets,
            "deletes": metrics.deletes,
            "errors": metrics.errors,
            "latency": {
                "get": {
                    "avg_ms": f"{metrics.avg_get_latency:.2f}",
                    "p95_ms": f"{metrics.p95_get_latency:.2f}"
                },
                "set": {
                    "avg_ms": f"{metrics.avg_set_latency:.2f}",
                    "p95_ms": f"{metrics.p95_set_latency:.2f}"
                }
            },
            "size": {
                "total_bytes": metrics.total_size_bytes,
                "total_mb": f"{metrics.total_size_bytes / (1024 * 1024):.2f}",
                "key_count": metrics.key_count
            },
            "uptime_seconds": int(uptime.total_seconds())
        }
    
    def reset(self, namespace: Optional[str] = None):
        """重置统计数据"""
        if namespace:
            self.namespace_metrics[namespace] = CacheMetrics()
        else:
            self.global_metrics = CacheMetrics()
            self.namespace_metrics.clear()
            self.backend_metrics.clear()
            self.hot_keys.clear()
            self.key_access_count.clear()
    
    async def start_cleanup_task(self, interval: int = 3600):
        """启动定期清理任务"""
        async def cleanup():
            while True:
                await asyncio.sleep(interval)
                # 清理访问计数，防止内存泄漏
                threshold = 100
                keys_to_remove = [k for k, v in self.key_access_count.items() if v < 5]
                for key in keys_to_remove[:threshold]:  # 每次最多清理100个
                    del self.key_access_count[key]
        
        if self._cleanup_task:
            self._cleanup_task.cancel()
        
        self._cleanup_task = asyncio.create_task(cleanup())
    
    def stop_cleanup_task(self):
        """停止清理任务"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            self._cleanup_task = None


# 全局统计实例
global_cache_stats = CacheStats()