"""
缓存管理器

统一的缓存管理器，支持多级缓存、自动降级等功能。
"""
import asyncio
import time
from typing import Any, Optional, List, Dict, Union
from datetime import timedelta

from .base import CacheBackend
from .stats import CacheStats, global_cache_stats
from .config import CacheProfile, CacheConfig
from .serializers import Serializer, auto_serializer
from app.core import logging

logger = logging.getLogger(__name__)


class CacheManager:
    """统一的缓存管理器"""
    
    def __init__(self, backends: List[CacheBackend], stats: Optional[CacheStats] = None):
        """
        初始化缓存管理器
        
        Args:
            backends: 缓存后端列表，按优先级排序（L1, L2, L3...）
            stats: 缓存统计实例，默认使用全局统计
        """
        self.backends = backends
        self.stats = stats or global_cache_stats
        self._enabled = True
        
        # 健康检查
        self._backend_health: Dict[int, bool] = {i: True for i in range(len(backends))}
        self._health_check_task: Optional[asyncio.Task] = None
    
    async def get(self, key: str, profile: Optional[CacheProfile] = None) -> Optional[Any]:
        """
        从缓存获取值，支持多级缓存
        
        Args:
            key: 缓存键
            profile: 缓存配置
            
        Returns:
            缓存的值，如果不存在则返回None
        """
        if not self._enabled:
            return None
        
        profile = profile or CacheProfile()
        if not profile.enabled:
            return None
        
        final_key = profile.make_key(key)
        start_time = time.time()
        
        # 从多级缓存中查找
        for i, backend in enumerate(self.backends):
            if not self._backend_health[i]:
                continue
            
            try:
                value = await backend.get(final_key)
                if value is not None:
                    # 缓存命中
                    latency_ms = (time.time() - start_time) * 1000
                    self.stats.record_hit(
                        key, latency_ms, 
                        namespace=profile.namespace,
                        backend=backend.__class__.__name__
                    )
                    
                    # 反序列化
                    try:
                        deserialized_value = profile.serializer.deserialize(value) if isinstance(value, bytes) else value
                        
                        # 回填到上层缓存
                        await self._backfill_cache(final_key, value, i, profile)
                        
                        logger.debug(f"Cache hit for {final_key} at level {i}")
                        return deserialized_value
                    except Exception as e:
                        logger.error(f"Failed to deserialize cached value for {final_key}: {e}")
                        # 删除损坏的缓存
                        await backend.delete(final_key)
                        continue
                        
            except Exception as e:
                logger.error(f"Cache backend {i} error: {e}")
                self._backend_health[i] = False
                continue
        
        # 缓存未命中
        latency_ms = (time.time() - start_time) * 1000
        self.stats.record_miss(
            key, latency_ms,
            namespace=profile.namespace
        )
        
        logger.debug(f"Cache miss for {final_key}")
        return None
    
    async def set(self, key: str, value: Any, profile: Optional[CacheProfile] = None) -> bool:
        """
        设置缓存值到所有可用的后端
        
        Args:
            key: 缓存键
            value: 要缓存的值
            profile: 缓存配置
            
        Returns:
            是否至少在一个后端设置成功
        """
        if not self._enabled:
            return False
        
        profile = profile or CacheProfile()
        if not profile.enabled:
            return False
        
        # 条件检查
        if profile.condition and not profile.condition(value):
            return False
        
        final_key = profile.make_key(key)
        start_time = time.time()
        success_count = 0
        
        # 序列化值
        try:
            serialized_value = profile.serializer.serialize(value)
            size_bytes = len(serialized_value) if isinstance(serialized_value, bytes) else 0
        except Exception as e:
            logger.error(f"Failed to serialize value for {final_key}: {e}")
            return False
        
        expire = profile.get_expire_with_variance()
        
        # 写入所有可用的后端
        for i, backend in enumerate(self.backends):
            if not self._backend_health[i]:
                continue
            
            try:
                success = await backend.set(final_key, serialized_value, expire)
                if success:
                    success_count += 1
                    
                    # 添加标签
                    if profile.tags:
                        for tag in profile.tags:
                            await backend.tag(final_key, tag)
                            
            except Exception as e:
                logger.error(f"Cache backend {i} set error: {e}")
                self._backend_health[i] = False
        
        # 记录统计
        latency_ms = (time.time() - start_time) * 1000
        self.stats.record_set(
            key, latency_ms, size_bytes,
            namespace=profile.namespace
        )
        
        is_success = success_count > 0
        if is_success:
            logger.debug(f"Cached {final_key} to {success_count} backends")
        else:
            logger.warning(f"Failed to cache {final_key} to any backend")
        
        return is_success
    
    async def delete(self, key: str, profile: Optional[CacheProfile] = None) -> bool:
        """
        从所有后端删除缓存
        
        Args:
            key: 缓存键
            profile: 缓存配置
            
        Returns:
            是否至少从一个后端删除成功
        """
        if not self._enabled:
            return False
        
        profile = profile or CacheProfile()
        final_key = profile.make_key(key)
        success_count = 0
        
        for i, backend in enumerate(self.backends):
            if not self._backend_health[i]:
                continue
            
            try:
                success = await backend.delete(final_key)
                if success:
                    success_count += 1
            except Exception as e:
                logger.error(f"Cache backend {i} delete error: {e}")
                self._backend_health[i] = False
        
        # 记录统计
        self.stats.record_delete(
            key,
            namespace=profile.namespace
        )
        
        return success_count > 0
    
    async def exists(self, key: str, profile: Optional[CacheProfile] = None) -> bool:
        """检查缓存键是否存在"""
        if not self._enabled:
            return False
        
        profile = profile or CacheProfile()
        if not profile.enabled:
            return False
        
        final_key = profile.make_key(key)
        
        # 检查任一后端
        for i, backend in enumerate(self.backends):
            if not self._backend_health[i]:
                continue
            
            try:
                if await backend.exists(final_key):
                    return True
            except Exception as e:
                logger.error(f"Cache backend {i} exists error: {e}")
                self._backend_health[i] = False
        
        return False
    
    async def clear(self, namespace: Optional[str] = None) -> bool:
        """
        清空缓存
        
        Args:
            namespace: 命名空间，None表示清空所有
            
        Returns:
            是否成功
        """
        success_count = 0
        
        for i, backend in enumerate(self.backends):
            if not self._backend_health[i]:
                continue
            
            try:
                if namespace:
                    # 按前缀删除
                    pattern = f"*{namespace}*"
                    keys = await backend.keys(pattern)
                    if keys:
                        await backend.mdelete(keys)
                else:
                    await backend.clear()
                success_count += 1
            except Exception as e:
                logger.error(f"Cache backend {i} clear error: {e}")
                self._backend_health[i] = False
        
        return success_count > 0
    
    async def invalidate_tag(self, tag: str) -> int:
        """
        使带有指定标签的所有缓存失效
        
        Args:
            tag: 标签名
            
        Returns:
            失效的缓存数量
        """
        total_invalidated = 0
        
        for i, backend in enumerate(self.backends):
            if not self._backend_health[i]:
                continue
            
            try:
                count = await backend.invalidate_tag(tag)
                total_invalidated += count
            except Exception as e:
                logger.error(f"Cache backend {i} invalidate tag error: {e}")
                self._backend_health[i] = False
        
        return total_invalidated
    
    async def _backfill_cache(self, key: str, value: Any, hit_level: int, profile: CacheProfile):
        """回填上层缓存"""
        expire = profile.get_expire_with_variance()
        
        # 只回填到更高优先级的缓存层
        for i in range(hit_level):
            if not self._backend_health[i]:
                continue
            
            try:
                await self.backends[i].set(key, value, expire)
                logger.debug(f"Backfilled cache {key} to level {i}")
            except Exception as e:
                logger.debug(f"Failed to backfill cache to level {i}: {e}")
    
    async def start_health_check(self, interval: int = 60):
        """启动后端健康检查"""
        async def health_check():
            while True:
                await asyncio.sleep(interval)
                await self._check_backend_health()
        
        if self._health_check_task:
            self._health_check_task.cancel()
        
        self._health_check_task = asyncio.create_task(health_check())
    
    async def _check_backend_health(self):
        """检查后端健康状态"""
        for i, backend in enumerate(self.backends):
            try:
                # 简单的健康检查：设置并获取一个测试键
                test_key = f"_health_check_{int(time.time())}"
                await backend.set(test_key, "ok", 10)
                value = await backend.get(test_key)
                await backend.delete(test_key)
                
                if value is not None:
                    if not self._backend_health[i]:
                        logger.info(f"Cache backend {i} recovered")
                    self._backend_health[i] = True
                else:
                    self._backend_health[i] = False
                    
            except Exception as e:
                if self._backend_health[i]:
                    logger.warning(f"Cache backend {i} became unhealthy: {e}")
                self._backend_health[i] = False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        stats = self.stats.get_summary()
        stats["backends"] = {
            f"level_{i}": {
                "class": backend.__class__.__name__,
                "healthy": self._backend_health[i]
            }
            for i, backend in enumerate(self.backends)
        }
        return stats
    
    def enable(self):
        """启用缓存"""
        self._enabled = True
    
    def disable(self):
        """禁用缓存"""
        self._enabled = False
    
    @property
    def enabled(self) -> bool:
        """缓存是否启用"""
        return self._enabled
    
    async def close(self):
        """关闭缓存管理器"""
        if self._health_check_task:
            self._health_check_task.cancel()
        
        # 关闭所有后端
        for backend in self.backends:
            if hasattr(backend, 'close'):
                await backend.close()