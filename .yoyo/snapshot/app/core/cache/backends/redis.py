"""
Redis缓存后端实现

基于Redis的缓存实现，支持集群、持久化等高级特性。
"""
import asyncio
import time
from typing import Any, Optional, List, Dict, Union
from datetime import timedelta

from ..base import CacheBackend
from app.core.config import settings
from app.core import logging

logger = logging.getLogger(__name__)


class RedisCacheBackend(CacheBackend):
    """Redis缓存后端"""
    
    def __init__(self, redis_url: Optional[str] = None, **kwargs):
        """
        初始化Redis缓存
        
        Args:
            redis_url: Redis连接URL
            **kwargs: 其他Redis配置参数
        """
        self.redis_url = redis_url or settings.REDIS_URL
        self.config = {
            "decode_responses": False,  # 使用二进制模式支持序列化
            "max_connections": 50,
            "socket_connect_timeout": 5,
            "socket_timeout": 5,
            "retry_on_timeout": True,
            "health_check_interval": 30,
            **kwargs
        }
        
        self._redis_client = None
        self._connection_pool = None
        self._lock = asyncio.Lock()
        self._connected = False
    
    async def _get_client(self):
        """获取Redis客户端（延迟初始化）"""
        if self._redis_client is None:
            async with self._lock:
                if self._redis_client is None:
                    try:
                        import redis.asyncio as redis
                        from redis.asyncio import ConnectionPool
                        
                        # 创建连接池
                        self._connection_pool = ConnectionPool.from_url(
                            self.redis_url,
                            **self.config
                        )
                        
                        self._redis_client = redis.Redis(
                            connection_pool=self._connection_pool
                        )
                        
                        # 测试连接
                        await self._redis_client.ping()
                        self._connected = True
                        logger.info("Redis连接成功")
                        
                    except ImportError:
                        logger.error("Redis包未安装，请安装redis[hiredis]")
                        raise
                    except Exception as e:
                        logger.error(f"Redis连接失败: {str(e)}")
                        self._redis_client = None
                        self._connected = False
                        raise
        
        return self._redis_client
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        try:
            client = await self._get_client()
            value = await client.get(key)
            return value if value is not None else None
        except Exception as e:
            logger.error(f"Redis GET失败 {key}: {str(e)}")
            self._connected = False
            return None
    
    async def set(self, key: str, value: Any, expire: Optional[Union[int, timedelta]] = None) -> bool:
        """设置缓存值"""
        try:
            client = await self._get_client()
            
            if expire is not None:
                if isinstance(expire, timedelta):
                    expire_seconds = int(expire.total_seconds())
                else:
                    expire_seconds = expire
                
                if expire_seconds > 0:
                    await client.setex(key, expire_seconds, value)
                else:
                    await client.set(key, value)
            else:
                await client.set(key, value)
            
            return True
            
        except Exception as e:
            logger.error(f"Redis SET失败 {key}: {str(e)}")
            self._connected = False
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        try:
            client = await self._get_client()
            result = await client.delete(key)
            return result > 0
        except Exception as e:
            logger.error(f"Redis DELETE失败 {key}: {str(e)}")
            self._connected = False
            return False
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        try:
            client = await self._get_client()
            result = await client.exists(key)
            return result > 0
        except Exception as e:
            logger.error(f"Redis EXISTS失败 {key}: {str(e)}")
            self._connected = False
            return False
    
    async def expire(self, key: str, seconds: Union[int, timedelta]) -> bool:
        """设置键的过期时间"""
        try:
            client = await self._get_client()
            
            if isinstance(seconds, timedelta):
                seconds = int(seconds.total_seconds())
            
            result = await client.expire(key, seconds)
            return result
        except Exception as e:
            logger.error(f"Redis EXPIRE失败 {key}: {str(e)}")
            self._connected = False
            return False
    
    async def ttl(self, key: str) -> int:
        """获取键的剩余生存时间"""
        try:
            client = await self._get_client()
            return await client.ttl(key)
        except Exception as e:
            logger.error(f"Redis TTL失败 {key}: {str(e)}")
            self._connected = False
            return -2  # 键不存在
    
    async def mget(self, keys: List[str]) -> List[Optional[Any]]:
        """批量获取"""
        if not keys:
            return []
        
        try:
            client = await self._get_client()
            values = await client.mget(keys)
            # Redis mget返回的None需要保持
            return [value if value is not None else None for value in values]
        except Exception as e:
            logger.error(f"Redis MGET失败: {str(e)}")
            self._connected = False
            return [None] * len(keys)
    
    async def mset(self, mapping: Dict[str, Any], expire: Optional[Union[int, timedelta]] = None) -> bool:
        """批量设置"""
        if not mapping:
            return True
        
        try:
            client = await self._get_client()
            
            # 使用pipeline提高性能
            async with client.pipeline() as pipe:
                pipe.mset(mapping)
                
                # 如果有过期时间，为每个键设置过期
                if expire is not None:
                    if isinstance(expire, timedelta):
                        expire_seconds = int(expire.total_seconds())
                    else:
                        expire_seconds = expire
                    
                    if expire_seconds > 0:
                        for key in mapping.keys():
                            pipe.expire(key, expire_seconds)
                
                await pipe.execute()
            
            return True
            
        except Exception as e:
            logger.error(f"Redis MSET失败: {str(e)}")
            self._connected = False
            return False
    
    async def mdelete(self, keys: List[str]) -> int:
        """批量删除"""
        if not keys:
            return 0
        
        try:
            client = await self._get_client()
            result = await client.delete(*keys)
            return result
        except Exception as e:
            logger.error(f"Redis MDELETE失败: {str(e)}")
            self._connected = False
            return 0
    
    async def keys(self, pattern: str = "*") -> List[str]:
        """获取匹配的键"""
        try:
            client = await self._get_client()
            
            # 使用SCAN避免阻塞
            keys = []
            cursor = 0
            
            while True:
                cursor, batch_keys = await client.scan(
                    cursor, 
                    match=pattern,
                    count=100
                )
                
                # 解码键名
                keys.extend([key.decode('utf-8') if isinstance(key, bytes) else key for key in batch_keys])
                
                if cursor == 0:
                    break
            
            return keys
            
        except Exception as e:
            logger.error(f"Redis KEYS失败: {str(e)}")
            self._connected = False
            return []
    
    async def clear(self) -> bool:
        """清空所有缓存（慎用）"""
        try:
            client = await self._get_client()
            await client.flushdb()
            return True
        except Exception as e:
            logger.error(f"Redis CLEAR失败: {str(e)}")
            self._connected = False
            return False
    
    async def size(self) -> int:
        """获取数据库中的键数量"""
        try:
            client = await self._get_client()
            return await client.dbsize()
        except Exception as e:
            logger.error(f"Redis SIZE失败: {str(e)}")
            self._connected = False
            return 0
    
    async def incr(self, key: str, amount: int = 1) -> int:
        """原子递增"""
        try:
            client = await self._get_client()
            if amount == 1:
                return await client.incr(key)
            else:
                return await client.incrby(key, amount)
        except Exception as e:
            logger.error(f"Redis INCR失败 {key}: {str(e)}")
            self._connected = False
            raise
    
    async def decr(self, key: str, amount: int = 1) -> int:
        """原子递减"""
        try:
            client = await self._get_client()
            if amount == 1:
                return await client.decr(key)
            else:
                return await client.decrby(key, amount)
        except Exception as e:
            logger.error(f"Redis DECR失败 {key}: {str(e)}")
            self._connected = False
            raise
    
    async def tag(self, key: str, tags: Union[str, List[str]]) -> bool:
        """为键添加标签"""
        if isinstance(tags, str):
            tags = [tags]
        
        try:
            client = await self._get_client()
            
            # 使用集合存储标签关系
            async with client.pipeline() as pipe:
                for tag in tags:
                    tag_key = f"_tag:{tag}"
                    pipe.sadd(tag_key, key)
                    
                    # 为标签键添加标记（用于清理）
                    key_tags_key = f"_key_tags:{key}"
                    pipe.sadd(key_tags_key, tag)
                
                await pipe.execute()
            
            return True
            
        except Exception as e:
            logger.error(f"Redis TAG失败 {key}: {str(e)}")
            self._connected = False
            return False
    
    async def invalidate_tag(self, tag: str) -> int:
        """使带有指定标签的所有缓存失效"""
        try:
            client = await self._get_client()
            tag_key = f"_tag:{tag}"
            
            # 获取所有带该标签的键
            tagged_keys = await client.smembers(tag_key)
            if not tagged_keys:
                return 0
            
            # 解码键名
            tagged_keys = [key.decode('utf-8') if isinstance(key, bytes) else key for key in tagged_keys]
            
            async with client.pipeline() as pipe:
                # 删除所有标记的键
                for key in tagged_keys:
                    pipe.delete(key)
                    # 清理键的标签记录
                    key_tags_key = f"_key_tags:{key}"
                    pipe.delete(key_tags_key)
                
                # 删除标签键
                pipe.delete(tag_key)
                
                results = await pipe.execute()
            
            # 统计实际删除的键数量
            deleted_count = sum(1 for i, result in enumerate(results) if i < len(tagged_keys) and result > 0)
            return deleted_count
            
        except Exception as e:
            logger.error(f"Redis INVALIDATE_TAG失败 {tag}: {str(e)}")
            self._connected = False
            return 0
    
    async def close(self):
        """关闭Redis连接"""
        if self._redis_client:
            try:
                await self._redis_client.close()
            except:
                pass
            self._redis_client = None
        
        if self._connection_pool:
            try:
                await self._connection_pool.disconnect()
            except:
                pass
            self._connection_pool = None
        
        self._connected = False
    
    @property
    def connected(self) -> bool:
        """检查是否连接"""
        return self._connected
    
    async def ping(self) -> bool:
        """测试连接"""
        try:
            client = await self._get_client()
            await client.ping()
            self._connected = True
            return True
        except Exception as e:
            logger.error(f"Redis PING失败: {str(e)}")
            self._connected = False
            return False
    
    async def info(self) -> Dict[str, Any]:
        """获取Redis信息"""
        try:
            client = await self._get_client()
            info = await client.info()
            
            # 提取有用的信息
            return {
                "redis_version": info.get("redis_version", "unknown"),
                "used_memory": info.get("used_memory", 0),
                "used_memory_human": info.get("used_memory_human", "0B"),
                "connected_clients": info.get("connected_clients", 0),
                "total_commands_processed": info.get("total_commands_processed", 0),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0),
                "uptime_in_seconds": info.get("uptime_in_seconds", 0)
            }
        except Exception as e:
            logger.error(f"Redis INFO失败: {str(e)}")
            self._connected = False
            return {}