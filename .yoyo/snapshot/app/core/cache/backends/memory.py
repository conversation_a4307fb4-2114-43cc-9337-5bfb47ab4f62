"""
内存缓存后端实现

基于内存的缓存实现，支持LRU淘汰策略。
"""
import time
import asyncio
from typing import Any, Optional, List, Dict, Set, Union
from datetime import timedelta
from collections import OrderedDict
import threading
import sys

from ..base import CacheBackend
from app.core import logging

logger = logging.getLogger(__name__)


class MemoryCacheBackend(CacheBackend):
    """内存缓存后端"""
    
    def __init__(self, max_size: int = 1000, max_memory_mb: int = 100, eviction_policy: str = "lru"):
        """
        初始化内存缓存
        
        Args:
            max_size: 最大键数量
            max_memory_mb: 最大内存使用量（MB）
            eviction_policy: 淘汰策略 ("lru", "fifo", "random")
        """
        self.max_size = max_size
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.eviction_policy = eviction_policy
        
        # 存储结构：{key: {"value": value, "expires_at": timestamp, "size": bytes}}
        self._cache: OrderedDict[str, Dict[str, Any]] = OrderedDict()
        self._lock = threading.RLock()
        
        # 统计信息
        self._current_memory = 0
        self._access_times: Dict[str, float] = {}  # 用于LRU
        
        # 标签支持
        self._tags: Dict[str, Set[str]] = {}  # tag -> set of keys
        self._key_tags: Dict[str, Set[str]] = {}  # key -> set of tags
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            if key not in self._cache:
                return None
            
            cache_item = self._cache[key]
            
            # 检查过期
            if cache_item["expires_at"] and cache_item["expires_at"] < time.time():
                self._remove_key(key)
                return None
            
            # 更新访问时间（LRU）
            if self.eviction_policy == "lru":
                self._access_times[key] = time.time()
                # 移动到末尾
                self._cache.move_to_end(key)
            
            return cache_item["value"]
    
    async def set(self, key: str, value: Any, expire: Optional[Union[int, timedelta]] = None) -> bool:
        """设置缓存值"""
        # 计算过期时间
        expires_at = None
        if expire is not None:
            if isinstance(expire, timedelta):
                expire_seconds = int(expire.total_seconds())
            else:
                expire_seconds = expire
            
            if expire_seconds > 0:
                expires_at = time.time() + expire_seconds
        
        # 估算值的大小
        value_size = self._estimate_size(value)
        
        with self._lock:
            # 如果键已存在，先删除旧的
            if key in self._cache:
                self._remove_key(key)
            
            # 检查是否需要淘汰
            while (
                (len(self._cache) >= self.max_size or 
                 self._current_memory + value_size > self.max_memory_bytes) and
                len(self._cache) > 0
            ):
                self._evict_one()
            
            # 如果单个值太大，不缓存
            if value_size > self.max_memory_bytes:
                logger.warning(f"Value too large to cache: {value_size} bytes")
                return False
            
            # 设置缓存
            self._cache[key] = {
                "value": value,
                "expires_at": expires_at,
                "size": value_size,
                "created_at": time.time()
            }
            
            self._current_memory += value_size
            
            if self.eviction_policy == "lru":
                self._access_times[key] = time.time()
            
            return True
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        with self._lock:
            if key in self._cache:
                self._remove_key(key)
                return True
            return False
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        with self._lock:
            if key not in self._cache:
                return False
            
            cache_item = self._cache[key]
            # 检查过期
            if cache_item["expires_at"] and cache_item["expires_at"] < time.time():
                self._remove_key(key)
                return False
            
            return True
    
    async def expire(self, key: str, seconds: Union[int, timedelta]) -> bool:
        """设置键的过期时间"""
        if isinstance(seconds, timedelta):
            seconds = int(seconds.total_seconds())
        
        with self._lock:
            if key in self._cache:
                expires_at = time.time() + seconds if seconds > 0 else None
                self._cache[key]["expires_at"] = expires_at
                return True
            return False
    
    async def ttl(self, key: str) -> int:
        """获取键的剩余生存时间"""
        with self._lock:
            if key not in self._cache:
                return -2  # 键不存在
            
            cache_item = self._cache[key]
            if not cache_item["expires_at"]:
                return -1  # 永不过期
            
            remaining = int(cache_item["expires_at"] - time.time())
            return max(0, remaining)
    
    async def mget(self, keys: List[str]) -> List[Optional[Any]]:
        """批量获取"""
        return [await self.get(key) for key in keys]
    
    async def mset(self, mapping: Dict[str, Any], expire: Optional[Union[int, timedelta]] = None) -> bool:
        """批量设置"""
        results = []
        for key, value in mapping.items():
            result = await self.set(key, value, expire)
            results.append(result)
        return all(results)
    
    async def mdelete(self, keys: List[str]) -> int:
        """批量删除"""
        count = 0
        for key in keys:
            if await self.delete(key):
                count += 1
        return count
    
    async def keys(self, pattern: str = "*") -> List[str]:
        """获取匹配的键"""
        import fnmatch
        
        with self._lock:
            # 清理过期键
            self._cleanup_expired()
            
            if pattern == "*":
                return list(self._cache.keys())
            
            # 使用fnmatch进行模式匹配
            return [key for key in self._cache.keys() if fnmatch.fnmatch(key, pattern)]
    
    async def clear(self) -> bool:
        """清空所有缓存"""
        with self._lock:
            self._cache.clear()
            self._access_times.clear()
            self._tags.clear()
            self._key_tags.clear()
            self._current_memory = 0
            return True
    
    async def size(self) -> int:
        """获取键数量"""
        with self._lock:
            self._cleanup_expired()
            return len(self._cache)
    
    async def incr(self, key: str, amount: int = 1) -> int:
        """原子递增"""
        with self._lock:
            current_value = await self.get(key) or 0
            if not isinstance(current_value, (int, float)):
                raise ValueError(f"Value at key '{key}' is not numeric")
            
            new_value = int(current_value) + amount
            await self.set(key, new_value)
            return new_value
    
    async def decr(self, key: str, amount: int = 1) -> int:
        """原子递减"""
        return await self.incr(key, -amount)
    
    async def tag(self, key: str, tags: Union[str, List[str]]) -> bool:
        """为键添加标签"""
        if isinstance(tags, str):
            tags = [tags]
        
        with self._lock:
            if key not in self._cache:
                return False
            
            for tag in tags:
                if tag not in self._tags:
                    self._tags[tag] = set()
                self._tags[tag].add(key)
                
                if key not in self._key_tags:
                    self._key_tags[key] = set()
                self._key_tags[key].add(tag)
            
            return True
    
    async def invalidate_tag(self, tag: str) -> int:
        """使带有指定标签的所有缓存失效"""
        with self._lock:
            if tag not in self._tags:
                return 0
            
            tagged_keys = self._tags[tag].copy()
            count = 0
            
            for key in tagged_keys:
                if key in self._cache:
                    self._remove_key(key)
                    count += 1
            
            # 清理标签
            del self._tags[tag]
            
            return count
    
    def _remove_key(self, key: str):
        """内部方法：删除键和相关数据"""
        if key in self._cache:
            cache_item = self._cache[key]
            self._current_memory -= cache_item["size"]
            del self._cache[key]
        
        if key in self._access_times:
            del self._access_times[key]
        
        # 清理标签关联
        if key in self._key_tags:
            for tag in self._key_tags[key]:
                if tag in self._tags:
                    self._tags[tag].discard(key)
                    if not self._tags[tag]:  # 标签下没有键了
                        del self._tags[tag]
            del self._key_tags[key]
    
    def _evict_one(self):
        """淘汰一个键"""
        if not self._cache:
            return
        
        if self.eviction_policy == "lru":
            # 找到最少使用的键
            oldest_key = min(self._access_times.keys(), key=lambda k: self._access_times[k])
        elif self.eviction_policy == "fifo":
            # 先进先出
            oldest_key = next(iter(self._cache))
        else:  # random
            import random
            oldest_key = random.choice(list(self._cache.keys()))
        
        self._remove_key(oldest_key)
        logger.debug(f"Evicted key: {oldest_key}")
    
    def _cleanup_expired(self):
        """清理过期的键"""
        now = time.time()
        expired_keys = []
        
        for key, cache_item in self._cache.items():
            if cache_item["expires_at"] and cache_item["expires_at"] < now:
                expired_keys.append(key)
        
        for key in expired_keys:
            self._remove_key(key)
    
    def _estimate_size(self, value: Any) -> int:
        """估算值的内存大小"""
        try:
            if isinstance(value, (str, bytes)):
                return len(value)
            elif isinstance(value, (int, float, bool)):
                return sys.getsizeof(value)
            elif isinstance(value, (list, tuple, dict, set)):
                return sys.getsizeof(value) + sum(self._estimate_size(item) for item in (value if not isinstance(value, dict) else value.values()))
            else:
                return sys.getsizeof(value)
        except:
            return 1024  # 默认1KB
    
    def get_memory_info(self) -> Dict[str, Any]:
        """获取内存使用信息"""
        with self._lock:
            self._cleanup_expired()
            return {
                "current_memory_bytes": self._current_memory,
                "current_memory_mb": self._current_memory / (1024 * 1024),
                "max_memory_mb": self.max_memory_bytes / (1024 * 1024),
                "memory_usage_percent": (self._current_memory / self.max_memory_bytes) * 100,
                "key_count": len(self._cache),
                "max_size": self.max_size,
                "size_usage_percent": (len(self._cache) / self.max_size) * 100,
                "eviction_policy": self.eviction_policy
            }