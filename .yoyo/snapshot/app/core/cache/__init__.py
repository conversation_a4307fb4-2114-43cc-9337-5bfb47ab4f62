"""
统一缓存系统模块

提供统一的缓存抽象层，支持多种缓存后端（内存、Redis等），
实现多级缓存、批量操作、监控统计等高级功能。
"""
from .base import CacheBackend
from .manager import CacheManager
from .stats import CacheStats
from .serializers import Json<PERSON>erial<PERSON>, PickleSerializer
from .backends.memory import MemoryCacheBackend
from .backends.redis import RedisCacheBackend
from .decorators import cache, cache_key_builder, stock_cache, indicator_cache, scanner_cache
from .config import CacheConfig, CacheProfile

# 为了向后兼容，保留原有的API
from .legacy import (
    get_cached_result,
    cache_result,
    delete_cache,
    clear_cache,
    clear_cache_by_prefix,
    clean_expired_cache,
    get_cache_stats,
    initialize_cache_system,
    shutdown_cache_system,
    MemoryCache,
    RedisCache,
    get_cache_client,
    cache_client
)

__all__ = [
    # 新API
    'CacheBackend',
    'CacheManager',
    'CacheStats',
    'JsonSerializer',
    'PickleSerializer',
    'MemoryCacheBackend',
    'RedisCacheBackend',
    'cache',
    'cache_key_builder',
    'stock_cache',
    'indicator_cache',
    'scanner_cache',
    'CacheConfig',
    'CacheProfile',
    # 兼容旧API
    'get_cached_result',
    'cache_result',
    'delete_cache',
    'clear_cache',
    'clear_cache_by_prefix',
    'clean_expired_cache',
    'get_cache_stats',
    'initialize_cache_system',
    'shutdown_cache_system',
    'MemoryCache',
    'RedisCache',
    'get_cache_client',
    'cache_client'
]