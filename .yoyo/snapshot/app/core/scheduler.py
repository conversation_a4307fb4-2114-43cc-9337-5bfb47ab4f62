"""
任务调度模块

管理定时任务的调度和执行。
"""

import importlib
import pkgutil
from pathlib import Path
from typing import Any, Callable, Dict, Optional, List, Set
import pytz
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.job import Job
from functools import wraps

from app.core import logging
from app.core.config import settings

logger = logging.getLogger(__name__)

def discover_and_load_tasks(package_name: str) -> None:
    """
    自动发现并导入指定包下的所有模块，以触发装饰器注册
    
    Args:
        package_name: 要扫描的包名，如 'app.tasks'
    """
    logger.info(f"开始扫描任务模块: {package_name}")
    
    try:
        package = importlib.import_module(package_name)
        package_path = Path(package.__file__).parent # type: ignore
        
        # 遍历包下的所有模块
        for _, module_name, is_pkg in pkgutil.iter_modules([str(package_path)]):
            full_module_name = f"{package_name}.{module_name}"
            
            try:
                # 导入模块以触发装饰器执行
                importlib.import_module(full_module_name)
                logger.debug(f"已加载任务模块: {full_module_name}")
                
                # 如果是子包，递归扫描
                if is_pkg:
                    discover_and_load_tasks(full_module_name)
            except Exception as e:
                logger.error(f"加载模块 {full_module_name} 失败: {str(e)}")
                
        logger.info(f"完成扫描任务模块: {package_name}")
    except Exception as e:
        logger.error(f"扫描任务包 {package_name} 失败: {str(e)}")  

# 存储所有通过装饰器注册的任务
_scheduled_tasks: List[Dict[str, Any]] = []

class ScheduledTask:
    """定时任务装饰器"""
    
    # 用于缓存服务类实例的字典
    _service_instances = {}
    
    # 在 ScheduledTask 类中添加一个字典来存储原始函数
    _original_methods = {}

    def __init__(self, cron: str, task_id: str = '', replace_existing: bool = True):
        """
        初始化定时任务装饰器
        
        Args:
            cron: cron表达式
            task_id: 任务ID，如果为None则使用函数全名
            replace_existing: 是否替换已存在的同名任务
        """
        self.cron = cron
        self.task_id = task_id
        self.replace_existing = replace_existing
    
    def __call__(self, func: Callable):
        """装饰器实现"""
        task_id = self.task_id or f"{func.__module__}.{func.__qualname__}"
        
        # 存储原始函数引用
        self._original_methods[task_id] = func
        
        # 检查是否为实例方法或类方法
        is_method = hasattr(func, "__qualname__") and "." in func.__qualname__
        
        if is_method:
            # 获取类名
            class_name = func.__qualname__.split('.')[0]
            module_name = func.__module__
            
            # 创建包装函数处理类方法
            @wraps(func)
            async def method_wrapper(*args, **kwargs):
                # 尝试获取或创建类的实例
                instance_key = f"{module_name}.{class_name}"
                
                if instance_key not in self._service_instances:
                    try:
                        # 导入类并创建实例
                        module = importlib.import_module(module_name)
                        cls = getattr(module, class_name)
                        self._service_instances[instance_key] = cls()
                        logger.info(f"已创建服务实例: {instance_key}")
                    except Exception as e:
                        logger.error(f"创建服务实例失败: {instance_key}, 错误: {str(e)}")
                        raise
                
                # 使用缓存的实例
                instance = self._service_instances[instance_key]
                
                # 直接使用存储的原始函数，而不是通过 getattr 获取
                original_func = self._original_methods[task_id]
                logger.debug(f"调用原始方法: {instance_key}.{func.__name__}")
                return await original_func(instance, *args, **kwargs)
            
            wrapped_func = method_wrapper
        else:
            # 普通函数直接包装
            @wraps(func)
            def function_wrapper(*args, **kwargs):
                return func(*args, **kwargs)
            
            wrapped_func = function_wrapper
        
        # 记录被装饰的函数信息
        _scheduled_tasks.append({
            "func": wrapped_func,  # 使用包装后的函数
            "cron": self.cron,
            "task_id": task_id,
            "replace_existing": self.replace_existing
        })
        
        return wrapped_func


class Scheduler:
    """任务调度器类"""
    
    def __init__(self):
        self.scheduler = AsyncIOScheduler(
            timezone=pytz.timezone('UTC'),  # 使用UTC时区
            job_defaults={
                'misfire_grace_time': None
            }
        )
        self._jobs: Dict[str, Job] = {}
    
    def start(self) -> None:
        """启动调度器"""
        if settings.SCHEDULER_ENABLED:
            self.scheduler.start()
            logger.info("任务调度器已启动")
    
    def shutdown(self) -> None:
        """关闭调度器"""
        self.scheduler.shutdown()
        logger.info("任务调度器已关闭")
    
    def add_job(
        self,
        func: Callable,
        job_id: str,
        cron: str,
        args: Optional[tuple] = None,
        kwargs: Optional[dict] = None,
        replace_existing: bool = True
    ) -> Optional[Job]:
        """
        添加定时任务
        
        Args:
            func: 要执行的函数
            job_id: 任务ID
            cron: cron表达式
            args: 函数参数
            kwargs: 函数关键字参数
            replace_existing: 是否替换已存在的同名任务
            
        Returns:
            Optional[Job]: 任务实例
        """
        if not settings.SCHEDULER_ENABLED:
            logger.warning(f"调度器未启用，跳过添加任务: {job_id}")
            return None
            
        try:
            job = self.scheduler.add_job(
                func=func,
                trigger=CronTrigger.from_crontab(cron),
                args=args or (),
                kwargs=kwargs or {},
                id=job_id,
                replace_existing=replace_existing,
                timezone=pytz.timezone('UTC')  # 使用UTC时区
            )
            self._jobs[job_id] = job
            logger.info(f"已添加定时任务: {job_id}, cron: {cron}")
            return job
        except Exception as e:
            logger.error(f"添加任务失败: {job_id}, 错误: {str(e)}")
            return None
    
    def remove_job(self, job_id: str) -> bool:
        """
        移除定时任务
        
        Args:
            job_id: 任务ID
            
        Returns:
            bool: 是否成功移除
        """
        try:
            self.scheduler.remove_job(job_id)
            self._jobs.pop(job_id, None)
            logger.info(f"已移除定时任务: {job_id}")
            return True
        except Exception as e:
            logger.error(f"移除任务失败: {job_id}, 错误: {str(e)}")
            return False
    
    def get_job(self, job_id: str) -> Optional[Job]:
        """
        获取任务实例
        
        Args:
            job_id: 任务ID
            
        Returns:
            Optional[Job]: 任务实例
        """
        return self._jobs.get(job_id)
    
    def modify_job(
        self,
        job_id: str,
        cron: Optional[str] = None,
        args: Optional[tuple] = None,
        kwargs: Optional[dict] = None
    ) -> bool:
        """
        修改任务配置
        
        Args:
            job_id: 任务ID
            cron: 新的cron表达式
            args: 新的函数参数
            kwargs: 新的函数关键字参数
            
        Returns:
            bool: 是否成功修改
        """
        try:
            job = self.get_job(job_id)
            if not job:
                logger.error(f"任务不存在: {job_id}")
                return False
                
            if cron:
                job.reschedule(trigger=CronTrigger.from_crontab(cron, timezone=pytz.timezone('UTC')))
                
            if args is not None:
                job.modify(args=args)
                
            if kwargs is not None:
                job.modify(kwargs=kwargs)
                
            logger.info(f"已修改任务配置: {job_id}")
            return True
        except Exception as e:
            logger.error(f"修改任务失败: {job_id}, 错误: {str(e)}")
            return False

    def register_annotated_tasks(self) -> Set[str]:
        """
        注册所有使用@ScheduledTask装饰器定义的任务
        
        Returns:
            Set[str]: 成功注册的任务ID集合
        """
        registered_tasks = set()
        logger.info("开始注册通过注解定义的定时任务...")
        for task in _scheduled_tasks:
            job = self.add_job(
                func=task["func"],
                job_id=task["task_id"],
                cron=task["cron"],
                replace_existing=task["replace_existing"]
            )
            
            if job:
                registered_tasks.add(task["task_id"])
                
        logger.info(f"已注册{len(registered_tasks)}个通过注解定义的定时任务")
        return registered_tasks

    def auto_discover_and_register_tasks(self, packages: List[str]) -> Set[str]:
        """
        自动发现并注册所有使用 @ScheduledTask 装饰的任务
        
        Args:
            packages: 要扫描的包名列表，例如 ["app.tasks", "app.jobs"]
            
        Returns:
            Set[str]: 成功注册的任务ID集合
        """
        # 先扫描并导入所有模块，触发装饰器执行
        for package in packages:
            discover_and_load_tasks(package)
            
        # 然后注册收集到的所有任务
        return self.register_annotated_tasks()

# 创建全局调度器实例
scheduler = Scheduler()
