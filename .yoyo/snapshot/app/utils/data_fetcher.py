"""
股票数据获取工具类
提供统一的数据获取和预处理功能，支持跨分区表查询
用于减轻service层的负担，分离非业务代码
"""
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
from app.services.storage.stock_storage import StockStorageService
from app.core.exceptions import ValidationException
from app.core.logging import logging
from app.core.config import settings

# 使用延迟导入避免循环引用问题
from functools import lru_cache

logger = logging.getLogger(__name__)

class StockDataFetcher:
    """股票数据获取工具类"""
    
    def __init__(self, storage_service: StockStorageService):
        """初始化数据获取工具
        
        Args:
            storage_service: 股票数据存储服务
        """
        self.storage = storage_service
        self._data_provider = None  # 延迟初始化
        
    async def _get_data_provider(self):
        """延迟初始化数据提供者"""
        if self._data_provider is None:
            from app.services.data_fetcher.factory import DataFetcherFactory
            provider_type = settings.DATA_API_TYPE
            if provider_type == "tushare" and settings.TUSHARE_TOKEN:
                self._data_provider = DataFetcherFactory.get_fetcher(
                    provider_type, api_token=settings.TUSHARE_TOKEN
                )
            elif provider_type == "mairui" and settings.MAIRUI_TOKEN:
                self._data_provider = DataFetcherFactory.get_fetcher(
                    provider_type, licence=settings.MAIRUI_TOKEN
                )
            else:
                self._data_provider = DataFetcherFactory.get_fetcher(provider_type)
                
        return self._data_provider
    
    async def fetch_stock_data(
        self,
        stock_code: str,
        start_date: Union[str, datetime],
        end_date: Union[str, datetime],
        min_periods: Optional[int] = None,  # 改为可选参数
        extend_days: int = 0,    # 扩展查询的天数，用于技术指标计算
        use_provider_fallback: bool = True  # 是否在数据库查询失败时使用数据提供者
    ) -> pd.DataFrame:
        """获取股票日线数据并转换为DataFrame
        
        处理日期范围、跨分区表查询、数据类型转换等
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            min_periods: 最小需要的数据周期数，如果不指定，则使用时间范围内的工作日数量
            extend_days: 向前扩展查询的天数，用于技术指标计算
            use_provider_fallback: 是否在数据库查询失败时使用数据提供者获取数据
            
        Returns:
            DataFrame: 股票日线数据，包含OHLCV等信息
        """
        try:
            # 1. 标准化日期格式
            start_date_dt, end_date_dt = self._normalize_dates(start_date, end_date)
            
            # 2. 计算扩展日期和预期数据量
            original_start_date, extended_start_date, expected_days, required_periods = self._calculate_extended_date(
                start_date_dt, end_date_dt, min_periods, extend_days
            )
            
            # 3. 准备查询参数
            extended_start = extended_start_date.strftime("%Y-%m-%d") if isinstance(start_date, str) else extended_start_date
            
            # 4. 从数据库查询数据
            daily_data, db_query_failed, data_insufficient, data_in_range = await self._query_db_data(
                stock_code, extended_start, end_date, start_date_dt, end_date_dt, expected_days
            )
            
            # 5. 如果需要，从数据提供者获取补充数据
            if (not daily_data or db_query_failed or data_insufficient) and use_provider_fallback:
                daily_data = await self._fetch_from_provider(
                    stock_code, extended_start_date, end_date_dt, 
                    daily_data, data_insufficient, expected_days, data_in_range
                )
            
            # 6. 检查最终数据
            if not daily_data:
                raise ValidationException(f"未能从数据库或数据提供者获取股票 {stock_code} 在指定时间范围的数据")
            
            # 7. 转换为DataFrame并进行后处理
            df = self._convert_to_dataframe(daily_data, expected_days)
            
            if df.empty:
                return pd.DataFrame()
                
            # 8. 按用户请求的原始日期范围过滤数据
            df = self._filter_by_date_range(df, original_start_date)
            
            return df
            
        except ValidationException:
            raise  # 直接抛出已经处理过的异常
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 数据时发生错误: {str(e)}", exc_info=True)
            raise ValidationException(f"处理股票 {stock_code} 数据时出错: {str(e)}")
    
    async def _convert_provider_data(self, stock_code: str, provider_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """将数据提供者返回的数据格式转换为数据库模型格式
        
        Args:
            stock_code: 股票代码
            provider_data: 数据提供者返回的数据
            
        Returns:
            List[Dict[str, Any]]: 转换后的数据，可用于保存到数据库
        """
        converted_data = []
        try:
            for item in provider_data:
                # 处理日期格式
                if 'trade_date' in item:
                    trade_date = item['trade_date']
                    if isinstance(trade_date, str):
                        # 如果是字符串，尝试转换
                        try:
                            trade_date = pd.to_datetime(trade_date).to_pydatetime()
                        except Exception:
                            # 如果转换失败，跳过此条记录
                            logger.warning(f"无法解析日期格式: {trade_date}")
                            continue
                else:
                    # 没有日期字段，跳过
                    continue
                
                # 安全转换函数，确保numpy类型被转换为Python原生类型
                def safe_convert(value):
                    if value is None:
                        return None
                    # 处理numpy类型
                    if hasattr(value, 'dtype') and hasattr(value, 'item'):
                        try:
                            return value.item()  # 将numpy标量转换为Python标量
                        except (ValueError, AttributeError):
                            pass
                    # 处理其他类型
                    if isinstance(value, (int, float, bool, str)):
                        return value
                    try:
                        # 尝试转换为float，这可以处理大多数数值类型
                        return float(value)
                    except (TypeError, ValueError):
                        # 无法转换，返回字符串表示
                        return str(value)
                
                # 创建标准格式字典，所有数值都经过安全转换
                converted_item = {
                    'stock_code': stock_code,
                    'trade_date': trade_date,
                    'open': safe_convert(item.get('open', 0)),
                    'high': safe_convert(item.get('high', 0)),
                    'low': safe_convert(item.get('low', 0)),
                    'close': safe_convert(item.get('close', 0)),
                    'volume': safe_convert(item.get('volume', 0)),
                    'amount': safe_convert(item.get('amount')) if 'amount' in item else None,
                    'change_pct': safe_convert(item.get('change_pct')) if 'change_pct' in item else None,
                    'turnover_rate': safe_convert(item.get('turnover')) if 'turnover' in item else 
                                   (safe_convert(item.get('turnover_rate')) if 'turnover_rate' in item else None)
                }
                converted_data.append(converted_item)
        except Exception as e:
            logger.error(f"转换数据格式失败: {str(e)}", exc_info=True)  # 添加异常栈跟踪
        
        return converted_data
    
    def _save_data_to_db_async(self, stock_code: str, data: List[Dict[str, Any]]):
        """异步将数据保存到数据库中并确保创建必要的分区表（不阻塞主流程）

        Args:
            stock_code: 股票代码
            data: 要保存的数据
        """
        import asyncio

        async def _save_task():
            if not data:
                return

            try:
                # 按分区键（例如 YYYYMM）分组数据
                partition_groups = {}
                from app.models.stock import StockDailyBase # Import here to access _time_format
                time_format = StockDailyBase._time_format

                for item in data:
                    trade_date = item.get('trade_date')
                    if not trade_date:
                        continue
                    # Ensure trade_date is a datetime object for formatting
                    if isinstance(trade_date, str):
                        try:
                            trade_date = datetime.strptime(trade_date, "%Y-%m-%d")
                        except ValueError:
                             try:
                                 trade_date = datetime.fromisoformat(trade_date)
                             except ValueError:
                                 logger.warning(f"无法解析日期格式: {trade_date} for stock {stock_code}")
                                 continue
                    elif not isinstance(trade_date, datetime):
                         # Handle other potential types like date objects
                         try:
                             trade_date = datetime.combine(trade_date, datetime.min.time())
                         except TypeError:
                             logger.warning(f"无法处理的日期类型: {type(trade_date)} for stock {stock_code}")
                             continue

                    partition_key = trade_date.strftime(time_format)
                    if partition_key not in partition_groups:
                        partition_groups[partition_key] = []
                    partition_groups[partition_key].append(item)

                # 跟踪已尝试创建的分区表
                created_partitions = set()

                # 分别处理每个分区的数据
                for partition_key, items in partition_groups.items():
                    if not items:
                        continue

                    # 使用该分区中的第一个日期来获取分区类和创建表
                    first_trade_date = items[0]['trade_date']
                    # Ensure first_trade_date is datetime for get_partition
                    if not isinstance(first_trade_date, datetime):
                         if isinstance(first_trade_date, str):
                             try:
                                 first_trade_date = datetime.strptime(first_trade_date, "%Y-%m-%d")
                             except ValueError:
                                 first_trade_date = datetime.fromisoformat(first_trade_date)
                         else: # Assume date object
                             first_trade_date = datetime.combine(first_trade_date, datetime.min.time())

                    partition_cls = StockDailyBase.get_partition(first_trade_date)
                    table_name = partition_cls.__tablename__

                    try:
                        # 仅在尚未尝试创建此分区表时才尝试创建
                        if table_name not in created_partitions:
                            try:
                                await self.storage.db.run_sync(
                                    lambda session: partition_cls.create_table(session.bind, checkfirst=True)
                                )
                                logger.info(f"为分区键 {partition_key} (表: {table_name}) 创建/检查分区表")
                                created_partitions.add(table_name) # 记录已尝试创建
                            except Exception as table_err:
                                err_msg = str(table_err).lower()
                                if "already exists" not in err_msg and "已存在" not in err_msg and "duplicate" not in err_msg:
                                    logger.error(f"创建分区表 {table_name} 失败: {str(table_err)}")
                                    # Consider if we should continue saving if table creation fails
                                else:
                                    # 表已存在，也记录下来，避免重复日志
                                    created_partitions.add(table_name)
                                    logger.debug(f"分区表 {table_name} 已存在")

                        # 批量保存当前分区的数据
                        # 注意：batch_save_stock_daily 需要能处理好分区逻辑，或者我们在这里传入分区类
                        # 假设 storage.batch_save_stock_daily 内部会根据日期确定分区或接受分区类
                        # 如果它只接受日期，我们需要确保它使用正确的日期来确定分区
                        # 传递 first_trade_date 作为分区提示
                        await self.storage.batch_save_stock_daily(stock_code, items, first_trade_date)
                        logger.info(f"成功保存股票 {stock_code} 在分区 {partition_key} 的 {len(items)} 条数据")

                    except Exception as e:
                        logger.error(f"保存股票 {stock_code} 在分区 {partition_key} 的数据失败: {str(e)}", exc_info=True)
                        # 继续处理其他分区组

            except Exception as e:
                logger.error(f"保存数据到数据库失败 (stock: {stock_code}): {str(e)}", exc_info=True)

        # 创建任务但不等待其完成
        asyncio.create_task(_save_task())
    
    async def _get_data_across_partitions(
        self, 
        stock_code: str, 
        start_date: datetime,
        end_date: datetime
    ) -> list:
        """跨分区表获取数据
        
        将时间范围按年拆分，分别查询不同的分区表，合并结果
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            list: 合并后的数据列表
        """
        # 存储所有查询结果
        all_results = []
        
        try:
            # 计算开始年份和结束年份
            start_year = start_date.year
            end_year = end_date.year
            current_year = datetime.now().year
            
            # 确保年份范围合理
            start_year = max(start_year, 1990)  # 股市数据一般从1990年开始
            end_year = min(end_year, current_year)
            
            # 按年查询数据
            for year in range(start_year, end_year + 1):
                try:
                    # 计算当年的开始日期和结束日期
                    year_start = max(start_date, datetime(year, 1, 1))
                    year_end = min(end_date, datetime(year, 12, 31))
                    
                    # 确保开始日期小于等于结束日期
                    if year_start > year_end:
                        continue
                    
                    # 安全地获取日期字符串表示
                    start_str = year_start
                    if hasattr(year_start, 'date'):
                        start_str = year_start.date()
                    
                    end_str = year_end
                    if hasattr(year_end, 'date'):
                        end_str = year_end.date()
                        
                    # 查询当年的数据
                    logger.info(f"查询股票 {stock_code} 在 {start_str} 至 {end_str} 的数据")
                    year_data = await self.storage.get_stock_daily(stock_code, year_start, year_end)
                    
                    if year_data:
                        all_results.extend(year_data)
                        
                except Exception as e:
                    # 忽略单个年份查询失败的错误，记录日志
                    logger.warning(f"查询股票 {stock_code} 在 {year} 年的数据失败: {str(e)}")
                    continue
        except Exception as e:
            logger.error(f"跨分区表获取数据时发生错误: {str(e)}", exc_info=True)  # 添加详细错误栈信息
        
        # 返回合并的结果
        return all_results
    
    def resample_kline(
        self, 
        df: pd.DataFrame, 
        freq: str
    ) -> pd.DataFrame:
        """重采样K线数据，转换为不同周期
        
        Args:
            df: 原始日线数据
            freq: 目标频率，'D'日线，'W'周线，'M'月线
            
        Returns:
            DataFrame: 重采样后的K线数据
        """
        if freq == 'D':
            return df  # 日线数据直接返回
            
        # 确保索引是日期类型
        if not isinstance(df.index, pd.DatetimeIndex):
            df.index = pd.to_datetime(df.index)
        
        # 定义重采样规则
        if freq == 'W':
            rule = 'W-FRI'  # 周线，以每周五为结束
        elif freq == 'M':
            rule = 'M'  # 月线
        else:
            raise ValueError(f"不支持的频率: {freq}")
        
        # 重采样OHLC数据
        resampled = df.resample(rule).agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum',
            'amount': 'sum' if 'amount' in df.columns else None
        })
        
        # 删除不存在的列
        resampled = resampled.dropna(axis=1, how='all')
        
        # 过滤掉没有数据的行
        return resampled.dropna()
    
    def filter_date_range(
        self,
        df: pd.DataFrame,
        start_date: Union[str, datetime],
        end_date: Union[str, datetime]
    ) -> pd.DataFrame:
        """按日期范围过滤数据
        
        Args:
            df: 原始数据
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            DataFrame: 过滤后的数据
        """
        if df.empty:
            return df
            
        # 转换日期格式
        if isinstance(df.index, pd.DatetimeIndex):
            start = pd.Timestamp(start_date) if not isinstance(start_date, pd.Timestamp) else start_date
            end = pd.Timestamp(end_date) if not isinstance(end_date, pd.Timestamp) else end_date
        else:
            # 如果索引不是日期类型，尝试转换
            df.index = pd.to_datetime(df.index)
            start = pd.Timestamp(start_date) if not isinstance(start_date, pd.Timestamp) else start_date
            end = pd.Timestamp(end_date) if not isinstance(end_date, pd.Timestamp) else end_date
        
        # 过滤数据
        filtered_df = df[(df.index >= start) & (df.index <= end)]
        
        return filtered_df

    def _count_business_days(self, start_date: datetime, end_date: datetime) -> int:
        """计算两个日期之间的工作日数量(周一至周五)
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            int: 工作日数量
        """
        # 确保日期格式正确
        if isinstance(start_date, str):
            start_date = datetime.strptime(start_date, "%Y-%m-%d")
        if isinstance(end_date, str):
            end_date = datetime.strptime(end_date, "%Y-%m-%d")
            
        # 转换为日期类型
        start = start_date.date() if hasattr(start_date, 'date') else start_date
        end = end_date.date() if hasattr(end_date, 'date') else end_date
        
        # 初始化计数器
        count = 0
        current = start
        
        # 遍历日期范围
        while current <= end:
            # 0代表周一，6代表周日
            if current.weekday() < 5:  # 0-4是周一至周五
                count += 1
            current += timedelta(days=1)
            
        return count

    def _normalize_dates(self, start_date: Union[str, datetime], end_date: Union[str, datetime]):
        """标准化日期格式
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            tuple: (开始日期datetime对象, 结束日期datetime对象)
        """
        # 标准化日期格式
        if isinstance(start_date, str):
            start_date_dt = datetime.strptime(start_date, "%Y-%m-%d")
        else:
            start_date_dt = start_date
            
        if isinstance(end_date, str):
            end_date_dt = datetime.strptime(end_date, "%Y-%m-%d")
        else:
            end_date_dt = end_date
            
        return start_date_dt, end_date_dt
        
    def _calculate_extended_date(self, start_date_dt: datetime, end_date_dt: datetime, min_periods: Optional[int], extend_days: int):
        """计算扩展的日期范围
        
        Args:
            start_date_dt: 开始日期
            end_date_dt: 结束日期
            min_periods: 最小数据周期数
            extend_days: 扩展天数
            
        Returns:
            tuple: (原始开始日期, 扩展后的开始日期, 预期数据天数, 实际所需周期数)
        """
        # 记录原始开始日期，用于最终过滤
        original_start_date = start_date_dt
            
        # 计算时间范围内的工作日数量(周一至周五)作为预期的数据量
        expected_days = self._count_business_days(start_date_dt, end_date_dt)
        
        # 如果未指定min_periods，使用工作日数量
        required_periods = min_periods if min_periods is not None else expected_days
        
        # 扩展开始日期，获取更多数据以确保计算指标的准确性
        # 如果指定了extend_days，使用它；否则，使用required_periods的两倍
        days_to_extend = extend_days if extend_days > 0 else required_periods * 2
        extended_start_date = start_date_dt - timedelta(days=days_to_extend)
        
        return original_start_date, extended_start_date, expected_days, required_periods

    async def _query_db_data(self, stock_code: str, extended_start, end_date, start_date_dt, end_date_dt, expected_days):
        """从数据库查询股票数据

        Args:
            stock_code: 股票代码
            extended_start: 扩展后的开始日期 (str or datetime)
            end_date: 结束日期 (str or datetime)
            start_date_dt: 原始开始日期 (datetime)
            end_date_dt: 原始结束日期 (datetime)
            expected_days: 预期的数据天数

        Returns:
            tuple: (数据列表, 数据库查询是否失败, 数据是否不足, 范围内数据量)
        """
        daily_data = []
        db_query_failed = False
        data_insufficient = False
        data_in_range = 0

        # Ensure dates are datetime objects for comparison and passing to sub-methods
        try:
            extended_start_dt = extended_start if isinstance(extended_start, datetime) else datetime.strptime(str(extended_start), "%Y-%m-%d")
            end_date_dt_param = end_date if isinstance(end_date, datetime) else datetime.strptime(str(end_date), "%Y-%m-%d")
        except ValueError as date_err:
            logger.error(f"无效的日期格式: extended_start={extended_start}, end_date={end_date}. Error: {date_err}")
            return daily_data, True, True, 0 # Return failure state

        try:
            # Check if the extended date range spans multiple years
            start_year = extended_start_dt.year
            end_year = end_date_dt_param.year

            if start_year != end_year:
                logger.info(f"查询范围 {start_year}-{end_year} 跨越年份，使用跨分区查询")
                daily_data = await self._get_data_across_partitions(stock_code, extended_start_dt, end_date_dt_param)
            else:
                # Date range is within a single year, try the direct query first
                logger.info(f"查询范围 {start_year} 在单一年份内，尝试直接查询")
                # Pass original types to storage.get_stock_daily as it might handle str/datetime
                daily_data = await self.storage.get_stock_daily(
                    stock_code, extended_start, end_date
                )

            # Calculate data in the original requested range
            data_in_range = self._count_data_in_range(daily_data, start_date_dt, end_date_dt)

            # Check if data is insufficient within the original range
            if data_in_range < expected_days * 0.7:
                logger.warning(f"股票 {stock_code} 数据库中的数据不足，在查询范围内仅有 {data_in_range} 条记录，少于预期的 {expected_days} 条")
                data_insufficient = True
            else:
                 # If we got enough data, ensure insufficient is False
                 data_insufficient = False


        except Exception as e:
            error_msg = str(e).lower()
            db_query_failed = True
            # If the error suggests a partition issue, explicitly try the cross-partition method
            # This handles cases where the single-year query fails due to partition issues
            if ("分区表" in error_msg or "partition" in error_msg or
                "表不存在" in error_msg or "not found" in error_msg or
                "no such table" in error_msg):
                logger.warning(f"数据库查询失败，尝试跨分区查询: {str(e)}")
                # Use the already converted datetime objects
                daily_data = await self._get_data_across_partitions(stock_code, extended_start_dt, end_date_dt_param)
                if daily_data:
                    db_query_failed = False # Succeeded via fallback
                    data_in_range = self._count_data_in_range(daily_data, start_date_dt, end_date_dt)
                    if data_in_range < expected_days * 0.7:
                        logger.warning(f"跨分区表查询后，股票 {stock_code} 数据仍不足，在查询范围内仅有 {data_in_range} 条记录，少于预期的 {expected_days} 条")
                        data_insufficient = True
                    else:
                         data_insufficient = False # Reset insufficient flag
                else:
                    # Cross-partition query also returned nothing
                    data_insufficient = True # Mark as insufficient if fallback yields nothing
            else:
                logger.error(f"从数据库获取数据失败: {str(e)}")
                data_insufficient = True # Mark as insufficient on other errors

        return daily_data, db_query_failed, data_insufficient, data_in_range
        
    def _count_data_in_range(self, daily_data, start_date_dt, end_date_dt):
        """计算指定日期范围内的数据量
        
        Args:
            daily_data: 数据列表
            start_date_dt: 开始日期
            end_date_dt: 结束日期
            
        Returns:
            int: 范围内的数据量
        """
        data_in_range = 0
        date_set = set()
        for item in daily_data:
            trade_date = getattr(item, 'trade_date', None)
            if trade_date:
                if isinstance(trade_date, str):
                    try:
                        trade_date = datetime.strptime(trade_date, "%Y-%m-%d").date()
                    except ValueError:
                        continue
                elif isinstance(trade_date, datetime):
                    trade_date = trade_date.date()
                
                # 只计算实际查询范围内的数据
                if start_date_dt.date() <= trade_date <= end_date_dt.date():
                    date_key = trade_date.isoformat()
                    if date_key not in date_set:
                        date_set.add(date_key)
                        data_in_range += 1
                        
        return data_in_range
        
    async def _fetch_from_provider(self, stock_code, extended_start_date, end_date_dt, daily_data, data_insufficient, expected_days, data_in_range):
        """从数据提供者获取数据
        
        Args:
            stock_code: 股票代码
            extended_start_date: 扩展后的开始日期
            end_date_dt: 结束日期
            daily_data: 现有数据列表
            data_insufficient: 数据是否不足
            expected_days: 预期的数据天数
            data_in_range: 范围内数据量
            
        Returns:
            list: 更新后的数据列表
        """
        try:
            if not daily_data:
                logger.info(f"从数据库未获取到数据，尝试从数据提供者获取股票 {stock_code} 数据")
            else:  # data_insufficient
                logger.info(f"数据库中的股票 {stock_code} 数据不足（在查询范围内有{data_in_range}条，预期{expected_days}条），尝试从数据提供者补充数据")
            
            # 获取数据提供者实例
            provider = await self._get_data_provider()
            if provider:
                # 从数据提供者获取数据
                provider_data = await provider.get_daily_data(
                    stock_code, 
                    start_date=extended_start_date, 
                    end_date=end_date_dt
                )
                
                if provider_data:
                    logger.info(f"从数据提供者成功获取到 {len(provider_data)} 条股票 {stock_code} 数据")
                    
                    # 转换provider返回的数据格式为数据库模型格式
                    fallback_data = await self._convert_provider_data(stock_code, provider_data)
                    
                    # 将数据保存到数据库中 - 异步进行，不阻塞主流程
                    self._save_data_to_db_async(stock_code, fallback_data)
                    
                    # 在数据不足的情况下，将两部分数据合并
                    if data_insufficient and daily_data:
                        daily_data = self._merge_data(daily_data, fallback_data)
                    else:
                        # 完全替换或添加新数据
                        daily_data.extend(fallback_data)
        except Exception as e:
            logger.error(f"从数据提供者获取数据失败: {str(e)}")
            
        return daily_data
            
    def _merge_data(self, daily_data, fallback_data):
        """合并两个数据源的数据，避免重复
        
        Args:
            daily_data: 原始数据列表
            fallback_data: 补充数据列表
            
        Returns:
            list: 合并后的数据列表
        """
        # 创建一个用于检测重复数据的集合
        existing_dates = set()
        for item in daily_data:
            trade_date = getattr(item, 'trade_date', None)
            if trade_date:
                # 确保日期格式一致
                if isinstance(trade_date, datetime):
                    date_key = trade_date.strftime('%Y-%m-%d')
                else:
                    date_key = str(trade_date)
                existing_dates.add(date_key)
        
        # 只添加不在原始数据中的记录
        merged_data = daily_data.copy()
        for item in fallback_data:
            trade_date = item.get('trade_date')
            if trade_date:
                # 确保日期格式一致
                if isinstance(trade_date, datetime):
                    date_key = trade_date.strftime('%Y-%m-%d')
                else:
                    date_key = str(trade_date)
                
                if date_key not in existing_dates:
                    merged_data.append(item)
                    existing_dates.add(date_key)
                    
        return merged_data
        
    def _convert_to_dataframe(self, daily_data, expected_days):
        """将数据列表转换为DataFrame
        
        Args:
            daily_data: 数据列表
            expected_days: 预期的数据天数
            
        Returns:
            DataFrame: 转换后的DataFrame
        """
        if not daily_data:
            return pd.DataFrame()
            
        # 安全获取属性函数
        def safe_get_attr(obj, attr_name, default=None):
            if hasattr(obj, attr_name):
                value = getattr(obj, attr_name)
                # 确保numpy类型转换为Python原生类型
                if hasattr(value, 'dtype') and hasattr(value, 'item'):
                    try:
                        return value.item()  # 将numpy标量转换为Python标量
                    except (ValueError, AttributeError):
                        return float(value) if value is not None else default
                return value
            elif isinstance(obj, dict) and attr_name in obj:
                value = obj[attr_name]
                # 确保numpy类型转换为Python原生类型
                if hasattr(value, 'dtype') and hasattr(value, 'item'):
                    try:
                        return value.item()  # 将numpy标量转换为Python标量
                    except (ValueError, AttributeError):
                        return float(value) if value is not None else default
                return value
            return default
            
        # 转换为DataFrame
        df_data = []
        for item in daily_data:
            data_point = {
                'date': safe_get_attr(item, 'trade_date'),
                'open': safe_get_attr(item, 'open', 0.0),
                'high': safe_get_attr(item, 'high', 0.0),
                'low': safe_get_attr(item, 'low', 0.0),
                'close': safe_get_attr(item, 'close', 0.0),
                'volume': safe_get_attr(item, 'volume', 0),
                'amount': safe_get_attr(item, 'amount'),
                'change_pct': safe_get_attr(item, 'change_pct'),
                'turnover_rate': safe_get_attr(item, 'turnover_rate')
            }
            # 确保日期有效
            if data_point['date'] is not None:
                df_data.append(data_point)
        
        df = pd.DataFrame(df_data)
        
        # 再次检查数据是否足够
        if len(df) < expected_days * 0.7:
            logger.warning(f"最终获取的数据仍不足，仅有 {len(df)} 条记录，少于预期的 {expected_days} 条")
        
        if df.empty:
            return df
            
        df.set_index('date', inplace=True)
        # 确保索引是日期类型
        if not isinstance(df.index, pd.DatetimeIndex):
            df.index = pd.to_datetime(df.index)
            
        df.sort_index(inplace=True)
        
        # 处理可能的重复数据
        df = df[~df.index.duplicated(keep='first')]
        
        return df
        
    def _filter_by_date_range(self, df, original_start_date):
        """按用户请求的日期范围过滤数据
        
        Args:
            df: DataFrame
            original_start_date: 原始开始日期
            
        Returns:
            DataFrame: 过滤后的DataFrame
        """
        if df.empty:
            return df
            
        try:
            # 将开始日期转换为pandas时间戳，以便与索引比较
            user_start = pd.Timestamp(original_start_date)
            # 确保时区一致
            if df.index.tz is not None and user_start.tz is None:
                user_start = user_start.tz_localize(df.index.tz)
            elif df.index.tz is None and user_start.tz is not None:
                user_start = user_start.tz_localize(None)
                
            # 过滤数据
            df = df[df.index >= user_start]
        except Exception as filter_err:
            logger.error(f"过滤日期范围时出错: {str(filter_err)}")
            # 如果过滤出错，返回所有数据以避免完全失败
            
        return df