"""
扫描器缓存系统 - 升级版本

迁移到新的统一缓存系统，保持API兼容性，同时获得以下好处：
1. 多级缓存支持（内存 + Redis）
2. 更好的序列化（支持DataFrame）
3. 自动过期管理
4. 统计和监控
5. 标签支持

使用说明：
- 现有API保持不变
- 底层自动使用新的缓存系统
- 支持Redis持久化和集群部署
"""
from typing import Dict, Optional, Any
from datetime import datetime
from dataclasses import dataclass
import pandas as pd
import logging
import asyncio
from concurrent.futures import ThreadPoolExecutor
from app.core.config import settings
from app.core.cache import cache, CacheProfile, get_cache_stats

logger = logging.getLogger(__name__)


@dataclass
class CachedStockData:
    """缓存的股票数据"""
    stock_code: str
    trade_date: str
    # 技术指标数据
    df_with_indicators: pd.DataFrame
    # 元数据
    cached_at: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'stock_code': self.stock_code,
            'trade_date': self.trade_date,
            'df_dict': self.df_with_indicators.to_dict('records'),
            'cached_at': self.cached_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CachedStockData':
        """从字典创建对象"""
        df = pd.DataFrame(data['df_dict'])
        return cls(
            stock_code=data['stock_code'],
            trade_date=data['trade_date'],
            df_with_indicators=df,
            cached_at=datetime.fromisoformat(data['cached_at'])
        )


# 全局异步缓存管理器实例
_async_cache_manager = None

def get_async_cache_manager():
    """获取异步缓存管理器实例"""
    global _async_cache_manager
    if _async_cache_manager is None:
        _async_cache_manager = AsyncIndicatorCacheManager()
    return _async_cache_manager


class IndicatorCacheManager:
    """
    技术指标缓存管理器 - 升级版本
    
    现在基于新的统一缓存系统，具有以下改进：
    - 支持多级缓存（内存 + Redis）
    - 自动序列化DataFrame
    - 更好的过期管理
    - 统计和监控
    """
    
    def __init__(self, max_size: int = 5000):
        """
        初始化缓存管理器
        
        Args:
            max_size: 最大缓存条目数（仅影响内存缓存层）
        """
        self.max_size = max_size
        
        # 创建专用的缓存配置
        self.cache_profile = CacheProfile(
            expire=21600,  # 6小时过期
            namespace="scanner",
            tags=["indicators", "scanner"],
            compress=True  # 压缩大数据
        )
        
        # 统计信息（向后兼容）
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0
        }
        
        # 线程池用于在异步环境中执行同步操作
        self._executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="cache")
        
        logger.info("扫描器缓存已升级到新的统一缓存系统")
    
    def _get_cache_key(self, stock_code: str, trade_date: str) -> str:
        """生成缓存键"""
        return f"indicators:{trade_date}:{stock_code}"
    
    def _get_current_trade_date(self) -> str:
        """获取当前交易日期"""
        return datetime.now().strftime('%Y-%m-%d')
    
    def get(self, stock_code: str, trade_date: Optional[str] = None) -> Optional[CachedStockData]:
        """
        获取缓存数据
        
        Args:
            stock_code: 股票代码
            trade_date: 交易日期，默认使用当前日期
            
        Returns:
            缓存的数据或None（如果缓存被禁用也返回None）
        """
        if not settings.CACHE_ENABLED:
            return None
        
        if trade_date is None:
            trade_date = self._get_current_trade_date()
        
        cache_key = self._get_cache_key(stock_code, trade_date)
        
        try:
            # 检查是否在异步环境中
            try:
                loop = asyncio.get_running_loop()
                # 在异步环境中，委托给异步缓存管理器
                async_cache = get_async_cache_manager()
                # 创建一个任务来获取缓存
                task = loop.create_task(async_cache.get(stock_code, trade_date))
                # 这里不能等待，所以直接返回None，让异步版本处理
                return None
            except RuntimeError:
                # 不在异步环境中，可以安全地运行异步代码
                cached_data = asyncio.run(self._async_get(cache_key))
                
                if cached_data:
                    self._stats['hits'] += 1
                    logger.debug(f"扫描器缓存命中: {stock_code} on {trade_date}")
                    return cached_data
                else:
                    self._stats['misses'] += 1
                    return None
                
        except Exception as e:
            logger.debug(f"扫描器缓存获取失败: {e}")
            self._stats['misses'] += 1
            return None
    
    async def _async_get(self, cache_key: str) -> Optional[CachedStockData]:
        """异步获取缓存"""
        from app.core.cache.legacy import get_cached_result
        
        cached_value = await get_cached_result(cache_key)
        if cached_value:
            try:
                # 反序列化为CachedStockData对象
                if isinstance(cached_value, dict):
                    return CachedStockData.from_dict(cached_value)
                else:
                    return cached_value
            except Exception as e:
                logger.error(f"反序列化缓存数据失败: {e}")
                return None
        return None
    
    def set(self, stock_code: str, df_with_indicators: pd.DataFrame, 
            trade_date: Optional[str] = None) -> None:
        """
        设置缓存数据
        
        Args:
            stock_code: 股票代码
            df_with_indicators: 包含技术指标的DataFrame
            trade_date: 交易日期，默认使用当前日期
        """
        if not settings.CACHE_ENABLED:
            return
        
        if trade_date is None:
            trade_date = self._get_current_trade_date()
        
        cache_key = self._get_cache_key(stock_code, trade_date)
        cached_data = CachedStockData(
            stock_code=stock_code,
            trade_date=trade_date,
            df_with_indicators=df_with_indicators.copy(),
            cached_at=datetime.now()
        )
        
        try:
            # 检查是否在异步环境中
            try:
                loop = asyncio.get_running_loop()
                # 在异步环境中，委托给异步缓存管理器
                async_cache = get_async_cache_manager()
                # 创建任务但不等待（fire and forget）
                loop.create_task(async_cache.set(stock_code, df_with_indicators, trade_date))
                logger.debug(f"扫描器缓存已设置（异步）: {stock_code} on {trade_date}")
            except RuntimeError:
                # 不在异步环境中，可以安全地运行异步代码
                asyncio.run(self._async_set(cache_key, cached_data))
                logger.debug(f"扫描器缓存已设置: {stock_code} on {trade_date}")
                
        except Exception as e:
            logger.debug(f"扫描器缓存设置失败: {e}")
    
    async def _async_set(self, cache_key: str, cached_data: CachedStockData):
        """异步设置缓存"""
        from app.core.cache.legacy import cache_result
        
        # 序列化为字典格式
        data_dict = cached_data.to_dict()
        await cache_result(cache_key, data_dict, expire=21600)  # 6小时
    
    def clear_expired(self) -> int:
        """
        清理过期缓存
        
        Returns:
            清理的条目数
        """
        # 新系统自动处理过期，但为了兼容性返回0
        logger.debug("新缓存系统自动处理过期，无需手动清理")
        return 0
    
    def clear_all(self) -> None:
        """清空所有缓存"""
        try:
            # 检查是否在异步环境中
            try:
                loop = asyncio.get_running_loop()
                # 在异步环境中，委托给异步缓存管理器
                async_cache = get_async_cache_manager()
                loop.create_task(async_cache.clear_all())
            except RuntimeError:
                # 不在异步环境中
                asyncio.run(self._async_clear_all())
            
            self._stats = {'hits': 0, 'misses': 0, 'evictions': 0}
            logger.info("扫描器缓存已清空")
            
        except Exception as e:
            logger.debug(f"清空扫描器缓存失败: {e}")
    
    async def _async_clear_all(self):
        """异步清空缓存"""
        from app.core.cache.legacy import clear_cache_by_prefix
        await clear_cache_by_prefix("scanner:")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        # 合并新旧统计数据
        new_stats = get_cache_stats()
        scanner_stats = new_stats.get('namespaces', {}).get('scanner', {})
        
        total_requests = self._stats['hits'] + self._stats['misses']
        hit_rate = self._stats['hits'] / total_requests if total_requests > 0 else 0
        
        return {
            # 兼容旧格式
            'size': scanner_stats.get('size', {}).get('key_count', 0),
            'hits': self._stats['hits'],
            'misses': self._stats['misses'],
            'evictions': self._stats['evictions'],
            'hit_rate': hit_rate,
            'total_requests': total_requests,
            'max_size': self.max_size,
            
            # 新的统计信息
            'new_stats': scanner_stats,
            'backend_info': new_stats.get('backends', {}),
            'cache_system': 'unified_v2'
        }
    
    def get_memory_usage_mb(self) -> float:
        """估算内存使用量（MB）"""
        stats = get_cache_stats()
        scanner_stats = stats.get('namespaces', {}).get('scanner', {})
        
        if 'size' in scanner_stats and 'total_mb' in scanner_stats['size']:
            try:
                return float(scanner_stats['size']['total_mb'])
            except:
                pass
        
        # 回退到估算
        size = scanner_stats.get('size', {}).get('key_count', 0)
        return size * 0.01  # 每个条目估算10KB


# 创建全局实例（保持向后兼容）
_global_indicator_cache = None

def get_indicator_cache_manager() -> IndicatorCacheManager:
    """获取全局扫描器缓存管理器实例"""
    global _global_indicator_cache
    if _global_indicator_cache is None:
        _global_indicator_cache = IndicatorCacheManager()
    return _global_indicator_cache


# 便捷的装饰器函数
def scanner_cache_decorator(expire: int = 21600):
    """
    扫描器专用缓存装饰器
    
    Args:
        expire: 过期时间（秒），默认6小时
    
    使用示例:
        @scanner_cache_decorator(expire=3600)
        async def calculate_indicators(stock_code: str, trade_date: str):
            # 计算逻辑
            return indicators
    """
    return cache(
        profile="scanner",
        expire=expire,
        tags=["scanner", "indicators"]
    )


# 异步版本的缓存管理器（推荐用于新代码）
class AsyncIndicatorCacheManager:
    """异步版本的指标缓存管理器，推荐在异步环境中使用"""
    
    def __init__(self, max_size: int = 5000):
        self.max_size = max_size
        self.cache_profile = CacheProfile(
            expire=21600,
            namespace="scanner",
            tags=["indicators", "scanner"],
            compress=True
        )
    
    def _get_cache_key(self, stock_code: str, trade_date: str) -> str:
        return f"indicators:{trade_date}:{stock_code}"
    
    async def get(self, stock_code: str, trade_date: Optional[str] = None) -> Optional[CachedStockData]:
        """异步获取缓存数据"""
        if not settings.CACHE_ENABLED:
            return None
        
        if trade_date is None:
            trade_date = datetime.now().strftime('%Y-%m-%d')
        
        cache_key = self._get_cache_key(stock_code, trade_date)
        
        from app.core.cache.legacy import get_cached_result
        cached_value = await get_cached_result(cache_key)
        
        if cached_value and isinstance(cached_value, dict):
            try:
                return CachedStockData.from_dict(cached_value)
            except Exception as e:
                logger.error(f"反序列化失败: {e}")
        
        return None
    
    async def set(self, stock_code: str, df_with_indicators: pd.DataFrame, 
                  trade_date: Optional[str] = None) -> None:
        """异步设置缓存数据"""
        if not settings.CACHE_ENABLED:
            return
        
        if trade_date is None:
            trade_date = datetime.now().strftime('%Y-%m-%d')
        
        cache_key = self._get_cache_key(stock_code, trade_date)
        cached_data = CachedStockData(
            stock_code=stock_code,
            trade_date=trade_date,
            df_with_indicators=df_with_indicators.copy(),
            cached_at=datetime.now()
        )
        
        from app.core.cache.legacy import cache_result
        await cache_result(cache_key, cached_data.to_dict(), expire=21600)
    
    async def clear_all(self) -> None:
        """异步清空所有缓存"""
        from app.core.cache.legacy import clear_cache_by_prefix
        await clear_cache_by_prefix("scanner:")