"""
股票技术指标计算器
用于技术指标扫描功能的指标计算
"""
from typing import List, Optional, Tuple, Any
import numpy as np
import pandas as pd

from app.core.logging import getLogger

logger = getLogger(__name__)


class StockIndicatorCalculator:
    """股票技术指标计算器"""
    
    # 默认参数常量（保持向后兼容）
    DEFAULT_KDJ_N = 9          # KDJ周期
    DEFAULT_KDJ_M1 = 3          # K值平滑周期  
    DEFAULT_KDJ_M2 = 3          # D值平滑周期
    DEFAULT_KDJ_METHOD = 'standard'  # KDJ计算方法，默认标准算法
    DEFAULT_BOLLINGER_WINDOW = 20    # 布林带窗口
    DEFAULT_BOLLINGER_STD = 2.0      # 布林带标准差倍数
    DEFAULT_EMA_PERIOD = 10          # EMA周期
    DATA_PERIOD = 20         # 历史数据周期
    
    def __init__(self) -> None:
        """初始化计算器"""
        pass
    
    def calculate_volume_pressure(
        self,
        internal_volume: pd.Series,
        external_volume: pd.Series,
        ema_period: int = None
    ) -> pd.Series:
        """
        计算成交量压力指标
        
        Args:
            internal_volume: 内盘成交量序列
            external_volume: 外盘成交量序列
            ema_period: EMA平滑周期，None时使用默认值
            
        Returns:
            成交量压力指标序列
        """
        if ema_period is None:
            ema_period = self.DEFAULT_EMA_PERIOD
        # 计算内外盘差值
        volume_diff = external_volume - internal_volume
        
        # 标准化处理（避免除零）
        total_volume = internal_volume + external_volume
        normalized_diff = volume_diff / (total_volume + 1e-8)
        
        # EMA平滑
        log_vol = normalized_diff.ewm(span=ema_period, adjust=False).mean()
        
        return log_vol
    
    def calculate_volume_bollinger(
        self,
        volume_pressure: pd.Series,
        window: int = None,
        std_dev: float = None
    ) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        计算成交量布林带
        
        Args:
            volume_pressure: 成交量压力指标序列
            window: 计算窗口，None时使用默认值
            std_dev: 标准差倍数，None时使用默认值
            
        Returns:
            (上轨, 中轨, 下轨) 元组
        """
        if window is None:
            window = self.DEFAULT_BOLLINGER_WINDOW
        if std_dev is None:
            std_dev = self.DEFAULT_BOLLINGER_STD
        # 计算移动平均（中轨）
        middle = volume_pressure.rolling(window=window).mean()
        
        # 计算标准差
        std = volume_pressure.rolling(window=window).std()
        
        # 计算上下轨
        upper = middle + (std_dev * std)
        lower = middle - (std_dev * std)
        
        return upper, middle, lower
    
    def calculate_kdj(
        self,
        high: pd.Series,
        low: pd.Series,
        close: pd.Series,
        n: int = None,
        m1: int = None,
        m2: int = None,
        method: str = None
    ) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        计算KDJ指标（标准算法）
        
        Args:
            high: 最高价序列
            low: 最低价序列
            close: 收盘价序列
            n: RSV周期，None时使用默认值
            m1: K值平滑周期，None时使用默认值（实际使用标准系数2/3和1/3）
            m2: D值平滑周期，None时使用默认值（实际使用标准系数2/3和1/3）
            method: 计算方法，None时使用默认值，'standard'为标准KDJ算法（默认），'sma'为简单移动平均，'ema'为指数移动平均
            
        Returns:
            (K线, D线, J线) 元组
        """
        if n is None:
            n = self.DEFAULT_KDJ_N
        if m1 is None:
            m1 = self.DEFAULT_KDJ_M1
        if m2 is None:
            m2 = self.DEFAULT_KDJ_M2
        if method is None:
            method = self.DEFAULT_KDJ_METHOD
        # 计算RSV
        lowest_low = low.rolling(window=n).min()
        highest_high = high.rolling(window=n).max()
        rsv = 100 * (close - lowest_low) / (highest_high - lowest_low + 1e-8)
        
        if method.lower() == 'standard':
            # 使用标准KDJ算法
            k = pd.Series(index=close.index, dtype='float64')
            d = pd.Series(index=close.index, dtype='float64')
            
            # 找到第一个有效RSV值的位置
            first_valid_idx = rsv.first_valid_index()
            if first_valid_idx is None:
                # 如果没有有效RSV，返回全NaN序列
                k[:] = np.nan
                d[:] = np.nan
                j = pd.Series([np.nan] * len(close), index=close.index)
                return k, d, j
            
            # 设置初始值：K和D的初始值为50（行业标准）
            k.loc[first_valid_idx] = 50.0
            d.loc[first_valid_idx] = 50.0
            
            # 计算后续的K值和D值（使用标准KDJ公式）
            for i in range(close.index.get_loc(first_valid_idx), len(close)):
                idx = close.index[i]
                
                if i == close.index.get_loc(first_valid_idx):
                    # 第一个值特殊处理：K = RSV, D = K
                    if not pd.isna(rsv.loc[idx]):
                        k.loc[idx] = rsv.loc[idx]
                        d.loc[idx] = k.loc[idx]
                    continue
                
                prev_idx = close.index[i-1]
                
                if not pd.isna(rsv.loc[idx]):
                    # 标准KDJ公式:
                    # K = 2/3 × 前日K值 + 1/3 × 今日RSV
                    # D = 2/3 × 前日D值 + 1/3 × 今日K值
                    k.loc[idx] = (2.0/3.0) * k.loc[prev_idx] + (1.0/3.0) * rsv.loc[idx]
                    d.loc[idx] = (2.0/3.0) * d.loc[prev_idx] + (1.0/3.0) * k.loc[idx]
                else:
                    k.loc[idx] = k.loc[prev_idx]  # 保持前值
                    d.loc[idx] = d.loc[prev_idx]  # 保持前值
                    
        elif method.lower() == 'ema':
            # 使用EMA算法计算K值和D值
            k = pd.Series(index=close.index, dtype='float64')
            d = pd.Series(index=close.index, dtype='float64')
            
            # 找到第一个有效RSV值的位置
            first_valid_idx = rsv.first_valid_index()
            if first_valid_idx is None:
                # 如果没有有效RSV，返回全NaN序列
                k[:] = np.nan
                d[:] = np.nan
                j = pd.Series([np.nan] * len(close), index=close.index)
                return k, d, j
            
            # 设置初始值
            k.loc[first_valid_idx] = rsv.loc[first_valid_idx]
            d.loc[first_valid_idx] = k.loc[first_valid_idx]
            
            # 计算后续的K值和D值（使用EMA公式）
            for i in range(close.index.get_loc(first_valid_idx) + 1, len(close)):
                idx = close.index[i]
                prev_idx = close.index[i-1]
                
                if not pd.isna(rsv.loc[idx]):
                    # K = (2*RSV + (m1-1)*K_prev) / (m1+1) 
                    k.loc[idx] = (2 * rsv.loc[idx] + (m1 - 1) * k.loc[prev_idx]) / (m1 + 1)
                    # D = (2*K + (m2-1)*D_prev) / (m2+1)
                    d.loc[idx] = (2 * k.loc[idx] + (m2 - 1) * d.loc[prev_idx]) / (m2 + 1)
                else:
                    k.loc[idx] = np.nan
                    d.loc[idx] = np.nan
        else:
            # 使用SMA算法计算K值和D值
            k = rsv.rolling(window=m1).mean()
            d = k.rolling(window=m2).mean()
        
        # 计算J值（标准公式）
        j = 3 * k - 2 * d
        
        return k, d, j
    
    def calculate_price_bollinger(
        self,
        close: pd.Series,
        window: int = 20,
        std_dev: float = 2.0
    ) -> Tuple[pd.Series, pd.Series, pd.Series]:
        """
        计算价格布林带
        
        Args:
            close: 收盘价序列
            window: 计算窗口
            std_dev: 标准差倍数
            
        Returns:
            (上轨, 中轨, 下轨) 元组
        """
        # 计算移动平均（中轨）
        middle = close.rolling(window=window).mean()
        
        # 计算标准差
        std = close.rolling(window=window).std()
        
        # 计算上下轨
        upper = middle + (std_dev * std)
        lower = middle - (std_dev * std)
        
        return upper, middle, lower
    
    def check_buy_signal(
        self,
        data: pd.DataFrame,
        current_index: int
    ) -> bool:
        """
        检查买入信号（AND逻辑）
        
        Args:
            data: 包含所有指标的DataFrame
            current_index: 当前时间索引
            
        Returns:
            是否满足买入条件
        """
        if current_index < 1:
            return False
            
        try:
            # 成交量压力突破检查
            vol_pressure_breakout = (
                data['log_vol'].iloc[current_index - 1] < data['average_vol'].iloc[current_index - 1] and
                data['log_vol'].iloc[current_index] > data['average_vol'].iloc[current_index]
            )
            
            # KDJ金叉检查
            kdj_golden_cross = (
                data['price_k'].iloc[current_index - 1] < data['price_d'].iloc[current_index - 1] and
                data['price_k'].iloc[current_index] > data['price_d'].iloc[current_index]
            )
            
            # AND逻辑：两个条件都满足
            return vol_pressure_breakout and kdj_golden_cross
            
        except (KeyError, IndexError):
            return False
    
    def check_sell_signal(
        self,
        data: pd.DataFrame,
        current_index: int
    ) -> bool:
        """
        检查卖出信号（OR逻辑）
        
        Args:
            data: 包含所有指标的DataFrame
            current_index: 当前时间索引
            
        Returns:
            是否满足卖出条件
        """
        if current_index < 1:
            return False
            
        try:
            # 成交量压力衰竭检查
            vol_pressure_exhaustion = (
                data['log_vol'].iloc[current_index - 1] > data['average_vol'].iloc[current_index - 1] and
                data['log_vol'].iloc[current_index] < data['average_vol'].iloc[current_index]
            )
            
            # KDJ死叉检查
            kdj_death_cross = (
                data['price_k'].iloc[current_index - 1] > data['price_d'].iloc[current_index - 1] and
                data['price_k'].iloc[current_index] < data['price_d'].iloc[current_index]
            )
            
            # OR逻辑：任一条件满足
            return vol_pressure_exhaustion or kdj_death_cross
            
        except (KeyError, IndexError):
            return False
    
    def check_stop_loss(
        self,
        data: pd.DataFrame,
        current_index: int
    ) -> bool:
        """
        检查止损信号
        
        Args:
            data: 包含所有指标的DataFrame
            current_index: 当前时间索引
            
        Returns:
            是否触发止损
        """
        try:
            # 收盘价跌破布林带下轨
            return data['close'].iloc[current_index] < data['dis_Lowr'].iloc[current_index]
        except (KeyError, IndexError):
            return False
    
    def calculate_all_indicators(
        self,
        df: pd.DataFrame,
        kdj_method: str = 'standard',
        parameters: Optional[Any] = None
    ) -> pd.DataFrame:
        """
        计算所有技术指标（支持动态参数）
        
        Args:
            df: 包含OHLCV和内外盘数据的DataFrame
            kdj_method: KDJ计算方法，'standard'为标准算法（默认），'sma'为简单移动平均，'ema'为指数移动平均
            parameters: 指标参数配置，None时使用默认值
            
        Returns:
            包含所有指标的DataFrame
        """
        # 解析参数，设置默认值
        kdj_n = self.DEFAULT_KDJ_N
        kdj_m1 = self.DEFAULT_KDJ_M1  
        kdj_m2 = self.DEFAULT_KDJ_M2
        volume_ema_period = self.DEFAULT_EMA_PERIOD
        bollinger_window = self.DEFAULT_BOLLINGER_WINDOW
        bollinger_std = self.DEFAULT_BOLLINGER_STD
        
        if parameters:
            if hasattr(parameters, 'kdj') and parameters.kdj:
                kdj_n = parameters.kdj.n
                kdj_m1 = parameters.kdj.m1
                kdj_m2 = parameters.kdj.m2
            if hasattr(parameters, 'volume_pressure') and parameters.volume_pressure:
                volume_ema_period = parameters.volume_pressure.ema_period
            if hasattr(parameters, 'bollinger') and parameters.bollinger:
                bollinger_window = parameters.bollinger.window
                bollinger_std = parameters.bollinger.std_dev
        result = df.copy()
        
        # 确保有必要的列
        required_columns = ['high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in result.columns:
                raise ValueError(f"缺少必要的列: {col}")
        
        # 计算成交量压力指标（使用动态EMA周期）
        if 'internal_volume' in result.columns and 'external_volume' in result.columns:
            result['log_vol'] = self.calculate_volume_pressure(
                result['internal_volume'],
                result['external_volume'],
                volume_ema_period
            )
        else:
            # 如果没有内外盘数据，使用成交量的近似计算
            # 这里简化处理，实际应根据tick数据计算
            result['log_vol'] = result['volume'].pct_change().fillna(0).ewm(span=volume_ema_period, adjust=False).mean()
        
        # 计算成交量布林带（使用动态参数）
        vol_upper, vol_middle, vol_lower = self.calculate_volume_bollinger(
            result['log_vol'],
            bollinger_window,
            bollinger_std
        )
        result['vol_upper'] = vol_upper
        result['average_vol'] = vol_middle  # 中轨
        result['vol_lower'] = vol_lower
        
        # 计算价格布林带（使用动态参数）
        price_upper, price_middle, price_lower = self.calculate_price_bollinger(
            result['close'],
            bollinger_window,
            bollinger_std
        )
        result['price_upper'] = price_upper
        result['price_middle'] = price_middle
        result['dis_Lowr'] = price_lower  # 下轨（止损线）
        
        # 计算KDJ（使用动态参数和指定方法）
        k, d, j = self.calculate_kdj(
            result['high'],
            result['low'],
            result['close'],
            kdj_n,
            kdj_m1,
            kdj_m2,
            kdj_method
        )
        result['price_k'] = k
        result['price_d'] = d
        result['price_j'] = j
        
        return result
    
    def check_kdj_golden_cross(self, data: pd.DataFrame, current_index: int) -> bool:
        """
        检查KDJ金叉条件: T-1时K<D, T时K>D
        
        Args:
            data: 包含KDJ指标的DataFrame
            current_index: 当前时间索引
            
        Returns:
            是否发生KDJ金叉
        """
        if current_index < 1:
            return False
            
        try:
            prev_k = data['price_k'].iloc[current_index - 1]
            prev_d = data['price_d'].iloc[current_index - 1]
            curr_k = data['price_k'].iloc[current_index]
            curr_d = data['price_d'].iloc[current_index]
            
            # T-1时K<D 且 T时K>D
            return prev_k < prev_d and curr_k > curr_d
        except (KeyError, IndexError):
            return False
    
    def check_kdj_death_cross(self, data: pd.DataFrame, current_index: int) -> bool:
        """
        检查KDJ死叉条件: T-1时K>D, T时K<D
        
        Args:
            data: 包含KDJ指标的DataFrame
            current_index: 当前时间索引
            
        Returns:
            是否发生KDJ死叉
        """
        if current_index < 1:
            return False
            
        try:
            prev_k = data['price_k'].iloc[current_index - 1]
            prev_d = data['price_d'].iloc[current_index - 1]
            curr_k = data['price_k'].iloc[current_index]
            curr_d = data['price_d'].iloc[current_index]
            
            # T-1时K>D 且 T时K<D
            return prev_k > prev_d and curr_k < curr_d
        except (KeyError, IndexError):
            return False
    
    def check_volume_pressure_breakout(self, data: pd.DataFrame, current_index: int) -> bool:
        """
        检查成交量压力突破条件: 当日值 > 20日平均
        
        Args:
            data: 包含成交量压力指标的DataFrame
            current_index: 当前时间索引
            
        Returns:
            是否发生成交量压力突破
        """
        try:
            curr_pressure = data['log_vol'].iloc[current_index]
            avg_pressure = data['average_vol'].iloc[current_index]
            
            # 当日成交量压力 > 20日平均
            return curr_pressure > avg_pressure
        except (KeyError, IndexError):
            return False
    
    def check_volume_pressure_decline(self, data: pd.DataFrame, current_index: int) -> bool:
        """
        检查成交量压力衰减条件: 当日值 < 20日平均
        
        Args:
            data: 包含成交量压力指标的DataFrame
            current_index: 当前时间索引
            
        Returns:
            是否发生成交量压力衰减
        """
        try:
            curr_pressure = data['log_vol'].iloc[current_index]
            avg_pressure = data['average_vol'].iloc[current_index]
            
            # 当日成交量压力 < 20日平均
            return curr_pressure < avg_pressure
        except (KeyError, IndexError):
            return False
    
    def check_bollinger_stop_loss(self, data: pd.DataFrame, current_index: int) -> bool:
        """
        检查布林带止损条件: 收盘价 < 下轨线
        
        Args:
            data: 包含布林带指标的DataFrame
            current_index: 当前时间索引
            
        Returns:
            是否触发布林带止损
        """
        try:
            close_price = data['close'].iloc[current_index]
            lower_band = data['dis_Lowr'].iloc[current_index]
            
            # 收盘价 < 布林带下轨
            return close_price < lower_band
        except (KeyError, IndexError):
            return False
    
    def check_bollinger_opportunity(self, data: pd.DataFrame, current_index: int) -> bool:
        """
        检查布林带买入机会条件: 接近下轨但未跌破
        
        Args:
            data: 包含布林带指标的DataFrame
            current_index: 当前时间索引
            
        Returns:
            是否存在布林带买入机会
        """
        try:
            close_price = data['close'].iloc[current_index]
            lower_band = data['dis_Lowr'].iloc[current_index]
            middle_band = data['price_middle'].iloc[current_index]
            
            # 接近下轨但未跌破：收盘价在下轨上方但低于中轨
            distance_to_lower = (close_price - lower_band) / lower_band
            return close_price >= lower_band and distance_to_lower <= 0.05 and close_price < middle_band
        except (KeyError, IndexError, ZeroDivisionError):
            return False
    
    def check_indicator_conditions(self, df: pd.DataFrame, selected_indicators: List[str]) -> bool:
        """
        根据选中指标检查是否满足筛选条件（严格匹配模式）
        
        Args:
            df: 包含所有技术指标的DataFrame
            selected_indicators: 选中的指标列表 ['kdj', 'volume_pressure', 'bollinger']
            
        Returns:
            是否严格满足选中的条件：选中的必须满足，未选中的必须不满足
        """
        if not selected_indicators:
            return False
            
        latest_index = len(df) - 1
        if latest_index < 0:
            return False
        
        # 定义所有可能的指标
        all_indicators = ['kdj', 'volume_pressure', 'bollinger']
        conditions_status = {}
        
        # 检查所有指标的状态
        # KDJ金叉条件
        kdj_condition = self.check_kdj_golden_cross(df, latest_index)
        conditions_status['kdj'] = kdj_condition
        
        # 成交量压力突破条件
        volume_condition = self.check_volume_pressure_breakout(df, latest_index)
        conditions_status['volume_pressure'] = volume_condition
        
        # 布林带买入机会条件
        bollinger_condition = self.check_bollinger_opportunity(df, latest_index)
        conditions_status['bollinger'] = bollinger_condition
        
        # 严格匹配逻辑：
        # 1. 所有选中的指标必须为True
        # 2. 所有未选中的指标必须为False
        for indicator in all_indicators:
            condition_result = conditions_status.get(indicator, False)
            if indicator in selected_indicators:
                # 选中的指标必须满足
                if not condition_result:
                    return False
            else:
                # 未选中的指标必须不满足
                if condition_result:
                    return False
        
        # logger.debug("All conditions satisfied in strict mode, returning True")
        return True
    
    def generate_signals(
        self,
        df: pd.DataFrame
    ) -> pd.DataFrame:
        """
        生成交易信号
        
        Args:
            df: 包含所有指标的DataFrame
            
        Returns:
            包含信号的DataFrame
        """
        result = df.copy()
        
        # 初始化信号列
        result['buy_signal'] = False
        result['sell_signal'] = False
        result['stop_loss_signal'] = False
        
        # 生成信号
        for i in range(1, len(result)):
            result.loc[result.index[i], 'buy_signal'] = self.check_buy_signal(result, i)
            result.loc[result.index[i], 'sell_signal'] = self.check_sell_signal(result, i)
            result.loc[result.index[i], 'stop_loss_signal'] = self.check_stop_loss(result, i)
        
        return result