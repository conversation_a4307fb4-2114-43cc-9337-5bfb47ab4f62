from datetime import datetime
from typing import Any, Dict, List, Optional
from app.core import logging
import tushare as ts
from tushare.pro.client import Data<PERSON>pi

from ..base import DataFetcher
from ..adapter import DataAdapter, BaseDataAdapter

logger = logging.getLogger(__name__)

class TushareAdapter(BaseDataAdapter):
    """Tushare数据适配器"""
    
    def normalize_stock_info(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        标准化Tushare的股票基本信息。
        
        Tushare字段映射:
            - ts_code -> code
            - name -> name
            - industry -> industry
            - market -> market
            - list_date -> list_date
        """
        return {
            "code": raw_data.get("ts_code", "").split(".")[0],  # 移除市场后缀
            "name": raw_data.get("name", ""),
            "industry": raw_data.get("industry", ""),
            "market": raw_data.get("market", ""),
            "list_date": raw_data.get("list_date", "")
        }

    def normalize_daily_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        标准化Tushare的日线数据。
        
        Tushare字段映射:
            - trade_date -> date
            - open -> open
            - high -> high
            - low -> low
            - close -> close
            - vol -> volume
            - amount -> amount
            - turnover_rate -> turnover
            - pct_chg -> change_pct
        """
        return {
            "date": datetime.strptime(str(raw_data.get("trade_date")), "%Y%m%d"),
            "open": float(raw_data.get("open", 0)),
            "high": float(raw_data.get("high", 0)),
            "low": float(raw_data.get("low", 0)),
            "close": float(raw_data.get("close", 0)),
            "volume": float(raw_data.get("vol", 0)),
            "amount": float(raw_data.get("amount", 0)),
            "turnover": float(raw_data.get("turnover_rate", 0)),
            "change_pct": float(raw_data.get("pct_chg", 0))
        }

    def normalize_realtime_quote(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        标准化Tushare的实时行情数据。
        
        Tushare字段映射:
            - ts_code -> code
            - current -> price
            - change -> change
            - pct_chg -> change_pct
            - vol -> volume
            - amount -> amount
            - trade_time -> time
        """
        return {
            "code": raw_data.get("ts_code", "").split(".")[0],
            "price": float(raw_data.get("current", 0)),
            "change": float(raw_data.get("change", 0)),
            "change_pct": float(raw_data.get("pct_chg", 0)),
            "volume": float(raw_data.get("vol", 0)),
            "amount": float(raw_data.get("amount", 0)),
            "time": datetime.strptime(
                f"{raw_data.get('trade_date')} {raw_data.get('trade_time')}",
                "%Y%m%d %H%M%S"
            ) if raw_data.get("trade_date") and raw_data.get("trade_time") else datetime.now()
        }

class TushareDataFetcher(DataFetcher):
    """Tushare数据获取实现"""

    def __init__(self, api_token: str):
        """
        初始化Tushare数据获取器。

        Args:
            api_token: Tushare API令牌
        """
        ts.set_token(api_token)
        self.pro = ts.pro_api()

    async def get_stock_list(self) -> List[Dict[str, Any]]:
        """获取股票列表"""
        try:
            df = self.pro.stock_basic(
                exchange='',
                list_status='L',
                fields='ts_code,name,industry,market,list_date'
            )
            return df.to_dict('records')
        except Exception as e:
            logger.error(f"获取股票列表失败: {str(e)}")
            raise

    async def get_daily_data(
        self,
        code: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """获取日线数据"""
        try:
            # 转换日期格式为Tushare所需的格式
            start = start_date.strftime("%Y%m%d") if start_date else ""
            end = end_date.strftime("%Y%m%d") if end_date else datetime.now().strftime("%Y%m%d")
            
            # 添加市场后缀
            ts_code = code if "." in code else f"{code}.{'SH' if code.startswith('6') else 'SZ'}"
            
            df = self.pro.daily(
                ts_code=ts_code,
                start_date=start,
                end_date=end,
                fields='ts_code,trade_date,open,high,low,close,vol,amount,turnover_rate,pct_chg'
            )
            return df.to_dict('records')
        except Exception as e:
            logger.error(f"获取日线数据失败: {str(e)}")
            raise

    async def get_realtime_quotes(self, codes: List[str]) -> List[Dict[str, Any]]:
        """获取实时行情"""
        try:
            # 添加市场后缀
            ts_codes = [
                f"{code}.{'SH' if code.startswith('6') else 'SZ'}" if "." not in code else code
                for code in codes
            ]
            df = ts.get_realtime_quotes(ts_codes)
            return df.to_dict('records')
        except Exception as e:
            logger.error(f"获取实时行情失败: {str(e)}")
            raise

    async def get_index_components(self, index_code: str) -> List[str]:
        """获取指数成分股"""
        try:
            df = self.pro.index_weight(
                index_code=index_code,
                trade_date=datetime.now().strftime("%Y%m%d")
            )
            return [item.split(".")[0] for item in df["con_code"].tolist()]
        except Exception as e:
            logger.error(f"获取指数成分股失败: {str(e)}")
            raise

    async def get_financial_data(
        self,
        code: str,
        report_type: str = 'annual'
    ) -> List[Dict[str, Any]]:
        """获取财务数据"""
        try:
            # 添加市场后缀
            ts_code = code if "." in code else f"{code}.{'SH' if code.startswith('6') else 'SZ'}"
            
            # 根据报表类型选择查询参数
            period = '1231' if report_type == 'annual' else ''
            
            df = self.pro.financials_mainbz(
                ts_code=ts_code,
                period=period,
                type='P'  # P表示按报告期
            )
            return df.to_dict('records')
        except Exception as e:
            logger.error(f"获取财务数据失败: {str(e)}")
            raise
