from datetime import datetime
from typing import Any, Dict, List, Optional
from app.core import logging
import akshare as ak
import pandas as pd

from ..base import DataFetcher
from ..adapter import DataAdapter, BaseDataAdapter

logger = logging.getLogger(__name__)

class AkshareAdapter(BaseDataAdapter):
    """Akshare数据适配器"""
    
    def normalize_stock_info(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        标准化Akshare的股票基本信息。
        
        Akshare字段映射:
            - code -> code
            - name -> name
            - industry -> industry
            - market -> market
            - list_date -> list_date
        """
        return {
            "code": str(raw_data.get("symbol", "")).zfill(6),
            "name": raw_data.get("name", ""),
            "industry": raw_data.get("industry", ""),
            "market": "SH" if str(raw_data.get("symbol", "")).startswith("6") else "SZ",
            "list_date": raw_data.get("list_date", "")
        }

    def normalize_daily_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        标准化Akshare的日线数据。
        
        Akshare字段映射:
            - date -> date
            - open -> open
            - high -> high
            - low -> low
            - close -> close
            - volume -> volume
            - amount -> amount
            - turn -> turnover
            - pct_chg -> change_pct
        """
        return {
            "date": pd.to_datetime(raw_data.get("date")).to_pydatetime(),
            "open": float(raw_data.get("open", 0)),
            "high": float(raw_data.get("high", 0)),
            "low": float(raw_data.get("low", 0)),
            "close": float(raw_data.get("close", 0)),
            "volume": float(raw_data.get("volume", 0)),
            "amount": float(raw_data.get("amount", 0)),
            "turnover": float(raw_data.get("turn", 0)),
            "change_pct": float(raw_data.get("pct_chg", 0))
        }

    def normalize_realtime_quote(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        标准化Akshare的实时行情数据。
        
        Akshare字段映射:
            - symbol -> code
            - current_price -> price
            - change -> change
            - change_percent -> change_pct
            - volume -> volume
            - amount -> amount
            - time -> time
        """
        return {
            "code": str(raw_data.get("symbol", "")).zfill(6),
            "price": float(raw_data.get("current_price", 0)),
            "change": float(raw_data.get("change", 0)),
            "change_pct": float(raw_data.get("change_percent", 0)),
            "volume": float(raw_data.get("volume", 0)),
            "amount": float(raw_data.get("amount", 0)),
            "time": pd.to_datetime(raw_data.get("time")).to_pydatetime()
        }

class AkshareDataFetcher(DataFetcher):
    """Akshare数据获取实现"""

    def __init__(self):
        """初始化Akshare数据获取器"""
        pass  # Akshare不需要初始化配置

    async def get_stock_list(self) -> List[Dict[str, Any]]:
        """获取股票列表"""
        try:
            # 获取A股上市公司基本信息
            df = ak.stock_info_a_code_name()
            
            # 获取行业信息
            industry_df = ak.stock_industry_category_cninfo()
            
            # 合并数据
            df = pd.merge(
                df,
                industry_df[["证券代码", "所属行业"]],
                left_on="code",
                right_on="证券代码",
                how="left"
            )
            
            # 重命名列
            df = df.rename(columns={
                "code": "symbol",
                "name": "name",
                "所属行业": "industry"
            })
            
            return df.to_dict('records')
        except Exception as e:
            logger.error(f"获取股票列表失败: {str(e)}")
            raise

    async def get_daily_data(
        self,
        code: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """获取日线数据"""
        try:
            # 格式化日期
            start = start_date.strftime("%Y%m%d") if start_date else ""
            end = end_date.strftime("%Y%m%d") if end_date else datetime.now().strftime("%Y%m%d")
            
            df = ak.stock_zh_a_hist(
                symbol=code,
                period="daily",
                start_date=start,
                end_date=end,
                adjust="qfq"  # 前复权
            )
            
            return df.to_dict('records')
        except Exception as e:
            logger.error(f"获取日线数据失败: {str(e)}")
            raise

    async def get_realtime_quotes(self, codes: List[str]) -> List[Dict[str, Any]]:
        """获取实时行情"""
        try:
            results = []
            # Akshare需要循环获取每个股票的实时数据
            for code in codes:
                df = ak.stock_zh_a_spot_em()
                df = df[df["代码"] == code]
                if not df.empty:
                    # 重命名列以匹配标准格式
                    df = df.rename(columns={
                        "代码": "symbol",
                        "最新价": "current_price",
                        "涨跌额": "change",
                        "涨跌幅": "change_percent",
                        "成交量": "volume",
                        "成交额": "amount",
                        "时间": "time"
                    })
                    results.extend(df.to_dict('records'))
            return results
        except Exception as e:
            logger.error(f"获取实时行情失败: {str(e)}")
            raise

    async def get_index_components(self, index_code: str) -> List[str]:
        """获取指数成分股"""
        try:
            # 获取指数成分股
            df = ak.index_stock_cons(symbol=index_code)
            # 提取股票代码列
            return df["股票代码"].tolist()
        except Exception as e:
            logger.error(f"获取指数成分股失败: {str(e)}")
            raise

    async def get_financial_data(
        self,
        code: str,
        report_type: str = 'annual'
    ) -> List[Dict[str, Any]]:
        """获取财务数据"""
        try:
            # 根据报表类型选择不同的查询函数
            if report_type == 'annual':
                df = ak.stock_financial_report_sina(
                    symbol=code,
                    report_type="年报"
                )
            else:
                df = ak.stock_financial_report_sina(
                    symbol=code,
                    report_type="季报"
                )
            
            return df.to_dict('records')
        except Exception as e:
            logger.error(f"获取财务数据失败: {str(e)}")
            raise
