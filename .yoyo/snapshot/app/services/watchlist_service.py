from typing import List, Dict, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete, update, and_, func
from sqlalchemy.exc import IntegrityError, NoResultFound

from app.models.watchlist import WatchlistItem
from app.schemas.watchlist import WatchlistItemCreate, WatchlistItemUpdate
# 导入 StockStorageService
from app.services.storage.stock_storage import StockStorageService

class WatchlistService:
    def __init__(self, db: AsyncSession):
        self.db = db
        # 实例化 StockStorageService
        self.stock_storage_service = StockStorageService(db)

    async def get_user_watchlist(
        self, user_id: str, include_details: bool = False
    ) -> List[Dict[str, Any]]:
        """获取用户自选股列表"""
        stmt = (
            select(WatchlistItem)
            .where(WatchlistItem.user_id == user_id)
            .order_by(WatchlistItem.sort_order.asc(), WatchlistItem.added_at.desc())
        )
        result = await self.db.execute(stmt)
        items = result.scalars().all()

        response_items = []
        stock_codes_to_fetch_details = []
        if include_details:
            stock_codes_to_fetch_details = [item.stock_code for item in items]
        
        stocks_details_map = {}
        if include_details and stock_codes_to_fetch_details:
            # 假设 StockStorageService 有一个批量获取股票信息的方法 get_stocks_by_codes
            # 如果没有，需要迭代调用 get_stock_info，但效率较低
            # fetched_stocks = await self.stock_storage_service.get_stocks_by_codes(stock_codes_to_fetch_details)
            # for stock_detail in fetched_stocks:
            #     stocks_details_map[stock_detail.code] = stock_detail.dict() # 假设返回StockInfo模型实例
            # 暂时使用循环调用 get_stock_info，后续可优化为批量接口
            for code in stock_codes_to_fetch_details:
                stock_info = await self.stock_storage_service.get_stock_info(code)
                if stock_info:
                    stocks_details_map[code] = {
                        "code": stock_info.code,
                        "name": stock_info.name,
                        "exchange": stock_info.exchange,
                        "industry": stock_info.industry,
                        # 根据 StockInfo 模型添加更多需要的字段
                    }

        for item in items:
            item_data = {
                "id": item.id,
                "user_id": item.user_id,
                "stock_code": item.stock_code,
                "added_at": item.added_at,
                "sort_order": item.sort_order,
                "stock_info": stocks_details_map.get(item.stock_code) if include_details else None,
            }
            response_items.append(item_data)
        return response_items

    async def add_to_watchlist(self, user_id: str, stock_code: str) -> WatchlistItem:
        """添加股票到自选列表"""
        # 检查股票是否存在 (通过 stock_storage_service)
        existing_stock = await self.stock_storage_service.get_stock_info(stock_code)
        if not existing_stock:
            raise ValueError(f"股票代码 {stock_code} 不存在或无效")
        
        # 检查是否已在自选列表中
        existing_item_stmt = select(WatchlistItem).where(
            and_(WatchlistItem.user_id == user_id, WatchlistItem.stock_code == stock_code)
        )
        result = await self.db.execute(existing_item_stmt)
        if result.scalar_one_or_none() is not None:
            raise IntegrityError("股票已在自选列表中", params=None, orig=None) # 更明确的错误

        # 获取当前最大排序值 + 1 作为新项目的排序值
        max_sort_order_stmt = select(func.max(WatchlistItem.sort_order)).where(
            WatchlistItem.user_id == user_id
        )
        max_sort_order_result = await self.db.execute(max_sort_order_stmt)
        current_max_sort_order = max_sort_order_result.scalar_one_or_none() or 0
        
        new_item = WatchlistItem(
            user_id=user_id,
            stock_code=stock_code,
            sort_order=current_max_sort_order + 1
        )
        self.db.add(new_item)
        try:
            await self.db.commit()
            await self.db.refresh(new_item)
            return new_item
        except IntegrityError as e: # 捕获可能的唯一约束冲突（理论上前面已检查，但作为保险）
            await self.db.rollback()
            raise e # Re-raise or handle as appropriate
        except Exception as e:
            await self.db.rollback()
            raise e

    async def remove_from_watchlist(self, user_id: str, stock_code: str) -> bool:
        """从自选列表移除股票"""
        stmt = (
            delete(WatchlistItem)
            .where(WatchlistItem.user_id == user_id, WatchlistItem.stock_code == stock_code)
        )
        result = await self.db.execute(stmt)
        await self.db.commit()
        return result.rowcount > 0

    async def update_watchlist_item_order(
        self, user_id: str, stock_code: str, new_sort_order: int
    ) -> Optional[WatchlistItem]:
        """更新单个自选股的排序"""
        stmt = (
            update(WatchlistItem)
            .where(WatchlistItem.user_id == user_id, WatchlistItem.stock_code == stock_code)
            .values(sort_order=new_sort_order)
            .returning(WatchlistItem) # 返回更新后的对象
        )
        result = await self.db.execute(stmt)
        await self.db.commit()
        updated_item = result.scalar_one_or_none()
        return updated_item

    async def bulk_update_watchlist_order(
        self, user_id: str, ordered_stock_codes: List[str]
    ) -> List[WatchlistItem]:
        """批量更新自选股列表的顺序，并添加不在列表中的新股票"""
        updated_items = []
        # 1. 获取用户当前的自选股列表
        current_watchlist_stmt = select(WatchlistItem).where(WatchlistItem.user_id == user_id)
        current_watchlist_result = await self.db.execute(current_watchlist_stmt)
        current_items_map = {item.stock_code: item for item in current_watchlist_result.scalars().all()}

        # 2. 遍历新的顺序列表
        for index, stock_code in enumerate(ordered_stock_codes):
            new_sort_order = index + 1 # 排序从1开始或0开始，根据需求调整
            if stock_code in current_items_map:
                # 如果股票已存在，更新其排序
                item_to_update = current_items_map[stock_code]
                if item_to_update.sort_order != new_sort_order:
                    item_to_update.sort_order = new_sort_order
                    self.db.add(item_to_update) # SQLAlchemy 会处理为 UPDATE
                    updated_items.append(item_to_update)
            else:
                # 如果股票不存在，添加为新的自选股
                # 检查股票有效性 (通过 stock_storage_service)
                existing_stock_check = await self.stock_storage_service.get_stock_info(stock_code)
                if not existing_stock_check:
                    # 可以选择跳过无效股票或抛出错误，这里选择抛出 ValueError
                    # 如果希望跳过，可以 print 警告并 continue
                    raise ValueError(f"股票代码 {stock_code} 无效，无法添加到自选股")
                
                new_item = WatchlistItem(
                    user_id=user_id,
                    stock_code=stock_code,
                    sort_order=new_sort_order
                )
                self.db.add(new_item)
                updated_items.append(new_item)
        
        # 3. 删除不在新列表中的旧自选股
        codes_to_remove = set(current_items_map.keys()) - set(ordered_stock_codes)
        if codes_to_remove:
            delete_stmt = delete(WatchlistItem).where(
                WatchlistItem.user_id == user_id,
                WatchlistItem.stock_code.in_(codes_to_remove)
            )
            await self.db.execute(delete_stmt)

        try:
            await self.db.commit()
            # 对于新添加的item，可能需要refresh来获取ID等信息
            for item in updated_items:
                if not item.id: # 简单判断是否为新添加
                    await self.db.refresh(item)
            return updated_items
        except Exception as e:
            await self.db.rollback()
            raise e
