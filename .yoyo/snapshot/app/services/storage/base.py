from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any, Union, Type
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession

from app.models.stock import (
    StockInfo, StockDailyBase, IndicatorVersion,
    StockIndicator
)

class BaseStorageService(ABC):
    """基础存储服务接口，提供数据存储、查询、更新和删除功能"""

    def __init__(self, db: AsyncSession):
        self.db = db

    @abstractmethod
    async def save_stock_info(self, stock_info: Dict[str, Any]) -> StockInfo:
        """保存股票基本信息"""
        pass

    @abstractmethod
    async def save_stock_daily(
        self, 
        stock_code: str, 
        daily_data: Dict[str, Any],
        partition_date: datetime
    ) -> StockDailyBase:
        """保存股票日线数据到指定分区"""
        pass

    @abstractmethod
    async def batch_save_stock_info(self, stock_info_list: List[Dict[str, Any]]) -> List[StockInfo]:
        """批量保存股票基本信息"""
        pass

    @abstractmethod
    async def batch_save_stock_daily(
        self, 
        stock_code: str, 
        daily_data_list: List[Dict[str, Any]],
        partition_date: datetime
    ) -> List[StockDailyBase]:
        """批量保存股票日线数据到指定分区"""
        pass

    @abstractmethod
    async def get_stock_info(self, stock_code: str) -> Optional[StockInfo]:
        """获取股票基本信息"""
        pass

    @abstractmethod
    async def get_stock_daily(
        self,
        stock_code: str,
        start_date: Optional[Union[datetime, str]] = None,
        end_date: Optional[Union[datetime, str]] = None
    ) -> List[StockDailyBase]:
        """获取股票日线数据"""
        pass

    @abstractmethod
    async def get_all_stock_codes(self) -> List[str]:
        """获取所有股票代码列表"""
        pass

    @abstractmethod
    async def update_stock_info(self, stock_code: str, update_data: Dict[str, Any]) -> StockInfo:
        """更新股票基本信息"""
        pass

    @abstractmethod
    async def delete_stock_info(self, stock_code: str) -> bool:
        """删除股票基本信息"""
        pass

    @abstractmethod
    async def delete_stock_daily(
        self,
        stock_code: str,
        start_date: Optional[Union[datetime, str]] = None,
        end_date: Optional[Union[datetime, str]] = None
    ) -> bool:
        """删除股票日线数据"""
        pass

    @abstractmethod
    async def save_indicator(
        self,
        stock_code: str,
        indicator_type: str,
        values: Dict[str, Any],
        trade_date: datetime,
        data_frequency: str = 'D1'
    ) -> StockIndicator:
        """保存技术指标数据"""
        pass

    @abstractmethod
    async def batch_save_indicators(
        self,
        stock_code: str,
        indicators_data: List[Dict[str, Any]]
    ) -> List[StockIndicator]:
        """批量保存技术指标数据"""
        pass

    @abstractmethod
    async def get_indicators(
        self,
        stock_code: str,
        indicator_type: str,
        start_date: Optional[Union[datetime, str]] = None,
        end_date: Optional[Union[datetime, str]] = None,
        data_frequency: str = 'D1'
    ) -> List[StockIndicator]:
        """获取技术指标数据"""
        pass

    @abstractmethod
    async def save_indicator_version(
        self,
        version_data: Dict[str, Any]
    ) -> IndicatorVersion:
        """保存指标版本信息"""
        pass

    @abstractmethod
    async def get_current_indicator_version(
        self,
        indicator_type: str
    ) -> Optional[IndicatorVersion]:
        """获取指标的当前版本信息"""
        pass
