"""
实时数据获取服务模块

提供获取股票实时价格、涨跌幅、成交量等数据的功能
"""

from typing import List, Dict, Any, Optional
import asyncio
from datetime import datetime

from app.core.config import settings
from app.core.logging import logging
from app.services.data_fetcher.factory import DataFetcherFactory

logger = logging.getLogger(__name__)


class RealtimeDataService:
    """实时数据获取服务"""
    
    def __init__(self):
        self._data_provider = None
        self._cache = {}
        self._cache_timeout = 60  # 缓存60秒
        self._last_update = {}
    
    async def _get_data_provider(self):
        """延迟初始化数据提供者"""
        if self._data_provider is None:
            try:
                provider_type = settings.DATA_API_TYPE
                if provider_type == "tushare" and settings.TUSHARE_TOKEN:
                    self._data_provider = DataFetcherFactory.get_fetcher(
                        provider_type, api_token=settings.TUSHARE_TOKEN
                    )
                elif provider_type == "mairui" and settings.MAIRUI_TOKEN:
                    self._data_provider = DataFetcherFactory.get_fetcher(
                        provider_type, licence=settings.MAIRUI_TOKEN
                    )
                else:
                    self._data_provider = DataFetcherFactory.get_fetcher(provider_type)
                    
                logger.info(f"初始化数据提供者: {provider_type}")
            except Exception as e:
                logger.error(f"初始化数据提供者失败: {str(e)}")
                
        return self._data_provider
    
    def _is_cache_valid(self, stock_code: str) -> bool:
        """检查缓存是否有效"""
        if stock_code not in self._last_update:
            return False
        
        time_diff = datetime.now() - self._last_update[stock_code]
        return time_diff.total_seconds() < self._cache_timeout
    
    async def get_realtime_quotes(self, stock_codes: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        获取多只股票的实时行情数据
        
        Args:
            stock_codes: 股票代码列表
            
        Returns:
            Dict[str, Dict[str, Any]]: 股票代码为key，行情数据为value的字典
        """
        if not stock_codes:
            return {}
        
        # 检查缓存
        result = {}
        need_fetch = []
        
        for code in stock_codes:
            if settings.CACHE_ENABLED and self._is_cache_valid(code) and code in self._cache:
                result[code] = self._cache[code]
            else:
                need_fetch.append(code)
        
        # 如果需要从数据提供者获取数据
        if need_fetch:
            try:
                provider = await self._get_data_provider()
                if provider:
                    logger.info(f"从数据提供者获取实时行情: {need_fetch}")
                    quotes = await provider.get_realtime_quotes(need_fetch)
                    
                    # 处理返回的数据
                    for quote in quotes:
                        code = quote.get('code') or quote.get('symbol')
                        if code:
                            # 标准化数据格式
                            normalized_quote = self._normalize_quote_data(quote)
                            result[code] = normalized_quote
                            
                            # 更新缓存（仅在启用缓存时）
                            if settings.CACHE_ENABLED:
                                self._cache[code] = normalized_quote
                                self._last_update[code] = datetime.now()
                            
            except Exception as e:
                logger.error(f"获取实时行情失败: {str(e)}")
        
        return result
    
    def _normalize_quote_data(self, quote: Dict[str, Any]) -> Dict[str, Any]:
        """标准化行情数据格式"""
        try:
            # 提取不同数据源的字段
            price = (quote.get('price') or 
                    quote.get('current_price') or 
                    quote.get('close') or 
                    quote.get('p') or 0.0)
            
            change_percent = (quote.get('change_pct') or 
                            quote.get('change_percent') or 
                            quote.get('pc') or 0.0)
            
            volume = (quote.get('volume') or 
                     quote.get('vol') or 
                     quote.get('v') or 0)
            
            # 确保数值类型正确
            price = float(price) if price else 0.0
            change_percent = float(change_percent) if change_percent else 0.0
            volume = int(float(volume)) if volume else 0
            
            return {
                'price': price,
                'change_percent': change_percent,
                'volume': volume,
                'timestamp': datetime.now()
            }
            
        except (ValueError, TypeError) as e:
            logger.warning(f"数据格式化失败: {str(e)}, 原始数据: {quote}")
            return {
                'price': 0.0,
                'change_percent': 0.0,
                'volume': 0,
                'timestamp': datetime.now()
            }
    
    async def get_single_quote(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """
        获取单只股票的实时行情
        
        Args:
            stock_code: 股票代码
            
        Returns:
            Optional[Dict[str, Any]]: 行情数据或None
        """
        quotes = await self.get_realtime_quotes([stock_code])
        return quotes.get(stock_code)
    
    def clear_cache(self):
        """清除缓存"""
        self._cache.clear()
        self._last_update.clear()
        logger.info("实时数据缓存已清除")


# 全局实例
_realtime_service = None


def get_realtime_service() -> RealtimeDataService:
    """获取实时数据服务实例"""
    global _realtime_service
    if _realtime_service is None:
        _realtime_service = RealtimeDataService()
    return _realtime_service
