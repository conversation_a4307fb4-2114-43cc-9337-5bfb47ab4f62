from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import pandas as pd
from sqlalchemy.ext.asyncio import AsyncSession

from app.core import logging
from app.core.scheduler import ScheduledTask
from app.core.config import settings
from app.core.database import get_db
# Moved import DataFetcherFactory into init_data_fetcher
from app.services.storage.stock_storage import StockStorageService

logger = logging.getLogger(__name__)

class StockDataFetchService:
    """Service for fetching stock data on a scheduled basis"""
    
    def __init__(self):
        self.data_fetcher = None

    async def init_data_fetcher(self):
        """初始化数据获取器"""
        if not self.data_fetcher:
            from app.services.data_fetcher.factory import DataFetcherFactory # Import moved here
            factory = DataFetcherFactory()
            self.data_fetcher = factory.create_fetcher(settings.DATA_API_TYPE)
        return self.data_fetcher
    
    async def _get_storage_service(self) -> Optional[StockStorageService]:
        """获取存储服务实例"""
        try:
            db = await get_db()
            return StockStorageService(db)
        except Exception as e:
            logger.error(f"获取数据库会话失败: {str(e)}")
            return None

    async def _get_stock_codes(self, storage: StockStorageService) -> List[str]:
        """获取股票代码列表，优先从数据库获取，失败则从数据提供者获取。"""
        stock_codes = []
        try:
            stock_codes = await storage.get_all_stock_codes()
            if not stock_codes:
                logger.warning("数据库中未找到股票代码，尝试从数据提供者获取。")
            else:
                logger.info(f"从数据库成功获取 {len(stock_codes)} 个股票代码。")
        except Exception as db_err:
            logger.error(f"从数据库获取股票代码失败: {db_err}，尝试从数据提供者获取。")

        # 如果数据库获取失败或为空，则从 fetcher 获取
        if not stock_codes:
            try:
                # 初始化数据获取器
                fetcher = await self.init_data_fetcher()
                result = await fetcher.get_stock_list()
                if result:
                    stock_codes = [stock['code'] for stock in result]
                    logger.info(f"从数据提供者成功获取 {len(stock_codes)} 个股票代码。")
                else:
                    logger.error("无法从数据提供者获取股票列表。")
                    # 返回空列表，让调用者处理
                    return []
            except Exception as fetch_err:
                logger.error(f"从数据提供者获取股票列表也失败: {fetch_err}")
                # 返回空列表，让调用者处理
                return []
        
        return stock_codes

    @ScheduledTask(cron=settings.STOCK_LIST_UPDATE_CRON)
    async def update_stock_list(self, *args):
        """定时更新股票列表"""
        try:
            logger.info("开始更新股票列表...")
            
            # 获取最新的股票列表
            # 初始化数据获取器
            fetcher = await self.init_data_fetcher()
            stock_list = await fetcher.get_stock_list()
            if not stock_list:
                logger.warning("未获取到股票列表数据")
                return
                
            # 保存到数据库
            storage = await self._get_storage_service()
            if(storage is None):
                logger.error("获取存储服务实例失败")
                return
            await storage.batch_save_stock_info(stock_list)
            
            logger.info(f"成功更新{len(stock_list)}只股票的基本信息")
            
        except Exception as e:
            logger.error(f"更新股票列表失败: {str(e)}")
            raise
    
    @ScheduledTask(cron=settings.DATA_UPDATE_CRON)
    async def update_daily_data(self, *args):
        """定时更新股票日线数据"""
        try:
            logger.info("开始更新股票日线数据...")

            # 初始化数据获取器
            fetcher = await self.init_data_fetcher()
            if not fetcher:
                logger.error("初始化数据获取器失败，更新日线数据任务终止。")
                return
            
            # 获取存储服务
            storage = await self._get_storage_service()
            if(storage is None):
                logger.error("获取存储服务实例失败")
                return
            
            # 获取所有股票列表
            stock_codes = await self._get_stock_codes(storage)
            if not stock_codes:
                logger.error("未能获取到任何股票代码，更新日线数据任务终止。")
                return

            # 获取当前日期
            today = datetime.now().date()
            
            # 按日期和股票代码收集所有数据
            all_data: Dict[datetime, Dict[str, List[Dict[str, Any]]]] = {}
            failed_stocks = 0
            
            # 为每只股票获取最新的日线数据
            for stock_code in stock_codes:
                try:
                    # 获取最新的日线数据
                    daily_data = await fetcher.get_daily_data(
                        stock_code,
                        start_date=(today - timedelta(days=1)),
                        end_date=today
                    )
                    
                    if not daily_data:
                        continue
                    
                    # 按日期分组数据
                    for data in daily_data:
                        trade_date = data['trade_date']
                        if isinstance(trade_date, str):
                            trade_date = datetime.strptime(trade_date, "%Y-%m-%d")
                        
                        if trade_date not in all_data:
                            all_data[trade_date] = {}
                        
                        if stock_code not in all_data[trade_date]:
                            all_data[trade_date][stock_code] = []
                        
                        all_data[trade_date][stock_code].append(data)
                    logger.debug(f"获取股票 {stock_code} 日线数据成功")
                    
                except Exception as e:
                    error_msg = f"获取股票 {stock_code} 日线数据失败: {str(e)}"
                    logger.error(error_msg)
                    failed_stocks += 1
            
            # 批量保存所有收集到的数据
            total_updated = 0
            for date, stocks_data in all_data.items():
                for stock_code, data in stocks_data.items():
                    await storage.batch_save_stock_daily(stock_code, data, date)
                    total_updated += 1
            
            logger.info(f"成功更新{total_updated}条股票的日线数据，失败{failed_stocks}条")
            
        except Exception as e:
            logger.error(f"更新日线数据失败: {str(e)}")
            raise


# Initialize the service
stock_data_fetch_service = StockDataFetchService()
