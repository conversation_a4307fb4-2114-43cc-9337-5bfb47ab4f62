"""
用户服务模块

提供用户相关的服务功能，如创建用户、验证用户等。
"""

from typing import Optional
from sqlalchemy.orm import Session
from fastapi import Header, HTTPException, Depends

from app.models.user import User
from app.utils.auth import get_password_hash, verify_password
from app.core.cache import get_cached_result, cache_result


class UserService:
    """用户服务"""
    
    @staticmethod
    def create_user(db: Session, username: str, password: str, email: Optional[str] = None, 
                   is_admin: bool = False) -> User:
        """
        创建新用户
        
        Args:
            db: 数据库会话
            username: 用户名
            password: 密码
            email: 邮箱
            is_admin: 是否为管理员
            
        Returns:
            User: 创建的用户对象
        """
        hashed_password = get_password_hash(password)
        db_user = User(
            username=username,
            password_hash=hashed_password,
            email=email,
            is_admin=is_admin
        )
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        return db_user
    
    @staticmethod
    def authenticate_user(db: Session, username: str, password: str) -> Optional[User]:
        """
        验证用户
        
        Args:
            db: 数据库会话
            username: 用户名
            password: 密码
            
        Returns:
            Optional[User]: 验证成功返回用户对象，失败返回None
        """
        user = db.query(User).filter(User.username == username).first()
        if not user or not verify_password(password, user.password_hash):
            return None
        return user
    
    @staticmethod
    def get_user_by_username(db: Session, username: str) -> Optional[User]:
        """
        通过用户名获取用户
        
        Args:
            db: 数据库会话
            username: 用户名
            
        Returns:
            Optional[User]: 找到返回用户对象，未找到返回None
        """
        return db.query(User).filter(User.username == username).first()
    
    @staticmethod
    def get_user_by_id(db: Session, user_id: int) -> Optional[User]:
        """
        通过ID获取用户
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            
        Returns:
            Optional[User]: 找到返回用户对象，未找到返回None
        """
        return db.query(User).filter(User.id == user_id).first()

# 假设的token到userId的映射，实际应用中应该从数据库或其他认证服务获取
# 这里为了演示，我们硬编码一些token和userId的对应关系
# 在实际部署时，这些token应该是动态生成和管理的
MOCK_TOKEN_USER_MAP = {
    "test_token_user1": "user_id_1",
    "test_token_admin": "admin_id_0",
}

async def get_user_id_from_token(token: str) -> Optional[str]:
    """
    根据token从缓存或模拟数据中获取用户ID
    """
    cache_key = f"user_token:{token}"
    user_id = await get_cached_result(cache_key)
    if user_id:
        return user_id

    # 如果缓存中没有，从模拟数据中查找 (实际应用中可能是查询数据库)
    user_id = MOCK_TOKEN_USER_MAP.get(token)
    if user_id:
        # 将token和userId的映射存入缓存，例如缓存1小时
        await cache_result(cache_key, user_id, expire=3600)
    return user_id

async def get_current_user_id(token: Optional[str] = Header(None, alias="Authorization")) -> Optional[str]:
    """
    从请求头中获取Token并返回用户ID。
    如果token以 "Bearer " 开头，则去除此前缀。
    """
    if not token:
        return None

    # 兼容 "Bearer <token>" 格式
    if token.startswith("Bearer "):
        token = token.split("Bearer ")[1]

    user_id = await get_user_id_from_token(token)
    if not user_id:
        # 实际应用中，如果token无效且接口强制要求认证，应该在此处抛出HTTPException
        # 但为了逐步迁移，这里暂时返回None，由接口自行决定如何处理
        return None
    return user_id

async def require_user(user_id: Optional[str] = Depends(get_current_user_id)) -> str:
    """
    FastAPI依赖项，用于确保用户已认证。
    如果用户未认证（即user_id为None），则抛出401 HTTPException。
    """
    if user_id is None:
        raise HTTPException(status_code=401, detail="Not authenticated")
    return user_id
