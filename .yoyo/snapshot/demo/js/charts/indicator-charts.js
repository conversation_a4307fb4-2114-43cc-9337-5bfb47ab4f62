/**
 * 技术指标图表组件
 * 提供完整的技术指标可视化功能
 */

class IndicatorCharts {
    constructor(options = {}) {
        this.containers = options.containers || {};
        this.charts = {};
        this.data = {};
        this.options = {
            theme: options.theme || 'dark',
            responsive: options.responsive !== false,
            animation: options.animation !== false,
            ...options
        };
        
        this.initializeCharts();
        this.setupEventListeners();
    }

    /**
     * 初始化所有图表
     */
    initializeCharts() {
        // 主图表：K线 + 布林带
        if (this.containers.main) {
            this.charts.main = echarts.init(
                document.getElementById(this.containers.main),
                this.options.theme
            );
        }

        // 成交量图表
        if (this.containers.volume) {
            this.charts.volume = echarts.init(
                document.getElementById(this.containers.volume),
                this.options.theme
            );
        }

        // 技术指标图表：KDJ + 成交量布林带
        if (this.containers.indicators) {
            this.charts.indicators = echarts.init(
                document.getElementById(this.containers.indicators),
                this.options.theme
            );
        }

        // 响应式处理
        if (this.options.responsive) {
            this.setupResponsive();
        }
    }

    /**
     * 设置响应式
     */
    setupResponsive() {
        const resizeHandler = ChartUtils.debounceChartUpdate(() => {
            Object.values(this.charts).forEach(chart => {
                if (chart) {
                    chart.resize();
                }
            });
        }, 300);

        window.addEventListener('resize', resizeHandler);
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 图表联动
        this.setupChartSync();
        
        // 十字线联动
        this.setupCrosshairSync();
    }

    /**
     * 设置图表同步
     */
    setupChartSync() {
        const charts = Object.values(this.charts).filter(chart => chart);
        
        charts.forEach(chart => {
            chart.on('datazoom', (params) => {
                charts.forEach(otherChart => {
                    if (otherChart !== chart) {
                        otherChart.dispatchAction({
                            type: 'dataZoom',
                            ...params
                        });
                    }
                });
            });
        });
    }

    /**
     * 设置十字线同步
     */
    setupCrosshairSync() {
        const charts = Object.values(this.charts).filter(chart => chart);
        
        charts.forEach(chart => {
            chart.on('mousemove', (params) => {
                if (params.dataIndex !== undefined) {
                    charts.forEach(otherChart => {
                        if (otherChart !== chart) {
                            otherChart.dispatchAction({
                                type: 'showTip',
                                seriesIndex: 0,
                                dataIndex: params.dataIndex
                            });
                        }
                    });
                }
            });

            chart.on('mouseleave', () => {
                charts.forEach(otherChart => {
                    if (otherChart !== chart) {
                        otherChart.dispatchAction({
                            type: 'hideTip'
                        });
                    }
                });
            });
        });
    }

    /**
     * 渲染主图表（K线 + 布林带）
     * @param {Object} data - 图表数据
     */
    renderMainChart(data) {
        if (!this.charts.main || !data) return;

        const { dates, klineData, bollingerData, signals } = data;
        
        const option = {
            ...ChartUtils.getDefaultTheme(),
            title: {
                text: '股价走势与布林带',
                left: 'center',
                textStyle: {
                    color: '#e2e8f0',
                    fontSize: 16,
                    fontWeight: 'bold'
                }
            },
            xAxis: {
                type: 'category',
                data: dates,
                ...ChartUtils.getDefaultTheme().xAxis
            },
            yAxis: {
                type: 'value',
                scale: true,
                ...ChartUtils.getDefaultTheme().yAxis
            },
            series: this.createMainChartSeries(klineData, bollingerData, signals),
            dataZoom: ChartUtils.createDataZoom(true),
            toolbox: ChartUtils.createToolbox(),
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                },
                formatter: this.createMainChartTooltip.bind(this),
                ...ChartUtils.getDefaultTheme().tooltip
            },
            legend: {
                data: ['K线', '布林带上轨', '布林带中轨', '布林带下轨'],
                ...ChartUtils.getDefaultTheme().legend
            }
        };

        // 添加响应式配置
        if (this.options.responsive) {
            const containerWidth = document.getElementById(this.containers.main).offsetWidth;
            Object.assign(option, ChartUtils.createResponsiveConfig(containerWidth));
        }

        this.charts.main.setOption(option, true);
        this.data.main = data;
    }

    /**
     * 创建主图表系列数据
     * @param {Array} klineData - K线数据
     * @param {Object} bollingerData - 布林带数据
     * @param {Object} signals - 交易信号
     * @returns {Array} 系列配置
     */
    createMainChartSeries(klineData, bollingerData, signals) {
        const series = [];

        // K线系列
        if (klineData) {
            const candlestickConfig = ChartUtils.getCandlestickConfig();
            series.push({
                name: 'K线',
                data: klineData,
                ...candlestickConfig,
                markPoint: signals ? this.createSignalMarkPoints(signals) : undefined
            });
        }

        // 布林带系列
        if (bollingerData) {
            const bollingerConfig = ChartUtils.getBollingerBandsConfig();
            
            // 上轨
            if (bollingerData.upper) {
                series.push({
                    name: '布林带上轨',
                    data: bollingerData.upper,
                    ...bollingerConfig.upper
                });
            }

            // 中轨
            if (bollingerData.middle) {
                series.push({
                    name: '布林带中轨',
                    data: bollingerData.middle,
                    ...bollingerConfig.middle
                });
            }

            // 下轨
            if (bollingerData.lower) {
                series.push({
                    name: '布林带下轨',
                    data: bollingerData.lower,
                    ...bollingerConfig.lower
                });
            }
        }

        return series;
    }

    /**
     * 创建交易信号标记点
     * @param {Object} signals - 交易信号
     * @returns {Object} 标记点配置
     */
    createSignalMarkPoints(signals) {
        const markPointData = [];

        // 买入信号
        if (signals.buySignals) {
            signals.buySignals.forEach(signal => {
                markPointData.push({
                    coord: [signal.index, signal.price],
                    value: 'B',
                    symbol: 'triangle',
                    symbolSize: [15, 20],
                    itemStyle: {
                        color: '#10b981'
                    },
                    label: {
                        show: true,
                        position: 'top',
                        formatter: 'B',
                        color: '#ffffff',
                        fontSize: 10,
                        fontWeight: 'bold'
                    }
                });
            });
        }

        // 卖出信号
        if (signals.sellSignals) {
            signals.sellSignals.forEach(signal => {
                markPointData.push({
                    coord: [signal.index, signal.price],
                    value: 'S',
                    symbol: 'pin',
                    symbolSize: [15, 20],
                    itemStyle: {
                        color: '#ef4444'
                    },
                    label: {
                        show: true,
                        position: 'bottom',
                        formatter: 'S',
                        color: '#ffffff',
                        fontSize: 10,
                        fontWeight: 'bold'
                    }
                });
            });
        }

        return {
            data: markPointData
        };
    }

    /**
     * 创建主图表工具提示
     * @param {Array} params - 参数数组
     * @returns {string} HTML字符串
     */
    createMainChartTooltip(params) {
        if (!params || params.length === 0) return '';

        const date = params[0].axisValue;
        let html = `<div style="margin-bottom: 8px; font-weight: bold;">${date}</div>`;

        params.forEach(param => {
            const { seriesName, data, color } = param;
            
            if (seriesName === 'K线' && Array.isArray(data)) {
                const [open, close, low, high] = data;
                const change = close - open;
                const changePercent = ((change / open) * 100).toFixed(2);
                const changeColor = change >= 0 ? '#10b981' : '#ef4444';
                
                html += `
                    <div style="margin: 4px 0;">
                        <span style="color: ${color};">●</span> ${seriesName}<br/>
                        <span style="margin-left: 12px;">开盘: ${ChartUtils.formatNumber(open)}</span><br/>
                        <span style="margin-left: 12px;">收盘: ${ChartUtils.formatNumber(close)}</span><br/>
                        <span style="margin-left: 12px;">最高: ${ChartUtils.formatNumber(high)}</span><br/>
                        <span style="margin-left: 12px;">最低: ${ChartUtils.formatNumber(low)}</span><br/>
                        <span style="margin-left: 12px; color: ${changeColor};">
                            涨跌: ${change >= 0 ? '+' : ''}${ChartUtils.formatNumber(change)} (${changePercent}%)
                        </span>
                    </div>
                `;
            } else if (typeof data === 'number') {
                html += `
                    <div style="margin: 2px 0;">
                        <span style="color: ${color};">●</span> ${seriesName}: ${ChartUtils.formatNumber(data)}
                    </div>
                `;
            }
        });

        return html;
    }

    /**
     * 渲染成交量图表
     * @param {Object} data - 成交量数据
     */
    renderVolumeChart(data) {
        if (!this.charts.volume || !data) return;

        const { dates, volumeData, volumeAnalysis } = data;

        const option = {
            ...ChartUtils.getDefaultTheme(),
            title: {
                text: '成交量分析',
                left: 'center',
                textStyle: {
                    color: '#e2e8f0',
                    fontSize: 14,
                    fontWeight: 'bold'
                }
            },
            xAxis: {
                type: 'category',
                data: dates,
                ...ChartUtils.getDefaultTheme().xAxis
            },
            yAxis: [
                {
                    type: 'value',
                    name: '成交量',
                    position: 'left',
                    ...ChartUtils.getDefaultTheme().yAxis
                },
                {
                    type: 'value',
                    name: '差异指标',
                    position: 'right',
                    ...ChartUtils.getDefaultTheme().yAxis
                }
            ],
            series: this.createVolumeChartSeries(volumeData, volumeAnalysis),
            dataZoom: ChartUtils.createDataZoom(false),
            tooltip: {
                trigger: 'axis',
                formatter: this.createVolumeChartTooltip.bind(this),
                ...ChartUtils.getDefaultTheme().tooltip
            },
            legend: {
                data: ['成交量', '内盘量', '外盘量', '差异曲线'],
                ...ChartUtils.getDefaultTheme().legend
            }
        };

        this.charts.volume.setOption(option, true);
        this.data.volume = data;
    }

    /**
     * 创建成交量图表系列数据
     * @param {Array} volumeData - 成交量数据
     * @param {Object} volumeAnalysis - 成交量分析数据
     * @returns {Array} 系列配置
     */
    createVolumeChartSeries(volumeData, volumeAnalysis) {
        const series = [];

        // 成交量柱状图
        if (volumeData) {
            series.push({
                name: '成交量',
                type: 'bar',
                data: volumeData,
                itemStyle: {
                    color: function(params) {
                        return params.dataIndex % 2 === 0 ? '#10b981' : '#ef4444';
                    },
                    opacity: 0.7
                },
                yAxisIndex: 0
            });
        }

        // 内外盘分析
        if (volumeAnalysis) {
            if (volumeAnalysis.inVolume) {
                series.push({
                    name: '内盘量',
                    type: 'line',
                    data: volumeAnalysis.inVolume,
                    lineStyle: {
                        color: '#ef4444',
                        width: 1
                    },
                    symbol: 'none',
                    yAxisIndex: 0
                });
            }

            if (volumeAnalysis.outVolume) {
                series.push({
                    name: '外盘量',
                    type: 'line',
                    data: volumeAnalysis.outVolume,
                    lineStyle: {
                        color: '#10b981',
                        width: 1
                    },
                    symbol: 'none',
                    yAxisIndex: 0
                });
            }

            if (volumeAnalysis.finalSmoothed) {
                series.push({
                    name: '差异曲线',
                    type: 'line',
                    data: volumeAnalysis.finalSmoothed,
                    lineStyle: {
                        color: '#3b82f6',
                        width: 2
                    },
                    symbol: 'none',
                    smooth: true,
                    yAxisIndex: 1
                });
            }
        }

        return series;
    }

    /**
     * 渲染技术指标图表（KDJ + 成交量布林带）
     * @param {Object} data - 技术指标数据
     */
    renderIndicatorChart(data) {
        if (!this.charts.indicators || !data) return;

        const { dates, kdjData, volumeBollingerData, signals } = data;

        const option = {
            ...ChartUtils.getDefaultTheme(),
            title: {
                text: 'KDJ指标与成交量布林带',
                left: 'center',
                textStyle: {
                    color: '#e2e8f0',
                    fontSize: 14,
                    fontWeight: 'bold'
                }
            },
            xAxis: {
                type: 'category',
                data: dates,
                ...ChartUtils.getDefaultTheme().xAxis
            },
            yAxis: [
                {
                    type: 'value',
                    name: 'KDJ',
                    min: 0,
                    max: 100,
                    position: 'left',
                    ...ChartUtils.getDefaultTheme().yAxis
                },
                {
                    type: 'value',
                    name: '成交量布林带',
                    position: 'right',
                    ...ChartUtils.getDefaultTheme().yAxis
                }
            ],
            series: this.createIndicatorChartSeries(kdjData, volumeBollingerData, signals),
            dataZoom: ChartUtils.createDataZoom(false),
            tooltip: {
                trigger: 'axis',
                formatter: this.createIndicatorChartTooltip.bind(this),
                ...ChartUtils.getDefaultTheme().tooltip
            },
            legend: {
                data: ['K', 'D', 'J', '成交量上轨', '成交量中轨', '成交量下轨'],
                ...ChartUtils.getDefaultTheme().legend
            }
        };

        // 添加KDJ超买超卖线
        if (kdjData) {
            option.series.push({
                name: '超买线',
                type: 'line',
                data: new Array(dates.length).fill(80),
                lineStyle: {
                    color: '#6b7280',
                    type: 'dashed',
                    width: 1
                },
                symbol: 'none',
                silent: true,
                yAxisIndex: 0
            });

            option.series.push({
                name: '超卖线',
                type: 'line',
                data: new Array(dates.length).fill(20),
                lineStyle: {
                    color: '#6b7280',
                    type: 'dashed',
                    width: 1
                },
                symbol: 'none',
                silent: true,
                yAxisIndex: 0
            });
        }

        this.charts.indicators.setOption(option, true);
        this.data.indicators = data;
    }

    /**
     * 创建技术指标图表系列数据
     * @param {Object} kdjData - KDJ数据
     * @param {Object} volumeBollingerData - 成交量布林带数据
     * @param {Object} signals - 交易信号
     * @returns {Array} 系列配置
     */
    createIndicatorChartSeries(kdjData, volumeBollingerData, signals) {
        const series = [];

        // KDJ系列
        if (kdjData) {
            const kdjConfig = ChartUtils.getKDJConfig();

            if (kdjData.K) {
                series.push({
                    name: 'K',
                    data: kdjData.K,
                    yAxisIndex: 0,
                    ...kdjConfig.K
                });
            }

            if (kdjData.D) {
                series.push({
                    name: 'D',
                    data: kdjData.D,
                    yAxisIndex: 0,
                    ...kdjConfig.D
                });
            }

            if (kdjData.J) {
                series.push({
                    name: 'J',
                    data: kdjData.J,
                    yAxisIndex: 0,
                    ...kdjConfig.J
                });
            }
        }

        // 成交量布林带系列
        if (volumeBollingerData) {
            if (volumeBollingerData.upper) {
                series.push({
                    name: '成交量上轨',
                    type: 'line',
                    data: volumeBollingerData.upper,
                    lineStyle: {
                        color: '#ef4444',
                        width: 1
                    },
                    symbol: 'none',
                    yAxisIndex: 1
                });
            }

            if (volumeBollingerData.middle) {
                series.push({
                    name: '成交量中轨',
                    type: 'line',
                    data: volumeBollingerData.middle,
                    lineStyle: {
                        color: '#f59e0b',
                        width: 2
                    },
                    symbol: 'none',
                    yAxisIndex: 1
                });
            }

            if (volumeBollingerData.lower) {
                series.push({
                    name: '成交量下轨',
                    type: 'line',
                    data: volumeBollingerData.lower,
                    lineStyle: {
                        color: '#10b981',
                        width: 1
                    },
                    symbol: 'none',
                    yAxisIndex: 1
                });
            }
        }

        return series;
    }

    /**
     * 创建成交量图表工具提示
     * @param {Array} params - 参数数组
     * @returns {string} HTML字符串
     */
    createVolumeChartTooltip(params) {
        if (!params || params.length === 0) return '';

        const date = params[0].axisValue;
        let html = `<div style="margin-bottom: 8px; font-weight: bold;">${date}</div>`;

        params.forEach(param => {
            const { seriesName, data, color } = param;

            if (typeof data === 'number') {
                let formattedValue = data;
                if (seriesName.includes('量')) {
                    formattedValue = this.formatVolume(data);
                } else {
                    formattedValue = ChartUtils.formatNumber(data);
                }

                html += `
                    <div style="margin: 2px 0;">
                        <span style="color: ${color};">●</span> ${seriesName}: ${formattedValue}
                    </div>
                `;
            }
        });

        return html;
    }

    /**
     * 创建技术指标图表工具提示
     * @param {Array} params - 参数数组
     * @returns {string} HTML字符串
     */
    createIndicatorChartTooltip(params) {
        if (!params || params.length === 0) return '';

        const date = params[0].axisValue;
        let html = `<div style="margin-bottom: 8px; font-weight: bold;">${date}</div>`;

        // 分组显示KDJ和成交量布林带
        const kdjParams = params.filter(p => ['K', 'D', 'J'].includes(p.seriesName));
        const volumeParams = params.filter(p => p.seriesName.includes('成交量'));

        if (kdjParams.length > 0) {
            html += '<div style="margin: 4px 0; font-weight: bold; color: #3b82f6;">KDJ指标</div>';
            kdjParams.forEach(param => {
                const { seriesName, data, color } = param;
                if (typeof data === 'number') {
                    html += `
                        <div style="margin: 2px 0; margin-left: 12px;">
                            <span style="color: ${color};">●</span> ${seriesName}: ${ChartUtils.formatNumber(data)}
                        </div>
                    `;
                }
            });
        }

        if (volumeParams.length > 0) {
            html += '<div style="margin: 4px 0; font-weight: bold; color: #f59e0b;">成交量布林带</div>';
            volumeParams.forEach(param => {
                const { seriesName, data, color } = param;
                if (typeof data === 'number') {
                    html += `
                        <div style="margin: 2px 0; margin-left: 12px;">
                            <span style="color: ${color};">●</span> ${seriesName}: ${ChartUtils.formatNumber(data)}
                        </div>
                    `;
                }
            });
        }

        return html;
    }

    /**
     * 格式化成交量显示
     * @param {number} volume - 成交量
     * @returns {string} 格式化后的字符串
     */
    formatVolume(volume) {
        if (volume >= 100000000) {
            return `${(volume / 100000000).toFixed(2)}亿`;
        } else if (volume >= 10000) {
            return `${(volume / 10000).toFixed(2)}万`;
        } else {
            return volume.toString();
        }
    }

    /**
     * 更新所有图表
     * @param {Object} allData - 所有图表数据
     */
    updateAllCharts(allData) {
        const { main, volume, indicators } = allData;

        if (main) {
            this.renderMainChart(main);
        }

        if (volume) {
            this.renderVolumeChart(volume);
        }

        if (indicators) {
            this.renderIndicatorChart(indicators);
        }
    }

    /**
     * 切换指标显示
     * @param {string} chartType - 图表类型
     * @param {string} seriesName - 系列名称
     * @param {boolean} visible - 是否显示
     */
    toggleSeries(chartType, seriesName, visible) {
        const chart = this.charts[chartType];
        if (!chart) return;

        chart.dispatchAction({
            type: visible ? 'legendSelect' : 'legendUnSelect',
            name: seriesName
        });
    }

    /**
     * 重置图表缩放
     * @param {string} chartType - 图表类型（可选，不传则重置所有图表）
     */
    resetZoom(chartType) {
        const charts = chartType ? [this.charts[chartType]] : Object.values(this.charts);

        charts.forEach(chart => {
            if (chart) {
                chart.dispatchAction({
                    type: 'dataZoom',
                    start: 0,
                    end: 100
                });
            }
        });
    }

    /**
     * 销毁所有图表
     */
    dispose() {
        Object.values(this.charts).forEach(chart => {
            if (chart) {
                chart.dispose();
            }
        });

        this.charts = {};
        this.data = {};

        // 移除事件监听器
        window.removeEventListener('resize', this.resizeHandler);
    }

    /**
     * 获取图表实例
     * @param {string} chartType - 图表类型
     * @returns {Object} ECharts实例
     */
    getChart(chartType) {
        return this.charts[chartType];
    }

    /**
     * 获取图表数据
     * @param {string} chartType - 图表类型
     * @returns {Object} 图表数据
     */
    getData(chartType) {
        return this.data[chartType];
    }

    /**
     * 设置图表主题
     * @param {string} theme - 主题名称
     */
    setTheme(theme) {
        this.options.theme = theme;

        // 重新初始化图表
        this.dispose();
        this.initializeCharts();

        // 重新渲染数据
        if (Object.keys(this.data).length > 0) {
            this.updateAllCharts(this.data);
        }
    }
}

// 导出图表组件类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = IndicatorCharts;
} else if (typeof window !== 'undefined') {
    window.IndicatorCharts = IndicatorCharts;
}
