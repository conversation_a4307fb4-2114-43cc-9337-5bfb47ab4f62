/**
 * 图表工具函数库
 * 提供ECharts图表的通用配置和工具函数
 */

class ChartUtils {
    /**
     * 获取默认的图表主题配置
     * @returns {Object} 主题配置
     */
    static getDefaultTheme() {
        return {
            backgroundColor: 'transparent',
            textStyle: {
                color: '#e2e8f0',
                fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true,
                backgroundColor: 'transparent',
                borderColor: '#374151'
            },
            xAxis: {
                type: 'category',
                axisLine: {
                    lineStyle: { color: '#374151' }
                },
                axisTick: {
                    lineStyle: { color: '#374151' }
                },
                axisLabel: {
                    color: '#9ca3af',
                    fontSize: 12
                },
                splitLine: {
                    show: false
                }
            },
            yAxis: {
                type: 'value',
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                axisLabel: {
                    color: '#9ca3af',
                    fontSize: 12
                },
                splitLine: {
                    lineStyle: {
                        color: '#374151',
                        type: 'dashed'
                    }
                }
            },
            tooltip: {
                backgroundColor: 'rgba(31, 41, 55, 0.95)',
                borderColor: '#374151',
                textStyle: {
                    color: '#e2e8f0',
                    fontSize: 12
                },
                extraCssText: 'backdrop-filter: blur(10px); border-radius: 8px;'
            },
            legend: {
                textStyle: {
                    color: '#e2e8f0'
                },
                top: 10,
                right: 20
            }
        };
    }

    /**
     * 获取K线图配置
     * @returns {Object} K线图配置
     */
    static getCandlestickConfig() {
        return {
            type: 'candlestick',
            itemStyle: {
                color: '#10b981',      // 阳线颜色
                color0: '#ef4444',     // 阴线颜色
                borderColor: '#10b981', // 阳线边框
                borderColor0: '#ef4444' // 阴线边框
            },
            emphasis: {
                itemStyle: {
                    color: '#34d399',
                    color0: '#f87171',
                    borderColor: '#34d399',
                    borderColor0: '#f87171'
                }
            }
        };
    }

    /**
     * 获取布林带配置
     * @returns {Object} 布林带配置
     */
    static getBollingerBandsConfig() {
        return {
            upper: {
                type: 'line',
                smooth: true,
                symbol: 'none',
                lineStyle: {
                    color: '#ef4444',
                    width: 1,
                    type: 'solid'
                },
                areaStyle: {
                    color: 'rgba(239, 68, 68, 0.1)'
                }
            },
            middle: {
                type: 'line',
                smooth: true,
                symbol: 'none',
                lineStyle: {
                    color: '#f59e0b',
                    width: 2,
                    type: 'solid'
                }
            },
            lower: {
                type: 'line',
                smooth: true,
                symbol: 'none',
                lineStyle: {
                    color: '#10b981',
                    width: 1,
                    type: 'solid'
                },
                areaStyle: {
                    color: 'rgba(16, 185, 129, 0.1)'
                }
            }
        };
    }

    /**
     * 获取KDJ指标配置
     * @returns {Object} KDJ配置
     */
    static getKDJConfig() {
        return {
            K: {
                type: 'line',
                smooth: true,
                symbol: 'none',
                lineStyle: {
                    color: '#3b82f6',
                    width: 2
                }
            },
            D: {
                type: 'line',
                smooth: true,
                symbol: 'none',
                lineStyle: {
                    color: '#8b5cf6',
                    width: 2
                }
            },
            J: {
                type: 'line',
                smooth: true,
                symbol: 'none',
                lineStyle: {
                    color: '#06b6d4',
                    width: 2
                }
            }
        };
    }

    /**
     * 获取成交量配置
     * @returns {Object} 成交量配置
     */
    static getVolumeConfig() {
        return {
            type: 'bar',
            itemStyle: {
                color: function(params) {
                    // 根据价格涨跌设置颜色
                    return params.data[1] > params.data[0] ? '#10b981' : '#ef4444';
                },
                opacity: 0.7
            },
            emphasis: {
                itemStyle: {
                    opacity: 1
                }
            }
        };
    }

    /**
     * 创建数据缩放配置
     * @param {boolean} showSlider - 是否显示滑块
     * @returns {Array} 数据缩放配置
     */
    static createDataZoom(showSlider = true) {
        const dataZoom = [
            {
                type: 'inside',
                xAxisIndex: [0, 1, 2],
                start: 70,
                end: 100,
                filterMode: 'filter'
            }
        ];

        if (showSlider) {
            dataZoom.push({
                type: 'slider',
                xAxisIndex: [0, 1, 2],
                start: 70,
                end: 100,
                height: 20,
                bottom: 10,
                borderColor: '#374151',
                fillerColor: 'rgba(59, 130, 246, 0.2)',
                handleStyle: {
                    color: '#3b82f6'
                },
                textStyle: {
                    color: '#9ca3af'
                }
            });
        }

        return dataZoom;
    }

    /**
     * 创建工具箱配置
     * @returns {Object} 工具箱配置
     */
    static createToolbox() {
        return {
            feature: {
                dataZoom: {
                    yAxisIndex: 'none',
                    title: {
                        zoom: '区域缩放',
                        back: '缩放还原'
                    }
                },
                restore: {
                    title: '还原'
                },
                saveAsImage: {
                    title: '保存图片',
                    backgroundColor: '#1f2937'
                }
            },
            iconStyle: {
                borderColor: '#9ca3af'
            },
            emphasis: {
                iconStyle: {
                    borderColor: '#3b82f6'
                }
            },
            top: 10,
            right: 80
        };
    }

    /**
     * 格式化日期
     * @param {Date|string|number} date - 日期
     * @returns {string} 格式化后的日期字符串
     */
    static formatDate(date) {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    /**
     * 格式化数值
     * @param {number} value - 数值
     * @param {number} decimals - 小数位数
     * @returns {string} 格式化后的数值字符串
     */
    static formatNumber(value, decimals = 2) {
        if (value === null || value === undefined || isNaN(value)) {
            return '--';
        }
        return Number(value).toFixed(decimals);
    }

    /**
     * 格式化百分比
     * @param {number} value - 数值
     * @param {number} decimals - 小数位数
     * @returns {string} 格式化后的百分比字符串
     */
    static formatPercent(value, decimals = 2) {
        if (value === null || value === undefined || isNaN(value)) {
            return '--';
        }
        return `${(value * 100).toFixed(decimals)}%`;
    }

    /**
     * 创建标记点配置
     * @param {Array} signals - 信号数组
     * @param {string} type - 信号类型 ('buy' | 'sell')
     * @returns {Object} 标记点配置
     */
    static createMarkPoint(signals, type) {
        const config = {
            symbol: type === 'buy' ? 'triangle' : 'pin',
            symbolSize: [15, 20],
            itemStyle: {
                color: type === 'buy' ? '#10b981' : '#ef4444'
            },
            label: {
                show: true,
                position: type === 'buy' ? 'top' : 'bottom',
                formatter: type === 'buy' ? 'B' : 'S',
                color: '#ffffff',
                fontSize: 10,
                fontWeight: 'bold'
            },
            data: signals.map(signal => ({
                coord: [signal.index, signal.price || signal.value],
                value: signal.type
            }))
        };

        return config;
    }

    /**
     * 创建标记线配置
     * @param {Array} levels - 水平线数组
     * @returns {Object} 标记线配置
     */
    static createMarkLine(levels) {
        return {
            silent: true,
            lineStyle: {
                color: '#6b7280',
                type: 'dashed',
                width: 1
            },
            label: {
                position: 'end',
                formatter: '{b}',
                color: '#9ca3af',
                fontSize: 10
            },
            data: levels.map(level => ({
                yAxis: level.value,
                name: level.name
            }))
        };
    }

    /**
     * 创建图表联动配置
     * @param {string} group - 联动组名
     * @returns {Object} 联动配置
     */
    static createBrush(group) {
        return {
            toolbox: ['rect', 'polygon', 'lineX', 'lineY', 'keep', 'clear'],
            xAxisIndex: 'all',
            brushLink: 'all',
            outOfBrush: {
                colorAlpha: 0.1
            },
            brushStyle: {
                borderWidth: 1,
                color: 'rgba(59, 130, 246, 0.2)',
                borderColor: '#3b82f6'
            }
        };
    }

    /**
     * 处理图表数据对齐
     * @param {Array} datasets - 数据集数组
     * @returns {Array} 对齐后的数据集
     */
    static alignChartData(datasets) {
        if (!datasets || datasets.length === 0) {
            return [];
        }

        // 找到最长的数据集
        const maxLength = Math.max(...datasets.map(dataset => dataset.length));
        
        // 对齐所有数据集
        return datasets.map(dataset => {
            const aligned = new Array(maxLength).fill(null);
            const offset = maxLength - dataset.length;
            
            for (let i = 0; i < dataset.length; i++) {
                aligned[offset + i] = dataset[i];
            }
            
            return aligned;
        });
    }

    /**
     * 创建响应式配置
     * @param {number} width - 容器宽度
     * @returns {Object} 响应式配置
     */
    static createResponsiveConfig(width) {
        const isMobile = width < 768;
        
        return {
            grid: {
                left: isMobile ? '5%' : '3%',
                right: isMobile ? '5%' : '4%',
                bottom: isMobile ? '15%' : '3%'
            },
            legend: {
                orient: isMobile ? 'horizontal' : 'horizontal',
                bottom: isMobile ? 5 : 'auto',
                top: isMobile ? 'auto' : 10
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    crossStyle: {
                        color: '#3b82f6'
                    }
                }
            },
            toolbox: {
                show: !isMobile
            }
        };
    }

    /**
     * 防抖函数用于图表更新
     * @param {Function} func - 更新函数
     * @param {number} delay - 延迟时间
     * @returns {Function} 防抖后的函数
     */
    static debounceChartUpdate(func, delay = 300) {
        let timeoutId;
        return function(...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    }

    /**
     * 性能监控装饰器
     * @param {Function} renderFunc - 渲染函数
     * @param {string} chartName - 图表名称
     * @returns {Function} 包装后的函数
     */
    static performanceMonitor(renderFunc, chartName) {
        return function(...args) {
            const startTime = performance.now();
            const result = renderFunc.apply(this, args);
            const endTime = performance.now();
            
            const duration = endTime - startTime;
            if (duration > 500) {
                console.warn(`${chartName} 渲染时间过长: ${duration.toFixed(2)}ms`);
            } else {
                console.log(`${chartName} 渲染完成: ${duration.toFixed(2)}ms`);
            }
            
            return result;
        };
    }
}

// 导出图表工具类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChartUtils;
} else if (typeof window !== 'undefined') {
    window.ChartUtils = ChartUtils;
}
