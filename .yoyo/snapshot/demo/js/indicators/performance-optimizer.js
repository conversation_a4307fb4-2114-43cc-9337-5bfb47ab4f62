/**
 * 性能优化器
 * 提供技术指标计算的性能优化和缓存管理
 */

class PerformanceOptimizer {
    constructor(options = {}) {
        this.cacheSize = options.cacheSize || 100;
        this.cache = new Map();
        this.computeQueue = [];
        this.isProcessing = false;
        this.workerPool = [];
        this.maxWorkers = options.maxWorkers || 2;
        
        this.metrics = {
            cacheHits: 0,
            cacheMisses: 0,
            computeTime: 0,
            totalRequests: 0
        };

        this.initWorkerPool();
    }

    /**
     * 初始化Web Worker池
     */
    initWorkerPool() {
        if (typeof Worker === 'undefined') {
            console.warn('Web Workers not supported, falling back to main thread');
            return;
        }

        // 创建计算Worker的代码
        const workerCode = `
            // 在Worker中实现技术指标计算
            self.onmessage = function(e) {
                const { type, data, id } = e.data;
                
                try {
                    let result;
                    
                    switch (type) {
                        case 'bollinger':
                            result = calculateBollingerInWorker(data);
                            break;
                        case 'kdj':
                            result = calculateKDJInWorker(data);
                            break;
                        case 'volume':
                            result = calculateVolumeInWorker(data);
                            break;
                        default:
                            throw new Error('Unknown calculation type: ' + type);
                    }
                    
                    self.postMessage({ id, result, success: true });
                } catch (error) {
                    self.postMessage({ id, error: error.message, success: false });
                }
            };

            // 布林带计算函数
            function calculateBollingerInWorker(data) {
                const { prices, window, k } = data;
                const result = { middle: [], upper: [], lower: [] };
                
                for (let i = 0; i < prices.length; i++) {
                    if (i < window - 1) {
                        result.middle.push(null);
                        result.upper.push(null);
                        result.lower.push(null);
                    } else {
                        const slice = prices.slice(i - window + 1, i + 1);
                        const mean = slice.reduce((sum, val) => sum + val, 0) / window;
                        const variance = slice.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / window;
                        const std = Math.sqrt(variance);
                        
                        result.middle.push(mean);
                        result.upper.push(mean + k * std);
                        result.lower.push(mean - k * std);
                    }
                }
                
                return result;
            }

            // KDJ计算函数
            function calculateKDJInWorker(data) {
                const { high, low, close, kPeriod, dPeriod } = data;
                const result = { K: [], D: [], J: [] };
                
                // 简化的KDJ计算实现
                for (let i = 0; i < close.length; i++) {
                    if (i < kPeriod - 1) {
                        result.K.push(null);
                        result.D.push(null);
                        result.J.push(null);
                    } else {
                        const highSlice = high.slice(i - kPeriod + 1, i + 1);
                        const lowSlice = low.slice(i - kPeriod + 1, i + 1);
                        const maxHigh = Math.max(...highSlice);
                        const minLow = Math.min(...lowSlice);
                        
                        const rsv = (close[i] - minLow) / (maxHigh - minLow) * 100;
                        const k = i === kPeriod - 1 ? rsv : (result.K[i - 1] * 2 + rsv) / 3;
                        const d = i === kPeriod - 1 ? k : (result.D[i - 1] * 2 + k) / 3;
                        const j = 3 * k - 2 * d;
                        
                        result.K.push(k);
                        result.D.push(d);
                        result.J.push(j);
                    }
                }
                
                return result;
            }

            // 成交量分析计算函数
            function calculateVolumeInWorker(data) {
                const { open, close, volume } = data;
                const result = { inVolume: [], outVolume: [], volumeDiff: [] };
                
                for (let i = 0; i < volume.length; i++) {
                    const priceChange = i > 0 ? close[i] - close[i - 1] : 0;
                    const outVol = priceChange > 0 ? volume[i] * 0.7 : volume[i] * 0.3;
                    const inVol = volume[i] - outVol;
                    
                    result.outVolume.push(outVol);
                    result.inVolume.push(inVol);
                    result.volumeDiff.push(outVol - inVol);
                }
                
                return result;
            }
        `;

        // 创建Worker
        try {
            const blob = new Blob([workerCode], { type: 'application/javascript' });
            const workerUrl = URL.createObjectURL(blob);
            
            for (let i = 0; i < this.maxWorkers; i++) {
                const worker = new Worker(workerUrl);
                worker.isAvailable = true;
                worker.onmessage = this.handleWorkerMessage.bind(this);
                worker.onerror = this.handleWorkerError.bind(this);
                this.workerPool.push(worker);
            }
            
            URL.revokeObjectURL(workerUrl);
            console.log(`Created ${this.maxWorkers} workers for indicator calculations`);
        } catch (error) {
            console.warn('Failed to create workers:', error);
        }
    }

    /**
     * 处理Worker消息
     * @param {MessageEvent} event - Worker消息事件
     */
    handleWorkerMessage(event) {
        const { id, result, error, success } = event.data;
        const worker = event.target;
        worker.isAvailable = true;

        // 查找对应的Promise resolver
        const pendingRequest = this.pendingRequests.get(id);
        if (pendingRequest) {
            this.pendingRequests.delete(id);
            
            if (success) {
                pendingRequest.resolve(result);
            } else {
                pendingRequest.reject(new Error(error));
            }
        }

        // 处理队列中的下一个任务
        this.processQueue();
    }

    /**
     * 处理Worker错误
     * @param {ErrorEvent} error - 错误事件
     */
    handleWorkerError(error) {
        console.error('Worker error:', error);
        const worker = error.target;
        worker.isAvailable = true;
    }

    /**
     * 获取缓存键
     * @param {string} type - 计算类型
     * @param {Object} params - 参数
     * @returns {string} 缓存键
     */
    getCacheKey(type, params) {
        return `${type}_${JSON.stringify(params)}`;
    }

    /**
     * 从缓存获取结果
     * @param {string} key - 缓存键
     * @returns {*} 缓存的结果
     */
    getFromCache(key) {
        if (this.cache.has(key)) {
            this.metrics.cacheHits++;
            const item = this.cache.get(key);
            
            // 更新访问时间
            item.lastAccessed = Date.now();
            
            return item.data;
        }
        
        this.metrics.cacheMisses++;
        return null;
    }

    /**
     * 存储到缓存
     * @param {string} key - 缓存键
     * @param {*} data - 数据
     */
    setCache(key, data) {
        // 如果缓存已满，删除最久未访问的项
        if (this.cache.size >= this.cacheSize) {
            let oldestKey = null;
            let oldestTime = Date.now();
            
            for (const [k, v] of this.cache.entries()) {
                if (v.lastAccessed < oldestTime) {
                    oldestTime = v.lastAccessed;
                    oldestKey = k;
                }
            }
            
            if (oldestKey) {
                this.cache.delete(oldestKey);
            }
        }
        
        this.cache.set(key, {
            data,
            createdAt: Date.now(),
            lastAccessed: Date.now()
        });
    }

    /**
     * 异步计算技术指标
     * @param {string} type - 计算类型
     * @param {Object} data - 输入数据
     * @param {Object} params - 参数
     * @returns {Promise} 计算结果
     */
    async calculateAsync(type, data, params = {}) {
        const startTime = performance.now();
        this.metrics.totalRequests++;
        
        // 检查缓存
        const cacheKey = this.getCacheKey(type, { data: data.slice(-5), params });
        const cached = this.getFromCache(cacheKey);
        
        if (cached) {
            return cached;
        }

        try {
            let result;
            
            // 如果有可用的Worker，使用Worker计算
            if (this.workerPool.length > 0) {
                result = await this.calculateWithWorker(type, data, params);
            } else {
                // 回退到主线程计算
                result = await this.calculateInMainThread(type, data, params);
            }
            
            // 缓存结果
            this.setCache(cacheKey, result);
            
            const endTime = performance.now();
            this.metrics.computeTime += endTime - startTime;
            
            return result;
            
        } catch (error) {
            console.error(`计算${type}指标失败:`, error);
            throw error;
        }
    }

    /**
     * 使用Worker计算
     * @param {string} type - 计算类型
     * @param {Object} data - 数据
     * @param {Object} params - 参数
     * @returns {Promise} 计算结果
     */
    calculateWithWorker(type, data, params) {
        return new Promise((resolve, reject) => {
            const worker = this.getAvailableWorker();
            
            if (!worker) {
                // 如果没有可用Worker，加入队列
                this.computeQueue.push({ type, data, params, resolve, reject });
                return;
            }
            
            const id = Date.now() + Math.random();
            
            if (!this.pendingRequests) {
                this.pendingRequests = new Map();
            }
            
            this.pendingRequests.set(id, { resolve, reject });
            
            worker.isAvailable = false;
            worker.postMessage({ type, data: { ...data, ...params }, id });
        });
    }

    /**
     * 获取可用的Worker
     * @returns {Worker|null} 可用的Worker
     */
    getAvailableWorker() {
        return this.workerPool.find(worker => worker.isAvailable) || null;
    }

    /**
     * 处理计算队列
     */
    processQueue() {
        if (this.computeQueue.length === 0) return;
        
        const worker = this.getAvailableWorker();
        if (!worker) return;
        
        const task = this.computeQueue.shift();
        this.calculateWithWorker(task.type, task.data, task.params)
            .then(task.resolve)
            .catch(task.reject);
    }

    /**
     * 主线程计算（回退方案）
     * @param {string} type - 计算类型
     * @param {Object} data - 数据
     * @param {Object} params - 参数
     * @returns {Promise} 计算结果
     */
    async calculateInMainThread(type, data, params) {
        // 使用requestIdleCallback在空闲时计算
        return new Promise((resolve, reject) => {
            const callback = () => {
                try {
                    let result;
                    
                    switch (type) {
                        case 'bollinger':
                            // 这里应该调用实际的布林带计算函数
                            result = this.calculateBollingerSync(data, params);
                            break;
                        case 'kdj':
                            result = this.calculateKDJSync(data, params);
                            break;
                        case 'volume':
                            result = this.calculateVolumeSync(data, params);
                            break;
                        default:
                            throw new Error(`Unknown calculation type: ${type}`);
                    }
                    
                    resolve(result);
                } catch (error) {
                    reject(error);
                }
            };
            
            if (typeof requestIdleCallback !== 'undefined') {
                requestIdleCallback(callback, { timeout: 1000 });
            } else {
                setTimeout(callback, 0);
            }
        });
    }

    /**
     * 同步布林带计算（简化版）
     */
    calculateBollingerSync(data, params) {
        // 这里应该是实际的计算逻辑
        // 为了演示，返回模拟结果
        return {
            middle: data.close.map(() => Math.random() * 100),
            upper: data.close.map(() => Math.random() * 100),
            lower: data.close.map(() => Math.random() * 100)
        };
    }

    /**
     * 同步KDJ计算（简化版）
     */
    calculateKDJSync(data, params) {
        return {
            K: data.close.map(() => Math.random() * 100),
            D: data.close.map(() => Math.random() * 100),
            J: data.close.map(() => Math.random() * 100)
        };
    }

    /**
     * 同步成交量计算（简化版）
     */
    calculateVolumeSync(data, params) {
        return {
            inVolume: data.volume.map(v => v * 0.5),
            outVolume: data.volume.map(v => v * 0.5),
            volumeDiff: data.volume.map(() => Math.random() * 1000000)
        };
    }

    /**
     * 获取性能指标
     * @returns {Object} 性能指标
     */
    getMetrics() {
        const cacheHitRate = this.metrics.totalRequests > 0 
            ? this.metrics.cacheHits / this.metrics.totalRequests 
            : 0;
            
        return {
            ...this.metrics,
            cacheHitRate: cacheHitRate,
            avgComputeTime: this.metrics.totalRequests > 0 
                ? this.metrics.computeTime / this.metrics.totalRequests 
                : 0,
            cacheSize: this.cache.size,
            queueLength: this.computeQueue.length
        };
    }

    /**
     * 清理缓存
     */
    clearCache() {
        this.cache.clear();
        this.metrics.cacheHits = 0;
        this.metrics.cacheMisses = 0;
    }

    /**
     * 销毁优化器
     */
    destroy() {
        // 终止所有Worker
        this.workerPool.forEach(worker => {
            worker.terminate();
        });
        
        this.workerPool = [];
        this.clearCache();
        this.computeQueue = [];
        
        if (this.pendingRequests) {
            this.pendingRequests.clear();
        }
    }
}

// 导出性能优化器
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceOptimizer;
} else if (typeof window !== 'undefined') {
    window.PerformanceOptimizer = PerformanceOptimizer;
}
