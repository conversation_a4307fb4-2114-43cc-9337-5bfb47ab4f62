/**
 * KDJ指标计算模块
 * 实现KDJ随机指标的计算和信号识别
 */

class KDJIndicator {
    constructor(options = {}) {
        this.defaultKPeriod = options.kPeriod || 9;
        this.defaultDPeriod = options.dPeriod || 3;
        this.defaultJPeriod = options.jPeriod || 3;
        this.cache = new Map();
    }

    /**
     * 计算KDJ指标
     * @param {Object} ohlcData - OHLC数据 {high, low, close}
     * @param {number} kPeriod - K值计算周期
     * @param {number} dPeriod - D值平滑周期
     * @param {number} jPeriod - J值计算周期
     * @returns {Object} KDJ指标结果
     */
    calculate(ohlcData, kPeriod = this.defaultKPeriod, dPeriod = this.defaultDPeriod, jPeriod = this.defaultJPeriod) {
        if (!ohlcData || !ohlcData.high || !ohlcData.low || !ohlcData.close) {
            throw new Error('OHLC数据格式错误');
        }

        const { high, low, close } = ohlcData;
        const dataLength = Math.min(high.length, low.length, close.length);

        if (dataLength < kPeriod) {
            throw new Error('数据长度不足以计算KDJ指标');
        }

        // 缓存键
        const cacheKey = `${dataLength}_${kPeriod}_${dPeriod}_${jPeriod}_${JSON.stringify(close.slice(-3))}`;
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        try {
            // 计算最高价和最低价的滚动窗口
            const { max: highMax, min: lowMin } = IndicatorUtils.calculateRollingMinMax(high, kPeriod);
            const lowMinArray = [];
            const highMaxArray = [];
            
            // 对low数组计算滚动最小值
            for (let i = 0; i < dataLength; i++) {
                if (i < kPeriod - 1) {
                    lowMinArray.push(null);
                    highMaxArray.push(null);
                } else {
                    const windowLow = low.slice(i - kPeriod + 1, i + 1);
                    const windowHigh = high.slice(i - kPeriod + 1, i + 1);
                    lowMinArray.push(Math.min(...windowLow));
                    highMaxArray.push(Math.max(...windowHigh));
                }
            }

            // 计算RSV (Raw Stochastic Value)
            const rsv = [];
            for (let i = 0; i < dataLength; i++) {
                if (lowMinArray[i] !== null && highMaxArray[i] !== null) {
                    const range = highMaxArray[i] - lowMinArray[i];
                    if (range !== 0) {
                        rsv.push((close[i] - lowMinArray[i]) / range * 100);
                    } else {
                        rsv.push(50); // 当最高价等于最低价时，设为中性值
                    }
                } else {
                    rsv.push(null);
                }
            }

            // 计算K值 (使用指数移动平均)
            const kValues = this.calculateKValue(rsv, dPeriod);

            // 计算D值 (K值的指数移动平均)
            const dValues = this.calculateDValue(kValues, jPeriod);

            // 计算J值
            const jValues = [];
            for (let i = 0; i < dataLength; i++) {
                if (kValues[i] !== null && dValues[i] !== null) {
                    jValues.push(3 * kValues[i] - 2 * dValues[i]);
                } else {
                    jValues.push(null);
                }
            }

            const result = {
                K: kValues,
                D: dValues,
                J: jValues,
                RSV: rsv,
                parameters: { kPeriod, dPeriod, jPeriod },
                metadata: {
                    dataPoints: dataLength,
                    validPoints: kValues.filter(val => val !== null).length,
                    calculatedAt: new Date().toISOString()
                }
            };

            // 缓存结果
            this.cache.set(cacheKey, result);
            
            return result;

        } catch (error) {
            console.error('KDJ指标计算错误:', error);
            throw new Error(`KDJ指标计算失败: ${error.message}`);
        }
    }

    /**
     * 计算K值（RSV的指数移动平均）
     * @param {number[]} rsv - RSV数组
     * @param {number} period - 平滑周期
     * @returns {number[]} K值数组
     */
    calculateKValue(rsv, period) {
        const alpha = 2 / (period + 1);
        const kValues = [];
        let previousK = 50; // 初始K值设为50

        for (let i = 0; i < rsv.length; i++) {
            if (rsv[i] !== null) {
                const currentK = alpha * rsv[i] + (1 - alpha) * previousK;
                kValues.push(currentK);
                previousK = currentK;
            } else {
                kValues.push(null);
            }
        }

        return kValues;
    }

    /**
     * 计算D值（K值的指数移动平均）
     * @param {number[]} kValues - K值数组
     * @param {number} period - 平滑周期
     * @returns {number[]} D值数组
     */
    calculateDValue(kValues, period) {
        const alpha = 2 / (period + 1);
        const dValues = [];
        let previousD = 50; // 初始D值设为50

        for (let i = 0; i < kValues.length; i++) {
            if (kValues[i] !== null) {
                const currentD = alpha * kValues[i] + (1 - alpha) * previousD;
                dValues.push(currentD);
                previousD = currentD;
            } else {
                dValues.push(null);
            }
        }

        return dValues;
    }

    /**
     * 识别KDJ金叉死叉信号
     * @param {Object} kdjData - KDJ数据
     * @returns {Object} 交叉信号
     */
    identifyGoldenDeathCross(kdjData) {
        if (!kdjData || !kdjData.K || !kdjData.D) {
            return { goldenCross: [], deathCross: [] };
        }

        const { K, D } = kdjData;
        const goldenCross = [];
        const deathCross = [];

        for (let i = 1; i < K.length; i++) {
            if (K[i] !== null && D[i] !== null && K[i-1] !== null && D[i-1] !== null) {
                // 金叉：K线从下方穿越D线
                if (K[i] > D[i] && K[i-1] <= D[i-1]) {
                    goldenCross.push({
                        index: i,
                        kValue: K[i],
                        dValue: D[i],
                        type: 'golden_cross',
                        strength: this.calculateCrossStrength(K, D, i)
                    });
                }

                // 死叉：K线从上方穿越D线
                if (K[i] < D[i] && K[i-1] >= D[i-1]) {
                    deathCross.push({
                        index: i,
                        kValue: K[i],
                        dValue: D[i],
                        type: 'death_cross',
                        strength: this.calculateCrossStrength(K, D, i)
                    });
                }
            }
        }

        return { goldenCross, deathCross };
    }

    /**
     * 计算交叉信号强度
     * @param {number[]} K - K值数组
     * @param {number[]} D - D值数组
     * @param {number} index - 交叉点索引
     * @returns {string} 信号强度
     */
    calculateCrossStrength(K, D, index) {
        const kValue = K[index];
        const dValue = D[index];
        const avgValue = (kValue + dValue) / 2;

        if (avgValue < 20) {
            return 'strong'; // 低位交叉，信号较强
        } else if (avgValue > 80) {
            return 'strong'; // 高位交叉，信号较强
        } else {
            return 'weak'; // 中位交叉，信号较弱
        }
    }

    /**
     * 识别超买超卖信号
     * @param {Object} kdjData - KDJ数据
     * @param {number} overboughtLevel - 超买线（默认80）
     * @param {number} oversoldLevel - 超卖线（默认20）
     * @returns {Object} 超买超卖信号
     */
    identifyOverboughtOversold(kdjData, overboughtLevel = 80, oversoldLevel = 20) {
        if (!kdjData || !kdjData.K || !kdjData.D) {
            return { overbought: [], oversold: [] };
        }

        const { K, D, J } = kdjData;
        const overbought = [];
        const oversold = [];

        for (let i = 0; i < K.length; i++) {
            if (K[i] !== null && D[i] !== null) {
                // 超买信号：K、D值都超过超买线
                if (K[i] > overboughtLevel && D[i] > overboughtLevel) {
                    overbought.push({
                        index: i,
                        kValue: K[i],
                        dValue: D[i],
                        jValue: J[i],
                        type: 'overbought'
                    });
                }

                // 超卖信号：K、D值都低于超卖线
                if (K[i] < oversoldLevel && D[i] < oversoldLevel) {
                    oversold.push({
                        index: i,
                        kValue: K[i],
                        dValue: D[i],
                        jValue: J[i],
                        type: 'oversold'
                    });
                }
            }
        }

        return { overbought, oversold };
    }

    /**
     * 识别KDJ背离信号
     * @param {number[]} prices - 价格序列
     * @param {Object} kdjData - KDJ数据
     * @param {number} lookback - 回看周期
     * @returns {Object} 背离信号
     */
    identifyDivergence(prices, kdjData, lookback = 10) {
        if (!prices || !kdjData || !kdjData.K) {
            return { bullishDivergence: [], bearishDivergence: [] };
        }

        const { K } = kdjData;
        const bullishDivergence = [];
        const bearishDivergence = [];

        for (let i = lookback; i < prices.length; i++) {
            if (K[i] !== null && K[i - lookback] !== null) {
                const priceChange = prices[i] - prices[i - lookback];
                const kChange = K[i] - K[i - lookback];

                // 看涨背离：价格创新低，但KDJ未创新低
                if (priceChange < 0 && kChange > 0) {
                    bullishDivergence.push({
                        index: i,
                        priceChange,
                        kChange,
                        type: 'bullish_divergence'
                    });
                }

                // 看跌背离：价格创新高，但KDJ未创新高
                if (priceChange > 0 && kChange < 0) {
                    bearishDivergence.push({
                        index: i,
                        priceChange,
                        kChange,
                        type: 'bearish_divergence'
                    });
                }
            }
        }

        return { bullishDivergence, bearishDivergence };
    }

    /**
     * 获取KDJ统计信息
     * @param {Object} kdjData - KDJ数据
     * @returns {Object} 统计信息
     */
    getStatistics(kdjData) {
        if (!kdjData) {
            return null;
        }

        const { K, D, J } = kdjData;
        const validK = K.filter(val => val !== null);
        const validD = D.filter(val => val !== null);
        const validJ = J.filter(val => val !== null);

        return {
            K: {
                current: validK[validK.length - 1],
                average: validK.reduce((sum, val) => sum + val, 0) / validK.length,
                min: Math.min(...validK),
                max: Math.max(...validK)
            },
            D: {
                current: validD[validD.length - 1],
                average: validD.reduce((sum, val) => sum + val, 0) / validD.length,
                min: Math.min(...validD),
                max: Math.max(...validD)
            },
            J: {
                current: validJ[validJ.length - 1],
                average: validJ.reduce((sum, val) => sum + val, 0) / validJ.length,
                min: Math.min(...validJ),
                max: Math.max(...validJ)
            }
        };
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.cache.clear();
    }
}

// 导出KDJ指标类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = KDJIndicator;
} else if (typeof window !== 'undefined') {
    window.KDJIndicator = KDJIndicator;
}
