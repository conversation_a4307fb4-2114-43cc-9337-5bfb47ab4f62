/**
 * 布林带（Bollinger Bands）指标计算模块
 * 实现价格布林带的计算和分析功能
 */

class BollingerBands {
    constructor(options = {}) {
        this.defaultWindow = options.window || 20;
        this.defaultStdDev = options.stdDev || 2.0;
        this.cache = new Map();
    }

    /**
     * 计算价格布林带
     * @param {number[]} prices - 价格序列（通常是收盘价）
     * @param {number} window - 计算窗口期（默认20）
     * @param {number} k - 标准差倍数（默认2）
     * @returns {Object} 布林带计算结果
     */
    calculate(prices, window = this.defaultWindow, k = this.defaultStdDev) {
        // 输入验证
        if (!prices || !Array.isArray(prices) || prices.length < window) {
            throw new Error('价格数据不足或格式错误');
        }

        // 缓存键
        const cacheKey = `${prices.length}_${window}_${k}_${JSON.stringify(prices.slice(-5))}`;
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        try {
            // 数据预处理
            const cleanPrices = IndicatorUtils.preprocessData(prices);
            
            // 计算移动平均线（中轨）
            const middle = IndicatorUtils.calculateSMA(cleanPrices, window);
            
            // 计算标准差
            const standardDev = IndicatorUtils.calculateStandardDeviation(cleanPrices, window);
            
            // 计算上轨和下轨
            const upper = [];
            const lower = [];
            
            for (let i = 0; i < cleanPrices.length; i++) {
                if (middle[i] !== null && standardDev[i] !== null) {
                    upper.push(middle[i] + k * standardDev[i]);
                    lower.push(middle[i] - k * standardDev[i]);
                } else {
                    upper.push(null);
                    lower.push(null);
                }
            }

            // 计算带宽（Bandwidth）
            const bandwidth = [];
            for (let i = 0; i < cleanPrices.length; i++) {
                if (upper[i] !== null && lower[i] !== null && middle[i] !== null && middle[i] !== 0) {
                    bandwidth.push((upper[i] - lower[i]) / middle[i]);
                } else {
                    bandwidth.push(null);
                }
            }

            // 计算%B指标
            const percentB = [];
            for (let i = 0; i < cleanPrices.length; i++) {
                if (upper[i] !== null && lower[i] !== null && upper[i] !== lower[i]) {
                    percentB.push((cleanPrices[i] - lower[i]) / (upper[i] - lower[i]));
                } else {
                    percentB.push(null);
                }
            }

            const result = {
                middle,
                upper,
                lower,
                bandwidth,
                percentB,
                parameters: { window, k },
                metadata: {
                    dataPoints: cleanPrices.length,
                    validPoints: middle.filter(val => val !== null).length,
                    calculatedAt: new Date().toISOString()
                }
            };

            // 缓存结果
            this.cache.set(cacheKey, result);
            
            return result;
            
        } catch (error) {
            console.error('布林带计算错误:', error);
            throw new Error(`布林带计算失败: ${error.message}`);
        }
    }

    /**
     * 计算布林带挤压状态
     * @param {Object} bollingerData - 布林带数据
     * @param {number} threshold - 挤压阈值（默认0.1）
     * @returns {boolean[]} 挤压状态数组
     */
    calculateSqueeze(bollingerData, threshold = 0.1) {
        if (!bollingerData || !bollingerData.bandwidth) {
            return [];
        }

        const { bandwidth } = bollingerData;
        const squeeze = [];
        
        // 计算带宽的移动平均作为基准
        const bandwidthMA = IndicatorUtils.calculateSMA(
            bandwidth.filter(val => val !== null), 
            20
        );
        
        let maIndex = 0;
        for (let i = 0; i < bandwidth.length; i++) {
            if (bandwidth[i] !== null) {
                const currentMA = bandwidthMA[maIndex];
                if (currentMA !== null) {
                    squeeze.push(bandwidth[i] < currentMA * threshold);
                } else {
                    squeeze.push(false);
                }
                maIndex++;
            } else {
                squeeze.push(false);
            }
        }
        
        return squeeze;
    }

    /**
     * 识别布林带突破信号
     * @param {number[]} prices - 价格序列
     * @param {Object} bollingerData - 布林带数据
     * @returns {Object} 突破信号
     */
    identifyBreakouts(prices, bollingerData) {
        if (!prices || !bollingerData) {
            return { upperBreakouts: [], lowerBreakouts: [] };
        }

        const { upper, lower } = bollingerData;
        const upperBreakouts = [];
        const lowerBreakouts = [];

        for (let i = 1; i < prices.length; i++) {
            // 上轨突破：价格从下方突破上轨
            if (upper[i] !== null && upper[i-1] !== null) {
                if (prices[i] > upper[i] && prices[i-1] <= upper[i-1]) {
                    upperBreakouts.push({
                        index: i,
                        price: prices[i],
                        upperBand: upper[i],
                        type: 'bullish_breakout'
                    });
                }
            }

            // 下轨突破：价格从上方突破下轨
            if (lower[i] !== null && lower[i-1] !== null) {
                if (prices[i] < lower[i] && prices[i-1] >= lower[i-1]) {
                    lowerBreakouts.push({
                        index: i,
                        price: prices[i],
                        lowerBand: lower[i],
                        type: 'bearish_breakout'
                    });
                }
            }
        }

        return { upperBreakouts, lowerBreakouts };
    }

    /**
     * 计算布林带回归信号
     * @param {number[]} prices - 价格序列
     * @param {Object} bollingerData - 布林带数据
     * @returns {Object} 回归信号
     */
    identifyMeanReversion(prices, bollingerData) {
        if (!prices || !bollingerData) {
            return { buySignals: [], sellSignals: [] };
        }

        const { upper, lower, middle, percentB } = bollingerData;
        const buySignals = [];
        const sellSignals = [];

        for (let i = 2; i < prices.length; i++) {
            if (percentB[i] !== null && percentB[i-1] !== null && percentB[i-2] !== null) {
                // 买入信号：%B从低位回升
                if (percentB[i-2] < 0.2 && percentB[i-1] < 0.2 && percentB[i] > 0.2) {
                    buySignals.push({
                        index: i,
                        price: prices[i],
                        percentB: percentB[i],
                        type: 'mean_reversion_buy'
                    });
                }

                // 卖出信号：%B从高位回落
                if (percentB[i-2] > 0.8 && percentB[i-1] > 0.8 && percentB[i] < 0.8) {
                    sellSignals.push({
                        index: i,
                        price: prices[i],
                        percentB: percentB[i],
                        type: 'mean_reversion_sell'
                    });
                }
            }
        }

        return { buySignals, sellSignals };
    }

    /**
     * 获取布林带统计信息
     * @param {Object} bollingerData - 布林带数据
     * @returns {Object} 统计信息
     */
    getStatistics(bollingerData) {
        if (!bollingerData) {
            return null;
        }

        const { bandwidth, percentB } = bollingerData;
        const validBandwidth = bandwidth.filter(val => val !== null);
        const validPercentB = percentB.filter(val => val !== null);

        return {
            bandwidth: {
                current: validBandwidth[validBandwidth.length - 1],
                average: validBandwidth.reduce((sum, val) => sum + val, 0) / validBandwidth.length,
                min: Math.min(...validBandwidth),
                max: Math.max(...validBandwidth)
            },
            percentB: {
                current: validPercentB[validPercentB.length - 1],
                average: validPercentB.reduce((sum, val) => sum + val, 0) / validPercentB.length,
                min: Math.min(...validPercentB),
                max: Math.max(...validPercentB)
            }
        };
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.cache.clear();
    }

    /**
     * 批量计算多个周期的布林带
     * @param {number[]} prices - 价格序列
     * @param {number[]} windows - 窗口期数组
     * @param {number} k - 标准差倍数
     * @returns {Object} 多周期布林带结果
     */
    calculateMultiplePeriods(prices, windows = [10, 20, 50], k = 2.0) {
        const results = {};
        
        windows.forEach(window => {
            try {
                results[`period_${window}`] = this.calculate(prices, window, k);
            } catch (error) {
                console.warn(`计算${window}周期布林带失败:`, error.message);
                results[`period_${window}`] = null;
            }
        });
        
        return results;
    }
}

// 导出布林带类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BollingerBands;
} else if (typeof window !== 'undefined') {
    window.BollingerBands = BollingerBands;
}
