/**
 * 成交量分析模块
 * 实现成交量内外盘差异分析和成交量布林带指标
 */

class VolumeAnalysis {
    constructor(options = {}) {
        this.defaultTriangleWindow = options.triangleWindow || 6;
        this.defaultEmaAlpha = options.emaAlpha || 0.3;
        this.cache = new Map();
    }

    /**
     * 计算成交量内外盘差异
     * @param {Object} ohlcvData - OHLCV数据 {open, high, low, close, volume}
     * @returns {Object} 内外盘差异分析结果
     */
    calculateInOutDifference(ohlcvData) {
        if (!ohlcvData || !ohlcvData.open || !ohlcvData.close || !ohlcvData.volume) {
            throw new Error('OHLCV数据格式错误');
        }

        const { open, high, low, close, volume } = ohlcvData;
        const dataLength = Math.min(open.length, high.length, low.length, close.length, volume.length);

        try {
            // 计算价格变化率
            const priceChange = [];
            for (let i = 1; i < dataLength; i++) {
                if (close[i-1] !== 0) {
                    priceChange.push((close[i] - close[i-1]) / close[i-1]);
                } else {
                    priceChange.push(0);
                }
            }
            priceChange.unshift(0); // 第一个值设为0

            // 计算成交量加权平均价格 (VWAP)
            const vwap = this.calculateVWAP(ohlcvData, 20);

            // 计算价格相对于VWAP的位置
            const priceRatio = [];
            for (let i = 0; i < dataLength; i++) {
                if (vwap[i] !== null && vwap[i] !== 0) {
                    priceRatio.push(close[i] / vwap[i]);
                } else {
                    priceRatio.push(1);
                }
            }

            // 估算外盘量（买盘）
            const outVolume = [];
            for (let i = 0; i < dataLength; i++) {
                if (priceChange[i] > 0) {
                    // 价格上涨时，成交量更多归为外盘
                    outVolume.push(volume[i] * priceRatio[i] * 0.8);
                } else if (priceChange[i] === 0) {
                    // 价格不变时，按比例分配
                    outVolume.push(volume[i] * 0.5);
                } else {
                    // 价格下跌时，较少归为外盘
                    outVolume.push(volume[i] * priceRatio[i] * 0.2);
                }
            }

            // 估算内盘量（卖盘）
            const inVolume = [];
            for (let i = 0; i < dataLength; i++) {
                inVolume.push(volume[i] - outVolume[i]);
            }

            // 计算差异
            const volumeDiff = [];
            for (let i = 0; i < dataLength; i++) {
                volumeDiff.push(outVolume[i] - inVolume[i]);
            }

            // 正值化处理：确保所有值为正数
            const minDiff = Math.min(...volumeDiff);
            const positiveDiff = volumeDiff.map(val => val - minDiff + 1);

            return {
                inVolume,
                outVolume,
                volumeDiff,
                positiveDiff,
                priceChange,
                vwap,
                metadata: {
                    dataPoints: dataLength,
                    minDiff,
                    calculatedAt: new Date().toISOString()
                }
            };

        } catch (error) {
            console.error('成交量内外盘差异计算错误:', error);
            throw new Error(`成交量分析失败: ${error.message}`);
        }
    }

    /**
     * 计算成交量加权平均价格 (VWAP)
     * @param {Object} ohlcvData - OHLCV数据
     * @param {number} window - 计算窗口
     * @returns {number[]} VWAP数组
     */
    calculateVWAP(ohlcvData, window = 20) {
        const { high, low, close, volume } = ohlcvData;
        const typicalPrice = [];
        
        // 计算典型价格
        for (let i = 0; i < high.length; i++) {
            typicalPrice.push((high[i] + low[i] + close[i]) / 3);
        }

        const vwap = [];
        for (let i = 0; i < typicalPrice.length; i++) {
            if (i < window - 1) {
                vwap.push(null);
            } else {
                let totalPV = 0;
                let totalVolume = 0;
                
                for (let j = i - window + 1; j <= i; j++) {
                    totalPV += typicalPrice[j] * volume[j];
                    totalVolume += volume[j];
                }
                
                vwap.push(totalVolume > 0 ? totalPV / totalVolume : typicalPrice[i]);
            }
        }
        
        return vwap;
    }

    /**
     * 应用平滑处理
     * @param {number[]} diffVol - 成交量差异序列
     * @param {number} triangleWindow - 三角移动平均窗口
     * @param {number} emaAlpha - EMA平滑系数
     * @returns {Object} 平滑处理结果
     */
    applySmoothing(diffVol, triangleWindow = this.defaultTriangleWindow, emaAlpha = this.defaultEmaAlpha) {
        if (!diffVol || diffVol.length === 0) {
            return { triangleMA: [], emaSmoothed: [], finalSmoothed: [] };
        }

        try {
            // 第一步：三角移动平均
            const triangleMA = IndicatorUtils.calculateTriangularMA(diffVol, triangleWindow);

            // 第二步：指数移动平均
            const emaSmoothed = IndicatorUtils.calculateEMA(triangleMA.filter(val => val !== null), Math.round(1 / emaAlpha));

            // 对齐数组长度
            const [alignedTriangle, alignedEMA] = IndicatorUtils.alignArrays(triangleMA, emaSmoothed);

            // 最终平滑结果
            const finalSmoothed = alignedEMA.map(val => val !== null ? val : 0);

            return {
                triangleMA: alignedTriangle,
                emaSmoothed: alignedEMA,
                finalSmoothed,
                parameters: { triangleWindow, emaAlpha }
            };

        } catch (error) {
            console.error('平滑处理错误:', error);
            throw new Error(`平滑处理失败: ${error.message}`);
        }
    }

    /**
     * 计算成交量布林带指数
     * @param {number[]} diffVol - 成交量差异序列
     * @param {number} window - 布林带窗口
     * @param {number} lag - 滞后期
     * @returns {Object} 成交量布林带结果
     */
    calculateVolumeBollinger(diffVol, window = 20, lag = 6) {
        if (!diffVol || diffVol.length < window) {
            throw new Error('成交量数据不足');
        }

        try {
            // 对数变换
            const logVol = IndicatorUtils.calculateLog(diffVol, 1);

            // 计算移动平均
            const ma = IndicatorUtils.calculateSMA(logVol, window);

            // 计算滞后移动平均
            const maLag = [];
            for (let i = 0; i < ma.length; i++) {
                if (i >= lag && ma[i - lag] !== null) {
                    maLag.push(ma[i - lag]);
                } else {
                    maLag.push(null);
                }
            }

            // 计算标准差
            const std = IndicatorUtils.calculateStandardDeviation(logVol, window);

            // 计算上下轨
            const upper = [];
            const lower = [];
            for (let i = 0; i < ma.length; i++) {
                if (ma[i] !== null && std[i] !== null) {
                    upper.push(ma[i] + 2 * std[i]);
                    lower.push(ma[i] - 2 * std[i]);
                } else {
                    upper.push(null);
                    lower.push(null);
                }
            }

            // 计算成交量布林带指数
            const volumeBollinger = [];
            for (let i = 0; i < logVol.length; i++) {
                if (maLag[i] !== null && std[i] !== null && std[i] !== 0) {
                    volumeBollinger.push((logVol[i] - maLag[i]) / std[i]);
                } else {
                    volumeBollinger.push(null);
                }
            }

            return {
                logVol,
                middle: ma,
                upper,
                lower,
                volumeBollinger,
                maLag,
                std,
                parameters: { window, lag }
            };

        } catch (error) {
            console.error('成交量布林带计算错误:', error);
            throw new Error(`成交量布林带计算失败: ${error.message}`);
        }
    }

    /**
     * 识别成交量突破信号
     * @param {Object} volumeBollingerData - 成交量布林带数据
     * @param {number} threshold - 突破阈值
     * @returns {Object} 突破信号
     */
    identifyVolumeBreakouts(volumeBollingerData, threshold = 2.0) {
        if (!volumeBollingerData || !volumeBollingerData.volumeBollinger) {
            return { buySignals: [], sellSignals: [] };
        }

        const { volumeBollinger } = volumeBollingerData;
        const buySignals = [];
        const sellSignals = [];

        for (let i = 1; i < volumeBollinger.length; i++) {
            if (volumeBollinger[i] !== null && volumeBollinger[i-1] !== null) {
                // 向上突破信号
                if (volumeBollinger[i] > threshold && volumeBollinger[i-1] <= threshold) {
                    buySignals.push({
                        index: i,
                        value: volumeBollinger[i],
                        type: 'volume_breakout_up'
                    });
                }

                // 向下突破信号
                if (volumeBollinger[i] < -threshold && volumeBollinger[i-1] >= -threshold) {
                    sellSignals.push({
                        index: i,
                        value: volumeBollinger[i],
                        type: 'volume_breakout_down'
                    });
                }
            }
        }

        return { buySignals, sellSignals };
    }

    /**
     * 计算成交量压力指标
     * @param {Object} ohlcvData - OHLCV数据
     * @param {number} period - 计算周期
     * @returns {Object} 成交量压力指标
     */
    calculateVolumePressure(ohlcvData, period = 20) {
        if (!ohlcvData) {
            throw new Error('OHLCV数据缺失');
        }

        const { close, volume } = ohlcvData;
        
        try {
            // 计算成交量比率
            const volumeMA = IndicatorUtils.calculateSMA(volume, period);
            const volumeRatio = [];
            for (let i = 0; i < volume.length; i++) {
                if (volumeMA[i] !== null && volumeMA[i] !== 0) {
                    volumeRatio.push(volume[i] / volumeMA[i]);
                } else {
                    volumeRatio.push(1);
                }
            }

            // 计算价格波动率
            const priceChange = IndicatorUtils.calculatePercentChange(close);
            const volatility = IndicatorUtils.calculateStandardDeviation(priceChange, period);

            // 计算压力值
            const pressure = [];
            for (let i = 0; i < close.length; i++) {
                if (volatility[i] !== null) {
                    pressure.push(volumeRatio[i] * volatility[i] * 100);
                } else {
                    pressure.push(0);
                }
            }

            // 归一化到0-1区间
            const normalizedPressure = IndicatorUtils.normalize(pressure, 0, 1);

            return {
                volumeRatio,
                volatility,
                pressure,
                normalizedPressure,
                parameters: { period }
            };

        } catch (error) {
            console.error('成交量压力指标计算错误:', error);
            throw new Error(`成交量压力指标计算失败: ${error.message}`);
        }
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.cache.clear();
    }
}

// 导出成交量分析类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = VolumeAnalysis;
} else if (typeof window !== 'undefined') {
    window.VolumeAnalysis = VolumeAnalysis;
}
