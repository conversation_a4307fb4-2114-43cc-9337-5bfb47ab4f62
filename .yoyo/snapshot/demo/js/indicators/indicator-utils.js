/**
 * 技术指标计算工具函数库
 * 提供通用的数学计算和数据处理功能
 */

class IndicatorUtils {
    /**
     * 计算简单移动平均线
     * @param {number[]} data - 数据序列
     * @param {number} window - 窗口大小
     * @returns {number[]} 移动平均线数组
     */
    static calculateSMA(data, window) {
        if (!data || data.length < window) {
            return [];
        }
        
        const result = [];
        for (let i = 0; i < data.length; i++) {
            if (i < window - 1) {
                result.push(null);
            } else {
                const sum = data.slice(i - window + 1, i + 1).reduce((a, b) => a + b, 0);
                result.push(sum / window);
            }
        }
        return result;
    }

    /**
     * 计算指数移动平均线
     * @param {number[]} data - 数据序列
     * @param {number} period - 周期
     * @returns {number[]} EMA数组
     */
    static calculateEMA(data, period) {
        if (!data || data.length === 0) return [];
        
        const alpha = 2 / (period + 1);
        const result = [data[0]];
        
        for (let i = 1; i < data.length; i++) {
            const ema = alpha * data[i] + (1 - alpha) * result[i - 1];
            result.push(ema);
        }
        
        return result;
    }

    /**
     * 计算标准差
     * @param {number[]} data - 数据序列
     * @param {number} window - 窗口大小
     * @returns {number[]} 标准差数组
     */
    static calculateStandardDeviation(data, window) {
        if (!data || data.length < window) {
            return [];
        }
        
        const result = [];
        const sma = this.calculateSMA(data, window);
        
        for (let i = 0; i < data.length; i++) {
            if (i < window - 1) {
                result.push(null);
            } else {
                const windowData = data.slice(i - window + 1, i + 1);
                const mean = sma[i];
                const variance = windowData.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / window;
                result.push(Math.sqrt(variance));
            }
        }
        
        return result;
    }

    /**
     * 计算三角移动平均
     * @param {number[]} data - 数据序列
     * @param {number} window - 窗口大小
     * @returns {number[]} 三角移动平均数组
     */
    static calculateTriangularMA(data, window) {
        if (!data || data.length < window) {
            return [];
        }
        
        // 先计算一次SMA
        const firstSMA = this.calculateSMA(data, Math.ceil(window / 2));
        // 再对结果计算SMA
        return this.calculateSMA(firstSMA.filter(val => val !== null), Math.floor(window / 2));
    }

    /**
     * 数据预处理：处理缺失值和异常值
     * @param {number[]} data - 原始数据
     * @returns {number[]} 处理后的数据
     */
    static preprocessData(data) {
        if (!data || data.length === 0) return [];
        
        const result = [...data];
        
        // 处理缺失值：使用前一个有效值填充
        for (let i = 1; i < result.length; i++) {
            if (result[i] === null || result[i] === undefined || isNaN(result[i])) {
                result[i] = result[i - 1];
            }
        }
        
        // 处理异常值：使用3σ原则
        const mean = result.reduce((sum, val) => sum + val, 0) / result.length;
        const std = Math.sqrt(result.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / result.length);
        const threshold = 3 * std;
        
        for (let i = 0; i < result.length; i++) {
            if (Math.abs(result[i] - mean) > threshold) {
                // 异常值替换为移动平均值
                const start = Math.max(0, i - 2);
                const end = Math.min(result.length, i + 3);
                const neighbors = result.slice(start, end).filter(val => Math.abs(val - mean) <= threshold);
                if (neighbors.length > 0) {
                    result[i] = neighbors.reduce((sum, val) => sum + val, 0) / neighbors.length;
                }
            }
        }
        
        return result;
    }

    /**
     * 数组对齐：确保多个数组长度一致
     * @param {...number[][]} arrays - 多个数组
     * @returns {number[][]} 对齐后的数组
     */
    static alignArrays(...arrays) {
        if (arrays.length === 0) return [];
        
        const maxLength = Math.max(...arrays.map(arr => arr.length));
        
        return arrays.map(arr => {
            const aligned = new Array(maxLength).fill(null);
            const offset = maxLength - arr.length;
            for (let i = 0; i < arr.length; i++) {
                aligned[offset + i] = arr[i];
            }
            return aligned;
        });
    }

    /**
     * 计算最大值和最小值的滚动窗口
     * @param {number[]} data - 数据序列
     * @param {number} window - 窗口大小
     * @returns {Object} {max: number[], min: number[]}
     */
    static calculateRollingMinMax(data, window) {
        if (!data || data.length < window) {
            return { max: [], min: [] };
        }
        
        const max = [];
        const min = [];
        
        for (let i = 0; i < data.length; i++) {
            if (i < window - 1) {
                max.push(null);
                min.push(null);
            } else {
                const windowData = data.slice(i - window + 1, i + 1);
                max.push(Math.max(...windowData));
                min.push(Math.min(...windowData));
            }
        }
        
        return { max, min };
    }

    /**
     * 计算百分比变化
     * @param {number[]} data - 数据序列
     * @returns {number[]} 百分比变化数组
     */
    static calculatePercentChange(data) {
        if (!data || data.length < 2) return [];
        
        const result = [0]; // 第一个值的变化率为0
        
        for (let i = 1; i < data.length; i++) {
            if (data[i - 1] !== 0) {
                result.push((data[i] - data[i - 1]) / data[i - 1]);
            } else {
                result.push(0);
            }
        }
        
        return result;
    }

    /**
     * 数据归一化到指定范围
     * @param {number[]} data - 数据序列
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @returns {number[]} 归一化后的数据
     */
    static normalize(data, min = 0, max = 1) {
        if (!data || data.length === 0) return [];
        
        const dataMin = Math.min(...data);
        const dataMax = Math.max(...data);
        const range = dataMax - dataMin;
        
        if (range === 0) return data.map(() => (min + max) / 2);
        
        return data.map(val => min + (val - dataMin) / range * (max - min));
    }

    /**
     * 计算对数变换
     * @param {number[]} data - 数据序列
     * @param {number} offset - 偏移量，避免负数和零
     * @returns {number[]} 对数变换后的数据
     */
    static calculateLog(data, offset = 1) {
        if (!data || data.length === 0) return [];
        
        return data.map(val => {
            const adjustedVal = val + offset;
            return adjustedVal > 0 ? Math.log(adjustedVal) : 0;
        });
    }

    /**
     * 性能监控装饰器
     * @param {Function} func - 要监控的函数
     * @param {string} name - 函数名称
     * @returns {Function} 包装后的函数
     */
    static performanceMonitor(func, name) {
        return function(...args) {
            const startTime = performance.now();
            const result = func.apply(this, args);
            const endTime = performance.now();
            
            if (endTime - startTime > 100) { // 超过100ms记录警告
                console.warn(`${name} 执行时间: ${(endTime - startTime).toFixed(2)}ms`);
            }
            
            return result;
        };
    }

    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} delay - 延迟时间（毫秒）
     * @returns {Function} 防抖后的函数
     */
    static debounce(func, delay) {
        let timeoutId;
        return function(...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    }
}

// 导出工具类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = IndicatorUtils;
} else if (typeof window !== 'undefined') {
    window.IndicatorUtils = IndicatorUtils;
}
