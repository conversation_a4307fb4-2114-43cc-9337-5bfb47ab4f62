/**
 * 阈值调整面板组件
 * 提供突破信号检测阈值的实时调整界面
 */

class ThresholdPanel {
    constructor(containerId, config = null, options = {}) {
        this.container = document.getElementById(containerId);
        this.config = config || window.INDICATOR_CONFIG;
        this.options = {
            onChange: options.onChange || (() => {}),
            onReset: options.onReset || (() => {}),
            debounceDelay: options.debounceDelay || 300,
            ...options
        };
        
        this.thresholds = this.config.getBreakthroughConfig().thresholds;
        this.isVisible = false;
        
        this.init();
    }

    /**
     * 初始化面板
     */
    init() {
        this.render();
        this.bindEvents();
    }

    /**
     * 渲染面板HTML
     */
    render() {
        this.container.innerHTML = `
            <div class="threshold-panel card p-6" style="display: ${this.isVisible ? 'block' : 'none'};">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-white flex items-center">
                        <i class="fas fa-sliders-h mr-2 text-blue-400"></i>
                        突破信号阈值设置
                    </h3>
                    <div class="flex space-x-2">
                        <button id="resetThresholds" class="btn-secondary text-sm px-3 py-1">
                            <i class="fas fa-undo mr-1"></i>重置
                        </button>
                        <button id="closeThresholdPanel" class="btn-secondary text-sm px-3 py-1">
                            <i class="fas fa-times mr-1"></i>关闭
                        </button>
                    </div>
                </div>

                <div class="space-y-6">
                    <!-- 价格突破阈值 -->
                    <div class="threshold-group">
                        <h4 class="text-md font-medium text-blue-400 mb-3">价格突破阈值</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="threshold-item">
                                <label class="block text-sm text-gray-400 mb-2">突破百分比</label>
                                <div class="flex items-center space-x-2">
                                    <input type="range" id="pricePercentage" min="0.005" max="0.1" step="0.005" 
                                           value="${this.thresholds.price.percentage}" 
                                           class="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <span id="pricePercentageValue" class="text-sm text-white mono-font w-12">
                                        ${(this.thresholds.price.percentage * 100).toFixed(1)}%
                                    </span>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">价格相对于20日均线的突破幅度</p>
                            </div>
                            <div class="threshold-item">
                                <label class="block text-sm text-gray-400 mb-2">最小绝对值</label>
                                <div class="flex items-center space-x-2">
                                    <input type="range" id="priceMinValue" min="0.001" max="0.1" step="0.001" 
                                           value="${this.thresholds.price.minValue}" 
                                           class="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <span id="priceMinValueValue" class="text-sm text-white mono-font w-12">
                                        ${this.thresholds.price.minValue.toFixed(3)}
                                    </span>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">最小绝对突破金额</p>
                            </div>
                        </div>
                    </div>

                    <!-- KDJ突破阈值 -->
                    <div class="threshold-group">
                        <h4 class="text-md font-medium text-purple-400 mb-3">KDJ突破阈值</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="threshold-item">
                                <label class="block text-sm text-gray-400 mb-2">突破点位</label>
                                <div class="flex items-center space-x-2">
                                    <input type="range" id="kdjPoints" min="1" max="20" step="1" 
                                           value="${this.thresholds.kdj.points}" 
                                           class="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <span id="kdjPointsValue" class="text-sm text-white mono-font w-12">
                                        ${this.thresholds.kdj.points}
                                    </span>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">KDJ值相对于20日均线的突破点位</p>
                            </div>
                            <div class="threshold-item">
                                <label class="block text-sm text-gray-400 mb-2">最小值</label>
                                <div class="flex items-center space-x-2">
                                    <input type="range" id="kdjMinValue" min="0.5" max="10" step="0.5" 
                                           value="${this.thresholds.kdj.minValue}" 
                                           class="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <span id="kdjMinValueValue" class="text-sm text-white mono-font w-12">
                                        ${this.thresholds.kdj.minValue}
                                    </span>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">最小突破点位</p>
                            </div>
                        </div>
                    </div>

                    <!-- 成交量突破阈值 -->
                    <div class="threshold-group">
                        <h4 class="text-md font-medium text-yellow-400 mb-3">成交量突破阈值</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="threshold-item">
                                <label class="block text-sm text-gray-400 mb-2">突破百分比</label>
                                <div class="flex items-center space-x-2">
                                    <input type="range" id="volumePercentage" min="0.1" max="2.0" step="0.1" 
                                           value="${this.thresholds.volume.percentage}" 
                                           class="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <span id="volumePercentageValue" class="text-sm text-white mono-font w-12">
                                        ${(this.thresholds.volume.percentage * 100).toFixed(0)}%
                                    </span>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">成交量相对于20日均量的突破幅度</p>
                            </div>
                            <div class="threshold-item">
                                <label class="block text-sm text-gray-400 mb-2">最小值</label>
                                <div class="flex items-center space-x-2">
                                    <input type="range" id="volumeMinValue" min="0.1" max="1.0" step="0.1" 
                                           value="${this.thresholds.volume.minValue}" 
                                           class="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <span id="volumeMinValueValue" class="text-sm text-white mono-font w-12">
                                        ${(this.thresholds.volume.minValue * 100).toFixed(0)}%
                                    </span>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">最小突破百分比</p>
                            </div>
                        </div>
                    </div>

                    <!-- 布林带突破阈值 -->
                    <div class="threshold-group">
                        <h4 class="text-md font-medium text-green-400 mb-3">布林带突破阈值</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="threshold-item">
                                <label class="block text-sm text-gray-400 mb-2">突破百分比</label>
                                <div class="flex items-center space-x-2">
                                    <input type="range" id="bollingerPercentage" min="0.001" max="0.05" step="0.001" 
                                           value="${this.thresholds.bollinger.percentage}" 
                                           class="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <span id="bollingerPercentageValue" class="text-sm text-white mono-font w-12">
                                        ${(this.thresholds.bollinger.percentage * 100).toFixed(1)}%
                                    </span>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">价格突破布林带轨道的幅度</p>
                            </div>
                            <div class="threshold-item">
                                <label class="block text-sm text-gray-400 mb-2">最小值</label>
                                <div class="flex items-center space-x-2">
                                    <input type="range" id="bollingerMinValue" min="0.001" max="0.02" step="0.001" 
                                           value="${this.thresholds.bollinger.minValue}" 
                                           class="flex-1 h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                    <span id="bollingerMinValueValue" class="text-sm text-white mono-font w-12">
                                        ${(this.thresholds.bollinger.minValue * 100).toFixed(1)}%
                                    </span>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">最小突破百分比</p>
                            </div>
                        </div>
                    </div>

                    <!-- 预设方案 -->
                    <div class="threshold-group">
                        <h4 class="text-md font-medium text-cyan-400 mb-3">预设方案</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                            <button class="preset-btn btn-secondary text-sm" data-preset="sensitive">
                                <i class="fas fa-search mr-2"></i>敏感型
                            </button>
                            <button class="preset-btn btn-secondary text-sm" data-preset="balanced">
                                <i class="fas fa-balance-scale mr-2"></i>平衡型
                            </button>
                            <button class="preset-btn btn-secondary text-sm" data-preset="conservative">
                                <i class="fas fa-shield-alt mr-2"></i>保守型
                            </button>
                        </div>
                        <div class="mt-3 text-xs text-gray-500">
                            <p><strong>敏感型</strong>：较低阈值，捕获更多信号但可能有噪音</p>
                            <p><strong>平衡型</strong>：中等阈值，平衡信号质量和数量</p>
                            <p><strong>保守型</strong>：较高阈值，只捕获强烈信号</p>
                        </div>
                    </div>

                    <!-- 实时预览 -->
                    <div class="threshold-group">
                        <h4 class="text-md font-medium text-orange-400 mb-3">实时预览</h4>
                        <div class="bg-gray-800 rounded-lg p-4">
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                <div class="text-center">
                                    <div class="text-gray-400">价格突破</div>
                                    <div class="text-blue-400 font-mono" id="previewPrice">--</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-gray-400">KDJ突破</div>
                                    <div class="text-purple-400 font-mono" id="previewKDJ">--</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-gray-400">成交量突破</div>
                                    <div class="text-yellow-400 font-mono" id="previewVolume">--</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-gray-400">布林带突破</div>
                                    <div class="text-green-400 font-mono" id="previewBollinger">--</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 防抖处理参数变化
        const debouncedOnChange = this.debounce(
            this.options.onChange.bind(this),
            this.options.debounceDelay
        );

        // 价格阈值
        this.bindRangeInput('pricePercentage', 'price.percentage', (value) => value / 100, debouncedOnChange);
        this.bindRangeInput('priceMinValue', 'price.minValue', null, debouncedOnChange);

        // KDJ阈值
        this.bindRangeInput('kdjPoints', 'kdj.points', (value) => parseInt(value), debouncedOnChange);
        this.bindRangeInput('kdjMinValue', 'kdj.minValue', null, debouncedOnChange);

        // 成交量阈值
        this.bindRangeInput('volumePercentage', 'volume.percentage', null, debouncedOnChange);
        this.bindRangeInput('volumeMinValue', 'volume.minValue', null, debouncedOnChange);

        // 布林带阈值
        this.bindRangeInput('bollingerPercentage', 'bollinger.percentage', null, debouncedOnChange);
        this.bindRangeInput('bollingerMinValue', 'bollinger.minValue', null, debouncedOnChange);

        // 重置按钮
        document.getElementById('resetThresholds').addEventListener('click', () => {
            this.resetToDefaults();
            this.options.onReset(this.thresholds);
        });

        // 关闭按钮
        document.getElementById('closeThresholdPanel').addEventListener('click', () => {
            this.hide();
        });

        // 预设方案
        document.querySelectorAll('.preset-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const preset = e.target.dataset.preset;
                this.applyPreset(preset);
                debouncedOnChange(this.thresholds);
            });
        });

        // 实时预览更新
        this.updatePreview();
    }

    /**
     * 绑定范围输入控件
     * @param {string} inputId - 输入控件ID
     * @param {string} paramPath - 参数路径
     * @param {Function} transform - 值转换函数
     * @param {Function} onChange - 变化回调
     */
    bindRangeInput(inputId, paramPath, transform, onChange) {
        const input = document.getElementById(inputId);
        const valueSpan = document.getElementById(inputId + 'Value');

        if (!input || !valueSpan) return;

        input.addEventListener('input', (e) => {
            let value = parseFloat(e.target.value);
            if (transform) {
                value = transform(value);
            }

            this.setParameterByPath(paramPath, value);
            this.updateValueDisplay(inputId, value);
            this.updatePreview();

            onChange(this.thresholds);
        });
    }

    /**
     * 更新值显示
     * @param {string} inputId - 输入控件ID
     * @param {number} value - 值
     */
    updateValueDisplay(inputId, value) {
        const valueSpan = document.getElementById(inputId + 'Value');
        if (!valueSpan) return;

        if (inputId.includes('Percentage')) {
            valueSpan.textContent = `${(value * 100).toFixed(1)}%`;
        } else if (inputId.includes('Points')) {
            valueSpan.textContent = value.toString();
        } else {
            valueSpan.textContent = value.toFixed(3);
        }
    }

    /**
     * 通过路径设置参数值
     * @param {string} path - 参数路径
     * @param {*} value - 参数值
     */
    setParameterByPath(path, value) {
        const keys = path.split('.');
        let obj = this.thresholds;

        for (let i = 0; i < keys.length - 1; i++) {
            obj = obj[keys[i]];
        }

        obj[keys[keys.length - 1]] = value;

        // 更新配置
        this.config.updateThresholds(this.thresholds);
    }

    /**
     * 应用预设方案
     * @param {string} preset - 预设名称
     */
    applyPreset(preset) {
        const presets = {
            sensitive: {
                price: { percentage: 0.01, minValue: 0.005 },      // 1%, 0.005元
                kdj: { points: 3, minValue: 1 },                   // 3点位, 1最小值
                volume: { percentage: 0.3, minValue: 0.15 },       // 30%, 15%最小值
                bollinger: { percentage: 0.005, minValue: 0.002 }  // 0.5%, 0.2%最小值
            },
            balanced: {
                price: { percentage: 0.02, minValue: 0.01 },       // 2%, 0.01元
                kdj: { points: 5, minValue: 2 },                   // 5点位, 2最小值
                volume: { percentage: 0.5, minValue: 0.2 },        // 50%, 20%最小值
                bollinger: { percentage: 0.01, minValue: 0.005 }   // 1%, 0.5%最小值
            },
            conservative: {
                price: { percentage: 0.03, minValue: 0.02 },       // 3%, 0.02元
                kdj: { points: 8, minValue: 3 },                   // 8点位, 3最小值
                volume: { percentage: 0.8, minValue: 0.3 },        // 80%, 30%最小值
                bollinger: { percentage: 0.02, minValue: 0.01 }    // 2%, 1%最小值
            }
        };

        if (presets[preset]) {
            Object.assign(this.thresholds, presets[preset]);
            this.updateUI();
        }
    }

    /**
     * 重置为默认值
     */
    resetToDefaults() {
        const defaultConfig = new (this.config.constructor)();
        this.thresholds = defaultConfig.getBreakthroughConfig().thresholds;
        this.config.updateThresholds(this.thresholds);
        this.updateUI();
    }

    /**
     * 更新UI显示
     */
    updateUI() {
        // 更新所有输入控件的值
        const updates = [
            ['pricePercentage', this.thresholds.price.percentage],
            ['priceMinValue', this.thresholds.price.minValue],
            ['kdjPoints', this.thresholds.kdj.points],
            ['kdjMinValue', this.thresholds.kdj.minValue],
            ['volumePercentage', this.thresholds.volume.percentage],
            ['volumeMinValue', this.thresholds.volume.minValue],
            ['bollingerPercentage', this.thresholds.bollinger.percentage],
            ['bollingerMinValue', this.thresholds.bollinger.minValue]
        ];

        updates.forEach(([id, value]) => {
            const input = document.getElementById(id);
            if (input) {
                input.value = value;
                this.updateValueDisplay(id, value);
            }
        });

        this.updatePreview();
    }

    /**
     * 更新实时预览
     */
    updatePreview() {
        const previewPrice = document.getElementById('previewPrice');
        const previewKDJ = document.getElementById('previewKDJ');
        const previewVolume = document.getElementById('previewVolume');
        const previewBollinger = document.getElementById('previewBollinger');

        if (previewPrice) {
            previewPrice.textContent = `${(this.thresholds.price.percentage * 100).toFixed(1)}%`;
        }
        if (previewKDJ) {
            previewKDJ.textContent = `${this.thresholds.kdj.points}点`;
        }
        if (previewVolume) {
            previewVolume.textContent = `${(this.thresholds.volume.percentage * 100).toFixed(0)}%`;
        }
        if (previewBollinger) {
            previewBollinger.textContent = `${(this.thresholds.bollinger.percentage * 100).toFixed(1)}%`;
        }
    }

    /**
     * 显示面板
     */
    show() {
        this.isVisible = true;
        const panel = this.container.querySelector('.threshold-panel');
        if (panel) {
            panel.style.display = 'block';
        }
    }

    /**
     * 隐藏面板
     */
    hide() {
        this.isVisible = false;
        const panel = this.container.querySelector('.threshold-panel');
        if (panel) {
            panel.style.display = 'none';
        }
    }

    /**
     * 切换面板显示状态
     */
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    /**
     * 获取当前阈值
     * @returns {Object} 当前阈值配置
     */
    getThresholds() {
        return { ...this.thresholds };
    }

    /**
     * 防抖函数
     * @param {Function} func - 函数
     * @param {number} delay - 延迟时间
     * @returns {Function} 防抖后的函数
     */
    debounce(func, delay) {
        let timeoutId;
        return function(...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(this, args), delay);
        };
    }

    /**
     * 销毁面板
     */
    destroy() {
        if (this.container) {
            this.container.innerHTML = '';
        }
        this.thresholds = null;
        this.config = null;
        this.options = null;
    }
}

// 导出阈值面板类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThresholdPanel;
} else if (typeof window !== 'undefined') {
    window.ThresholdPanel = ThresholdPanel;
}
