"""初始化数据库结构

Revision ID: d84cee4d1bbb
Revises: 
Create Date: 2025-03-19 19:49:03.067676

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd84cee4d1bbb'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('indicator_version',
    sa.Column('version_hash', sa.String(length=64), nullable=False),
    sa.Column('indicator_type', sa.String(length=20), nullable=False),
    sa.Column('formula', sa.String(length=2000), nullable=False),
    sa.Column('parameters', sa.JSON(), nullable=False),
    sa.Column('effective_date', sa.Date(), nullable=False),
    sa.Column('is_current', sa.<PERSON>(), server_default='1', nullable=False),
    sa.Column('description', sa.String(length=500), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_indicator_version')),
    sa.UniqueConstraint('version_hash', name=op.f('uq_indicator_version_version_hash'))
    )
    op.create_index('ix_indicator_version_type_current', 'indicator_version', ['indicator_type', 'is_current'], unique=False)
    op.create_table('stock_info',
    sa.Column('code', sa.String(length=10), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('exchange', sa.String(length=10), nullable=False),
    sa.Column('full_code', sa.String(length=20), nullable=False),
    sa.Column('industry', sa.String(length=50), nullable=True),
    sa.Column('sector', sa.String(length=50), nullable=True),
    sa.Column('listing_date', sa.Date(), nullable=True),
    sa.Column('total_shares', sa.BigInteger(), nullable=True),
    sa.Column('circulating_shares', sa.BigInteger(), nullable=True),
    sa.Column('company_profile', sa.String(length=2000), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('market_cap', sa.BigInteger(), nullable=True),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_stock_info'))
    )
    op.create_index(op.f('ix_stock_info_code'), 'stock_info', ['code'], unique=True)
    op.create_index(op.f('ix_stock_info_full_code'), 'stock_info', ['full_code'], unique=True)
    op.create_table('stock_indicator',
    sa.Column('stock_code', sa.String(length=10), nullable=False),
    sa.Column('trade_date', sa.Date(), nullable=False),
    sa.Column('indicator_type', sa.String(length=20), nullable=False),
    sa.Column('version_hash', sa.String(length=64), nullable=False),
    sa.Column('values', sa.JSON(), nullable=False),
    sa.Column('data_frequency', sa.String(length=2), nullable=False),
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['stock_code'], ['stock_info.code'], name=op.f('fk_stock_indicator_stock_code_stock_info')),
    sa.ForeignKeyConstraint(['version_hash'], ['indicator_version.version_hash'], name=op.f('fk_stock_indicator_version_hash_indicator_version')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_stock_indicator')),
    sa.UniqueConstraint('stock_code', 'trade_date', 'indicator_type', 'version_hash', name='uix_stock_indicator_code_date_type_version')
    )
    op.create_index('ix_stock_indicator_code_date_type', 'stock_indicator', ['stock_code', 'trade_date', 'indicator_type'], unique=False)
    op.create_index(op.f('ix_stock_indicator_stock_code'), 'stock_indicator', ['stock_code'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_stock_indicator_stock_code'), table_name='stock_indicator')
    op.drop_index('ix_stock_indicator_code_date_type', table_name='stock_indicator')
    op.drop_table('stock_indicator')
    op.drop_index(op.f('ix_stock_info_full_code'), table_name='stock_info')
    op.drop_index(op.f('ix_stock_info_code'), table_name='stock_info')
    op.drop_table('stock_info')
    op.drop_index('ix_indicator_version_type_current', table_name='indicator_version')
    op.drop_table('indicator_version')
    # ### end Alembic commands ###
