"""add_watchlist_item_table

Revision ID: 10389f6b60c5
Revises: d84cee4d1bbb
Create Date: 2025-06-16 19:09:06.286621

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '10389f6b60c5'
down_revision: Union[str, None] = 'd84cee4d1bbb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # 创建用户表
    op.create_table(
        'users',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('username', sa.String(length=50), nullable=False),
        sa.Column('password_hash', sa.String(length=128), nullable=False),
        sa.Column('email', sa.String(length=100), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('is_admin', sa.Boolean(), nullable=True),
        sa.Column('last_login', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id', name=op.f('pk_users')),
        sa.UniqueConstraint('username', name=op.f('uq_users_username')),
        sa.UniqueConstraint('email', name=op.f('uq_users_email'))
    )
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    
    # 创建自选股表
    op.create_table(
        'watchlist_items',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('stock_code', sa.String(length=10), nullable=False),
        sa.Column('added_at', sa.DateTime(), nullable=True),
        sa.Column('sort_order', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('fk_watchlist_items_user_id_users')),
        sa.PrimaryKeyConstraint('id', name=op.f('pk_watchlist_items')),
        sa.UniqueConstraint('user_id', 'stock_code', name='uix_user_stock')
    )
    op.create_index(op.f('ix_watchlist_items_id'), 'watchlist_items', ['id'], unique=False)
    op.create_index(op.f('ix_watchlist_items_user_id'), 'watchlist_items', ['user_id'], unique=False)
    op.create_index(op.f('ix_watchlist_items_stock_code'), 'watchlist_items', ['stock_code'], unique=False)
    
    # 初始化管理员用户 (密码哈希对应 'admin123', 可在实际使用前修改)
    op.execute("INSERT INTO users (created_at, updated_at, username, password_hash, email, is_active, is_admin) VALUES "
               "(CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'admin', '$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW', "
               "'<EMAIL>', True, True)")


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table('watchlist_items')
    op.drop_table('users')
