[pytest]
# 测试文件匹配模式
python_files = test_*.py
python_classes = Test* *Tests *TestCase
python_functions = test_*

# 测试标记定义
markers =
    integration: 集成测试，需要外部依赖
    slow: 执行时间较长的测试
    unit: 单元测试，不依赖外部系统

# 异步测试配置
asyncio_mode = strict

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)s] %(message)s (%(filename)s:%(lineno)s)
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 测试路径
testpaths = tests

# 忽略的目录
norecursedirs = .git .venv venv build dist *.egg-info

# 警告处理
filterwarnings =
    ignore::DeprecationWarning
    ignore::UserWarning

# 覆盖率配置
addopts = 
    --cov=app
    --cov-report=term-missing
    --cov-report=html
    --verbose
