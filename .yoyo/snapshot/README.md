# Python量化分析系统

一个高性能的股票量化分析系统，支持数据获取、存储、计算和API访问等功能。

## 功能特点

- 多数据源支持（Tushare/Akshare/Yahoo Finance）
- 技术指标计算（MACD、KDJ等）
- 多周期（日/周/月）数据分析
- REST API访问
- 自定义计算能力
- 数据定期自动更新
- 高性能计算引擎

## 项目结构

```
quantization/
├── alembic/               # 数据库迁移配置
├── app/                   # 应用主目录
│   ├── api/               # API层
│   │   ├── endpoints/     # API端点定义
│   │   └── dependencies/  # API依赖项
│   ├── core/              # 核心配置
│   │   ├── config.py      # 配置类
│   │   ├── database.py    # 数据库连接
│   │   └── scheduler.py   # 任务调度器
│   ├── models/            # 数据库模型
│   ├── schemas/           # Pydantic模型/验证
│   ├── services/          # 业务服务层
│   │   ├── data_fetcher/  # 数据获取服务
│   │   ├── indicators/    # 技术指标计算服务
│   │   └── storage/       # 数据存储服务
│   ├── utils/             # 工具函数
│   └── main.py            # 应用入口
├── tests/                 # 测试代码
├── config/                # 配置文件目录
│   └── api_providers/     # API提供者配置
├── .env                   # 环境变量
├── alembic.ini            # Alembic配置
├── pyproject.toml         # 项目依赖
└── README.md              # 项目说明
```

## 安装

1. 克隆仓库:
```bash
git clone https://github.com/yourusername/quantization.git
cd quantization
```

2. 创建并激活虚拟环境:
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows
```

3. 安装依赖:
```bash
# 使用 uv 同步依赖（推荐）
uv sync

# 或者使用传统的 pip 方式
pip install --upgrade pip
pip install -r requirements.txt --pre
```

4. 配置环境变量:
```bash
cp .env.example .env
# 编辑.env文件，设置必要的配置参数
```

## 使用方法

1. 初始化数据库:
```bash
# 执行所有待处理的迁移
alembic upgrade head

# 查看迁移历史
alembic history

# 回滚到上一个版本
alembic downgrade -1

# 回滚到初始状态
alembic downgrade base

# 生成新的迁移脚本(当模型发生变化时)
alembic revision --autogenerate -m "迁移说明"
```

2. 启动API服务:
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 使用uv启动
uv uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

#前后端一起启动
python run_dev.py
```

3. 访问API文档:
```
http://localhost:8000/docs
```

## 数据库管理

项目使用Alembic进行数据库版本控制，主要功能：

1. 数据库结构版本管理
- 支持自动生成迁移脚本
- 提供版本间迁移能力
- 支持迁移回滚操作

2. 迁移脚本位置
```
migrations/
├── versions/          # 迁移脚本目录
├── env.py            # 迁移环境配置
└── script.py.mako    # 迁移脚本模板
```

3. 常用操作
- 创建新迁移：当模型变更后，使用`alembic revision --autogenerate`生成迁移脚本
- 检查当前状态：使用`alembic current`查看当前数据库版本
- 版本管理：使用`alembic upgrade`和`alembic downgrade`在版本间切换

4. 注意事项
- 生产环境执行迁移前应先备份数据库
- 建议在测试环境验证迁移脚本
- 定期清理旧的迁移脚本

## Docker 构建与运行

本项目已内置 Dockerfile，可直接构建和运行 Docker 镜像。

### 构建镜像
```bash
docker build -t quantization:latest .
```

### 运行容器
```bash
docker run -d -p 8000:8000 --env-file .env --name quantization quantization:latest
```
- `--env-file .env` 可选，推荐用于加载环境变量。
- 访问接口文档：http://localhost:8000/docs

### 常见问题
- 如需自定义依赖或启动命令，请修改 Dockerfile。
- 若依赖私有源或特殊包，可在 Dockerfile 中添加相关 pip 配置。

## 开发

### 1. 环境准备

```bash
# 创建并激活虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装开发依赖
pip install -e ".[dev]"
pip install -r requirements-test.txt

# 安装pre-commit hooks
pre-commit install
```

### 2. 测试

项目使用pytest进行测试，包含单元测试和集成测试：

```
tests/
├── unit/                      # 单元测试
│   └── services/
│       └── data_fetcher/     # 数据获取服务单元测试
│           ├── test_adapter.py
│           ├── test_base.py
│           └── test_factory.py
├── integration/              # 集成测试
│   └── services/
│       └── data_fetcher/    # 数据获取服务集成测试
│           └── test_providers.py
└── conftest.py             # 全局测试配置
```

#### 运行测试

```bash
# 运行所有测试
pytest

# 只运行单元测试
pytest tests/unit -v

# 只运行集成测试
pytest tests/integration -v

# 运行特定模块测试
pytest tests/unit/services/data_fetcher -v

# 运行特定测试文件
pytest tests/unit/services/data_fetcher/test_adapter.py -v

# 运行特定测试类
pytest tests/unit/services/data_fetcher/test_base.py::TestDataFetcher

# 运行特定测试方法
pytest tests/unit/services/data_fetcher/test_base.py::TestDataFetcher::test_get_stock_list
```

#### 集成测试配置

运行集成测试需要配置必要的环境变量：

```bash
# Linux/Mac
export TUSHARE_TOKEN="your_token_here"

# Windows PowerShell
$env:TUSHARE_TOKEN="your_token_here"

# Windows CMD
set TUSHARE_TOKEN=your_token_here
```

#### 测试覆盖率

使用pytest-cov生成测试覆盖率报告：

```bash
# 生成HTML报告
pytest --cov=app --cov-report=html

# 生成XML报告(CI/CD集成)
pytest --cov=app --cov-report=xml

# 生成简要控制台报告
pytest --cov=app --cov-report=term-missing
```

覆盖率报告包含：
- 代码行覆盖率统计
- 未覆盖代码行标识
- 分支覆盖率分析
- 测试路径追踪

#### 测试标记说明

使用pytest标记区分不同类型的测试：

```python
@pytest.mark.unit          # 单元测试
@pytest.mark.integration  # 集成测试
@pytest.mark.slow        # 耗时测试
```

运行特定类型的测试：
```bash
# 运行单元测试
pytest -m unit

# 运行集成测试
pytest -m integration

# 排除耗时测试
pytest -m "not slow"
```
