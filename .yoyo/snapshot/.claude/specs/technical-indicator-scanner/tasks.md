# 技术指标扫描功能实现任务清单

## 任务概述

本任务清单基于需求文档和设计文档，将技术指标扫描功能的实现分解为具体的编码任务。每个任务都是可以由编程代理执行的具体编码活动，按照逻辑顺序排列，确保每个步骤都可以在前面步骤的基础上进行。

## 实现任务

### 1. 后端核心服务开发

#### 1.1. 创建扫描任务数据模型
- [ ] 在 `app/services/scan/` 目录下创建 `models.py` 文件
- [ ] 实现 `ScanTask` 数据类，包含任务状态、进度、结果等字段
- [ ] 实现 `ScanResult` 数据类，包含股票代码、信号类型、强度等字段  
- [ ] 实现 `ScanConfig` 数据类，包含扫描配置参数
- 参考需求：4.1, 4.2 (扫描任务管理)

#### 1.2. 实现技术指标计算核心模块
- [ ] 在 `app/services/indicators/` 目录下创建 `scanner_calculator.py` 文件
- [ ] 实现 `StockIndicatorCalculator` 类
- [ ] 编写 `calculate_volume_pressure_indicator()` 方法，实现成交量压力指标计算
- [ ] 编写 `calculate_volume_bollinger_middle()` 方法，计算成交量布林带中轨
- [ ] 编写 `calculate_kdj_precise()` 方法，实现精确KDJ计算
- [ ] 编写 `calculate_price_bollinger_bands()` 方法，实现价格布林带计算
- 参考需求：5.1, 5.4, 5.5 (技术指标计算)

#### 1.3. 实现交易信号识别逻辑
- [ ] 在 `StockIndicatorCalculator` 类中实现 `identify_trading_signals()` 方法
- [ ] 编写买入信号识别逻辑：成交量压力突破 AND KDJ金叉
- [ ] 编写止盈信号识别逻辑：成交量压力衰竭 OR KDJ死叉  
- [ ] 编写止损信号识别逻辑：收盘价跌破价格布林带下轨
- [ ] 添加信号强度计算逻辑
- 参考需求：5.2 (交易信号识别)

#### 1.4. 创建内存扫描任务管理服务
- [ ] 在 `app/services/scan/` 目录下创建 `memory_scanner.py` 文件
- [ ] 实现 `MemoryScannerService` 类
- [ ] 编写 `start_scan()` 方法，创建新扫描任务并启动异步扫描
- [ ] 编写 `stop_user_scan()` 方法，停止用户的扫描任务
- [ ] 编写 `get_user_task()` 方法，获取用户当前任务状态
- [ ] 编写 `cleanup_expired_tasks()` 方法，定期清理过期任务
- 参考需求：4.1, 4.3, 4.4, 4.6 (扫描任务管理)

#### 1.5. 实现会话管理和多用户隔离
- [ ] 在 `MemoryScannerService` 中实现用户会话隔离逻辑
- [ ] 编写 `_get_user_session_key()` 方法，生成用户会话唯一标识
- [ ] 实现基于用户ID和会话ID的任务隔离
- [ ] 添加并发控制锁，确保任务管理的线程安全
- 参考需求：6.1, 6.2, 6.3, 6.4 (多用户支持)

#### 1.6. 实现异步扫描执行逻辑
- [ ] 在 `MemoryScannerService` 中实现 `_run_scan()` 私有方法
- [ ] 编写获取股票池逻辑
- [ ] 实现批量股票数据获取和指标计算
- [ ] 添加扫描进度更新逻辑
- [ ] 实现扫描结果实时存储
- [ ] 添加扫描任务中途停止的支持
- 参考需求：2.2, 3.1 (进度跟踪和结果展示)

### 2. 后端API开发

#### 2.1. 创建会话管理工具
- [ ] 在 `app/utils/` 目录下创建 `session.py` 文件
- [ ] 实现 `SessionManager` 类
- [ ] 编写 `extract_session_info()` 方法，从请求中提取用户和会话信息
- [ ] 编写会话ID生成和验证逻辑
- 参考需求：6.2, 6.3 (多用户支持)

#### 2.2. 创建扫描API端点
- [ ] 在 `app/api/endpoints/` 目录下创建 `scan.py` 文件
- [ ] 实现 `POST /scan/start` 端点，启动扫描任务
- [ ] 实现 `POST /scan/stop` 端点，停止扫描任务
- [ ] 实现 `GET /scan/status` 端点，获取扫描状态和进度
- [ ] 实现 `GET /scan/results` 端点，获取扫描结果列表
- [ ] 为所有端点添加会话管理支持
- 参考需求：1.3, 1.5, 2.3 (扫描控制和进度跟踪)

#### 2.3. 创建技术指标详情API端点  
- [ ] 在现有的 `app/api/endpoints/advanced_indicators.py` 中添加新端点
- [ ] 实现 `GET /indicators/analysis/{stock_code}` 端点
- [ ] 返回单个股票的详细技术指标数据和图表数据
- [ ] 集成 `StockIndicatorCalculator` 进行实时计算
- 参考需求：3.4 (查看详情功能)

#### 2.4. 添加错误处理和验证
- [ ] 在所有API端点中添加输入参数验证
- [ ] 实现统一的错误处理机制
- [ ] 创建自定义异常类用于扫描相关错误
- [ ] 确保所有错误信息使用中文显示
- 参考需求：8.1, 8.3, 8.5 (错误处理)

#### 2.5. 集成API路由
- [ ] 在 `app/api/__init__.py` 中注册扫描API路由
- [ ] 确保API版本控制和路径正确配置
- [ ] 添加API文档和响应模型定义

### 3. 前端组件开发

#### 3.1. 创建会话管理工具
- [ ] 在 `frontend-app/src/utils/` 目录下创建 `session.js` 文件
- [ ] 实现 `SessionManager` 类
- [ ] 编写会话ID生成和本地存储逻辑
- [ ] 实现请求头构建方法，包含会话ID和用户Token
- 参考需求：6.4, 2.3 (多用户支持和状态恢复)

#### 3.2. 创建扫描API服务
- [ ] 在 `frontend-app/src/services/api/` 目录下创建 `scanApi.js` 文件
- [ ] 实现 `startScan()` 方法，调用开始扫描API
- [ ] 实现 `stopScan()` 方法，调用停止扫描API  
- [ ] 实现 `getStatus()` 方法，获取扫描状态
- [ ] 实现 `getResults()` 方法，获取扫描结果
- [ ] 集成会话管理器，自动添加会话头信息
- 参考需求：1.3, 1.5, 2.1 (扫描控制)

#### 3.3. 重构技术指标主页面
- [ ] 修改 `frontend-app/src/pages/TechnicalIndicators/index.vue`
- [ ] 移除现有的静态指标选择界面
- [ ] 实现扫描控制区，包含配置选项和控制按钮
- [ ] 添加成交量压力阈值设置（数值输入框）
- [ ] 添加KDJ金叉确认开关（复选框）  
- [ ] 添加扫描时间窗口设置
- 参考需求：1.1, 1.2 (扫描控制界面)

#### 3.4. 实现扫描控制逻辑
- [ ] 在技术指标页面中实现扫描开始逻辑
- [ ] 实现扫描停止逻辑  
- [ ] 添加按钮状态管理（禁用/启用逻辑）
- [ ] 实现扫描配置的本地存储和恢复
- 参考需求：1.3, 1.4, 1.5, 1.6 (扫描控制)

#### 3.5. 创建进度显示组件
- [ ] 在 `frontend-app/src/components/scan/` 目录下创建 `ProgressDisplay.vue`
- [ ] 实现进度条显示，显示扫描完成百分比
- [ ] 显示扫描统计信息（已扫描数/总数、发现数量）
- [ ] 实现不同扫描状态的视觉区分（运行中/完成/停止）
- 参考需求：2.1, 2.2, 2.4, 2.5 (进度跟踪)

#### 3.6. 创建扫描结果列表组件
- [ ] 在 `frontend-app/src/components/scan/` 目录下创建 `ResultList.vue`
- [ ] 实现结果项展示，包含股票信息、信号类型、强度
- [ ] 实现结果筛选功能（按信号类型筛选）
- [ ] 添加空状态提示
- [ ] 实现"查看详情"按钮，跳转到股票详情
- 参考需求：3.1, 3.2, 3.3, 3.4, 3.5 (结果展示)

#### 3.7. 实现实时数据更新
- [ ] 在技术指标页面中实现轮询机制
- [ ] 设置2秒间隔轮询扫描状态和结果
- [ ] 实现防抖机制避免频繁请求
- [ ] 添加轮询生命周期管理（开始/停止）
- [ ] 实现页面刷新后状态恢复
- 参考需求：2.3, 9.4 (状态恢复和性能)

#### 3.8. 创建扫描配置存储
- [ ] 实现扫描配置的localStorage存储
- [ ] 在页面加载时恢复用户的历史配置
- [ ] 实现配置的验证和默认值设置

### 4. 路由和导航集成

#### 4.1. 恢复技术指标路由
- [ ] 修改 `frontend-app/src/router/index.js`
- [ ] 移除技术指标路由的 `hidden: true` 配置
- [ ] 确保路由元信息正确配置
- 参考需求：7.2 (路由配置)

#### 4.2. 更新导航菜单
- [ ] 检查并更新侧边栏导航配置
- [ ] 确保技术指标菜单项可见
- [ ] 验证图标和标题显示正确
- 参考需求：7.1, 7.3 (导航集成)

#### 4.3. 创建股票详情路由
- [ ] 添加股票技术指标详情页面路由 `/technical-indicators/analysis/:stockCode`
- [ ] 确保路由支持参数传递
- 参考需求：7.4 (URL访问支持)

### 5. 错误处理和用户体验

#### 5.1. 实现前端错误处理
- [ ] 在 `frontend-app/src/utils/` 目录下创建 `errorHandler.js` 文件
- [ ] 实现统一的错误处理逻辑
- [ ] 创建用户友好的中文错误消息映射
- [ ] 集成到所有API调用中
- 参考需求：8.1, 8.2, 8.5 (错误处理)

#### 5.2. 添加加载状态指示
- [ ] 在扫描控制组件中添加Loading状态
- [ ] 在结果列表中添加加载指示器
- [ ] 实现骨架屏或加载动画
- 参考需求：8.4 (用户反馈)

#### 5.3. 实现网络错误重试
- [ ] 在API客户端中添加重试机制
- [ ] 实现网络错误的用户提示和重试选项
- 参考需求：8.2 (网络错误处理)

### 6. 性能优化和缓存

#### 6.1. 实现结果增量加载
- [ ] 修改结果列表组件支持分页或虚拟滚动
- [ ] 实现结果的增量获取API
- [ ] 添加"加载更多"功能
- 参考需求：9.2 (增量加载)

#### 6.2. 添加指标计算缓存
- [ ] 在后端指标计算服务中添加内存缓存
- [ ] 实现计算结果的TTL缓存机制
- [ ] 避免重复计算相同股票的指标
- 参考需求：9.3 (计算缓存)

#### 6.3. 优化并发扫描性能
- [ ] 实现股票数据的批量获取
- [ ] 添加并行计算支持
- [ ] 优化内存使用和垃圾回收
- 参考需求：9.1 (并发扫描)

### 7. 测试开发

#### 7.1. 编写后端单元测试
- [ ] 为 `StockIndicatorCalculator` 编写测试用例
- [ ] 为 `MemoryScannerService` 编写测试用例
- [ ] 测试技术指标计算的精度和边界条件
- [ ] 测试多用户任务隔离逻辑
- 参考需求：5.5, 6.1 (计算验证和隔离测试)

#### 7.2. 编写API集成测试
- [ ] 为扫描API端点编写集成测试
- [ ] 测试完整的扫描流程
- [ ] 测试错误场景和异常处理
- 参考需求：4.3, 8.1 (任务管理和错误处理)

#### 7.3. 编写前端组件测试
- [ ] 为扫描控制组件编写单元测试
- [ ] 为结果列表组件编写测试
- [ ] 测试用户交互和状态管理
- 参考需求：1.4, 3.5 (界面交互)

### 8. 扩展性预留

#### 8.1. 添加策略扩展接口
- [ ] 在扫描服务中预留策略接口
- [ ] 创建抽象的 `ScanStrategy` 基类
- [ ] 实现当前的 `VPKMStrategy`（成交量压力与动量双驱）
- 参考需求：9.6 (策略扩展)

#### 8.2. 添加存储扩展接口
- [ ] 创建抽象的 `TaskStorage` 接口
- [ ] 实现当前的 `MemoryTaskStorage`
- [ ] 预留 `DatabaseTaskStorage` 接口
- 参考需求：9.5 (存储扩展)

## 任务执行注意事项

1. **依赖关系**：请按照任务编号顺序执行，确保每个任务都在其依赖任务完成后进行
2. **测试驱动**：每个功能模块完成后应立即编写相应的测试用例
3. **增量集成**：每完成一个主要模块就进行集成测试，确保功能正常
4. **代码质量**：遵循现有项目的代码风格和架构模式
5. **需求对应**：每个任务都标注了对应的需求编号，确保实现符合需求