# 技术指标扫描器算法优化需求文档

## 概述

当前的股票技术指标扫描器允许用户配置复杂的参数来筛选股票，但经过量化分析师的专业评估，发现算法逻辑存在问题且参数配置过于复杂。本项目旨在将扫描器优化为基于固定专业算法的条件筛选工具，提供更准确的技术指标分析和更简洁的用户体验。

核心改进方向：
- 修正技术指标计算算法，确保符合量化分析标准
- 固化所有参数配置，使用专业量化团队验证的标准参数
- 简化用户界面，从"参数配置"改为"条件筛选"
- 增强结果展示，提供更详细的技术指标信息

## 功能需求

### 1. 技术指标算法修正

**用户故事**: 作为量化投资者，我希望扫描器使用准确的技术指标算法，以便获得可信赖的股票筛选结果。

**验收标准**:
1.1 系统应当检测KDJ金叉条件为：T-1日K值<D值 且 T日K值>D值
1.2 系统应当检测KDJ死叉条件为：T-1日K值>D值 且 T日K值<D值  
1.3 系统应当检测成交量压力突破条件为：当日成交量压力值 > 20日平均成交量压力值
1.4 系统应当检测成交量压力衰减条件为：当日成交量压力值 < 20日平均成交量压力值
1.5 系统应当检测布林带止损条件为：当日收盘价 < 布林带下轨线值
1.6 系统应当使用固定参数：KDJ(n=20, m1=3, m2=3)，布林带(window=20, std_dev=2.0)，EMA周期=10天
1.7 系统应当基于20日历史数据进行所有技术指标计算

### 2. 参数配置移除

**用户故事**: 作为普通投资者，我希望扫描器不需要复杂的参数配置，以便快速开始股票筛选而不需要专业知识。

**验收标准**:
2.1 系统应当移除所有用户可配置的技术指标参数输入框
2.2 系统应当移除参数配置对话框
2.3 系统应当移除参数配置相关的按钮和UI控件
2.4 系统应当移除快速预设功能（保守型、激进型、平衡型）
2.5 系统应当在后端硬编码所有技术指标计算参数

### 3. 条件筛选界面

**用户故事**: 作为投资者，我希望通过简单的条件选择来筛选股票，以便找到满足特定技术指标条件的投资机会。

**验收标准**:
3.1 系统应当提供KDJ指标筛选选项，描述为"KDJ金叉 (K线上穿D线)"
3.2 系统应当提供成交量压力筛选选项，描述为"成交量突破 (超过20日均线)"
3.3 系统应当提供布林带筛选选项，描述为"布林带机会 (收盘价接近下轨)"
3.4 系统应当允许用户同时选择多个筛选条件
3.5 系统应当使用AND逻辑组合多个选中的筛选条件
3.6 系统应当仅返回同时满足所有选中条件的股票

### 4. 扫描结果增强

**用户故事**: 作为投资决策者，我希望看到详细的技术指标数据和触发条件，以便做出更明智的投资决策。

**验收标准**:
4.1 系统应当在结果表格中显示KDJ当前值（K、D、J）和状态（金叉/死叉/正常）
4.2 系统应当在结果表格中显示成交量压力当前值、20日平均值和状态（突破/回落/正常）
4.3 系统应当在结果表格中显示收盘价、布林带下轨值和距离百分比
4.4 系统应当显示触发信号的具体原因（如"KDJ金叉 + 成交量突破"）
4.5 系统应当对跌破布林带的股票用红色高亮显示
4.6 系统应当保留股票代码、名称、价格、涨跌幅等基本信息显示

### 5. 后端数据结构优化

**用户故事**: 作为系统开发者，我需要扩展数据结构以支持新的显示需求，以便前端能够展示完整的技术指标信息。

**验收标准**:
5.1 系统应当在扫描结果中返回前一日KDJ的K值和D值用于判断金叉死叉
5.2 系统应当在扫描结果中返回20日平均成交量压力值
5.3 系统应当在扫描结果中返回布林带下轨线值
5.4 系统应当在扫描结果中返回当日收盘价
5.5 系统应当保持与现有StockIndicatorData结构的兼容性
5.6 系统应当确保所有新增数据字段的数值精度和类型正确性

### 6. API接口简化

**用户故事**: 作为前端开发者，我希望简化的API接口不再需要传递复杂的参数配置，以便减少代码复杂度和出错概率。

**验收标准**:
6.1 系统应当修改扫描启动接口，移除thresholds参数
6.2 系统应当仅接收indicators数组作为筛选条件参数
6.3 系统应当保持与现有扫描会话管理和进度跟踪的兼容性
6.4 系统应当确保API响应格式与前端期望的数据结构匹配
6.5 系统应当维护现有的错误处理和异常情况处理逻辑

### 7. 用户体验优化

**用户故事**: 作为投资者，我希望扫描器操作更加直观简单，以便快速找到符合条件的股票而不需要学习复杂的参数配置。

**验收标准**:
7.1 系统应当将扫描器标题和说明更新为反映"条件筛选"而非"参数配置"
7.2 系统应当提供清晰的条件选择说明文字
7.3 系统应当保持现有的扫描进度显示和结果分页功能
7.4 系统应当确保扫描性能不因算法修正而显著下降
7.5 系统应当在结果为空时提供有意义的提示信息