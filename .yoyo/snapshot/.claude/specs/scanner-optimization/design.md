# 技术指标扫描器算法优化设计文档

## 概述

本设计文档详细说明了如何将现有的参数化技术指标扫描器优化为基于固定专业算法的条件筛选工具。主要目标是修正技术指标计算逻辑、简化用户界面、并增强结果展示的专业性。

### 设计目标
- 修正KDJ、成交量压力、布林带等技术指标的计算算法
- 移除复杂的参数配置，使用量化专家验证的固定参数
- 将界面从"参数配置"模式改为"条件筛选"模式  
- 增强结果表格，显示更多技术分析相关的详细信息
- 保持现有系统架构和API兼容性

## 架构

### 系统架构概述
现有系统采用典型的三层架构：前端(Vue.js) → API层(FastAPI) → 服务层 → 数据层。优化将主要涉及服务层的算法逻辑和前端的UI组件，保持整体架构不变。

```mermaid
graph TD
    A[前端扫描器界面] --> B[API Gateway]
    B --> C[Scanner Service]
    C --> D[StockIndicatorCalculator]
    C --> E[MemoryScanner]
    D --> F[技术指标计算]
    E --> G[股票数据获取]
    F --> H[信号生成]
    G --> H
    H --> I[筛选结果]
    I --> B
    B --> A
```

### 关键组件

1. **StockIndicatorCalculator**: 技术指标计算核心，需要算法修正
2. **MemoryScanner**: 扫描执行器，需要简化参数处理
3. **Scanner Vue Component**: 前端主界面，需要UI简化
4. **ScannerResults Component**: 结果展示组件，需要增强显示
5. **StockIndicatorData Model**: 数据模型，需要扩展字段

## 组件和接口

### 后端组件修改

#### 1. StockIndicatorCalculator 算法修正

**现有问题**:
- KDJ计算使用n=9，应改为n=20
- 金叉死叉判断逻辑不准确  
- 成交量压力判断使用突破逻辑，应改为简单比较
- 参数可配置，应固化为专业标准参数

**设计改进**:
```python
class StockIndicatorCalculator:
    # 固定参数常量
    KDJ_N = 20          # KDJ周期
    KDJ_M1 = 3          # K值平滑周期  
    KDJ_M2 = 3          # D值平滑周期
    BOLLINGER_WINDOW = 20    # 布林带窗口
    BOLLINGER_STD = 2.0      # 布林带标准差倍数
    EMA_PERIOD = 10          # EMA周期
    DATA_PERIOD = 20         # 历史数据周期

    def calculate_all_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """使用固定参数计算所有技术指标"""
        # 移除所有参数，使用类常量
        
    def check_kdj_golden_cross(self, data: pd.DataFrame, current_index: int) -> bool:
        """检查KDJ金叉条件: T-1时K<D, T时K>D"""
        if current_index < 1:
            return False
        prev_k = data['price_k'].iloc[current_index - 1]
        prev_d = data['price_d'].iloc[current_index - 1] 
        curr_k = data['price_k'].iloc[current_index]
        curr_d = data['price_d'].iloc[current_index]
        return prev_k < prev_d and curr_k > curr_d

    def check_volume_pressure_breakout(self, data: pd.DataFrame, current_index: int) -> bool:
        """检查成交量压力突破: 当日值 > 20日平均"""
        curr_pressure = data['log_vol'].iloc[current_index]
        avg_pressure = data['average_vol'].iloc[current_index]
        return curr_pressure > avg_pressure

    def check_indicator_conditions(self, df: pd.DataFrame, selected_indicators: List[str]) -> bool:
        """根据选中指标检查是否满足筛选条件"""
        latest_index = len(df) - 1
        conditions_met = []
        
        if 'kdj' in selected_indicators:
            conditions_met.append(self.check_kdj_golden_cross(df, latest_index))
        if 'volume_pressure' in selected_indicators:
            conditions_met.append(self.check_volume_pressure_breakout(df, latest_index))
        if 'bollinger' in selected_indicators:
            # 布林带条件：收盘价接近下轨但未跌破（买入机会）
            conditions_met.append(self.check_bollinger_opportunity(df, latest_index))
            
        return all(conditions_met)  # AND逻辑
```

#### 2. MemoryScanner 筛选逻辑修改

**设计改进**:
```python  
def _analyze_stock(self, stock_code: str, indicators: List[str]) -> Optional[ScanResult]:
    """分析单只股票 - 移除thresholds参数"""
    df = self.calculator.calculate_all_indicators(df)
    
    # 使用新的条件检查逻辑
    if not self.calculator.check_indicator_conditions(df, indicators):
        return None  # 不满足条件，过滤掉
    
    # 构建满足条件的详细结果
    return self._build_scan_result(stock_code, df, indicators)
```

#### 3. StockIndicatorData 模型扩展

**现有模型不足**:
- 缺少前一日KDJ数据用于金叉死叉判断
- 缺少成交量20日平均值
- 缺少布林带详细信息

**设计改进**:
```python
class StockIndicatorData(BaseModel):
    # 现有字段保留
    kdj_k: float = Field(..., description="KDJ-K值")
    kdj_d: float = Field(..., description="KDJ-D值") 
    kdj_j: float = Field(..., description="KDJ-J值")
    volume_pressure: float = Field(..., description="成交量压力指标")
    bollinger_lower: float = Field(..., description="布林带下轨")
    
    # 新增字段
    prev_kdj_k: float = Field(0.0, description="前一日KDJ-K值")
    prev_kdj_d: float = Field(0.0, description="前一日KDJ-D值")
    volume_pressure_avg: float = Field(..., description="成交量压力20日平均")
    close_price: float = Field(..., description="收盘价")
    bollinger_distance_pct: float = Field(0.0, description="收盘价距离下轨百分比")
    
    # 计算属性
    def is_kdj_golden_cross(self) -> bool:
        return self.prev_kdj_k < self.prev_kdj_d and self.kdj_k > self.kdj_d
        
    def is_volume_breakout(self) -> bool:
        return self.volume_pressure > self.volume_pressure_avg
```

### 前端组件修改

#### 1. Scanner/index.vue 界面简化

**移除组件**:
- 参数配置按钮 (第45-49行)
- 参数配置对话框 (第84-194行)
- 所有参数输入控件
- 快速预设功能

**保留并优化组件**:
```vue
<!-- 指标选择区域 - 更新为条件筛选 -->
<div class="indicator-selection">
  <h3>选择筛选条件</h3>
  <p class="text-sm text-gray-600 mb-4">勾选要筛选的技术指标条件，只显示同时满足所有条件的股票</p>
  
  <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
    <label class="condition-card">
      <input type="checkbox" v-model="scanForm.indicators" value="kdj">
      <div class="condition-content">
        <Icon name="trending-up" />
        <h4>KDJ金叉</h4>
        <p>K线上穿D线买入信号</p>
      </div>
    </label>
    
    <label class="condition-card">
      <input type="checkbox" v-model="scanForm.indicators" value="volume_pressure">
      <div class="condition-content">
        <Icon name="bar-chart" />
        <h4>成交量突破</h4>
        <p>成交量压力超过20日平均</p>
      </div>
    </label>
    
    <label class="condition-card">
      <input type="checkbox" v-model="scanForm.indicators" value="bollinger">
      <div class="condition-content">
        <Icon name="target" />
        <h4>布林带机会</h4>
        <p>收盘价接近下轨买入机会</p>
      </div>
    </label>
  </div>
</div>
```

#### 2. ScannerResults.vue 结果增强

**新增表格列设计**:
```javascript
const enhancedTableColumns = [
  { prop: 'stock_code', label: '代码', minWidth: 100 },
  { prop: 'stock_name', label: '名称', minWidth: 120 },
  { prop: 'price', label: '价格', minWidth: 80 },
  { prop: 'change_percent', label: '涨跌幅', minWidth: 80 },
  { prop: 'triggered_conditions', label: '满足条件', minWidth: 150 },
  { prop: 'kdj_details', label: 'KDJ详情', minWidth: 140 },
  { prop: 'volume_details', label: '成交量详情', minWidth: 140 },
  { prop: 'bollinger_details', label: '布林带详情', minWidth: 140 }
]
```

**自定义模板设计**:
```vue
<template #triggered_conditions="{ row }">
  <div class="flex flex-wrap gap-1">
    <span v-if="row.indicator_data.is_kdj_golden_cross()" 
          class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded">
      KDJ金叉
    </span>
    <span v-if="row.indicator_data.is_volume_breakout()" 
          class="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
      量能突破
    </span>
  </div>
</template>

<template #kdj_details="{ row }">
  <div class="text-xs space-y-1">
    <div>K: {{ formatNumber(row.indicator_data.kdj_k) }}</div>
    <div>D: {{ formatNumber(row.indicator_data.kdj_d) }}</div>
    <div v-if="row.indicator_data.is_kdj_golden_cross()" 
         class="text-green-600 font-medium">
      ↗ 金叉
    </div>
  </div>
</template>
```

## 数据模型

### API请求简化
```typescript
// 旧版本 - 复杂参数
interface ScanRequest {
  indicators: string[]
  thresholds: {
    kdj_k_min: number
    kdj_k_max: number
    // ... 更多参数
  }
}

// 新版本 - 简化
interface ScanRequest {
  indicators: string[]  // 只传递筛选条件
}
```

### 响应数据增强
```typescript
interface ScanResult {
  stock_code: string
  stock_name: string
  signals: SignalType[]
  indicator_data: {
    // 现有字段
    kdj_k: number
    kdj_d: number
    kdj_j: number
    volume_pressure: number
    bollinger_lower: number
    
    // 新增字段
    prev_kdj_k: number
    prev_kdj_d: number
    volume_pressure_avg: number
    close_price: number
    bollinger_distance_pct: number
  }
  price: number
  change_percent: number
}
```

## 错误处理

### 计算错误处理
- 数据不足时的降级处理（少于20日数据时的处理策略）
- 除零错误防护（成交量为0时的处理）
- NaN值处理（技术指标计算异常时的默认值）

### 用户体验错误处理
- 未选择任何筛选条件时的提示
- 扫描结果为空时的友好提示
- 网络错误时的重试机制

## 测试策略

### 单元测试
1. **算法准确性测试**
   - KDJ金叉死叉判断逻辑测试
   - 成交量压力计算准确性测试
   - 布林带计算准确性测试
   - 边界条件测试（数据不足、异常值等）

2. **筛选逻辑测试**
   - 单一条件筛选测试
   - 多条件AND逻辑组合测试
   - 空结果场景测试

### 集成测试
1. **API接口测试**
   - 简化后的请求参数测试
   - 响应数据完整性测试
   - 错误场景处理测试

2. **端到端测试**
   - 完整扫描流程测试
   - 结果展示准确性测试
   - 性能基准测试

### 前端组件测试
1. **界面组件测试**
   - 条件选择器功能测试
   - 扫描按钮状态测试
   - 结果表格渲染测试

2. **用户交互测试**
   - 筛选条件切换测试
   - 扫描进度显示测试
   - 结果排序和分页测试

### 性能测试
- 大量股票数据扫描性能测试
- 并发扫描请求处理测试
- 内存使用优化验证

以上设计确保了系统的可维护性、可扩展性和用户体验的显著提升，同时保持了与现有系统架构的兼容性。