"""
股票数据补充脚本
用于补充股票的详细信息，如上市日期、总股本、流通股本等
"""
import asyncio
import logging
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Any, Optional
import pandas as pd

from app.core.database import get_async_session
from app.services.storage.stock_storage import StockStorageService
from app.models.stock import StockInfo

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StockDataEnricher:
    """股票数据补充工具"""
    
    def __init__(self, storage_service: StockStorageService):
        self.storage = storage_service
    
    async def enrich_with_akshare(self) -> Dict[str, Any]:
        """使用AKShare补充股票详细信息"""
        try:
            import akshare as ak
            logger.info("开始使用AKShare补充股票数据...")
            
            # 获取所有股票基本信息
            stocks_result = await self.storage.get_stock_list(limit=10000)
            stocks = stocks_result["stocks"]
            
            enriched_count = 0
            failed_count = 0
            
            for stock in stocks:
                try:
                    await self._enrich_single_stock_akshare(stock, ak)
                    enriched_count += 1
                    logger.info(f"已补充股票 {stock['code']} - {stock['name']} 的详细信息")
                    
                    # 每处理10只股票休息一下，避免频率限制
                    if enriched_count % 10 == 0:
                        await asyncio.sleep(1)
                        
                except Exception as e:
                    failed_count += 1
                    logger.warning(f"补充股票 {stock['code']} 数据失败: {str(e)}")
                    continue
            
            return {
                "total_processed": len(stocks),
                "enriched_count": enriched_count,
                "failed_count": failed_count
            }
            
        except ImportError:
            logger.error("AKShare未安装，请先安装: pip install akshare")
            raise
        except Exception as e:
            logger.error(f"使用AKShare补充数据失败: {str(e)}")
            raise
    
    async def _enrich_single_stock_akshare(self, stock: Dict[str, Any], ak) -> None:
        """使用AKShare补充单只股票的详细信息"""
        code = stock["code"]
        
        try:
            # 获取股票基本信息
            stock_info_df = ak.stock_individual_info_em(symbol=code)
            
            if stock_info_df.empty:
                logger.warning(f"未找到股票 {code} 的详细信息")
                return
            
            # 解析股票信息
            info_dict = {}
            for _, row in stock_info_df.iterrows():
                key = row['item']
                value = row['value']
                info_dict[key] = value
            
            # 构建更新数据
            update_data = {}
            
            # 上市日期
            if '上市时间' in info_dict:
                try:
                    listing_date = datetime.strptime(info_dict['上市时间'], '%Y-%m-%d').date()
                    update_data['listing_date'] = listing_date
                except (ValueError, TypeError):
                    pass
            
            # 总股本（转换为股数）
            if '总股本' in info_dict:
                try:
                    total_shares_str = info_dict['总股本']
                    total_shares = self._parse_shares(total_shares_str)
                    if total_shares:
                        update_data['total_shares'] = total_shares
                except (ValueError, TypeError):
                    pass
            
            # 流通股本
            if '流通股' in info_dict:
                try:
                    circulating_shares_str = info_dict['流通股']
                    circulating_shares = self._parse_shares(circulating_shares_str)
                    if circulating_shares:
                        update_data['circulating_shares'] = circulating_shares
                except (ValueError, TypeError):
                    pass
            
            # 行业（如果当前为空）
            if not stock.get('industry') and '所属行业' in info_dict:
                update_data['industry'] = info_dict['所属行业']
            
            # 公司简介
            if '公司简介' in info_dict:
                update_data['company_profile'] = info_dict['公司简介'][:2000]  # 限制长度
            
            # 更新数据库
            if update_data:
                await self.storage.update_stock_info(code, update_data)
                logger.debug(f"已更新股票 {code} 的字段: {list(update_data.keys())}")
                
        except Exception as e:
            logger.warning(f"处理股票 {code} 详细信息时出错: {str(e)}")
            raise
    
    def _parse_shares(self, shares_str: str) -> Optional[int]:
        """解析股本字符串，转换为股数"""
        if not shares_str or shares_str == '-':
            return None
            
        try:
            # 移除单位和非数字字符
            shares_str = shares_str.replace('万股', '').replace('亿股', '').replace('股', '')
            shares_str = shares_str.replace(',', '')
            
            # 尝试解析数字
            shares = float(shares_str)
            
            # 如果原文包含单位，进行换算
            if '亿' in shares_str:
                shares = shares * 100000000  # 1亿 = 1E8
            elif '万' in shares_str:
                shares = shares * 10000  # 1万 = 1E4
            
            return int(shares)
            
        except (ValueError, TypeError):
            return None
    
    async def enrich_with_manual_data(self, manual_data: List[Dict[str, Any]]) -> int:
        """使用手动提供的数据补充股票信息"""
        updated_count = 0
        
        for data in manual_data:
            try:
                code = data.pop('code')
                await self.storage.update_stock_info(code, data)
                updated_count += 1
                logger.info(f"已手动更新股票 {code} 的信息")
            except Exception as e:
                logger.error(f"手动更新股票数据失败: {str(e)}")
                continue
        
        return updated_count
    
    async def generate_sample_data(self) -> Dict[str, Any]:
        """为演示目的生成一些样本数据"""
        import random
        from datetime import date, timedelta
        
        # 获取前100只股票
        stocks_result = await self.storage.get_stock_list(limit=100)
        stocks = stocks_result["stocks"]
        
        updated_count = 0
        
        for stock in stocks:
            try:
                # 生成随机的示例数据
                listing_date = date.today() - timedelta(days=random.randint(365, 7300))  # 1-20年前
                total_shares = random.randint(100000000, 10000000000)  # 1亿到100亿股
                circulating_shares = int(total_shares * random.uniform(0.3, 0.9))  # 30%-90%流通
                
                update_data = {
                    'listing_date': listing_date,
                    'total_shares': total_shares,
                    'circulating_shares': circulating_shares,
                    'market_cap': int(total_shares * random.uniform(10, 100))  # 假设股价10-100元
                }
                
                await self.storage.update_stock_info(stock['code'], update_data)
                updated_count += 1
                
                if updated_count % 10 == 0:
                    logger.info(f"已生成 {updated_count} 只股票的示例数据")
                    
            except Exception as e:
                logger.error(f"生成股票 {stock['code']} 示例数据失败: {str(e)}")
                continue
        
        return {
            "updated_count": updated_count,
            "message": "样本数据生成完成（仅用于演示）"
        }

async def main():
    """主函数"""
    logger.info("开始股票数据补充...")
    
    # 获取数据库会话
    async for session in get_async_session():
        storage = StockStorageService(session)
        enricher = StockDataEnricher(storage)
        
        try:
            # 方法1: 使用AKShare补充真实数据（推荐）
            # result = await enricher.enrich_with_akshare()
            
            # 方法2: 生成示例数据（仅用于演示）
            result = await enricher.generate_sample_data()
            
            logger.info(f"数据补充完成: {result}")
            
        except Exception as e:
            logger.error(f"数据补充失败: {str(e)}")
            await session.rollback()
            raise
        else:
            await session.commit()
        finally:
            await session.close()
        
        break  # 只使用第一个会话

if __name__ == "__main__":
    asyncio.run(main())
