#!/usr/bin/env python3
"""
测试搜索API功能
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_stock_search():
    """测试股票搜索API"""
    print("=== 测试股票搜索API ===")
    
    # 测试快速搜索
    try:
        response = requests.get(f"{BASE_URL}/stocks/search/quick-search", 
                              params={"q": "600", "limit": 10})
        print(f"快速搜索状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"搜索结果数量: {len(data.get('data', []))}")
            if data.get('data'):
                print(f"第一个结果: {data['data'][0]}")
        else:
            print(f"快速搜索失败: {response.text}")
    except Exception as e:
        print(f"快速搜索出错: {e}")
    
    # 测试完整搜索
    try:
        response = requests.get(f"{BASE_URL}/stocks/search/search", 
                              params={"q": "茅台", "limit": 5})
        print(f"\n完整搜索状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"搜索结果: {json.dumps(data, ensure_ascii=False, indent=2)}")
        else:
            print(f"完整搜索失败: {response.text}")
    except Exception as e:
        print(f"完整搜索出错: {e}")
    
    # 测试热门股票
    try:
        response = requests.get(f"{BASE_URL}/stocks/search/popular", 
                              params={"limit": 5})
        print(f"\n热门股票状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"热门股票数量: {len(data.get('data', []))}")
            if data.get('data'):
                print(f"第一个热门股票: {data['data'][0]}")
        else:
            print(f"获取热门股票失败: {response.text}")
    except Exception as e:
        print(f"获取热门股票出错: {e}")

def test_watchlist_api():
    """测试自选股API"""
    print("\n=== 测试自选股API ===")
    
    # 测试获取自选股
    try:
        response = requests.get(f"{BASE_URL}/watchlist/", 
                              headers={"user-id": "1"})  # 模拟用户ID
        print(f"获取自选股状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"自选股数量: {len(data.get('data', []))}")
        else:
            print(f"获取自选股失败: {response.text}")
    except Exception as e:
        print(f"获取自选股出错: {e}")

if __name__ == "__main__":
    test_stock_search()
    test_watchlist_api()
