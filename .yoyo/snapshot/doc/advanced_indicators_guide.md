# 高级技术指标功能使用指南

## 概述

基于 `new_feature_formatted.md` 文档，我们为量化交易系统新增了高级技术指标分析功能。这些功能主要包括：

1. **多周期数据选择** - 支持日线、周线、月线、15分钟、30分钟、60分钟数据
2. **Bollinger Bands (布林带)** - 价格波动区间分析
3. **成交量内外盘分析** - 基于价格变动估算内外盘差异
4. **成交量布林带** - 基于成交量差异的布林带指标
5. **增强版KDJ指标** - 支持自定义参数的KDJ计算
6. **买卖点信号** - 基于成交量突破和KDJ金叉死叉的交易信号

## API接口说明

### 1. 完整技术指标分析

**端点:** `GET /api/v1/advanced-indicators/complete/{stock_code}`

**参数:**
- `stock_code` (路径参数): 股票代码，如 "000001"
- `start_date` (可选): 开始日期，格式 YYYY-MM-DD
- `end_date` (可选): 结束日期，格式 YYYY-MM-DD
- `period_index` (可选): 周期索引 (1-6)，默认 1
  - 1: 日线 (dh)
  - 2: 周线 (wh)
  - 3: 月线 (mh)
  - 4: 15分钟线 (15m)
  - 5: 30分钟线 (30m)
  - 6: 60分钟线 (60m)
- `bollinger_window` (可选): 布林带窗口大小 (5-100)，默认 20
- `lag` (可选): 移动平均滞后期 (1-10)，默认 2

**响应示例:**
```json
{
  "period_type": "dh",
  "data_points": 100,
  "date_range": {
    "start": "2023-01-01T00:00:00",
    "end": "2023-12-31T00:00:00"
  },
  "price_bollinger": {
    "middle": [100.0, 101.5, ...],
    "upper": [110.0, 111.5, ...],
    "lower": [90.0, 91.5, ...]
  },
  "volume_analysis": {
    "in_volume": [50000.0, 51000.0, ...],
    "ex_volume": [50000.0, 49000.0, ...],
    "volume_difference": [1000.0, 1100.0, ...]
  },
  "volume_bollinger": {
    "middle": [1000.0, 1010.0, ...],
    "upper": [1200.0, 1210.0, ...],
    "lower": [800.0, 810.0, ...],
    "smoothed_volume": [1000.0, 1005.0, ...]
  },
  "kdj_indicators": {
    "K": [50.0, 52.3, ...],
    "D": [50.0, 51.1, ...],
    "J": [50.0, 54.7, ...]
  },
  "trading_signals": {
    "buy_signals": [10, 30, 50],
    "sell_signals": [20, 40, 60]
  },
  "parameters": {
    "bollinger_window": 20,
    "lag": 2,
    "period_index": 1
  }
}
```

### 2. 单独指标接口

#### Bollinger Bands
`GET /api/v1/advanced-indicators/bollinger/{stock_code}`

#### 成交量内外盘分析
`GET /api/v1/advanced-indicators/volume-analysis/{stock_code}`

#### 成交量布林带
`GET /api/v1/advanced-indicators/volume-bollinger/{stock_code}`

#### 增强版KDJ
`GET /api/v1/advanced-indicators/kdj-enhanced/{stock_code}`

#### 交易信号
`GET /api/v1/advanced-indicators/trading-signals/{stock_code}`

#### 获取支持的周期
`GET /api/v1/advanced-indicators/periods`

## 前端使用方法

### 1. JavaScript API调用

```javascript
import { advancedIndicatorService } from '@/api';

// 获取完整技术指标
const result = await advancedIndicatorService.getCompleteIndicators('000001', {
  start_date: '2023-01-01',
  end_date: '2023-12-31',
  period_index: 1,
  bollinger_window: 20,
  lag: 2
});

// 获取单独的Bollinger Bands
const bollinger = await advancedIndicatorService.getBollingerBands('000001', {
  window: 20,
  std_dev: 2.0
});

// 获取交易信号
const signals = await advancedIndicatorService.getTradingSignals('000001');
```

### 2. 演示页面

访问 `demo-frontend/advanced-indicators.html` 查看完整的演示页面，包括：

- 交互式参数配置
- 实时图表展示
- 交易信号列表
- 响应式设计

## 业务逻辑说明

### 1. 数据周期选择

系统支持6种不同的数据周期，用户可以根据交易策略选择合适的时间框架：

- **日线数据**: 适合中长期投资分析
- **分钟级数据**: 适合日内交易和短期策略
- **周线/月线**: 适合长期趋势分析

### 2. Bollinger Bands 计算

布林带由三条线组成：
- **中轨**: N日移动平均线
- **上轨**: 中轨 + K倍标准差
- **下轨**: 中轨 - K倍标准差

用于判断价格是否偏离正常波动范围。

### 3. 成交量内外盘分析

基于以下逻辑估算内外盘：
- 当收盘价接近最高价时，认为外盘比例较高
- 当收盘价接近最低价时，认为内盘比例较高
- 外盘成交量 = 总成交量 × (收盘价-最低价)/(最高价-最低价)
- 内盘成交量 = 总成交量 - 外盘成交量

### 4. 买卖点信号算法

买入信号条件：
- 成交量从下方突破成交量均线 **OR**
- KDJ指标中K线从下方突破D线（金叉）

卖出信号条件：
- 成交量从上方跌破成交量均线 **OR**
- KDJ指标中K线从上方跌破D线（死叉）

## 性能优化

### 1. 缓存策略

- 完整指标计算结果缓存30分钟
- 使用Redis进行分布式缓存
- 支持按参数组合生成缓存键

### 2. 异步处理

- 所有数据获取和计算操作均为异步
- 支持并发请求处理
- 避免阻塞主线程

### 3. 数据压缩

- 大量数值数据使用高效的序列化格式
- 前端按需加载数据
- 支持分页和数据截取

## 错误处理

### 常见错误码

- `400`: 参数验证失败
- `404`: 股票代码不存在
- `422`: 请求参数格式错误
- `500`: 服务器内部错误

### 错误响应格式

```json
{
  "detail": "错误描述信息",
  "error_code": "VALIDATION_ERROR",
  "timestamp": "2023-12-01T10:00:00Z"
}
```

## 扩展开发

### 1. 添加新的技术指标

1. 在 `AdvancedIndicatorService` 中添加计算方法
2. 在 `advanced_indicators.py` 中添加API端点
3. 更新前端API服务
4. 添加相应的测试用例

### 2. 自定义信号算法

可以通过继承 `AdvancedIndicatorService` 类并重写 `calculate_buy_sell_signals` 方法来实现自定义的信号算法。

### 3. 数据源扩展

系统支持多种数据源，可以通过配置不同的数据提供者来获取不同来源的数据。

## 测试

### 运行单元测试

```bash
# 测试高级指标服务
pytest tests/unit/services/indicators/test_advanced_indicator_service.py -v

# 测试API端点
pytest tests/unit/api/endpoints/test_advanced_indicators.py -v

# 运行所有相关测试
pytest tests/ -k "advanced_indicator" -v
```

### 集成测试

```bash
# 启动开发服务器
uvicorn app.main:app --reload

# 访问API文档
http://localhost:8000/api/docs

# 测试演示页面
open demo-frontend/advanced-indicators.html
```

## 部署说明

### 1. 环境要求

- Python 3.8+
- FastAPI
- Pandas 1.5+
- NumPy
- Pandas-TA (技术指标库)

### 2. 配置项

在 `app/core/config.py` 中添加相关配置：

```python
# 高级指标配置
ADVANCED_INDICATORS_CACHE_TTL: int = 1800  # 缓存时间（秒）
ADVANCED_INDICATORS_MAX_PERIODS: int = 1000  # 最大数据点数
ADVANCED_INDICATORS_DEFAULT_WINDOW: int = 20  # 默认窗口大小
```

### 3. 监控和日志

- 所有计算操作都有详细的日志记录
- 支持性能监控和异常告警
- 提供计算耗时统计

## 常见问题

### Q1: 为什么某些股票的交易信号很少？

A: 交易信号的产生依赖于市场波动和成交量变化。对于交易不活跃的股票，可能信号较少。可以调整参数或使用更短的时间周期。

### Q2: 如何优化计算性能？

A: 可以通过以下方式优化：
- 减少数据时间范围
- 使用较大的窗口参数
- 启用缓存机制
- 选择合适的数据周期

### Q3: 成交量内外盘分析的准确性如何？

A: 这是基于价格变动的估算方法，与实际的内外盘数据可能有差异。主要用于趋势分析，不能作为精确的交易依据。

## 版本历史

- **v1.0.0** (2024-01-01): 初始版本，包含基础功能
- **v1.1.0** (待定): 计划添加更多技术指标和信号算法

## 联系支持

如有问题或建议，请联系开发团队或提交Issue。
