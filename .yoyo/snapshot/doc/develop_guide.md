# Python量化项目开发规划

## 1. 用户需求

- 获取并存储股票市场数据
- 执行技术分析指标计算（MACD、KDJ等）
- 支持多周期（日/周/月）数据分析
- 通过REST API访问计算结果
- 定制化计算能力

## 2. 研发需求

- 搭建可扩展的数据获取与存储框架
- 实现数据定期自动更新（可配置执行频率）
- 构建数据库无缝切换能力（SQLite到MySQL）
- 开发高性能计算引擎处理大量股票数据
- 设计REST API接口
- 考虑使用缓存机制提升性能
- 采用类Spring Boot的配置方式

## 3. 技术选型

### 3.1 核心技术栈
- **Python 3.8+**：主要开发语言
- **FastAPI**：REST API框架，性能优于Flask
- **SQLAlchemy**：ORM框架，支持多数据库切换
- **Alembic**：数据库迁移工具
- **APScheduler**：任务调度，支持cron表达式
- **pandas**：数据分析和技术指标计算
- **pandas-ta**：技术分析库
- **tushare/akshare/yfinance**：股票数据API
- **Redis**：可选缓存层
- **Pydantic**：数据验证和配置管理
- **pytest**：单元测试框架

### 3.2 数据存储
- **SQLite**：开发阶段和小规模部署
- **MySQL**：生产环境和大规模部署
- **Redis**：热数据缓存，提升查询性能

## 4. 开发任务

### 4.1 基础架构
- [ ] 项目结构搭建
- [ ] 配置管理系统实现
- [ ] 数据库连接与ORM模型定义
- [ ] 日志系统实现

### 4.2 数据获取与存储
- [ ] 设计股票数据模型
- [ ] 实现数据API连接器
- [ ] 开发数据获取服务
- [ ] 实现数据持久化层
- [ ] 数据更新调度服务

### 4.3 技术分析引擎
- [ ] 基础指标计算模块（MACD、KDJ等）
- [ ] 多周期转换与计算框架
- [ ] 自定义指标扩展机制
- [ ] 指标计算结果缓存

### 4.4 REST API服务
- [ ] API路由设计
- [ ] 接口参数验证
- [ ] 响应数据格式化
- [ ] API文档生成

### 4.5 测试与部署
- [ ] 单元测试
- [ ] 集成测试
- [ ] Docker容器化
- [ ] CI/CD配置

## 5. 项目架构

### 5.1 整体架构

```
quantization/
├── alembic/               # 数据库迁移配置
├── app/                   # 应用主目录
│   ├── api/               # API层
│   │   ├── endpoints/     # API端点定义
│   │   └── dependencies/  # API依赖项
│   ├── core/              # 核心配置
│   │   ├── config.py      # 配置类
│   │   ├── database.py    # 数据库连接
│   │   └── scheduler.py   # 任务调度器
│   ├── models/            # 数据库模型
│   ├── schemas/           # Pydantic模型/验证
│   ├── services/          # 业务服务层
│   │   ├── data_fetcher/  # 数据获取服务
│   │   ├── indicators/    # 技术指标计算服务
│   │   └── storage/       # 数据存储服务
│   ├── utils/             # 工具函数
│   └── main.py            # 应用入口
├── tests/                 # 测试代码
├── .env                   # 环境变量
├── .env.example           # 环境变量示例
├── alembic.ini            # Alembic配置
├── pyproject.toml         # 项目依赖
└── README.md              # 项目说明
```

### 5.2 数据流程

1. **数据获取流程**：
   - 定时任务触发数据获取服务
   - 数据获取服务从外部API拉取股票数据
   - 通过ORM将数据持久化到数据库
   - 更新数据获取状态

2. **指标计算流程**：
   - API请求触发指标计算
   - 从数据库读取原始股票数据
   - 应用技术指标算法
   - 缓存计算结果（可选）
   - 返回计算结果

3. **多周期处理**：
   - 基于日K数据进行周期转换
   - 对转换后的数据应用指标计算
   - 将结果存储或直接返回

### 5.3 模块职责

- **配置模块**：管理应用配置，支持环境变量覆盖默认配置
- **数据获取模块**：负责从外部API获取股票数据
- **数据存储模块**：处理数据持久化和查询
- **调度模块**：管理定时任务，使用cron表达式设置执行时间
- **指标计算模块**：实现各类技术指标算法
- **API模块**：处理HTTP请求，返回计算结果

## 6. 关键代码实现

### 6.1 配置管理（类Spring Boot风格）

```python
# app/core/config.py
from pydantic import BaseSettings, Field
from typing import Optional
import os

class Settings(BaseSettings):
    # 数据库配置
    DATABASE_URL: str = Field("sqlite:///./quantization.db", 
                             description="Database connection string")
    DATABASE_TYPE: str = Field("sqlite", description="Database type: sqlite or mysql")
    
    # Redis配置
    REDIS_URL: Optional[str] = Field(None, description="Redis connection string")
    USE_REDIS_CACHE: bool = Field(False, description="Whether to use Redis for caching")
    
    # 数据获取配置
    STOCK_API_TYPE: str = Field("tushare", description="Stock API provider")
    STOCK_API_TOKEN: Optional[str] = Field(None, description="API token for stock data")
    STOCK_API_CUSTOM_PATH: Optional[str] = Field(None, 
                                         description="Custom API provider module path")
    STOCK_API_CONFIG_PATH: str = Field("config/api_providers", 
                                    description="API provider configuration directory")
    
    # 调度配置
    DATA_UPDATE_CRON: str = Field("0 18 * * 1-5", 
                                  description="Cron expression for data update schedule")
    
    # API配置
    API_PREFIX: str = Field("/api/v1", description="API route prefix")
    
    class Config:
        env_file = ".env"
        case_sensitive = True

settings = Settings()
```

### 6.2 数据库连接与ORM模型

```python
# app/core/database.py
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.core.config import settings

engine = create_engine(settings.DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
```

```python
# app/models/stock.py
from sqlalchemy import Column, Integer, String, Float, Date, DateTime
from sqlalchemy.sql import func
from app.core.database import Base

class StockInfo(Base):
    __tablename__ = "stock_info"
    
    id = Column(Integer, primary_key=True, index=True)
    stock_code = Column(String, index=True)
    stock_name = Column(String)
    industry = Column(String, nullable=True)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
class StockDaily(Base):
    __tablename__ = "stock_daily"
    
    id = Column(Integer, primary_key=True, index=True)
    stock_code = Column(String, index=True)
    date = Column(Date, index=True)
    open = Column(Float)
    high = Column(Float)
    low = Column(Float)
    close = Column(Float)
    volume = Column(Float)
    amount = Column(Float, nullable=True)
    
    # 复合索引
    __table_args__ = (
        # 股票代码和日期的复合唯一索引
        {'sqlite_autoincrement': True},
    )
```

### 6.3 数据获取服务

```python
# app/services/data_fetcher/base_fetcher.py
from abc import ABC, abstractmethod
from datetime import date
from typing import List, Dict, Any

class BaseFetcher(ABC):
    """股票数据获取基类"""
    
    @abstractmethod
    async def fetch_stock_list(self) -> List[Dict[str, Any]]:
        """获取股票列表信息"""
        pass
    
    @abstractmethod
    async def fetch_daily_data(self, stock_code: str, 
                               start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """获取日线数据"""
        pass

# app/services/data_fetcher/tushare_fetcher.py
import tushare as ts
from datetime import date
from typing import List, Dict, Any
from app.services.data_fetcher.base_fetcher import BaseFetcher
from app.core.config import settings

class TushareFetcher(BaseFetcher):
    def __init__(self):
        self.api = ts.pro_api(settings.STOCK_API_TOKEN)
    
    async def fetch_stock_list(self) -> List[Dict[str, Any]]:
        data = self.api.stock_basic(exchange='', list_status='L')
        return data.to_dict('records')
    
    async def fetch_daily_data(self, stock_code: str, 
                             start_date: date, end_date: date) -> List[Dict[str, Any]]:
        data = self.api.daily(ts_code=stock_code, 
                              start_date=start_date.strftime('%Y%m%d'), 
                              end_date=end_date.strftime('%Y%m%d'))
        return data.to_dict('records')
```

### 6.4 调度服务

```python
# app/core/scheduler.py
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from app.core.config import settings
from app.services.data_fetcher.factory import create_fetcher
from app.services.storage.stock_service import StockService
from datetime import datetime, timedelta
from app.core.database import SessionLocal
from app.core import logging

logger = logging.getLogger(__name__)

class SchedulerService:
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        
    async def setup_jobs(self):
        # 配置股票数据更新任务
        self.scheduler.add_job(
            self.update_stock_data,
            CronTrigger.from_crontab(settings.DATA_UPDATE_CRON),
            id="update_stock_data",
            replace_existing=True
        )
        
        logger.info(f"Scheduled job update_stock_data with cron: {settings.DATA_UPDATE_CRON}")
    
    async def update_stock_data(self):
        logger.info("Starting stock data update job")
        fetcher = create_fetcher()
        db = SessionLocal()
        
        try:
            stock_service = StockService(db)
            
            # 更新股票列表
            stocks = await fetcher.fetch_stock_list()
            await stock_service.update_stock_list(stocks)
            
            # 更新股票日线数据
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=7)  # 获取最近一周数据
            
            for stock in await stock_service.get_all_stocks():
                try:
                    daily_data = await fetcher.fetch_daily_data(
                        stock.stock_code, start_date, end_date
                    )
                    await stock_service.update_daily_data(daily_data)
                except Exception as e:
                    logger.error(f"Error updating data for {stock.stock_code}: {e}")
                    
        except Exception as e:
            logger.error(f"Stock data update job failed: {e}")
        finally:
            db.close()
            
    def start(self):
        self.scheduler.start()
        
    def shutdown(self):
        self.scheduler.shutdown()
```

### 6.5 技术指标计算服务

```python
# app/services/indicators/indicator_service.py
import pandas as pd
import pandas_ta as ta
from typing import List, Dict, Any, Optional
from app.services.storage.stock_service import StockService

class IndicatorService:
    def __init__(self, stock_service: StockService):
        self.stock_service = stock_service
        
    async def calculate_macd(self, stock_code: str, 
                            start_date: str, end_date: str, 
                            fast_period: int = 12, 
                            slow_period: int = 26, 
                            signal_period: int = 9,
                            freq: str = 'D') -> Dict[str, Any]:
        """计算MACD指标"""
        # 获取数据
        df = await self._get_data_by_freq(stock_code, start_date, end_date, freq)
        if df.empty:
            return {"error": "No data available"}
            
        # 计算MACD
        macd = ta.macd(df['close'], fast=fast_period, slow=slow_period, signal=signal_period)
        
        # 合并结果
        result = pd.concat([df, macd], axis=1)
        
        return {
            "stock_code": stock_code,
            "indicator": "macd",
            "params": {
                "fast_period": fast_period,
                "slow_period": slow_period,
                "signal_period": signal_period,
                "freq": freq
            },
            "data": result.reset_index().to_dict('records')
        }
    
    async def calculate_kdj(self, stock_code: str, 
                          start_date: str, end_date: str, 
                          window: int = 9, 
                          freq: str = 'D') -> Dict[str, Any]:
        """计算KDJ指标"""
        # 获取数据
        df = await self._get_data_by_freq(stock_code, start_date, end_date, freq)
        if df.empty:
            return {"error": "No data available"}
            
        # 计算KDJ
        kdj = ta.kdj(df['high'], df['low'], df['close'], window=window)
        
        # 合并结果
        result = pd.concat([df, kdj], axis=1)
        
        return {
            "stock_code": stock_code,
            "indicator": "kdj",
            "params": {
                "window": window,
                "freq": freq
            },
            "data": result.reset_index().to_dict('records')
        }
        
    async def _get_data_by_freq(self, stock_code: str, 
                              start_date: str, end_date: str, 
                              freq: str = 'D') -> pd.DataFrame:
        """按频率获取数据，支持日/周/月转换"""
        # 获取原始日线数据
        daily_data = await self.stock_service.get_daily_data(
            stock_code, start_date, end_date
        )
        
        if not daily_data:
            return pd.DataFrame()
            
        # 转换为pandas DataFrame
        df = pd.DataFrame(daily_data)
        df['date'] = pd.to_datetime(df['date'])
        df.set_index('date', inplace=True)
        
        # 按照不同周期转换
        if freq == 'W':
            # 周线转换
            return self._convert_to_weekly(df)
        elif freq == 'M':
            # 月线转换
            return self._convert_to_monthly(df)
        else:
            # 日线直接返回
            return df
            
    def _convert_to_weekly(self, df: pd.DataFrame) -> pd.DataFrame:
        """转换为周线数据"""
        weekly = df.resample('W').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum',
            'amount': 'sum'
        })
        return weekly.dropna()
        
    def _convert_to_monthly(self, df: pd.DataFrame) -> pd.DataFrame:
        """转换为月线数据"""
        monthly = df.resample('M').agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum',
            'amount': 'sum'
        })
        return monthly.dropna()
```

### 6.6 REST API实现

```python
# app/api/endpoints/indicators.py
from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from datetime import date, timedelta

from app.core.database import get_db
from app.services.storage.stock_service import StockService
from app.services.indicators.indicator_service import IndicatorService
from app.core.cache import cache_result, get_cached_result

router = APIRouter()

@router.get("/macd/{stock_code}")
async def get_macd(
    stock_code: str,
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    fast_period: int = Query(12),
    slow_period: int = Query(26),
    signal_period: int = Query(9),
    freq: str = Query("D", regex="^[DMW]$"),
    db: Session = Depends(get_db)
):
    """计算并返回MACD指标"""
    # 设置默认日期
    if not end_date:
        end_date = date.today().isoformat()
    if not start_date:
        start_date = (date.fromisoformat(end_date) - timedelta(days=100)).isoformat()
    
    # 尝试从缓存获取
    cache_key = f"macd:{stock_code}:{start_date}:{end_date}:{fast_period}:{slow_period}:{signal_period}:{freq}"
    cached_data = await get_cached_result(cache_key)
    if cached_data:
        return cached_data
    
    # 计算指标
    stock_service = StockService(db)
    indicator_service = IndicatorService(stock_service)
    
    result = await indicator_service.calculate_macd(
        stock_code, start_date, end_date, 
        fast_period, slow_period, signal_period, freq
    )
    
    # 缓存结果
    await cache_result(cache_key, result, expire=3600)  # 缓存1小时
    
    return result

@router.get("/kdj/{stock_code}")
async def get_kdj(
    stock_code: str,
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    window: int = Query(9),
    freq: str = Query("D", regex="^[DMW]$"),
    db: Session = Depends(get_db)
):
    """计算并返回KDJ指标"""
    # 设置默认日期
    if not end_date:
        end_date = date.today().isoformat()
    if not start_date:
        start_date = (date.fromisoformat(end_date) - timedelta(days=100)).isoformat()
    
    # 尝试从缓存获取
    cache_key = f"kdj:{stock_code}:{start_date}:{end_date}:{window}:{freq}"
    cached_data = await get_cached_result(cache_key)
    if cached_data:
        return cached_data
    
    # 计算指标
    stock_service = StockService(db)
    indicator_service = IndicatorService(stock_service)
    
    result = await indicator_service.calculate_kdj(
        stock_code, start_date, end_date, window, freq
    )
    
    # 缓存结果
    await cache_result(cache_key, result, expire=3600)  # 缓存1小时
    
    return result
```

### 6.7 应用入口

```python
# app/main.py
import uvicorn
from fastapi import FastAPI
from app.core import logging
from app.core.config import settings
from app.api.router import api_router
from app.core.scheduler import SchedulerService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger(__name__)

app = FastAPI(title="Stock Quantization API")

# 添加API路由
app.include_router(api_router, prefix=settings.API_PREFIX)

# 创建调度器
scheduler = SchedulerService()

@app.on_event("startup")
async def startup_event():
    logger.info("Starting application")
    # 初始化调度器
    await scheduler.setup_jobs()
    scheduler.start()
    
@app.on_event("shutdown")
async def shutdown_event():
    logger.info("Shutting down application")
    # 关闭调度器
    scheduler.shutdown()

if __name__ == "__main__":
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)
```

## 7. 部署指南

### 7.1 环境准备

1. 安装Python 3.8+
2. 安装项目依赖：`pip install -r requirements.txt`
3. 配置环境变量或.env文件

### 7.2 数据库迁移

```bash
# 初始化数据库
alembic upgrade head
```

### 7.3 启动应用

```bash
# 开发模式
uvicorn app.main:app --reload

# 生产模式
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

### 7.4 Docker部署

```bash
# 构建镜像
docker build -t stock-quantization .

# 运行容器
docker run -d -p 8000:8000 --name stock-quant stock-quantization
```

## 8. 未来扩展

- 实现Web前端展示
- 增加回测功能
- 接入多种数据源
- 添加机器学习模型预测
- 建立实时交易接口
- 实现分布式计算处理大规模数据

## 9. 灵活自定义的股票数据API

为了满足不同数据源和特定计算需求，项目需要实现一个灵活可自定义的股票数据API机制。

### 9.1 自定义API设计原则

- **接口统一性**：所有数据提供者实现统一的接口
- **可插拔性**：支持动态添加和切换数据源
- **配置驱动**：通过配置文件定义API参数和行为
- **扩展性**：支持自定义数据处理和转换
- **错误处理**：统一的错误处理和重试机制

### 9.2 API提供者注册机制

```python
# app/services/data_fetcher/provider_registry.py
from typing import Dict, Type, Optional
from app.services.data_fetcher.base_fetcher import BaseFetcher
import importlib
from app.core import logging

logger = logging.getLogger(__name__)

class ProviderRegistry:
    """股票数据提供者注册中心"""
    
    _providers: Dict[str, Type[BaseFetcher]] = {}
    
    @classmethod
    def register(cls, name: str, provider_class: Type[BaseFetcher]):
        """注册数据提供者"""
        cls._providers[name] = provider_class
        logger.info(f"Registered data provider: {name}")
    
    @classmethod
    def get_provider(cls, name: str) -> Optional[Type[BaseFetcher]]:
        """获取数据提供者类"""
        if name not in cls._providers:
            logger.warning(f"Provider {name} not found")
            return None
        return cls._providers[name]
    
    @classmethod
    def list_providers(cls) -> Dict[str, Type[BaseFetcher]]:
        """列出所有注册的提供者"""
        return cls._providers.copy()
    
    @classmethod
    def load_provider_from_path(cls, name: str, module_path: str):
        """从模块路径动态加载提供者"""
        try:
            module = importlib.import_module(module_path)
            if hasattr(module, name):
                provider_class = getattr(module, name)
                cls.register(name.lower(), provider_class)
                return True
            else:
                logger.error(f"Provider class {name} not found in module {module_path}")
                return False
        except ImportError as e:
            logger.error(f"Failed to import module {module_path}: {e}")
            return False
```

### 9.3 自定义API配置系统

```python
# app/core/api_config.py
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
import yaml
import os
from app.core import logging

logger = logging.getLogger(__name__)

class ApiEndpoint(BaseModel):
    """API端点配置"""
    name: str
    path: str
    method: str = "GET"
    params: Dict[str, Any] = {}
    headers: Dict[str, str] = {}
    body_template: Optional[Dict[str, Any]] = None
    response_mapping: Dict[str, str] = {}
    error_path: Optional[str] = None
    retry_count: int = 3
    timeout: int = 30

class ApiProviderConfig(BaseModel):
    """API提供者配置"""
    name: str
    base_url: str
    auth_type: str = "none"  # none, token, apikey, oauth
    auth_params: Dict[str, Any] = {}
    rate_limit: Optional[int] = None  # 每分钟请求次数限制
    endpoints: Dict[str, ApiEndpoint] = {}

class ApiConfigManager:
    """API配置管理器"""
    
    def __init__(self, config_path: str = "config/api_providers"):
        self.config_path = config_path
        self.providers: Dict[str, ApiProviderConfig] = {}
        self._load_configs()
    
    def _load_configs(self):
        """加载所有API配置文件"""
        if not os.path.exists(self.config_path):
            logger.warning(f"API config path {self.config_path} not exists")
            return
            
        for filename in os.listdir(self.config_path):
            if filename.endswith('.yaml') or filename.endswith('.yml'):
                provider_name = filename.split('.')[0]
                file_path = os.path.join(self.config_path, filename)
                try:
                    with open(file_path, 'r') as f:
                        config_data = yaml.safe_load(f)
                        provider_config = ApiProviderConfig(**config_data)
                        self.providers[provider_name] = provider_config
                        logger.info(f"Loaded API provider config: {provider_name}")
                except Exception as e:
                    logger.error(f"Failed to load API config {file_path}: {e}")
    
    def get_provider_config(self, provider_name: str) -> Optional[ApiProviderConfig]:
        """获取指定提供者的配置"""
        return self.providers.get(provider_name)
    
    def list_providers(self) -> List[str]:
        """列出所有可用的API提供者"""
        return list(self.providers.keys())
    
    def reload_config(self, provider_name: str) -> bool:
        """重新加载特定提供者的配置"""
        file_path = os.path.join(self.config_path, f"{provider_name}.yaml")
        if not os.path.exists(file_path):
            logger.warning(f"API config file {file_path} not exists")
            return False
            
        try:
            with open(file_path, 'r') as f:
                config_data = yaml.safe_load(f)
                provider_config = ApiProviderConfig(**config_data)
                self.providers[provider_name] = provider_config
                logger.info(f"Reloaded API provider config: {provider_name}")
                return True
        except Exception as e:
            logger.error(f"Failed to reload API config {file_path}: {e}")
            return False
```

### 9.4 通用API调用适配器

```python
# app/services/data_fetcher/api_adapter.py
import aiohttp
import json
from app.core import logging
from typing import Dict, Any, Optional, Union
from datetime import date, datetime
from app.core.api_config import ApiProviderConfig, ApiEndpoint
from app.services.data_fetcher.base_fetcher import BaseFetcher

logger = logging.getLogger(__name__)

class ApiAdapter(BaseFetcher):
    """通用API调用适配器"""
    
    def __init__(self, config: ApiProviderConfig):
        self.config = config
        self.session = None
        self.headers = {}
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(base_url=self.config.base_url)
        
        # 设置授权头
        if self.config.auth_type == "token":
            self.headers["Authorization"] = f"Bearer {self.config.auth_params.get('token', '')}"
        elif self.config.auth_type == "apikey":
            key_name = self.config.auth_params.get("key_name", "apikey")
            key_value = self.config.auth_params.get("key_value", "")
            self.headers[key_name] = key_value
        
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
            self.session = None
    
    async def _call_endpoint(self, endpoint_name: str, **kwargs) -> Dict[str, Any]:
        """调用指定的API端点"""
        if not self.session:
            raise RuntimeError("API session not initialized, use 'async with' context")
            
        if endpoint_name not in self.config.endpoints:
            raise ValueError(f"Endpoint {endpoint_name} not defined in API config")
            
        endpoint = self.config.endpoints[endpoint_name]
        
        # 准备请求参数
        url = endpoint.path
        params = {**endpoint.params}
        headers = {**self.headers, **endpoint.headers}
        
        # 替换URL中的参数
        for k, v in kwargs.items():
            if f"{{{k}}}" in url:
                url = url.replace(f"{{{k}}}", str(v))
            else:
                params[k] = v
        
        # 处理日期类型
        for k, v in params.items():
            if isinstance(v, (date, datetime)):
                params[k] = v.strftime('%Y%m%d')
        
        # 准备请求体
        body = None
        if endpoint.body_template:
            body = json.dumps({
                k: kwargs.get(v, None) if v in kwargs else v 
                for k, v in endpoint.body_template.items()
            })
        
        # 发送请求
        method = endpoint.method
        for retry in range(endpoint.retry_count):
            try:
                async with self.session.request(
                    method=method,
                    url=url,
                    params=params,
                    headers=headers,
                    data=body,
                    timeout=endpoint.timeout
                ) as response:
                    response_data = await response.json()
                    
                    # 检查错误
                    if endpoint.error_path:
                        error_value = self._get_nested_value(response_data, endpoint.error_path)
                        if error_value:
                            logger.warning(f"API error: {error_value}")
                            if retry < endpoint.retry_count - 1:
                                logger.info(f"Retrying {retry+1}/{endpoint.retry_count}...")
                                continue
                            else:
                                raise ValueError(f"API error after {endpoint.retry_count} retries: {error_value}")
                    
                    # 映射响应字段
                    if endpoint.response_mapping:
                        return self._map_response(response_data, endpoint.response_mapping)
                    
                    return response_data
            except Exception as e:
                logger.error(f"API request failed: {e}")
                if retry < endpoint.retry_count - 1:
                    logger.info(f"Retrying {retry+1}/{endpoint.retry_count}...")
                else:
                    raise
        
        raise RuntimeError(f"Failed to call endpoint {endpoint_name} after {endpoint.retry_count} retries")
    
    def _get_nested_value(self, data: Dict[str, Any], path: str) -> Any:
        """从嵌套字典中获取值"""
        parts = path.split('.')
        for part in parts:
            if isinstance(data, dict) and part in data:
                data = data[part]
            else:
                return None
        return data
    
    def _map_response(self, data: Dict[str, Any], mapping: Dict[str, str]) -> Dict[str, Any]:
        """映射响应字段"""
        result = {}
        for target_key, source_path in mapping.items():
            result[target_key] = self._get_nested_value(data, source_path)
        return result
    
    async def fetch_stock_list(self) -> list:
        """获取股票列表"""
        return await self._call_endpoint('stock_list')
    
    async def fetch_daily_data(self, stock_code: str, 
                             start_date: date, end_date: date) -> list:
        """获取日线数据"""
        return await self._call_endpoint('daily_data', 
                                    stock_code=stock_code,
                                    start_date=start_date,
                                    end_date=end_date)
```

### 9.5 配置文件示例

```yaml
# config/api_providers/tushare.yaml
name: tushare
base_url: http://api.tushare.pro
auth_type: apikey
auth_params:
  key_name: token
  key_value: YOUR_TUSHARE_TOKEN
rate_limit: 500
endpoints:
  stock_list:
    name: 获取股票列表
    path: /stock_basic
    method: POST
    body_template:
      api_name: stock_basic
      token: "{token}"
      params:
        exchange: ""
        list_status: "L"
    response_mapping:
      data: data.items
      fields: data.fields
    error_path: code
    
  daily_data:
    name: 获取日线数据
    path: /daily
    method: POST
    body_template:
      api_name: daily
      token: "{token}"
      params:
        ts_code: "{stock_code}"
        start_date: "{start_date}"
        end_date: "{end_date}"
    response_mapping:
      data: data.items
      fields: data.fields
    error_path: code
```

```yaml
# config/api_providers/yahoo_finance.yaml
name: yahoo_finance
base_url: https://query1.finance.yahoo.com
auth_type: none
endpoints:
  stock_list:
    name: 获取股票列表
    path: /v7/finance/quote
    params:
      symbols: "{symbols}"
    response_mapping:
      data: quoteResponse.result
    error_path: quoteResponse.error
    
  daily_data:
    name: 获取日线数据
    path: /v8/finance/chart/{stock_code}
    params:
      period1: "{start_timestamp}"
      period2: "{end_timestamp}"
      interval: "1d"
      events: "history"
    response_mapping:
      timestamp: chart.result.0.timestamp
      open: chart.result.0.indicators.quote.0.open
      high: chart.result.0.indicators.quote.0.high
      low: chart.result.0.indicators.quote.0.low
      close: chart.result.0.indicators.quote.0.close
      volume: chart.result.0.indicators.quote.0.volume
    error_path: chart.error
```

### 9.6 API提供者工厂

```python
# app/services/data_fetcher/factory.py
from app.services.data_fetcher.base_fetcher import BaseFetcher
from app.services.data_fetcher.provider_registry import ProviderRegistry
from app.services.data_fetcher.api_adapter import ApiAdapter
from app.core.api_config import ApiConfigManager
from app.core.config import settings
from app.core import logging

logger = logging.getLogger(__name__)

# 内置提供者导入
from app.services.data_fetcher.tushare_fetcher import TushareFetcher
from app.services.data_fetcher.akshare_fetcher import AkShareFetcher

# 注册内置提供者
ProviderRegistry.register('tushare', TushareFetcher)
ProviderRegistry.register('akshare', AkShareFetcher)

# API配置管理器
api_config_manager = ApiConfigManager()

async def create_fetcher() -> BaseFetcher:
    """创建股票数据获取器"""
    provider_type = settings.STOCK_API_TYPE.lower()
    
    # 1. 尝试从注册中心获取内置提供者
    provider_class = ProviderRegistry.get_provider(provider_type)
    if provider_class:
        logger.info(f"Using registered provider: {provider_type}")
        return provider_class()
    
    # 2. 尝试从配置创建通用API适配器
    provider_config = api_config_manager.get_provider_config(provider_type)
    if provider_config:
        logger.info(f"Using API adapter for: {provider_type}")
        return ApiAdapter(provider_config)
    
    # 3. 尝试动态加载
    custom_provider_path = settings.STOCK_API_CUSTOM_PATH
    if custom_provider_path:
        logger.info(f"Trying to load custom provider: {provider_type} from {custom_provider_path}")
        success = ProviderRegistry.load_provider_from_path(provider_type, custom_provider_path)
        if success:
            provider_class = ProviderRegistry.get_provider(provider_type)
            if provider_class:
                return provider_class()
    
    # 默认使用TuShare
    logger.warning(f"Provider {provider_type} not found, using default TuShare")
    return TushareFetcher()
```

### 9.7 自定义API的配置和扩展

在项目中新增配置项，使其支持自定义API：

```python
# app/core/config.py
from pydantic import BaseSettings, Field
from typing import Optional
import os

class Settings(BaseSettings):
    # ...existing code...
    
    # 数据获取配置
    STOCK_API_TYPE: str = Field("tushare", description="Stock API provider")
    STOCK_API_TOKEN: Optional[str] = Field(None, description="API token for stock data")
    STOCK_API_CUSTOM_PATH: Optional[str] = Field(None, 
                                         description="Custom API provider module path")
    STOCK_API_CONFIG_PATH: str = Field("config/api_providers", 
                                    description="API provider configuration directory")
    
    # ...existing code...
```

### 9.8 API接口扩展

通过REST API暴露自定义API配置管理：

```python
# app/api/endpoints/api_manager.py
from fastapi import APIRouter, HTTPException, Depends
from app.core.api_config import ApiConfigManager, ApiProviderConfig
from app.services.data_fetcher.provider_registry import ProviderRegistry
from typing import List, Dict, Any
import yaml
import os

router = APIRouter()
api_config_manager = ApiConfigManager()

@router.get("/providers")
async def list_providers():
    """列出所有可用的API提供者"""
    # 内置提供者
    built_in = list(ProviderRegistry.list_providers().keys())
    # 配置提供者
    config_providers = api_config_manager.list_providers()
    
    return {
        "built_in_providers": built_in,
        "config_providers": config_providers
    }

@router.get("/providers/{provider_name}")
async def get_provider_config(provider_name: str):
    """获取指定提供者的配置"""
    config = api_config_manager.get_provider_config(provider_name)
    if not config:
        raise HTTPException(status_code=404, detail=f"Provider {provider_name} not found")
    
    return config

@router.post("/providers/{provider_name}")
async def update_provider_config(provider_name: str, config: Dict[str, Any]):
    """更新或添加API提供者配置"""
    # 验证配置
    try:
        provider_config = ApiProviderConfig(**config)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Invalid configuration: {str(e)}")
    
    # 保存配置
    config_dir = os.path.dirname(api_config_manager.config_path)
    if not os.path.exists(config_dir):
        os.makedirs(config_dir)
        
    file_path = os.path.join(api_config_manager.config_path, f"{provider_name}.yaml")
    with open(file_path, 'w') as f:
        yaml.dump(config, f)
    
    # 重新加载配置
    success = api_config_manager.reload_config(provider_name)
    if not success:
        raise HTTPException(status_code=500, detail="Failed to reload configuration")
    
    return {"status": "success", "message": f"Provider {provider_name} configuration updated"}

@router.delete("/providers/{provider_name}")
async def delete_provider_config(provider_name: str):
    """删除API提供者配置"""
    file_path = os.path.join(api_config_manager.config_path, f"{provider_name}.yaml")
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail=f"Provider {provider_name} config not found")
        
    try:
        os.remove(file_path)
        return {"status": "success", "message": f"Provider {provider_name} configuration deleted"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete configuration: {str(e)}")
```

### 9.9 使用示例

```python
# 使用示例代码
from app.services.data_fetcher.factory import create_fetcher
from datetime import date

async def fetch_stock_data():
    # 创建API获取器
    fetcher = await create_fetcher()
    
    # 使用上下文管理器确保资源正确释放
    async with fetcher:
        # 获取股票列表
        stocks = await fetcher.fetch_stock_list()
        print(f"获取到 {len(stocks)} 只股票")
        
        # 获取日线数据
        daily_data = await fetcher.fetch_daily_data(
            "000001.SZ", 
            date(2023, 1, 1), 
            date(2023, 1, 31)
        )
        print(f"获取到 {len(daily_data)} 条日线数据")
        
        return stocks, daily_data
```

本设计允许您通过以下几种方式自定义股票数据API：

1. 通过YAML配置文件定义新的API提供者
2. 实现BaseFetcher接口创建自定义数据源
3. 通过REST API动态管理API配置
4. 支持多种认证方式和错误处理策略
5. 提供统一的接口隐藏底层实现细节
