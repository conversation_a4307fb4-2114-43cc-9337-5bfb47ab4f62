<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票量化分析系统 - 专业版</title>
    <!-- Tailwind CSS CDN -->
    <link href="https://cdn.staticfile.net/tailwindcss/2.2.9/tailwind.min.css" rel="stylesheet">
    <!-- Font Awesome CDN -->
    <link href="https://cdn.staticfile.net/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <!-- ECharts CDN -->
    <script src="https://cdn.staticfile.net/echarts/5.4.3/echarts.min.js"></script>
    <style>
        :root {
            /* 深色主题变量 - 优化版 */
            --bg-primary: #0a0e1a;
            --bg-secondary: #1a1f2e;
            --bg-tertiary: #2a2f3e;
            --bg-quaternary: #3a3f4e;
            --text-primary: #ffffff;
            --text-secondary: #e2e8f0;
            --text-muted: #94a3b8;
            --text-accent: #60a5fa;
            --border-color: #374151;
            --border-light: #4b5563;
            --accent-primary: #3b82f6;
            --accent-secondary: #8b5cf6;
            --accent-tertiary: #06b6d4;

            /* 金融色彩 - 增强版 */
            --price-up: #10b981;
            --price-up-bg: rgba(16, 185, 129, 0.1);
            --price-down: #ef4444;
            --price-down-bg: rgba(239, 68, 68, 0.1);
            --price-flat: #6b7280;
            --volume-color: #f59e0b;
            --volume-bg: rgba(245, 158, 11, 0.1);

            /* 现代渐变色 */
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --gradient-warning: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            --gradient-danger: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            --gradient-info: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            --gradient-dark: linear-gradient(135deg, #232526 0%, #414345 100%);

            /* 玻璃态效果 */
            --glass-bg: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.1);
            --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

            /* 阴影系统 */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
        }

        /* 浅色主题 - 优化版 */
        .light-theme {
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #e2e8f0;
            --bg-quaternary: #cbd5e1;
            --text-primary: #1e293b;
            --text-secondary: #475569;
            --text-muted: #64748b;
            --border-color: #e2e8f0;
            --border-light: #f1f5f9;
            --glass-bg: rgba(255, 255, 255, 0.8);
            --glass-border: rgba(255, 255, 255, 0.2);
            --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.2);
        }

        /* 基础样式 - 增强版 */
        body {
            background: var(--bg-primary);
            color: var(--text-primary);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow-x: hidden;
        }

        /* 卡片样式 - 现代化设计 */
        .card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            box-shadow: var(--shadow-md);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--accent-primary), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .card:hover {
            box-shadow: var(--shadow-xl);
            transform: translateY(-4px);
            border-color: var(--border-light);
        }

        .card:hover::before {
            opacity: 1;
        }

        /* 玻璃态卡片 */
        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            box-shadow: var(--glass-shadow);
        }

        /* 渐变背景 - 扩展版 */
        .gradient-bg { background: var(--gradient-primary); }
        .gradient-success { background: var(--gradient-success); }
        .gradient-warning { background: var(--gradient-warning); }
        .gradient-danger { background: var(--gradient-danger); }
        .gradient-info { background: var(--gradient-info); }
        .gradient-dark { background: var(--gradient-dark); }

        /* 价格颜色 - 增强版 */
        .price-up {
            color: var(--price-up);
            font-weight: 600;
        }
        .price-down {
            color: var(--price-down);
            font-weight: 600;
        }
        .price-flat {
            color: var(--price-flat);
            font-weight: 500;
        }

        /* 价格背景 */
        .price-up-bg {
            background: var(--price-up-bg);
            color: var(--price-up);
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        .price-down-bg {
            background: var(--price-down-bg);
            color: var(--price-down);
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 600;
        }

        /* 数字字体 - 优化版 */
        .mono-font {
            font-family: 'JetBrains Mono', 'Monaco', 'Consolas', 'SF Mono', monospace;
            font-variant-numeric: tabular-nums;
            letter-spacing: 0.025em;
        }

        /* 发光效果 */
        .glow {
            box-shadow: var(--shadow-glow);
        }

        .glow-text {
            text-shadow: 0 0 10px currentColor;
        }

        /* 导航样式 - 现代化设计 */
        .nav-item {
            position: relative;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 12px;
            overflow: hidden;
        }

        .nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .nav-item:hover::before {
            left: 100%;
        }

        .nav-item.active {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
        }

        .nav-item.active::after {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 20px;
            background: var(--accent-primary);
            border-radius: 0 2px 2px 0;
            box-shadow: 0 0 10px var(--accent-primary);
        }

        /* 按钮样式 - 增强版 */
        .btn-primary {
            background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            font-size: 0.875rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        /* 次要按钮 */
        .btn-secondary {
            background: var(--bg-tertiary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-secondary:hover {
            background: var(--bg-quaternary);
            border-color: var(--accent-primary);
            transform: translateY(-1px);
        }

        /* 输入框样式 - 现代化设计 */
        .input-field {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 12px 16px;
            color: var(--text-primary);
            font-size: 0.875rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .input-field:focus {
            outline: none;
            border-color: var(--accent-primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: var(--bg-secondary);
            transform: translateY(-1px);
        }

        .input-field::placeholder {
            color: var(--text-muted);
            transition: color 0.3s ease;
        }

        .input-field:focus::placeholder {
            color: var(--text-secondary);
        }

        /* 搜索框特殊样式 */
        .search-input {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-muted);
        }

        /* 动画 - 扩展版 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @keyframes glow {
            0%, 100% { box-shadow: 0 0 5px var(--accent-primary); }
            50% { box-shadow: 0 0 20px var(--accent-primary); }
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* 动画类 */
        .fade-in { animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1); }
        .slide-in-left { animation: slideInLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1); }
        .slide-in-right { animation: slideInRight 0.6s cubic-bezier(0.4, 0, 0.2, 1); }
        .scale-in { animation: scaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1); }
        .pulse { animation: pulse 2s infinite; }
        .glow-animation { animation: glow 2s infinite; }

        /* 加载动画 */
        .loading-shimmer {
            position: relative;
            overflow: hidden;
            background: var(--bg-tertiary);
        }

        .loading-shimmer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: shimmer 1.5s infinite;
        }

        /* 响应式隐藏 */
        .mobile-hidden {
            display: block;
        }

        @media (max-width: 768px) {
            .mobile-hidden {
                display: none;
            }
        }

        /* 图表容器 - 增强版 */
        .chart-container {
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 16px;
            height: 400px;
            position: relative;
            overflow: hidden;
        }

        .chart-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .chart-toolbar {
            position: absolute;
            top: 8px;
            right: 8px;
            display: flex;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 10;
        }

        .chart-container:hover .chart-toolbar {
            opacity: 1;
        }

        .chart-tool-btn {
            width: 32px;
            height: 32px;
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            color: var(--text-secondary);
        }

        .chart-tool-btn:hover {
            background: var(--bg-tertiary);
            color: var(--text-primary);
            transform: scale(1.1);
        }

        .chart-tool-btn.active {
            background: var(--accent-primary);
            color: white;
        }

        /* 图表联动指示器 */
        .chart-sync-indicator {
            position: absolute;
            top: 8px;
            left: 8px;
            background: var(--accent-primary);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .chart-sync-indicator.active {
            opacity: 1;
        }

        /* 十字线样式 */
        .chart-crosshair {
            position: absolute;
            pointer-events: none;
            z-index: 5;
        }

        .chart-crosshair-line {
            position: absolute;
            background: var(--accent-primary);
            opacity: 0.6;
        }

        .chart-crosshair-vertical {
            width: 1px;
            height: 100%;
        }

        .chart-crosshair-horizontal {
            width: 100%;
            height: 1px;
        }

        .chart-tooltip {
            position: absolute;
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 8px;
            font-size: 12px;
            pointer-events: none;
            z-index: 10;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .chart-tooltip.show {
            opacity: 1;
        }

        /* 图表全屏模式 */
        .chart-fullscreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: var(--bg-primary);
            z-index: 9999;
            padding: 20px;
        }

        .chart-fullscreen .chart-container {
            width: 100%;
            height: 100%;
        }

        /* 技术指标叠加控制 */
        .indicator-overlay-controls {
            position: absolute;
            bottom: 8px;
            left: 8px;
            display: flex;
            gap: 4px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .chart-container:hover .indicator-overlay-controls {
            opacity: 1;
        }

        .indicator-toggle {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: var(--text-muted);
        }

        .indicator-toggle:hover {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }

        .indicator-toggle.active {
            background: var(--accent-primary);
            color: white;
        }

        /* 侧边栏 */
        .sidebar {
            background: var(--bg-secondary);
            border-right: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .sidebar.collapsed {
            width: 60px;
        }

        /* 状态指示器 */
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-online {
            background: var(--price-up);
            box-shadow: 0 0 8px rgba(16, 185, 129, 0.6);
        }

        .status-offline {
            background: var(--price-down);
        }

        /* 数据表格 */
        .data-table {
            background: var(--bg-secondary);
            border-radius: 8px;
            overflow: hidden;
        }

        .data-table th {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            font-weight: 600;
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .data-table td {
            padding: 12px;
            border-bottom: 1px solid var(--border-color);
        }

        .data-table tr:hover {
            background: var(--bg-tertiary);
        }

        /* 工具提示 */
        .tooltip {
            position: relative;
        }

        .tooltip::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--bg-tertiary);
            color: var(--text-primary);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
            z-index: 1000;
        }

        .tooltip:hover::after {
            opacity: 1;
        }

        /* 拖拽排序样式 */
        .draggable {
            cursor: move;
            transition: all 0.3s ease;
        }

        .draggable:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .dragging {
            opacity: 0.5;
            transform: rotate(5deg);
            z-index: 1000;
        }

        .drag-over {
            border: 2px dashed var(--accent-primary);
            background: rgba(59, 130, 246, 0.1);
        }

        .drag-placeholder {
            height: 60px;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
            border: 2px dashed var(--accent-primary);
            border-radius: 8px;
            margin: 4px 0;
            animation: pulse 1.5s infinite;
        }

        /* 右键菜单样式 */
        .context-menu {
            position: fixed;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-shadow: var(--shadow-xl);
            padding: 4px 0;
            min-width: 180px;
            z-index: 9999;
            opacity: 0;
            transform: scale(0.95);
            transition: all 0.2s ease;
        }

        .context-menu.show {
            opacity: 1;
            transform: scale(1);
        }

        .context-menu-item {
            padding: 8px 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: background 0.2s ease;
            font-size: 14px;
        }

        .context-menu-item:hover {
            background: var(--bg-tertiary);
        }

        .context-menu-item.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .context-menu-item.disabled:hover {
            background: transparent;
        }

        .context-menu-separator {
            height: 1px;
            background: var(--border-color);
            margin: 4px 0;
        }

        /* 快速预览卡片 */
        .preview-card {
            position: fixed;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            padding: 16px;
            min-width: 280px;
            max-width: 320px;
            box-shadow: var(--shadow-xl);
            z-index: 8888;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            pointer-events: none;
        }

        .preview-card.show {
            opacity: 1;
            transform: translateY(0);
        }

        .preview-card-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 12px;
        }

        .preview-card-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .preview-card-subtitle {
            font-size: 12px;
            color: var(--text-muted);
        }

        /* 交易相关样式 */
        .buy-btn.active {
            background: var(--price-up);
            color: white;
        }

        .sell-btn.active {
            background: var(--price-down);
            color: white;
        }

        .buy-btn, .sell-btn {
            background: transparent;
            color: var(--text-secondary);
        }

        .trade-input-group {
            position: relative;
        }

        .trade-input-group .input-field {
            padding-right: 60px;
        }

        .trade-input-suffix {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        /* 持仓相关样式 */
        .position-profit {
            color: var(--price-up);
            font-weight: 600;
        }

        .position-loss {
            color: var(--price-down);
            font-weight: 600;
        }

        .position-neutral {
            color: var(--text-muted);
            font-weight: 500;
        }

        /* 多选操作样式 */
        .multi-select-toolbar {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 12px 20px;
            box-shadow: var(--shadow-xl);
            display: flex;
            align-items: center;
            gap: 12px;
            z-index: 1000;
            opacity: 0;
            transform: translateX(-50%) translateY(100%);
            transition: all 0.3s ease;
        }

        .multi-select-toolbar.show {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }

        .selection-checkbox {
            width: 18px;
            height: 18px;
            border: 2px solid var(--border-color);
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .selection-checkbox:hover {
            border-color: var(--accent-primary);
        }

        .selection-checkbox.checked {
            background: var(--accent-primary);
            border-color: var(--accent-primary);
        }

        .selection-checkbox.checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .selection-checkbox.indeterminate {
            background: var(--accent-primary);
            border-color: var(--accent-primary);
        }

        .selection-checkbox.indeterminate::after {
            content: '−';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 14px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- 主容器 -->
    <div class="min-h-screen flex">
        <!-- 侧边栏 -->
        <div class="sidebar w-64 min-h-screen" id="sidebar">
            <!-- Logo区域 -->
            <div class="p-6 border-b border-gray-700">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 gradient-bg rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-lg font-bold">量化分析</h1>
                        <p class="text-xs text-gray-400">专业版</p>
                    </div>
                </div>
            </div>

            <!-- 导航菜单 -->
            <nav class="p-4">
                <div class="space-y-2">
                    <button class="nav-item active w-full flex items-center space-x-3 px-4 py-3 rounded-lg hover:bg-gray-700 transition-colors" data-tab="dashboard">
                        <i class="fas fa-tachometer-alt w-5"></i>
                        <span>仪表板</span>
                    </button>
                    <button class="nav-item w-full flex items-center space-x-3 px-4 py-3 rounded-lg hover:bg-gray-700 transition-colors" data-tab="analysis">
                        <i class="fas fa-chart-candlestick w-5"></i>
                        <span>股票分析</span>
                    </button>
                    <!-- 这个应该在自选股中作为二级菜单 不应该直接展示 -->
                    <button class="nav-item w-full flex items-center space-x-3 px-4 py-3 rounded-lg hover:bg-gray-700 transition-colors" data-tab="indicators">
                        <i class="fas fa-calculator w-5"></i>
                        <span>技术指标</span>
                    </button>
                    <button class="nav-item w-full flex items-center space-x-3 px-4 py-3 rounded-lg hover:bg-gray-700 transition-colors" data-tab="watchlist">
                        <i class="fas fa-star w-5"></i>
                        <span>自选股</span>
                    </button>
                    <!-- 不需要 -->
                    <!-- <button class="nav-item w-full flex items-center space-x-3 px-4 py-3 rounded-lg hover:bg-gray-700 transition-colors" data-tab="trading">
                        <i class="fas fa-exchange-alt w-5"></i>
                        <span>交易</span>
                    </button> -->
                    <!-- 不需要 -->
                    <!-- <button class="nav-item w-full flex items-center space-x-3 px-4 py-3 rounded-lg hover:bg-gray-700 transition-colors" data-tab="positions">
                        <i class="fas fa-briefcase w-5"></i>
                        <span>持仓</span>
                    </button> -->
                    <button class="nav-item w-full flex items-center space-x-3 px-4 py-3 rounded-lg hover:bg-gray-700 transition-colors" data-tab="data">
                        <i class="fas fa-database w-5"></i>
                        <span>数据管理</span>
                    </button>
                </div>

                <!-- 底部设置 -->
                <div class="mt-8 pt-4 border-t border-gray-700">
                    <button class="nav-item w-full flex items-center space-x-3 px-4 py-3 rounded-lg hover:bg-gray-700 transition-colors" data-tab="settings">
                        <i class="fas fa-cog w-5"></i>
                        <span>设置</span>
                    </button>
                    <button class="w-full flex items-center space-x-3 px-4 py-3 rounded-lg hover:bg-gray-700 transition-colors" onclick="toggleTheme()">
                        <i class="fas fa-moon w-5" id="themeIcon"></i>
                        <span>主题切换</span>
                    </button>
                </div>
            </nav>
        </div>

        <!-- 主内容区域 -->
        <div class="flex-1 flex flex-col">
            <!-- 顶部导航栏 -->
            <header class="bg-gray-800 border-b border-gray-700 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <button class="lg:hidden" onclick="toggleSidebar()">
                            <i class="fas fa-bars text-gray-400"></i>
                        </button>
                        <div class="flex items-center space-x-2">
                            <span class="status-indicator status-online"></span>
                            <span class="text-sm text-gray-400">实时连接</span>
                        </div>
                    </div>

                    <!-- 搜索框 - 现代化设计 -->
                    <div class="flex-1 max-w-lg mx-8">
                        <div class="relative group">
                            <input type="text"
                                   class="search-input w-full pl-12 pr-12 py-3 text-sm"
                                   placeholder="搜索股票代码或名称..."
                                   id="globalSearch">
                            <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-blue-400 transition-colors"></i>
                            <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <kbd class="px-2 py-1 text-xs bg-gray-700 text-gray-300 rounded border border-gray-600">⌘K</kbd>
                            </div>
                            <!-- 搜索建议下拉框 -->
                            <div class="absolute top-full left-0 right-0 mt-2 bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-50 hidden" id="searchSuggestions">
                                <div class="p-2">
                                    <div class="text-xs text-gray-400 mb-2">最近搜索</div>
                                    <div class="space-y-1">
                                        <div class="flex items-center space-x-3 p-2 hover:bg-gray-700 rounded cursor-pointer">
                                            <i class="fas fa-history text-gray-500"></i>
                                            <span class="text-sm">000001 平安银行</span>
                                        </div>
                                        <div class="flex items-center space-x-3 p-2 hover:bg-gray-700 rounded cursor-pointer">
                                            <i class="fas fa-history text-gray-500"></i>
                                            <span class="text-sm">600519 贵州茅台</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧工具栏 -->
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-400 mono-font" id="currentTime"></span>
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                            <span class="text-sm">演示用户</span>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 页面内容 -->
            <main class="flex-1 p-6 overflow-auto">
                <!-- 仪表板 -->
                <div id="dashboard" class="tab-content">
                    <!-- 市场概览卡片 - 现代化设计 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="card p-6 hover:scale-105 transition-transform duration-300 fade-in">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2 mb-2">
                                        <p class="text-sm font-medium text-gray-400">上证指数</p>
                                        <div class="status-indicator status-online"></div>
                                    </div>
                                    <p class="text-3xl font-bold mono-font mb-1" id="shIndex">3,245.67</p>
                                    <div class="flex items-center space-x-2">
                                        <span class="price-up-bg">+1.23%</span>
                                        <span class="text-xs text-gray-500">+39.87</span>
                                    </div>
                                </div>
                                <div class="w-14 h-14 gradient-success rounded-xl flex items-center justify-center shadow-lg">
                                    <i class="fas fa-arrow-trend-up text-white text-lg"></i>
                                </div>
                            </div>
                            <div class="mt-4 h-1 bg-gray-700 rounded-full overflow-hidden">
                                <div class="h-full bg-gradient-to-r from-green-500 to-emerald-400 rounded-full" style="width: 75%"></div>
                            </div>
                        </div>

                        <div class="card p-6 hover:scale-105 transition-transform duration-300 fade-in" style="animation-delay: 0.1s">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2 mb-2">
                                        <p class="text-sm font-medium text-gray-400">深证成指</p>
                                        <div class="status-indicator status-online"></div>
                                    </div>
                                    <p class="text-3xl font-bold mono-font mb-1" id="szIndex">12,456.89</p>
                                    <div class="flex items-center space-x-2">
                                        <span class="price-down-bg">-0.45%</span>
                                        <span class="text-xs text-gray-500">-56.12</span>
                                    </div>
                                </div>
                                <div class="w-14 h-14 gradient-danger rounded-xl flex items-center justify-center shadow-lg">
                                    <i class="fas fa-arrow-trend-down text-white text-lg"></i>
                                </div>
                            </div>
                            <div class="mt-4 h-1 bg-gray-700 rounded-full overflow-hidden">
                                <div class="h-full bg-gradient-to-r from-red-500 to-pink-400 rounded-full" style="width: 45%"></div>
                            </div>
                        </div>

                        <div class="card p-6 hover:scale-105 transition-transform duration-300 fade-in" style="animation-delay: 0.2s">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2 mb-2">
                                        <p class="text-sm font-medium text-gray-400">创业板指</p>
                                        <div class="status-indicator status-online"></div>
                                    </div>
                                    <p class="text-3xl font-bold mono-font mb-1" id="cybIndex">2,789.34</p>
                                    <div class="flex items-center space-x-2">
                                        <span class="price-up-bg">+2.15%</span>
                                        <span class="text-xs text-gray-500">+58.73</span>
                                    </div>
                                </div>
                                <div class="w-14 h-14 gradient-info rounded-xl flex items-center justify-center shadow-lg">
                                    <i class="fas fa-rocket text-white text-lg"></i>
                                </div>
                            </div>
                            <div class="mt-4 h-1 bg-gray-700 rounded-full overflow-hidden">
                                <div class="h-full bg-gradient-to-r from-cyan-500 to-blue-400 rounded-full" style="width: 85%"></div>
                            </div>
                        </div>

                        <div class="card p-6 hover:scale-105 transition-transform duration-300 fade-in" style="animation-delay: 0.3s">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2 mb-2">
                                        <p class="text-sm font-medium text-gray-400">活跃股票</p>
                                        <span class="pulse w-2 h-2 bg-blue-500 rounded-full"></span>
                                    </div>
                                    <p class="text-3xl font-bold mono-font mb-1">1,247</p>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-xs bg-blue-500 bg-opacity-20 text-blue-400 px-2 py-1 rounded-full">实时监控</span>
                                    </div>
                                </div>
                                <div class="w-14 h-14 gradient-primary rounded-xl flex items-center justify-center shadow-lg">
                                    <i class="fas fa-chart-line text-white text-lg"></i>
                                </div>
                            </div>
                            <div class="mt-4 h-1 bg-gray-700 rounded-full overflow-hidden">
                                <div class="h-full bg-gradient-to-r from-blue-500 to-purple-400 rounded-full loading-shimmer" style="width: 100%"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 热门股票和技术指标概览 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        <!-- 热门股票 -->
                        <div class="card p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold">热门股票</h3>
                                <button class="text-sm text-blue-400 hover:text-blue-300">查看更多</button>
                            </div>
                            <div class="space-y-3" id="hotStocks">
                                <!-- 热门股票列表将通过JavaScript填充 -->
                            </div>
                        </div>

                        <!-- 技术指标概览 -->
                        <div class="card p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold">技术指标概览</h3>
                                <button class="text-sm text-blue-400 hover:text-blue-300">详细分析</button>
                            </div>
                            <div class="space-y-4" id="indicatorOverview">
                                <!-- 指标概览将通过JavaScript填充 -->
                            </div>
                        </div>
                    </div>

                    <!-- 市场趋势图表 -->
                    <div class="card p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold">市场趋势</h3>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded">日线</button>
                                <button class="px-3 py-1 text-sm text-gray-400 hover:text-white rounded">周线</button>
                                <button class="px-3 py-1 text-sm text-gray-400 hover:text-white rounded">月线</button>
                            </div>
                        </div>
                        <div class="chart-container" id="marketTrendChart"></div>
                    </div>
                </div>

                <!-- 股票分析 -->
                <div id="analysis" class="tab-content hidden">
                    <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
                        <!-- 股票选择面板 -->
                        <div class="xl:col-span-1">
                            <div class="card p-6 mb-6">
                                <h3 class="text-lg font-semibold mb-4">股票选择</h3>
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm text-gray-400 mb-2">股票代码</label>
                                        <select class="input-field w-full" id="stockSelector">
                                            <option value="">选择股票</option>
                                            <option value="000001">000001 平安银行</option>
                                            <option value="000002">000002 万科A</option>
                                            <option value="600036">600036 招商银行</option>
                                            <option value="600519">600519 贵州茅台</option>
                                            <option value="000858">000858 五粮液</option>
                                        </select>
                                    </div>

                                    <div>
                                        <label class="block text-sm text-gray-400 mb-2">时间周期</label>
                                        <select class="input-field w-full" id="periodSelector">
                                            <option value="D1">日线</option>
                                            <option value="W1">周线</option>
                                            <option value="M1">月线</option>
                                        </select>
                                    </div>

                                    <div>
                                        <label class="block text-sm text-gray-400 mb-2">时间范围</label>
                                        <select class="input-field w-full" id="rangeSelector">
                                            <option value="30">最近30天</option>
                                            <option value="60">最近60天</option>
                                            <option value="120">最近120天</option>
                                            <option value="250">最近250天</option>
                                        </select>
                                    </div>

                                    <button class="btn-primary w-full" onclick="loadStockAnalysis()" data-loading="stock-analysis">
                                        <i class="fas fa-chart-line mr-2"></i>
                                        开始分析
                                    </button>
                                </div>
                            </div>

                            <!-- 股票基本信息 -->
                            <div class="card p-6" id="stockInfo" style="display: none;">
                                <h3 class="text-lg font-semibold mb-4">基本信息</h3>
                                <div class="space-y-3" id="stockBasicInfo">
                                    <!-- 股票基本信息将通过JavaScript填充 -->
                                </div>
                            </div>
                        </div>

                        <!-- 主图表区域 -->
                        <div class="xl:col-span-3">
                            <!-- K线图表 - 增强版 -->
                            <div class="card p-6 mb-6">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="text-lg font-semibold">K线图表</h3>
                                    <div class="flex space-x-2">
                                        <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded" data-indicator="none">主图</button>
                                        <button class="px-3 py-1 text-sm text-gray-400 hover:text-white rounded" data-indicator="ma">均线</button>
                                        <button class="px-3 py-1 text-sm text-gray-400 hover:text-white rounded" data-indicator="bollinger">布林带</button>
                                    </div>
                                </div>
                                <div class="chart-container" id="klineChart" style="height: 500px;" data-context-menu="chart">
                                    <div class="chart-wrapper">
                                        <div class="chart-toolbar">
                                            <div class="chart-tool-btn tooltip" data-tooltip="缩放" onclick="app.toggleChartZoom('klineChart')">
                                                <i class="fas fa-search-plus"></i>
                                            </div>
                                            <div class="chart-tool-btn tooltip" data-tooltip="平移" onclick="app.toggleChartPan('klineChart')">
                                                <i class="fas fa-hand-paper"></i>
                                            </div>
                                            <div class="chart-tool-btn tooltip" data-tooltip="十字线" onclick="app.toggleChartCrosshair('klineChart')">
                                                <i class="fas fa-crosshairs"></i>
                                            </div>
                                            <div class="chart-tool-btn tooltip" data-tooltip="全屏" onclick="app.toggleChartFullscreen('klineChart')">
                                                <i class="fas fa-expand"></i>
                                            </div>
                                            <div class="chart-tool-btn tooltip" data-tooltip="保存" onclick="app.saveChartImage('klineChart')">
                                                <i class="fas fa-camera"></i>
                                            </div>
                                        </div>
                                        <div class="chart-sync-indicator" id="klineChartSync">
                                            <i class="fas fa-link"></i> 已联动
                                        </div>
                                        <div class="indicator-overlay-controls">
                                            <div class="indicator-toggle" data-indicator="ma5" onclick="app.toggleIndicatorOverlay('klineChart', 'ma5')">MA5</div>
                                            <div class="indicator-toggle" data-indicator="ma10" onclick="app.toggleIndicatorOverlay('klineChart', 'ma10')">MA10</div>
                                            <div class="indicator-toggle" data-indicator="ma20" onclick="app.toggleIndicatorOverlay('klineChart', 'ma20')">MA20</div>
                                            <div class="indicator-toggle" data-indicator="boll" onclick="app.toggleIndicatorOverlay('klineChart', 'boll')">BOLL</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 技术指标图表 -->
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div class="card p-6">
                                    <div class="flex items-center justify-between mb-4">
                                        <h3 class="text-lg font-semibold">MACD指标</h3>
                                        <button class="text-sm text-blue-400 hover:text-blue-300">参数设置</button>
                                    </div>
                                    <div class="chart-container" id="macdChart" style="height: 300px;"></div>
                                </div>

                                <div class="card p-6">
                                    <div class="flex items-center justify-between mb-4">
                                        <h3 class="text-lg font-semibold">成交量</h3>
                                        <button class="text-sm text-blue-400 hover:text-blue-300">详细分析</button>
                                    </div>
                                    <div class="chart-container" id="volumeChart" style="height: 300px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 技术指标 -->
                <div id="indicators" class="tab-content hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                        <!-- 指标选择 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">指标选择</h3>
                            <div class="space-y-3">
                                <label class="flex items-center space-x-3">
                                    <input type="checkbox" class="form-checkbox" checked data-indicator="macd">
                                    <span>MACD</span>
                                </label>
                                <label class="flex items-center space-x-3">
                                    <input type="checkbox" class="form-checkbox" data-indicator="kdj">
                                    <span>KDJ</span>
                                </label>
                                <label class="flex items-center space-x-3">
                                    <input type="checkbox" class="form-checkbox" data-indicator="rsi">
                                    <span>RSI</span>
                                </label>
                                <label class="flex items-center space-x-3">
                                    <input type="checkbox" class="form-checkbox" data-indicator="bollinger">
                                    <span>Bollinger Bands</span>
                                </label>
                                <label class="flex items-center space-x-3">
                                    <input type="checkbox" class="form-checkbox" data-indicator="arbr">
                                    <span>ARBR</span>
                                </label>
                            </div>
                        </div>

                        <!-- 参数设置 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">参数设置</h3>
                            <div class="space-y-4" id="indicatorParams">
                                <div>
                                    <label class="block text-sm text-gray-400 mb-2">MACD参数</label>
                                    <div class="grid grid-cols-3 gap-2">
                                        <input type="number" class="input-field text-sm" value="12" placeholder="快线">
                                        <input type="number" class="input-field text-sm" value="26" placeholder="慢线">
                                        <input type="number" class="input-field text-sm" value="9" placeholder="信号线">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 信号提示 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">交易信号</h3>
                            <div class="space-y-3" id="tradingSignals">
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                    <span class="text-sm">MACD金叉</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                    <span class="text-sm">RSI超买</span>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                    <span class="text-sm">KDJ死叉</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 指标图表网格 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">MACD指标</h3>
                            <div class="chart-container" id="indicatorMacdChart"></div>
                        </div>

                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">KDJ指标</h3>
                            <div class="chart-container" id="indicatorKdjChart"></div>
                        </div>

                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">RSI指标</h3>
                            <div class="chart-container" id="indicatorRsiChart"></div>
                        </div>

                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">Bollinger Bands</h3>
                            <div class="chart-container" id="indicatorBollingerChart"></div>
                        </div>
                    </div>
                </div>

                <!-- 自选股 -->
                <div id="watchlist" class="tab-content hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- 自选股列表 - 增强版 -->
                        <div class="lg:col-span-2">
                            <div class="card p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center gap-3">
                                        <h3 class="text-lg font-semibold">我的自选股</h3>
                                        <div class="selection-checkbox" onclick="app.multiSelectManager.toggleSelectAll()" style="display: none;"></div>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <button class="btn-secondary text-sm" onclick="app.toggleMultiSelectMode()">
                                            <i class="fas fa-check-square mr-1"></i>多选
                                        </button>
                                        <button class="btn-primary" onclick="showAddWatchlistModal()">
                                            <i class="fas fa-plus mr-2"></i>添加股票
                                        </button>
                                    </div>
                                </div>

                                <div class="data-table">
                                    <table class="w-full">
                                        <thead>
                                            <tr>
                                                <th style="width: 40px;"></th>
                                                <th>代码</th>
                                                <th>名称</th>
                                                <th>现价</th>
                                                <th>涨跌幅</th>
                                                <th>成交量</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="watchlistTable" data-drop-zone="watchlist">
                                            <!-- 自选股数据将通过JavaScript填充 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- 快速分析 -->
                        <div class="space-y-6">
                            <div class="card p-6">
                                <h3 class="text-lg font-semibold mb-4">快速分析</h3>
                                <div class="space-y-3" id="quickAnalysis">
                                    <div class="text-sm">
                                        <span class="text-gray-400">强势股票:</span>
                                        <span class="text-green-400">3只</span>
                                    </div>
                                    <div class="text-sm">
                                        <span class="text-gray-400">弱势股票:</span>
                                        <span class="text-red-400">2只</span>
                                    </div>
                                    <div class="text-sm">
                                        <span class="text-gray-400">震荡股票:</span>
                                        <span class="text-yellow-400">1只</span>
                                    </div>
                                </div>
                            </div>

                            <div class="card p-6">
                                <h3 class="text-lg font-semibold mb-4">预警提醒</h3>
                                <div class="space-y-3" id="alertList">
                                    <div class="flex items-center space-x-3 p-3 bg-red-500 bg-opacity-20 rounded-lg">
                                        <i class="fas fa-exclamation-triangle text-red-400"></i>
                                        <div class="text-sm">
                                            <div class="font-medium">000001 平安银行</div>
                                            <div class="text-gray-400">跌破支撑位</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据管理 -->
                <div id="data" class="tab-content hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                        <!-- 数据源状态 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">数据源状态</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-3 bg-green-500 bg-opacity-20 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="status-indicator status-online"></div>
                                        <span>Tushare API</span>
                                    </div>
                                    <span class="text-sm text-green-400">正常</span>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-green-500 bg-opacity-20 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="status-indicator status-online"></div>
                                        <span>AkShare API</span>
                                    </div>
                                    <span class="text-sm text-green-400">正常</span>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-yellow-500 bg-opacity-20 rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <div class="status-indicator" style="background: #f59e0b;"></div>
                                        <span>Yahoo Finance</span>
                                    </div>
                                    <span class="text-sm text-yellow-400">延迟</span>
                                </div>
                            </div>
                        </div>

                        <!-- 数据统计 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">数据统计</h3>
                            <div class="space-y-4">
                                <div class="flex justify-between">
                                    <span class="text-gray-400">股票总数</span>
                                    <span class="mono-font">4,856</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">今日更新</span>
                                    <span class="mono-font">4,823</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">历史数据</span>
                                    <span class="mono-font">2.3TB</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">最后更新</span>
                                    <span class="mono-font" id="lastUpdate">15:30:00</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 股票列表管理 -->
                    <div class="card p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold">股票列表管理</h3>
                            <div class="flex space-x-2">
                                <button class="btn-primary" onclick="refreshStockData()" data-loading="refresh-data">
                                    <i class="fas fa-sync mr-2"></i>刷新数据
                                </button>
                                <button class="btn-secondary" onclick="exportStockData()">
                                    <i class="fas fa-download mr-2"></i>导出数据
                                </button>
                            </div>
                        </div>

                        <!-- 搜索和筛选 -->
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                            <input type="text" class="input-field" placeholder="搜索股票代码或名称" id="stockSearchInput">
                            <select class="input-field" id="industryFilter">
                                <option value="">所有行业</option>
                                <option value="银行">银行</option>
                                <option value="房地产">房地产</option>
                                <option value="白酒">白酒</option>
                                <option value="科技">科技</option>
                            </select>
                            <select class="input-field" id="marketFilter">
                                <option value="">所有市场</option>
                                <option value="SH">上海</option>
                                <option value="SZ">深圳</option>
                                <option value="CYB">创业板</option>
                            </select>
                            <button class="btn-primary" onclick="filterStocks()">
                                <i class="fas fa-filter mr-2"></i>筛选
                            </button>
                        </div>

                        <!-- 股票数据表格 -->
                        <div class="data-table">
                            <table class="w-full">
                                <thead>
                                    <tr>
                                        <th>代码</th>
                                        <th>名称</th>
                                        <th>行业</th>
                                        <th>市场</th>
                                        <th>现价</th>
                                        <th>涨跌幅</th>
                                        <th>成交量</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="stockDataTable">
                                    <!-- 股票数据将通过JavaScript填充 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <div class="flex items-center justify-between mt-4">
                            <span class="text-sm text-gray-400">显示 1-20 条，共 4,856 条记录</span>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 text-sm border border-gray-600 rounded hover:bg-gray-700">上一页</button>
                                <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded">1</button>
                                <button class="px-3 py-1 text-sm border border-gray-600 rounded hover:bg-gray-700">2</button>
                                <button class="px-3 py-1 text-sm border border-gray-600 rounded hover:bg-gray-700">3</button>
                                <button class="px-3 py-1 text-sm border border-gray-600 rounded hover:bg-gray-700">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 交易 -->
                <div id="trading" class="tab-content hidden">
                    <div class="grid grid-cols-1 xl:grid-cols-3 gap-6">
                        <!-- 交易面板 -->
                        <div class="xl:col-span-2">
                            <div class="card p-6">
                                <div class="flex items-center justify-between mb-6">
                                    <h3 class="text-xl font-semibold">股票交易</h3>
                                    <div class="flex items-center space-x-2">
                                        <span class="status-indicator status-online"></span>
                                        <span class="text-sm text-gray-400">交易系统正常</span>
                                    </div>
                                </div>

                                <!-- 股票选择 -->
                                <div class="mb-6">
                                    <label class="block text-sm text-gray-400 mb-3">选择股票</label>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <select class="input-field" id="tradingStockSelector">
                                            <option value="">请选择股票</option>
                                        </select>
                                        <button class="btn-secondary" onclick="app.selectFromWatchlist()">
                                            <i class="fas fa-star mr-2"></i>从自选股选择
                                        </button>
                                    </div>
                                </div>

                                <!-- 选中股票信息 -->
                                <div id="selectedStockInfo" class="mb-6 p-4 bg-gray-700 rounded-lg hidden">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="text-lg font-semibold" id="selectedStockName">股票名称</h4>
                                            <p class="text-sm text-gray-400" id="selectedStockCode">000000</p>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-2xl font-bold mono-font" id="selectedStockPrice">0.00</div>
                                            <div class="text-sm" id="selectedStockChange">+0.00%</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 交易类型切换 -->
                                <div class="mb-6">
                                    <div class="flex space-x-1 bg-gray-700 rounded-lg p-1">
                                        <button class="flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors buy-btn active"
                                                onclick="app.switchTradeType('buy')">
                                            <i class="fas fa-arrow-up mr-2"></i>买入
                                        </button>
                                        <button class="flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors sell-btn"
                                                onclick="app.switchTradeType('sell')">
                                            <i class="fas fa-arrow-down mr-2"></i>卖出
                                        </button>
                                    </div>
                                </div>

                                <!-- 交易参数 -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- 价格类型 -->
                                    <div>
                                        <label class="block text-sm text-gray-400 mb-2">价格类型</label>
                                        <select class="input-field" id="priceType" onchange="app.updatePriceType()">
                                            <option value="market">市价单</option>
                                            <option value="limit">限价单</option>
                                        </select>
                                    </div>

                                    <!-- 交易价格 -->
                                    <div>
                                        <label class="block text-sm text-gray-400 mb-2">交易价格</label>
                                        <input type="number" class="input-field mono-font" id="tradePrice"
                                               placeholder="0.00" step="0.01" onchange="app.calculateTradeAmount()">
                                    </div>

                                    <!-- 交易数量 -->
                                    <div>
                                        <label class="block text-sm text-gray-400 mb-2">交易数量（股）</label>
                                        <input type="number" class="input-field mono-font" id="tradeQuantity"
                                               placeholder="100" step="100" min="100" onchange="app.calculateTradeAmount()">
                                        <div class="text-xs text-gray-500 mt-1">最小交易单位：100股</div>
                                    </div>

                                    <!-- 交易金额 -->
                                    <div>
                                        <label class="block text-sm text-gray-400 mb-2">交易金额</label>
                                        <input type="text" class="input-field mono-font bg-gray-600" id="tradeAmount"
                                               placeholder="0.00" readonly>
                                    </div>
                                </div>

                                <!-- 快速数量选择 -->
                                <div class="mt-4">
                                    <label class="block text-sm text-gray-400 mb-2">快速选择</label>
                                    <div class="flex space-x-2">
                                        <button class="px-3 py-1 text-sm bg-gray-600 hover:bg-gray-500 rounded" onclick="app.setQuickQuantity(100)">100股</button>
                                        <button class="px-3 py-1 text-sm bg-gray-600 hover:bg-gray-500 rounded" onclick="app.setQuickQuantity(500)">500股</button>
                                        <button class="px-3 py-1 text-sm bg-gray-600 hover:bg-gray-500 rounded" onclick="app.setQuickQuantity(1000)">1000股</button>
                                        <button class="px-3 py-1 text-sm bg-gray-600 hover:bg-gray-500 rounded" onclick="app.setQuickQuantity(2000)">2000股</button>
                                    </div>
                                </div>

                                <!-- 交易按钮 -->
                                <div class="mt-8">
                                    <button class="btn-primary w-full py-4 text-lg" onclick="app.showTradeConfirmModal()" id="tradeSubmitBtn" disabled>
                                        <i class="fas fa-exchange-alt mr-2"></i>
                                        <span id="tradeSubmitText">请选择股票</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 账户信息和交易记录 -->
                        <div class="space-y-6">
                            <!-- 账户资金 -->
                            <div class="card p-6">
                                <h3 class="text-lg font-semibold mb-4">账户资金</h3>
                                <div class="space-y-3">
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">总资产</span>
                                        <span class="mono-font font-semibold" id="totalAssets">1,000,000.00</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">可用资金</span>
                                        <span class="mono-font text-green-400" id="availableFunds">500,000.00</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">持仓市值</span>
                                        <span class="mono-font" id="positionValue">500,000.00</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">今日盈亏</span>
                                        <span class="mono-font price-up" id="todayPnL">+12,345.67</span>
                                    </div>
                                </div>
                            </div>

                            <!-- 费用预估 -->
                            <div class="card p-6">
                                <h3 class="text-lg font-semibold mb-4">费用预估</h3>
                                <div class="space-y-3" id="feeEstimation">
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">佣金费用</span>
                                        <span class="mono-font" id="commissionFee">0.00</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">印花税</span>
                                        <span class="mono-font" id="stampTax">0.00</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-400">过户费</span>
                                        <span class="mono-font" id="transferFee">0.00</span>
                                    </div>
                                    <div class="border-t border-gray-600 pt-2">
                                        <div class="flex justify-between font-semibold">
                                            <span>总费用</span>
                                            <span class="mono-font" id="totalFee">0.00</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 风险提示 -->
                            <div class="card p-6 border-l-4 border-yellow-500">
                                <h3 class="text-lg font-semibold mb-3 text-yellow-400">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>风险提示
                                </h3>
                                <div class="text-sm text-gray-300 space-y-2">
                                    <p>• 股票投资有风险，入市需谨慎</p>
                                    <p>• 请根据自身风险承受能力进行投资</p>
                                    <p>• 本系统为模拟交易，仅供学习使用</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 交易历史 -->
                    <div class="card p-6 mt-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold">交易历史</h3>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded">今日</button>
                                <button class="px-3 py-1 text-sm text-gray-400 hover:text-white rounded">本周</button>
                                <button class="px-3 py-1 text-sm text-gray-400 hover:text-white rounded">本月</button>
                            </div>
                        </div>
                        <div class="data-table">
                            <table class="w-full">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>股票</th>
                                        <th>操作</th>
                                        <th>价格</th>
                                        <th>数量</th>
                                        <th>金额</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody id="tradeHistoryTable">
                                    <!-- 交易历史数据将通过JavaScript填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 持仓 -->
                <div id="positions" class="tab-content hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-6">
                        <!-- 持仓汇总卡片 -->
                        <div class="card p-6 hover:scale-105 transition-transform duration-300">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-400 mb-2">持仓市值</p>
                                    <p class="text-2xl font-bold mono-font" id="totalPositionValue">500,000.00</p>
                                    <div class="text-sm text-gray-500 mt-1">持仓股票数：<span id="positionCount">5</span></div>
                                </div>
                                <div class="w-12 h-12 gradient-info rounded-xl flex items-center justify-center">
                                    <i class="fas fa-briefcase text-white"></i>
                                </div>
                            </div>
                        </div>

                        <div class="card p-6 hover:scale-105 transition-transform duration-300">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-400 mb-2">总盈亏</p>
                                    <p class="text-2xl font-bold mono-font price-up" id="totalPnL">+25,680.50</p>
                                    <div class="text-sm price-up mt-1">+5.14%</div>
                                </div>
                                <div class="w-12 h-12 gradient-success rounded-xl flex items-center justify-center">
                                    <i class="fas fa-chart-line text-white"></i>
                                </div>
                            </div>
                        </div>

                        <div class="card p-6 hover:scale-105 transition-transform duration-300">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-400 mb-2">今日盈亏</p>
                                    <p class="text-2xl font-bold mono-font price-up" id="todayPositionPnL">+3,245.80</p>
                                    <div class="text-sm price-up mt-1">+0.65%</div>
                                </div>
                                <div class="w-12 h-12 gradient-warning rounded-xl flex items-center justify-center">
                                    <i class="fas fa-calendar-day text-white"></i>
                                </div>
                            </div>
                        </div>

                        <div class="card p-6 hover:scale-105 transition-transform duration-300">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-400 mb-2">持仓成本</p>
                                    <p class="text-2xl font-bold mono-font" id="totalCost">474,319.50</p>
                                    <div class="text-sm text-gray-500 mt-1">平均成本</div>
                                </div>
                                <div class="w-12 h-12 gradient-primary rounded-xl flex items-center justify-center">
                                    <i class="fas fa-coins text-white"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 持仓列表 -->
                    <div class="card p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center gap-3">
                                <h3 class="text-lg font-semibold">持仓明细</h3>
                                <div class="selection-checkbox" onclick="app.multiSelectManager.toggleSelectAll()" style="display: none;"></div>
                            </div>
                            <div class="flex items-center gap-2">
                                <button class="btn-secondary text-sm" onclick="app.toggleMultiSelectMode()">
                                    <i class="fas fa-check-square mr-1"></i>多选
                                </button>
                                <button class="btn-primary" onclick="app.refreshPositions()">
                                    <i class="fas fa-sync mr-2"></i>刷新持仓
                                </button>
                            </div>
                        </div>

                        <div class="data-table">
                            <table class="w-full">
                                <thead>
                                    <tr>
                                        <th style="width: 40px;"></th>
                                        <th>股票代码</th>
                                        <th>股票名称</th>
                                        <th>持有股数</th>
                                        <th>开仓均价</th>
                                        <th>当前价格</th>
                                        <th>持仓市值</th>
                                        <th>持仓盈亏</th>
                                        <th>盈亏比例</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="positionsTable">
                                    <!-- 持仓数据将通过JavaScript填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 持仓分析图表 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">持仓分布</h3>
                            <div class="chart-container" id="positionDistributionChart" style="height: 300px;"></div>
                        </div>

                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">盈亏分析</h3>
                            <div class="chart-container" id="pnlAnalysisChart" style="height: 300px;"></div>
                        </div>
                    </div>
                </div>

                <!-- 设置 -->
                <div id="settings" class="tab-content hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 界面设置 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">界面设置</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm text-gray-400 mb-2">主题模式</label>
                                    <select class="input-field w-full" id="themeSelect">
                                        <option value="dark">深色主题</option>
                                        <option value="light">浅色主题</option>
                                        <option value="auto">跟随系统</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm text-gray-400 mb-2">语言设置</label>
                                    <select class="input-field w-full">
                                        <option value="zh-CN">简体中文</option>
                                        <option value="en-US">English</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="flex items-center space-x-3">
                                        <input type="checkbox" class="form-checkbox" checked>
                                        <span>启用实时数据更新</span>
                                    </label>
                                </div>

                                <div>
                                    <label class="flex items-center space-x-3">
                                        <input type="checkbox" class="form-checkbox">
                                        <span>启用声音提醒</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- 数据设置 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">数据设置</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm text-gray-400 mb-2">数据更新频率</label>
                                    <select class="input-field w-full">
                                        <option value="1">1秒</option>
                                        <option value="3" selected>3秒</option>
                                        <option value="5">5秒</option>
                                        <option value="10">10秒</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm text-gray-400 mb-2">默认K线周期</label>
                                    <select class="input-field w-full">
                                        <option value="D1" selected>日线</option>
                                        <option value="W1">周线</option>
                                        <option value="M1">月线</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm text-gray-400 mb-2">历史数据范围</label>
                                    <select class="input-field w-full">
                                        <option value="30">30天</option>
                                        <option value="60">60天</option>
                                        <option value="120" selected>120天</option>
                                        <option value="250">250天</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 技术指标设置 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">技术指标设置</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm text-gray-400 mb-2">MACD参数</label>
                                    <div class="grid grid-cols-3 gap-2">
                                        <input type="number" class="input-field text-sm" value="12" placeholder="快线">
                                        <input type="number" class="input-field text-sm" value="26" placeholder="慢线">
                                        <input type="number" class="input-field text-sm" value="9" placeholder="信号线">
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm text-gray-400 mb-2">KDJ参数</label>
                                    <div class="grid grid-cols-3 gap-2">
                                        <input type="number" class="input-field text-sm" value="9" placeholder="K值">
                                        <input type="number" class="input-field text-sm" value="3" placeholder="D值">
                                        <input type="number" class="input-field text-sm" value="3" placeholder="J值">
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm text-gray-400 mb-2">RSI参数</label>
                                    <input type="number" class="input-field w-full" value="14" placeholder="周期">
                                </div>
                            </div>
                        </div>

                        <!-- 系统信息 -->
                        <div class="card p-6">
                            <h3 class="text-lg font-semibold mb-4">系统信息</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-400">版本号</span>
                                    <span class="mono-font">v2.1.0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">构建时间</span>
                                    <span class="mono-font">2024-06-14</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">API版本</span>
                                    <span class="mono-font">v1.0</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">数据库</span>
                                    <span class="mono-font">MySQL 8.0</span>
                                </div>
                            </div>

                            <div class="mt-6 pt-4 border-t border-gray-700">
                                <button class="btn-primary w-full">
                                    <i class="fas fa-save mr-2"></i>保存设置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="addWatchlistModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="card p-6 w-full max-w-md">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">添加自选股</h3>
                    <button onclick="hideAddWatchlistModal()" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm text-gray-400 mb-2">股票代码</label>
                        <input type="text" class="input-field w-full" placeholder="例如: 000001" id="newWatchlistCode">
                    </div>
                    <div>
                        <label class="block text-sm text-gray-400 mb-2">股票名称</label>
                        <input type="text" class="input-field w-full" placeholder="例如: 平安银行" id="newWatchlistName">
                    </div>
                    <div class="flex space-x-3">
                        <button onclick="hideAddWatchlistModal()" class="flex-1 px-4 py-2 border border-gray-600 rounded hover:bg-gray-700">
                            取消
                        </button>
                        <button onclick="addToWatchlist()" class="flex-1 btn-primary">
                            添加
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 右键菜单 -->
    <div id="contextMenu" class="context-menu">
        <!-- 菜单项将通过JavaScript动态生成 -->
    </div>

    <!-- 快速预览卡片 -->
    <div id="previewCard" class="preview-card">
        <div class="preview-card-header">
            <div>
                <div class="preview-card-title" id="previewTitle">股票名称</div>
                <div class="preview-card-subtitle" id="previewSubtitle">000000</div>
            </div>
            <div class="text-right">
                <div class="text-lg font-bold mono-font" id="previewPrice">0.00</div>
                <div class="text-sm" id="previewChange">+0.00%</div>
            </div>
        </div>
        <div class="space-y-2" id="previewContent">
            <!-- 预览内容将通过JavaScript动态生成 -->
        </div>
    </div>

    <!-- 交易确认模态框 -->
    <div id="tradeConfirmModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="card p-6 w-full max-w-md">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">确认交易</h3>
                    <button onclick="hideTradeConfirmModal()" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="bg-gray-700 p-4 rounded-lg">
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-400">股票</span>
                            <span id="confirmStockInfo">000001 平安银行</span>
                        </div>
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-400">操作</span>
                            <span id="confirmTradeType" class="font-semibold">买入</span>
                        </div>
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-400">价格</span>
                            <span id="confirmPrice" class="mono-font">12.85</span>
                        </div>
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-400">数量</span>
                            <span id="confirmQuantity" class="mono-font">1000股</span>
                        </div>
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-400">金额</span>
                            <span id="confirmAmount" class="mono-font font-semibold">12,850.00</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">手续费</span>
                            <span id="confirmFee" class="mono-font">12.85</span>
                        </div>
                    </div>

                    <div class="bg-yellow-500 bg-opacity-20 border border-yellow-500 rounded-lg p-3">
                        <div class="flex items-start space-x-2">
                            <i class="fas fa-exclamation-triangle text-yellow-400 mt-0.5"></i>
                            <div class="text-sm text-yellow-300">
                                <p class="font-medium mb-1">风险提示</p>
                                <p>请确认交易信息无误，交易一旦提交将无法撤销。</p>
                            </div>
                        </div>
                    </div>

                    <div class="flex space-x-3">
                        <button onclick="hideTradeConfirmModal()" class="flex-1 px-4 py-2 border border-gray-600 rounded hover:bg-gray-700">
                            取消
                        </button>
                        <button onclick="confirmTrade()" class="flex-1 btn-primary">
                            确认交易
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 股票选择模态框 -->
    <div id="stockSelectModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="card p-6 w-full max-w-2xl">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">选择股票</h3>
                    <button onclick="hideStockSelectModal()" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="mb-4">
                    <input type="text" class="input-field w-full" placeholder="搜索股票代码或名称..."
                           id="stockSelectSearch" onkeyup="filterStockSelect()">
                </div>

                <div class="max-h-96 overflow-y-auto">
                    <div class="data-table">
                        <table class="w-full">
                            <thead>
                                <tr>
                                    <th>代码</th>
                                    <th>名称</th>
                                    <th>现价</th>
                                    <th>涨跌幅</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="stockSelectTable">
                                <!-- 股票选择数据将通过JavaScript填充 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 多选操作工具栏 -->
    <div id="multiSelectToolbar" class="multi-select-toolbar">
        <div class="flex items-center gap-3">
            <span class="text-sm text-gray-400">已选择 <span id="selectedCount">0</span> 项</span>
            <div class="w-px h-6 bg-gray-600"></div>
            <button class="btn-primary text-sm px-3 py-1" onclick="batchAddToWatchlist()">
                <i class="fas fa-star mr-1"></i>批量添加自选
            </button>
            <button class="btn-secondary text-sm px-3 py-1" onclick="batchRemoveFromWatchlist()">
                <i class="fas fa-trash mr-1"></i>批量删除
            </button>
            <button class="btn-secondary text-sm px-3 py-1" onclick="exportSelectedStocks()">
                <i class="fas fa-download mr-1"></i>导出选中
            </button>
            <button class="text-gray-400 hover:text-white ml-2" onclick="clearSelection()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <script>
        // 全局变量和状态管理 - 增强版
        class StockAnalysisApp {
            constructor() {
                this.currentTab = 'dashboard';
                this.currentTheme = 'dark';
                this.selectedStock = null;
                this.charts = {};
                this.apiBaseUrl = 'http://localhost:8000/api/v1'; // 后端API地址
                this.isRealTimeEnabled = true;
                this.updateInterval = 3000; // 3秒更新一次
                this.retryCount = 0;
                this.maxRetries = 3;

                // 数据存储
                this.stockData = [];
                this.watchlistData = [];
                this.positionsData = [];
                this.tradeHistoryData = [];
                this.accountData = {
                    totalAssets: 1000000.00,
                    availableFunds: 500000.00,
                    positionValue: 500000.00,
                    todayPnL: 12345.67
                };
                this.marketData = {
                    shIndex: { value: 3245.67, change: 1.23, volume: '2.3亿' },
                    szIndex: { value: 12456.89, change: -0.45, volume: '1.8亿' },
                    cybIndex: { value: 2789.34, change: 2.15, volume: '0.9亿' }
                };

                // 交易相关状态
                this.currentTradeType = 'buy';
                this.selectedTradingStock = null;

                // 缓存管理
                this.cache = new Map();
                this.cacheTimeout = 60000; // 1分钟缓存

                // 加载状态管理
                this.loadingStates = new Set();

                // WebSocket连接（用于实时数据）
                this.ws = null;
                this.wsReconnectAttempts = 0;
                this.maxWsReconnectAttempts = 5;

                // 高级交互功能管理 - 延迟初始化
                this.dragManager = null;
                this.contextMenuManager = null;
                this.previewManager = null;
                this.multiSelectManager = null;
                this.chartEnhancer = null;

                this.init();
            }

            // 初始化应用
            init() {
                try {
                    this.setupEventListeners();
                    this.loadInitialData();
                    this.startRealTimeUpdates();
                    this.updateCurrentTime();
                    this.loadUserPreferences();
                    this.setupKeyboardShortcuts();

                    // 延迟初始化高级功能管理器（等待类定义完成）
                    setTimeout(() => {
                        this.initializeAdvancedFeatures();
                    }, 100);

                    setInterval(() => this.updateCurrentTime(), 1000);
                } catch (error) {
                    console.error('应用初始化失败:', error);
                    this.showNotification('系统初始化失败，请刷新页面重试', 'error');
                }
            }

            // 初始化高级功能管理器
            initializeAdvancedFeatures() {
                try {
                    if (typeof DragManager !== 'undefined') {
                        this.dragManager = new DragManager();
                    }
                    if (typeof ContextMenuManager !== 'undefined') {
                        this.contextMenuManager = new ContextMenuManager();
                    }
                    if (typeof PreviewManager !== 'undefined') {
                        this.previewManager = new PreviewManager();
                    }
                    if (typeof MultiSelectManager !== 'undefined') {
                        this.multiSelectManager = new MultiSelectManager();
                    }
                    if (typeof ChartEnhancer !== 'undefined') {
                        this.chartEnhancer = new ChartEnhancer();
                    }
                    console.log('高级功能管理器初始化完成');
                } catch (error) {
                    console.error('高级功能管理器初始化失败:', error);
                }
            }

            // 设置WebSocket连接
            setupWebSocket() {
                // 暂时禁用WebSocket连接，使用模拟数据
                console.log('WebSocket连接已禁用，使用模拟数据模式');
                return;

                if (!this.isRealTimeEnabled) return;

                try {
                    const wsUrl = this.apiBaseUrl.replace('http', 'ws') + '/ws';
                    this.ws = new WebSocket(wsUrl);

                    this.ws.onopen = () => {
                        console.log('WebSocket连接已建立');
                        this.wsReconnectAttempts = 0;
                        this.showNotification('实时数据连接成功', 'success');
                    };

                    this.ws.onmessage = (event) => {
                        try {
                            const data = JSON.parse(event.data);
                            this.handleRealtimeData(data);
                        } catch (error) {
                            console.error('WebSocket消息解析失败:', error);
                        }
                    };

                    this.ws.onclose = () => {
                        console.log('WebSocket连接已关闭');
                        this.reconnectWebSocket();
                    };

                    this.ws.onerror = (error) => {
                        console.error('WebSocket错误:', error);
                    };
                } catch (error) {
                    console.error('WebSocket连接失败:', error);
                }
            }

            // WebSocket重连
            reconnectWebSocket() {
                if (this.wsReconnectAttempts < this.maxWsReconnectAttempts) {
                    this.wsReconnectAttempts++;
                    const delay = Math.pow(2, this.wsReconnectAttempts) * 1000; // 指数退避

                    setTimeout(() => {
                        console.log(`尝试重连WebSocket (${this.wsReconnectAttempts}/${this.maxWsReconnectAttempts})`);
                        this.setupWebSocket();
                    }, delay);
                } else {
                    this.showNotification('实时数据连接失败，请刷新页面重试', 'error');
                }
            }

            // 处理实时数据
            handleRealtimeData(data) {
                switch (data.type) {
                    case 'stock_price':
                        this.updateStockPrice(data.payload);
                        break;
                    case 'market_index':
                        this.updateMarketIndex(data.payload);
                        break;
                    case 'trading_signal':
                        this.showTradingSignal(data.payload);
                        break;
                    default:
                        console.log('未知的实时数据类型:', data.type);
                }
            }

            // 加载用户偏好设置
            loadUserPreferences() {
                const preferences = localStorage.getItem('stockAnalysisPreferences');
                if (preferences) {
                    try {
                        const prefs = JSON.parse(preferences);
                        this.currentTheme = prefs.theme || 'dark';
                        this.updateInterval = prefs.updateInterval || 3000;
                        this.isRealTimeEnabled = prefs.realTimeEnabled !== false;

                        // 应用主题
                        if (prefs.theme === 'light') {
                            document.body.classList.add('light-theme');
                        }
                    } catch (error) {
                        console.error('加载用户偏好设置失败:', error);
                    }
                }
            }

            // 保存用户偏好设置
            saveUserPreferences() {
                const preferences = {
                    theme: this.currentTheme,
                    updateInterval: this.updateInterval,
                    realTimeEnabled: this.isRealTimeEnabled
                };
                localStorage.setItem('stockAnalysisPreferences', JSON.stringify(preferences));
            }

            // 设置键盘快捷键
            setupKeyboardShortcuts() {
                document.addEventListener('keydown', (e) => {
                    // Cmd/Ctrl + K 打开搜索
                    if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
                        e.preventDefault();
                        const searchInput = document.getElementById('globalSearch');
                        if (searchInput) {
                            searchInput.focus();
                        }
                    }

                    // ESC 关闭模态框
                    if (e.key === 'Escape') {
                        this.closeAllModals();
                    }

                    // 数字键切换标签页
                    if (e.key >= '1' && e.key <= '5' && (e.metaKey || e.ctrlKey)) {
                        e.preventDefault();
                        const tabs = ['dashboard', 'analysis', 'indicators', 'watchlist', 'data'];
                        const tabIndex = parseInt(e.key) - 1;
                        if (tabs[tabIndex]) {
                            this.switchTab(tabs[tabIndex]);
                        }
                    }
                });
            }

            // 关闭所有模态框
            closeAllModals() {
                const modals = document.querySelectorAll('[id$="Modal"]');
                modals.forEach(modal => {
                    modal.classList.add('hidden');
                });
            }

            // 设置事件监听器
            setupEventListeners() {
                // 导航切换
                document.querySelectorAll('.nav-item').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const tab = e.currentTarget.dataset.tab;
                        if (tab) this.switchTab(tab);
                    });
                });

                // 股票选择器
                const stockSelector = document.getElementById('stockSelector');
                if (stockSelector) {
                    stockSelector.addEventListener('change', (e) => {
                        this.selectStock(e.target.value);
                    });
                }

                // 交易股票选择器
                const tradingStockSelector = document.getElementById('tradingStockSelector');
                if (tradingStockSelector) {
                    tradingStockSelector.addEventListener('change', (e) => {
                        this.selectTradingStock(e.target.value);
                    });
                }

                // 全局搜索 - 增强版
                const globalSearch = document.getElementById('globalSearch');
                if (globalSearch) {
                    let searchTimeout;
                    globalSearch.addEventListener('input', (e) => {
                        clearTimeout(searchTimeout);
                        searchTimeout = setTimeout(() => {
                            this.performGlobalSearch(e.target.value);
                        }, 300); // 防抖
                    });

                    globalSearch.addEventListener('focus', () => {
                        this.showSearchSuggestions();
                    });

                    globalSearch.addEventListener('blur', () => {
                        // 延迟隐藏，允许点击建议项
                        setTimeout(() => {
                            this.hideSearchSuggestions();
                        }, 200);
                    });

                    globalSearch.addEventListener('keydown', (e) => {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            this.handleSearchEnter(e.target.value);
                        }
                    });
                }

                // 主图指标切换
                document.querySelectorAll('[data-indicator]').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        this.toggleIndicator(e.target.dataset.indicator);
                    });
                });
            }

            // 加载初始数据
            async loadInitialData() {
                try {
                    // 模拟API调用，实际应用中替换为真实API
                    this.stockData = await this.mockApiCall('/stocks/list', [
                        {
                            code: '000001',
                            name: '平安银行',
                            industry: '银行',
                            market: 'SZ',
                            currentPrice: 12.85,
                            openPrice: 12.90,
                            highPrice: 13.10,
                            lowPrice: 12.80,
                            prevClose: 12.88,
                            changeAmount: -0.03,
                            changePercent: -0.23,
                            volume: 15420000,
                            turnover: 198450000
                        },
                        {
                            code: '000002',
                            name: '万科A',
                            industry: '房地产',
                            market: 'SZ',
                            currentPrice: 18.45,
                            openPrice: 18.20,
                            highPrice: 18.68,
                            lowPrice: 18.15,
                            prevClose: 18.30,
                            changeAmount: 0.15,
                            changePercent: 0.82,
                            volume: 8950000,
                            turnover: 164820000
                        },
                        {
                            code: '600036',
                            name: '招商银行',
                            industry: '银行',
                            market: 'SH',
                            currentPrice: 35.68,
                            openPrice: 35.20,
                            highPrice: 36.15,
                            lowPrice: 35.10,
                            prevClose: 35.45,
                            changeAmount: 0.23,
                            changePercent: 0.65,
                            volume: 12350000,
                            turnover: 441230000
                        },
                        {
                            code: '600519',
                            name: '贵州茅台',
                            industry: '白酒',
                            market: 'SH',
                            currentPrice: 1685.50,
                            openPrice: 1680.00,
                            highPrice: 1695.80,
                            lowPrice: 1675.20,
                            prevClose: 1678.90,
                            changeAmount: 6.60,
                            changePercent: 0.39,
                            volume: 1250000,
                            turnover: 2108750000
                        },
                        {
                            code: '000858',
                            name: '五粮液',
                            industry: '白酒',
                            market: 'SZ',
                            currentPrice: 128.45,
                            openPrice: 129.80,
                            highPrice: 130.20,
                            lowPrice: 127.90,
                            prevClose: 129.60,
                            changeAmount: -1.15,
                            changePercent: -0.89,
                            volume: 3680000,
                            turnover: 473240000
                        }
                    ]);

                    // 初始化自选股
                    this.watchlistData = this.stockData.slice(0, 3);

                    // 初始化模拟持仓数据
                    this.initializePositionsData();

                    // 初始化模拟交易历史
                    this.initializeTradeHistoryData();

                    // 渲染初始界面
                    this.renderDashboard();
                    this.renderWatchlist();
                    this.renderStockDataTable();
                    this.updateStockSelector();
                    this.updateTradingStockSelector();

                } catch (error) {
                    console.error('加载初始数据失败:', error);
                    this.showNotification('数据加载失败，请检查网络连接', 'error');
                }
            }

            // 模拟API调用
            async mockApiCall(endpoint, mockData) {
                // 模拟网络延迟
                await new Promise(resolve => setTimeout(resolve, 100));
                return mockData;
            }

            // 真实API调用（当后端可用时使用）- 增强版
            async apiCall(endpoint, options = {}) {
                const cacheKey = `${endpoint}_${JSON.stringify(options)}`;

                // 检查缓存
                if (this.cache.has(cacheKey)) {
                    const cached = this.cache.get(cacheKey);
                    if (Date.now() - cached.timestamp < this.cacheTimeout) {
                        return cached.data;
                    }
                }

                try {
                    this.showLoading(endpoint);

                    const response = await fetch(`${this.apiBaseUrl}${endpoint}`, {
                        headers: {
                            'Content-Type': 'application/json',
                            ...options.headers
                        },
                        ...options
                    });

                    if (!response.ok) {
                        throw new Error(`API调用失败: ${response.status} ${response.statusText}`);
                    }

                    const data = await response.json();

                    // 缓存结果
                    this.cache.set(cacheKey, {
                        data,
                        timestamp: Date.now()
                    });

                    this.retryCount = 0;
                    return data;
                } catch (error) {
                    console.error('API调用错误:', error);

                    if (this.retryCount < this.maxRetries) {
                        this.retryCount++;
                        await this.delay(1000 * this.retryCount);
                        return this.apiCall(endpoint, options);
                    }

                    this.showError(`数据加载失败: ${error.message}`);
                    throw error;
                } finally {
                    this.hideLoading(endpoint);
                }
            }

            // 工具函数
            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }

            showLoading(key) {
                this.loadingStates.add(key);
                const loadingElements = document.querySelectorAll(`[data-loading="${key}"]`);
                loadingElements.forEach(el => {
                    el.classList.add('loading-shimmer');
                });
            }

            hideLoading(key) {
                this.loadingStates.delete(key);
                const loadingElements = document.querySelectorAll(`[data-loading="${key}"]`);
                loadingElements.forEach(el => {
                    el.classList.remove('loading-shimmer');
                });
            }

            showError(message) {
                this.showNotification(message, 'error');
            }

            showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-xl z-50 transition-all duration-300 ${
                    type === 'error' ? 'bg-red-500' :
                    type === 'success' ? 'bg-green-500' :
                    'bg-blue-500'
                } text-white`;
                notification.innerHTML = `
                    <div class="flex items-center space-x-2">
                        <i class="fas ${
                            type === 'error' ? 'fa-exclamation-circle' :
                            type === 'success' ? 'fa-check-circle' :
                            'fa-info-circle'
                        }"></i>
                        <span>${message}</span>
                        <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-white hover:text-gray-200">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;

                document.body.appendChild(notification);

                // 动画进入
                setTimeout(() => {
                    notification.style.transform = 'translateX(0)';
                }, 10);

                // 自动移除
                setTimeout(() => {
                    notification.style.transform = 'translateX(100%)';
                    setTimeout(() => notification.remove(), 300);
                }, 5000);
            }

            // 获取股票列表
            async getStockList(params = {}) {
                try {
                    const queryParams = new URLSearchParams(params).toString();
                    const endpoint = `/stocks/list${queryParams ? '?' + queryParams : ''}`;
                    return await this.apiCall(endpoint);
                } catch (error) {
                    console.error('获取股票列表失败:', error);
                    // 返回模拟数据作为后备
                    return this.stockData;
                }
            }

            // 获取股票详情
            async getStockDetail(stockCode) {
                try {
                    return await this.apiCall(`/stocks/list/${stockCode}`);
                } catch (error) {
                    console.error('获取股票详情失败:', error);
                    return this.stockData.find(stock => stock.code === stockCode);
                }
            }

            // 获取股票K线数据
            async getStockKlineData(stockCode, params = {}) {
                try {
                    const queryParams = new URLSearchParams(params).toString();
                    const endpoint = `/stocks/data/${stockCode}${queryParams ? '?' + queryParams : ''}`;
                    return await this.apiCall(endpoint);
                } catch (error) {
                    console.error('获取K线数据失败:', error);
                    return await this.generateMockKlineData(stockCode);
                }
            }

            // 获取技术指标数据
            async getIndicatorData(stockCode, indicatorType, params = {}) {
                try {
                    const queryParams = new URLSearchParams(params).toString();
                    const endpoint = `/indicators/${indicatorType}/${stockCode}${queryParams ? '?' + queryParams : ''}`;
                    return await this.apiCall(endpoint);
                } catch (error) {
                    console.error('获取技术指标数据失败:', error);
                    // 返回模拟数据
                    switch (indicatorType) {
                        case 'macd':
                            return await this.generateMockMacdData();
                        case 'kdj':
                            return await this.generateMockKdjData();
                        case 'rsi':
                            return await this.generateMockRsiData();
                        default:
                            return null;
                    }
                }
            }

            // 全局搜索功能 - 增强版
            async performGlobalSearch(query) {
                if (!query || query.length < 2) {
                    this.hideSearchSuggestions();
                    return;
                }

                try {
                    // 搜索股票
                    const results = this.stockData.filter(stock =>
                        stock.code.toLowerCase().includes(query.toLowerCase()) ||
                        stock.name.toLowerCase().includes(query.toLowerCase())
                    ).slice(0, 8);

                    this.showSearchResults(results, query);
                } catch (error) {
                    console.error('搜索失败:', error);
                }
            }

            showSearchSuggestions() {
                const suggestions = document.getElementById('searchSuggestions');
                if (suggestions) {
                    suggestions.classList.remove('hidden');
                }
            }

            hideSearchSuggestions() {
                const suggestions = document.getElementById('searchSuggestions');
                if (suggestions) {
                    suggestions.classList.add('hidden');
                }
            }

            showSearchResults(results, query) {
                const suggestions = document.getElementById('searchSuggestions');
                if (!suggestions) return;

                if (results.length === 0) {
                    suggestions.innerHTML = `
                        <div class="p-4 text-center text-gray-400">
                            <i class="fas fa-search mb-2"></i>
                            <div>未找到匹配的股票</div>
                        </div>
                    `;
                } else {
                    suggestions.innerHTML = `
                        <div class="p-2">
                            <div class="text-xs text-gray-400 mb-2">搜索结果</div>
                            <div class="space-y-1">
                                ${results.map(stock => `
                                    <div class="flex items-center justify-between p-2 hover:bg-gray-700 rounded cursor-pointer"
                                         onclick="app.selectStockFromSearch('${stock.code}')">
                                        <div class="flex items-center space-x-3">
                                            <i class="fas fa-chart-line text-blue-400"></i>
                                            <div>
                                                <div class="text-sm font-medium">${this.highlightText(stock.code, query)} ${this.highlightText(stock.name, query)}</div>
                                                <div class="text-xs text-gray-400">${stock.industry}</div>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-sm mono-font">${this.formatNumber(stock.currentPrice)}</div>
                                            <div class="text-xs ${this.getPriceClass(stock.changePercent)}">
                                                ${stock.changePercent >= 0 ? '+' : ''}${this.formatNumber(stock.changePercent)}%
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    `;
                }

                suggestions.classList.remove('hidden');
            }

            highlightText(text, query) {
                if (!query) return text;
                const regex = new RegExp(`(${query})`, 'gi');
                return text.replace(regex, '<mark class="bg-blue-500 bg-opacity-30 text-blue-300">$1</mark>');
            }

            handleSearchEnter(query) {
                const results = this.stockData.filter(stock =>
                    stock.code.toLowerCase().includes(query.toLowerCase()) ||
                    stock.name.toLowerCase().includes(query.toLowerCase())
                );

                if (results.length > 0) {
                    this.selectStockFromSearch(results[0].code);
                }
            }

            selectStockFromSearch(stockCode) {
                this.hideSearchSuggestions();
                this.switchTab('analysis');
                const stockSelector = document.getElementById('stockSelector');
                if (stockSelector) {
                    stockSelector.value = stockCode;
                    this.selectStock(stockCode);
                }

                // 清空搜索框
                const searchInput = document.getElementById('globalSearch');
                if (searchInput) {
                    searchInput.value = '';
                }
            }

            showSearchResults(results, query) {
                const suggestions = document.getElementById('searchSuggestions');
                if (!suggestions) return;

                if (results.length === 0) {
                    suggestions.innerHTML = `
                        <div class="p-4 text-center text-gray-400">
                            <i class="fas fa-search mb-2"></i>
                            <div>未找到匹配的股票</div>
                        </div>
                    `;
                } else {
                    suggestions.innerHTML = `
                        <div class="p-2">
                            <div class="text-xs text-gray-400 mb-2">搜索结果</div>
                            <div class="space-y-1">
                                ${results.map(stock => `
                                    <div class="flex items-center justify-between p-2 hover:bg-gray-700 rounded cursor-pointer"
                                         onclick="app.selectStockFromSearch('${stock.code}')">
                                        <div class="flex items-center space-x-3">
                                            <i class="fas fa-chart-line text-blue-400"></i>
                                            <div>
                                                <div class="text-sm font-medium">${this.highlightText(stock.code, query)} ${this.highlightText(stock.name, query)}</div>
                                                <div class="text-xs text-gray-400">${stock.industry}</div>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-sm mono-font">${this.formatNumber(stock.currentPrice)}</div>
                                            <div class="text-xs ${this.getPriceClass(stock.changePercent)}">
                                                ${stock.changePercent >= 0 ? '+' : ''}${this.formatNumber(stock.changePercent)}%
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    `;
                }

                suggestions.classList.remove('hidden');
            }

            highlightText(text, query) {
                if (!query) return text;
                const regex = new RegExp(`(${query})`, 'gi');
                return text.replace(regex, '<mark class="bg-blue-500 bg-opacity-30 text-blue-300">$1</mark>');
            }

            handleSearchEnter(query) {
                const results = this.stockData.filter(stock =>
                    stock.code.toLowerCase().includes(query.toLowerCase()) ||
                    stock.name.toLowerCase().includes(query.toLowerCase())
                );

                if (results.length > 0) {
                    this.selectStockFromSearch(results[0].code);
                }
            }

            selectStockFromSearch(stockCode) {
                this.hideSearchSuggestions();
                this.switchTab('analysis');
                const stockSelector = document.getElementById('stockSelector');
                if (stockSelector) {
                    stockSelector.value = stockCode;
                    this.selectStock(stockCode);
                }

                // 清空搜索框
                const searchInput = document.getElementById('globalSearch');
                if (searchInput) {
                    searchInput.value = '';
                }
            }

            // 切换标签页 - 增强版
            switchTab(tabName) {
                // 更新导航状态
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });
                const activeTab = document.querySelector(`[data-tab="${tabName}"]`);
                if (activeTab) {
                    activeTab.classList.add('active');
                }

                // 显示对应内容
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.add('hidden');
                });
                const targetContent = document.getElementById(tabName);
                if (targetContent) {
                    targetContent.classList.remove('hidden');
                    targetContent.classList.add('fade-in');
                }

                this.currentTab = tabName;

                // 根据标签页加载相应数据
                switch(tabName) {
                    case 'dashboard':
                        this.renderDashboard();
                        break;
                    case 'analysis':
                        this.initializeCharts();
                        break;
                    case 'indicators':
                        this.loadIndicatorCharts();
                        break;
                    case 'watchlist':
                        this.renderWatchlist();
                        break;
                    case 'trading':
                        this.renderTradingInterface();
                        break;
                    case 'positions':
                        this.renderPositions();
                        break;
                    case 'data':
                        this.renderStockDataTable();
                        break;
                    case 'settings':
                        this.loadSettings();
                        break;
                }
            }

            // 渲染仪表板
            renderDashboard() {
                this.renderHotStocks();
                this.renderIndicatorOverview();
                this.renderMarketTrendChart();
                this.updateMarketIndices();
            }

            // 渲染热门股票
            renderHotStocks() {
                const container = document.getElementById('hotStocks');
                if (!container) return;

                const hotStocks = this.stockData
                    .sort((a, b) => Math.abs(b.changePercent) - Math.abs(a.changePercent))
                    .slice(0, 5);

                container.innerHTML = hotStocks.map(stock => `
                    <div class="flex items-center justify-between p-3 hover:bg-gray-700 rounded-lg cursor-pointer"
                         onclick="app.selectStockFromDashboard('${stock.code}')">
                        <div>
                            <div class="font-medium">${stock.code} ${stock.name}</div>
                            <div class="text-sm text-gray-400">${stock.industry}</div>
                        </div>
                        <div class="text-right">
                            <div class="mono-font">${this.formatNumber(stock.currentPrice)}</div>
                            <div class="text-sm ${this.getPriceClass(stock.changePercent)}">
                                ${stock.changePercent >= 0 ? '+' : ''}${this.formatNumber(stock.changePercent)}%
                            </div>
                        </div>
                    </div>
                `).join('');
            }

            // 渲染技术指标概览
            renderIndicatorOverview() {
                const container = document.getElementById('indicatorOverview');
                if (!container) return;

                const indicators = [
                    { name: 'MACD', status: 'bullish', signal: '金叉' },
                    { name: 'KDJ', status: 'bearish', signal: '死叉' },
                    { name: 'RSI', status: 'neutral', signal: '中性' },
                    { name: 'BOLL', status: 'bullish', signal: '突破上轨' }
                ];

                container.innerHTML = indicators.map(indicator => `
                    <div class="flex items-center justify-between">
                        <span class="text-sm">${indicator.name}</span>
                        <div class="flex items-center space-x-2">
                            <span class="text-xs ${
                                indicator.status === 'bullish' ? 'text-green-400' :
                                indicator.status === 'bearish' ? 'text-red-400' : 'text-yellow-400'
                            }">${indicator.signal}</span>
                            <div class="w-2 h-2 rounded-full ${
                                indicator.status === 'bullish' ? 'bg-green-400' :
                                indicator.status === 'bearish' ? 'bg-red-400' : 'bg-yellow-400'
                            }"></div>
                        </div>
                    </div>
                `).join('');
            }

            // 渲染市场趋势图表
            renderMarketTrendChart() {
                const container = document.getElementById('marketTrendChart');
                if (!container) return;

                // 生成模拟的市场趋势数据
                const dates = [];
                const values = [];
                const baseValue = 3200;

                for (let i = 30; i >= 0; i--) {
                    const date = new Date();
                    date.setDate(date.getDate() - i);
                    dates.push(date.toISOString().split('T')[0]);

                    const randomChange = (Math.random() - 0.5) * 100;
                    const value = baseValue + randomChange + (Math.random() * 200 - 100);
                    values.push(value.toFixed(2));
                }

                const chart = echarts.init(container);
                const option = {
                    backgroundColor: 'transparent',
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: dates,
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8' }
                    },
                    yAxis: {
                        type: 'value',
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8' },
                        splitLine: { lineStyle: { color: '#334155' } }
                    },
                    series: [{
                        data: values,
                        type: 'line',
                        smooth: true,
                        lineStyle: { color: '#3b82f6', width: 2 },
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0, y: 0, x2: 0, y2: 1,
                                colorStops: [
                                    { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },
                                    { offset: 1, color: 'rgba(59, 130, 246, 0.05)' }
                                ]
                            }
                        }
                    }],
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: '#1e293b',
                        borderColor: '#475569',
                        textStyle: { color: '#f8fafc' }
                    }
                };

                chart.setOption(option);
                this.charts.marketTrend = chart;
            }

            // 更新市场指数
            updateMarketIndices() {
                const indices = ['shIndex', 'szIndex', 'cybIndex'];
                indices.forEach(index => {
                    const element = document.getElementById(index);
                    if (element) {
                        // 模拟价格波动
                        const currentValue = parseFloat(element.textContent.replace(',', ''));
                        const change = (Math.random() - 0.5) * 20;
                        const newValue = currentValue + change;
                        element.textContent = this.formatNumber(newValue);
                    }
                });
            }

            // 选择股票（从仪表板）
            selectStockFromDashboard(stockCode) {
                this.switchTab('analysis');
                const stockSelector = document.getElementById('stockSelector');
                if (stockSelector) {
                    stockSelector.value = stockCode;
                    this.selectStock(stockCode);
                }
            }

            // 选择股票进行分析
            async selectStock(stockCode) {
                if (!stockCode) {
                    this.selectedStock = null;
                    document.getElementById('stockInfo').style.display = 'none';
                    return;
                }

                this.selectedStock = this.stockData.find(stock => stock.code === stockCode);
                if (this.selectedStock) {
                    this.renderStockInfo();
                    await this.loadStockAnalysis();
                }
            }

            // 渲染股票基本信息
            renderStockInfo() {
                const container = document.getElementById('stockBasicInfo');
                const infoPanel = document.getElementById('stockInfo');

                if (!container || !this.selectedStock) return;

                const stock = this.selectedStock;
                const changeClass = this.getPriceClass(stock.changePercent);

                container.innerHTML = `
                    <div class="text-lg font-semibold mb-2">${stock.name} (${stock.code})</div>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-400">现价</span>
                            <span class="mono-font ${changeClass}">${this.formatNumber(stock.currentPrice)}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">涨跌幅</span>
                            <span class="mono-font ${changeClass}">
                                ${stock.changePercent >= 0 ? '+' : ''}${this.formatNumber(stock.changePercent)}%
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">成交量</span>
                            <span class="mono-font">${this.formatVolume(stock.volume)}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">行业</span>
                            <span>${stock.industry}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">市场</span>
                            <span>${stock.market}</span>
                        </div>
                    </div>
                `;

                infoPanel.style.display = 'block';
            }

            // 加载股票分析数据
            async loadStockAnalysis() {
                if (!this.selectedStock) return;

                try {
                    // 模拟加载K线数据
                    const klineData = await this.generateMockKlineData(this.selectedStock.code);
                    this.renderKlineChart(klineData);

                    // 加载技术指标
                    const macdData = await this.generateMockMacdData();
                    this.renderMacdChart(macdData);

                    const volumeData = await this.generateMockVolumeData();
                    this.renderVolumeChart(volumeData);

                } catch (error) {
                    console.error('加载股票分析数据失败:', error);
                    this.showNotification('分析数据加载失败', 'error');
                }
            }

            // 生成模拟K线数据
            async generateMockKlineData(stockCode) {
                const data = [];
                const basePrice = this.selectedStock.currentPrice;
                let currentPrice = basePrice;

                for (let i = 120; i >= 0; i--) {
                    const date = new Date();
                    date.setDate(date.getDate() - i);

                    const open = currentPrice;
                    const change = (Math.random() - 0.5) * basePrice * 0.05;
                    const close = Math.max(0.01, open + change);
                    const high = Math.max(open, close) * (1 + Math.random() * 0.03);
                    const low = Math.min(open, close) * (1 - Math.random() * 0.03);
                    const volume = Math.floor(Math.random() * 10000000) + 1000000;

                    data.push([
                        date.toISOString().split('T')[0],
                        open.toFixed(2),
                        close.toFixed(2),
                        low.toFixed(2),
                        high.toFixed(2),
                        volume
                    ]);

                    currentPrice = close;
                }

                return data;
            }

            // 渲染K线图表
            renderKlineChart(data) {
                const container = document.getElementById('klineChart');
                if (!container) return;

                const chart = echarts.init(container);
                const option = {
                    backgroundColor: 'transparent',
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: data.map(item => item[0]),
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8' }
                    },
                    yAxis: {
                        type: 'value',
                        scale: true,
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8' },
                        splitLine: { lineStyle: { color: '#334155' } }
                    },
                    series: [{
                        type: 'candlestick',
                        data: data.map(item => [item[1], item[2], item[3], item[4]]),
                        itemStyle: {
                            color: '#10b981',
                            color0: '#ef4444',
                            borderColor: '#10b981',
                            borderColor0: '#ef4444'
                        }
                    }],
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: '#1e293b',
                        borderColor: '#475569',
                        textStyle: { color: '#f8fafc' },
                        formatter: function(params) {
                            const data = params[0].data;
                            return `
                                日期: ${params[0].axisValue}<br/>
                                开盘: ${data[0]}<br/>
                                收盘: ${data[1]}<br/>
                                最低: ${data[2]}<br/>
                                最高: ${data[3]}
                            `;
                        }
                    },
                    dataZoom: [{
                        type: 'inside',
                        start: 70,
                        end: 100
                    }]
                };

                chart.setOption(option);
                this.charts.kline = chart;
            }

            // 生成模拟MACD数据
            async generateMockMacdData() {
                const data = [];
                for (let i = 0; i < 120; i++) {
                    const date = new Date();
                    date.setDate(date.getDate() - (120 - i));

                    data.push({
                        date: date.toISOString().split('T')[0],
                        dif: (Math.random() - 0.5) * 2,
                        dea: (Math.random() - 0.5) * 1.5,
                        macd: (Math.random() - 0.5) * 0.5
                    });
                }
                return data;
            }

            // 渲染MACD图表
            renderMacdChart(data) {
                const container = document.getElementById('macdChart');
                if (!container) return;

                const chart = echarts.init(container);
                const option = {
                    backgroundColor: 'transparent',
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: data.map(item => item.date),
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 }
                    },
                    yAxis: {
                        type: 'value',
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 },
                        splitLine: { lineStyle: { color: '#334155' } }
                    },
                    series: [
                        {
                            name: 'DIF',
                            type: 'line',
                            data: data.map(item => item.dif),
                            lineStyle: { color: '#3b82f6', width: 1 },
                            symbol: 'none'
                        },
                        {
                            name: 'DEA',
                            type: 'line',
                            data: data.map(item => item.dea),
                            lineStyle: { color: '#f59e0b', width: 1 },
                            symbol: 'none'
                        },
                        {
                            name: 'MACD',
                            type: 'bar',
                            data: data.map(item => item.macd),
                            itemStyle: {
                                color: function(params) {
                                    return params.value >= 0 ? '#10b981' : '#ef4444';
                                }
                            }
                        }
                    ],
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: '#1e293b',
                        borderColor: '#475569',
                        textStyle: { color: '#f8fafc' }
                    },
                    legend: {
                        textStyle: { color: '#94a3b8' },
                        top: 10
                    }
                };

                chart.setOption(option);
                this.charts.macd = chart;
            }

            // 生成模拟成交量数据
            async generateMockVolumeData() {
                const data = [];
                for (let i = 0; i < 120; i++) {
                    const date = new Date();
                    date.setDate(date.getDate() - (120 - i));

                    data.push({
                        date: date.toISOString().split('T')[0],
                        volume: Math.floor(Math.random() * 10000000) + 1000000,
                        price: this.selectedStock.currentPrice + (Math.random() - 0.5) * 5
                    });
                }
                return data;
            }

            // 渲染成交量图表
            renderVolumeChart(data) {
                const container = document.getElementById('volumeChart');
                if (!container) return;

                const chart = echarts.init(container);
                const option = {
                    backgroundColor: 'transparent',
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: data.map(item => item.date),
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 }
                    },
                    yAxis: {
                        type: 'value',
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 },
                        splitLine: { lineStyle: { color: '#334155' } }
                    },
                    series: [{
                        type: 'bar',
                        data: data.map(item => item.volume),
                        itemStyle: {
                            color: function(params) {
                                return params.dataIndex % 2 === 0 ? '#10b981' : '#ef4444';
                            }
                        }
                    }],
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: '#1e293b',
                        borderColor: '#475569',
                        textStyle: { color: '#f8fafc' },
                        formatter: function(params) {
                            const data = params[0];
                            return `
                                日期: ${data.axisValue}<br/>
                                成交量: ${(data.value / 10000).toFixed(0)}万
                            `;
                        }
                    }
                };

                chart.setOption(option);
                this.charts.volume = chart;
            }

            // 渲染自选股列表
            renderWatchlist() {
                const tbody = document.getElementById('watchlistTable');
                if (!tbody) return;

                tbody.innerHTML = this.watchlistData.map((stock, index) => {
                    const changeClass = this.getPriceClass(stock.changePercent);
                    const changePrefix = stock.changePercent >= 0 ? '+' : '';

                    return `
                        <tr class="hover:bg-gray-700 draggable"
                            data-selectable="true"
                            data-selectable-id="${stock.code}"
                            data-stock-code="${stock.code}"
                            data-context-menu="stock-item"
                            data-preview="stock"
                            data-drag-data='{"type":"stock","code":"${stock.code}","index":${index}}'>
                            <td style="width: 40px;">
                                <div class="selection-checkbox" style="display: none;"></div>
                                <i class="fas fa-grip-vertical text-gray-500 drag-handle" style="cursor: move; margin-left: 8px;"></i>
                            </td>
                            <td class="mono-font">${stock.code}</td>
                            <td>${stock.name}</td>
                            <td class="mono-font ${changeClass}">${this.formatNumber(stock.currentPrice)}</td>
                            <td class="mono-font ${changeClass}">${changePrefix}${this.formatNumber(stock.changePercent)}%</td>
                            <td class="mono-font">${this.formatVolume(stock.volume)}</td>
                            <td>
                                <div class="flex items-center gap-2">
                                    <button class="text-green-400 hover:text-green-300 tooltip"
                                            data-tooltip="交易"
                                            onclick="app.tradeStock('${stock.code}')">
                                        <i class="fas fa-exchange-alt"></i>
                                    </button>
                                    <button class="text-blue-400 hover:text-blue-300 tooltip"
                                            data-tooltip="查看详情"
                                            onclick="app.analyzeStock('${stock.code}')">
                                        <i class="fas fa-chart-line"></i>
                                    </button>
                                    <button class="text-red-400 hover:text-red-300 tooltip"
                                            data-tooltip="移除"
                                            onclick="app.removeFromWatchlist('${stock.code}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `;
                }).join('');

                // 为新添加的元素设置拖拽功能
                tbody.querySelectorAll('.draggable').forEach(row => {
                    try {
                        const dragData = JSON.parse(row.dataset.dragData);
                        if (this.dragManager && this.dragManager.makeDraggable) {
                            this.dragManager.makeDraggable(row, dragData);
                        }
                    } catch (error) {
                        console.error('设置拖拽功能失败:', error);
                    }
                });
            }

            // 渲染股票数据表格
            renderStockDataTable() {
                const tbody = document.getElementById('stockDataTable');
                if (!tbody) return;

                tbody.innerHTML = this.stockData.slice(0, 20).map(stock => {
                    const changeClass = this.getPriceClass(stock.changePercent);
                    const changePrefix = stock.changePercent >= 0 ? '+' : '';

                    return `
                        <tr class="hover:bg-gray-700">
                            <td class="mono-font">${stock.code}</td>
                            <td>${stock.name}</td>
                            <td>${stock.industry}</td>
                            <td>${stock.market}</td>
                            <td class="mono-font ${changeClass}">${this.formatNumber(stock.currentPrice)}</td>
                            <td class="mono-font ${changeClass}">${changePrefix}${this.formatNumber(stock.changePercent)}%</td>
                            <td class="mono-font">${this.formatVolume(stock.volume)}</td>
                            <td>
                                <button class="text-blue-400 hover:text-blue-300 mr-2" onclick="app.addToWatchlist('${stock.code}')">
                                    <i class="fas fa-star"></i>
                                </button>
                                <button class="text-green-400 hover:text-green-300" onclick="app.analyzeStock('${stock.code}')">
                                    <i class="fas fa-chart-line"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                }).join('');
            }

            // 更新股票选择器
            updateStockSelector() {
                const selector = document.getElementById('stockSelector');
                if (!selector) return;

                selector.innerHTML = '<option value="">选择股票</option>' +
                    this.stockData.map(stock =>
                        `<option value="${stock.code}">${stock.code} ${stock.name}</option>`
                    ).join('');
            }

            // 分析股票
            analyzeStock(stockCode) {
                this.switchTab('analysis');
                const stockSelector = document.getElementById('stockSelector');
                if (stockSelector) {
                    stockSelector.value = stockCode;
                    this.selectStock(stockCode);
                }
            }

            // 添加到自选股
            addToWatchlist(stockCode) {
                const stock = this.stockData.find(s => s.code === stockCode);
                if (stock && !this.watchlistData.find(w => w.code === stockCode)) {
                    this.watchlistData.push(stock);
                    this.renderWatchlist();
                    this.showNotification('已添加到自选股', 'success');
                } else {
                    this.showNotification('股票已在自选股中', 'warning');
                }
            }

            // 从自选股移除
            removeFromWatchlist(stockCode) {
                this.watchlistData = this.watchlistData.filter(stock => stock.code !== stockCode);
                this.renderWatchlist();
                this.showNotification('已从自选股移除', 'success');
            }

            // 初始化图表
            initializeCharts() {
                // 确保图表容器存在后再初始化
                setTimeout(() => {
                    if (this.selectedStock) {
                        this.loadStockAnalysis();
                    }
                }, 100);
            }

            // 加载指标图表
            loadIndicatorCharts() {
                // 加载各种技术指标图表
                this.renderIndicatorMacd();
                this.renderIndicatorKdj();
                this.renderIndicatorRsi();
                this.renderIndicatorBollinger();
            }

            // 渲染MACD指标图表
            renderIndicatorMacd() {
                const container = document.getElementById('indicatorMacdChart');
                if (!container) return;

                // 生成模拟MACD数据
                const data = [];
                for (let i = 0; i < 60; i++) {
                    data.push({
                        date: new Date(Date.now() - (60 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                        dif: (Math.random() - 0.5) * 2,
                        dea: (Math.random() - 0.5) * 1.5,
                        macd: (Math.random() - 0.5) * 0.5
                    });
                }

                const chart = echarts.init(container);
                const option = {
                    backgroundColor: 'transparent',
                    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
                    xAxis: {
                        type: 'category',
                        data: data.map(item => item.date),
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 }
                    },
                    yAxis: {
                        type: 'value',
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 },
                        splitLine: { lineStyle: { color: '#334155' } }
                    },
                    series: [
                        {
                            name: 'DIF',
                            type: 'line',
                            data: data.map(item => item.dif),
                            lineStyle: { color: '#3b82f6', width: 1 },
                            symbol: 'none'
                        },
                        {
                            name: 'DEA',
                            type: 'line',
                            data: data.map(item => item.dea),
                            lineStyle: { color: '#f59e0b', width: 1 },
                            symbol: 'none'
                        },
                        {
                            name: 'MACD',
                            type: 'bar',
                            data: data.map(item => item.macd),
                            itemStyle: {
                                color: function(params) {
                                    return params.value >= 0 ? '#10b981' : '#ef4444';
                                }
                            }
                        }
                    ],
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: '#1e293b',
                        borderColor: '#475569',
                        textStyle: { color: '#f8fafc' }
                    },
                    legend: {
                        textStyle: { color: '#94a3b8' },
                        top: 10
                    }
                };

                chart.setOption(option);
                this.charts.indicatorMacd = chart;
            }

            // 渲染KDJ指标图表
            renderIndicatorKdj() {
                const container = document.getElementById('indicatorKdjChart');
                if (!container) return;

                const data = [];
                for (let i = 0; i < 60; i++) {
                    data.push({
                        date: new Date(Date.now() - (60 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                        k: Math.random() * 100,
                        d: Math.random() * 100,
                        j: Math.random() * 100
                    });
                }

                const chart = echarts.init(container);
                const option = {
                    backgroundColor: 'transparent',
                    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
                    xAxis: {
                        type: 'category',
                        data: data.map(item => item.date),
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 }
                    },
                    yAxis: {
                        type: 'value',
                        min: 0,
                        max: 100,
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 },
                        splitLine: { lineStyle: { color: '#334155' } }
                    },
                    series: [
                        {
                            name: 'K',
                            type: 'line',
                            data: data.map(item => item.k),
                            lineStyle: { color: '#3b82f6', width: 1 },
                            symbol: 'none'
                        },
                        {
                            name: 'D',
                            type: 'line',
                            data: data.map(item => item.d),
                            lineStyle: { color: '#f59e0b', width: 1 },
                            symbol: 'none'
                        },
                        {
                            name: 'J',
                            type: 'line',
                            data: data.map(item => item.j),
                            lineStyle: { color: '#8b5cf6', width: 1 },
                            symbol: 'none'
                        }
                    ],
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: '#1e293b',
                        borderColor: '#475569',
                        textStyle: { color: '#f8fafc' }
                    },
                    legend: {
                        textStyle: { color: '#94a3b8' },
                        top: 10
                    }
                };

                chart.setOption(option);
                this.charts.indicatorKdj = chart;
            }

            // 渲染RSI指标图表
            renderIndicatorRsi() {
                const container = document.getElementById('indicatorRsiChart');
                if (!container) return;

                const data = [];
                for (let i = 0; i < 60; i++) {
                    data.push({
                        date: new Date(Date.now() - (60 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                        rsi: Math.random() * 100
                    });
                }

                const chart = echarts.init(container);
                const option = {
                    backgroundColor: 'transparent',
                    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
                    xAxis: {
                        type: 'category',
                        data: data.map(item => item.date),
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 }
                    },
                    yAxis: {
                        type: 'value',
                        min: 0,
                        max: 100,
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 },
                        splitLine: { lineStyle: { color: '#334155' } }
                    },
                    series: [{
                        name: 'RSI',
                        type: 'line',
                        data: data.map(item => item.rsi),
                        lineStyle: { color: '#8b5cf6', width: 2 },
                        symbol: 'none',
                        markLine: {
                            data: [
                                { yAxis: 70, lineStyle: { color: '#ef4444', type: 'dashed' } },
                                { yAxis: 30, lineStyle: { color: '#10b981', type: 'dashed' } }
                            ]
                        }
                    }],
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: '#1e293b',
                        borderColor: '#475569',
                        textStyle: { color: '#f8fafc' }
                    }
                };

                chart.setOption(option);
                this.charts.indicatorRsi = chart;
            }

            // 渲染Bollinger Bands指标图表
            renderIndicatorBollinger() {
                const container = document.getElementById('indicatorBollingerChart');
                if (!container) return;

                const data = [];
                const basePrice = 100;
                for (let i = 0; i < 60; i++) {
                    const price = basePrice + (Math.random() - 0.5) * 20;
                    data.push({
                        date: new Date(Date.now() - (60 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                        price: price,
                        upper: price + Math.random() * 5 + 2,
                        lower: price - Math.random() * 5 - 2,
                        middle: price + (Math.random() - 0.5) * 2
                    });
                }

                const chart = echarts.init(container);
                const option = {
                    backgroundColor: 'transparent',
                    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
                    xAxis: {
                        type: 'category',
                        data: data.map(item => item.date),
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 }
                    },
                    yAxis: {
                        type: 'value',
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 },
                        splitLine: { lineStyle: { color: '#334155' } }
                    },
                    series: [
                        {
                            name: '上轨',
                            type: 'line',
                            data: data.map(item => item.upper),
                            lineStyle: { color: '#ef4444', width: 1 },
                            symbol: 'none'
                        },
                        {
                            name: '中轨',
                            type: 'line',
                            data: data.map(item => item.middle),
                            lineStyle: { color: '#3b82f6', width: 1 },
                            symbol: 'none'
                        },
                        {
                            name: '下轨',
                            type: 'line',
                            data: data.map(item => item.lower),
                            lineStyle: { color: '#10b981', width: 1 },
                            symbol: 'none'
                        },
                        {
                            name: '价格',
                            type: 'line',
                            data: data.map(item => item.price),
                            lineStyle: { color: '#f59e0b', width: 2 },
                            symbol: 'none'
                        }
                    ],
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: '#1e293b',
                        borderColor: '#475569',
                        textStyle: { color: '#f8fafc' }
                    },
                    legend: {
                        textStyle: { color: '#94a3b8' },
                        top: 10
                    }
                };

                chart.setOption(option);
                this.charts.indicatorBollinger = chart;
            }

            // 全局搜索功能
            performGlobalSearch(query) {
                if (!query.trim()) return;

                const results = this.stockData.filter(stock =>
                    stock.code.toLowerCase().includes(query.toLowerCase()) ||
                    stock.name.toLowerCase().includes(query.toLowerCase())
                );

                if (results.length > 0) {
                    // 显示搜索结果（这里可以实现一个下拉搜索结果）
                    console.log('搜索结果:', results);
                }
            }

            // 切换指标显示
            toggleIndicator(indicator) {
                // 更新按钮状态
                document.querySelectorAll('[data-indicator]').forEach(btn => {
                    btn.classList.remove('bg-blue-600', 'text-white');
                    btn.classList.add('text-gray-400');
                });

                const activeBtn = document.querySelector(`[data-indicator="${indicator}"]`);
                if (activeBtn) {
                    activeBtn.classList.add('bg-blue-600', 'text-white');
                    activeBtn.classList.remove('text-gray-400');
                }

                // 重新渲染K线图（这里可以添加不同的指标叠加）
                if (this.selectedStock) {
                    this.loadStockAnalysis();
                }
            }

            // 实时数据更新
            startRealTimeUpdates() {
                if (!this.isRealTimeEnabled) return;

                setInterval(() => {
                    this.updateRealtimeData();
                }, this.updateInterval);
            }

            // 更新实时数据
            updateRealtimeData() {
                // 模拟股价波动
                this.stockData.forEach(stock => {
                    const changeRate = (Math.random() - 0.5) * 0.02; // ±1%的随机波动
                    const newPrice = stock.currentPrice * (1 + changeRate);

                    stock.currentPrice = Math.max(0.01, newPrice);
                    stock.changeAmount = stock.currentPrice - stock.prevClose;
                    stock.changePercent = (stock.changeAmount / stock.prevClose) * 100;

                    // 更新成交量（模拟增长）
                    stock.volume += Math.floor(Math.random() * 100000);
                    stock.turnover = stock.volume * stock.currentPrice;
                });

                // 更新自选股数据
                this.watchlistData.forEach(stock => {
                    const updatedStock = this.stockData.find(s => s.code === stock.code);
                    if (updatedStock) {
                        Object.assign(stock, updatedStock);
                    }
                });

                // 更新持仓价格
                this.updatePositionPrices();

                // 重新渲染当前显示的数据
                if (this.currentTab === 'dashboard') {
                    this.renderDashboard();
                } else if (this.currentTab === 'watchlist') {
                    this.renderWatchlist();
                } else if (this.currentTab === 'trading') {
                    this.updateAccountInfo();
                    if (this.selectedTradingStock) {
                        this.updateSelectedStockInfo();
                        this.calculateTradeAmount();
                    }
                } else if (this.currentTab === 'positions') {
                    this.renderPositions();
                } else if (this.currentTab === 'data') {
                    this.renderStockDataTable();
                }
            }

            // 更新当前时间
            updateCurrentTime() {
                const now = new Date();
                const timeString = now.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
                const timeElement = document.getElementById('currentTime');
                if (timeElement) {
                    timeElement.textContent = timeString;
                }

                // 更新最后更新时间
                const lastUpdateElement = document.getElementById('lastUpdate');
                if (lastUpdateElement) {
                    lastUpdateElement.textContent = now.toLocaleTimeString();
                }
            }

            // 切换多选模式
            toggleMultiSelectMode() {
                if (this.multiSelectManager) {
                    if (this.multiSelectManager.isSelectionMode) {
                        this.multiSelectManager.exitSelectionMode();
                    } else {
                        this.multiSelectManager.enterSelectionMode();
                    }
                }
            }

            // 刷新所有图表
            refreshAllCharts() {
                Object.values(this.charts).forEach(chart => {
                    if (chart && chart.resize) {
                        try {
                            chart.resize();
                        } catch (error) {
                            console.error('图表调整大小失败:', error);
                        }
                    }
                });
            }

            // 初始化持仓数据
            initializePositionsData() {
                this.positionsData = [
                    {
                        code: '000001',
                        name: '平安银行',
                        quantity: 2000,
                        avgPrice: 12.50,
                        currentPrice: 12.85,
                        marketValue: 25700,
                        cost: 25000,
                        pnl: 700,
                        pnlPercent: 2.80,
                        todayPnL: 140
                    },
                    {
                        code: '600036',
                        name: '招商银行',
                        quantity: 1000,
                        avgPrice: 35.20,
                        currentPrice: 35.68,
                        marketValue: 35680,
                        cost: 35200,
                        pnl: 480,
                        pnlPercent: 1.36,
                        todayPnL: 230
                    },
                    {
                        code: '600519',
                        name: '贵州茅台',
                        quantity: 100,
                        avgPrice: 1650.00,
                        currentPrice: 1685.50,
                        marketValue: 168550,
                        cost: 165000,
                        pnl: 3550,
                        pnlPercent: 2.15,
                        todayPnL: 660
                    }
                ];
            }

            // 初始化交易历史数据
            initializeTradeHistoryData() {
                this.tradeHistoryData = [
                    {
                        id: 'T001',
                        time: '2024-06-14 09:30:15',
                        code: '000001',
                        name: '平安银行',
                        type: 'buy',
                        price: 12.50,
                        quantity: 1000,
                        amount: 12500,
                        fee: 12.50,
                        status: 'completed'
                    },
                    {
                        id: 'T002',
                        time: '2024-06-14 10:15:32',
                        code: '600036',
                        name: '招商银行',
                        type: 'buy',
                        price: 35.20,
                        quantity: 1000,
                        amount: 35200,
                        fee: 35.20,
                        status: 'completed'
                    }
                ];
            }

            // 渲染交易界面
            renderTradingInterface() {
                this.updateTradingStockSelector();
                this.updateAccountInfo();
                this.renderTradeHistory();
                this.resetTradeForm();
            }

            // 更新交易股票选择器
            updateTradingStockSelector() {
                const selector = document.getElementById('tradingStockSelector');
                if (!selector) return;

                selector.innerHTML = '<option value="">请选择股票</option>' +
                    this.stockData.map(stock =>
                        `<option value="${stock.code}">${stock.code} ${stock.name}</option>`
                    ).join('');
            }

            // 更新账户信息
            updateAccountInfo() {
                const elements = {
                    totalAssets: document.getElementById('totalAssets'),
                    availableFunds: document.getElementById('availableFunds'),
                    positionValue: document.getElementById('positionValue'),
                    todayPnL: document.getElementById('todayPnL')
                };

                if (elements.totalAssets) elements.totalAssets.textContent = this.formatNumber(this.accountData.totalAssets);
                if (elements.availableFunds) elements.availableFunds.textContent = this.formatNumber(this.accountData.availableFunds);
                if (elements.positionValue) elements.positionValue.textContent = this.formatNumber(this.accountData.positionValue);
                if (elements.todayPnL) {
                    elements.todayPnL.textContent = this.formatPercent(this.accountData.todayPnL, true);
                    elements.todayPnL.className = `mono-font ${this.getPriceClass(this.accountData.todayPnL)}`;
                }
            }

            // 渲染交易历史
            renderTradeHistory() {
                const tbody = document.getElementById('tradeHistoryTable');
                if (!tbody) return;

                tbody.innerHTML = this.tradeHistoryData.map(trade => {
                    const typeClass = trade.type === 'buy' ? 'text-green-400' : 'text-red-400';
                    const typeText = trade.type === 'buy' ? '买入' : '卖出';
                    const statusClass = trade.status === 'completed' ? 'text-green-400' :
                                       trade.status === 'pending' ? 'text-yellow-400' : 'text-red-400';
                    const statusText = trade.status === 'completed' ? '已成交' :
                                      trade.status === 'pending' ? '待成交' : '已撤销';

                    return `
                        <tr class="hover:bg-gray-700">
                            <td class="text-sm">${trade.time}</td>
                            <td>${trade.code} ${trade.name}</td>
                            <td class="${typeClass}">${typeText}</td>
                            <td class="mono-font">${this.formatNumber(trade.price)}</td>
                            <td class="mono-font">${trade.quantity}</td>
                            <td class="mono-font">${this.formatNumber(trade.amount)}</td>
                            <td class="${statusClass}">${statusText}</td>
                        </tr>
                    `;
                }).join('');
            }

            // 渲染持仓界面
            renderPositions() {
                this.updatePositionSummary();
                this.renderPositionsList();
                this.renderPositionCharts();
            }

            // 更新持仓汇总
            updatePositionSummary() {
                const totalValue = this.positionsData.reduce((sum, pos) => sum + pos.marketValue, 0);
                const totalPnL = this.positionsData.reduce((sum, pos) => sum + pos.pnl, 0);
                const totalCost = this.positionsData.reduce((sum, pos) => sum + pos.cost, 0);
                const todayPnL = this.positionsData.reduce((sum, pos) => sum + pos.todayPnL, 0);

                const elements = {
                    totalPositionValue: document.getElementById('totalPositionValue'),
                    totalPnL: document.getElementById('totalPnL'),
                    totalCost: document.getElementById('totalCost'),
                    todayPositionPnL: document.getElementById('todayPositionPnL'),
                    positionCount: document.getElementById('positionCount')
                };

                if (elements.totalPositionValue) elements.totalPositionValue.textContent = this.formatNumber(totalValue);
                if (elements.totalPnL) {
                    elements.totalPnL.textContent = this.formatPercent(totalPnL, true);
                    elements.totalPnL.className = `text-2xl font-bold mono-font ${this.getPriceClass(totalPnL)}`;
                }
                if (elements.totalCost) elements.totalCost.textContent = this.formatNumber(totalCost);
                if (elements.todayPositionPnL) {
                    elements.todayPositionPnL.textContent = this.formatPercent(todayPnL, true);
                    elements.todayPositionPnL.className = `text-2xl font-bold mono-font ${this.getPriceClass(todayPnL)}`;
                }
                if (elements.positionCount) elements.positionCount.textContent = this.positionsData.length;
            }

            // 渲染持仓列表
            renderPositionsList() {
                const tbody = document.getElementById('positionsTable');
                if (!tbody) return;

                tbody.innerHTML = this.positionsData.map((position, index) => {
                    const pnlClass = this.getPriceClass(position.pnl);
                    const pnlPercentClass = this.getPriceClass(position.pnlPercent);

                    return `
                        <tr class="hover:bg-gray-700 draggable"
                            data-selectable="true"
                            data-selectable-id="${position.code}"
                            data-position-code="${position.code}"
                            data-context-menu="position-item">
                            <td style="width: 40px;">
                                <div class="selection-checkbox" style="display: none;"></div>
                                <i class="fas fa-grip-vertical text-gray-500 drag-handle" style="cursor: move; margin-left: 8px;"></i>
                            </td>
                            <td class="mono-font">${position.code}</td>
                            <td>${position.name}</td>
                            <td class="mono-font">${position.quantity}</td>
                            <td class="mono-font">${this.formatNumber(position.avgPrice)}</td>
                            <td class="mono-font">${this.formatNumber(position.currentPrice)}</td>
                            <td class="mono-font">${this.formatNumber(position.marketValue)}</td>
                            <td class="mono-font ${pnlClass}">${this.formatPercent(position.pnl, true)}</td>
                            <td class="mono-font ${pnlPercentClass}">${this.formatPercent(position.pnlPercent)}%</td>
                            <td>
                                <div class="flex items-center gap-2">
                                    <button class="text-green-400 hover:text-green-300 tooltip"
                                            data-tooltip="加仓"
                                            onclick="app.addPosition('${position.code}')">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                    <button class="text-red-400 hover:text-red-300 tooltip"
                                            data-tooltip="卖出"
                                            onclick="app.sellPosition('${position.code}')">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                    <button class="text-blue-400 hover:text-blue-300 tooltip"
                                            data-tooltip="详情"
                                            onclick="app.viewPositionDetail('${position.code}')">
                                        <i class="fas fa-info-circle"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `;
                }).join('');
            }

            // 工具函数 - 增强版
            formatNumber(num, decimals = 2) {
                if (typeof num !== 'number') return num;
                return num.toLocaleString('zh-CN', {
                    minimumFractionDigits: decimals,
                    maximumFractionDigits: decimals
                });
            }

            formatVolume(volume) {
                if (volume >= 100000000) {
                    return (volume / 100000000).toFixed(2) + '亿';
                } else if (volume >= 10000) {
                    return (volume / 10000).toFixed(0) + '万';
                }
                return volume.toString();
            }

            formatAmount(amount) {
                if (amount >= 100000000) {
                    return (amount / 100000000).toFixed(2) + '亿元';
                } else if (amount >= 10000) {
                    return (amount / 10000).toFixed(2) + '万元';
                } else {
                    return amount.toString() + '元';
                }
            }

            formatPercent(percent, showSign = true) {
                const sign = showSign && percent > 0 ? '+' : '';
                return `${sign}${this.formatNumber(percent)}%`;
            }

            getPriceClass(changePercent) {
                if (changePercent > 0) return 'price-up';
                if (changePercent < 0) return 'price-down';
                return 'price-flat';
            }

            getPriceBgClass(changePercent) {
                if (changePercent > 0) return 'price-up-bg';
                if (changePercent < 0) return 'price-down-bg';
                return 'bg-gray-500 bg-opacity-20 text-gray-400';
            }

            formatTime(timestamp) {
                const date = new Date(timestamp);
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
            }

            formatDate(timestamp) {
                const date = new Date(timestamp);
                return date.toLocaleDateString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                });
            }

            // 计算技术指标
            calculateMA(data, period) {
                const result = [];
                for (let i = period - 1; i < data.length; i++) {
                    const sum = data.slice(i - period + 1, i + 1).reduce((acc, val) => acc + val, 0);
                    result.push(sum / period);
                }
                return result;
            }

            calculateRSI(prices, period = 14) {
                const gains = [];
                const losses = [];

                for (let i = 1; i < prices.length; i++) {
                    const change = prices[i] - prices[i - 1];
                    gains.push(change > 0 ? change : 0);
                    losses.push(change < 0 ? Math.abs(change) : 0);
                }

                const avgGain = this.calculateMA(gains, period);
                const avgLoss = this.calculateMA(losses, period);

                return avgGain.map((gain, i) => {
                    const rs = gain / avgLoss[i];
                    return 100 - (100 / (1 + rs));
                });
            }

            // 数据验证
            validateStockCode(code) {
                const pattern = /^[0-9]{6}$/;
                return pattern.test(code);
            }

            validateStockName(name) {
                return name && name.trim().length > 0 && name.trim().length <= 20;
            }

            // 本地存储管理
            saveToLocalStorage(key, data) {
                try {
                    localStorage.setItem(key, JSON.stringify(data));
                } catch (error) {
                    console.error('保存到本地存储失败:', error);
                }
            }

            loadFromLocalStorage(key, defaultValue = null) {
                try {
                    const data = localStorage.getItem(key);
                    return data ? JSON.parse(data) : defaultValue;
                } catch (error) {
                    console.error('从本地存储加载失败:', error);
                    return defaultValue;
                }
            }

            // 防抖函数
            debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }

            // 节流函数
            throttle(func, limit) {
                let inThrottle;
                return function() {
                    const args = arguments;
                    const context = this;
                    if (!inThrottle) {
                        func.apply(context, args);
                        inThrottle = true;
                        setTimeout(() => inThrottle = false, limit);
                    }
                };
            }

            // 交易相关方法
            selectTradingStock(stockCode) {
                this.selectedTradingStock = this.stockData.find(stock => stock.code === stockCode);
                if (this.selectedTradingStock) {
                    this.updateSelectedStockInfo();
                    this.updateTradePrice();
                    this.updateTradeButton();
                    this.calculateTradeAmount();
                }
            }

            updateSelectedStockInfo() {
                if (!this.selectedTradingStock) return;

                const elements = {
                    selectedStockName: document.getElementById('selectedStockName'),
                    selectedStockCode: document.getElementById('selectedStockCode'),
                    selectedStockPrice: document.getElementById('selectedStockPrice'),
                    selectedStockChange: document.getElementById('selectedStockChange'),
                    selectedStockInfo: document.getElementById('selectedStockInfo')
                };

                if (elements.selectedStockName) elements.selectedStockName.textContent = this.selectedTradingStock.name;
                if (elements.selectedStockCode) elements.selectedStockCode.textContent = this.selectedTradingStock.code;
                if (elements.selectedStockPrice) elements.selectedStockPrice.textContent = this.formatNumber(this.selectedTradingStock.currentPrice);
                if (elements.selectedStockChange) {
                    elements.selectedStockChange.textContent = this.formatPercent(this.selectedTradingStock.changePercent, true) + '%';
                    elements.selectedStockChange.className = `text-sm ${this.getPriceClass(this.selectedTradingStock.changePercent)}`;
                }
                if (elements.selectedStockInfo) elements.selectedStockInfo.classList.remove('hidden');
            }

            updateTradePrice() {
                const priceType = document.getElementById('priceType').value;
                const priceInput = document.getElementById('tradePrice');

                if (priceType === 'market' && this.selectedTradingStock) {
                    priceInput.value = this.selectedTradingStock.currentPrice;
                }
            }

            updateTradeButton() {
                const submitBtn = document.getElementById('tradeSubmitBtn');
                const submitText = document.getElementById('tradeSubmitText');

                if (this.selectedTradingStock) {
                    submitBtn.disabled = false;
                    submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');

                    if (this.currentTradeType === 'buy') {
                        submitText.textContent = `买入 ${this.selectedTradingStock.name}`;
                    } else {
                        submitText.textContent = `卖出 ${this.selectedTradingStock.name}`;
                    }
                } else {
                    submitBtn.disabled = true;
                    submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
                    submitText.textContent = '请选择股票';
                }
            }

            calculateTradeAmount() {
                const price = parseFloat(document.getElementById('tradePrice').value) || 0;
                const quantity = parseInt(document.getElementById('tradeQuantity').value) || 0;
                const amount = price * quantity;

                document.getElementById('tradeAmount').value = this.formatNumber(amount);

                // 计算费用
                this.calculateTradeFees(amount);
            }

            calculateTradeFees(amount) {
                const commissionRate = 0.0003; // 万分之三
                const stampTaxRate = this.currentTradeType === 'sell' ? 0.001 : 0; // 卖出时收印花税
                const transferFeeRate = 0.00002; // 万分之二

                const commission = Math.max(amount * commissionRate, 5); // 最低5元
                const stampTax = amount * stampTaxRate;
                const transferFee = amount * transferFeeRate;
                const totalFee = commission + stampTax + transferFee;

                document.getElementById('commissionFee').textContent = this.formatNumber(commission);
                document.getElementById('stampTax').textContent = this.formatNumber(stampTax);
                document.getElementById('transferFee').textContent = this.formatNumber(transferFee);
                document.getElementById('totalFee').textContent = this.formatNumber(totalFee);
            }

            showTradeConfirmModal() {
                if (!this.selectedTradingStock) {
                    this.showNotification('请先选择股票', 'warning');
                    return;
                }

                const price = parseFloat(document.getElementById('tradePrice').value);
                const quantity = parseInt(document.getElementById('tradeQuantity').value);

                if (!price || !quantity) {
                    this.showNotification('请填写完整的交易信息', 'warning');
                    return;
                }

                if (quantity < 100 || quantity % 100 !== 0) {
                    this.showNotification('交易数量必须是100的整数倍', 'warning');
                    return;
                }

                const amount = price * quantity;
                const totalFee = parseFloat(document.getElementById('totalFee').textContent.replace(/,/g, ''));

                // 检查资金是否充足
                if (this.currentTradeType === 'buy' && amount + totalFee > this.accountData.availableFunds) {
                    this.showNotification('可用资金不足', 'error');
                    return;
                }

                // 检查持仓是否充足
                if (this.currentTradeType === 'sell') {
                    const position = this.positionsData.find(p => p.code === this.selectedTradingStock.code);
                    if (!position || position.quantity < quantity) {
                        this.showNotification('持仓数量不足', 'error');
                        return;
                    }
                }

                // 填充确认信息
                document.getElementById('confirmStockInfo').textContent = `${this.selectedTradingStock.code} ${this.selectedTradingStock.name}`;
                document.getElementById('confirmTradeType').textContent = this.currentTradeType === 'buy' ? '买入' : '卖出';
                document.getElementById('confirmPrice').textContent = this.formatNumber(price);
                document.getElementById('confirmQuantity').textContent = `${quantity}股`;
                document.getElementById('confirmAmount').textContent = this.formatNumber(amount);
                document.getElementById('confirmFee').textContent = this.formatNumber(totalFee);

                document.getElementById('tradeConfirmModal').classList.remove('hidden');
            }

            executeTrade() {
                const price = parseFloat(document.getElementById('tradePrice').value);
                const quantity = parseInt(document.getElementById('tradeQuantity').value);
                const amount = price * quantity;
                const totalFee = parseFloat(document.getElementById('totalFee').textContent.replace(/,/g, ''));

                // 创建交易记录
                const trade = {
                    id: 'T' + Date.now(),
                    time: new Date().toLocaleString('zh-CN'),
                    code: this.selectedTradingStock.code,
                    name: this.selectedTradingStock.name,
                    type: this.currentTradeType,
                    price: price,
                    quantity: quantity,
                    amount: amount,
                    fee: totalFee,
                    status: 'completed'
                };

                this.tradeHistoryData.unshift(trade);

                // 更新账户资金
                if (this.currentTradeType === 'buy') {
                    this.accountData.availableFunds -= (amount + totalFee);
                    this.updateOrCreatePosition(this.selectedTradingStock.code, quantity, price);
                } else {
                    this.accountData.availableFunds += (amount - totalFee);
                    this.updatePositionAfterSell(this.selectedTradingStock.code, quantity);
                }

                // 更新持仓市值
                this.updateAccountPositionValue();

                // 重新渲染相关界面
                this.renderTradeHistory();
                this.updateAccountInfo();
                this.renderPositions();

                // 关闭模态框并重置表单
                document.getElementById('tradeConfirmModal').classList.add('hidden');
                this.resetTradeForm();

                this.showNotification(`${trade.type === 'buy' ? '买入' : '卖出'}成功`, 'success');
            }

            updateOrCreatePosition(stockCode, quantity, price) {
                const existingPosition = this.positionsData.find(p => p.code === stockCode);

                if (existingPosition) {
                    // 更新现有持仓
                    const totalCost = existingPosition.cost + (quantity * price);
                    const totalQuantity = existingPosition.quantity + quantity;
                    existingPosition.avgPrice = totalCost / totalQuantity;
                    existingPosition.quantity = totalQuantity;
                    existingPosition.cost = totalCost;
                    existingPosition.marketValue = totalQuantity * existingPosition.currentPrice;
                    existingPosition.pnl = existingPosition.marketValue - existingPosition.cost;
                    existingPosition.pnlPercent = (existingPosition.pnl / existingPosition.cost) * 100;
                } else {
                    // 创建新持仓
                    const stock = this.stockData.find(s => s.code === stockCode);
                    const newPosition = {
                        code: stockCode,
                        name: stock.name,
                        quantity: quantity,
                        avgPrice: price,
                        currentPrice: stock.currentPrice,
                        marketValue: quantity * stock.currentPrice,
                        cost: quantity * price,
                        pnl: (quantity * stock.currentPrice) - (quantity * price),
                        pnlPercent: ((quantity * stock.currentPrice) - (quantity * price)) / (quantity * price) * 100,
                        todayPnL: 0
                    };
                    this.positionsData.push(newPosition);
                }
            }

            updatePositionAfterSell(stockCode, quantity) {
                const position = this.positionsData.find(p => p.code === stockCode);
                if (position) {
                    position.quantity -= quantity;
                    position.cost = position.quantity * position.avgPrice;
                    position.marketValue = position.quantity * position.currentPrice;
                    position.pnl = position.marketValue - position.cost;
                    position.pnlPercent = position.cost > 0 ? (position.pnl / position.cost) * 100 : 0;

                    // 如果全部卖出，移除持仓
                    if (position.quantity <= 0) {
                        const index = this.positionsData.indexOf(position);
                        this.positionsData.splice(index, 1);
                    }
                }
            }

            updateAccountPositionValue() {
                this.accountData.positionValue = this.positionsData.reduce((sum, pos) => sum + pos.marketValue, 0);
                this.accountData.totalAssets = this.accountData.availableFunds + this.accountData.positionValue;
            }

            resetTradeForm() {
                document.getElementById('tradePrice').value = '';
                document.getElementById('tradeQuantity').value = '';
                document.getElementById('tradeAmount').value = '';
                document.getElementById('selectedStockInfo').classList.add('hidden');
                this.selectedTradingStock = null;
                this.updateTradeButton();
            }

            showStockSelectModal() {
                const tbody = document.getElementById('stockSelectTable');
                tbody.innerHTML = this.stockData.map(stock => {
                    const changeClass = this.getPriceClass(stock.changePercent);
                    return `
                        <tr class="hover:bg-gray-700 cursor-pointer" onclick="selectStockForTrading('${stock.code}')">
                            <td class="mono-font">${stock.code}</td>
                            <td>${stock.name}</td>
                            <td class="mono-font">${this.formatNumber(stock.currentPrice)}</td>
                            <td class="mono-font ${changeClass}">${this.formatPercent(stock.changePercent)}%</td>
                            <td>
                                <button class="btn-primary text-sm px-3 py-1">选择</button>
                            </td>
                        </tr>
                    `;
                }).join('');

                document.getElementById('stockSelectModal').classList.remove('hidden');
            }

            // 持仓相关方法
            updatePositionPrices() {
                this.positionsData.forEach(position => {
                    const stock = this.stockData.find(s => s.code === position.code);
                    if (stock) {
                        const oldPrice = position.currentPrice;
                        position.currentPrice = stock.currentPrice;
                        position.marketValue = position.quantity * position.currentPrice;
                        position.pnl = position.marketValue - position.cost;
                        position.pnlPercent = (position.pnl / position.cost) * 100;
                        position.todayPnL = position.quantity * (position.currentPrice - oldPrice);
                    }
                });

                this.updateAccountPositionValue();
            }

            renderPositionCharts() {
                this.renderPositionDistributionChart();
                this.renderPnLAnalysisChart();
            }

            renderPositionDistributionChart() {
                const container = document.getElementById('positionDistributionChart');
                if (!container) return;

                const chart = echarts.init(container);
                const data = this.positionsData.map(position => ({
                    name: position.name,
                    value: position.marketValue
                }));

                const option = {
                    backgroundColor: 'transparent',
                    tooltip: {
                        trigger: 'item',
                        backgroundColor: '#1e293b',
                        borderColor: '#475569',
                        textStyle: { color: '#f8fafc' },
                        formatter: '{a} <br/>{b}: {c} ({d}%)'
                    },
                    series: [{
                        name: '持仓分布',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        data: data,
                        itemStyle: {
                            borderRadius: 5,
                            borderColor: '#1e293b',
                            borderWidth: 2
                        },
                        label: {
                            color: '#f8fafc'
                        }
                    }]
                };

                chart.setOption(option);
                this.charts.positionDistribution = chart;
            }

            renderPnLAnalysisChart() {
                const container = document.getElementById('pnlAnalysisChart');
                if (!container) return;

                const chart = echarts.init(container);
                const data = this.positionsData.map(position => ({
                    name: position.name,
                    value: position.pnl,
                    itemStyle: {
                        color: position.pnl >= 0 ? '#10b981' : '#ef4444'
                    }
                }));

                const option = {
                    backgroundColor: 'transparent',
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: '#1e293b',
                        borderColor: '#475569',
                        textStyle: { color: '#f8fafc' }
                    },
                    xAxis: {
                        type: 'category',
                        data: data.map(item => item.name),
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 }
                    },
                    yAxis: {
                        type: 'value',
                        axisLine: { lineStyle: { color: '#475569' } },
                        axisLabel: { color: '#94a3b8', fontSize: 10 },
                        splitLine: { lineStyle: { color: '#334155' } }
                    },
                    series: [{
                        type: 'bar',
                        data: data,
                        barWidth: '60%'
                    }]
                };

                chart.setOption(option);
                this.charts.pnlAnalysis = chart;
            }

            // 显示通知
            showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transition-all duration-300 ${
                    type === 'success' ? 'bg-green-500 text-white' :
                    type === 'error' ? 'bg-red-500 text-white' :
                    type === 'warning' ? 'bg-yellow-500 text-white' :
                    'bg-blue-500 text-white'
                }`;

                notification.innerHTML = `
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-${
                            type === 'success' ? 'check-circle' :
                            type === 'error' ? 'exclamation-circle' :
                            type === 'warning' ? 'exclamation-triangle' :
                            'info-circle'
                        }"></i>
                        <span>${message}</span>
                    </div>
                `;

                document.body.appendChild(notification);

                // 添加进入动画
                setTimeout(() => {
                    notification.style.transform = 'translateX(0)';
                    notification.style.opacity = '1';
                }, 10);

                // 自动移除
                setTimeout(() => {
                    notification.style.transform = 'translateX(100%)';
                    notification.style.opacity = '0';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 300);
                }, 3000);
            }
        }

        // 拖拽管理器类
        class DragManager {
            constructor() {
                this.draggedElement = null;
                this.draggedData = null;
                this.placeholder = null;
                this.init();
            }

            init() {
                this.setupDragEvents();
            }

            setupDragEvents() {
                document.addEventListener('dragstart', this.handleDragStart.bind(this));
                document.addEventListener('dragend', this.handleDragEnd.bind(this));
                document.addEventListener('dragover', this.handleDragOver.bind(this));
                document.addEventListener('drop', this.handleDrop.bind(this));
                document.addEventListener('dragenter', this.handleDragEnter.bind(this));
                document.addEventListener('dragleave', this.handleDragLeave.bind(this));
            }

            makeDraggable(element, data) {
                element.draggable = true;
                element.classList.add('draggable');
                element.dataset.dragData = JSON.stringify(data);
            }

            handleDragStart(e) {
                if (!e.target.classList.contains('draggable')) return;

                this.draggedElement = e.target;
                this.draggedData = JSON.parse(e.target.dataset.dragData);

                e.target.classList.add('dragging');
                e.dataTransfer.effectAllowed = 'move';
                e.dataTransfer.setData('text/html', e.target.outerHTML);

                // 创建占位符
                this.createPlaceholder();
            }

            handleDragEnd(e) {
                if (e.target === this.draggedElement) {
                    e.target.classList.remove('dragging');
                    this.removePlaceholder();
                    this.draggedElement = null;
                    this.draggedData = null;
                }
            }

            handleDragOver(e) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';
            }

            handleDragEnter(e) {
                if (this.isValidDropTarget(e.target)) {
                    e.target.classList.add('drag-over');
                }
            }

            handleDragLeave(e) {
                e.target.classList.remove('drag-over');
            }

            handleDrop(e) {
                e.preventDefault();
                e.target.classList.remove('drag-over');

                if (!this.isValidDropTarget(e.target) || !this.draggedElement) return;

                const dropTarget = e.target.closest('[data-drop-zone]');
                if (dropTarget) {
                    this.performDrop(dropTarget);
                }
            }

            isValidDropTarget(element) {
                return element.hasAttribute('data-drop-zone') ||
                       element.closest('[data-drop-zone]');
            }

            createPlaceholder() {
                this.placeholder = document.createElement('div');
                this.placeholder.className = 'drag-placeholder';
                this.draggedElement.parentNode.insertBefore(this.placeholder, this.draggedElement.nextSibling);
            }

            removePlaceholder() {
                if (this.placeholder && this.placeholder.parentNode) {
                    this.placeholder.parentNode.removeChild(this.placeholder);
                    this.placeholder = null;
                }
            }

            performDrop(dropTarget) {
                const dropZone = dropTarget.dataset.dropZone;
                const dropIndex = Array.from(dropTarget.children).indexOf(this.placeholder);

                // 触发自定义事件
                const event = new CustomEvent('itemDropped', {
                    detail: {
                        draggedData: this.draggedData,
                        dropZone: dropZone,
                        dropIndex: dropIndex,
                        draggedElement: this.draggedElement
                    }
                });

                document.dispatchEvent(event);
            }
        }

        // 右键菜单管理器类
        class ContextMenuManager {
            constructor() {
                this.menu = null;
                this.currentTarget = null;
                this.init();
            }

            init() {
                this.menu = document.getElementById('contextMenu');
                this.setupEvents();
            }

            setupEvents() {
                document.addEventListener('contextmenu', this.handleContextMenu.bind(this));
                document.addEventListener('click', this.hideMenu.bind(this));
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') this.hideMenu();
                });
            }

            handleContextMenu(e) {
                const target = e.target.closest('[data-context-menu]');
                if (!target) return;

                e.preventDefault();
                this.currentTarget = target;

                const menuType = target.dataset.contextMenu;
                const menuItems = this.getMenuItems(menuType, target);

                this.showMenu(e.clientX, e.clientY, menuItems);
            }

            getMenuItems(menuType, target) {
                switch (menuType) {
                    case 'stock-item':
                        return this.getStockMenuItems(target);
                    case 'chart':
                        return this.getChartMenuItems(target);
                    case 'watchlist':
                        return this.getWatchlistMenuItems(target);
                    default:
                        return [];
                }
            }

            getStockMenuItems(target) {
                const stockCode = target.dataset.stockCode;
                const isInWatchlist = app.watchlistData.some(stock => stock.code === stockCode);

                return [
                    {
                        icon: 'fas fa-chart-line',
                        text: '查看详情',
                        action: () => app.viewStockDetail(stockCode)
                    },
                    {
                        icon: isInWatchlist ? 'fas fa-star-minus' : 'fas fa-star',
                        text: isInWatchlist ? '移出自选' : '添加自选',
                        action: () => isInWatchlist ?
                            app.removeFromWatchlist(stockCode) :
                            app.addToWatchlistQuick(stockCode)
                    },
                    { separator: true },
                    {
                        icon: 'fas fa-copy',
                        text: '复制代码',
                        action: () => navigator.clipboard.writeText(stockCode)
                    },
                    {
                        icon: 'fas fa-external-link-alt',
                        text: '在新窗口打开',
                        action: () => window.open(`#stock/${stockCode}`, '_blank')
                    }
                ];
            }

            getChartMenuItems(target) {
                return [
                    {
                        icon: 'fas fa-save',
                        text: '保存图片',
                        action: () => app.saveChartImage(target)
                    },
                    {
                        icon: 'fas fa-cog',
                        text: '图表设置',
                        action: () => app.showChartSettings(target)
                    },
                    { separator: true },
                    {
                        icon: 'fas fa-expand',
                        text: '全屏显示',
                        action: () => app.toggleChartFullscreen(target)
                    }
                ];
            }

            getWatchlistMenuItems(target) {
                return [
                    {
                        icon: 'fas fa-sort',
                        text: '排序选项',
                        submenu: [
                            { text: '按代码排序', action: () => app.sortWatchlist('code') },
                            { text: '按涨跌幅排序', action: () => app.sortWatchlist('change') },
                            { text: '按成交量排序', action: () => app.sortWatchlist('volume') }
                        ]
                    },
                    { separator: true },
                    {
                        icon: 'fas fa-download',
                        text: '导出自选股',
                        action: () => app.exportWatchlist()
                    }
                ];
            }

            showMenu(x, y, items) {
                this.menu.innerHTML = this.renderMenuItems(items);
                this.menu.style.left = x + 'px';
                this.menu.style.top = y + 'px';
                this.menu.classList.add('show');

                // 确保菜单在视窗内
                this.adjustMenuPosition();
            }

            renderMenuItems(items) {
                return items.map((item, index) => {
                    if (item.separator) {
                        return '<div class="context-menu-separator"></div>';
                    }

                    const disabled = item.disabled ? 'disabled' : '';
                    const actionId = `menuAction_${Date.now()}_${index}`;

                    // 将action函数存储到全局对象中
                    if (item.action && typeof item.action === 'function') {
                        window[actionId] = item.action;
                    }

                    return `
                        <div class="context-menu-item ${disabled}" onclick="if(window['${actionId}']) window['${actionId}'](); app.contextMenuManager.hideMenu();">
                            <i class="${item.icon}"></i>
                            <span>${item.text}</span>
                        </div>
                    `;
                }).join('');
            }

            adjustMenuPosition() {
                const rect = this.menu.getBoundingClientRect();
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;

                if (rect.right > viewportWidth) {
                    this.menu.style.left = (viewportWidth - rect.width - 10) + 'px';
                }

                if (rect.bottom > viewportHeight) {
                    this.menu.style.top = (viewportHeight - rect.height - 10) + 'px';
                }
            }

            hideMenu() {
                if (this.menu) {
                    this.menu.classList.remove('show');
                    this.currentTarget = null;
                }
            }


        }

        // 快速预览管理器类
        class PreviewManager {
            constructor() {
                this.previewCard = null;
                this.previewTimeout = null;
                this.currentTarget = null;
                this.init();
            }

            init() {
                this.previewCard = document.getElementById('previewCard');
                this.setupEvents();
            }

            setupEvents() {
                document.addEventListener('mouseenter', this.handleMouseEnter.bind(this), true);
                document.addEventListener('mouseleave', this.handleMouseLeave.bind(this), true);
                document.addEventListener('mousemove', this.handleMouseMove.bind(this));
            }

            handleMouseEnter(e) {
                const target = e.target.closest('[data-preview]');
                if (!target) return;

                this.currentTarget = target;
                this.previewTimeout = setTimeout(() => {
                    this.showPreview(target, e);
                }, 500); // 500ms延迟防止误触
            }

            handleMouseLeave(e) {
                const target = e.target.closest('[data-preview]');
                if (target === this.currentTarget) {
                    this.clearPreviewTimeout();
                    this.hidePreview();
                }
            }

            handleMouseMove(e) {
                if (this.previewCard && this.previewCard.classList.contains('show')) {
                    this.updatePreviewPosition(e);
                }
            }

            showPreview(target, event) {
                const previewType = target.dataset.preview;
                const previewData = this.getPreviewData(previewType, target);

                if (!previewData) return;

                this.renderPreview(previewData);
                this.updatePreviewPosition(event);
                this.previewCard.classList.add('show');
            }

            getPreviewData(previewType, target) {
                switch (previewType) {
                    case 'stock':
                        return this.getStockPreviewData(target);
                    case 'indicator':
                        return this.getIndicatorPreviewData(target);
                    default:
                        return null;
                }
            }

            getStockPreviewData(target) {
                const stockCode = target.dataset.stockCode;
                const stock = app.stockData.find(s => s.code === stockCode);

                if (!stock) return null;

                return {
                    title: stock.name,
                    subtitle: stock.code,
                    price: stock.currentPrice,
                    change: stock.changePercent,
                    details: [
                        { label: '开盘', value: app.formatNumber(stock.openPrice) },
                        { label: '最高', value: app.formatNumber(stock.highPrice) },
                        { label: '最低', value: app.formatNumber(stock.lowPrice) },
                        { label: '成交量', value: app.formatVolume(stock.volume) },
                        { label: '行业', value: stock.industry },
                        { label: '市场', value: stock.market }
                    ]
                };
            }

            getIndicatorPreviewData(target) {
                const indicatorType = target.dataset.indicatorType;
                const descriptions = {
                    'macd': 'MACD指标用于判断股价趋势变化，金叉看涨，死叉看跌',
                    'kdj': 'KDJ指标用于判断超买超卖，K值>80超买，K值<20超卖',
                    'rsi': 'RSI指标衡量价格变动速度，>70超买，<30超卖',
                    'bollinger': '布林带指标显示价格波动区间，突破上轨看涨，跌破下轨看跌'
                };

                return {
                    title: indicatorType.toUpperCase(),
                    subtitle: '技术指标',
                    description: descriptions[indicatorType] || '技术分析指标'
                };
            }

            renderPreview(data) {
                document.getElementById('previewTitle').textContent = data.title;
                document.getElementById('previewSubtitle').textContent = data.subtitle;

                if (data.price !== undefined) {
                    document.getElementById('previewPrice').textContent = app.formatNumber(data.price);
                    const changeElement = document.getElementById('previewChange');
                    changeElement.textContent = app.formatPercent(data.change);
                    changeElement.className = `text-sm ${app.getPriceClass(data.change)}`;
                }

                const contentElement = document.getElementById('previewContent');
                if (data.details) {
                    contentElement.innerHTML = data.details.map(detail => `
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">${detail.label}</span>
                            <span>${detail.value}</span>
                        </div>
                    `).join('');
                } else if (data.description) {
                    contentElement.innerHTML = `<div class="text-sm text-gray-300">${data.description}</div>`;
                }
            }

            updatePreviewPosition(event) {
                const cardWidth = 320;
                const cardHeight = 200;
                const margin = 10;

                let x = event.clientX + margin;
                let y = event.clientY + margin;

                // 确保卡片在视窗内
                if (x + cardWidth > window.innerWidth) {
                    x = event.clientX - cardWidth - margin;
                }

                if (y + cardHeight > window.innerHeight) {
                    y = event.clientY - cardHeight - margin;
                }

                this.previewCard.style.left = x + 'px';
                this.previewCard.style.top = y + 'px';
            }

            hidePreview() {
                if (this.previewCard) {
                    this.previewCard.classList.remove('show');
                }
                this.currentTarget = null;
            }

            clearPreviewTimeout() {
                if (this.previewTimeout) {
                    clearTimeout(this.previewTimeout);
                    this.previewTimeout = null;
                }
            }
        }

        // 多选管理器类
        class MultiSelectManager {
            constructor() {
                this.selectedItems = new Set();
                this.isSelectionMode = false;
                this.toolbar = null;
                this.init();
            }

            init() {
                this.toolbar = document.getElementById('multiSelectToolbar');
                this.setupEvents();
            }

            setupEvents() {
                document.addEventListener('click', this.handleClick.bind(this));
                document.addEventListener('keydown', this.handleKeyDown.bind(this));
            }

            handleClick(e) {
                const checkbox = e.target.closest('.selection-checkbox');
                if (checkbox) {
                    e.stopPropagation();
                    this.toggleSelection(checkbox);
                    return;
                }

                const selectableItem = e.target.closest('[data-selectable]');
                if (selectableItem && this.isSelectionMode) {
                    if (e.ctrlKey || e.metaKey) {
                        this.toggleItemSelection(selectableItem);
                    } else if (e.shiftKey) {
                        this.selectRange(selectableItem);
                    } else {
                        this.selectSingle(selectableItem);
                    }
                }
            }

            handleKeyDown(e) {
                if (e.key === 'Escape' && this.isSelectionMode) {
                    this.clearSelection();
                }

                if ((e.ctrlKey || e.metaKey) && e.key === 'a' && this.isSelectionMode) {
                    e.preventDefault();
                    this.selectAll();
                }
            }

            toggleSelection(checkbox) {
                const item = checkbox.closest('[data-selectable]');
                if (!item) return;

                if (checkbox.classList.contains('checked')) {
                    this.deselectItem(item);
                } else {
                    this.selectItem(item);
                }
            }

            selectItem(item) {
                const itemId = item.dataset.selectableId;
                this.selectedItems.add(itemId);

                const checkbox = item.querySelector('.selection-checkbox');
                if (checkbox) {
                    checkbox.classList.add('checked');
                }

                item.classList.add('selected');
                this.updateSelectionMode();
            }

            deselectItem(item) {
                const itemId = item.dataset.selectableId;
                this.selectedItems.delete(itemId);

                const checkbox = item.querySelector('.selection-checkbox');
                if (checkbox) {
                    checkbox.classList.remove('checked');
                }

                item.classList.remove('selected');
                this.updateSelectionMode();
            }

            toggleItemSelection(item) {
                if (this.selectedItems.has(item.dataset.selectableId)) {
                    this.deselectItem(item);
                } else {
                    this.selectItem(item);
                }
            }

            selectSingle(item) {
                this.clearSelection();
                this.selectItem(item);
            }

            selectRange(endItem) {
                // 实现Shift+点击范围选择
                const items = Array.from(document.querySelectorAll('[data-selectable]'));
                const endIndex = items.indexOf(endItem);

                if (this.lastSelectedIndex !== undefined) {
                    const startIndex = this.lastSelectedIndex;
                    const start = Math.min(startIndex, endIndex);
                    const end = Math.max(startIndex, endIndex);

                    for (let i = start; i <= end; i++) {
                        this.selectItem(items[i]);
                    }
                }

                this.lastSelectedIndex = endIndex;
            }

            selectAll() {
                const items = document.querySelectorAll('[data-selectable]');
                items.forEach(item => this.selectItem(item));
            }

            clearSelection() {
                this.selectedItems.clear();

                document.querySelectorAll('[data-selectable].selected').forEach(item => {
                    item.classList.remove('selected');
                    const checkbox = item.querySelector('.selection-checkbox');
                    if (checkbox) {
                        checkbox.classList.remove('checked');
                    }
                });

                this.updateSelectionMode();
            }

            updateSelectionMode() {
                const hasSelection = this.selectedItems.size > 0;

                if (hasSelection && !this.isSelectionMode) {
                    this.enterSelectionMode();
                } else if (!hasSelection && this.isSelectionMode) {
                    this.exitSelectionMode();
                }

                this.updateToolbar();
            }

            enterSelectionMode() {
                this.isSelectionMode = true;
                document.body.classList.add('selection-mode');

                // 显示所有复选框
                document.querySelectorAll('.selection-checkbox').forEach(checkbox => {
                    checkbox.style.display = 'block';
                });
            }

            exitSelectionMode() {
                this.isSelectionMode = false;
                document.body.classList.remove('selection-mode');

                // 隐藏所有复选框
                document.querySelectorAll('.selection-checkbox').forEach(checkbox => {
                    checkbox.style.display = 'none';
                });

                this.hideToolbar();
            }

            updateToolbar() {
                if (this.selectedItems.size > 0) {
                    document.getElementById('selectedCount').textContent = this.selectedItems.size;
                    this.showToolbar();
                } else {
                    this.hideToolbar();
                }
            }

            showToolbar() {
                if (this.toolbar) {
                    this.toolbar.classList.add('show');
                }
            }

            hideToolbar() {
                if (this.toolbar) {
                    this.toolbar.classList.remove('show');
                }
            }

            getSelectedItems() {
                return Array.from(this.selectedItems);
            }
        }

        // 图表增强器类
        class ChartEnhancer {
            constructor() {
                this.charts = new Map();
                this.syncedCharts = new Set();
                this.crosshairEnabled = new Set();
                this.overlayIndicators = new Map();
                this.init();
            }

            init() {
                this.setupGlobalEvents();
            }

            setupGlobalEvents() {
                // 监听图表创建事件
                document.addEventListener('chartCreated', this.handleChartCreated.bind(this));

                // 监听窗口大小变化
                window.addEventListener('resize', this.debounce(() => {
                    this.resizeAllCharts();
                }, 250));
            }

            registerChart(chartId, chartInstance, options = {}) {
                this.charts.set(chartId, {
                    instance: chartInstance,
                    container: document.getElementById(chartId),
                    options: options,
                    overlays: new Set()
                });

                this.enhanceChart(chartId);
            }

            enhanceChart(chartId) {
                const chartData = this.charts.get(chartId);
                if (!chartData) return;

                const { instance, container } = chartData;

                // 添加鼠标事件监听
                instance.on('mousemove', (params) => {
                    this.handleChartMouseMove(chartId, params);
                });

                instance.on('click', (params) => {
                    this.handleChartClick(chartId, params);
                });

                // 添加缩放和平移支持
                this.addZoomPanSupport(chartId);

                // 添加十字线支持
                this.addCrosshairSupport(chartId);
            }

            addZoomPanSupport(chartId) {
                const chartData = this.charts.get(chartId);
                if (!chartData) return;

                const { instance } = chartData;

                // 启用数据缩放
                instance.setOption({
                    dataZoom: [
                        {
                            type: 'inside',
                            xAxisIndex: 0,
                            filterMode: 'none'
                        },
                        {
                            type: 'slider',
                            xAxisIndex: 0,
                            bottom: 10,
                            height: 20,
                            handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z',
                            handleSize: '80%',
                            handleStyle: {
                                color: '#3b82f6'
                            }
                        }
                    ]
                });
            }

            addCrosshairSupport(chartId) {
                const chartData = this.charts.get(chartId);
                if (!chartData) return;

                const { container } = chartData;

                // 创建十字线元素
                const crosshair = document.createElement('div');
                crosshair.className = 'chart-crosshair';
                crosshair.innerHTML = `
                    <div class="chart-crosshair-line chart-crosshair-vertical"></div>
                    <div class="chart-crosshair-line chart-crosshair-horizontal"></div>
                `;
                container.appendChild(crosshair);

                // 创建工具提示
                const tooltip = document.createElement('div');
                tooltip.className = 'chart-tooltip';
                container.appendChild(tooltip);
            }

            handleChartMouseMove(chartId, params) {
                if (this.crosshairEnabled.has(chartId)) {
                    this.updateCrosshair(chartId, params);
                }

                if (this.syncedCharts.has(chartId)) {
                    this.syncChartCursor(chartId, params);
                }

                this.updateTooltip(chartId, params);
            }

            handleChartClick(chartId, params) {
                // 处理图表点击事件
                if (params.componentType === 'series') {
                    this.showDataPointDetails(chartId, params);
                }
            }

            updateCrosshair(chartId, params) {
                const chartData = this.charts.get(chartId);
                if (!chartData) return;

                const crosshair = chartData.container.querySelector('.chart-crosshair');
                if (!crosshair || !params.event) return;

                const rect = chartData.container.getBoundingClientRect();
                const x = params.event.offsetX;
                const y = params.event.offsetY;

                const vertical = crosshair.querySelector('.chart-crosshair-vertical');
                const horizontal = crosshair.querySelector('.chart-crosshair-horizontal');

                vertical.style.left = x + 'px';
                horizontal.style.top = y + 'px';

                crosshair.style.display = 'block';
            }

            updateTooltip(chartId, params) {
                const chartData = this.charts.get(chartId);
                if (!chartData) return;

                const tooltip = chartData.container.querySelector('.chart-tooltip');
                if (!tooltip || !params.event) return;

                if (params.data) {
                    const content = this.formatTooltipContent(chartId, params);
                    tooltip.innerHTML = content;
                    tooltip.style.left = (params.event.offsetX + 10) + 'px';
                    tooltip.style.top = (params.event.offsetY - 10) + 'px';
                    tooltip.classList.add('show');
                } else {
                    tooltip.classList.remove('show');
                }
            }

            formatTooltipContent(chartId, params) {
                const data = params.data;
                if (Array.isArray(data)) {
                    // K线数据格式
                    return `
                        <div><strong>日期:</strong> ${data[0]}</div>
                        <div><strong>开盘:</strong> ${data[1]}</div>
                        <div><strong>收盘:</strong> ${data[2]}</div>
                        <div><strong>最低:</strong> ${data[3]}</div>
                        <div><strong>最高:</strong> ${data[4]}</div>
                    `;
                } else {
                    // 普通数据格式
                    return `
                        <div><strong>${params.seriesName}:</strong> ${data.value}</div>
                        <div><strong>时间:</strong> ${data.name}</div>
                    `;
                }
            }

            syncChartCursor(sourceChartId, params) {
                // 同步其他图表的光标位置
                this.syncedCharts.forEach(chartId => {
                    if (chartId !== sourceChartId) {
                        const chartData = this.charts.get(chartId);
                        if (chartData && params.dataIndex !== undefined) {
                            chartData.instance.dispatchAction({
                                type: 'showTip',
                                seriesIndex: 0,
                                dataIndex: params.dataIndex
                            });
                        }
                    }
                });
            }

            toggleChartSync(chartId) {
                if (this.syncedCharts.has(chartId)) {
                    this.syncedCharts.delete(chartId);
                    this.hideSyncIndicator(chartId);
                } else {
                    this.syncedCharts.add(chartId);
                    this.showSyncIndicator(chartId);
                }
            }

            showSyncIndicator(chartId) {
                const indicator = document.getElementById(chartId + 'Sync');
                if (indicator) {
                    indicator.classList.add('active');
                }
            }

            hideSyncIndicator(chartId) {
                const indicator = document.getElementById(chartId + 'Sync');
                if (indicator) {
                    indicator.classList.remove('active');
                }
            }

            toggleCrosshair(chartId) {
                if (this.crosshairEnabled.has(chartId)) {
                    this.crosshairEnabled.delete(chartId);
                    this.hideCrosshair(chartId);
                } else {
                    this.crosshairEnabled.add(chartId);
                    this.showCrosshair(chartId);
                }
            }

            showCrosshair(chartId) {
                const chartData = this.charts.get(chartId);
                if (chartData) {
                    const crosshair = chartData.container.querySelector('.chart-crosshair');
                    if (crosshair) {
                        crosshair.style.display = 'block';
                    }
                }
            }

            hideCrosshair(chartId) {
                const chartData = this.charts.get(chartId);
                if (chartData) {
                    const crosshair = chartData.container.querySelector('.chart-crosshair');
                    if (crosshair) {
                        crosshair.style.display = 'none';
                    }
                }
            }

            toggleIndicatorOverlay(chartId, indicatorType) {
                const chartData = this.charts.get(chartId);
                if (!chartData) return;

                if (chartData.overlays.has(indicatorType)) {
                    this.removeIndicatorOverlay(chartId, indicatorType);
                } else {
                    this.addIndicatorOverlay(chartId, indicatorType);
                }
            }

            addIndicatorOverlay(chartId, indicatorType) {
                const chartData = this.charts.get(chartId);
                if (!chartData) return;

                // 生成指标数据
                const indicatorData = this.generateIndicatorData(indicatorType);

                // 添加到图表
                chartData.instance.setOption({
                    series: [{
                        name: indicatorType.toUpperCase(),
                        type: 'line',
                        data: indicatorData,
                        lineStyle: {
                            color: this.getIndicatorColor(indicatorType),
                            width: 1
                        },
                        symbol: 'none'
                    }]
                });

                chartData.overlays.add(indicatorType);
                this.updateIndicatorToggle(chartId, indicatorType, true);
            }

            removeIndicatorOverlay(chartId, indicatorType) {
                const chartData = this.charts.get(chartId);
                if (!chartData) return;

                // 从图表中移除指标
                const option = chartData.instance.getOption();
                const newSeries = option.series.filter(s => s.name !== indicatorType.toUpperCase());

                chartData.instance.setOption({
                    series: newSeries
                });

                chartData.overlays.delete(indicatorType);
                this.updateIndicatorToggle(chartId, indicatorType, false);
            }

            updateIndicatorToggle(chartId, indicatorType, active) {
                const toggle = document.querySelector(`[data-indicator="${indicatorType}"]`);
                if (toggle) {
                    if (active) {
                        toggle.classList.add('active');
                    } else {
                        toggle.classList.remove('active');
                    }
                }
            }

            generateIndicatorData(indicatorType) {
                // 模拟生成指标数据
                const data = [];
                const baseValue = 100;

                for (let i = 0; i < 120; i++) {
                    let value;
                    switch (indicatorType) {
                        case 'ma5':
                            value = baseValue + Math.sin(i * 0.1) * 10;
                            break;
                        case 'ma10':
                            value = baseValue + Math.sin(i * 0.05) * 15;
                            break;
                        case 'ma20':
                            value = baseValue + Math.sin(i * 0.025) * 20;
                            break;
                        default:
                            value = baseValue + Math.random() * 20 - 10;
                    }
                    data.push(value.toFixed(2));
                }

                return data;
            }

            getIndicatorColor(indicatorType) {
                const colors = {
                    'ma5': '#ff6b6b',
                    'ma10': '#4ecdc4',
                    'ma20': '#45b7d1',
                    'boll': '#96ceb4'
                };
                return colors[indicatorType] || '#888888';
            }

            resizeAllCharts() {
                this.charts.forEach((chartData) => {
                    if (chartData.instance && chartData.instance.resize) {
                        chartData.instance.resize();
                    }
                });
            }

            debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }
        }

        // 全局函数和工具函数 - 增强版
        function toggleTheme() {
            if (app.currentTheme === 'dark') {
                document.body.classList.add('light-theme');
                app.currentTheme = 'light';
                const themeIcon = document.getElementById('themeIcon');
                if (themeIcon) {
                    themeIcon.className = 'fas fa-sun w-5';
                }
                app.showNotification('已切换到浅色主题', 'success');
            } else {
                document.body.classList.remove('light-theme');
                app.currentTheme = 'dark';
                const themeIcon = document.getElementById('themeIcon');
                if (themeIcon) {
                    themeIcon.className = 'fas fa-moon w-5';
                }
                app.showNotification('已切换到深色主题', 'success');
            }

            // 保存主题偏好
            app.saveUserPreferences();

            // 重新渲染图表以适应新主题
            app.refreshAllCharts();
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                sidebar.classList.toggle('collapsed');

                // 调整图表大小
                setTimeout(() => {
                    if (app && app.charts) {
                        Object.values(app.charts).forEach(chart => {
                            if (chart && chart.resize) {
                                chart.resize();
                            }
                        });
                    }
                }, 300);
            }
        }

        // 增强的股票分析加载函数
        async function loadStockAnalysis() {
            const stockCode = document.getElementById('stockSelector').value;
            if (!stockCode) {
                app.showNotification('请先选择股票', 'warning');
                return;
            }

            try {
                app.showLoading('stock-analysis');
                await app.selectStock(stockCode);
                app.showNotification('股票分析数据加载完成', 'success');
            } catch (error) {
                console.error('加载股票分析失败:', error);
                app.showNotification('股票分析数据加载失败', 'error');
            } finally {
                app.hideLoading('stock-analysis');
            }
        }

        // 增强的自选股管理
        function showAddWatchlistModal() {
            const modal = document.getElementById('addWatchlistModal');
            if (modal) {
                modal.classList.remove('hidden');
                modal.classList.add('scale-in');

                // 聚焦到股票代码输入框
                const codeInput = document.getElementById('newWatchlistCode');
                if (codeInput) {
                    setTimeout(() => codeInput.focus(), 100);
                }
            }
        }

        function hideAddWatchlistModal() {
            const modal = document.getElementById('addWatchlistModal');
            if (modal) {
                modal.classList.add('hidden');
                modal.classList.remove('scale-in');

                // 清空输入框
                const codeInput = document.getElementById('newWatchlistCode');
                const nameInput = document.getElementById('newWatchlistName');
                if (codeInput) codeInput.value = '';
                if (nameInput) nameInput.value = '';
            }
        }

        async function addToWatchlist() {
            const code = document.getElementById('newWatchlistCode').value.trim();
            const name = document.getElementById('newWatchlistName').value.trim();

            // 验证输入
            if (!app.validateStockCode(code)) {
                app.showNotification('请输入正确的6位股票代码', 'error');
                return;
            }

            if (!app.validateStockName(name)) {
                app.showNotification('请输入有效的股票名称', 'error');
                return;
            }

            // 检查是否已存在
            if (app.watchlistData.find(stock => stock.code === code)) {
                app.showNotification('该股票已在自选股中', 'warning');
                return;
            }

            try {
                // 尝试从API获取股票信息
                let stockInfo;
                try {
                    stockInfo = await app.getStockDetail(code);
                } catch (error) {
                    // 如果API失败，创建模拟数据
                    stockInfo = {
                        code: code,
                        name: name,
                        industry: '未知',
                        market: code.startsWith('6') ? 'SH' : 'SZ',
                        currentPrice: Math.random() * 50 + 10,
                        prevClose: Math.random() * 50 + 10,
                        volume: Math.floor(Math.random() * 50000000),
                        turnover: Math.floor(Math.random() * 1000000000)
                    };

                    // 计算涨跌
                    stockInfo.changeAmount = stockInfo.currentPrice - stockInfo.prevClose;
                    stockInfo.changePercent = (stockInfo.changeAmount / stockInfo.prevClose) * 100;
                }

                // 添加到数据中
                app.stockData.push(stockInfo);
                app.watchlistData.push(stockInfo);

                // 保存到本地存储
                app.saveToLocalStorage('watchlist', app.watchlistData);

                // 重新渲染相关界面
                app.renderWatchlist();
                app.renderStockDataTable();
                app.updateStockSelector();

                hideAddWatchlistModal();
                app.showNotification(`${name} (${code}) 添加成功`, 'success');

            } catch (error) {
                console.error('添加自选股失败:', error);
                app.showNotification('添加自选股失败，请重试', 'error');
            }
        }

        // 增强的股票筛选功能
        function filterStocks() {
            const searchTerm = document.getElementById('stockSearchInput').value.toLowerCase();
            const industry = document.getElementById('industryFilter').value;
            const market = document.getElementById('marketFilter').value;

            let filteredData = [...app.stockData];

            if (searchTerm) {
                filteredData = filteredData.filter(stock =>
                    stock.code.toLowerCase().includes(searchTerm) ||
                    stock.name.toLowerCase().includes(searchTerm)
                );
            }

            if (industry) {
                filteredData = filteredData.filter(stock => stock.industry === industry);
            }

            if (market) {
                filteredData = filteredData.filter(stock => stock.market === market);
            }

            // 更新表格显示
            app.renderStockDataTable(filteredData);
            app.showNotification(`找到 ${filteredData.length} 只股票`, 'info');
        }

        // 导出数据功能
        function exportStockData() {
            try {
                const data = app.stockData.map(stock => ({
                    代码: stock.code,
                    名称: stock.name,
                    行业: stock.industry,
                    市场: stock.market,
                    现价: stock.currentPrice,
                    涨跌幅: stock.changePercent + '%',
                    成交量: stock.volume,
                    成交额: stock.turnover
                }));

                const csv = [
                    Object.keys(data[0]).join(','),
                    ...data.map(row => Object.values(row).join(','))
                ].join('\n');

                const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `股票数据_${new Date().toISOString().split('T')[0]}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                app.showNotification('数据导出成功', 'success');
            } catch (error) {
                console.error('导出数据失败:', error);
                app.showNotification('数据导出失败', 'error');
            }
        }

        // 刷新数据功能
        async function refreshStockData() {
            try {
                app.showLoading('refresh-data');

                // 模拟数据刷新
                await app.delay(1000);

                // 更新股票数据
                app.stockData.forEach(stock => {
                    const changeRate = (Math.random() - 0.5) * 0.02;
                    stock.currentPrice = Math.max(0.01, stock.currentPrice * (1 + changeRate));
                    stock.changeAmount = stock.currentPrice - stock.prevClose;
                    stock.changePercent = (stock.changeAmount / stock.prevClose) * 100;
                    stock.volume += Math.floor(Math.random() * 100000);
                    stock.turnover = stock.volume * stock.currentPrice;
                });

                // 重新渲染界面
                app.renderDashboard();
                app.renderWatchlist();
                app.renderStockDataTable();

                app.showNotification('数据刷新完成', 'success');
            } catch (error) {
                console.error('刷新数据失败:', error);
                app.showNotification('数据刷新失败', 'error');
            } finally {
                app.hideLoading('refresh-data');
            }
        }

        // 高级交互功能的全局函数

        // 多选操作相关
        function batchAddToWatchlist() {
            const selectedItems = app.multiSelectManager.getSelectedItems();
            if (selectedItems.length === 0) {
                app.showNotification('请先选择股票', 'warning');
                return;
            }

            selectedItems.forEach(itemId => {
                const stock = app.stockData.find(s => s.code === itemId);
                if (stock && !app.watchlistData.find(w => w.code === stock.code)) {
                    app.watchlistData.push(stock);
                }
            });

            app.renderWatchlist();
            app.multiSelectManager.clearSelection();
            app.showNotification(`已添加 ${selectedItems.length} 只股票到自选股`, 'success');
        }

        function batchRemoveFromWatchlist() {
            const selectedItems = app.multiSelectManager.getSelectedItems();
            if (selectedItems.length === 0) {
                app.showNotification('请先选择股票', 'warning');
                return;
            }

            selectedItems.forEach(itemId => {
                const index = app.watchlistData.findIndex(stock => stock.code === itemId);
                if (index !== -1) {
                    app.watchlistData.splice(index, 1);
                }
            });

            app.renderWatchlist();
            app.multiSelectManager.clearSelection();
            app.showNotification(`已删除 ${selectedItems.length} 只股票`, 'success');
        }

        function exportSelectedStocks() {
            const selectedItems = app.multiSelectManager.getSelectedItems();
            if (selectedItems.length === 0) {
                app.showNotification('请先选择股票', 'warning');
                return;
            }

            const selectedStocks = app.stockData.filter(stock =>
                selectedItems.includes(stock.code)
            );

            const data = selectedStocks.map(stock => ({
                代码: stock.code,
                名称: stock.name,
                现价: stock.currentPrice,
                涨跌幅: stock.changePercent + '%',
                成交量: stock.volume
            }));

            const csv = [
                Object.keys(data[0]).join(','),
                ...data.map(row => Object.values(row).join(','))
            ].join('\n');

            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `选中股票_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            app.showNotification('选中股票导出成功', 'success');
        }

        function clearSelection() {
            app.multiSelectManager.clearSelection();
        }

        // 图表交互功能
        function toggleChartZoom(chartId) {
            // 切换图表缩放模式
            app.chartEnhancer.toggleZoomMode(chartId);
        }

        function toggleChartPan(chartId) {
            // 切换图表平移模式
            app.chartEnhancer.togglePanMode(chartId);
        }

        function toggleChartCrosshair(chartId) {
            // 切换十字线
            app.chartEnhancer.toggleCrosshair(chartId);
        }

        function toggleChartFullscreen(chartId) {
            // 切换全屏模式
            const container = document.getElementById(chartId).closest('.card');
            if (container.classList.contains('chart-fullscreen')) {
                container.classList.remove('chart-fullscreen');
                document.body.style.overflow = '';
            } else {
                container.classList.add('chart-fullscreen');
                document.body.style.overflow = 'hidden';
            }
        }

        function saveChartImage(chartId) {
            // 保存图表为图片
            const chartData = app.chartEnhancer.charts.get(chartId);
            if (chartData && chartData.instance) {
                const url = chartData.instance.getDataURL({
                    pixelRatio: 2,
                    backgroundColor: '#fff'
                });

                const link = document.createElement('a');
                link.download = `${chartId}_${new Date().toISOString().split('T')[0]}.png`;
                link.href = url;
                link.click();

                app.showNotification('图表已保存', 'success');
            }
        }

        function toggleIndicatorOverlay(chartId, indicatorType) {
            // 切换技术指标叠加
            app.chartEnhancer.toggleIndicatorOverlay(chartId, indicatorType);
        }

        // 交易相关功能
        function tradeStock(stockCode) {
            app.switchTab('trading');
            const tradingSelector = document.getElementById('tradingStockSelector');
            if (tradingSelector) {
                tradingSelector.value = stockCode;
                app.selectTradingStock(stockCode);
            }
        }

        function selectFromWatchlist() {
            app.showStockSelectModal();
        }

        function switchTradeType(type) {
            app.currentTradeType = type;

            // 更新按钮状态
            document.querySelectorAll('.buy-btn, .sell-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            if (type === 'buy') {
                document.querySelector('.buy-btn').classList.add('active');
                document.getElementById('tradeSubmitText').textContent = '买入股票';
            } else {
                document.querySelector('.sell-btn').classList.add('active');
                document.getElementById('tradeSubmitText').textContent = '卖出股票';
            }

            app.updateTradeButton();
        }

        function updatePriceType() {
            const priceType = document.getElementById('priceType').value;
            const priceInput = document.getElementById('tradePrice');

            if (priceType === 'market') {
                priceInput.disabled = true;
                priceInput.placeholder = '市价';
                if (app.selectedTradingStock) {
                    priceInput.value = app.selectedTradingStock.currentPrice;
                }
            } else {
                priceInput.disabled = false;
                priceInput.placeholder = '请输入价格';
            }

            app.calculateTradeAmount();
        }

        function calculateTradeAmount() {
            app.calculateTradeAmount();
        }

        function setQuickQuantity(quantity) {
            document.getElementById('tradeQuantity').value = quantity;
            app.calculateTradeAmount();
        }

        function showTradeConfirmModal() {
            app.showTradeConfirmModal();
        }

        function hideTradeConfirmModal() {
            document.getElementById('tradeConfirmModal').classList.add('hidden');
        }

        function confirmTrade() {
            app.executeTrade();
        }

        function showStockSelectModal() {
            app.showStockSelectModal();
        }

        function hideStockSelectModal() {
            document.getElementById('stockSelectModal').classList.add('hidden');
        }

        function selectStockForTrading(stockCode) {
            const tradingSelector = document.getElementById('tradingStockSelector');
            if (tradingSelector) {
                tradingSelector.value = stockCode;
                app.selectTradingStock(stockCode);
            }
            hideStockSelectModal();
        }

        function filterStockSelect() {
            const searchTerm = document.getElementById('stockSelectSearch').value.toLowerCase();
            const tbody = document.getElementById('stockSelectTable');

            const filteredStocks = app.stockData.filter(stock =>
                stock.code.toLowerCase().includes(searchTerm) ||
                stock.name.toLowerCase().includes(searchTerm)
            );

            tbody.innerHTML = filteredStocks.map(stock => {
                const changeClass = app.getPriceClass(stock.changePercent);
                return `
                    <tr class="hover:bg-gray-700 cursor-pointer" onclick="selectStockForTrading('${stock.code}')">
                        <td class="mono-font">${stock.code}</td>
                        <td>${stock.name}</td>
                        <td class="mono-font">${app.formatNumber(stock.currentPrice)}</td>
                        <td class="mono-font ${changeClass}">${app.formatPercent(stock.changePercent)}%</td>
                        <td>
                            <button class="btn-primary text-sm px-3 py-1">选择</button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // 持仓相关功能
        function addPosition(stockCode) {
            app.switchTab('trading');
            const tradingSelector = document.getElementById('tradingStockSelector');
            if (tradingSelector) {
                tradingSelector.value = stockCode;
                app.selectTradingStock(stockCode);
                switchTradeType('buy');
            }
        }

        function sellPosition(stockCode) {
            app.switchTab('trading');
            const tradingSelector = document.getElementById('tradingStockSelector');
            if (tradingSelector) {
                tradingSelector.value = stockCode;
                app.selectTradingStock(stockCode);
                switchTradeType('sell');

                // 自动填入持仓数量
                const position = app.positionsData.find(p => p.code === stockCode);
                if (position) {
                    document.getElementById('tradeQuantity').value = position.quantity;
                    app.calculateTradeAmount();
                }
            }
        }

        function viewPositionDetail(stockCode) {
            app.switchTab('analysis');
            const stockSelector = document.getElementById('stockSelector');
            if (stockSelector) {
                stockSelector.value = stockCode;
                app.selectStock(stockCode);
            }
        }

        function refreshPositions() {
            app.updatePositionPrices();
            app.renderPositions();
            app.showNotification('持仓数据已刷新', 'success');
        }

        // 股票操作相关
        function viewStockDetail(stockCode) {
            app.switchTab('analysis');
            const stockSelector = document.getElementById('stockSelector');
            if (stockSelector) {
                stockSelector.value = stockCode;
                app.selectStock(stockCode);
            }
        }

        function addToWatchlistQuick(stockCode) {
            const stock = app.stockData.find(s => s.code === stockCode);
            if (stock && !app.watchlistData.find(w => w.code === stockCode)) {
                app.watchlistData.push(stock);
                app.renderWatchlist();
                app.showNotification(`${stock.name} 已添加到自选股`, 'success');
            }
        }

        function removeFromWatchlist(stockCode) {
            const index = app.watchlistData.findIndex(stock => stock.code === stockCode);
            if (index !== -1) {
                const stock = app.watchlistData[index];
                app.watchlistData.splice(index, 1);
                app.renderWatchlist();
                app.showNotification(`${stock.name} 已移出自选股`, 'success');
            }
        }

        function loadStockAnalysis() {
            const stockCode = document.getElementById('stockSelector').value;
            if (stockCode) {
                app.selectStock(stockCode);
            } else {
                app.showNotification('请先选择股票', 'warning');
            }
        }

        function showAddWatchlistModal() {
            document.getElementById('addWatchlistModal').classList.remove('hidden');
        }

        function hideAddWatchlistModal() {
            document.getElementById('addWatchlistModal').classList.add('hidden');
            document.getElementById('newWatchlistCode').value = '';
            document.getElementById('newWatchlistName').value = '';
        }

        function addToWatchlist() {
            const code = document.getElementById('newWatchlistCode').value.trim();
            const name = document.getElementById('newWatchlistName').value.trim();

            if (!code || !name) {
                app.showNotification('请填写完整的股票信息', 'error');
                return;
            }

            // 检查是否已存在
            if (app.watchlistData.find(stock => stock.code === code)) {
                app.showNotification('该股票已在自选股中', 'warning');
                return;
            }

            // 添加新股票（模拟数据）
            const newStock = {
                code: code,
                name: name,
                industry: '未知',
                market: code.startsWith('6') ? 'SH' : 'SZ',
                currentPrice: Math.random() * 50 + 10,
                openPrice: Math.random() * 50 + 10,
                highPrice: Math.random() * 50 + 10,
                lowPrice: Math.random() * 50 + 10,
                prevClose: Math.random() * 50 + 10,
                changeAmount: (Math.random() - 0.5) * 2,
                changePercent: (Math.random() - 0.5) * 10,
                volume: Math.floor(Math.random() * 50000000),
                turnover: Math.floor(Math.random() * 1000000000)
            };

            // 计算涨跌
            newStock.changeAmount = newStock.currentPrice - newStock.prevClose;
            newStock.changePercent = (newStock.changeAmount / newStock.prevClose) * 100;

            app.stockData.push(newStock);
            app.watchlistData.push(newStock);
            app.renderWatchlist();
            app.renderStockDataTable();
            app.updateStockSelector();
            hideAddWatchlistModal();
            app.showNotification('股票添加成功', 'success');
        }

        function filterStocks() {
            const searchTerm = document.getElementById('stockSearchInput').value.toLowerCase();
            const industry = document.getElementById('industryFilter').value;
            const market = document.getElementById('marketFilter').value;

            let filteredData = app.stockData;

            if (searchTerm) {
                filteredData = filteredData.filter(stock =>
                    stock.code.toLowerCase().includes(searchTerm) ||
                    stock.name.toLowerCase().includes(searchTerm)
                );
            }

            if (industry) {
                filteredData = filteredData.filter(stock => stock.industry === industry);
            }

            if (market) {
                filteredData = filteredData.filter(stock => stock.market === market);
            }

            // 重新渲染表格（这里简化处理，实际应该更新表格内容）
            app.renderStockDataTable();
            app.showNotification(`找到 ${filteredData.length} 只股票`, 'info');
        }

        // 全局应用实例
        let app;

        // 初始化应用 - 简化版
        document.addEventListener('DOMContentLoaded', function() {
            try {
                console.log('开始初始化股票量化分析系统...');

                // 创建应用实例
                app = new StockAnalysisApp();
                window.app = app;

                // 添加基础错误处理
                window.addEventListener('error', function(event) {
                    console.error('全局错误:', event.error);
                });

                // 添加未处理的Promise拒绝处理
                window.addEventListener('unhandledrejection', function(event) {
                    console.error('未处理的Promise拒绝:', event.reason);
                    event.preventDefault();
                });

                console.log('股票量化分析系统初始化完成');

            } catch (error) {
                console.error('应用初始化失败:', error);

                // 显示错误页面
                const errorHtml = `
                    <div class="min-h-screen flex items-center justify-center bg-gray-900 text-white">
                        <div class="text-center">
                            <i class="fas fa-exclamation-triangle text-6xl text-red-500 mb-4"></i>
                            <h1 class="text-2xl font-bold mb-2">系统初始化失败</h1>
                            <p class="text-gray-400 mb-4">错误信息: ${error.message}</p>
                            <button onclick="location.reload()" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                                刷新页面
                            </button>
                        </div>
                    </div>
                `;

                document.body.innerHTML = errorHtml;
            }
        });

        // 窗口大小改变时重新调整图表 - 优化版
        const resizeHandler = app ? app.throttle(function() {
            if (app && app.charts) {
                Object.values(app.charts).forEach(chart => {
                    if (chart && chart.resize) {
                        try {
                            chart.resize();
                        } catch (error) {
                            console.error('图表调整大小失败:', error);
                        }
                    }
                });
            }
        }, 250) : function() {};

        window.addEventListener('resize', resizeHandler);

        // 页面卸载时清理资源 - 增强版
        window.addEventListener('beforeunload', function() {
            try {
                // 清理图表资源
                if (app && app.charts) {
                    Object.values(app.charts).forEach(chart => {
                        if (chart && chart.dispose) {
                            chart.dispose();
                        }
                    });
                }

                // 关闭WebSocket连接
                if (app && app.ws) {
                    app.ws.close();
                }

                // 清理定时器
                if (app && app.updateTimer) {
                    clearInterval(app.updateTimer);
                }

                // 保存用户偏好设置
                if (app && app.saveUserPreferences) {
                    app.saveUserPreferences();
                }

                console.log('资源清理完成');
            } catch (error) {
                console.error('资源清理失败:', error);
            }
        });

        // 页面可见性变化处理
        document.addEventListener('visibilitychange', function() {
            if (app) {
                if (document.hidden) {
                    // 页面隐藏时暂停实时更新
                    app.isRealTimeEnabled = false;
                    console.log('页面隐藏，暂停实时更新');
                } else {
                    // 页面显示时恢复实时更新
                    app.isRealTimeEnabled = true;
                    app.startRealTimeUpdates();
                    console.log('页面显示，恢复实时更新');
                }
            }
        });

        // 添加键盘快捷键提示
        function showKeyboardShortcuts() {
            const shortcuts = [
                { key: 'Ctrl/Cmd + K', desc: '打开搜索' },
                { key: 'Ctrl/Cmd + 1-5', desc: '切换标签页' },
                { key: 'ESC', desc: '关闭模态框' },
                { key: 'F5', desc: '刷新数据' }
            ];

            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
            modal.innerHTML = `
                <div class="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-white">键盘快捷键</h3>
                        <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-white">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="space-y-3">
                        ${shortcuts.map(shortcut => `
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300">${shortcut.desc}</span>
                                <kbd class="px-2 py-1 text-xs bg-gray-700 text-gray-300 rounded border border-gray-600">${shortcut.key}</kbd>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        // 系统状态管理
        class SystemStatusManager {
            constructor() {
                this.createStatusIndicator();
                this.startStatusMonitoring();
            }

            createStatusIndicator() {
                const statusDiv = document.createElement('div');
                statusDiv.id = 'systemStatus';
                statusDiv.className = 'fixed bottom-4 left-4 z-40';
                statusDiv.innerHTML = `
                    <div class="flex items-center space-x-2 bg-gray-800 rounded-lg px-3 py-2 border border-gray-700">
                        <div class="status-indicator status-online" id="apiStatus"></div>
                        <span class="text-sm text-gray-300" id="statusText">系统正常</span>
                        <div class="text-xs text-gray-500" id="lastUpdateTime">刚刚更新</div>
                    </div>
                `;
                document.body.appendChild(statusDiv);
            }

            updateStatus(status, message) {
                const apiStatus = document.getElementById('apiStatus');
                const statusText = document.getElementById('statusText');
                const lastUpdateTime = document.getElementById('lastUpdateTime');

                if (apiStatus) {
                    apiStatus.className = `status-indicator ${status === 'online' ? 'status-online' : 'status-offline'}`;
                }
                if (statusText) {
                    statusText.textContent = message;
                }
                if (lastUpdateTime) {
                    lastUpdateTime.textContent = new Date().toLocaleTimeString();
                }
            }

            startStatusMonitoring() {
                // 模拟API状态检查
                setInterval(() => {
                    // 这里可以添加实际的API健康检查
                    const isOnline = Math.random() > 0.1; // 90%在线率模拟
                    this.updateStatus(
                        isOnline ? 'online' : 'offline',
                        isOnline ? '系统正常' : '连接异常'
                    );
                }, 30000); // 每30秒检查一次
            }
        }

        // 性能监控
        class PerformanceMonitor {
            constructor() {
                this.startMonitoring();
            }

            startMonitoring() {
                // 监控页面加载性能
                window.addEventListener('load', () => {
                    const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
                    console.log(`页面加载时间: ${loadTime}ms`);

                    if (loadTime > 3000) {
                        this.showPerformanceWarning();
                    }
                });

                // 监控内存使用
                if ('memory' in performance) {
                    setInterval(() => {
                        const memory = performance.memory;
                        const usedMB = Math.round(memory.usedJSHeapSize / 1048576);
                        const limitMB = Math.round(memory.jsHeapSizeLimit / 1048576);

                        if (usedMB / limitMB > 0.8) {
                            console.warn(`内存使用率过高: ${usedMB}MB / ${limitMB}MB`);
                        }
                    }, 60000); // 每分钟检查一次
                }
            }

            showPerformanceWarning() {
                const warning = document.createElement('div');
                warning.className = 'fixed top-4 right-4 bg-yellow-600 text-white px-4 py-2 rounded-lg shadow-lg z-50';
                warning.innerHTML = `
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>页面加载较慢，建议刷新页面</span>
                        <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-yellow-200 hover:text-white">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
                document.body.appendChild(warning);

                // 5秒后自动消失
                setTimeout(() => {
                    if (warning.parentElement) {
                        warning.remove();
                    }
                }, 5000);
            }
        }

        // 添加帮助按钮到页面
        document.addEventListener('DOMContentLoaded', function() {
            // 创建帮助按钮
            const helpButton = document.createElement('button');
            helpButton.className = 'fixed bottom-4 right-4 w-12 h-12 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 transition-colors z-40';
            helpButton.innerHTML = '<i class="fas fa-question"></i>';
            helpButton.onclick = showKeyboardShortcuts;
            helpButton.title = '查看快捷键 (F1)';
            document.body.appendChild(helpButton);

            // 初始化系统状态管理
            window.systemStatus = new SystemStatusManager();

            // 初始化性能监控
            window.performanceMonitor = new PerformanceMonitor();

            // 添加错误边界处理
            window.addEventListener('error', function(event) {
                console.error('页面错误:', event.error);
                window.systemStatus.updateStatus('offline', '系统异常');
            });

            // 添加未处理的Promise拒绝处理
            window.addEventListener('unhandledrejection', function(event) {
                console.error('未处理的Promise拒绝:', event.reason);
                window.systemStatus.updateStatus('offline', '数据加载失败');
            });
        });
    </script>
</body>
</html>
