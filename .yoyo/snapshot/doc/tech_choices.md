# 量化系统技术选型确认文档

本文档详细记录项目技术选型的决策过程、理由及相关配置信息。

## 数据库方案

**决策**: SQLite -> MySQL

**理由**:
- 初期开发使用SQLite:
  - 零配置，即开即用
  - 单文件存储方便开发和测试
  - 支持事务和SQL标准
  - 无需额外服务进程
- 后期迁移至MySQL:
  - 更好的并发性能
  - 支持大量数据存储和复杂查询
  - 更完善的权限管理
  - 更强大的扩展性

**迁移策略**:
- 使用SQLAlchemy ORM抽象层，确保数据库无关性
- 采用Alembic管理数据库版本和迁移
- 测试环境使用SQLite，生产环境使用MySQL

**连接配置**:
```python
# SQLite配置
DATABASE_URL = "sqlite:///./quantization.db"

# MySQL配置（未来迁移）
# DATABASE_URL = "mysql+pymysql://user:password@localhost/quantization"
```

## 数据源

**决策**: 自定义API (http://api.mairui.club/hslt/list/{licence})

**理由**:
- 提供了所需的股票基本信息
- API接口简单明了，返回格式规范
- 验证通过licence参数，使用方便

**API详情**:
- 接口地址: http://api.mairui.club/hslt/list/{licence}
- 授权参数: licence = 88007827-85DD-4C2C-BADB-2491CA84441E
- 返回数据格式:
```json
[
    {
        "dm": "601398",  // 股票代码
        "mc": "工商银行",  // 股票名称
        "jys": "sh"      // 交易所，"sh"表示上证，"sz"表示深证
    },
    ...
]
```

**集成实现计划**:
1. 创建自定义API适配器类
2. 实现数据格式转换
3. 添加错误处理和重试机制
4. 配置定时任务获取数据
5. 增加API返回结果缓存机制

## API框架

**决策**: FastAPI

**理由**:
- 高性能：基于Starlette和Pydantic，性能优于Flask
- 自动文档：集成Swagger和ReDoc文档
- 类型安全：支持类型注解，减少运行时错误
- 异步支持：原生支持异步请求处理
- 验证系统：基于Pydantic的数据验证
- 简洁API设计：开发效率高

**配置示例**:
```python
from fastapi import FastAPI

app = FastAPI(
    title="股票量化分析系统",
    description="提供股票数据获取和技术指标计算的API服务",
    version="1.0.0"
)

# 添加CORS支持
from fastapi.middleware.cors import CORSMiddleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 可以设置为具体的前端域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

## 任务调度方案

**决策**: APScheduler

**理由**:
- 支持多种调度器：背景执行、定时任务、Cron表达式
- 可持久化任务：支持任务存储到数据库
- 易于集成：与FastAPI集成方便
- 灵活的任务管理：可动态添加、修改、删除任务
- 健壮的错误处理：支持任务失败重试和异常处理

**配置示例**:
```python
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger

# 创建调度器
scheduler = AsyncIOScheduler()

# 添加定时任务 - 每个工作日18:00更新数据
scheduler.add_job(
    update_stock_data,
    CronTrigger.from_crontab("0 18 * * 1-5"),  # 周一到周五每天18:00
    id="daily_stock_update",
    replace_existing=True
)

# 添加定时任务 - 每周日更新股票列表
scheduler.add_job(
    update_stock_list,
    CronTrigger.from_crontab("0 12 * * 0"),  # 每周日中午12:00
    id="weekly_stock_list_update",
    replace_existing=True
)
```

## 其他技术选型

### 1. ORM框架

**决策**: SQLAlchemy

**理由**:
- 功能完善的ORM和SQL表达式语言
- 支持多种数据库，便于迁移
- 丰富的查询功能和关系映射
- 支持Alembic进行数据库迁移

### 2. 数据处理

**决策**: pandas + pandas-ta

**理由**:
- pandas: 强大的数据分析工具，适合处理时间序列数据
- pandas-ta: 专为技术分析设计，提供常用指标计算

### 3. 缓存机制

**决策**: Redis (可选)

**理由**:
- 高性能的内存数据存储
- 支持多种数据结构
- 可设置过期时间，自动管理缓存生命周期
- 支持分布式环境

### 4. 配置管理

**决策**: Pydantic + python-dotenv

**理由**:
- Pydantic提供强类型配置验证
- python-dotenv支持从.env文件加载环境变量
- 类Spring Boot风格的配置管理

## 集成计划

1. 搭建基础项目结构
2. 配置数据库连接与ORM模型
3. 实现自定义API数据获取适配器
4. 设置调度任务进行数据更新
5. 开发FastAPI接口提供数据访问
6. 添加测试和文档
