"""
自定义API测试脚本

此脚本用于测试股票数据API的连接和数据获取功能。
执行此脚本将从API获取股票列表数据并展示。
"""

import requests
import json
from typing import List, Dict, Any
from app.core import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# API配置
API_URL = "http://api.mairui.club/hslt/list/{licence}"
LICENCE = "88007827-85DD-4C2C-BADB-2491CA84441E"

class StockAPI:
    """自定义股票API封装类"""
    
    def __init__(self, api_url: str, licence: str):
        self.api_url = api_url
        self.licence = licence
    
    def get_stock_list(self) -> List[Dict[str, Any]]:
        """
        获取股票列表
        
        Returns:
            List[Dict[str, Any]]: 股票列表数据
                [
                    {
                        "dm": "601398",  # 股票代码
                        "mc": "工商银行",  # 股票名称
                        "jys": "sh"      # 交易所，"sh"表示上证，"sz"表示深证
                    },
                    ...
                ]
        """
        url = self.api_url.format(licence=self.licence)
        logger.info(f"正在请求股票列表: {url}")
        
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()  # 检查HTTP错误
            
            data = response.json()
            logger.info(f"成功获取股票列表，共 {len(data)} 条数据")
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"请求失败: {e}")
            return []
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return []
    
    def convert_to_standard_format(self, stock_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        将API返回的数据转换为标准格式
        
        Args:
            stock_list: API返回的原始数据
            
        Returns:
            List[Dict[str, Any]]: 标准格式的股票数据
        """
        result = []
        for stock in stock_list:
            # 转换为标准格式
            standard_stock = {
                "code": stock["dm"],                    # 股票代码
                "name": stock["mc"],                    # 股票名称
                "exchange": stock["jys"].upper(),       # 交易所代码(SH/SZ)
                "full_code": f"{stock['jys'].upper()}{stock['dm']}"  # 完整代码(SH601398)
            }
            result.append(standard_stock)
        
        return result

def main():
    """主函数"""
    api = StockAPI(API_URL, LICENCE)
    
    # 获取股票列表
    stock_list = api.get_stock_list()
    
    if not stock_list:
        logger.error("未获取到股票列表数据")
        return
    
    # 显示前5条原始数据
    logger.info("原始数据示例(前5条):")
    for i, stock in enumerate(stock_list[:5]):
        logger.info(f"{i+1}. {stock}")
    
    # 转换为标准格式
    standard_stocks = api.convert_to_standard_format(stock_list)
    
    # 显示前5条标准格式数据
    logger.info("标准格式数据示例(前5条):")
    for i, stock in enumerate(standard_stocks[:5]):
        logger.info(f"{i+1}. {stock}")
    
    # 统计上证和深证股票数量
    sh_count = sum(1 for stock in standard_stocks if stock["exchange"] == "SH")
    sz_count = sum(1 for stock in standard_stocks if stock["exchange"] == "SZ")
    
    logger.info(f"股票总数: {len(standard_stocks)}")
    logger.info(f"上证股票: {sh_count}支")
    logger.info(f"深证股票: {sz_count}支")

if __name__ == "__main__":
    main()
