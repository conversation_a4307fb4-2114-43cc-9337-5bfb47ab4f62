import subprocess
import os
import signal
import sys
import platform
import time

# --- 配置 ---
# Python 后端项目的根目录 (run_dev.py 所在的目录)
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))

# 后端配置
BACKEND_COMMAND = "uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"
BACKEND_WORKING_DIR = PROJECT_ROOT # FastAPI 应用在项目根目录的 app 文件夹内

# 前端配置
FRONTEND_DIR_NAME = "frontend-app" # 前端项目的文件夹名
FRONTEND_WORKING_DIR = os.path.join(PROJECT_ROOT, FRONTEND_DIR_NAME)
FRONTEND_COMMAND = "npm run dev" # 使用 npm 而不是 pnpm

# --- 执行 ---
backend_process = None
frontend_process = None

def kill_proc_tree(pid, sig=signal.SIGTERM, include_parent=True, timeout=None, on_terminate=None):
    """Kill a process tree (including grandchildren) with signal
    "sig" and return a (gone, still_alive) tuple.
    "on_terminate", if specified, is a callback function which is
    called as soon as a child terminates.
    """
    assert pid != os.getpid(), "won't kill myself"
    parent = psutil.Process(pid)
    children = parent.children(recursive=True)
    if include_parent:
        children.append(parent)
    for p in children:
        try:
            p.send_signal(sig)
        except psutil.NoSuchProcess:
            pass
    gone, alive = psutil.wait_procs(children, timeout=timeout, callback=on_terminate)
    return (gone, alive)

try:
    print(f"启动后端服务于: {BACKEND_WORKING_DIR}")
    print(f"命令: {BACKEND_COMMAND}")
    # 使用 start_new_session=True (在非Windows上) 或 creationflags (在Windows上) 来确保子进程可以独立于父进程被杀死
    if platform.system() == "Windows":
        backend_process = subprocess.Popen(BACKEND_COMMAND, shell=True, cwd=BACKEND_WORKING_DIR, creationflags=subprocess.CREATE_NEW_PROCESS_GROUP)
    else:
        backend_process = subprocess.Popen(BACKEND_COMMAND, shell=True, cwd=BACKEND_WORKING_DIR, start_new_session=True)


    if not os.path.isdir(FRONTEND_WORKING_DIR):
        print(f"错误: 前端目录 '{FRONTEND_WORKING_DIR}' 不存在。")
        print(f"请先在 '{PROJECT_ROOT}' 下创建 '{FRONTEND_DIR_NAME}' 目录并初始化前端项目。")
        if backend_process and backend_process.poll() is None:
            print("正在终止已启动的后端服务...")
            if platform.system() == "Windows":
                subprocess.call(['taskkill', '/F', '/T', '/PID', str(backend_process.pid)])
            else:
                os.killpg(os.getpgid(backend_process.pid), signal.SIGTERM)
            backend_process.wait()
        sys.exit(1)

    print(f"启动前端开发服务器于: {FRONTEND_WORKING_DIR}")
    print(f"命令: {FRONTEND_COMMAND}")
    if platform.system() == "Windows":
        frontend_process = subprocess.Popen(FRONTEND_COMMAND, shell=True, cwd=FRONTEND_WORKING_DIR, creationflags=subprocess.CREATE_NEW_PROCESS_GROUP)
    else:
        frontend_process = subprocess.Popen(FRONTEND_COMMAND, shell=True, cwd=FRONTEND_WORKING_DIR, start_new_session=True)


    # 等待任一进程结束或Ctrl+C
    while True:
        if backend_process.poll() is not None:
            print("后端服务已停止。")
            break
        if frontend_process.poll() is not None:
            print("前端服务已停止。")
            break
        time.sleep(0.5) # 减少CPU占用

except KeyboardInterrupt:
    print("\n检测到 Ctrl+C，正在关闭所有服务...")

finally:
    # 尝试更优雅地终止进程组
    # 注意: psutil 不是标准库，如果环境没有需要额外安装 (pip install psutil)
    # 为了普适性，这里仍然使用 os.killpg 和 taskkill，但 psutil.Process(pid).children(recursive=True) 是更可靠的方式
    # 如果要使用 psutil，需要 import psutil

    if frontend_process and frontend_process.poll() is None:
        print("正在终止前端服务...")
        if platform.system() == "Windows":
            subprocess.call(['taskkill', '/F', '/T', '/PID', str(frontend_process.pid)])
        else:
            try:
                os.killpg(os.getpgid(frontend_process.pid), signal.SIGTERM)
                frontend_process.wait(timeout=5)
            except (ProcessLookupError, subprocess.TimeoutExpired):
                if frontend_process.poll() is None: # 如果还没结束，强制杀死
                    os.killpg(os.getpgid(frontend_process.pid), signal.SIGKILL)
        print("前端服务已终止。")

    if backend_process and backend_process.poll() is None:
        print("正在终止后端服务...")
        if platform.system() == "Windows":
            subprocess.call(['taskkill', '/F', '/T', '/PID', str(backend_process.pid)])
        else:
            try:
                os.killpg(os.getpgid(backend_process.pid), signal.SIGTERM)
                backend_process.wait(timeout=5)
            except (ProcessLookupError, subprocess.TimeoutExpired):
                 if backend_process.poll() is None: # 如果还没结束，强制杀死
                    os.killpg(os.getpgid(backend_process.pid), signal.SIGKILL)
        print("后端服务已终止。")
    print("所有服务已关闭。")
