import { defineConfig } from 'windicss/helpers'
import colors from 'windicss/colors'
import plugin from 'windicss/plugin'

export default defineConfig({
  darkMode: 'class',
  // 启用属性化模式，类似于 UnoCSS 的 presetAttributify
  attributify: true,
  // 扫描 Vue 单文件组件中的类名
  extract: {
    include: ['src/**/*.{vue,html,jsx,tsx}'],
  },
  theme: {
    extend: {
      colors: {
        'bg-primary': 'var(--bg-primary)',
        'bg-secondary': 'var(--bg-secondary)',
        'bg-tertiary': 'var(--bg-tertiary)',
        'bg-quaternary': 'var(--bg-quaternary)',
        'text-primary': 'var(--text-primary)',
        'text-secondary': 'var(--text-secondary)',
        'text-muted': 'var(--text-muted)',
        'text-accent': 'var(--text-accent)',
        'border-color': 'var(--border-color)',
        'border-light': 'var(--border-light)',
        'accent-primary': 'var(--accent-primary)',
        'accent-secondary': 'var(--accent-secondary)',
        'accent-tertiary': 'var(--accent-tertiary)',
        'price-up': 'var(--price-up)',
        'price-down': 'var(--price-down)',
        'price-flat': 'var(--price-flat)',
        'volume-color': 'var(--volume-color)',
      },
    },
  },
  shortcuts: {
    'btn-base': 'px-4 py-2 rounded-lg font-medium transition-all duration-300 cursor-pointer',
    'btn-primary': 'btn-base bg-accent-primary text-white hover:bg-accent-secondary',
    'btn-secondary': 'btn-base bg-bg-tertiary text-text-primary border border-border-color hover:bg-bg-quaternary',
    'card': 'bg-bg-secondary border border-border-color rounded-2xl shadow-md transition-all duration-300 hover:shadow-xl',
    'input-field': 'bg-bg-tertiary border border-border-color rounded-xl px-4 py-3 text-text-primary transition-all duration-300 focus:outline-none focus:border-accent-primary',
    'flex-center': 'flex items-center justify-center',
    'price-up': 'text-price-up font-semibold',
    'price-down': 'text-price-down font-semibold',
    'price-flat': 'text-price-flat font-medium',
  },  plugins: [
    // 添加自定义插件
    plugin(({ addComponents }) => {
      const newComponents = {
        '.glass-bg': {
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
        },
      }
      addComponents(newComponents)
    }),
  ],
})
