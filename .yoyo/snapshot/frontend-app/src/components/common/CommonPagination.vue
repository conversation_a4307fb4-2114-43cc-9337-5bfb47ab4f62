<template>
  <div 
    v-if="total > 0" 
    class="common-pagination-container"
  >
    <!-- 分页 -->
    <div class="flex items-center justify-between">
      <span v-if="showInfo" class="text-sm text-text-muted">
        显示 {{ (internalCurrentPage - 1) * internalPageSize + 1 }}-{{
          Math.min(internalCurrentPage * internalPageSize, total)
        }}
        条，共 {{ total }} 条记录
        <span v-if="showFiltered" class="ml-2 text-accent-primary">
          (已筛选)
        </span>
      </span>
      <div class="flex space-x-2">
        <button
          class="btn-secondary text-sm p-2"
          @click="prevPage"
          :disabled="internalCurrentPage <= 1"
        >
          上一页
        </button>
        <span class="flex space-x-1">
          <button
            v-for="page in visiblePages"
            :key="page"
            :class="[
              'text-sm p-2 min-w-8',
              page === internalCurrentPage ? 'btn-primary' : 'btn-secondary',
              page === '...' ? 'cursor-default' : ''
            ]"
            @click="page !== '...' ? goToPage(page) : null"
            :disabled="page === '...'"
          >
            {{ page }}
          </button>
        </span>
        <button
          class="btn-secondary text-sm p-2"
          @click="nextPage"
          :disabled="internalCurrentPage >= totalPages"
        >
          下一页
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// Props 定义
const props = defineProps({
  // 总记录数
  total: {
    type: Number,
    required: true,
    default: 0
  },
  // 当前页码
  currentPage: {
    type: Number,
    default: 1
  },
  // 每页显示条数
  pageSize: {
    type: Number,
    default: 20
  },
  // 是否显示分页信息
  showInfo: {
    type: Boolean,
    default: true
  },
  // 是否显示筛选标识
  showFiltered: {
    type: Boolean,
    default: false
  }
})

// Emits 定义
const emit = defineEmits([
  'update:currentPage',
  'update:pageSize',
  'page-change',
  'size-change',
  'current-change'
])

// 内部状态
const internalCurrentPage = ref(props.currentPage)
const internalPageSize = ref(props.pageSize)

// 监听外部属性变化
watch(() => props.currentPage, (newVal) => {
  if (newVal !== internalCurrentPage.value) {
    internalCurrentPage.value = newVal
  }
})

watch(() => props.pageSize, (newVal) => {
  if (newVal !== internalPageSize.value) {
    internalPageSize.value = newVal
  }
})

// 分页相关方法
const goToPage = (page) => {
  if (typeof page === "number" && page >= 1 && page <= totalPages.value) {
    internalCurrentPage.value = page
    emit('update:currentPage', page)
    emit('current-change', page)
    emit('page-change', { page, size: internalPageSize.value })
  }
}

const prevPage = () => {
  if (internalCurrentPage.value > 1) {
    const newPage = internalCurrentPage.value - 1
    internalCurrentPage.value = newPage
    emit('update:currentPage', newPage)
    emit('current-change', newPage)
    emit('page-change', { page: newPage, size: internalPageSize.value })
  }
}

const nextPage = () => {
  if (internalCurrentPage.value < totalPages.value) {
    const newPage = internalCurrentPage.value + 1
    internalCurrentPage.value = newPage
    emit('update:currentPage', newPage)
    emit('current-change', newPage)
    emit('page-change', { page: newPage, size: internalPageSize.value })
  }
}

// 保持兼容性的事件处理方法
const handleSizeChange = (size) => {
  internalPageSize.value = size
  internalCurrentPage.value = 1 // 改变页面大小时重置到第一页
  
  emit('update:pageSize', size)
  emit('update:currentPage', 1)
  emit('size-change', size)
  emit('page-change', { page: 1, size })
}

const handleCurrentChange = (page) => {
  goToPage(page)
}

// 计算总页数
const totalPages = computed(() => {
  return Math.max(1, Math.ceil(props.total / internalPageSize.value))
})

// 显示的页码
const visiblePages = computed(() => {
  const total = totalPages.value
  const current = internalCurrentPage.value
  const pages = []

  if (total <= 7) {
    // 总页数小于等于7，显示所有页码
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // 总页数大于7，显示部分页码
    if (current <= 4) {
      for (let i = 1; i <= 5; i++) {
        pages.push(i)
      }
      pages.push("...", total)
    } else if (current >= total - 3) {
      pages.push(1, "...")
      for (let i = total - 4; i <= total; i++) {
        pages.push(i)
      }
    } else {
      pages.push(1, "...")
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push(i)
      }
      pages.push("...", total)
    }
  }

  return pages.filter(
    (page) => page !== "..." || pages.indexOf(page) % 2 === 1
  )
})

// 暴露方法和属性给父组件
defineExpose({
  currentPage: internalCurrentPage,
  pageSize: internalPageSize,
  totalPages
})
</script>

<style lang="scss" scoped>
.common-pagination-container {
  margin-top: 1.5rem;
}

/* 按钮样式 */
.btn-primary {
  background: linear-gradient(
    135deg,
    var(--accent-primary),
    var(--accent-secondary)
  );
  color: white;
  padding: 0.625rem 1.25rem;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  border: none;
  cursor: pointer;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
}

.btn-primary:disabled {
  background: var(--bg-tertiary);
  color: var(--text-muted);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-secondary {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  padding: 0.625rem 1.25rem;
  border-radius: 0.5rem;
  border: 1px solid var(--border-color);
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.btn-secondary:hover {
  background-color: var(--bg-quaternary);
  border-color: var(--accent-primary);
}

.btn-secondary:disabled {
  background-color: var(--bg-secondary);
  color: var(--text-muted);
  cursor: not-allowed;
  border-color: var(--border-color);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .common-pagination-container {
    .flex {
      flex-direction: column;
      gap: 1rem;
      
      &:first-child {
        align-items: center;
      }
    }
  }
}
</style>