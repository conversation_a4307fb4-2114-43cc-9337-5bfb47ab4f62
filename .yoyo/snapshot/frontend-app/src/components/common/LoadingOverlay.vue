<template>
  <teleport to="body">
    <div class="loading-overlay fixed inset-0 z-50 flex-center bg-black/20 backdrop-blur-sm">
      <div class="loading-spinner glass-card p-8 rounded-2xl flex-center flex-col space-y-4">
        <div class="spinner w-12 h-12 border-4 border-accent-primary/20 border-t-accent-primary rounded-full animate-spin"></div>
        <p class="text-text-secondary font-medium">{{ message }}</p>
      </div>
    </div>
  </teleport>
</template>

<script setup>
defineProps({
  message: {
    type: String,
    default: '加载中...'
  }
})
</script>

<style scoped lang="scss">
.loading-overlay {
  animation: fadeIn 0.3s ease;
}

.loading-spinner {
  animation: scaleIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { 
    opacity: 0;
    transform: scale(0.9);
  }
  to { 
    opacity: 1;
    transform: scale(1);
  }
}
</style>
