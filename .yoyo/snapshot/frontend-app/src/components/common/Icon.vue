<!-- filepath: d:\workspace\quantization\frontend-app\src\components\common\Icon.vue -->
<template>
  <!-- 如果name包含 'i-' 前缀，直接作为CSS类使用 -->
  <i v-if="name.startsWith('i-')" :class="[name, className]" :style="{ fontSize: `${size || 24}px` }" v-bind="$attrs" />
  <!-- 否则使用Iconify图标 -->
  <Icon
    v-else
    :icon="`${collection}:${name}`"
    :class="className"
    :style="{ fontSize: `${size || 24}px` }"
    v-bind="$attrs"
  />
</template>

<script setup>
import { Icon } from '@iconify/vue'

defineProps({
  collection: {
    type: String,
    default: 'carbon'
  },
  name: {
    type: String,
    required: true
  },
  size: {
    type: [Number, String],
    default: null
  },
  className: {
    type: String,
    default: ''
  }
})
</script>
