<template>
  <div class="common-table-container">
    <!-- 表格 -->
    <el-table
      :data="data"
      :loading="loading"
      :empty-text="emptyText || (loading ? '加载中...' : '暂无数据')"
      :row-key="rowKey"
      :header-cell-class-name="headerCellClassName"
      :row-class-name="rowClassName"
      :max-height="maxHeight"
      :height="height"
      :border="border"
      :stripe="stripe"
      :show-header="showHeader"
      :highlight-current-row="highlightCurrentRow"
      @row-click="handleRowClick"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
      class="common-table"
      v-bind="$attrs"
    >
      <!-- 动态列渲染 -->
      <template v-for="column in columns" :key="column.prop || column.type">
        <!-- 选择列 -->
        <el-table-column
          v-if="column.type === 'selection'"
          :type="column.type"
          :width="column.width || 55"
          :fixed="column.fixed"
          :selectable="column.selectable"
        />
        
        <!-- 索引列 -->
        <el-table-column
          v-else-if="column.type === 'index'"
          :type="column.type"
          :width="column.width || 60"
          :fixed="column.fixed"
          :label="column.label || '序号'"
          :index="column.index"
        />
        
        <!-- 展开列 -->
        <el-table-column
          v-else-if="column.type === 'expand'"
          :type="column.type"
          :width="column.width || 55"
          :fixed="column.fixed"
        >
          <template #default="scope">
            <slot name="expand" :row="scope.row" :index="scope.$index" />
          </template>
        </el-table-column>
        
        <!-- 普通列 -->
        <el-table-column
          v-else
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth || (column.width ? undefined : 120)"
          :fixed="column.fixed"
          :align="column.align || 'left'"
          :header-align="column.headerAlign || column.align || 'left'"
          :sortable="column.sortable"
          :sort-method="column.sortMethod"
          :sort-by="column.sortBy"
          :sort-orders="column.sortOrders"
          :resizable="column.resizable !== false"
          :formatter="column.formatter"
          :show-overflow-tooltip="column.showOverflowTooltip !== false"
          :class-name="column.className"
          :label-class-name="column.labelClassName"
        >
          <!-- 自定义表头 -->
          <template v-if="column.headerSlot" #header="scope">
            <slot 
              :name="column.headerSlot" 
              :column="scope.column" 
              :index="scope.$index"
            />
          </template>
          
          <!-- 自定义单元格内容 -->
          <template #default="scope">
            <slot 
              v-if="column.slot" 
              :name="column.slot" 
              :row="scope.row" 
              :column="scope.column" 
              :index="scope.$index"
              :value="scope.row[column.prop]"
            />
            <span v-else-if="column.formatter">
              {{ column.formatter(scope.row, scope.column, scope.row[column.prop], scope.$index) }}
            </span>
            <span v-else>
              {{ scope.row[column.prop] }}
            </span>
          </template>
        </el-table-column>
      </template>
      
      <!-- 操作列 -->
      <el-table-column
        v-if="$slots.actions"
        label="操作"
        :width="actionsWidth || 120"
        :min-width="actionsMinWidth"
        :fixed="actionsFixed || 'right'"
        :align="actionsAlign || 'center'"
        class-name="actions-column"
      >
        <template #default="scope">
          <slot 
            name="actions" 
            :row="scope.row" 
            :index="scope.$index"
          />
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页器 -->
    <CommonPagination
      v-if="showPagination"
      :total="pagination.total"
      :current-page="currentPage"
      :page-size="currentPageSize"
      :page-sizes="pagination.pageSizes || [10, 20, 50, 100]"
      :layout="pagination.layout || 'total, sizes, prev, pager, next'"
      :pager-count="pagination.pagerCount || 7"
      :small="pagination.small"
      :background="pagination.background !== false"
      :disabled="loading"
      :show-info="true"
      @page-change="handlePageChange"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import CommonPagination from './CommonPagination.vue'

// Props 定义
const props = defineProps({
  // 表格数据
  data: {
    type: Array,
    default: () => []
  },
  // 列配置
  columns: {
    type: Array,
    required: true
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false
  },
  // 空数据文本
  emptyText: {
    type: String,
    default: ''
  },
  // 行标识
  rowKey: {
    type: [String, Function],
    default: 'id'
  },
  // 表格高度
  height: {
    type: [String, Number],
    default: undefined
  },
  // 最大高度
  maxHeight: {
    type: [String, Number],
    default: undefined
  },
  // 是否显示边框
  border: {
    type: Boolean,
    default: false
  },
  // 是否显示斑马纹
  stripe: {
    type: Boolean,
    default: false
  },
  // 是否显示表头
  showHeader: {
    type: Boolean,
    default: true
  },
  // 是否高亮当前行
  highlightCurrentRow: {
    type: Boolean,
    default: false
  },
  // 表头单元格类名
  headerCellClassName: {
    type: [String, Function],
    default: 'common-table-header'
  },
  // 行类名
  rowClassName: {
    type: [String, Function],
    default: 'common-table-row'
  },
  // 是否显示分页
  showPagination: {
    type: Boolean,
    default: true
  },
  // 分页配置
  pagination: {
    type: Object,
    default: () => ({
      total: 0,
      currentPage: 1,
      pageSize: 20,
      pageSizes: [10, 20, 50, 100],
      layout: 'total, sizes, prev, pager, next, jumper'
    })
  },
  // 操作列配置
  actionsWidth: {
    type: [String, Number],
    default: undefined
  },
  actionsMinWidth: {
    type: [String, Number],
    default: undefined
  },
  actionsFixed: {
    type: [String, Boolean],
    default: 'right'
  },
  actionsAlign: {
    type: String,
    default: 'center'
  }
})

// Emits 定义
const emit = defineEmits([
  'row-click',
  'selection-change',
  'sort-change',
  'page-change',
  'size-change'
])

// 分页数据
const currentPage = ref(props.pagination.currentPage || 1)
const currentPageSize = ref(props.pagination.pageSize || 20)

// 监听分页参数变化
watch(() => props.pagination.currentPage, (newVal) => {
  if (newVal !== currentPage.value) {
    currentPage.value = newVal
  }
})

watch(() => props.pagination.pageSize, (newVal) => {
  if (newVal !== currentPageSize.value) {
    currentPageSize.value = newVal
  }
})

// 事件处理
const handleRowClick = (row, column, event) => {
  emit('row-click', row, column, event)
}

const handleSelectionChange = (selection) => {
  emit('selection-change', selection)
}

const handleSortChange = (sortInfo) => {
  emit('sort-change', sortInfo)
}

const handlePageChange = ({ page, size }) => {
  currentPage.value = page
  currentPageSize.value = size
  emit('page-change', { page, size })
}

// 暴露方法给父组件
defineExpose({
  currentPage,
  currentPageSize
})
</script>

<style lang="scss" scoped>
.common-table-container {
  width: 100%;
}

/* Element Plus 表格自定义样式 */
:deep(.common-table) {
  background-color: transparent;
  
  .el-table__header-wrapper {
    background-color: var(--bg-tertiary);
  }
  
  .el-table__header {
    background-color: var(--bg-tertiary);
  }
  
  .common-table-header {
    background-color: var(--bg-tertiary) !important;
    color: var(--text-muted) !important;
    font-weight: 600 !important;
    font-size: 0.875rem !important;
    padding: 12px 0px !important;
  }
  
  .el-table__row {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    transition: background-color 0.3s ease;
    
    &:hover {
      background-color: var(--bg-tertiary) !important;
    }
    
    .el-table__cell {
      border-bottom: none;
      padding: 12px 0px;
    }
  }
  
  .el-table__empty-block {
    background-color: var(--bg-secondary);
  }
  
  .el-table__empty-text {
    color: var(--text-secondary);
  }
  
  // 操作列样式
  .actions-column {
    .el-table__cell {
      .el-button {
        &.is-text {
          color: var(--text-primary);
          
          &:hover {
            color: var(--accent-primary);
            background-color: transparent;
          }
        }
      }
    }
  }
}

/* Element Plus 加载动画自定义样式 */
:deep(.el-loading-mask) {
  background-color: rgba(var(--bg-secondary-rgb), 0.8);
  
  .el-loading-spinner {
    .path {
      stroke: var(--accent-primary);
    }
    
    .el-loading-text {
      color: var(--text-primary);
    }
  }
}
</style>