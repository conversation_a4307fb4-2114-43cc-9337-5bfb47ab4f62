<template>
  <div class="sidebar" :class="{ 'collapsed': uiStore.sidebarCollapsed }">
    <div class="sidebar-header h-80px px-4 flex items-center border-b border-border-color">      <!-- Logo 和标题 -->
      <div class="flex items-center space-x-3">        
        <div class="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center">
          <AppIcon name="chart-line" class="text-white text-lg" />
        </div>
        <div v-if="!uiStore.sidebarCollapsed" class="flex-1">
          <h1 class="text-lg font-bold text-text-primary">量化分析</h1>
          <p class="text-xs text-text-muted">专业版</p>
        </div>
      </div>
    </div>
    
    <!-- 导航菜单 -->    <nav class="sidebar-nav p-4">
      <div class="space-y-2">
        <router-link
          v-for="item in menuItems" 
          :key="item.name"
          :to="item.path"
          class="nav-item w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors"
          :class="{ 'active': $route.path === item.path }"
        >
          <AppIcon :name="item.iconName" class="w-5 text-sm" />
          <span v-if="!uiStore.sidebarCollapsed" class="font-medium">{{ item.label }}</span>
        </router-link>
      </div>
    </nav>      <!-- 侧边栏底部 -->    
    <div class="sidebar-footer p-4 mt-4 border-t border-border-color">
      <div class="space-y-2">
        <button
          @click="router.push('/settings')"
          class="nav-item w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors hover:bg-bg-tertiary"
          :class="{ 'active': $route.path === '/settings', 'justify-start': !uiStore.sidebarCollapsed, 'justify-center': uiStore.sidebarCollapsed }"
          title="设置"
        >
          <AppIcon name="settings" class="w-5 text-sm" />
          <span v-if="!uiStore.sidebarCollapsed" class="font-medium">设置</span>
        </button>
        <button
          @click="themeStore.toggleTheme"
          class="w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors hover:bg-bg-tertiary"
          :class="{ 'justify-start': !uiStore.sidebarCollapsed, 'justify-center': uiStore.sidebarCollapsed }"
          :title="themeStore.isDark ? '切换浅色模式' : '切换深色模式'"
        >            
          <AppIcon :name="themeStore.isDark ? 'light' : 'moon'" class="w-5 text-sm" />
          <span v-if="!uiStore.sidebarCollapsed" class="font-medium">
            {{ themeStore.isDark ? '浅色模式' : '深色模式' }}
          </span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue' // Import computed
import { useThemeStore, useUiStore } from '@/store'
import AppIcon from '@/components/common/Icon.vue'
import { routes } from '@/router' // Import routes from router
import { useRouter } from 'vue-router'; // Import useRouter

const themeStore = useThemeStore()
const uiStore = useUiStore()
const router = useRouter(); // Initialize router

// 菜单项 - 从路由动态生成
const menuItems = computed(() => {
  return routes
    .filter(route => (!route.meta || !route.meta.hidden) && route.path !== '/settings') // 过滤掉 hidden: true 和 /settings 的路由
    .map(route => ({
      name: route.name,
      label: route.meta && route.meta.title ? route.meta.title : '未命名', // 使用 meta.title 作为 label
      path: route.path,
      // 假设路由 meta 中有 icon 字段，或者根据 name/path 映射
      // 为了简单起见，这里使用一个默认图标或根据 path 简单判断
      iconName: route.meta && route.meta.icon ? route.meta.icon : getIconForRoute(route.path)
    }))
})

// 根据路由路径获取图标名称的辅助函数
// 您可以根据需要扩展此函数
function getIconForRoute(path) {
  if (path === '/') return 'chart-line' // 股票分析
  if (path === '/watchlist') return 'star'
  if (path === '/data') return 'data-base'
  // settings icon is handled directly in the template now
  return 'document' // 默认图标
}
</script>

<style scoped lang="scss">
.sidebar {
  width: 16rem;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  
  &.collapsed {
    width: 5rem; // Adjusted width for collapsed state to better fit icons

    .sidebar-footer .flex > * { // Ensure buttons in footer take full width when collapsed
        width: auto; // Allow icons to center
    }
  }
}

.sidebar-header {
  .bg-gradient-primary {
    background: var(--gradient-primary);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2);
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 6px 12px rgba(59, 130, 246, 0.3);
      transform: translateY(-1px);
    }
  }
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-item {
  &.active {
    background: var(--bg-tertiary);
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 20px;
      background: var(--accent-primary);
      border-radius: 0 2px 2px 0;
    }
  }
  
  &:hover:not(.active) {
    background: var(--bg-tertiary);
  }
}

.sidebar-footer {
  margin-top: 0;
  
  &.collapsed {
    .flex > * {
      flex-grow: 0; // Prevent growing when collapsed
    }
  }
}
</style>
