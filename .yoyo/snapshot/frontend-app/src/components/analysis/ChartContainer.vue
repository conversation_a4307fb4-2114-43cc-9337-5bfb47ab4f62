<template>
  <div class="chart-container" :style="{ height: height }">
    <!-- 图表容器 - 始终渲染 -->
    <div ref="chartRef" class="w-full h-full" :style="{ opacity: hasData && !loading && !error ? 1 : 0 }"></div>
    
    <!-- 错误状态 -->
    <div v-if="error" class="chart-overlay">
      <div class="text-center">
        <i class="i-carbon-warning text-4xl mb-3 text-red-400"></i>
        <p class="text-red-400 mb-3">{{ error }}</p>
        <button 
          v-if="showRetry"
          class="btn-primary px-4 py-2 text-sm"
          @click="$emit('retry')"
        >
          重新加载
        </button>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-else-if="!hasData && !loading" class="chart-overlay">
      <div class="text-center">
        <i :class="emptyIcon" class="text-4xl mb-3 opacity-50"></i>
        <p class="text-gray-400">{{ emptyText }}</p>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div v-else-if="loading" class="chart-overlay">
      <div class="text-center">
        <div class="animate-spin w-8 h-8 border-2 border-blue-400 border-t-transparent rounded-full mx-auto mb-3"></div>
        <p class="text-gray-400">{{ loadingText }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

const props = defineProps({
  height: {
    type: String,
    default: '400px'
  },
  loading: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: null
  },
  hasData: {
    type: Boolean,
    default: false
  },
  emptyText: {
    type: String,
    default: '暂无数据'
  },
  loadingText: {
    type: String,
    default: '加载中...'
  },
  emptyIcon: {
    type: String,
    default: 'i-carbon-chart-line text-blue-400'
  },
  showRetry: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['retry'])

const chartRef = ref()

defineExpose({
  chartRef
})
</script>

<style scoped lang="scss">
.chart-container {
  position: relative;
  background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: var(--accent-primary);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
  }
  
  // 添加微妙的纹理效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: 1;
  }
  
  // 确保内容在纹理上方
  > * {
    position: relative;
    z-index: 2;
  }
}

.chart-overlay {
  position: absolute !important;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(17, 24, 39, 0.8);
  backdrop-filter: blur(4px);
  z-index: 10;
}</style>
