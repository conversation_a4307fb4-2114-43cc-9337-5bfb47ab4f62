<template>
  <div class="indicator-settings">
    <el-dialog
      v-model="visible"
      title="技术指标参数设置"
      width="500px"
      :before-close="handleClose"
    >
      <div class="settings-content">
        <!-- MACD参数设置 -->
        <div v-if="indicatorType === 'macd'" class="setting-group">
          <h4 class="setting-title">MACD参数</h4>
          <div class="setting-row">
            <label>快线周期:</label>
            <el-input-number v-model="macdSettings.fastPeriod" :min="1" :max="100" />
          </div>
          <div class="setting-row">
            <label>慢线周期:</label>
            <el-input-number v-model="macdSettings.slowPeriod" :min="1" :max="200" />
          </div>
          <div class="setting-row">
            <label>信号线周期:</label>
            <el-input-number v-model="macdSettings.signalPeriod" :min="1" :max="100" />
          </div>
        </div>

        <!-- KDJ参数设置 -->
        <div v-if="indicatorType === 'kdj'" class="setting-group">
          <h4 class="setting-title">KDJ参数</h4>
          <div class="setting-row">
            <label>K值周期:</label>
            <el-input-number v-model="kdjSettings.kPeriod" :min="1" :max="100" />
          </div>
          <div class="setting-row">
            <label>D值周期:</label>
            <el-input-number v-model="kdjSettings.dPeriod" :min="1" :max="100" />
          </div>
          <div class="setting-row">
            <label>J值周期:</label>
            <el-input-number v-model="kdjSettings.jPeriod" :min="1" :max="100" />
          </div>
        </div>

        <!-- RSI参数设置 -->
        <div v-if="indicatorType === 'rsi'" class="setting-group">
          <h4 class="setting-title">RSI参数</h4>
          <div class="setting-row">
            <label>RSI周期:</label>
            <el-input-number v-model="rsiSettings.period" :min="1" :max="100" />
          </div>
        </div>

        <!-- 移动平均线参数设置 -->
        <div v-if="indicatorType === 'ma'" class="setting-group">
          <h4 class="setting-title">移动平均线参数</h4>
          <div class="setting-row">
            <label>MA5周期:</label>
            <el-input-number v-model="maSettings.ma5" :min="1" :max="100" />
          </div>
          <div class="setting-row">
            <label>MA10周期:</label>
            <el-input-number v-model="maSettings.ma10" :min="1" :max="100" />
          </div>
          <div class="setting-row">
            <label>MA20周期:</label>
            <el-input-number v-model="maSettings.ma20" :min="1" :max="100" />
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button @click="resetToDefault">重置默认</el-button>
          <el-button type="primary" @click="handleConfirm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  indicatorType: {
    type: String,
    default: 'macd'
  },
  currentSettings: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'confirm'])

const visible = ref(props.modelValue)

// 各指标的参数设置
const macdSettings = reactive({
  fastPeriod: 12,
  slowPeriod: 26,
  signalPeriod: 9
})

const kdjSettings = reactive({
  kPeriod: 9,
  dPeriod: 3,
  jPeriod: 3
})

const rsiSettings = reactive({
  period: 14
})

const maSettings = reactive({
  ma5: 5,
  ma10: 10,
  ma20: 20
})

// 默认参数
const defaultSettings = {
  macd: { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9 },
  kdj: { kPeriod: 9, dPeriod: 3, jPeriod: 3 },
  rsi: { period: 14 },
  ma: { ma5: 5, ma10: 10, ma20: 20 }
}

// 监听模态框显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    loadCurrentSettings()
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 加载当前设置
const loadCurrentSettings = () => {
  const current = props.currentSettings
  switch (props.indicatorType) {
    case 'macd':
      Object.assign(macdSettings, { ...defaultSettings.macd, ...current })
      break
    case 'kdj':
      Object.assign(kdjSettings, { ...defaultSettings.kdj, ...current })
      break
    case 'rsi':
      Object.assign(rsiSettings, { ...defaultSettings.rsi, ...current })
      break
    case 'ma':
      Object.assign(maSettings, { ...defaultSettings.ma, ...current })
      break
  }
}

// 重置为默认值
const resetToDefault = () => {
  switch (props.indicatorType) {
    case 'macd':
      Object.assign(macdSettings, defaultSettings.macd)
      break
    case 'kdj':
      Object.assign(kdjSettings, defaultSettings.kdj)
      break
    case 'rsi':
      Object.assign(rsiSettings, defaultSettings.rsi)
      break
    case 'ma':
      Object.assign(maSettings, defaultSettings.ma)
      break
  }
}

// 获取当前设置
const getCurrentSettings = () => {
  switch (props.indicatorType) {
    case 'macd':
      return { ...macdSettings }
    case 'kdj':
      return { ...kdjSettings }
    case 'rsi':
      return { ...rsiSettings }
    case 'ma':
      return { ...maSettings }
    default:
      return {}
  }
}

// 处理确认
const handleConfirm = () => {
  emit('confirm', {
    type: props.indicatorType,
    settings: getCurrentSettings()
  })
  visible.value = false
}

// 处理关闭
const handleClose = () => {
  visible.value = false
}
</script>

<style scoped lang="scss">
.indicator-settings {
  .settings-content {
    padding: 20px 0;
  }

  .setting-group {
    margin-bottom: 30px;

    .setting-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 15px;
      color: var(--text-primary);
      border-bottom: 2px solid var(--accent-primary);
      padding-bottom: 8px;
    }

    .setting-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 15px;
      padding: 0 10px;

      label {
        font-size: 14px;
        color: var(--text-secondary);
        min-width: 100px;
      }

      .el-input-number {
        width: 120px;
      }
    }
  }

  .dialog-footer {
    text-align: right;

    .el-button + .el-button {
      margin-left: 10px;
    }
  }
}
</style>
