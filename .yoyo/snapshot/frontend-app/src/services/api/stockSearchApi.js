import apiClient from '../apiClient'

/**
 * 股票搜索API服务
 * 提供股票搜索相关的API调用
 */
export const stockSearchApi = {
  /**
   * 搜索股票
   * @param {string} query - 搜索关键词
   * @param {Object} options - 搜索选项
   * @returns {Promise}
   */
  searchStocks(query, options = {}) {
    const params = {
      q: query,
      limit: options.limit || 20,
      ...options
    }
    return apiClient.get('/v1/stocks/search/search', { params })
  },

  /**
   * 快速搜索股票 (用于搜索建议)
   * @param {string} query - 搜索关键词
   * @param {number} limit - 返回数量限制
   * @returns {Promise}
   */
  quickSearchStocks(query, limit = 10) {
    return apiClient.get('/v1/stocks/search/quick-search', {
      params: { q: query, limit }
    })
  },

  /**
   * 获取热门股票列表
   * @param {Object} options - 筛选选项
   * @returns {Promise}
   */
  getPopularStocks(options = {}) {
    const params = {
      limit: options.limit || 10,
      ...options
    }
    return apiClient.get('/v1/stocks/search/popular', { params })
  }
}

export default stockSearchApi
