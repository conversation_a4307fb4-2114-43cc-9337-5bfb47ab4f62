<template>
  <div class="analysis-page">
    <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
      <!-- 左侧：股票选择和基本信息 -->
      <div class="xl:col-span-1">
        <!-- 股票选择面板 -->
        <div class="card p-6 mb-6">
          <h3 class="text-lg font-semibold mb-4">股票选择</h3>
          <div class="space-y-4">
            <div>
              <label class="block text-sm text-gray-400 mb-2">股票代码</label>
              <select
                v-model="selectedStock"
                class="input-field w-full"
                @change="onStockChange"
              >
                <option value="">选择股票</option>
                <optgroup
                  v-if="combinedPopularStocks.length > 0"
                  label="热门推荐"
                >
                  <option
                    v-for="stock in combinedPopularStocks"
                    :key="stock.code"
                    :value="stock.code"
                  >
                    {{ stock.code }} {{ stock.name }}
                    <span v-if="stock.searchCount > 0"
                      >({{ stock.searchCount }}次搜索)</span
                    >
                  </option>
                </optgroup>
              </select>
            </div>
            <!-- 时间周期已隐藏，默认使用日线 -->
            <!--
            <div>
              <label class="block text-sm text-gray-400 mb-2">时间周期</label>
              <select v-model="selectedPeriod" class="input-field w-full" @change="onPeriodChange">
                <option value="D">日线</option>
                <option value="W">周线</option>
                <option value="M">月线</option>
              </select>
            </div>
            -->

            <div>
              <label class="block text-sm text-gray-400 mb-2">时间范围</label>
              <select
                v-model="selectedRange"
                class="input-field w-full"
                @change="onRangeChange"
              >
                <option value="30">最近30天</option>
                <option value="60">最近60天</option>
                <option value="120">最近120天</option>
                <option value="250">最近250天</option>
              </select>
            </div>
            <button
              class="btn-primary w-full flex items-center justify-center"
              @click="loadStockAnalysis"
              :disabled="!selectedStock || loading"
            >
              <AppIcon name="chart-line" class="mr-2" />
              {{ loading ? "分析中..." : "开始分析" }}
            </button>
          </div>
        </div>

        <!-- 股票基本信息 -->
        <div v-if="stockInfo" class="card p-6">
          <h3 class="text-lg font-semibold mb-4">基本信息</h3>
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-gray-400">股票名称</span>
              <span>{{ stockInfo.name }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-400">股票代码</span>
              <span>{{ stockInfo.code }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-400">当前价格</span>
              <span
                :class="
                  stockInfo.changePercent >= 0
                    ? 'text-green-400'
                    : 'text-red-400'
                "
              >
                ¥{{ stockInfo.price }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-400">涨跌幅</span>
              <span
                :class="
                  stockInfo.changePercent >= 0
                    ? 'text-green-400'
                    : 'text-red-400'
                "
              >
                {{ stockInfo.changePercent >= 0 ? "+" : ""
                }}{{ stockInfo.changePercent }}%
              </span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-400">成交量</span>
              <span>{{ stockInfo.volume }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-400">市值</span>
              <span>{{ stockInfo.marketCap }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：图表区域 -->
      <div class="xl:col-span-3">
        <!-- K线图表 -->
        <div class="card p-6 mb-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">K线图表</h3>
            <div class="flex space-x-2">
              <button
                class="px-3 py-1 text-sm rounded"
                :class="
                  chartOverlay === 'none'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-400 hover:text-white'
                "
                @click="setChartOverlay('none')"
              >
                主图
              </button>
              <button
                class="px-3 py-1 text-sm rounded"
                :class="
                  chartOverlay === 'ma'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-400 hover:text-white'
                "
                @click="setChartOverlay('ma')"
              >
                均线
              </button>
              <button
                class="px-3 py-1 text-sm rounded"
                :class="
                  chartOverlay === 'bollinger'
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-400 hover:text-white'
                "
                @click="setChartOverlay('bollinger')"
              >
                布林带
              </button>
            </div>
          </div>
          <ChartContainer
            ref="klineChartContainer"
            height="500px"
            :loading="loading"
            :error="error"
            :has-data="!!chartData?.kline || !selectedStock"
            empty-text="请选择股票开始分析"
            loading-text="K线图表加载中..."
            empty-icon="i-carbon-chart-candlestick text-blue-400"
            @retry="loadStockAnalysis"
          />
        </div>

        <!-- 技术指标图表 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="card p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold">MACD指标</h3>
              <button
                class="text-sm text-blue-400 hover:text-blue-300"
                @click="showIndicatorSettings('macd')"
              >
                参数设置
              </button>
            </div>
            <ChartContainer
              ref="macdChartContainer"
              height="300px"
              :loading="loading"
              :error="error"
              :has-data="!!chartData?.macd || !selectedStock"
              empty-text="请选择股票开始分析"
              loading-text="MACD指标加载中..."
              empty-icon="i-carbon-chart-line text-purple-400"
              @retry="loadStockAnalysis"
            />
          </div>

          <div class="card p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold">成交量</h3>
              <button class="text-sm text-blue-400 hover:text-blue-300">
                详细分析
              </button>
            </div>
            <ChartContainer
              ref="volumeChartContainer"
              height="300px"
              :loading="loading"
              :error="error"
              :has-data="!!chartData?.volume || !selectedStock"
              empty-text="请选择股票开始分析"
              loading-text="成交量图表加载中..."
              empty-icon="i-carbon-chart-column text-green-400"
              @retry="loadStockAnalysis"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 指标参数设置对话框 -->
    <IndicatorSettings
      v-model="showSettingsDialog"
      :indicator-type="currentIndicatorType"
      :current-settings="currentIndicatorSettings"
      @confirm="handleIndicatorSettingsConfirm"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed, watch } from "vue";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import * as echarts from "echarts";
import AppIcon from "@/components/common/Icon.vue";
import ChartContainer from "@/components/analysis/ChartContainer.vue";
import IndicatorSettings from "@/components/analysis/IndicatorSettings.vue";
import { api } from "@/services/api";
import { searchStatsManager } from "@/utils/searchStats";

const route = useRoute();

// 选择状态
const selectedStock = ref("");
const selectedPeriod = ref("D");
const selectedRange = ref("30");
const chartOverlay = ref("none");

// 数据状态
const loading = ref(false);
const chartData = ref(null);
const stockInfo = ref(null);
const error = ref(null);

// ECharts实例
const klineChart = ref(null);
const macdChart = ref(null);
const volumeChart = ref(null);

// 热门股票列表 - 基于搜索统计和后端数据
const hotStocks = ref([]);

// 基于搜索统计的热门股票
const popularStocks = computed(() => {
  const searchStats = searchStatsManager.getPopularStocks(5);
  return searchStats;
});

// 合并热门推荐（优先显示搜索统计，再显示后端热门股票）
const combinedPopularStocks = computed(() => {
  const searchBased = popularStocks.value;
  const backendBased = hotStocks.value;

  // 合并并去重
  const combined = [...searchBased];
  const existingCodes = new Set(searchBased.map((stock) => stock.code));

  for (const stock of backendBased) {
    if (!existingCodes.has(stock.code)) {
      combined.push({
        code: stock.code,
        name: stock.name,
        industry: stock.industry || "未分类",
        searchCount: 0, // 后端热门股票没有搜索次数
      });
      if (combined.length >= 10) break; // 限制总数
    }
  }

  return combined;
});

// 获取后端热门股票
const loadHotStocks = async () => {
  try {
    const response = await api.stockSearch.getPopularStocks({ limit: 8 });
    hotStocks.value = response.data || [];
  } catch (error) {
    console.error("获取热门股票失败:", error);
    // 如果API失败，使用默认热门股票
    hotStocks.value = [
      { code: "000001", name: "平安银行", industry: "银行" },
      { code: "000002", name: "万科A", industry: "房地产" },
      { code: "000858", name: "五粮液", industry: "白酒" },
      { code: "002230", name: "科大讯飞", industry: "人工智能" },
      { code: "002415", name: "海康威视", industry: "安防" },
      { code: "600036", name: "招商银行", industry: "银行" },
      { code: "600519", name: "贵州茅台", industry: "白酒" },
      { code: "600887", name: "伊利股份", industry: "乳业" },
    ];
  }
};

// 图表容器引用
const klineChartContainer = ref(null);
const macdChartContainer = ref(null);
const volumeChartContainer = ref(null);

// 指标设置相关
const showSettingsDialog = ref(false);
const currentIndicatorType = ref("macd");
const currentIndicatorSettings = ref({});

// 事件处理函数
const onStockChange = (stockCode) => {
  // 如果传入的是事件对象，获取target.value
  if (typeof stockCode === 'object' && stockCode.target) {
    stockCode = stockCode.target.value;
  }
  
  if (!stockCode) {
    console.warn("No stock code provided");
    return;
  }
  
  console.log("Stock changed:", stockCode);
  selectedStock.value = stockCode;
  stockInfo.value = null;
  chartData.value = null;
  error.value = null;

  // 从热门股票列表中查找股票信息用于搜索统计
  const stockInfo = combinedPopularStocks.value.find(stock => stock.code === stockCode);
  if (stockInfo) {
    searchStatsManager.incrementSearchCount(
      stockInfo.code, 
      stockInfo.name, 
      stockInfo.industry || "未分类"
    );
  }

  if (selectedStock.value) {
    loadStockAnalysis();
  }
};

// 通用的股票加载函数
const loadStockByCode = async (stockCode, stockName = null, industry = null) => {
  if (!stockCode) {
    console.warn("No stock code provided");
    return;
  }
  
  console.log("Loading stock:", stockCode, stockName);
  
  // 更新状态
  selectedStock.value = stockCode;
  stockInfo.value = null;
  chartData.value = null;
  error.value = null;
  
  // 如果没有提供股票名称，尝试从热门股票列表中查找
  if (!stockName) {
    const stockInfo = combinedPopularStocks.value.find(stock => stock.code === stockCode);
    if (stockInfo) {
      stockName = stockInfo.name;
      industry = stockInfo.industry || "未分类";
    }
  }
  
  // 记录搜索统计
  if (stockName) {
    searchStatsManager.incrementSearchCount(stockCode, stockName, industry || "未分类");
  }
  
  // 加载分析数据
  await loadStockAnalysis();
};

const onPeriodChange = () => {
  console.log("Period changed:", selectedPeriod.value);
  if (selectedStock.value) {
    loadStockAnalysis();
  }
};

const onRangeChange = () => {
  console.log("Range changed:", selectedRange.value);
  if (selectedStock.value) {
    loadStockAnalysis();
  }
};

const setChartOverlay = (overlay) => {
  chartOverlay.value = overlay;
  console.log("Chart overlay changed:", overlay);
  // 重新渲染K线图表以应用叠加层
  if (chartData.value && klineChart.value) {
    renderKlineChart();
  }
};

// 显示指标参数设置
const showIndicatorSettings = (indicatorType) => {
  currentIndicatorType.value = indicatorType;
  currentIndicatorSettings.value = getIndicatorSettings(indicatorType);
  showSettingsDialog.value = true;
};

// 获取指标当前设置
const getIndicatorSettings = (indicatorType) => {
  // 这里可以从localStorage或其他地方获取用户保存的设置
  const savedSettings = localStorage.getItem(
    `indicator_settings_${indicatorType}`
  );
  return savedSettings ? JSON.parse(savedSettings) : {};
};

// 处理指标设置确认
const handleIndicatorSettingsConfirm = (data) => {
  console.log("Indicator settings confirmed:", data);
  // 保存设置到localStorage
  localStorage.setItem(
    `indicator_settings_${data.type}`,
    JSON.stringify(data.settings)
  );

  // 重新加载对应的指标数据
  if (selectedStock.value) {
    // 如果修改的是K线叠加指标，只需重渲染K线图
    if (['ma', 'bollinger'].includes(data.type)) {
      renderKlineChart();
    } else {
      loadStockAnalysis();
    }
  }
};

// 自适应移动平均线计算
const calculateMA = (data, period) => {
  const result = [];
  if (data.length < 3) {
    return Array(data.length).fill(null);
  }
  
  // 自适应周期：根据数据长度调整周期
  const adaptivePeriod = Math.min(period, Math.max(3, Math.floor(data.length / 2)));
  
  for (let i = 0; i < data.length; i++) {
    if (i < adaptivePeriod - 1) {
      // 对于数据点不足的情况，使用已有数据点计算
      const currentPeriod = Math.min(i + 1, adaptivePeriod);
      const sum = data.slice(Math.max(0, i - currentPeriod + 1), i + 1).reduce((a, b) => a + b, 0);
      result.push((sum / currentPeriod).toFixed(2));
    } else {
      const sum = data.slice(i - adaptivePeriod + 1, i + 1).reduce((a, b) => a + b, 0);
      result.push((sum / adaptivePeriod).toFixed(2));
    }
  }
  return result;
};

// 转换前端周期格式到后端格式
const convertPeriodToBackend = (period) => {
  const periodMap = {
    D: "D",
    D1: "D",
    W: "W",
    W1: "W",
    M: "M",
    M1: "M",
  };
  return periodMap[period] || "D";
};

// 计算自适应布林带
const calculateBollingerBands = (data, period = 20, stdDev = 2) => {
  if (data.length < 3) {
    // 如果数据点太少，返回空数组
    return {
      middle: Array(data.length).fill(null),
      upper: Array(data.length).fill(null),
      lower: Array(data.length).fill(null),
    };
  }
  
  // 自适应周期：根据数据长度调整周期
  const adaptivePeriod = Math.min(period, Math.max(3, Math.floor(data.length / 2)));
  
  console.log(`布林带计算：数据长度=${data.length}, 原始周期=${period}, 自适应周期=${adaptivePeriod}`);
  
  const result = {
    middle: [],
    upper: [],
    lower: [],
  };

  for (let i = 0; i < data.length; i++) {
    if (i < adaptivePeriod - 1) {
      // 对于数据点不足的情况，使用已有数据点计算
      const currentPeriod = Math.min(i + 1, adaptivePeriod);
      const slice = data.slice(Math.max(0, i - currentPeriod + 1), i + 1);
      const avg = slice.reduce((a, b) => a + b, 0) / slice.length;
      const variance = slice.reduce((a, b) => a + Math.pow(b - avg, 2), 0) / slice.length;
      const std = Math.sqrt(variance);

      result.middle.push(avg.toFixed(2));
      result.upper.push((avg + stdDev * std).toFixed(2));
      result.lower.push((avg - stdDev * std).toFixed(2));
    } else {
      const slice = data.slice(i - adaptivePeriod + 1, i + 1);
      const avg = slice.reduce((a, b) => a + b, 0) / adaptivePeriod;
      const variance = slice.reduce((a, b) => a + Math.pow(b - avg, 2), 0) / adaptivePeriod;
      const std = Math.sqrt(variance);

      result.middle.push(avg.toFixed(2));
      result.upper.push((avg + stdDev * std).toFixed(2));
      result.lower.push((avg - stdDev * std).toFixed(2));
    }
  }

  return result;
};

const formatVolume = (volume) => {
  if (!volume) return "0";
  if (volume >= 100000000) {
    return (volume / 100000000).toFixed(2) + "亿";
  } else if (volume >= 10000) {
    return (volume / 10000).toFixed(2) + "万";
  }
  return volume.toString();
};

const formatMarketCap = (marketCap) => {
  if (!marketCap) return "0";
  if (marketCap >= 100000000) {
    return (marketCap / 100000000).toFixed(2) + "亿";
  }
  return marketCap.toString();
};

// 处理窗口大小变化
const handleResize = () => {
  if (klineChart.value) klineChart.value.resize();
  if (macdChart.value) macdChart.value.resize();
  if (volumeChart.value) volumeChart.value.resize();
};

// 添加 tooltip 通用样式配置
const getTooltipCommonConfig = () => ({
  trigger: "axis",
  show: true,
  backgroundColor: "rgba(0, 0, 0, 0.9)",
  borderColor: "#4a90e2",
  borderWidth: 1,
  borderRadius: 8,
  textStyle: { 
    color: "#fff", 
    fontSize: 12,
    lineHeight: 18
  },
  padding: [12, 16],
  extraCssText: 'max-width: 300px; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5); z-index: 9999;',
  confine: true,
  position: function (point, params, dom, rect, size) {
    // 自定义 tooltip 位置，确保不超出容器边界
    const tooltipWidth = size.contentSize[0];
    const tooltipHeight = size.contentSize[1];
    const containerWidth = size.viewSize[0];
    const containerHeight = size.viewSize[1];
    
    let x = point[0] + 10;
    let y = point[1] - tooltipHeight - 10;
    
    // 边界检查
    if (x + tooltipWidth > containerWidth) {
      x = point[0] - tooltipWidth - 10;
    }
    if (y < 0) {
      y = point[1] + 10;
    }
    
    return [x, y];
  }
});

// 添加 tooltip 调试日志
const debugTooltip = (chartType, content) => {
  console.log(`[${chartType}] Tooltip content:`, content);
  return content;
};

const loadStockAnalysis = async () => {
  if (!selectedStock.value) return;

  console.log("开始加载股票分析数据:", selectedStock.value);
  loading.value = true;
  error.value = null;

  try {
    // 获取股票基本信息
    await loadStockInfo();

    // 计算日期范围
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - parseInt(selectedRange.value));
    const startDateStr = startDate.toISOString().split("T")[0];
    const endDateStr = endDate.toISOString().split("T")[0];

    console.log("请求参数:", {
      stock: selectedStock.value,
      period: selectedPeriod.value,
      startDate: startDateStr,
      endDate: endDateStr,
    });

    // 转换周期格式
    const backendPeriod = convertPeriodToBackend(selectedPeriod.value);

    // 并行获取数据
    const [klineData, macdData] = await Promise.all([
      // 获取K线数据（包含指标）
      api.stockData
        .getKlineData(selectedStock.value, backendPeriod, {
          start_date: startDateStr,
          end_date: endDateStr,
          with_indicators: true,
          indicators: ["macd"],
        })
        .then((ret) => {
          console.log("K线数据API响应:", ret);
          return ret.data;
        })
        .catch((err) => {
          console.warn("K线数据获取失败，使用备用方案:", err);
          // 备用方案：尝试获取基础K线数据
          return api.stockData.getKlineData(
            selectedStock.value,
            backendPeriod,
            {
              start_date: startDateStr,
              end_date: endDateStr,
            }
          );
        }),
      // 单独获取MACD指标数据
      api.indicator
        .getIndicator(selectedStock.value, "macd", {
          start_date: startDateStr,
          end_date: endDateStr,
          freq: backendPeriod,
        })
        .then((ret) => {
          console.log("MACD数据API响应:", ret);
          return ret.data;
        })
        .catch((err) => {
          console.warn("MACD指标获取失败:", err);
          return null;
        }),
    ]);

    console.log("处理后的数据:", {
      klineData,
      macdData,
    });

    // 处理K线数据
    if (!klineData || !klineData.kline_data) {
      throw new Error("无法获取K线数据");
    }

    chartData.value = {
      kline: klineData,
      macd: macdData || klineData.indicators?.macd, // 优先使用单独获取的MACD数据
      volume: klineData.kline_data, // 成交量数据包含在K线数据中
    };

    console.log("设置 chartData:", chartData.value);

    // 等待DOM更新后渲染图表
    console.log("等待DOM更新...");
    await nextTick();
    console.log("DOM更新完成，开始渲染图表");
    await renderCharts();

    console.log("股票分析数据加载成功");
  } catch (err) {
    console.error("加载股票分析数据失败:", err);
    error.value =
      err.response?.data?.message || err.message || "加载股票分析数据失败";
    ElMessage.error(error.value);
  } finally {
    loading.value = false;
  }
};

// 加载股票基本信息
const loadStockInfo = async () => {
  try {
    const stockDetail = await api.stockList.getStockInfo(selectedStock.value);
    stockInfo.value = {
      code: selectedStock.value,
      name: stockDetail.name || "未知股票",
      price: stockDetail.current_price?.toFixed(2) || "0.00",
      changePercent: stockDetail.change_percent?.toFixed(2) || "0.00",
      volume: formatVolume(stockDetail.volume),
      marketCap: formatMarketCap(stockDetail.market_cap),
    };
  } catch (err) {
    console.warn("获取股票详细信息失败，使用基本信息:", err);
    // 如果获取详细信息失败，使用基本信息
    const stockDetails = combinedPopularStocks.value.find(
      (stock) => stock.code === selectedStock.value
    );
    stockInfo.value = {
      code: selectedStock.value,
      name: stockDetails?.name || "未知股票",
      price: "0.00",
      changePercent: "0.00",
      volume: "0万手",
      marketCap: "0亿",
    };
  }
};

// 渲染所有图表
const renderCharts = async () => {
  if (!chartData.value) {
    console.warn("No chart data available");
    return;
  }

  console.log("开始渲染图表, chartData:", chartData.value);
  console.log("容器引用状态:", {
    kline: !!klineChartContainer.value,
    macd: !!macdChartContainer.value,
    volume: !!volumeChartContainer.value,
  });

  try {
    console.log("开始渲染图表");
    await Promise.all([
      renderKlineChart(),
      renderMacdChart(),
      renderVolumeChart(),
    ]);
    console.log("所有图表渲染完成");
  } catch (err) {
    console.error("Failed to render charts:", err);
    ElMessage.error("图表渲染失败");
  }
};

// 渲染K线图表
const renderKlineChart = async () => {
  if (!klineChartContainer.value?.chartRef || !chartData.value.kline) {
    console.warn("K线图表渲染条件不满足:", {
      hasContainer: !!klineChartContainer.value?.chartRef,
      hasData: !!chartData.value?.kline,
      chartData: chartData.value,
    });
    return;
  }

  console.log("开始渲染K线图表");

  // 销毁旧图表
  if (klineChart.value) {
    klineChart.value.dispose();
  }

  // 确保容器存在
  const chartDom = klineChartContainer.value.chartRef;
  if (!chartDom) {
    console.error("图表容器 DOM 不存在");
    return;
  }

  console.log("初始化 ECharts 实例, 容器:", chartDom);
  klineChart.value = echarts.init(chartDom);

  const klineData = chartData.value.kline.kline_data || [];
  console.log("K线数据:", klineData);

  if (klineData.length === 0) {
    console.warn("K线数据为空");
    return;
  }

  const dates = klineData.map((item) => item.date);
  const values = klineData.map((item) => [
    item.open,
    item.close,
    item.low,
    item.high,
  ]);
  // 计算移动平均线（自适应周期）
  const closePrices = klineData.map((item) => item.close);
  const dataLength = closePrices.length;
  
  // 根据数据长度自适应调整MA周期
  const adaptiveMA5Period = Math.min(5, Math.max(3, Math.floor(dataLength / 6)));
  const adaptiveMA10Period = Math.min(10, Math.max(5, Math.floor(dataLength / 3)));
  const adaptiveMA20Period = Math.min(20, Math.max(10, Math.floor(dataLength / 2)));
  
  const ma5 = calculateMA(closePrices, adaptiveMA5Period);
  const ma10 = calculateMA(closePrices, adaptiveMA10Period);
  const ma20 = calculateMA(closePrices, adaptiveMA20Period);

  // 计算布林带（自适应周期）
  const bollingerSettings = getIndicatorSettings('bollinger');
  const bollingerPeriod = bollingerSettings.period || 20;
  const bollingerStdDev = bollingerSettings.stdDev || 2;
  
  const bollinger = calculateBollingerBands(
    closePrices,
    bollingerPeriod,
    bollingerStdDev
  );

  // 创建 tooltip 格式化函数，包含指标数据的闭包
  const createKlineTooltipFormatter = (klineData, values, ma5, ma10, ma20, bollinger, adaptiveMA5Period, adaptiveMA10Period, adaptiveMA20Period) => {
    return function (params) {
      const data = params[0];
      const dataIndex = data.dataIndex;
      const kline = values[dataIndex];
      if (!kline) return "";

      const [open, close, low, high] = kline;
      const change = close - open;
      const changePercent = ((change / open) * 100).toFixed(2);
      const isUp = change >= 0;
      
      // 获取对应的成交量数据
      const volumeValue = klineData[dataIndex]?.volume || 0;
      
      let content = `
        <div style="border-bottom: 1px solid #333; padding-bottom: 8px; margin-bottom: 8px;">
          <div style="font-weight: bold; color: #4a90e2; font-size: 14px;">${data.axisValue}</div>
        </div>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
          <div>
            <div style="color: #999; font-size: 11px;">开盘价</div>
            <div style="color: #fff; font-weight: bold;">¥${open}</div>
          </div>
          <div>
            <div style="color: #999; font-size: 11px;">收盘价</div>
            <div style="color: ${isUp ? '#22c55e' : '#ef4444'}; font-weight: bold;">¥${close}</div>
          </div>
          <div>
            <div style="color: #999; font-size: 11px;">最高价</div>
            <div style="color: #fff; font-weight: bold;">¥${high}</div>
          </div>
          <div>
            <div style="color: #999; font-size: 11px;">最低价</div>
            <div style="color: #fff; font-weight: bold;">¥${low}</div>
          </div>
          <div>
            <div style="color: #999; font-size: 11px;">涨跌额</div>
            <div style="color: ${isUp ? '#22c55e' : '#ef4444'}; font-weight: bold;">
              ${isUp ? '+' : ''}${change.toFixed(2)}
            </div>
          </div>
          <div>
            <div style="color: #999; font-size: 11px;">涨跌幅</div>
            <div style="color: ${isUp ? '#22c55e' : '#ef4444'}; font-weight: bold;">
              ${isUp ? '+' : ''}${changePercent}%
            </div>
          </div>
        </div>
        <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px;">
          <div style="color: #999; font-size: 11px;">成交量</div>
          <div style="color: #4ecdc4; font-weight: bold;">${formatVolume(volumeValue)}</div>
        </div>
      `;

      // 如果有指标数据，显示指标信息
      if (chartOverlay.value === "ma") {
        const ma5Value = ma5[dataIndex];
        const ma10Value = ma10[dataIndex];
        const ma20Value = ma20[dataIndex];
        
        content += `
          <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px;">
            <div style="color: #999; font-size: 11px; margin-bottom: 4px;">移动平均线</div>
            <div style="display: flex; gap: 12px;">
              ${ma5Value ? `<div><span style="color: #ff6b6b;">MA${adaptiveMA5Period}:</span> <span style="color: #fff;">¥${ma5Value}</span></div>` : ''}
              ${ma10Value ? `<div><span style="color: #4ecdc4;">MA${adaptiveMA10Period}:</span> <span style="color: #fff;">¥${ma10Value}</span></div>` : ''}
              ${ma20Value ? `<div><span style="color: #45b7d1;">MA${adaptiveMA20Period}:</span> <span style="color: #fff;">¥${ma20Value}</span></div>` : ''}
            </div>
          </div>
        `;
      }

      if (chartOverlay.value === "bollinger") {
        const upperValue = bollinger.upper[dataIndex];
        const middleValue = bollinger.middle[dataIndex];
        const lowerValue = bollinger.lower[dataIndex];
        
        content += `
          <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px;">
            <div style="color: #999; font-size: 11px; margin-bottom: 4px;">布林带</div>
            <div style="display: flex; gap: 12px; flex-wrap: wrap;">
              ${upperValue ? `<div><span style="color: #ff6b6b;">上轨:</span> <span style="color: #fff;">¥${upperValue}</span></div>` : ''}
              ${middleValue ? `<div><span style="color: #4ecdc4;">中轨:</span> <span style="color: #fff;">¥${middleValue}</span></div>` : ''}
              ${lowerValue ? `<div><span style="color: #22c55e;">下轨:</span> <span style="color: #fff;">¥${lowerValue}</span></div>` : ''}
            </div>
          </div>
        `;
      }

      return debugTooltip('K线图表', content);
    };
  };

  const option = {
    animation: true,
    backgroundColor: "transparent",
    grid: {
      left: "50px",
      right: "50px",
      top: "60px",
      bottom: "60px",
    },
    xAxis: {
      type: "category",
      data: dates,
      axisLine: { lineStyle: { color: "#666" } },
      axisLabel: { color: "#999" },
    },
    yAxis: {
      type: "value",
      scale: true,
      axisLine: { lineStyle: { color: "#666" } },
      axisLabel: { color: "#999" },
      splitLine: { lineStyle: { color: "#333" } },
    },
    tooltip: {
      ...getTooltipCommonConfig(),
      formatter: createKlineTooltipFormatter(klineData, values, ma5, ma10, ma20, bollinger, adaptiveMA5Period, adaptiveMA10Period, adaptiveMA20Period),
    },
    series: [
      {
        name: "K线",
        type: "candlestick",
        data: values,
        itemStyle: {
          color: "#ef4444",
          color0: "#22c55e",
          borderColor: "#ef4444",
          borderColor0: "#22c55e",
        },
      },
      ...(chartOverlay.value === "ma"
        ? [
            {
              name: `MA${adaptiveMA5Period}`,
              type: "line",
              data: ma5,
              smooth: true,
              lineStyle: { color: "#ff6b6b", width: 1 },
              showSymbol: false,
            },
            {
              name: `MA${adaptiveMA10Period}`,
              type: "line",
              data: ma10,
              smooth: true,
              lineStyle: { color: "#4ecdc4", width: 1 },
              showSymbol: false,
            },
            {
              name: `MA${adaptiveMA20Period}`,
              type: "line",
              data: ma20,
              smooth: true,
              lineStyle: { color: "#45b7d1", width: 1 },
              showSymbol: false,
            },
          ]
        : []),
      ...(chartOverlay.value === "bollinger"
        ? [
            {
              name: `布林上轨(${Math.min(bollingerPeriod, Math.max(3, Math.floor(dataLength / 2)))})`,
              type: "line",
              data: bollinger.upper,
              smooth: true,
              lineStyle: { color: "#ff6b6b", width: 1, type: "dashed" },
              showSymbol: false,
            },
            {
              name: `布林中轨(${Math.min(bollingerPeriod, Math.max(3, Math.floor(dataLength / 2)))})`,
              type: "line",
              data: bollinger.middle,
              smooth: true,
              lineStyle: { color: "#4ecdc4", width: 1 },
              showSymbol: false,
            },
            {
              name: `布林下轨(${Math.min(bollingerPeriod, Math.max(3, Math.floor(dataLength / 2)))})`,
              type: "line",
              data: bollinger.lower,
              smooth: true,
              lineStyle: { color: "#22c55e", width: 1, type: "dashed" },
              showSymbol: false,
            },
          ]
        : []),
    ],
  };

  console.log("设置图表配置");
  klineChart.value.setOption(option);
  console.log("K线图表渲染完成");
};

// 渲染MACD图表
const renderMacdChart = async () => {
  if (!macdChartContainer.value?.chartRef || !chartData.value.macd) {
    console.warn("MACD图表渲染条件不满足:", {
      hasContainer: !!macdChartContainer.value?.chartRef,
      hasData: !!chartData.value?.macd,
    });
    return;
  }

  console.log("开始渲染MACD图表");

  // 销毁旧图表
  if (macdChart.value) {
    macdChart.value.dispose();
  }

  const chartDom = macdChartContainer.value.chartRef;
  if (!chartDom) {
    console.error("MACD图表容器 DOM 不存在");
    return;
  }

  console.log("初始化 MACD ECharts 实例");
  macdChart.value = echarts.init(chartDom);

  const macdData = chartData.value.macd;
  console.log("MACD Data:", macdData); // 调试用

  let dates = [];
  let dif = [];
  let dea = [];
  let histogram = [];

  // 处理MACD数据格式 - 支持多种API返回格式
  if (macdData.data && Array.isArray(macdData.data)) {
    // 格式1: {data: [{date, MACD_12_26_9, MACDs_12_26_9, MACDh_12_26_9}, ...]}
    dates = macdData.data.map((item) => item.date);
    dif = macdData.data.map((item) =>
      parseFloat(item.MACD_12_26_9 || item.dif || item.diff || 0)
    );
    dea = macdData.data.map((item) =>
      parseFloat(item.MACDs_12_26_9 || item.dea || item.signal || 0)
    );
    histogram = macdData.data.map((item) =>
      parseFloat(item.MACDh_12_26_9 || item.histogram || item.hist || 0)
    );
  } else if (macdData.date && Array.isArray(macdData.date)) {
    // 格式2: {date: [], dif: [], dea: [], histogram: []}
    dates = macdData.date;
    dif = macdData.dif || macdData.diff || [];
    dea = macdData.dea || macdData.signal || [];
    histogram = macdData.histogram || macdData.hist || [];
  } else if (Array.isArray(macdData)) {
    // 格式3: 直接是数组
    dates = macdData.map((item) => item.date);
    dif = macdData.map((item) =>
      parseFloat(item.MACD_12_26_9 || item.dif || item.diff || 0)
    );
    dea = macdData.map((item) =>
      parseFloat(item.MACDs_12_26_9 || item.dea || item.signal || 0)
    );
    histogram = macdData.map((item) =>
      parseFloat(item.MACDh_12_26_9 || item.histogram || item.hist || 0)
    );
  }

  // 创建 MACD tooltip 格式化函数
  const createMacdTooltipFormatter = (dif, dea, histogram) => {
    return function (params) {
      if (!params || params.length === 0) return "";
      
      const date = params[0].axisValue;
      const dataIndex = params[0].dataIndex;
      
      let content = `
        <div style="border-bottom: 1px solid #333; padding-bottom: 8px; margin-bottom: 8px;">
          <div style="font-weight: bold; color: #4a90e2; font-size: 14px;">${date}</div>
          <div style="color: #999; font-size: 11px;">MACD 指标</div>
        </div>
      `;

      // 获取各个指标的值
      const difValue = dif[dataIndex];
      const deaValue = dea[dataIndex];
      const histogramValue = histogram[dataIndex];
      
      content += `
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 8px;">
          <div>
            <div style="color: #999; font-size: 11px;">DIF (快线)</div>
            <div style="color: #ff6b6b; font-weight: bold;">${difValue?.toFixed(4) || "0.0000"}</div>
          </div>
          <div>
            <div style="color: #999; font-size: 11px;">DEA (慢线)</div>
            <div style="color: #4ecdc4; font-weight: bold;">${deaValue?.toFixed(4) || "0.0000"}</div>
          </div>
        </div>
      `;

      // 计算 DIF 和 DEA 的差值
      const difDeaDiff = difValue && deaValue ? difValue - deaValue : 0;
      const isPositive = histogramValue >= 0;
      
      content += `
        <div style="border-top: 1px solid #333; padding-top: 8px;">
          <div style="color: #999; font-size: 11px;">HISTOGRAM (柱状线)</div>
          <div style="color: ${isPositive ? '#ef4444' : '#22c55e'}; font-weight: bold;">
            ${isPositive ? '+' : ''}${histogramValue?.toFixed(4) || "0.0000"}
          </div>
        </div>
      `;

      // 添加信号提示
      if (difValue && deaValue) {
        const signal = difValue > deaValue ? "多头信号" : "空头信号";
        const signalColor = difValue > deaValue ? "#22c55e" : "#ef4444";
        
        content += `
          <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px;">
            <div style="color: #999; font-size: 11px;">趋势信号</div>
            <div style="color: ${signalColor}; font-weight: bold;">${signal}</div>
          </div>
        `;
      }

      return debugTooltip('MACD图表', content);
    };
  };

  const option = {
    animation: true,
    backgroundColor: "transparent",
    grid: {
      left: "50px",
      right: "50px",
      top: "30px",
      bottom: "50px",
    },
    xAxis: {
      type: "category",
      data: dates,
      axisLine: { lineStyle: { color: "#666" } },
      axisLabel: { color: "#999", fontSize: 11 },
    },
    yAxis: {
      type: "value",
      axisLine: { lineStyle: { color: "#666" } },
      axisLabel: { color: "#999", fontSize: 11 },
      splitLine: { lineStyle: { color: "#333" } },
    },
    tooltip: {
      ...getTooltipCommonConfig(),
      formatter: createMacdTooltipFormatter(dif, dea, histogram),
    },
    legend: {
      textStyle: { color: "#999" },
      top: 5,
    },
    series: [
      {
        name: "DIF",
        type: "line",
        data: dif,
        lineStyle: { color: "#ff6b6b", width: 1 },
        showSymbol: false,
      },
      {
        name: "DEA",
        type: "line",
        data: dea,
        lineStyle: { color: "#4ecdc4", width: 1 },
        showSymbol: false,
      },
      {
        name: "HISTOGRAM",
        type: "bar",
        data: histogram,
        itemStyle: {
          color: function (params) {
            return params.value >= 0 ? "#ef4444" : "#22c55e";
          },
        },
      },
    ],
  };

  console.log("设置MACD图表配置");
  macdChart.value.setOption(option);
  console.log("MACD图表渲染完成");
};

// 渲染成交量图表
const renderVolumeChart = async () => {
  if (!volumeChartContainer.value?.chartRef || !chartData.value.volume) {
    console.warn("成交量图表渲染条件不满足:", {
      hasContainer: !!volumeChartContainer.value?.chartRef,
      hasData: !!chartData.value?.volume,
    });
    return;
  }

  console.log("开始渲染成交量图表");

  if (volumeChart.value) {
    volumeChart.value.dispose();
  }

  const chartDom = volumeChartContainer.value.chartRef;
  if (!chartDom) {
    console.error("成交量图表容器 DOM 不存在");
    return;
  }

  console.log("初始化成交量 ECharts 实例");
  volumeChart.value = echarts.init(chartDom);
  const volumeData = chartData.value.volume || [];
  console.log("成交量数据:", volumeData);

  // 提取成交量图表所需的数据
  const dates = volumeData.map((item) => item.date);
  const volumes = volumeData.map((item) => item.volume);

  if (dates.length === 0 || volumes.length === 0) {
    console.warn("成交量数据为空");
    return;
  }

  // 创建成交量 tooltip 格式化函数
  const createVolumeTooltipFormatter = (volumeData) => {
    return function (params) {
      const data = params[0];
      const dataIndex = data.dataIndex;
      const currentData = volumeData[dataIndex];
      
      if (!currentData) return "";
      
      // 计算与前一天的成交量对比
      const prevData = dataIndex > 0 ? volumeData[dataIndex - 1] : null;
      const volumeChange = prevData ? currentData.volume - prevData.volume : 0;
      const volumeChangePercent = prevData ? ((volumeChange / prevData.volume) * 100) : 0;
      
      // 判断价格涨跌
      const priceChange = currentData.close - currentData.open;
      const isPriceUp = priceChange >= 0;
      
      // 计算成交金额（成交量 * 平均价格）
      const avgPrice = (currentData.high + currentData.low + currentData.close + currentData.open) / 4;
      const turnover = currentData.volume * avgPrice;
      
      let content = `
        <div style="border-bottom: 1px solid #333; padding-bottom: 8px; margin-bottom: 8px;">
          <div style="font-weight: bold; color: #4a90e2; font-size: 14px;">${data.axisValue}</div>
          <div style="color: #999; font-size: 11px;">成交量分析</div>
        </div>
      `;

      content += `
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 8px;">
          <div>
            <div style="color: #999; font-size: 11px;">成交量</div>
            <div style="color: #4ecdc4; font-weight: bold;">${formatVolume(currentData.volume)}</div>
          </div>
          <div>
            <div style="color: #999; font-size: 11px;">成交金额</div>
            <div style="color: #ff9f43; font-weight: bold;">${formatVolume(turnover)}元</div>
          </div>
        </div>
      `;

      // 如果有前一天的数据，显示变化情况
      if (prevData) {
        const isVolumeUp = volumeChange >= 0;
        content += `
          <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px;">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
              <div>
                <div style="color: #999; font-size: 11px;">量比变化</div>
                <div style="color: ${isVolumeUp ? '#22c55e' : '#ef4444'}; font-weight: bold;">
                  ${isVolumeUp ? '+' : ''}${formatVolume(volumeChange)}
                </div>
              </div>
              <div>
                <div style="color: #999; font-size: 11px;">量比幅度</div>
                <div style="color: ${isVolumeUp ? '#22c55e' : '#ef4444'}; font-weight: bold;">
                  ${isVolumeUp ? '+' : ''}${volumeChangePercent.toFixed(2)}%
                </div>
              </div>
            </div>
          </div>
        `;
      }

      // 添加价格信息
      content += `
        <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px;">
          <div style="color: #999; font-size: 11px;">当日价格</div>
          <div style="display: flex; gap: 12px; flex-wrap: wrap;">
            <div><span style="color: #999;">开:</span> <span style="color: #fff;">¥${currentData.open}</span></div>
            <div><span style="color: #999;">收:</span> <span style="color: ${isPriceUp ? '#22c55e' : '#ef4444'};">¥${currentData.close}</span></div>
            <div><span style="color: #999;">高:</span> <span style="color: #fff;">¥${currentData.high}</span></div>
            <div><span style="color: #999;">低:</span> <span style="color: #fff;">¥${currentData.low}</span></div>
          </div>
        </div>
      `;

      // 添加量价关系分析
      const volumeLevel = currentData.volume > (prevData?.volume || 0) ? "放量" : "缩量";
      const priceLevel = isPriceUp ? "上涨" : "下跌";
      const relationship = `${volumeLevel}${priceLevel}`;
      
      let relationshipColor = "#4ecdc4";
      if (relationship === "放量上涨") relationshipColor = "#22c55e";
      else if (relationship === "放量下跌") relationshipColor = "#ef4444";
      else if (relationship === "缩量上涨") relationshipColor = "#ff9f43";
      else if (relationship === "缩量下跌") relationshipColor = "#9b59b6";

      content += `
        <div style="border-top: 1px solid #333; padding-top: 8px; margin-top: 8px;">
          <div style="color: #999; font-size: 11px;">量价关系</div>
          <div style="color: ${relationshipColor}; font-weight: bold;">${relationship}</div>
        </div>
      `;

      return debugTooltip('成交量图表', content);
    };
  };

  const option = {
    animation: true,
    backgroundColor: "transparent",
    grid: {
      left: "50px",
      right: "50px",
      top: "30px",
      bottom: "50px",
    },
    xAxis: {
      type: "category",
      data: dates,
      axisLine: { lineStyle: { color: "#666" } },
      axisLabel: { color: "#999", fontSize: 11 },
    },
    yAxis: {
      type: "value",
      axisLine: { lineStyle: { color: "#666" } },
      axisLabel: {
        color: "#999",
        fontSize: 11,
        formatter: function (value) {
          return formatVolume(value);
        },
      },
      splitLine: { lineStyle: { color: "#333" } },
    },
    tooltip: {
      ...getTooltipCommonConfig(),
      formatter: createVolumeTooltipFormatter(volumeData),
    },
    series: [
      {
        name: "成交量",
        type: "bar",
        data: volumes,
        itemStyle: {
          color: function (params) {
            // 根据涨跌情况设置颜色
            const index = params.dataIndex;
            if (index === 0) return "#4ecdc4";
            const current = volumeData[index];
            const previous = volumeData[index - 1];
            return current.close >= previous.close ? "#ef4444" : "#22c55e";
          },
        },
      },
    ],
  };

  console.log("设置成交量图表配置");
  volumeChart.value.setOption(option);
  console.log("成交量图表渲染完成");
};

// 模拟数据生成函数（备用）
const generateMockKlineData = () => {
  return [];
};

const generateMockMacdData = () => {
  return [];
};

const generateMockVolumeData = () => {
  return [];
};

// 监听路由query变化
watch(
  () => route.query,
  async (newQuery, oldQuery) => {
    // 检查是否有股票代码变化
    if (newQuery.stock && newQuery.stock !== oldQuery?.stock) {
      console.log("检测到路由query变化:", newQuery.stock, newQuery.name);
      
      // 使用通用函数加载股票数据
      await loadStockByCode(
        newQuery.stock,
        newQuery.name,
        newQuery.industry
      );
    }
  },
  { immediate: false, deep: true }
);

// 初始化处理路由参数
onMounted(async () => {
  // 加载热门股票数据
  await loadHotStocks();

  // 检查是否从自选股页面跳转过来
  if (route.query.stock) {
    console.log("从自选股跳转，预选股票:", route.query.stock, route.query.name);
    
    // 使用通用函数加载股票数据
    await loadStockByCode(
      route.query.stock,
      route.query.name,
      route.query.industry
    );
  }

  // 添加窗口大小变化监听
  window.addEventListener("resize", handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
  if (klineChart.value) klineChart.value.dispose();
  if (macdChart.value) macdChart.value.dispose();
  if (volumeChart.value) volumeChart.value.dispose();
});
</script>

<style scoped lang="scss">
.analysis-page {
  max-width: 1400px;
  margin: 0 auto;
}

.chart-container {
  position: relative;
  background: linear-gradient(
    135deg,
    var(--bg-tertiary) 0%,
    var(--bg-secondary) 100%
  );
  border: 1px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    border-color: var(--accent-primary);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
  }

  // 添加微妙的纹理效果
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(
        circle at 20% 20%,
        rgba(59, 130, 246, 0.05) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 80% 80%,
        rgba(139, 92, 246, 0.05) 0%,
        transparent 50%
      );
    pointer-events: none;
    z-index: 1;
  }

  // 确保内容在纹理上方
  > * {
    position: relative;
    z-index: 2;
  }
}

// 添加渐变边框效果
.card {
  &:hover .chart-container {
    background: linear-gradient(
      135deg,
      var(--bg-tertiary) 0%,
      rgba(59, 130, 246, 0.05) 50%,
      var(--bg-secondary) 100%
    );
  }
}

// 加载动画优化
@keyframes pulse-glow {
  0%,
  100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 图标悬停效果
i[class*="i-carbon-"] {
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1);
    animation: pulse-glow 2s infinite;
  }
}
</style>
