<template>
  <div class="scanner-page p-6">
    <div class="grid grid-cols-1 gap-6 mb-6">
      <!-- 扫描配置和操作 -->
      <div class="card p-6">
        <!-- 指标选择标题 -->
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-2xl font-semibold text-text-primary">扫描指标</h2>
        </div>

        <!-- 指标网格和操作面板 -->
        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6 ">
          <!-- 指标选择区域 -->
          <div class="xl:col-span-3">
            <div class="h-full grid grid-cols-1 md:grid-cols-3 gap-4">
              <label v-for="indicator in indicatorOptions" :key="indicator.value" class="relative cursor-pointer"
                :class="{ 'cursor-not-allowed opacity-50': isScanning }">
                <input type="checkbox" v-model="scanForm.indicators" :value="indicator.value" :disabled="isScanning"
                  class="sr-only peer" />
                <div class="indicator-card" :class="{
                  'indicator-card--active': scanForm.indicators.includes(indicator.value),
                  'indicator-card--disabled': isScanning
                }">
                  <div class="text-center">
                    <Icon :name="indicator.icon" class="text-2xl mb-2 transition-colors"
                      :class="scanForm.indicators.includes(indicator.value) ? 'text-primary' : (themeStore.isDark ? 'text-white text-opacity-90' : 'text-gray-800')" />
                    <div class="font-medium text-sm transition-colors"
                      :class="scanForm.indicators.includes(indicator.value) ? 'text-primary' : (themeStore.isDark ? 'text-white text-opacity-90' : 'text-gray-800')">
                      {{ indicator.label }}
                    </div>
                    <div class="text-xs mt-1 transition-colors"
                      :class="scanForm.indicators.includes(indicator.value) ? 'text-primary opacity-80' : (themeStore.isDark ? 'text-white opacity-60' : 'text-gray-500')">
                      {{ formatParameterText(indicator.value) }}
                    </div>
                  </div>
                </div>
              </label>
            </div>
          </div>

          <!-- 操作面板 -->
          <div class="xl:col-span-1">
            <div class="space-y-3">
              <!-- 参数配置按钮 -->
              <button 
                @click="showParameterDialog = true" 
                :disabled="isScanning"
                class="w-full px-4 py-3 bg-white bg-opacity-10 text-white rounded-lg hover:bg-opacity-20 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center border-0"
              >
                <Icon name="settings" class="mr-2" />
                参数配置
              </button>
              
              <!-- 开始/停止按钮 -->
              <button v-if="!isScanning" @click="startScan" :disabled="isStarting || scanForm.indicators.length === 0"
                class="btn-primary px-4 py-3 w-full flex items-center justify-center">
                <Icon name="play" class="mr-2" />
                {{ isStarting ? '启动中...' : '开始扫描' }}
              </button>
              <button v-else @click="stopScan" :disabled="isStopping"
                class="w-full px-4 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center">
                <Icon name="stop" class="mr-2" />
                {{ isStopping ? '停止中...' : '停止扫描' }}
              </button>

              <!-- 扫描进度 -->
              <div class="space-y-2">
                <div class="flex items-center justify-between text-sm">
                  <span class="text-text-secondary">{{ currentTask ? progressText : '0 / 0' }}</span>
                  <span class="text-primary font-medium">{{ currentTask ? progressPercentage : 0 }}%</span>
                </div>
                <div class="w-full bg-bg-primary rounded-full h-2 overflow-hidden">
                  <div class="h-full transition-all duration-300 ease-out rounded-full"
                    :class="currentTask ? progressBarClass : 'bg-gray-400'"
                    :style="{ width: `${currentTask ? progressPercentage : 0}%` }" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 扫描结果 -->
        <ScannerResults class="mt-6" />
        
        <!-- 参数配置对话框 -->
        <ParameterConfigDialog 
          v-model="showParameterDialog" 
          @confirm="handleParameterConfirm" 
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import Icon from '@/components/common/Icon.vue'
import ScannerResults from './ScannerResults.vue'
import ParameterConfigDialog from './ParameterConfigDialog.vue'
import { useScannerStore } from '@/store'
import { useThemeStore } from '@/store'
import { useStorage } from '@/composables/useStorage'
const themeStore = useThemeStore()

const scannerStore = useScannerStore()
const { formatParameterText, indicatorParameters } = useStorage()

// 参数配置对话框显示状态
const showParameterDialog = ref(false)

// 指标选项
const indicatorOptions = [
  {
    value: 'kdj',
    label: 'KDJ指标',
    desc: '动量振荡器',
    icon: 'chart-line'
  },
  {
    value: 'volume_pressure',
    label: '成交量压力',
    desc: '成交量分析',
    icon: 'volume-file-storage'
  },
  {
    value: 'bollinger',
    label: '布林带',
    desc: '波动率指标',
    icon: 'chart-area'
  }
]

// 扫描表单
const scanForm = ref({
  indicators: ['kdj', 'volume_pressure']
})

// 状态标志
const isStarting = ref(false)
const isStopping = ref(false)

// 计算属性
const currentTask = computed(() => scannerStore.currentTask)
const isScanning = computed(() => scannerStore.isScanning)

const statusText = computed(() => {
  if (!currentTask.value) return '未开始'
  switch (currentTask.value.status) {
    case 'pending': return '等待中'
    case 'running': return '扫描中'
    case 'completed': return '已完成'
    case 'cancelled': return '已取消'
    case 'failed': return '失败'
    default: return '未知'
  }
})

const statusIcon = computed(() => {
  if (!currentTask.value) return 'dot-mark'
  switch (currentTask.value.status) {
    case 'pending': return 'time'
    case 'running': return 'in-progress'
    case 'completed': return 'checkmark-filled'
    case 'cancelled': return 'close'
    case 'failed': return 'warning-filled'
    default: return 'dot-mark'
  }
})

const statusClass = computed(() => {
  if (!currentTask.value) return 'bg-white bg-opacity-20 text-white text-opacity-90'
  switch (currentTask.value.status) {
    case 'pending': return 'bg-white bg-opacity-20 text-yellow-300'
    case 'running': return 'bg-white bg-opacity-20 text-blue-300'
    case 'completed': return 'bg-white bg-opacity-20 text-green-300'
    case 'cancelled': return 'bg-white bg-opacity-20 text-gray-300'
    case 'failed': return 'bg-white bg-opacity-20 text-red-300'
    default: return 'bg-white bg-opacity-20 text-white text-opacity-90'
  }
})

const progressPercentage = computed(() => {
  if (!currentTask.value?.progress) return 0
  return Math.round(currentTask.value.progress.percentage || 0)
})

const progressText = computed(() => {
  if (!currentTask.value?.progress) return '0 / 0'
  const { current, total } = currentTask.value.progress
  return `${current} / ${total}`
})

const progressBarClass = computed(() => {
  if (!currentTask.value) return 'bg-gray-400'
  switch (currentTask.value.status) {
    case 'completed': return 'bg-green-500'
    case 'failed': return 'bg-red-500'
    default: return 'bg-gray-400'
  }
})

// 方法
const startScan = async () => {
  if (scanForm.value.indicators.length === 0) {
    ElMessage.warning('请选择至少一个扫描指标')
    return
  }

  try {
    isStarting.value = true

    // 获取当前的参数配置并发送给后端
    await scannerStore.startScan({
      indicators: scanForm.value.indicators,
      parameters: indicatorParameters.value
    })

    ElMessage.success('扫描任务已启动')
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message)
    }
  } finally {
    isStarting.value = false
  }
}

const stopScan = async () => {
  try {
    isStopping.value = true
    await scannerStore.stopScan()
    ElMessage.success('扫描任务已停止')
  } catch (error) {
    ElMessage.error(error.message || '停止失败')
  } finally {
    isStopping.value = false
  }
}

// 参数配置确认处理
const handleParameterConfirm = (parameters) => {
  // 参数已在对话框组件中保存到localStorage
  // 这里可以添加额外的处理逻辑，比如更新界面显示
  console.log('参数配置已更新:', parameters)
}

// 生命周期
onMounted(() => {
  // 初始化会话
  scannerStore.initSession()
})

onUnmounted(() => {
  // 清理
  scannerStore.cleanup()
})
</script>

<style scoped lang="scss">
.scanner-page {
  // max-width: 1400px;
  margin: 0 auto;
}

.indicator-card {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  background-color: var(--indicator-card-bg);
  border-radius: 0.5rem;
  transition: all 200ms;
  cursor: pointer;

  &:hover {
    background-color: var(--indicator-card-bg-active);
    transform: scale(1.01);
  }

  &--active {
    background-color: var(--indicator-card-bg-active) !important;
    transform: scale(1.01);
  }

  &--disabled {
    cursor: not-allowed;
    opacity: 0.5;

    &:hover {
      transform: none;
    }
  }
}

.hover\:bg-bg-primary:hover {
  background-color: var(--bg-primary);
}

.hover\:border-primary:hover {
  border-color: var(--primary);
}

.hover\:bg-primary-dark:hover {
  background-color: var(--primary-dark);
}
</style>