<template>
  <div class="card hover:transform-none hover:border-border-color bg-bg-secondary p-6 rounded-lg shadow-lg">
    <!-- 标题栏 -->
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-xl font-semibold text-text-primary">
        扫描结果
      </h3>
      <div v-if="totalCount > 0" class="text-sm text-text-secondary">
        共 {{ totalCount }} 条记录
      </div>
    </div>

    <!-- 结果表格 -->
    <CommonTable
      :data="results"
      :columns="tableColumns"
      :loading="loading"
      :pagination="paginationConfig"
      :show-pagination="totalCount > 0"
      @row-click="viewDetail"
      @page-change="handlePageChange"
    >
      <!-- 自定义列内容 -->
      <template #stock_code="{ value }">
        <span class="font-medium text-text-primary">{{ value }}</span>
      </template>

      <template #stock_name="{ value }">
        <span class="text-text-primary">{{ value }}</span>
      </template>

      <template #industry="{ value }">
        <span class="text-text-secondary text-sm">{{ value || '-' }}</span>
      </template>

      <template #price="{ value }">
        <span class="font-medium text-text-primary text-center">{{ formatPrice(value) }}</span>
      </template>

      <template #change_percent="{ value }">
        <span
          class="font-medium"
          :class="getPriceChangeClass(value)"
        >
          {{ formatPercent(value) }}
        </span>
      </template>

      <template #signals="{ row }">
        <div class="flex justify-center gap-1">
          <span
            v-for="signal in row.signals"
            :key="signal"
            class="px-2 py-1 text-xs rounded-full font-medium"
            :class="getSignalClass(signal)"
          >
            {{ getSignalText(signal) }}
          </span>
        </div>
      </template>

      <template #kdj="{ row }">
        <div class="indicator-card">
          <div class="indicator-values">
            <div class="value-row">
              <span class="value-label">K</span>
              <span class="value-number primary">{{ formatNumber(row.indicator_data?.kdj_k) }}</span>
            </div>
            <div class="value-row">
              <span class="value-label">D</span>
              <span class="value-number primary">{{ formatNumber(row.indicator_data?.kdj_d) }}</span>
            </div>
            <div class="value-row">
              <span class="value-label">J</span>
              <span class="value-number secondary">{{ formatNumber(row.indicator_data?.kdj_j) }}</span>
            </div>
          </div>
          <!-- KDJ状态标识 -->
          <div v-if="isKDJGoldenCross(row)" class="indicator-badge golden">
            <span class="badge-text">金叉</span>
          </div>
          <div v-else-if="isKDJDeathCross(row)" class="indicator-badge death">
            <span class="badge-text">死叉</span>
          </div>
        </div>
      </template>

      <template #volume_pressure="{ row }">
        <div class="indicator-card">
          <div class="indicator-values">
            <div class="value-row">
              <span class="value-label">当前</span>
              <span class="value-number primary">{{ formatNumber(row.indicator_data?.volume_pressure) }}</span>
            </div>
            <div class="value-row">
              <span class="value-label">平均</span>
              <span class="value-number secondary">{{ formatNumber(row.indicator_data?.volume_pressure_avg) }}</span>
            </div>
            <div class="value-row placeholder">
              <span class="value-label">&nbsp;</span>
              <span class="value-number">&nbsp;</span>
            </div>
          </div>
          <!-- 成交量突破状态 -->
          <div v-if="isVolumeBreakout(row)" class="indicator-badge breakout">
            <span class="badge-text">突破</span>
          </div>
        </div>
      </template>

      <template #bollinger="{ row }">
        <div class="indicator-card">
          <div class="indicator-values bollinger-bands">
            <div class="band-row upper">
              <span class="band-label">上轨</span>
              <span class="band-value">{{ formatNumber(row.indicator_data?.bollinger_upper) }}</span>
            </div>
            <div class="band-row middle">
              <span class="band-label">中轨</span>
              <span class="band-value">{{ formatNumber(row.indicator_data?.bollinger_middle) }}</span>
            </div>
            <div class="band-row lower">
              <span class="band-label">下轨</span>
              <span class="band-value">{{ formatNumber(row.indicator_data?.bollinger_lower) }}</span>
            </div>
          </div>
          <!-- 布林带位置状态 -->
          <div class="indicator-badge position" :class="getBollingerPositionClass(row)">
            {{ getBollingerPositionText(row) }}
          </div>
        </div>
      </template>

      <template #trigger_reason="{ row }">
        <div class="text-sm">
          <div v-for="reason in getTriggerReasons(row)" :key="reason" class="text-blue-600 text-xs mb-1">
            • {{ reason }}
          </div>
        </div>
      </template>

      <template #scan_time="{ value }">
        <span class="text-text-secondary text-sm">{{ formatTime(value) }}</span>
      </template>

      <template #actions="{ row }">
        <el-button
          type="text"
          size="small"
          @click.stop="viewDetail(row)"
          class="text-primary hover:text-primary-dark"
        >
          详情
        </el-button>
      </template>
    </CommonTable>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import CommonTable from '@/components/common/CommonTable.vue'
import { useScannerStore } from '@/store'
import { formatDate } from '@/utils/date'

const router = useRouter()
const scannerStore = useScannerStore()

// 数据
const currentPage = ref(1)
const pageSize = ref(20)

// 计算属性
const results = computed(() => scannerStore.results)
const totalCount = computed(() => scannerStore.totalCount)
const loading = computed(() => scannerStore.loadingResults)

// 表格列配置
const tableColumns = [
  {
    prop: 'stock_code',
    label: '代码',
    minWidth: 120,
    slot: 'stock_code'
  },
  {
    prop: 'stock_name',
    label: '名称',
    minWidth: 160,
    slot: 'stock_name'
  },
  {
    prop: 'price',
    label: '最新价',
    minWidth: 100,
    align: 'center',
    slot: 'price'
  },
  {
    prop: 'change_percent',
    label: '涨跌幅',
    minWidth: 100,
    align: 'center',
    slot: 'change_percent'
  },
  {
    prop: 'signals',
    label: '信号',
    minWidth: 120,
    align: 'center',
    slot: 'signals'
  },
  {
    prop: 'kdj',
    label: 'KDJ指标',
    minWidth: 140,
    align: 'center',
    slot: 'kdj'
  },
  {
    prop: 'volume_pressure',
    label: '成交量压力',
    minWidth: 140,
    align: 'center',
    slot: 'volume_pressure'
  },
  {
    prop: 'bollinger',
    label: '布林带',
    minWidth: 140,
    align: 'center',
    slot: 'bollinger'
  },
  {
    prop: 'trigger_reason',
    label: '触发原因',
    minWidth: 160,
    align: 'left',
    slot: 'trigger_reason'
  },
  {
    prop: 'scan_time',
    label: '扫描时间',
    minWidth: 120,
    slot: 'scan_time'
  }
]

// 分页配置
const paginationConfig = computed(() => ({
  total: totalCount.value,
  currentPage: currentPage.value,
  pageSize: pageSize.value,
  pageSizes: [10, 20, 50, 100],
  layout: 'total, sizes, prev, pager, next'
}))

// 监听任务完成，自动加载结果
watch(() => scannerStore.currentTask?.status, (newStatus, oldStatus) => {
  if (newStatus === 'completed' && oldStatus === 'running') {
    // 任务完成，加载第一页结果
    currentPage.value = 1
    loadResults()
  }
})

// 方法
const loadResults = () => {
  if (scannerStore.currentTask?.id) {
    scannerStore.fetchResults(currentPage.value, pageSize.value)
  }
}

const handlePageChange = ({ page, size }) => {
  if (size && size !== pageSize.value) {
    pageSize.value = size
    currentPage.value = 1
  } else {
    currentPage.value = page
  }
  loadResults()
}

const viewDetail = (row) => {
  // 跳转到股票分析页面，并传递股票代码、名称和行业
  router.push({
    path: '/analysis',
    query: { 
      stock: row.stock_code,
      name: row.stock_name,
      industry: row.industry
    }
  })
}

// 格式化函数
const formatPrice = (price) => {
  return price?.toFixed(2) || '-'
}

const formatPercent = (percent) => {
  if (percent == null) return '-'
  const prefix = percent > 0 ? '+' : ''
  return `${prefix}${percent.toFixed(2)}%`
}

const formatNumber = (num) => {
  return num?.toFixed(2) || '-'
}

const formatTime = (time) => {
  return formatDate(time, 'MM-DD HH:mm')
}

const getPriceChangeClass = (change) => {
  if (change > 0) return 'text-red-500'
  if (change < 0) return 'text-green-500'
  return 'text-text-secondary'
}

const getSignalClass = (signal) => {
  switch (signal) {
    case 'buy': return 'bg-green-100 text-green-700'
    case 'sell': return 'bg-red-100 text-red-700'
    case 'stop_loss': return 'bg-yellow-100 text-yellow-700'
    default: return 'bg-gray-100 text-gray-700'
  }
}

const getSignalText = (signal) => {
  switch (signal) {
    case 'buy': return '买入'
    case 'sell': return '卖出'
    case 'stop_loss': return '止损'
    default: return signal
  }
}

// 新增辅助函数
const isKDJGoldenCross = (row) => {
  const data = row.indicator_data
  if (!data || !data.prev_kdj_k || !data.prev_kdj_d) return false
  
  // T-1时K<D 且 T时K>D
  return data.prev_kdj_k < data.prev_kdj_d && data.kdj_k > data.kdj_d
}

const isKDJDeathCross = (row) => {
  const data = row.indicator_data
  if (!data || !data.prev_kdj_k || !data.prev_kdj_d) return false
  
  // T-1时K>D 且 T时K<D
  return data.prev_kdj_k > data.prev_kdj_d && data.kdj_k < data.kdj_d
}

const isVolumeBreakout = (row) => {
  const data = row.indicator_data
  if (!data || !data.volume_pressure || !data.volume_pressure_avg) return false
  
  // 当前成交量压力 > 20日平均
  return data.volume_pressure > data.volume_pressure_avg
}

const getBollingerPositionClass = (row) => {
  const data = row.indicator_data
  if (!data || !data.close_price || !data.bollinger_lower || !data.bollinger_upper) return 'text-text-secondary'
  
  const { close_price, bollinger_lower, bollinger_upper, bollinger_middle } = data
  
  if (close_price <= bollinger_lower) {
    return 'text-red-600' // 跌破下轨
  } else if (close_price >= bollinger_upper) {
    return 'text-green-600' // 突破上轨
  } else if (close_price > bollinger_middle) {
    return 'text-blue-600' // 中轨上方
  } else {
    return 'text-yellow-600' // 中轨下方
  }
}

const getBollingerPositionText = (row) => {
  const data = row.indicator_data
  if (!data || !data.close_price || !data.bollinger_lower || !data.bollinger_upper) return '-'
  
  const { close_price, bollinger_lower, bollinger_upper, bollinger_middle, bollinger_distance_pct } = data
  
  if (close_price <= bollinger_lower) {
    return `下轨 (${formatNumber(bollinger_distance_pct)}%)`
  } else if (close_price >= bollinger_upper) {
    return `上轨突破`
  } else if (close_price > bollinger_middle) {
    return `中轨上方`
  } else {
    return `中轨下方`
  }
}

const getTriggerReasons = (row) => {
  const reasons = []
  
  if (isKDJGoldenCross(row)) {
    reasons.push('KDJ金叉信号')
  }
  
  if (isVolumeBreakout(row)) {
    reasons.push('成交量压力突破')
  }
  
  const data = row.indicator_data
  if (data && data.bollinger_distance_pct <= 5) {
    reasons.push('布林带下轨机会')
  }
  
  return reasons.length > 0 ? reasons : ['条件匹配']
}
</script>

<style lang="scss" scoped>
/* 原有样式保持不变 */

/* 技术指标卡片样式 */
.indicator-card {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
  }
}

.indicator-values {
  .value-row, .band-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    &.placeholder {
      opacity: 0;
      pointer-events: none;
    }
  }
  
  .value-label, .band-label {
    font-size: 11px;
    color: var(--text-secondary);
    opacity: 0.8;
    font-weight: 500;
    min-width: 20px;
  }
  
  .value-number {
    font-size: 12px;
    font-weight: 600;
    font-family: 'Monaco', 'Menlo', monospace;
    
    &.primary {
      color: var(--text-primary);
    }
    
    &.secondary {
      color: var(--text-secondary);
      opacity: 0.9;
    }
  }
  
  .band-value {
    font-size: 11px;
    font-weight: 500;
    font-family: 'Monaco', 'Menlo', monospace;
    color: var(--text-primary);
  }
}

/* 布林带特殊样式 */
.bollinger-bands {
  .band-row {
    &.upper .band-label {
      color: #ef4444;
      opacity: 0.9;
    }
    
    &.middle .band-label {
      color: #3b82f6;
      opacity: 0.9;
    }
    
    &.lower .band-label {
      color: #10b981;
      opacity: 0.9;
    }
  }
}

/* 状态徽章样式 */
.indicator-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  margin-top: 8px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  
  .badge-text {
    line-height: 1;
  }
  
  &.golden {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(22, 163, 74, 0.1));
    color: #16a34a;
    border: 1px solid rgba(34, 197, 94, 0.2);
  }
  
  &.death {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.1));
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
  }
  
  &.breakout {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(37, 99, 235, 0.1));
    color: #2563eb;
    border: 1px solid rgba(59, 130, 246, 0.2);
  }
  
  &.position {
    padding: 2px 6px;
    font-size: 9px;
    border-radius: 8px;
    
    &.text-red-600 {
      background: rgba(239, 68, 68, 0.1);
      color: #dc2626;
      border: 1px solid rgba(239, 68, 68, 0.2);
    }
    
    &.text-green-600 {
      background: rgba(34, 197, 94, 0.1);
      color: #16a34a;
      border: 1px solid rgba(34, 197, 94, 0.2);
    }
    
    &.text-blue-600 {
      background: rgba(59, 130, 246, 0.1);
      color: #2563eb;
      border: 1px solid rgba(59, 130, 246, 0.2);
    }
    
    &.text-yellow-600 {
      background: rgba(245, 158, 11, 0.1);
      color: #d97706;
      border: 1px solid rgba(245, 158, 11, 0.2);
    }
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .indicator-card {
    padding: 8px;
  }
  
  .indicator-values {
    .value-number, .band-value {
      font-size: 11px;
    }
  }
}
</style>