<template>
  <div class="dashboard page-container">
    <div class="page-header mb-8">
      <h1 class="text-3xl font-bold text-text-primary">仪表盘</h1>
      <p class="text-text-muted mt-2">欢迎使用股票量化分析系统</p>
    </div>
    
    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <StatCard
        title="总资产"
        value="¥1,234,567"
        change="+5.67%"
        trend="up"
        icon="i-carbon-wallet"
      />
      <StatCard
        title="今日收益"
        value="¥12,345"
        change="+2.34%"
        trend="up"
        icon="i-carbon-trending-up"
      />
      <StatCard
        title="持仓股票"
        value="15"
        change="+2"
        trend="up"
        icon="i-carbon-portfolio"
      />
      <StatCard
        title="关注股票"
        value="42"
        change="+5"
        trend="up"
        icon="i-carbon-star"
      />
    </div>
    
    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- 市场概览图表 -->
      <div class="card p-6">
        <h3 class="text-lg font-semibold mb-4">市场概览</h3>
        <div class="chart-container h-80">          <div class="flex-center h-full text-text-muted">
            <div class="text-center">
              <AppIcon name="chart-line" :size="40" class="mb-2" />
              <p>图表加载中...</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 投资组合分布 -->
      <div class="card p-6">
        <h3 class="text-lg font-semibold mb-4">投资组合分布</h3>
        <div class="chart-container h-80">
          <div class="flex-center h-full text-text-muted">
            <div class="text-center">
              <i class="i-carbon-chart-pie text-4xl mb-2"></i>
              <p>图表加载中...</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 快捷操作 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 热门股票 -->
      <div class="card p-6">
        <h3 class="text-lg font-semibold mb-4">热门股票</h3>
        <div class="space-y-4">
          <div v-for="stock in hotStocks" :key="stock.code" class="flex items-center justify-between">
            <div>
              <div class="font-medium">{{ stock.name }}</div>
              <div class="text-sm text-text-muted">{{ stock.code }}</div>
            </div>
            <div class="text-right">
              <div class="font-mono">¥{{ stock.price }}</div>
              <div :class="stock.change >= 0 ? 'price-up' : 'price-down'" class="text-sm">
                {{ stock.change >= 0 ? '+' : '' }}{{ stock.change }}%
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 自选股票 -->
      <div class="card p-6">
        <h3 class="text-lg font-semibold mb-4">自选股票</h3>
        <div class="space-y-4">
          <div v-for="stock in watchlistStocks" :key="stock.code" class="flex items-center justify-between">
            <div>
              <div class="font-medium">{{ stock.name }}</div>
              <div class="text-sm text-text-muted">{{ stock.code }}</div>
            </div>
            <div class="text-right">
              <div class="font-mono">¥{{ stock.price }}</div>
              <div :class="stock.change >= 0 ? 'price-up' : 'price-down'" class="text-sm">
                {{ stock.change >= 0 ? '+' : '' }}{{ stock.change }}%
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 市场动态 -->
      <div class="card p-6">
        <h3 class="text-lg font-semibold mb-4">市场动态</h3>
        <div class="space-y-4">
          <div v-for="news in marketNews" :key="news.id" class="border-b border-border-color pb-3 last:border-b-0">
            <h4 class="font-medium mb-1 line-clamp-2">{{ news.title }}</h4>
            <p class="text-sm text-text-muted">{{ news.time }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import StatCard from '@/components/common/StatCard.vue'
import AppIcon from '@/components/common/Icon.vue'

// 模拟数据
const hotStocks = ref([
  { code: '000001', name: '平安银行', price: '12.34', change: 2.5 },
  { code: '000002', name: '万科A', price: '23.45', change: -1.2 },
  { code: '000858', name: '五粮液', price: '156.78', change: 3.4 },
  { code: '600036', name: '招商银行', price: '45.67', change: 1.8 }
])

const watchlistStocks = ref([
  { code: '600519', name: '贵州茅台', price: '1678.90', change: 2.1 },
  { code: '000858', name: '五粮液', price: '156.78', change: 3.4 },
  { code: '002415', name: '海康威视', price: '34.56', change: -0.8 }
])

const marketNews = ref([
  { id: 1, title: '央行宣布降准0.5个百分点，释放流动性约1.2万亿元', time: '2小时前' },
  { id: 2, title: '科技股集体上涨，人工智能概念股表现强劲', time: '4小时前' },
  { id: 3, title: '新能源汽车产业链迎来政策利好', time: '6小时前' },
  { id: 4, title: '房地产板块分化明显，龙头企业表现稳健', time: '8小时前' }
])

onMounted(() => {
  // 组件挂载后的初始化逻辑
  console.log('仪表盘页面已加载')
})
</script>

<style scoped lang="scss">
.page-container {
  max-width: 1200px;
  margin: 0 auto;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
