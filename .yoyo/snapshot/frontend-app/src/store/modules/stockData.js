import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/services/api'

export const useStockDataStore = defineStore('stockData', () => {  // 状态
  const stocks = ref([])
  const currentStock = ref(null)
  const klineData = ref([])
  const indicators = ref({})
  const watchlist = ref([])
  const watchlistStocksData = ref([]) // 用于存储自选股的详细信息
  const loading = ref(false)
  const error = ref(null)
  
  // 实时数据
  const realtimeData = ref(new Map())
  const isRealtimeConnected = ref(false)
    // 计算属性
  const stockCount = computed(() => stocks.value.length)
  const currentStockCode = computed(() => currentStock.value?.code || null)
  const watchlistStocks = computed({
    get() {
      // 如果 watchlistStocksData 有数据，优先使用
      if (watchlistStocksData.value.length > 0) {
        return watchlistStocksData.value
      }
      // 否则从 stocks 中过滤出自选股
      return stocks.value.filter(stock => watchlist.value.includes(stock.code))
    },
    set(value) {
      watchlistStocksData.value = value
    }
  })
  
  // 动作
  const fetchStocks = async (params = {}) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.stockList.getStockList(params)
      console.log('股票列表', response.data);
      
      stocks.value = response.data.stocks || []
      return response
    } catch (err) {
      error.value = err.message || '获取股票列表失败'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const fetchStockDetail = async (code) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.stockList.getStockInfo(code)
      currentStock.value = response.data
      return response
    } catch (err) {
      error.value = err.message || '获取股票详情失败'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const fetchKlineData = async (code, period = '1d', limit = 200) => {
    loading.value = true
    error.value = null
    
    try {
      const response = await api.stockData.getKlineData(code, period, { limit })
      klineData.value = response.data
      return response
    } catch (err) {
      error.value = err.message || '获取K线数据失败'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const fetchIndicators = async (code, indicatorTypes = []) => {
    loading.value = true
    error.value = null
    
    try {
      // 假设 indicatorTypes 是指标名数组，批量获取
      const promises = indicatorTypes.map(type => api.indicator.getIndicator(code, type))
      const results = await Promise.all(promises)
      indicators.value = {
        ...indicators.value,
        [code]: results.map((res, idx) => ({ type: indicatorTypes[idx], data: res.data }))
      }
      return results
    } catch (err) {
      error.value = err.message || '获取技术指标失败'
      throw err
    } finally {
      loading.value = false
    }
  }
  
  const addToWatchlist = async (code) => {
    try {
      await api.watchlist.addToWatchlist(code)
      if (!watchlist.value.includes(code)) {
        watchlist.value.push(code)
        saveWatchlist()
      }
      // 重新加载自选股数据以获取最新信息
      await loadWatchlist()
    } catch (err) {
      error.value = err.message || '添加自选股失败'
      throw err
    }
  }
  
  const removeFromWatchlist = async (code) => {
    try {
      await api.watchlist.removeFromWatchlist(code)
      const index = watchlist.value.indexOf(code)
      if (index > -1) {
        watchlist.value.splice(index, 1)
        saveWatchlist()
      }
      // 同时从 watchlistStocks 中移除
      const stockIndex = watchlistStocksData.value.findIndex(stock => stock.code === code)
      if (stockIndex > -1) {
        watchlistStocksData.value.splice(stockIndex, 1)
      }
    } catch (err) {
      error.value = err.message || '移除自选股失败'
      throw err
    }
  }
  
  const toggleWatchlist = async (code) => {
    if (watchlist.value.includes(code)) {
      await removeFromWatchlist(code)
    } else {
      await addToWatchlist(code)
    }
  }
  
  const saveWatchlist = () => {
    localStorage.setItem('watchlist', JSON.stringify(watchlist.value))
  }
  const loadWatchlist = async () => {
    try {
      // 首先尝试从服务器获取用户自选股
      const response = await api.watchlist.getWatchlist()
      
      if (response.data && response.success) {
        // 新的 CommonResponse 格式
        const watchlistData = response.data || []
        
        // 提取股票代码并存储到 watchlist
        watchlist.value = watchlistData.map(item => item.stock_code)
        
        console.log('获取自选股列表:', watchlistData);
        // 将包含股票详细信息的数据转换为前端需要的格式
        watchlistStocksData.value = watchlistData.map(item => {
          const stockInfo = item.stock_info
          if (stockInfo) {
            return {
              code: stockInfo.code,
              name: stockInfo.name,
              exchange: stockInfo.exchange,
              industry: stockInfo.industry || '未分类',
              price: stockInfo.price || 0,
              changePercent: stockInfo.change_percent || 0,
              volume: stockInfo.volume || 0,
              addedAt: item.added_at,
              sortOrder: item.sort_order
            }
          } else {
            // 如果没有股票信息，创建基本对象
            return {
              code: item.stock_code,
              name: item.stock_code, // 使用代码作为名称
              exchange: 'unknown',
              industry: '未分类',
              price: 0,
              changePercent: 0,
              volume: 0,
              addedAt: item.added_at,
              sortOrder: item.sort_order
            }
          }
        })
      } else {
        // 兼容旧格式
        if (response.data && response.data.stocks) {
          // 服务器返回的是股票详情列表，提取代码
          watchlist.value = response.data.stocks.map(stock => stock.code)
        } else if (response.data && Array.isArray(response.data)) {
          // 如果直接返回股票代码数组
          watchlist.value = response.data
        }
      }
      
      saveWatchlist() // 同步到本地存储
    } catch (err) {
      console.warn('从服务器获取自选股失败，使用本地数据:', err.message)
      // 如果服务器获取失败，回退到本地存储
      const saved = localStorage.getItem('watchlist')
      if (saved) {
        try {
          watchlist.value = JSON.parse(saved)
        } catch (parseErr) {
          console.error('解析收藏列表失败:', parseErr)
          watchlist.value = []
        }
      }
    }
  }
  
  const updateRealtimeData = (code, data) => {
    realtimeData.value.set(code, {
      ...data,
      timestamp: Date.now()
    })
  }
  
  const getRealtimeData = (code) => {
    return realtimeData.value.get(code) || null
  }
  
  const setRealtimeConnection = (connected) => {
    isRealtimeConnected.value = connected
  }
  
  const clearData = () => {
    stocks.value = []
    currentStock.value = null
    klineData.value = []
    indicators.value = {}
    error.value = null
  }
  
  const setCurrentStock = (stock) => {
    currentStock.value = stock
  }
  
  const updateStock = (code, updates) => {
    const index = stocks.value.findIndex(stock => stock.code === code)
    if (index > -1) {
      stocks.value[index] = { ...stocks.value[index], ...updates }
    }
  }
    return {
    // 状态
    stocks,
    currentStock,
    klineData,
    indicators,
    watchlist,
    watchlistStocksData,
    loading,
    error,
    realtimeData,
    isRealtimeConnected,
    
    // 计算属性
    stockCount,
    currentStockCode,
    watchlistStocks,
    
    // 动作
    fetchStocks,
    fetchStockDetail,
    fetchKlineData,
    fetchIndicators,
    addToWatchlist,
    removeFromWatchlist,
    toggleWatchlist,
    loadWatchlist,
    updateRealtimeData,
    getRealtimeData,
    setRealtimeConnection,
    clearData,
    setCurrentStock,
    updateStock
  }
})
