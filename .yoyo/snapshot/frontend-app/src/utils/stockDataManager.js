/**
 * 数据管理和API调用
 */
import { api } from "@/services/api";
import {
  convertPeriodToBackend,
  formatVolume,
  formatMarketCap,
} from "./chartHelpers";

export class StockDataManager {
  constructor() {
    this.loading = false;
    this.error = null;
    this.chartData = null;
    this.stockInfo = null;
  }

  async loadStockAnalysis(selectedStock, selectedPeriod, selectedRange) {
    if (!selectedStock) return;

    console.log("开始加载股票分析数据:", selectedStock);
    this.loading = true;
    this.error = null;

    try {
      // 计算日期范围
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - parseInt(selectedRange));
      const startDateStr = startDate.toISOString().split("T")[0];
      const endDateStr = endDate.toISOString().split("T")[0];

      // 转换周期格式
      const backendPeriod = convertPeriodToBackend(selectedPeriod);

      // 并行获取数据
      const [klineData, macdData] = await Promise.all([
        // 获取K线数据（包含指标）
        api.stockData
          .getKlineData(selectedStock, backendPeriod, {
            start_date: startDateStr,
            end_date: endDateStr,
            with_indicators: true,
            indicators: ["macd"],
          })
          .then((ret) => ret.data)
          .catch((err) => {
            console.warn("K线数据获取失败，使用备用方案:", err);
            return api.stockData.getKlineData(selectedStock, backendPeriod, {
              start_date: startDateStr,
              end_date: endDateStr,
            });
          }),
        // 单独获取MACD指标数据
        api.indicator
          .getIndicator(selectedStock, "macd", {
            start_date: startDateStr,
            end_date: endDateStr,
            freq: backendPeriod,
          })
          .then((ret) => ret.data)
          .catch((err) => {
            console.warn("MACD指标获取失败:", err);
            return null;
          }),
      ]);

      // 处理K线数据
      if (!klineData || !klineData.kline_data) {
        throw new Error("无法获取K线数据");
      }

      this.chartData = {
        kline: klineData,
        macd: macdData || klineData.indicators?.macd,
        volume: klineData.kline_data,
      };

      // 获取股票基本信息，并传入K线数据用于提取实时信息
      await this.loadStockInfo(selectedStock, klineData);

      console.log("股票分析数据加载成功", {
        klineData: this.chartData.kline,
        macdData: this.chartData.macd,
        volumeData: this.chartData.volume,
        stockInfo: this.stockInfo,
      });
      return this.chartData;
    } catch (err) {
      console.error("加载股票分析数据失败:", err);
      this.error =
        err.response?.data?.message || err.message || "加载股票分析数据失败";
      throw err;
    } finally {
      this.loading = false;
    }
  }

  async loadStockInfo(selectedStock, klineData = null) {
    try {
      console.log("开始获取股票基本信息:", selectedStock);
      
      // 首先尝试获取股票基本信息（名称等）
      let stockName = "未知股票";
      let totalShares = null;
      
      try {
        const { data: stockDetail } = await api.stockList.getStockInfo(selectedStock);
        console.log("股票基本信息:", stockDetail);
        stockName = stockDetail.name || "未知股票";
        totalShares = stockDetail.total_shares;
      } catch (err) {
        console.warn("获取股票基本信息失败，使用默认名称:", err);
        
        // 尝试从默认股票列表中获取名称
        const defaultStocks = [
          { code: "000001", name: "平安银行" },
          { code: "000002", name: "万科A" },
          { code: "000858", name: "五粮液" },
          { code: "002230", name: "科大讯飞" },
          { code: "002415", name: "海康威视" },
          { code: "600000", name: "浦发银行" },
          { code: "600036", name: "招商银行" },
          { code: "600519", name: "贵州茅台" },
          { code: "600887", name: "伊利股份" },
          { code: "300072", name: "三聚环保" },
        ];

        const foundStock = defaultStocks.find(
          (stock) => stock.code === selectedStock
        );
        stockName = foundStock?.name || "未知股票";
      }

      // 如果有K线数据，从中提取实时信息
      if (klineData && klineData.kline_data && klineData.kline_data.length > 0) {
        const data = klineData.kline_data;
        const latestData = data[data.length - 1]; // 最新一天的数据
        const previousData = data.length > 1 ? data[data.length - 2] : null; // 前一天的数据
        
        // 优先使用后端计算的涨跌幅，如果没有则前端计算
        let changePercent = 0;
        let changeAmount = 0; // 涨跌额
        
        if (latestData.change_pct !== undefined && latestData.change_pct !== null) {
          // 使用后端计算的涨跌幅
          changePercent = parseFloat(latestData.change_pct);
          
          // 计算涨跌额：根据涨跌幅和前一日收盘价计算
          if (previousData && previousData.close) {
            changeAmount = latestData.close - previousData.close;
          }
        } else if (previousData && previousData.close && latestData.close) {
          // 前端计算涨跌幅和涨跌额（备用方案）
          changeAmount = latestData.close - previousData.close;
          changePercent = (changeAmount / previousData.close) * 100;
        }
        
        // 计算市值（如果有总股本信息）
        let marketCap = null;
        if (totalShares && latestData.close) {
          marketCap = totalShares * latestData.close;
        }
        
        this.stockInfo = {
          code: selectedStock,
          name: stockName,
          price: latestData.close?.toFixed(2) || "0.00",
          changePercent: changePercent.toFixed(2),
          changeAmount: changeAmount.toFixed(2), // 添加涨跌额
          volume: formatVolume(latestData.volume),
          marketCap: formatMarketCap(marketCap),
        };
        
        console.log("从K线数据提取的股票信息:", this.stockInfo, "原始change_pct:", latestData.change_pct);
      } else {
        // 没有K线数据时的默认值
        this.stockInfo = {
          code: selectedStock,
          name: stockName,
          price: "0.00",
          changePercent: "0.00",
          changeAmount: "0.00", // 添加涨跌额默认值
          volume: "0万手",
          marketCap: "0亿",
        };
        
        console.log("使用默认股票信息:", this.stockInfo);
      }
    } catch (err) {
      console.error("获取股票信息失败:", err);
      
      this.stockInfo = {
        code: selectedStock,
        name: "未知股票",
        price: "0.00",
        changePercent: "0.00",
        changeAmount: "0.00", // 添加涨跌额默认值
        volume: "0万手",
        marketCap: "0亿",
      };
    }
  }

  async loadHotStocks() {
    try {
      const response = await api.stockSearch.getPopularStocks({ limit: 8 });
      return response.data || [];
    } catch (error) {
      console.error("获取热门股票失败:", error);
      // 如果API失败，使用默认热门股票
      return [
        { code: "000001", name: "平安银行", industry: "银行" },
        { code: "000002", name: "万科A", industry: "房地产" },
        { code: "000858", name: "五粮液", industry: "白酒" },
        { code: "002230", name: "科大讯飞", industry: "人工智能" },
        { code: "002415", name: "海康威视", industry: "安防" },
        { code: "600036", name: "招商银行", industry: "银行" },
        { code: "600519", name: "贵州茅台", industry: "白酒" },
        { code: "600887", name: "伊利股份", industry: "乳业" },
      ];
    }
  }

  getChartData() {
    return this.chartData;
  }

  getStockInfo() {
    return this.stockInfo;
  }

  getError() {
    return this.error;
  }

  isLoading() {
    return this.loading;
  }
}
