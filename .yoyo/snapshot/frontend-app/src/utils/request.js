import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1',
  timeout: 30000,
  withCredentials: true // 允许携带cookie
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 从 localStorage 获取 token
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 从 cookie 获取 session_id（如果需要）
    const sessionId = document.cookie
      .split('; ')
      .find(row => row.startsWith('scan_session_id='))
      ?.split('=')[1]
    
    if (sessionId) {
      config.headers['X-Session-ID'] = sessionId
    }
    
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 如果响应中包含 session_id，保存到 cookie
    const setCookie = response.headers['set-cookie']
    if (setCookie) {
      // 浏览器会自动处理 set-cookie
    }
    
    return response.data
  },
  error => {
    console.error('响应错误:', error)
    
    // 处理错误
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          ElMessage.error('会话已过期，请重新登录')
          // 可以在这里处理登录跳转
          break
        case 403:
          ElMessage.error('没有权限访问')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error(data.detail || '服务器错误')
          break
        default:
          ElMessage.error(data.detail || `请求失败: ${status}`)
      }
    } else if (error.request) {
      ElMessage.error('网络错误，请检查网络连接')
    } else {
      ElMessage.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

export default service