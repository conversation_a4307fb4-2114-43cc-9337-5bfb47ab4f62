/**
 * 图表相关工具函数
 */

// 自适应移动平均线计算
export const calculateMA = (data, period) => {
  const result = [];
  if (data.length < 3) {
    return Array(data.length).fill(null);
  }
  
  // 自适应周期：根据数据长度调整周期
  const adaptivePeriod = Math.min(period, Math.max(3, Math.floor(data.length / 2)));
  
  for (let i = 0; i < data.length; i++) {
    if (i < adaptivePeriod - 1) {
      // 对于数据点不足的情况，使用已有数据点计算
      const currentPeriod = Math.min(i + 1, adaptivePeriod);
      const sum = data.slice(Math.max(0, i - currentPeriod + 1), i + 1).reduce((a, b) => a + b, 0);
      result.push((sum / currentPeriod).toFixed(2));
    } else {
      const sum = data.slice(i - adaptivePeriod + 1, i + 1).reduce((a, b) => a + b, 0);
      result.push((sum / adaptivePeriod).toFixed(2));
    }
  }
  return result;
};

// 计算自适应布林带
export const calculateBollingerBands = (data, period = 20, stdDev = 2) => {
  if (data.length < 3) {
    return {
      middle: Array(data.length).fill(null),
      upper: Array(data.length).fill(null),
      lower: Array(data.length).fill(null),
    };
  }
  
  // 自适应周期：根据数据长度调整周期
  const adaptivePeriod = Math.min(period, Math.max(3, Math.floor(data.length / 2)));
  
  const result = {
    middle: [],
    upper: [],
    lower: [],
  };

  for (let i = 0; i < data.length; i++) {
    if (i < adaptivePeriod - 1) {
      // 对于数据点不足的情况，使用已有数据点计算
      const currentPeriod = Math.min(i + 1, adaptivePeriod);
      const slice = data.slice(Math.max(0, i - currentPeriod + 1), i + 1);
      const avg = slice.reduce((a, b) => a + b, 0) / slice.length;
      const variance = slice.reduce((a, b) => a + Math.pow(b - avg, 2), 0) / slice.length;
      const std = Math.sqrt(variance);

      result.middle.push(avg.toFixed(2));
      result.upper.push((avg + stdDev * std).toFixed(2));
      result.lower.push((avg - stdDev * std).toFixed(2));
    } else {
      const slice = data.slice(i - adaptivePeriod + 1, i + 1);
      const avg = slice.reduce((a, b) => a + b, 0) / adaptivePeriod;
      const variance = slice.reduce((a, b) => a + Math.pow(b - avg, 2), 0) / adaptivePeriod;
      const std = Math.sqrt(variance);

      result.middle.push(avg.toFixed(2));
      result.upper.push((avg + stdDev * std).toFixed(2));
      result.lower.push((avg - stdDev * std).toFixed(2));
    }
  }

  return result;
};

// 格式化成交量
export const formatVolume = (volume) => {
  if (!volume) return "0";
  if (volume >= 100000000) {
    return (volume / 100000000).toFixed(2) + "亿";
  } else if (volume >= 10000) {
    return (volume / 10000).toFixed(2) + "万";
  }
  return volume.toString();
};

// 格式化市值
export const formatMarketCap = (marketCap) => {
  if (!marketCap) return "0";
  if (marketCap >= 100000000) {
    return (marketCap / 100000000).toFixed(2) + "亿";
  }
  return marketCap.toString();
};

// 转换前端周期格式到后端格式
export const convertPeriodToBackend = (period) => {
  const periodMap = {
    D: "D",
    D1: "D",
    W: "W",
    W1: "W",
    M: "M",
    M1: "M",
  };
  return periodMap[period] || "D";
};

// 计算自适应MA周期
export const calculateAdaptivePeriods = (dataLength) => {
  return {
    ma5: Math.min(5, Math.max(3, Math.floor(dataLength / 6))),
    ma10: Math.min(10, Math.max(5, Math.floor(dataLength / 3))),
    ma20: Math.min(20, Math.max(10, Math.floor(dataLength / 2))),
  };
};
