/**
 * 图表渲染器
 */
import * as echarts from 'echarts';
import { 
  calculateMA, 
  calculateBollingerBands, 
  calculateAdaptivePeriods 
} from './chartHelpers';
import { 
  getTooltipCommonConfig,
  createKlineTooltipFormatter,
  createMacdTooltipFormatter,
  createVolumeTooltipFormatter
} from './tooltipHelpers';

// K线图表渲染器
export class KlineChartRenderer {
  constructor(chartContainer, getIndicatorSettings) {
    this.chartContainer = chartContainer;
    this.getIndicatorSettings = getIndicatorSettings;
    this.chart = null;
  }

  render(klineData, chartOverlay) {
    console.log("K线图表渲染器开始渲染:", {
      container: this.chartContainer,
      chartRef: this.chartContainer?.chartRef,
      chartRefValue: this.chartContainer?.chartRef?.value,
      klineData: klineData
    });
    
    if (!this.chartContainer?.chartRef || !klineData) {
      console.warn("K线图表渲染条件不满足:", {
        hasContainer: !!this.chartContainer,
        hasChartRef: !!this.chartContainer?.chartRef,
        hasChartRefValue: !!this.chartContainer?.chartRef?.value,
        hasKlineData: !!klineData
      });
      return;
    }

    // 销毁旧图表
    if (this.chart) {
      this.chart.dispose();
    }

    const chartDom = this.chartContainer.chartRef;
    console.log("初始化图表，DOM元素:", chartDom);
    this.chart = echarts.init(chartDom);

    const data = klineData.kline_data || [];
    if (data.length === 0) {
      console.warn("K线数据为空");
      return;
    }

    // 准备数据，格式化日期显示
    const dates = data.map(item => {
      const dateStr = item.date;
      if (dateStr.includes('T')) {
        return dateStr.split('T')[0]; // 仅保留日期部分，去掉时间
      }
      return dateStr;
    });
    const values = data.map(item => [item.open, item.close, item.low, item.high]);
    const closePrices = data.map(item => item.close);
    const dataLength = closePrices.length;

    // 计算自适应周期
    const adaptivePeriods = calculateAdaptivePeriods(dataLength);

    // 计算指标
    const ma5 = calculateMA(closePrices, adaptivePeriods.ma5);
    const ma10 = calculateMA(closePrices, adaptivePeriods.ma10);
    const ma20 = calculateMA(closePrices, adaptivePeriods.ma20);

    const bollingerSettings = this.getIndicatorSettings('bollinger');
    const bollinger = calculateBollingerBands(
      closePrices,
      bollingerSettings.period || 20,
      bollingerSettings.stdDev || 2
    );

    // 基础配置
    const option = {
      animation: true,
      backgroundColor: "transparent",
      grid: {
        left: "50px",
        right: "50px",
        top: "60px",
        bottom: "60px",
      },
      xAxis: {
        type: "category",
        data: dates,
        axisLine: { lineStyle: { color: "#666" } },
        axisLabel: { color: "#999" },
      },
      yAxis: {
        type: "value",
        scale: true,
        axisLine: { lineStyle: { color: "#666" } },
        axisLabel: { color: "#999" },
        splitLine: { lineStyle: { color: "#333" } },
      },
      tooltip: {
        ...getTooltipCommonConfig(),
        formatter: createKlineTooltipFormatter(
          data, values, ma5, ma10, ma20, bollinger, adaptivePeriods, chartOverlay
        ),
      },
      series: [
        {
          name: "K线",
          type: "candlestick",
          data: values,
          itemStyle: {
            color: "#ef4444",     // 涨：红色
            color0: "#22c55e",    // 跌：绿色
            borderColor: "#ef4444",
            borderColor0: "#22c55e",
          },
        },
      ],
    };

    // 添加MA线
    if (chartOverlay === "ma") {
      option.series.push(
        {
          name: `MA${adaptivePeriods.ma5}`,
          type: "line",
          data: ma5,
          smooth: true,
          lineStyle: { color: "#ff6b6b", width: 1 },
          showSymbol: false,
        },
        {
          name: `MA${adaptivePeriods.ma10}`,
          type: "line",
          data: ma10,
          smooth: true,
          lineStyle: { color: "#4ecdc4", width: 1 },
          showSymbol: false,
        },
        {
          name: `MA${adaptivePeriods.ma20}`,
          type: "line",
          data: ma20,
          smooth: true,
          lineStyle: { color: "#45b7d1", width: 1 },
          showSymbol: false,
        }
      );
    }

    // 添加布林带
    if (chartOverlay === "bollinger") {
      const adaptiveBollingerPeriod = Math.min(
        bollingerSettings.period || 20,
        Math.max(3, Math.floor(dataLength / 2))
      );
      
      option.series.push(
        {
          name: `布林上轨(${adaptiveBollingerPeriod})`,
          type: "line",
          data: bollinger.upper,
          smooth: true,
          lineStyle: { color: "#ff6b6b", width: 1, type: "dashed" },
          showSymbol: false,
        },
        {
          name: `布林中轨(${adaptiveBollingerPeriod})`,
          type: "line",
          data: bollinger.middle,
          smooth: true,
          lineStyle: { color: "#4ecdc4", width: 1 },
          showSymbol: false,
        },
        {
          name: `布林下轨(${adaptiveBollingerPeriod})`,
          type: "line",
          data: bollinger.lower,
          smooth: true,
          lineStyle: { color: "#22c55e", width: 1, type: "dashed" },
          showSymbol: false,
        }
      );
    }

    this.chart.setOption(option);
    console.log("K线图表选项设置完成");
  }

  resize() {
    if (this.chart) {
      this.chart.resize();
    }
  }

  dispose() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  }
}

// MACD图表渲染器
export class MacdChartRenderer {
  constructor(chartContainer) {
    this.chartContainer = chartContainer;
    this.chart = null;
  }

  render(macdData) {
    if (!this.chartContainer?.chartRef || !macdData) {
      console.warn("MACD图表渲染条件不满足");
      return;
    }

    // 销毁旧图表
    if (this.chart) {
      this.chart.dispose();
    }

    const chartDom = this.chartContainer.chartRef;
    this.chart = echarts.init(chartDom);

    // 解析MACD数据
    let dates = [], dif = [], dea = [], histogram = [];

    // 日期格式化函数
    const formatDate = (dateStr) => {
      if (dateStr && dateStr.includes('T')) {
        return dateStr.split('T')[0]; // 仅保留日期部分，去掉时间
      }
      return dateStr;
    };

    if (macdData.data && Array.isArray(macdData.data)) {
      dates = macdData.data.map(item => formatDate(item.date));
      dif = macdData.data.map(item => parseFloat(item.MACD_12_26_9 || item.dif || item.diff || 0));
      dea = macdData.data.map(item => parseFloat(item.MACDs_12_26_9 || item.dea || item.signal || 0));
      histogram = macdData.data.map(item => parseFloat(item.MACDh_12_26_9 || item.histogram || item.hist || 0));
    } else if (macdData.date && Array.isArray(macdData.date)) {
      dates = macdData.date.map(formatDate);
      dif = macdData.dif || macdData.diff || [];
      dea = macdData.dea || macdData.signal || [];
      histogram = macdData.histogram || macdData.hist || [];
    } else if (Array.isArray(macdData)) {
      dates = macdData.map(item => formatDate(item.date));
      dif = macdData.map(item => parseFloat(item.MACD_12_26_9 || item.dif || item.diff || 0));
      dea = macdData.map(item => parseFloat(item.MACDs_12_26_9 || item.dea || item.signal || 0));
      histogram = macdData.map(item => parseFloat(item.MACDh_12_26_9 || item.histogram || item.hist || 0));
    }

    const option = {
      animation: true,
      backgroundColor: "transparent",
      grid: {
        left: "50px",
        right: "50px",
        top: "30px",
        bottom: "50px",
      },
      xAxis: {
        type: "category",
        data: dates,
        axisLine: { lineStyle: { color: "#666" } },
        axisLabel: { color: "#999", fontSize: 11 },
      },
      yAxis: {
        type: "value",
        axisLine: { lineStyle: { color: "#666" } },
        axisLabel: { color: "#999", fontSize: 11 },
        splitLine: { lineStyle: { color: "#333" } },
      },
      tooltip: {
        ...getTooltipCommonConfig(),
        formatter: createMacdTooltipFormatter(dates, dif, dea, histogram),
      },
      legend: {
        textStyle: { color: "#999" },
        top: 5,
      },
      series: [
        {
          name: "DIF",
          type: "line",
          data: dif,
          lineStyle: { color: "#ff6b6b", width: 1 },
          showSymbol: false,
        },
        {
          name: "DEA",
          type: "line",
          data: dea,
          lineStyle: { color: "#4ecdc4", width: 1 },
          showSymbol: false,
        },
        {
          name: "HISTOGRAM",
          type: "bar",
          data: histogram,
          itemStyle: {
            color: function (params) {
              return params.value >= 0 ? "#ef4444" : "#22c55e";  // 正值红色，负值绿色
            },
          },
        },
      ],
    };

    this.chart.setOption(option);
  }

  resize() {
    if (this.chart) {
      this.chart.resize();
    }
  }

  dispose() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  }
}

// 成交量图表渲染器
export class VolumeChartRenderer {
  constructor(chartContainer) {
    this.chartContainer = chartContainer;
    this.chart = null;
  }

  render(volumeData) {
    if (!this.chartContainer?.chartRef || !volumeData) {
      console.warn("成交量图表渲染条件不满足");
      return;
    }

    // 销毁旧图表
    if (this.chart) {
      this.chart.dispose();
    }

    const chartDom = this.chartContainer.chartRef;
    this.chart = echarts.init(chartDom);

    // 格式化日期，仅保留日期部分
    const dates = volumeData.map(item => {
      const dateStr = item.date;
      if (dateStr && dateStr.includes('T')) {
        return dateStr.split('T')[0]; // 仅保留日期部分，去掉时间
      }
      return dateStr;
    });
    const volumes = volumeData.map(item => item.volume);

    if (dates.length === 0 || volumes.length === 0) {
      console.warn("成交量数据为空");
      return;
    }

    const option = {
      animation: true,
      backgroundColor: "transparent",
      grid: {
        left: "50px",
        right: "50px",
        top: "30px",
        bottom: "50px",
      },
      xAxis: {
        type: "category",
        data: dates,
        axisLine: { lineStyle: { color: "#666" } },
        axisLabel: { color: "#999", fontSize: 11 },
      },
      yAxis: {
        type: "value",
        axisLine: { lineStyle: { color: "#666" } },
        axisLabel: {
          color: "#999",
          fontSize: 11,
          formatter: function (value) {
            return value >= 100000000 ? (value / 100000000).toFixed(1) + '亿' : 
                   value >= 10000 ? (value / 10000).toFixed(1) + '万' : value;
          },
        },
        splitLine: { lineStyle: { color: "#333" } },
      },
      tooltip: {
        ...getTooltipCommonConfig(),
        formatter: createVolumeTooltipFormatter(volumeData),
      },
      series: [
        {
          name: "成交量",
          type: "bar",
          data: volumes,
          itemStyle: {
            color: function (params) {
              const index = params.dataIndex;
              if (index === 0) return "#4ecdc4";
              const current = volumeData[index];
              const previous = volumeData[index - 1];
              return current.close >= previous.close ? "#ef4444" : "#22c55e";  // 涨红跌绿
            },
          },
        },
      ],
    };

    this.chart.setOption(option);
  }

  resize() {
    if (this.chart) {
      this.chart.resize();
    }
  }

  dispose() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  }
}
