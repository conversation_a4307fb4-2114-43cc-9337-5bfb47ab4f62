import { defineConfig, presetUno, presetAttributify, presetIcons } from 'unocss'

export default defineConfig({
  presets: [
    presetUno(),
    presetAttributify(), 
    presetIcons({
      scale: 1.2,
      warn: true,
      collections: {
        carbon: () => import('@iconify-json/carbon/icons.json', { with: { type: 'json' } }).then(i => i.default)
      }
    }),
  ],
  theme: {
    colors: {
      'bg-primary': 'var(--bg-primary)',
      'bg-secondary': 'var(--bg-secondary)',
      'bg-tertiary': 'var(--bg-tertiary)',
      'bg-quaternary': 'var(--bg-quaternary)',
      'text-primary': 'var(--text-primary)',
      'text-secondary': 'var(--text-secondary)',
      'text-muted': 'var(--text-muted)',
      'text-accent': 'var(--text-accent)',
      'border-color': 'var(--border-color)',
      'border-light': 'var(--border-light)',
      'accent-primary': 'var(--accent-primary)',
      'accent-secondary': 'var(--accent-secondary)',
      'accent-tertiary': 'var(--accent-tertiary)',
      'price-up': 'var(--price-up)',
      'price-down': 'var(--price-down)',
      'price-flat': 'var(--price-flat)',
      'volume-color': 'var(--volume-color)',
    },
  },
  shortcuts: {
    'btn-base': 'px-4 py-2 rounded-lg font-medium transition-all duration-300 cursor-pointer',
    'btn-primary': 'btn-base bg-accent-primary text-white hover:bg-accent-secondary',
    'btn-secondary': 'btn-base bg-bg-tertiary text-text-primary border border-border-color hover:bg-bg-quaternary',
    'card': 'bg-bg-secondary border border-border-color rounded-2xl shadow-md transition-all duration-300 hover:shadow-xl',
    'input-field': 'bg-bg-tertiary border border-border-color rounded-xl px-4 py-3 text-text-primary transition-all duration-300 focus:outline-none focus:border-accent-primary',
    'flex-center': 'flex items-center justify-center',
    'price-up': 'text-price-up font-semibold',
    'price-down': 'text-price-down font-semibold',
    'price-flat': 'text-price-flat font-medium',
  },
})
