"""集成测试配置"""
import pytest
from typing import AsyncGenerator, Dict, Any
import logging

from app.services.data_fetcher import DataFetcherFactory

logger = logging.getLogger(__name__)

@pytest.fixture(autouse=True)
async def setup_data_fetchers() -> AsyncGenerator[None, None]:
    """设置数据获取器"""
    # 重置工厂状态
    await DataFetcherFactory.reset()
    
    yield
    
    # 清理
    await DataFetcherFactory.reset()

@pytest.fixture
def test_stock_code() -> str:
    """用于测试的股票代码"""
    return "000001"  # 平安银行，用于测试的标准股票

@pytest.fixture
def test_index_code() -> str:
    """用于测试的指数代码"""
    return "000300"  # 沪深300，用于测试的标准指数
