"""数据提供者集成测试"""
import os
import pytest
from datetime import datetime, timedelta

from app.services.data_fetcher import DataFetcherFactory
from app.core.config import settings

@pytest.mark.integration
class TestDataProviderIntegration:
    """数据提供者集成测试"""
    
    @pytest.fixture(autouse=True)
    def setup(self):
        """设置测试环境"""
        DataFetcherFactory.reset()
        yield
        DataFetcherFactory.reset()

    @pytest.mark.asyncio
    async def test_tushare_data_fetch(self):
        """测试Tushare数据获取完整流程"""
        token = os.getenv("TUSHARE_TOKEN")
        if not token:
            pytest.skip("需要设置TUSHARE_TOKEN环境变量才能运行Tushare集成测试")
            
        fetcher = DataFetcherFactory.get_fetcher(
            "tushare",
            api_token=token
        )
        
        # 获取股票列表
        stocks = await fetcher.get_stock_list()
        assert len(stocks) > 0
        assert all(
            key in stocks[0] 
            for key in ["code", "name", "industry", "market"]
        )
        
        # 获取日线数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        daily_data = await fetcher.get_daily_data(
            stocks[0]["code"],
            start_date=start_date,
            end_date=end_date
        )
        assert len(daily_data) > 0
        assert all(
            key in daily_data[0] 
            for key in ["date", "open", "high", "low", "close"]
        )
        
        # 获取实时行情
        quotes = await fetcher.get_realtime_quotes([stocks[0]["code"]])
        assert len(quotes) > 0
        assert all(
            key in quotes[0] 
            for key in ["code", "price", "volume", "time"]
        )

    @pytest.mark.asyncio
    async def test_akshare_data_fetch(self):
        token = os.getenv("AKSHARE_TOKEN")
        if not token:
            pytest.skip("需要设置AKSHARE_TOKEN环境变量才能运行Akshare集成测试")
        """测试Akshare数据获取完整流程"""
        fetcher = DataFetcherFactory.get_fetcher("akshare", api_token=token)
        
        # 获取股票列表
        stocks = await fetcher.get_stock_list()
        assert len(stocks) > 0
        assert all(
            key in stocks[0] 
            for key in ["code", "name", "industry", "market"]
        )
        
        # 获取日线数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        daily_data = await fetcher.get_daily_data(
            stocks[0]["code"],
            start_date=start_date,
            end_date=end_date
        )
        assert len(daily_data) > 0
        assert all(
            key in daily_data[0] 
            for key in ["date", "open", "high", "low", "close"]
        )
        
        # 获取实时行情
        quotes = await fetcher.get_realtime_quotes([stocks[0]["code"]])
        assert len(quotes) > 0
        assert all(
            key in quotes[0] 
            for key in ["code", "price", "volume", "time"]
        )

    @pytest.mark.asyncio
    async def test_mairui_data_fetch(self):
        """测试麦睿数据获取完整流程"""
        token = settings.MAIRUI_TOKEN
        if not token:
            pytest.skip("需要设置MAIRUI_TOKEN才能运行麦睿集成测试")
            
        fetcher = DataFetcherFactory.get_fetcher(
            "mairui",
            licence=token
        )
        
        # 获取股票列表
        stocks = await fetcher.get_stock_list()
        assert len(stocks) > 0
        assert all(
            key in stocks[0] 
            for key in ["code", "name", "market"]
        )
        
        # 获取日线数据（不带时间范围）
        daily_data = await fetcher.get_daily_data(stocks[0]["code"])
        assert len(daily_data) > 0
        assert all(
            key in daily_data[0] 
            for key in [
                "date", "open", "high", "low", "close",
                "volume", "amount", "turnover", "change_pct"
            ]
        )
        
        # 获取日线数据（带时间范围）
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        daily_data_range = await fetcher.get_daily_data(
            stocks[0]["code"],
            start_date=start_date,
            end_date=end_date
        )
        assert len(daily_data_range) > 0
        assert all(
            key in daily_data_range[0] 
            for key in [
                "date", "open", "high", "low", "close",
                "volume", "amount", "turnover", "change_pct"
            ]
        )
        
        # 获取实时行情
        quotes = await fetcher.get_realtime_quotes([stocks[0]["code"]])
        assert len(quotes) > 0
        assert all(
            key in quotes[0] 
            for key in [
                "price", "change", "change_pct", "volume", "amount", "time",
                "open", "high", "low", "prev_close", "turnover", "volume_ratio",
                "amplitude", "market_value", "circulating_value", "pe_ratio",
                "pb_ratio", "speed", "change_60d", "change_ytd", "change_5m"
            ]
        )
        
        # 测试不支持的接口
        with pytest.raises(NotImplementedError):
            await fetcher.get_index_components("000001")
            
        with pytest.raises(NotImplementedError):
            await fetcher.get_financial_data("000001")

    @pytest.mark.asyncio
    async def test_data_consistency(self):
        """测试不同数据源的数据一致性"""
        token = os.getenv("TUSHARE_TOKEN")
        if not token:
            pytest.skip("需要设置TUSHARE_TOKEN环境变量才能运行数据一致性测试")
            
        tushare_fetcher = DataFetcherFactory.get_fetcher(
            "tushare",
            api_token=token
        )
        akshare_fetcher = DataFetcherFactory.get_fetcher("akshare")
        
        # 获取共同的股票
        code = "000001"  # 平安银行
        end_date = datetime.now()
        start_date = end_date - timedelta(days=1)
        
        # 获取两个数据源的日线数据
        tushare_data = await tushare_fetcher.get_daily_data(
            code,
            start_date=start_date,
            end_date=end_date
        )
        akshare_data = await akshare_fetcher.get_daily_data(
            code,
            start_date=start_date,
            end_date=end_date
        )
        
        # 检查数据一致性
        if tushare_data and akshare_data:
            tushare_latest = tushare_data[-1]
            akshare_latest = akshare_data[-1]
            
            # 价格差异不超过1%
            assert abs(
                tushare_latest["close"] - akshare_latest["close"]
            ) / tushare_latest["close"] < 0.01
            
            # 成交量差异不超过5%
            assert abs(
                tushare_latest["volume"] - akshare_latest["volume"]
            ) / tushare_latest["volume"] < 0.05
