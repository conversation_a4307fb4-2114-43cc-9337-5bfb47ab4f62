"""
API集成测试包

说明:
此包包含所有需要完整API环境的集成测试，包括数据库连接、
缓存服务、外部API等依赖项。

环境要求:
1. 数据库
   - TEST_DATABASE_URL: 测试数据库连接URL
   - 需要具有创建/删除数据库的权限

2. 外部服务
   - TEST_REDIS_URL: Redis服务连接
   - TEST_TUSHARE_TOKEN: Tushare API token
   - TEST_AKSHARE_CONFIG: Akshare配置

3. 安全配置
   - TEST_SECRET_KEY: 测试用密钥
   - TEST_API_KEY: API访问密钥

测试数据:
- fixtures/stocks.json: 股票基础数据
- fixtures/quotations.json: 行情测试数据
- fixtures/users.json: 测试用户数据

运行方式:
# 运行所有集成测试
$ pytest tests/integration

# 运行API集成测试
$ pytest tests/integration/api

# 运行特定接口测试
$ pytest tests/integration/api/test_stock_api.py

注意事项:
1. 运行集成测试前需要正确配置环境变量
2. 测试数据库会在测试过程中被重置
3. 部分测试可能需要网络连接
"""
