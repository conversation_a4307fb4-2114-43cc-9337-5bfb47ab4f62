import pytest
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, Mock
from datetime import date, timedelta
import pandas as pd

# 假设你的 FastAPI app 实例在 app.main 模块中
from app.main import app
from app.services.indicators.indicator_service import IndicatorService
from app.api.endpoints.indicators import get_indicator_service
from app.services.storage.stock_storage import StockStorageService

# --- Fixture for Test Client with Dependency Override ---
@pytest.fixture(scope="module")
def test_client():
    # Apply dependency override before creating the client
    app.dependency_overrides[get_indicator_service] = override_get_indicator_service
    client = TestClient(app)
    yield client # Provide the client to tests
    # Cleanup after tests in this module are done
    app.dependency_overrides.clear()

# --- 模拟数据 ---
MOCK_STOCK_CODE = "600519.SH" # 换一个股票代码
MOCK_START_DATE = (date.today() - timedelta(days=15)).isoformat()
MOCK_END_DATE = date.today().isoformat()

MOCK_MACD_DATA = [
    {"date": "2024-01-10", "dif": 0.1, "dea": 0.08, "hist": 0.04},
    {"date": "2024-01-11", "dif": 0.12, "dea": 0.09, "hist": 0.06},
]
MOCK_KDJ_DATA = [
    {"date": "2024-01-10", "k": 50, "d": 45, "j": 60},
    {"date": "2024-01-11", "k": 55, "d": 48, "j": 69},
]
MOCK_RSI_DATA = [
    {"date": "2024-01-10", "rsi_6": 60, "rsi_12": 55, "rsi_24": 52},
    {"date": "2024-01-11", "rsi_6": 62, "rsi_12": 57, "rsi_24": 54},
]
MOCK_ARBR_DATA = [
    {"date": "2024-01-10", "ar": 80, "br": 90},
    {"date": "2024-01-11", "ar": 85, "br": 95},
]
MOCK_VOLUME_DATA = [
    {"date": "2024-01-10", "volume": 50000, "ma5": 45000, "ma10": 48000},
    {"date": "2024-01-11", "volume": 55000, "ma5": 47000, "ma10": 49000},
]

# --- 模拟服务 ---
mock_indicator_service = AsyncMock(spec=IndicatorService)

# 定义模拟方法的返回值
async def mock_calculate_macd(*args, **kwargs):
    return MOCK_MACD_DATA

async def mock_calculate_kdj(*args, **kwargs):
    return MOCK_KDJ_DATA

async def mock_calculate_rsi(*args, **kwargs):
    return MOCK_RSI_DATA

async def mock_calculate_arbr(*args, **kwargs):
    return MOCK_ARBR_DATA

async def mock_get_volume_analysis(*args, **kwargs):
    return MOCK_VOLUME_DATA

# 使用 side_effect 绑定模拟方法 (确保属性仍是 Mock 对象)
mock_indicator_service.calculate_macd.side_effect = mock_calculate_macd
mock_indicator_service.calculate_kdj.side_effect = mock_calculate_kdj
mock_indicator_service.calculate_rsi.side_effect = mock_calculate_rsi
mock_indicator_service.calculate_arbr.side_effect = mock_calculate_arbr
mock_indicator_service.get_volume_analysis.side_effect = mock_get_volume_analysis

# --- 依赖覆盖 ---
def make_fake_kline_df(num=100, start_date="2024-01-01"):
    """生成指定数量的假K线数据DataFrame，index为日期"""
    from datetime import datetime, timedelta
    import pandas as pd
    base_date = datetime.strptime(start_date, "%Y-%m-%d")
    dates = [base_date + timedelta(days=i) for i in range(num)]
    df = pd.DataFrame({
        'open': [10 + i * 0.1 for i in range(num)],
        'high': [11 + i * 0.1 for i in range(num)],
        'low': [9 + i * 0.1 for i in range(num)],
        'close': [10.5 + i * 0.1 for i in range(num)],
        'volume': [1000 + i * 10 for i in range(num)],
        'amount': [11000 + i * 100 for i in range(num)],
    }, index=pd.to_datetime(dates))
    return df

async def override_get_indicator_service():
    """覆盖原始的依赖注入函数，返回真实IndicatorService，数据库和数据获取都mock"""
    mock_db = AsyncMock()
    mock_db.execute = AsyncMock()
    mock_db.commit = AsyncMock()
    mock_db.add = Mock()
    mock_db.add_all = Mock()
    mock_db.run_sync = AsyncMock()
    storage = StockStorageService(mock_db)
    service = IndicatorService(storage)
    # mock fetch_stock_data 返回假K线数据
    fake_df = make_fake_kline_df(num=100, start_date="2024-01-01")
    fake_df = fake_df.fillna(0)  # 保证没有NaN，防止json序列化报错
    service.data_fetcher.fetch_stock_data = AsyncMock(return_value=fake_df)
    service.data_fetcher.resample_kline = Mock(side_effect=lambda df, freq: df.fillna(0))
    return service

# --- 测试用例 ---

# MACD 测试
def test_get_macd_success(test_client): # Use the fixture
    """测试成功获取 MACD 指标"""
    mock_indicator_service.reset_mock() # Reset mock before test
    fast_period, slow_period, signal_period = 12, 26, 9
    freq = "D"
    response = test_client.get( # Use test_client from fixture
        f"/api/v1/indicators/macd/{MOCK_STOCK_CODE}",
        params={
            "start_date": MOCK_START_DATE,
            "end_date": MOCK_END_DATE,
            "fast_period": fast_period,
            "slow_period": slow_period,
            "signal_period": signal_period,
            "freq": freq
        }
    )
    assert response.status_code == 200
    result = response.json()
    assert "data" in result
    last2 = result["data"][-2:]
    for row in last2:
        assert "MACD_12_26_9" in row
        assert "MACDs_12_26_9" in row
        assert "MACDh_12_26_9" in row

# KDJ 测试
def test_get_kdj_success(test_client): # Use the fixture
    """测试成功获取 KDJ 指标"""
    mock_indicator_service.reset_mock() # Reset mock before test
    window, signal_period = 9, 3
    freq = "W"
    response = test_client.get( # Use test_client from fixture
        f"/api/v1/indicators/kdj/{MOCK_STOCK_CODE}",
        params={
            "start_date": MOCK_START_DATE,
            "end_date": MOCK_END_DATE,
            "window": window,
            "signal_period": signal_period,
            "freq": freq
        }
    )
    assert response.status_code == 200
    result = response.json()
    assert "data" in result
    last2 = result["data"][-2:]
    for row in last2:
        assert "K" in row
        assert "D" in row
        assert "J" in row

# RSI 测试
def test_get_rsi_success(test_client): # Use the fixture
    """测试成功获取 RSI 指标"""
    mock_indicator_service.reset_mock() # Reset mock before test
    periods = [6, 12, 24]
    freq = "M"
    response = test_client.get( # Use test_client from fixture
        f"/api/v1/indicators/rsi/{MOCK_STOCK_CODE}",
        params={
            "start_date": MOCK_START_DATE,
            "end_date": MOCK_END_DATE,
            "periods": periods, # 传递列表参数
            "freq": freq
        }
    )
    assert response.status_code == 200
    result = response.json()
    assert "data" in result
    last2 = result["data"][-2:]
    for row in last2:
        assert "RSI_6" in row
        assert "RSI_12" in row
        assert "RSI_24" in row

# ARBR 测试
def test_get_arbr_success(test_client): # Use the fixture
    """测试成功获取 ARBR 指标"""
    mock_indicator_service.reset_mock() # Reset mock before test
    window = 26
    freq = "D"
    response = test_client.get( # Use test_client from fixture
        f"/api/v1/indicators/arbr/{MOCK_STOCK_CODE}",
        params={
            "start_date": MOCK_START_DATE,
            "end_date": MOCK_END_DATE,
            "window": window,
            "freq": freq
        }
    )
    assert response.status_code == 200
    result = response.json()
    assert "data" in result
    last2 = result["data"][-2:]
    for row in last2:
        assert "AR" in row
        assert "BR" in row

# Volume 测试
def test_get_volume_analysis_success(test_client): # Use the fixture
    """测试成功获取成交量分析数据"""
    mock_indicator_service.reset_mock() # Reset mock before test
    ma_periods = [5, 10, 20]
    freq = "D"
    response = test_client.get(
        f"/api/v1/indicators/volume/{MOCK_STOCK_CODE}",
        params={
            "start_date": MOCK_START_DATE,
            "end_date": MOCK_END_DATE,
            "ma_periods": ma_periods,
            "freq": freq
        }
    )
    assert response.status_code == 200
    result = response.json()
    last2 = result["data"][-2:]
    for row in last2:
        assert "volume" in row
        assert "volume_ma5" in row
        assert "volume_ma10" in row
        assert "volume_ma20" in row

# 测试默认参数 (以 MACD 为例)
def test_get_macd_default_params(test_client):
    """测试 MACD 使用默认参数"""
    mock_indicator_service.reset_mock()
    response = test_client.get(f"/api/v1/indicators/macd/{MOCK_STOCK_CODE}")
    assert response.status_code == 200
    result = response.json()
    assert "data" in result
    last2 = result["data"][-2:]
    for row in last2:
        assert "MACD_12_26_9" in row
        assert "MACDs_12_26_9" in row
        assert "MACDh_12_26_9" in row

# 测试无效参数 (以 KDJ 为例)
def test_get_kdj_invalid_freq(test_client): # Use the fixture
    """测试 KDJ 使用无效频率"""
    mock_indicator_service.reset_mock() # Reset mock before test
    response = test_client.get( # Use test_client from fixture
        f"/api/v1/indicators/kdj/{MOCK_STOCK_CODE}",
        params={"freq": "Y"} # 无效频率
    )
    assert response.status_code == 422 # Unprocessable Entity
    mock_indicator_service.calculate_kdj.assert_not_called()

# --- 清理依赖覆盖 (Now handled by the test_client fixture) ---
# The cleanup_dependency_override fixture is no longer needed.
