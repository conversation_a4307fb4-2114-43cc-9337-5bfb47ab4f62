# 测试目录说明

## 目录结构

```
tests/
├── unit/                      # 单元测试
│   ├── api/                  # API单元测试
│   │   └── __init__.py
│   ├── core/                # 核心组件测试
│   │   └── __init__.py
│   ├── models/              # 数据模型测试
│   │   └── __init__.py
│   └── services/            # 服务层测试
│       └── data_fetcher/    # 数据获取服务测试
│           ├── conftest.py
│           ├── test_adapter.py
│           ├── test_base.py
│           └── test_factory.py
├── integration/              # 集成测试
│   ├── api/                 # API集成测试
│   │   └── __init__.py
│   └── services/           # 服务集成测试
│       └── data_fetcher/   # 数据获取服务集成测试
│           ├── conftest.py
│           └── test_providers.py
├── conftest.py             # 全局测试配置
├── pytest.ini              # pytest配置文件
└── README.md              # 本文件

## 测试分类

### 单元测试 (tests/unit/)
- 独立测试单个组件
- 不依赖外部服务
- 使用mock对象模拟依赖
- 运行快速，覆盖度高

### 集成测试 (tests/integration/)
- 测试多个组件的协同
- 可能依赖外部服务
- 使用真实的依赖项
- 验证端到端功能

## 运行测试

1. 安装测试依赖:
```bash
pip install -r requirements-test.txt
```

2. 运行所有测试:
```bash
pytest
```

3. 运行特定类型的测试:
```bash
# 单元测试
pytest tests/unit

# 集成测试
pytest tests/integration
```

4. 运行特定模块的测试:
```bash
# 数据获取服务测试
pytest tests/unit/services/data_fetcher

# API测试
pytest tests/unit/api
```

5. 生成覆盖率报告:
```bash
pytest --cov=app --cov-report=html
```

## 编写测试

1. 单元测试规范:
- 一个类对应一个测试类
- 每个方法对应一个或多个测试用例
- 使用合适的fixture管理测试数据
- 测试异常和边界情况

2. 集成测试规范:
- 关注组件间的交互
- 验证完整的业务流程
- 包含性能和负载测试
- 测试配置和环境依赖

3. 命名规范:
- 测试文件: test_*.py
- 测试类: Test*
- 测试方法: test_*
