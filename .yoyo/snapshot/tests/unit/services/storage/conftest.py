"""
StockStorageService测试的共享fixture
"""
import pytest
from unittest.mock import AsyncMock, Mock
from datetime import datetime

from app.services.storage.stock_storage import StockStorageService

@pytest.fixture
def sample_stock_info():
    """样例股票基本信息"""
    return {
        "code": "000001",
        "name": "平安银行",
        "industry": "银行",
        "exchange": "SZ",
        "full_code": "SZ000001"
    }

@pytest.fixture
def sample_daily_data():
    """样例日线数据"""
    return {
        "trade_date": datetime(2025, 3, 21),
        "open": 10.0,
        "high": 10.8,
        "low": 9.8,
        "close": 10.5,
        "volume": 100000,
        "amount": 1050000.0,
        "turnover_rate": 0.15
    }

@pytest.fixture
def stock_storage():
    """StockStorageService实例"""
    # 创建mock数据库会话
    mock_db = AsyncMock()
    mock_db.add = Mock()
    mock_db.add_all = Mock()
    mock_db.flush = AsyncMock()
    mock_db.execute = AsyncMock()
    mock_db.run_sync = AsyncMock()
    
    # 创建服务实例
    service = StockStorageService(mock_db)
    return service
