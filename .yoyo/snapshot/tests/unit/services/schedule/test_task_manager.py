"""
TaskManager测试用例
"""

import pytest
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

from app.services.schedule.task_manager import TaskManager


class TestTaskManager:
    """TaskManager测试类"""

    def test_list_jobs(self, mock_scheduler):
        """测试列出所有任务"""
        current_time = datetime.now()
        mock_jobs = [
            Mock(
                id='job1',
                name='Job 1',
                func=Mock(__module__='test_module', __name__='test_func1'),
                trigger=Mock(fields=[Mock(name='hour', expression='0')]),
                next_run_time=current_time + timedelta(hours=1)
            ),
            Mock(
                id='job2',
                name='Job 2',
                func=Mock(__module__='test_module', __name__='test_func2'),
                trigger=Mock(fields=[Mock(name='hour', expression='12')]),
                next_run_time=current_time + timedelta(hours=2)
            )
        ]
        # 设置Mock对象的name属性返回值
        mock_jobs[0].name = 'Job 1'
        mock_jobs[1].name = 'Job 2'
        mock_scheduler.get_jobs.return_value = mock_jobs

        jobs = TaskManager.list_jobs()

        assert len(jobs) == 2
        assert jobs[0]['id'] == 'job1'
        assert jobs[0]['name'] == 'Job 1'
        assert jobs[0]['func'] == 'test_module.test_func1'
        assert jobs[0]['schedule'] == '0'
        assert jobs[0]['next_run'] == (current_time + timedelta(hours=1)).isoformat()

    def test_get_job_info_existing(self, mock_scheduler):
        """测试获取存在的任务信息"""
        current_time = datetime.now()
        mock_job = Mock(
            id='test_job',
            name='Test Job',
            func=Mock(__module__='test_module', __name__='test_func'),
            trigger=Mock(fields=[Mock(name='hour', expression='0')]),
            next_run_time=current_time + timedelta(hours=1)
        )
        # 设置Mock对象的name属性返回值
        mock_job.name = 'Test Job'
        mock_scheduler.get_job.return_value = mock_job

        job_info = TaskManager.get_job_info('test_job')

        assert job_info is not None
        assert job_info['id'] == 'test_job'
        assert job_info['name'] == 'Test Job'
        assert job_info['func'] == 'test_module.test_func'
        assert job_info['schedule'] == '0'
        assert job_info['next_run'] == (current_time + timedelta(hours=1)).isoformat()

    def test_get_job_info_not_found(self, mock_scheduler):
        """测试获取不存在的任务信息"""
        mock_scheduler.get_job.return_value = None

        job_info = TaskManager.get_job_info('non_existent_job')

        assert job_info is None

    def test_pause_job_success(self, mock_scheduler):
        """测试成功暂停任务"""
        mock_job = Mock()
        mock_scheduler.get_job.return_value = mock_job

        result = TaskManager.pause_job('test_job')

        assert result is True
        mock_job.pause.assert_called_once()
        mock_scheduler.get_job.assert_called_once_with('test_job')

    def test_pause_job_not_found(self, mock_scheduler):
        """测试暂停不存在的任务"""
        mock_scheduler.get_job.return_value = None

        result = TaskManager.pause_job('non_existent_job')

        assert result is False

    def test_resume_job_success(self, mock_scheduler):
        """测试成功恢复任务"""
        mock_job = Mock()
        mock_scheduler.get_job.return_value = mock_job

        result = TaskManager.resume_job('test_job')

        assert result is True
        mock_job.resume.assert_called_once()
        mock_scheduler.get_job.assert_called_once_with('test_job')

    def test_resume_job_not_found(self, mock_scheduler):
        """测试恢复不存在的任务"""
        mock_scheduler.get_job.return_value = None

        result = TaskManager.resume_job('non_existent_job')

        assert result is False

    def test_modify_job_schedule_success(self, mock_scheduler):
        """测试成功修改任务调度时间"""
        mock_job = Mock()
        mock_scheduler.get_job.return_value = mock_job

        result = TaskManager.modify_job_schedule('test_job', '0 12 * * *')

        assert result is True
        mock_job.reschedule.assert_called_once()
        mock_scheduler.get_job.assert_called_once_with('test_job')

    def test_modify_job_schedule_not_found(self, mock_scheduler):
        """测试修改不存在任务的调度时间"""
        mock_scheduler.get_job.return_value = None

        result = TaskManager.modify_job_schedule('non_existent_job', '0 12 * * *')

        assert result is False
