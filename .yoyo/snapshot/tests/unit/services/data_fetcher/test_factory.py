"""测试数据提供者工厂"""
import unittest
import pytest
from unittest.mock import Mock, patch, AsyncMock

from app.services.data_fetcher import (
    DataFetcher,
    DataAdapter,
    BaseDataAdapter,
    DataFetcherFactory,
    TushareDataFetcher,
    TushareAdapter
)

@pytest.mark.asyncio
class TestDataFetcherFactory:
    """测试数据提供者工厂"""
    
    @pytest.fixture(autouse=True)
    async def setup(self):
        """每个测试用例前重置工厂状态"""
        await DataFetcherFactory.reset()
        yield
        await DataFetcherFactory.reset()

    async def test_singleton_pattern(self):
        """测试单例模式"""
        factory1 = DataFetcherFactory()
        factory2 = DataFetcherFactory()
        
        assert factory1 is factory2

    async def test_get_supported_providers(self):
        """测试获取支持的提供者列表"""
        providers = DataFetcherFactory.get_supported_providers()
        
        assert "tushare" in providers
        assert "akshare" in providers

    async def test_register_new_provider(self):
        """测试注册新的数据提供者"""
        # 创建模拟的数据提供者和适配器
        mock_fetcher = Mock(spec=DataFetcher)
        mock_adapter = Mock(spec=DataAdapter)
        
        # 注册新提供者
        DataFetcherFactory.register_provider(
            "mock_provider",
            mock_fetcher,
            mock_adapter
        )
        
        # 验证注册成功
        providers = DataFetcherFactory.get_supported_providers()
        assert "mock_provider" in providers

    async def test_remove_provider(self):
        """测试移除数据提供者"""
        # 首先注册一个提供者
        mock_fetcher = Mock(spec=DataFetcher)
        mock_adapter = Mock(spec=DataAdapter)
        DataFetcherFactory.register_provider(
            "test_provider",
            mock_fetcher,
            mock_adapter
        )
        
        # 移除提供者
        DataFetcherFactory.remove_provider("test_provider")
        
        # 验证移除成功
        providers = DataFetcherFactory.get_supported_providers()
        assert "test_provider" not in providers

    async def test_create_fetcher_invalid_provider(self):
        """测试创建无效的提供者"""
        with pytest.raises(ValueError):
            DataFetcherFactory.create_fetcher("invalid_provider")

    def test_create_tushare_fetcher(self):
        """测试创建Tushare提供者"""
        with patch.dict(DataFetcherFactory._PROVIDER_MAP, {
            "tushare": (AsyncMock(spec=TushareDataFetcher), TushareAdapter)
        }):
            # 创建提供者实例
            fetcher = DataFetcherFactory.get_fetcher(
                "tushare",
                api_token="test_token"
            )
            
            # 验证调用
            provider_cls = DataFetcherFactory._PROVIDER_MAP["tushare"][0]
            provider_cls.assert_called_once_with(api_token="test_token")
        assert fetcher is not None

    async def test_get_fetcher_caching(self):
        """测试获取提供者的缓存机制"""
        # 第一次获取
        fetcher1 = DataFetcherFactory.get_fetcher(
            "tushare",
            api_token="test_token"
        )
        
        # 第二次获取
        fetcher2 = DataFetcherFactory.get_fetcher(
            "tushare",
            api_token="test_token"
        )
        
        # 验证返回同一个实例
        assert fetcher1 is fetcher2

class TestDataFetcherFactoryIntegration:
    """数据提供者工厂集成测试"""

    @pytest.fixture(autouse=True)
    async def setup_teardown(self):
        """设置和清理"""
        await DataFetcherFactory.reset()
        yield
        await DataFetcherFactory.reset()

    @pytest.mark.asyncio
    async def test_create_and_use_fetcher(self):
        """测试创建并使用数据提供者"""
        # 创建一个测试用的提供者类
        class TestFetcher(DataFetcher):
            async def get_stock_list(self):
                return [{"code": "000001", "name": "Test Stock"}]
            
            async def get_daily_data(self, code, start_date=None, end_date=None):
                return [{"date": "2021-01-01", "close": 10.0}]
            
            async def get_realtime_quotes(self, codes):
                return [{"code": c, "price": 10.0} for c in codes]
            
            async def get_index_components(self, index_code):
                return ["000001", "000002"]
            
            async def get_financial_data(self, code, report_type="annual"):
                return [{"revenue": 1000000}]

        # 注册测试提供者
        DataFetcherFactory.register_provider(
            "test",
            TestFetcher,
            BaseDataAdapter
        )
        
        # 获取提供者实例
        fetcher = DataFetcherFactory.get_fetcher("test")
        
        # 测试各个方法
        stock_list = await fetcher.get_stock_list()
        assert len(stock_list) == 1
        assert stock_list[0]["code"] == "000001"
        
        daily_data = await fetcher.get_daily_data("000001")
        assert len(daily_data) == 1
        assert daily_data[0]["close"] == 10.0
        
        quotes = await fetcher.get_realtime_quotes(["000001"])
        assert len(quotes) == 1
        assert quotes[0]["price"] == 10.0

    def test_provider_registration_lifecycle(self):
        """测试提供者的注册生命周期"""
        # 创建测试提供者
        class TestProvider(DataFetcher):
            pass
        
        class TestAdapter(BaseDataAdapter):
            pass
        
        # 注册提供者
        DataFetcherFactory.register_provider(
            "test_lifecycle",
            TestProvider,
            TestAdapter
        )
        
        # 验证注册成功
        providers = DataFetcherFactory.get_supported_providers()
        assert "test_lifecycle" in providers
        
        # 移除提供者
        DataFetcherFactory.remove_provider("test_lifecycle")
        
        # 验证移除成功
        providers = DataFetcherFactory.get_supported_providers()
        assert "test_lifecycle" not in providers
