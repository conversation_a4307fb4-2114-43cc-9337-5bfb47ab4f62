"""Pytest配置文件"""
import pytest
import logging
from typing import Dict, Any, List
from datetime import datetime

from app.services.data_fetcher.base import DataFetcher
from app.services.data_fetcher.adapter import BaseDataAdapter
from app.services.data_fetcher.factory import DataFetcherFactory

# 配置日志
logging.basicConfig(level=logging.INFO)

@pytest.fixture(autouse=True)
def setup_teardown():
    """每个测试用例前后的设置和清理"""
    # 清理
    DataFetcherFactory.reset()

@pytest.fixture
def mock_stock_data() -> Dict[str, Any]:
    """模拟股票数据"""
    return {
        "code": "000001",
        "name": "平安银行",
        "industry": "银行",
        "market": "SZ",
        "list_date": "19910403"
    }

@pytest.fixture
def mock_daily_data() -> Dict[str, Any]:
    """模拟日线数据"""
    return {
        "date": datetime(2021, 1, 4),
        "open": 10.0,
        "high": 10.5,
        "low": 9.8,
        "close": 10.2,
        "volume": 100000,
        "amount": 1020000.0,
        "turnover": 2.5,
        "change_pct": 1.5
    }

@pytest.fixture
def mock_realtime_data() -> Dict[str, Any]:
    """模拟实时行情数据"""
    return {
        "code": "000001",
        "price": 10.2,
        "change": 0.2,
        "change_pct": 1.5,
        "volume": 50000,
        "amount": 510000.0,
        "time": datetime.now()
    }

class MockDataFetcher(DataFetcher):
    """用于测试的模拟数据提供者"""
    
    def __init__(self, mock_data: Dict[str, Any] = None):
        self.mock_data = mock_data or {}
    
    async def get_stock_list(self) -> List[Dict[str, Any]]:
        return [self.mock_data.get("stock", {})]
    
    async def get_daily_data(self, code: str, start_date=None, end_date=None):
        return [self.mock_data.get("daily", {})]
    
    async def get_realtime_quotes(self, codes: List[str]):
        return [self.mock_data.get("realtime", {}) for _ in codes]
    
    async def get_index_components(self, index_code: str):
        return self.mock_data.get("components", [])
    
    async def get_financial_data(self, code: str, report_type="annual"):
        return [self.mock_data.get("financial", {})]

@pytest.fixture
def mock_fetcher(mock_stock_data, mock_daily_data, mock_realtime_data):
    """创建模拟的数据提供者"""
    return MockDataFetcher({
        "stock": mock_stock_data,
        "daily": mock_daily_data,
        "realtime": mock_realtime_data,
        "components": ["000001", "000002", "000003"],
        "financial": {"revenue": 1000000, "profit": 100000}
    })

@pytest.fixture
def mock_adapter():
    """创建模拟的数据适配器"""
    return BaseDataAdapter()
