"""测试基础数据获取接口"""
import pytest
from datetime import datetime
from typing import Dict, List, Any

from app.services.data_fetcher.base import DataFetcher

class MockDataFetcher(DataFetcher):
    """用于测试的模拟数据提供者"""
    
    def __init__(self):
        self.stock_list_called = False
        self.daily_data_called = False
        self.realtime_quotes_called = False
        self.index_components_called = False
        self.financial_data_called = False
    
    async def get_stock_list(self) -> List[Dict[str, Any]]:
        self.stock_list_called = True
        return [
            {
                "code": "000001",
                "name": "平安银行",
                "industry": "银行",
                "market": "SZ",
                "list_date": "19910403"
            }
        ]
    
    async def get_daily_data(self, code: str, start_date=None, end_date=None):
        self.daily_data_called = True
        return [
            {
                "date": datetime(2021, 1, 4),
                "open": 10.0,
                "high": 10.5,
                "low": 9.8,
                "close": 10.2,
                "volume": 100000,
                "amount": 1020000.0,
                "turnover": 2.5,
                "change_pct": 1.5
            }
        ]
    
    async def get_realtime_quotes(self, codes: List[str]):
        self.realtime_quotes_called = True
        return [
            {
                "code": code,
                "price": 10.2,
                "change": 0.2,
                "change_pct": 1.5,
                "volume": 50000,
                "amount": 510000.0,
                "time": datetime.now()
            }
            for code in codes
        ]
    
    async def get_index_components(self, index_code: str):
        self.index_components_called = True
        return ["000001", "000002", "000003"]
    
    async def get_financial_data(self, code: str, report_type="annual"):
        self.financial_data_called = True
        return [
            {
                "report_date": "20201231",
                "revenue": 100000000.0,
                "net_profit": 10000000.0
            }
        ]

@pytest.mark.asyncio
class TestDataFetcher:
    """测试数据获取接口"""
    
    @pytest.fixture
    def fetcher(self):
        """创建测试用的数据获取器"""
        return MockDataFetcher()
    
    async def test_get_stock_list(self, fetcher):
        """测试获取股票列表"""
        stocks = await fetcher.get_stock_list()
        
        assert fetcher.stock_list_called
        assert len(stocks) == 1
        assert stocks[0]["code"] == "000001"
        assert stocks[0]["name"] == "平安银行"
        assert stocks[0]["industry"] == "银行"
    
    async def test_get_daily_data(self, fetcher):
        """测试获取日线数据"""
        daily_data = await fetcher.get_daily_data("000001")
        
        assert fetcher.daily_data_called
        assert len(daily_data) == 1
        assert daily_data[0]["open"] == 10.0
        assert daily_data[0]["close"] == 10.2
        assert daily_data[0]["volume"] == 100000
    
    async def test_get_realtime_quotes(self, fetcher):
        """测试获取实时行情"""
        codes = ["000001", "000002"]
        quotes = await fetcher.get_realtime_quotes(codes)
        
        assert fetcher.realtime_quotes_called
        assert len(quotes) == 2
        for quote in quotes:
            assert quote["code"] in codes
            assert quote["price"] == 10.2
            assert quote["change"] == 0.2
    
    async def test_get_index_components(self, fetcher):
        """测试获取指数成分股"""
        components = await fetcher.get_index_components("000300")
        
        assert fetcher.index_components_called
        assert len(components) == 3
        assert components[0] == "000001"
    
    async def test_get_financial_data(self, fetcher):
        """测试获取财务数据"""
        financial_data = await fetcher.get_financial_data("000001")
        
        assert fetcher.financial_data_called
        assert len(financial_data) == 1
        assert financial_data[0]["report_date"] == "20201231"
        assert financial_data[0]["revenue"] == 100000000.0
