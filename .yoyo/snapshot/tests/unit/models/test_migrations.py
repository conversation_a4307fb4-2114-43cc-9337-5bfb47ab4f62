"""
数据迁移测试模块

测试数据库迁移脚本的正确性，包括表结构更改和数据迁移。
"""

import pytest
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.exc import IntegrityError

from app.models.base import BaseModel
from app.models import (
    StockInfo, StockDailyBase, 
    StockIndicator, IndicatorVersion
)

# 测试用内存数据库
TEST_DB_URL = "sqlite:///:memory:"

@pytest.fixture(scope="function")
def db_engine():
    """创建测试数据库引擎"""
    engine = create_engine(TEST_DB_URL)
    yield engine
    BaseModel.metadata.drop_all(engine)

@pytest.fixture(scope="function")
def db_session(db_engine):
    """创建测试数据库会话"""
    BaseModel.metadata.create_all(db_engine)
    session = Session(db_engine)
    yield session
    session.close()

def test_stock_info_table_structure(db_engine):
    """测试股票信息表结构"""
    # 首先创建表结构
    BaseModel.metadata.create_all(db_engine)
    
    inspector = inspect(db_engine)
    columns = {col['name']: col for col in inspector.get_columns('stock_info')}
    
    # 验证必要的字段存在且类型正确
    assert 'code' in columns
    assert 'name' in columns
    assert 'listing_date' in columns
    assert 'total_shares' in columns
    assert 'is_active' in columns
    
    # 验证索引
    indices = inspector.get_indexes('stock_info')
    index_names = [idx['name'] for idx in indices]
    assert 'ix_stock_info_code' in index_names
    assert 'ix_stock_info_full_code' in index_names

def test_partition_table_creation(db_session):
    """测试分区表创建"""
    # 测试创建过去、当前和未来的分区
    dates = [
        datetime.now() - timedelta(days=32),  # 上月
        datetime.now(),                       # 本月
        datetime.now() + timedelta(days=32)   # 下月
    ]
    
    created_tables = set()
    for date in dates:
        partition_class = StockDailyBase.get_partition(date)
        table_name = partition_class.__tablename__
        created_tables.add(table_name)
        
        # 创建分区表，使用create_table方法自动检查表是否已存在
        partition_class.create_table(db_session.get_bind())
        
        # 验证分区表是按月创建的
        assert table_name.startswith('stock_daily_')
        assert table_name.endswith(date.strftime('%Y%m'))

def test_indicator_version_upgrade(db_session):
    """测试技术指标版本升级"""
    # 创建初始版本
    v1 = IndicatorVersion(
        version_hash="v1_hash",
        indicator_type="MACD",
        formula="EMA(CLOSE, 12) - EMA(CLOSE, 26)",
        parameters={"short": 12, "long": 26, "signal": 9},
        effective_date=datetime.now().date(),
        is_current=True
    )
    db_session.add(v1)
    db_session.commit()
    
    # 创建新版本
    v2 = IndicatorVersion(
        version_hash="v2_hash",
        indicator_type="MACD",
        formula="EMA(CLOSE, 10) - EMA(CLOSE, 20)",  # 新的计算方法
        parameters={"short": 10, "long": 20, "signal": 7},
        effective_date=(datetime.now() + timedelta(days=1)).date(),
        is_current=True
    )
    db_session.add(v2)
    
    # 更新旧版本状态
    v1.is_current = False
    db_session.commit()
    
    # 验证版本更新
    current_version = db_session.query(IndicatorVersion)\
        .filter_by(indicator_type="MACD", is_current=True)\
        .first()
    assert current_version.version_hash == "v2_hash"
    
    old_version = db_session.query(IndicatorVersion)\
        .filter_by(version_hash="v1_hash")\
        .first()
    assert old_version.is_current is False

def test_data_integrity_constraints(db_session):
    """测试数据完整性约束"""
    # 创建基础股票信息
    stock = StockInfo(
        code="000001",
        name="平安银行",
        exchange="SZ",
        full_code="SZ000001",
        is_active=True
    )
    db_session.add(stock)
    db_session.commit()
    
    # 测试日期约束
    partition_class = StockDailyBase.get_partition(datetime.now())
    daily_data = partition_class(
        stock_code="000001",
        trade_date=None,  # 不允许的空日期
        open=10.0,
        high=11.0,
        low=9.0,
        close=10.5,
        volume=1000000
    )
    
    # 验证非空约束生效
    with pytest.raises(IntegrityError):
        db_session.add(daily_data)
        db_session.commit()

def test_partition_data_isolation(db_session):
    """测试分区数据隔离"""
    # 创建测试股票
    stock = StockInfo(
        code="000001",
        name="平安银行",
        exchange="SZ",
        full_code="SZ000001",
        is_active=True
    )
    db_session.add(stock)
    db_session.commit()
    
    # 创建两个不同月份的数据
    current_date = datetime.now()
    next_month = current_date + timedelta(days=32)
    
    # 获取两个月份的分区表
    current_partition = StockDailyBase.get_partition(current_date)
    next_partition = StockDailyBase.get_partition(next_month)
    
    # 确保表存在，使用create_table方法自动检查表是否已存在
    current_partition.create_table(db_session.get_bind())
    next_partition.create_table(db_session.get_bind())
    
    # 添加测试数据
    current_data = current_partition(
        stock_code="000001",
        trade_date=current_date.date(),
        open=10.0,
        high=11.0,
        low=9.0,
        close=10.5,
        volume=1000000
    )
    
    next_data = next_partition(
        stock_code="000001",
        trade_date=next_month.date(),
        open=11.0,
        high=12.0,
        low=10.0,
        close=11.5,
        volume=1200000
    )
    
    db_session.add(current_data)
    db_session.add(next_data)
    db_session.commit()
    
    # 验证数据隔离
    current_count = db_session.query(current_partition)\
        .filter_by(stock_code="000001")\
        .count()
    next_count = db_session.query(next_partition)\
        .filter_by(stock_code="000001")\
        .count()
    
    assert current_count == 1
    assert next_count == 1
