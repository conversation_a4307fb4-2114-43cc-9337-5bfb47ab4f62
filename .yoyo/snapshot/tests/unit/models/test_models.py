"""
数据模型测试模块

测试数据模型的字段、关系和功能的正确性。
"""

import pytest
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import create_engine
from sqlalchemy.exc import IntegrityError

from app.models.base import BaseModel
from app.models import (
    StockInfo, StockDailyBase, 
    StockIndicator, IndicatorVersion,
    PartitionManager
)

# 测试用内存数据库
TEST_DB_URL = "sqlite:///:memory:"

@pytest.fixture(scope="function")
def db_session():
    """创建测试用数据库会话"""
    engine = create_engine(TEST_DB_URL)
    BaseModel.metadata.create_all(engine)
    
    session = Session(engine)
    yield session
    session.close()
    
    # 清理数据库
    BaseModel.metadata.drop_all(engine)

def test_stock_info_model(db_session):
    """测试股票基本信息模型"""
    # 创建测试数据
    stock = StockInfo(
        code="601398",
        name="工商银行",
        exchange="SH",
        full_code="SH601398",
        industry="银行",
        sector="金融",
        listing_date=datetime(1999, 11, 15).date(),
        total_shares=86795324089,
        circulating_shares=86795324089,
        company_profile="中国工商银行股份有限公司",
        market_cap=1234567890000,
        is_active=True
    )
    
    # 保存并验证
    db_session.add(stock)
    db_session.commit()
    
    # 查询验证
    saved_stock = db_session.query(StockInfo).filter_by(code="601398").first()
    assert saved_stock is not None
    assert saved_stock.name == "工商银行"
    assert saved_stock.total_shares == 86795324089
    assert saved_stock.is_active is True

def test_stock_daily_partition(db_session):
    """测试股票日线数据分表"""
    # 创建基础股票信息
    stock = StockInfo(
        code="601398",
        name="工商银行",
        exchange="SH",
        full_code="SH601398",
        is_active=True
    )
    db_session.add(stock)
    db_session.commit()
    
    # 获取当月分区表
    current_date = datetime.now()
    partition_class = StockDailyBase.get_partition(current_date)
    
    # 创建分区表
    partition_class.create_table(db_session.get_bind())
    
    # 创建测试数据
    daily_data = partition_class(
        stock_code="601398",
        trade_date=current_date.date(),
        open=5.5,
        high=5.6,
        low=5.4,
        close=5.5,
        volume=1000000,
        amount=5500000,
        change_pct=1.2,
        turnover_rate=0.5,
        limit_status="N",
        is_st=False
    )
    
    # 保存并验证
    db_session.add(daily_data)
    db_session.commit()
    
    # 查询验证
    saved_data = db_session.query(partition_class).filter_by(stock_code="601398").first()
    assert saved_data is not None
    assert saved_data.close == 5.5
    assert saved_data.volume == 1000000
    assert saved_data.limit_status == "N"

def test_indicator_version_control(db_session):
    """测试技术指标版本控制"""
    # 创建基础股票信息
    stock = StockInfo(
        code="601398",
        name="工商银行",
        exchange="SH",
        full_code="SH601398",
        is_active=True
    )
    db_session.add(stock)
    
    # 创建指标版本
    version = IndicatorVersion(
        version_hash="abc123",
        indicator_type="MACD",
        formula="EMA(CLOSE, 12) - EMA(CLOSE, 26)",
        parameters={"short": 12, "long": 26, "signal": 9},
        effective_date=datetime.now().date(),
        is_current=True,
        description="MACD指标计算方法V1"
    )
    db_session.add(version)
    
    # 创建指标数据
    indicator = StockIndicator(
        stock_code="601398",
        trade_date=datetime.now().date(),
        indicator_type="MACD",
        version_hash="abc123",
        values={"D1": {"diff": 0.5, "dea": 0.3, "macd": 0.4}},
        data_frequency="D1"
    )
    db_session.add(indicator)
    
    # 保存并验证
    db_session.commit()
    
    # 查询验证
    saved_indicator = db_session.query(StockIndicator).first()
    assert saved_indicator is not None
    assert saved_indicator.version_hash == "abc123"
    assert saved_indicator.values["D1"]["diff"] == 0.5
    
    # 验证版本关联
    assert saved_indicator.version.formula == "EMA(CLOSE, 12) - EMA(CLOSE, 26)"
    assert saved_indicator.version.is_current is True

def test_unique_constraints(db_session):
    """测试唯一性约束"""
    # 创建重复的股票代码
    stock1 = StockInfo(
        code="601398",
        name="工商银行",
        exchange="SH",
        full_code="SH601398",
        is_active=True
    )
    stock2 = StockInfo(
        code="601398",  # 重复的代码
        name="工商银行2",
        exchange="SH",
        full_code="SH601398_2",
        is_active=True
    )
    
    db_session.add(stock1)
    db_session.commit()
    
    # 验证重复代码会引发异常
    with pytest.raises(IntegrityError):
        db_session.add(stock2)
        db_session.commit()

def test_partition_manager(db_session):
    """测试分区管理器"""
    # 注册当前分区
    current_date = datetime.now()
    PartitionManager.register_partition("stock_daily", StockDailyBase)
    
    # 获取分区表
    partition_class = PartitionManager.get_partition("stock_daily", current_date)
    assert partition_class is not None
    assert issubclass(partition_class, StockDailyBase)
    
    # 验证表名格式
    expected_table_name = f"stock_daily_{current_date.strftime('%Y%m')}"
    assert partition_class.__tablename__ == expected_table_name
