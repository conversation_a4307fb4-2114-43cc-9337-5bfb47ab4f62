import pytest
import sqlalchemy
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.exc import ResourceClosedError, StatementError
from app.core.database import Base, get_db, db_session, engine, AsyncSessionLocal
from app.core.config import settings

@pytest.fixture(scope="function")
async def test_db():
    """创建测试数据库连接"""
    # 使用内存数据库进行测试
    test_engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        pool_pre_ping=True,
        echo=False
    )
    
    # 创建所有表
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # 创建测试会话
    async_session = AsyncSession(bind=test_engine)
    
    try:
        yield async_session
    finally:
        await async_session.close()
        async with test_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
        await test_engine.dispose()

@pytest.mark.asyncio
async def test_engine_configuration():
    """测试数据库引擎配置"""
    # 测试引擎基本配置
    assert engine.dialect.is_async
    assert engine.echo == settings.SQL_ECHO
    assert hasattr(engine, 'pool')

@pytest.mark.asyncio
async def test_session_factory():
    """测试会话工厂配置"""
    session = AsyncSessionLocal()
    try:
        assert isinstance(session, AsyncSession)
        assert not session.sync_session.info.get('autocommit', False)
        assert not session.sync_session.info.get('autoflush', False)
    finally:
        await session.close()

@pytest.mark.asyncio
async def test_get_db():
    """测试get_db生成器"""
    session = await get_db()
    try:
        assert isinstance(session, AsyncSession)
        # 测试会话是否可用
        result = await session.scalar(text("SELECT 1"))
        assert result == 1
    finally:
        await session.close()

@pytest.mark.asyncio
async def test_db_session_context_manager(test_db):
    """测试db_session上下文管理器"""
    async with db_session() as session:
        assert isinstance(session, AsyncSession)
        # 测试会话是否可用
        result = await session.scalar(text("SELECT 1"))
        assert result == 1

@pytest.mark.asyncio
async def test_db_session_exception_handling(test_db):
    """测试db_session异常处理"""
    session = None
    try:
        async with db_session() as session:
            assert isinstance(session, AsyncSession)
            raise ValueError("测试异常")
    except ValueError:
        if session:
            # 验证会话已被回滚
            assert not session.in_transaction()

@pytest.mark.asyncio
async def test_multiple_sessions():
    """测试多个会话"""
    session1 = AsyncSessionLocal()
    session2 = AsyncSessionLocal()
    
    try:
        assert session1 is not session2
        # 测试两个会话是否都可用
        result1 = await session1.scalar(text("SELECT 1"))
        result2 = await session2.scalar(text("SELECT 1"))
        assert result1 == result2 == 1
    finally:
        await session1.close()
        await session2.close()

@pytest.mark.asyncio
async def test_session_rollback(test_db):
    """测试会话回滚"""
    async with db_session() as session:
        # 执行一个查询
        initial_result = await session.scalar(text("SELECT 1"))
        assert initial_result == 1
        
        # 模拟失败的操作
        try:
            await session.execute(text("INVALID SQL"))
            await session.commit()
        except Exception:
            await session.rollback()
        
        # 验证会话仍然可用
        final_result = await session.scalar(text("SELECT 1"))
        assert final_result == 1

@pytest.fixture
async def test_engine():
    """创建测试数据库引擎"""
    engine = create_async_engine("sqlite+aiosqlite:///:memory:")
    
    # 创建所有表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    return engine

@pytest.mark.asyncio
async def test_session_commit():
    """测试会话提交"""
    # 创建测试引擎
    engine = create_async_engine("sqlite+aiosqlite:///:memory:")
    
    # 创建所有表
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    # 创建测试会话
    session_factory = async_sessionmaker(
        bind=engine,
        class_=AsyncSession,
        expire_on_commit=False
    )
    
    async with session_factory() as session:
        # 创建测试表
        await session.execute(text("""
            CREATE TABLE test_table (
                id INTEGER PRIMARY KEY,
                value TEXT
            )
        """))
        await session.commit()
    
    async with session_factory() as session:
        # 插入数据
        await session.execute(
            text("INSERT INTO test_table (id, value) VALUES (:id, :value)"),
            {"id": 1, "value": "test"}
        )
        await session.commit()
        
        # 验证数据已保存
        result = await session.scalar(
            text("SELECT value FROM test_table WHERE id = :id"),
            {"id": 1}
        )
        assert result == "test"

@pytest.mark.asyncio
async def test_connection_pool():
    """测试连接池行为"""
    sessions = []
    try:
        # 创建多个会话
        for _ in range(5):
            session = AsyncSessionLocal()
            sessions.append(session)
            # 验证会话可用
            result = await session.scalar(text("SELECT 1"))
            assert result == 1
    finally:
        # 关闭所有会话
        for session in sessions:
            await session.close()

@pytest.mark.asyncio
async def test_base_declarative():
    """测试Base声明类"""
    # 验证Base类具有必要的属性和方法
    assert hasattr(Base, 'metadata')
    assert hasattr(Base, '__init__')
    # registry是SQLAlchemy 2.0的新特性，但不是必需的
    assert hasattr(Base, 'metadata')
