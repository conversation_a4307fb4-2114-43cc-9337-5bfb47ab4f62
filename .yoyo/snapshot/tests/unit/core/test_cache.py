"""
缓存系统测试

测试Redis缓存和内存缓存的功能
"""
import pytest
import asyncio
from typing import Dict, Any
from unittest.mock import patch, AsyncMock
from app.core.cache import MemoryCache, RedisCache, get_cache_client
from app.core.cache_decorator import cached, indicator_cache
from app.core.config import settings


@pytest.mark.unit
class TestMemoryCache:
    """测试内存缓存"""
    
    async def test_basic_operations(self):
        """测试基本的缓存操作"""
        cache = MemoryCache()
        
        # 测试设置和获取
        await cache.set("test_key", "test_value", expire=60)
        value = await cache.get("test_key")
        assert value == "test_value"
        
        # 测试删除
        await cache.delete("test_key")
        value = await cache.get("test_key")
        assert value is None
        
        # 测试过期
        await cache.set("expire_key", "expire_value", expire=1)
        await asyncio.sleep(1.1)
        value = await cache.get("expire_key")
        assert value is None
    
    async def test_clear_operations(self):
        """测试清理操作"""
        cache = MemoryCache()
        
        # 设置多个缓存项
        await cache.set("prefix:key1", "value1")
        await cache.set("prefix:key2", "value2")
        await cache.set("other:key3", "value3")
        
        # 测试按前缀清理
        deleted = await cache.clear_by_prefix("prefix:")
        assert deleted == 2
        assert await cache.get("prefix:key1") is None
        assert await cache.get("prefix:key2") is None
        assert await cache.get("other:key3") == "value3"
        
        # 测试清空所有
        await cache.clear()
        assert await cache.get("other:key3") is None


@pytest.mark.unit
class TestCacheDecorator:
    """测试缓存装饰器"""
    
    async def test_cached_decorator(self):
        """测试基本的缓存装饰器"""
        call_count = 0
        
        @cached(expire=60, namespace="test")
        async def expensive_function(param: str) -> Dict[str, Any]:
            nonlocal call_count
            call_count += 1
            return {"param": param, "call_count": call_count}
        
        # 第一次调用，应该执行函数
        result1 = await expensive_function("test_param")
        assert result1["call_count"] == 1
        
        # 第二次调用相同参数，应该从缓存获取
        result2 = await expensive_function("test_param")
        assert result2["call_count"] == 1  # 调用次数没有增加
        
        # 使用不同参数调用，应该执行函数
        result3 = await expensive_function("different_param")
        assert result3["call_count"] == 2
        
        # 清除缓存
        await expensive_function.clear_cache()
        
        # 再次调用，应该执行函数
        result4 = await expensive_function("test_param")
        assert result4["call_count"] == 3
    
    async def test_indicator_cache_decorator(self):
        """测试预定义的指标缓存装饰器"""
        
        class TestService:
            @indicator_cache()
            async def calculate_indicator(self, stock_code: str) -> float:
                import random
                return random.random()
        
        service = TestService()
        
        # 多次调用应该返回相同的值（从缓存获取）
        result1 = await service.calculate_indicator("600000")
        result2 = await service.calculate_indicator("600000")
        assert result1 == result2


@pytest.mark.integration
@pytest.mark.asyncio
class TestRedisCache:
    """测试Redis缓存（需要Redis服务器运行）"""
    
    @pytest.fixture
    async def redis_cache(self):
        """创建Redis缓存实例"""
        # 临时设置REDIS_URL以测试
        original_redis_url = settings.REDIS_URL
        settings.REDIS_URL = "redis://localhost:6379/15"  # 使用测试数据库
        
        cache = RedisCache()
        yield cache
        
        # 清理并恢复设置
        try:
            await cache.clear()
            await cache.close()
        except:
            pass
        settings.REDIS_URL = original_redis_url
    
    async def test_redis_operations(self, redis_cache):
        """测试Redis基本操作"""
        try:
            # 测试连接
            await redis_cache._get_client()
        except Exception as e:
            pytest.skip(f"Redis服务器未运行: {str(e)}")
        
        # 测试各种数据类型
        test_cases = [
            ("string", "simple string value"),
            ("integer", 42),
            ("float", 3.14),
            ("boolean", True),
            ("list", [1, 2, 3, "four"]),
            ("dict", {"key": "value", "nested": {"data": 123}}),
            ("complex", {"data": [1, 2, 3], "metadata": {"created": "2024-01-01"}})
        ]
        
        for key, value in test_cases:
            await redis_cache.set(f"test:{key}", value, expire=60)
            retrieved = await redis_cache.get(f"test:{key}")
            assert retrieved == value, f"Failed for {key}: {retrieved} != {value}"
    
    async def test_redis_scan_delete(self, redis_cache):
        """测试Redis的扫描删除功能"""
        try:
            await redis_cache._get_client()
        except:
            pytest.skip("Redis服务器未运行")
        
        # 设置多个键
        for i in range(10):
            await redis_cache.set(f"scan:test:{i}", i, expire=60)
            await redis_cache.set(f"other:{i}", i, expire=60)
        
        # 删除特定前缀
        deleted = await redis_cache.clear_by_prefix("scan:test:")
        assert deleted == 10
        
        # 验证删除结果
        for i in range(10):
            assert await redis_cache.get(f"scan:test:{i}") is None
            assert await redis_cache.get(f"other:{i}") == i


@pytest.mark.unit
class TestCacheFactory:
    """测试缓存工厂"""
    
    def test_cache_client_selection(self):
        """测试缓存客户端选择逻辑"""
        # 没有Redis URL时应该返回MemoryCache
        with patch.object(settings, 'REDIS_URL', None):
            client = get_cache_client()
            assert client == MemoryCache
        
        # 有Redis URL时应该返回RedisCache
        with patch.object(settings, 'REDIS_URL', 'redis://localhost:6379/0'):
            client = get_cache_client()
            assert client == RedisCache