"""
全局测试配置

提供测试环境设置和共享的fixture
"""
import aiohttp # Explicitly import aiohttp early to potentially fix initialization issues
import pytest
import logging
from datetime import datetime
from typing import Dict, Any

# 配置日志级别
logging.basicConfig(level=logging.INFO)

@pytest.fixture(scope="session", autouse=True)
def setup_test_env():
    """设置测试环境"""
    # 在这里添加全局的测试环境设置
    yield
    # 在这里添加测试环境清理代码

@pytest.fixture
def mock_stock_data() -> Dict[str, Any]:
    """模拟股票基本数据"""
    return {
        "code": "000001",
        "name": "测试股票",
        "industry": "测试行业",
        "market": "SZ",
        "list_date": "20240101"
    }

@pytest.fixture
def mock_daily_data() -> Dict[str, Any]:
    """模拟日线数据"""
    return {
        "date": datetime(2024, 1, 1),
        "open": 10.0,
        "high": 11.0,
        "low": 9.0,
        "close": 10.5,
        "volume": 1000000,
        "amount": 10500000.0,
        "turnover": 2.5,
        "change_pct": 1.5
    }

@pytest.fixture
def mock_real_time_data() -> Dict[str, Any]:
    """模拟实时行情数据"""
    return {
        "code": "000001",
        "price": 10.5,
        "change": 0.5,
        "change_pct": 1.5,
        "volume": 500000,
        "amount": 5250000.0,
        "time": datetime.now()
    }
