# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

A high-performance stock quantitative analysis system with multi-data source support, technical indicators, and RESTful API access. Built with FastAPI backend and Vue.js frontend.

## Key Commands

### Backend Development
```bash
# Install dependencies (recommended: uv)
uv sync                                 # Install with uv (faster)
pip install -r requirements.txt --pre  # Alternative with pip

# Database operations
alembic upgrade head                    # Apply migrations
alembic revision --autogenerate -m "msg" # Generate new migration
alembic current                         # Check current version
alembic history                         # View migration history
alembic downgrade -1                    # Rollback one version

# Server deployment (fix version mismatch issues)
./scripts/deploy_db.sh                  # Server database deployment script
alembic stamp head                      # Fix "table already exists" errors
alembic stamp base && alembic stamp head # Reset version tracking

# Run backend
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000  # With uv
python run_dev.py                       # Run backend + frontend together

# Development tools
black .                                 # Format code
isort .                                 # Sort imports  
flake8                                  # Lint code
mypy app                               # Type checking

# Testing
pytest                                  # Run all tests
pytest tests/unit -v                    # Run unit tests only
pytest tests/integration -v             # Run integration tests
pytest --cov=app --cov-report=html      # Generate coverage report
pytest -m "not slow"                   # Exclude slow tests
pytest tests/unit/services/data_fetcher/test_adapter.py -v  # Single test file

# Environment setup
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate    # Windows
```

### Frontend Development
```bash
cd frontend-app
npm install
npm run dev                             # Development server
npm run build                           # Production build
```

### Docker
```bash
docker build -t quantization:latest .
docker run -d -p 8000:8000 --env-file .env --name quantization quantization:latest
```

## Architecture Overview

### Backend Structure (FastAPI)
- **Layered Architecture**: API → Services → Storage → Database
- **Primary Data Flow**: Frontend → API Endpoints → Service Layer → Storage/Data Fetcher → Database/External APIs

### Key Components

#### 1. Data Provider System (`app/services/data_fetcher/`)
- **Factory Pattern**: `DataFetcherFactory.create_fetcher()` for provider selection
- **Providers**: Tushare, Akshare, Mairui APIs
- **Configuration**: `DATA_API_TYPE` in environment variables

#### 2. Technical Indicators (`app/services/indicators/`)
- **Service**: `IndicatorService` and `AdvancedIndicatorService`
- **Indicators**: MACD, KDJ, RSI, ARBR, Volume analysis
- **Versioning**: `IndicatorVersion` model for calculation reproducibility

#### 3. Database Models (`app/models/`)
- **StockInfo**: Basic stock metadata
- **StockDailyBase**: Time-partitioned daily data (monthly tables)
- **StockIndicator**: Technical indicator values
- **User/Watchlist**: User management for watchlists

#### 4. API Structure (`app/api/endpoints/`)
- `/api/v1/stocks/` - Stock data endpoints
- `/api/v1/indicators/` - Technical indicators
- `/api/v1/advanced-indicators/` - Complex calculations
- `/api/v1/analytics/` - K-line and analytics data
- `/api/v1/watchlist/` - User watchlist management

#### 5. Storage Layer (`app/services/storage/`)
- **Repository Pattern**: `StockStorage` abstract interface
- **Batch Operations**: Efficient bulk data handling
- **Caching**: In-memory LRU cache for stock info

### Frontend Structure (Vue.js 3)
- **Framework**: Vue 3 with Composition API + Vite
- **State Management**: Pinia stores (`stockData`, `theme`, `ui`)
- **Styling**: UnoCSS + Element Plus components (migrated from WindiCSS)
- **Icons**: Iconify with Carbon and MDI icon sets
- **Routing**: Vue Router with lazy-loaded pages
- **API Client**: Axios-based in `src/services/api/`
- **Charts**: ECharts for data visualization

## Project Memories
- 前端的Icon为carbon系列
- 我这个项目python的包管理工具是uv
- 前端默认样式是dark-mode, 同时注意还有light
- Icon组件默认使用的图标库为carbon

## Database Development Guidelines

### AsyncSession Usage (CRITICAL)
**ALWAYS use proper AsyncSession patterns in this project:**

1. **Import the correct session context manager:**
   ```python
   from app.core.database import db_session  # Use this for async context management
   ```

2. **Database session pattern:**
   ```python
   # CORRECT - Use async context manager
   async with db_session() as db:
       stmt = select(Model).where(...)
       result = await db.execute(stmt)
       data = result.scalars().all()
   
   # WRONG - Don't use sync patterns
   # db.query(Model).filter(...)  # This will fail with AsyncSession
   ```

3. **Common AsyncSession patterns:**
   - Use `select()` instead of `query()`
   - Use `await db.execute(stmt)` instead of direct query methods
   - Use `result.scalar()` or `result.scalars().all()` to get data
   - Use `await db.commit()` for transactions

4. **Task/Background job database access:**
   ```python
   # Always import db_session in task files
   from app.core.database import db_session
   
   async def background_task():
       async with db_session() as db:
           # Database operations here
           pass
   ```

5. **Connection Pool Management:**
   - **CRITICAL**: Always use `async with db_session()` context managers
   - Never create AsyncSession instances manually without proper cleanup
   - All authentication functions (`jwt_auth.py`, `dependencies.py`) must be async
   - Connection pool is configured with: pool_size=5, max_overflow=10, pool_timeout=30

**Note: This is a recurring issue - always check imports and use async patterns when working with database code.**

## Key Configuration Files

- **Backend Config**: `app/core/config.py` - Centralized settings
- **Project Dependencies**: `pyproject.toml` - Modern Python packaging
- **Database**: `DATABASE_URL` in `.env` (SQLite default)
- **Data Providers**: `TUSHARE_TOKEN`, `MAIRUI_TOKEN` in `.env`
- **Frontend**: `frontend-app/vite.config.js`
- **Development Runner**: `run_dev.py` - Concurrent backend/frontend startup

## Development Workflow

### Adding New Indicators
1. Add calculation logic in `app/services/indicators/`
2. Create/update schemas in `app/schemas/indicators.py`
3. Add API endpoint in `app/api/endpoints/indicators.py`
4. Add frontend components in `frontend-app/src/components/`
5. Write tests in `tests/unit/services/indicators/`

### Adding New Data Provider
1. Create provider class in `app/services/data_fetcher/provider/`
2. Implement `DataFetcher` interface
3. Register in `DataFetcherFactory`
4. Add configuration in `app/core/config.py`

### Database Changes
1. Update models in `app/models/`
2. Generate migration: `alembic revision --autogenerate -m "description"`
3. Apply migration: `alembic upgrade head`

## Testing Strategy

### Test Markers
- `@pytest.mark.unit` - Unit tests
- `@pytest.mark.integration` - Integration tests
- `@pytest.mark.slow` - Long-running tests

### Running Specific Tests
```bash
pytest -m unit                    # Unit tests only
pytest -m integration            # Integration tests only
pytest -m "not slow"              # Exclude slow tests
pytest tests/unit/services/data_fetcher/ -v  # Specific module
pytest tests/unit/services/data_fetcher/test_adapter.py::TestDataFetcher::test_get_stock_list -v  # Specific test method
```

## Code Quality and Formatting

The project uses several tools for code quality (configured in `pyproject.toml`):

```bash
# Format and lint (run before committing)
black .                           # Code formatting
isort .                           # Import sorting
flake8                            # Linting
mypy app                          # Type checking

# Install pre-commit hooks (recommended)
pre-commit install
```

## Environment Variables

Required in `.env`:
```bash
# Database
DATABASE_URL=sqlite:///./quantization.db

# Data Providers
TUSHARE_TOKEN=your_token_here
MAIRUI_TOKEN=your_token_here

# API
API_HOST=0.0.0.0
API_PORT=8000

# Optional
REDIS_URL=redis://localhost:6379/0
LOG_LEVEL=INFO
```

## Common Development Tasks

### Quick Start
1. `python -m venv venv && source venv/bin/activate`
2. `pip install -r requirements.txt --pre`
3. `alembic upgrade head`
4. `python run_dev.py` (backend + frontend)
5. Access: http://localhost:8000/docs

### API Documentation
- Swagger UI: http://localhost:8000/api/docs
- ReDoc: http://localhost:8000/api/redoc
- openapi: http://localhost:8000/openapi.json

### Common Issues
- **Database locked**: Ensure no other processes using SQLite
- **Import errors**: Check virtual environment activation
- **CORS issues**: Verify `BACKEND_CORS_ORIGINS` configuration
- **Provider tokens**: Ensure valid API tokens in .env
```