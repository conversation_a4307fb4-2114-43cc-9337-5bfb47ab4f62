# 量化策略系统使用指南

## 快速开始

### 1. 打开主界面
在浏览器中打开 `index.html` 文件，你将看到股票量化分析系统的主界面。

### 2. 访问策略功能
点击左侧导航栏中的 **"量化策略"** 按钮，进入策略页面。

### 3. 基本操作流程

#### 步骤1：配置策略参数
- 在策略配置区域，你可以调整起飞模式和强势模式的参数
- 默认配置已经过优化，初学者可以直接使用
- 高级用户可以根据市场情况调整参数

#### 步骤2：选择监控股票
- 在实时监控区域，从下拉菜单中选择要监控的股票
- 点击"设置监控"按钮确认选择

#### 步骤3：开始监控
- 点击"开始监控"按钮启动实时监控
- 系统将每5秒检查一次信号
- 当检测到信号时，会在界面上显示并记录到历史中

## 策略回测

### 1. 进入回测页面
点击左侧导航栏中的 **"策略回测"** 按钮。

### 2. 设置回测参数
- **回测股票**：选择要回测的股票
- **回测周期**：选择回测的时间长度（推荐120天）
- **初始资金**：设置回测的初始资金
- **手续费率**：设置交易手续费率

### 3. 运行回测
- 点击"开始回测"按钮
- 等待回测完成（通常需要几秒钟）
- 查看回测结果和统计数据

### 4. 分析结果
回测结果包括：
- **总收益率**：整个回测期间的收益率
- **年化收益率**：按年计算的收益率
- **最大回撤**：最大的亏损幅度
- **夏普比率**：风险调整后的收益指标
- **胜率**：盈利交易占总交易的比例
- **交易明细**：每笔交易的详细记录

## 策略测试（开发者功能）

### 1. 打开测试页面
在浏览器中打开 `strategy-test.html` 文件。

### 2. 测试功能
- **生成测试数据**：创建随机的股票数据
- **生成演示数据**：创建包含预设信号的演示数据
- **测试策略引擎**：运行策略分析并查看结果
- **运行回测**：执行简化的回测流程

### 3. 查看结果
- **策略信号**：显示检测到的交易信号
- **技术指标**：显示计算出的技术指标值
- **回测结果**：显示回测的统计结果
- **日志输出**：显示详细的执行日志

## 策略说明

### 起飞模式（预备仓）
这是一个相对保守的信号，用于提前建立小仓位：

**触发条件（4选3）：**
1. K线上穿D线，且K值在20-50之间
2. 收盘价上穿20日指数移动平均线
3. 成交量达到20日平均的1.2倍，或OBV连续3天上涨
4. MACD柱线连续2天上升

**操作建议：**
- 建立1/3仓位
- 如果3-5个交易日内未达到+0.5R收益，则止损退出

### 强势模式（核心仓）
这是一个更严格的信号，用于建立主要仓位：

**触发条件（全部满足）：**
1. 收盘价在60日均线上方，且60日均线向上，周线趋势向上
2. 成交量达到20日平均的1.5倍，且对数成交量在成交量布林带上轨之上
3. K线上穿D线，且K值大于50
4. 收盘价突破20日最高价或布林带上轨
5. MACD柱线大于0且连续2天上升

**操作建议：**
- 建立核心仓位
- 同样执行3-5日的时间止损规则

### 风控机制
- **信号去抖**：同一方向的信号在7个交易日内只取一次
- **时间止损**：如果3-5个交易日内未达到+0.5R收益，自动止损
- **目标收益**：达到+0.5R收益时考虑止盈

## 参数调优建议

### 市场环境适应
- **牛市**：可以适当放宽起飞模式的条件，提高信号频率
- **熊市**：应该提高强势模式的要求，降低风险
- **震荡市**：重点关注时间止损，避免长时间持仓

### 个股特性
- **大盘股**：可以适当降低成交量要求
- **小盘股**：应该提高成交量要求，避免操纵风险
- **高波动股**：可以适当放宽价格突破的条件

### 风险偏好
- **保守型**：提高强势模式的比重，降低起飞模式的仓位
- **激进型**：可以适当提高目标收益率，延长持仓时间

## 常见问题

### Q: 为什么没有检测到信号？
A: 可能的原因：
1. 当前市场条件不满足策略要求
2. 策略参数设置过于严格
3. 数据质量问题

### Q: 信号频率太低怎么办？
A: 可以尝试：
1. 适当放宽起飞模式的条件要求
2. 降低成交量比率阈值
3. 调整K值范围

### Q: 回测结果不理想怎么办？
A: 建议：
1. 检查参数设置是否合理
2. 尝试不同的时间周期
3. 考虑市场环境的影响
4. 优化止损和止盈策略

### Q: 如何提高策略胜率？
A: 可以考虑：
1. 提高强势模式的权重
2. 加强趋势过滤条件
3. 优化入场时机
4. 改进止损策略

## 注意事项

### 风险提示
1. **历史表现不代表未来收益**：回测结果仅供参考
2. **市场风险**：股票投资存在亏损风险
3. **策略局限性**：任何策略都有其适用范围和局限性
4. **实盘差异**：实际交易中可能存在滑点、手续费等额外成本

### 使用建议
1. **充分回测**：在实盘使用前进行充分的历史回测
2. **小仓位试验**：初期使用小仓位验证策略效果
3. **持续监控**：定期检查策略表现，及时调整参数
4. **风险控制**：严格执行止损规则，控制单次损失

### 技术要求
- **浏览器兼容性**：建议使用Chrome 80+、Firefox 75+或Safari 13+
- **JavaScript支持**：确保浏览器启用JavaScript
- **本地文件访问**：某些功能可能需要通过HTTP服务器访问

## 技术支持

如果遇到技术问题：
1. 检查浏览器控制台的错误信息
2. 确认所有JavaScript文件都已正确加载
3. 验证数据格式是否正确
4. 查看策略测试页面的日志输出

更多详细信息请参考 `STRATEGY_README.md` 文档。
