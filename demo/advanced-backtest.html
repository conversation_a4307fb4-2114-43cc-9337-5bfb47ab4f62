<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级策略回测系统 - 量化交易平台</title>
    
    <!-- 基础样式 -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/backtest.css">
    
    <!-- 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- ECharts 图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            color: #F9FAFB;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .header {
            background: rgba(31, 41, 55, 0.9);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid #374151;
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            display: flex;
            align-items: center;
            font-size: 1.5rem;
            font-weight: bold;
            color: #3B82F6;
        }
        
        .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }
        
        .nav-link {
            color: #D1D5DB;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            transition: all 0.2s;
        }
        
        .nav-link:hover {
            background-color: rgba(59, 130, 246, 0.1);
            color: #3B82F6;
        }
        
        .nav-link.active {
            background-color: #3B82F6;
            color: white;
        }
        
        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .footer {
            background: rgba(31, 41, 55, 0.9);
            border-top: 1px solid #374151;
            padding: 2rem;
            text-align: center;
            color: #9CA3AF;
            margin-top: 4rem;
        }
        
        /* 工具提示样式 */
        .tooltip {
            position: relative;
            cursor: help;
        }
        
        .tooltip::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(17, 24, 39, 0.95);
            color: #F9FAFB;
            padding: 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            white-space: nowrap;
            z-index: 1000;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s;
        }
        
        .tooltip:hover::after {
            opacity: 1;
        }
        
        /* 加载动画 */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #374151;
            border-radius: 50%;
            border-top-color: #3B82F6;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .header {
                padding: 1rem;
            }
            
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            
            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
                gap: 1rem;
            }
            
            .main-content {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-chart-line mr-2"></i>
                量化交易平台
            </div>
            <nav class="nav-links">
                <a href="index.html" class="nav-link">
                    <i class="fas fa-home mr-1"></i>
                    首页
                </a>
                <a href="strategy-test.html" class="nav-link">
                    <i class="fas fa-flask mr-1"></i>
                    策略测试
                </a>
                <a href="advanced-backtest.html" class="nav-link active">
                    <i class="fas fa-rocket mr-1"></i>
                    高级回测
                </a>
                <a href="#" class="nav-link">
                    <i class="fas fa-database mr-1"></i>
                    数据中心
                </a>
                <a href="#" class="nav-link">
                    <i class="fas fa-cog mr-1"></i>
                    设置
                </a>
            </nav>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 页面介绍 -->
        <div class="card p-6 mb-6">
            <div class="flex items-start justify-between">
                <div>
                    <h1 class="text-3xl font-bold mb-2 flex items-center">
                        <i class="fas fa-rocket mr-3 text-purple-400"></i>
                        高级策略回测系统
                    </h1>
                    <p class="text-gray-400 text-lg mb-4">
                        基于真实市场数据的专业级策略回测平台，提供完整的信号标注、交易模拟和统计分析功能
                    </p>
                    <div class="flex flex-wrap gap-4 text-sm">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-400 mr-2"></i>
                            <span>K线图信号标注</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-400 mr-2"></i>
                            <span>精确交易模拟</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-400 mr-2"></i>
                            <span>专业统计分析</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-400 mr-2"></i>
                            <span>多维度可视化</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-400 mr-2"></i>
                            <span>Excel报告导出</span>
                        </div>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-sm text-gray-400 mb-2">系统版本</div>
                    <div class="text-lg font-bold text-blue-400">v2.0.0</div>
                    <div class="text-xs text-gray-500 mt-1">Beta</div>
                </div>
            </div>
        </div>

        <!-- 快速开始指南 -->
        <div class="card p-6 mb-6">
            <h3 class="text-lg font-semibold mb-4 flex items-center">
                <i class="fas fa-play-circle mr-2 text-green-400"></i>
                快速开始
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="text-center p-4 bg-gray-800 rounded-lg">
                    <div class="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-3">
                        <span class="text-white font-bold">1</span>
                    </div>
                    <h4 class="font-semibold mb-2">配置参数</h4>
                    <p class="text-sm text-gray-400">设置股票代码、时间范围和交易参数</p>
                </div>
                <div class="text-center p-4 bg-gray-800 rounded-lg">
                    <div class="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-3">
                        <span class="text-white font-bold">2</span>
                    </div>
                    <h4 class="font-semibold mb-2">运行回测</h4>
                    <p class="text-sm text-gray-400">点击开始按钮执行策略回测分析</p>
                </div>
                <div class="text-center p-4 bg-gray-800 rounded-lg">
                    <div class="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-3">
                        <span class="text-white font-bold">3</span>
                    </div>
                    <h4 class="font-semibold mb-2">查看结果</h4>
                    <p class="text-sm text-gray-400">分析K线图标注和统计报告</p>
                </div>
                <div class="text-center p-4 bg-gray-800 rounded-lg">
                    <div class="w-12 h-12 bg-yellow-600 rounded-full flex items-center justify-center mx-auto mb-3">
                        <span class="text-white font-bold">4</span>
                    </div>
                    <h4 class="font-semibold mb-2">导出报告</h4>
                    <p class="text-sm text-gray-400">导出详细的Excel分析报告</p>
                </div>
            </div>
        </div>

        <!-- 回测系统主界面 -->
        <div id="strategyResults">
            <!-- 这里将由 BacktestManager 动态生成内容 -->
        </div>
    </main>

    <!-- 页面底部 -->
    <footer class="footer">
        <div class="max-w-1400px mx-auto">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-6">
                <div>
                    <h4 class="font-semibold mb-3">功能特性</h4>
                    <ul class="space-y-2 text-sm">
                        <li>• 起飞模式和强势模式信号检测</li>
                        <li>• 多种止盈止损策略</li>
                        <li>• 实时资金曲线和回撤分析</li>
                        <li>• 月度收益热力图</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-3">技术指标</h4>
                    <ul class="space-y-2 text-sm">
                        <li>• KDJ随机指标</li>
                        <li>• MACD指数平滑移动平均</li>
                        <li>• EMA指数移动平均线</li>
                        <li>• OBV能量潮指标</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-3">统计分析</h4>
                    <ul class="space-y-2 text-sm">
                        <li>• 胜率和盈亏比分析</li>
                        <li>• 夏普比率和最大回撤</li>
                        <li>• 策略效果对比</li>
                        <li>• 交易明细记录</li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 pt-6">
                <p>&copy; 2024 量化交易平台. 专业的策略回测和分析工具.</p>
                <p class="text-sm mt-2">
                    <span class="tooltip" data-tooltip="本系统仅供学习和研究使用">
                        <i class="fas fa-info-circle mr-1"></i>
                        风险提示
                    </span>
                    ：历史回测结果不代表未来表现，投资有风险，入市需谨慎。
                </p>
            </div>
        </div>
    </footer>

    <!-- JavaScript 依赖 -->
    <!-- 策略引擎 -->
    <script src="js/strategy/strategy-engine.js"></script>

    <!-- 演示数据生成器 -->
    <script src="js/strategy/demo-data.js"></script>

    <!-- 回测系统组件 -->
    <script src="js/strategy/advanced-backtest-engine.js"></script>
    <script src="js/strategy/backtest-visualizer.js"></script>
    <script src="js/strategy/backtest-config.js"></script>
    <script src="js/strategy/backtest-report.js"></script>
    <script src="js/strategy/backtest-manager.js"></script>

    <!-- 页面初始化脚本 -->
    <script>
        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            // 延迟检查，确保所有脚本都已加载
            setTimeout(() => {
                console.log('高级策略回测系统已加载');

                // 检查必要的依赖
                const requiredClasses = [
                    'StrategyEngine',
                    'DemoDataGenerator',
                    'AdvancedBacktestEngine',
                    'BacktestVisualizer',
                    'BacktestConfig',
                    'BacktestReport',
                    'BacktestManager'
                ];

                const missingClasses = requiredClasses.filter(className => !window[className]);

                if (missingClasses.length > 0) {
                    console.error('缺少必要的组件:', missingClasses);
                    showErrorMessage('系统初始化失败，缺少必要组件: ' + missingClasses.join(', '));

                    // 显示调试信息
                    console.log('当前可用的组件:', Object.keys(window).filter(key =>
                        key.includes('Engine') || key.includes('Backtest') || key.includes('Strategy')
                    ));
                    return;
                }

                // 显示系统就绪消息
                setTimeout(() => {
                    showSuccessMessage('高级回测系统已就绪，可以开始使用！');
                }, 1000);

                // 添加键盘快捷键提示
                document.addEventListener('keydown', function(e) {
                    if (e.ctrlKey && e.shiftKey && e.key === '?') {
                        showHelpModal();
                    }
                });
            }, 2000); // 增加延迟时间
        });
        
        // 显示成功消息
        function showSuccessMessage(message) {
            showNotification(message, 'success');
        }
        
        // 显示错误消息
        function showErrorMessage(message) {
            showNotification(message, 'error');
        }
        
        // 通用通知函数
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-${getIconForType(type)} mr-2"></i>
                    <span>${message}</span>
                </div>
            `;
            
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 6px;
                color: white;
                z-index: 10000;
                max-width: 400px;
                word-wrap: break-word;
                animation: slideIn 0.3s ease-out;
            `;
            
            const colors = {
                success: '#10B981',
                error: '#EF4444',
                warning: '#F59E0B',
                info: '#3B82F6'
            };
            
            notification.style.backgroundColor = colors[type] || colors.info;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }
        
        // 获取通知类型对应的图标
        function getIconForType(type) {
            const icons = {
                success: 'check-circle',
                error: 'exclamation-circle',
                warning: 'exclamation-triangle',
                info: 'info-circle'
            };
            return icons[type] || 'info-circle';
        }
        
        // 显示帮助模态框
        function showHelpModal() {
            const helpContent = `
                <div style="max-width: 600px; background: #1F2937; padding: 2rem; border-radius: 0.75rem; color: #F9FAFB;">
                    <h3 style="margin-bottom: 1rem; color: #3B82F6;">快捷键说明</h3>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin-bottom: 0.5rem;"><kbd>Ctrl + Enter</kbd> - 开始回测</li>
                        <li style="margin-bottom: 0.5rem;"><kbd>Ctrl + Esc</kbd> - 停止回测</li>
                        <li style="margin-bottom: 0.5rem;"><kbd>Ctrl + Shift + ?</kbd> - 显示帮助</li>
                    </ul>
                    <div style="margin-top: 1.5rem; padding-top: 1rem; border-top: 1px solid #374151;">
                        <button onclick="this.closest('.modal-overlay').remove()" 
                                style="background: #3B82F6; color: white; padding: 0.5rem 1rem; border: none; border-radius: 0.375rem; cursor: pointer;">
                            关闭
                        </button>
                    </div>
                </div>
            `;
            
            const overlay = document.createElement('div');
            overlay.className = 'modal-overlay';
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            `;
            overlay.innerHTML = helpContent;
            
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    overlay.remove();
                }
            });
            
            document.body.appendChild(overlay);
        }
        
        // 添加滑入滑出动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
            kbd {
                background: #374151;
                border: 1px solid #4B5563;
                border-radius: 0.25rem;
                padding: 0.125rem 0.25rem;
                font-family: monospace;
                font-size: 0.75rem;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
