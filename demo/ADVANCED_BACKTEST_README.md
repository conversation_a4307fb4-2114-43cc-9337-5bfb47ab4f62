# 高级策略回测系统

## 概述

高级策略回测系统是一个专业级的量化交易策略回测平台，提供完整的信号标注、交易模拟和统计分析功能。系统基于现有的策略引擎，实现了精确的交易模拟和多维度的可视化分析。

## 核心功能

### 1. 信号标注与可视化
- ✅ **K线图信号标注**：在K线图上清晰标注所有买入信号（起飞模式、强势模式）
- ✅ **卖出信号标注**：标注对应的卖出信号（止盈、止损、时间止损）
- ✅ **信号分类显示**：使用不同颜色和图标区分不同类型的信号
- ✅ **悬停详情**：支持信号详情的悬停显示（价格、时间、触发条件）

### 2. 交易模拟执行
- ✅ **策略规则执行**：严格按照策略规则执行（起飞模式1/3仓位，强势模式核心仓位）
- ✅ **时间止损机制**：实现3-5根K线的时间止损机制
- ✅ **目标止盈机制**：实现+0.5R的目标止盈机制
- ✅ **费用计算**：精确计算手续费、印花税和滑点成本

### 3. 用户参数配置
- ✅ **交易设置**：每次交易股数设置（固定股数或按资金比例）
- ✅ **费用设置**：手续费率（0.1‰-5‰）、印花税（默认1‰）、滑点设置
- ✅ **资金管理**：初始资金设置、最大持仓比例限制
- ✅ **策略参数**：起飞/强势模式仓位比例、止盈止损参数
- ✅ **高级设置**：价格止损、移动止损等高级功能

### 4. 统计分析功能
- ✅ **胜率统计**：盈利交易次数/总交易次数
- ✅ **收益统计**：总收益率、年化收益率、月度收益率
- ✅ **风险指标**：最大回撤、夏普比率、波动率
- ✅ **交易分析**：平均持仓天数、平均盈利、平均亏损、盈亏比
- ✅ **策略效果**：起飞模式vs强势模式的胜率对比
- ✅ **退出分析**：不同退出原因的统计分析

### 5. 结果展示
- ✅ **资金曲线图表**：净值走势和收益率曲线
- ✅ **回撤曲线图表**：实时回撤监控
- ✅ **月度收益热力图**：直观的月度表现展示
- ✅ **交易记录表格**：详细的交易记录（开仓时间、平仓时间、持仓天数、收益率等）
- ✅ **收益分布图**：交易收益的分布统计
- ✅ **持仓天数分布**：持仓周期分析

### 6. 交互功能
- ✅ **时间段选择**：支持选择不同的回测时间段
- ✅ **参数优化**：提供保守型、平衡型、激进型、短线型预设
- ✅ **结果导出**：支持Excel和JSON格式的结果导出
- ✅ **配置管理**：支持配置的保存和加载

## 技术架构

### 核心组件

1. **AdvancedBacktestEngine** - 高级回测引擎
   - 负责策略信号分析和交易模拟
   - 实现精确的费用计算和风险控制
   - 生成详细的统计数据

2. **BacktestVisualizer** - 可视化组件
   - 基于ECharts的专业图表展示
   - K线图信号标注和交易标记
   - 多维度数据可视化

3. **BacktestConfig** - 参数配置组件
   - 用户友好的参数设置界面
   - 预设配置和自定义配置
   - 配置验证和保存功能

4. **BacktestReport** - 统计报告组件
   - 详细的统计分析和报告生成
   - 交易明细和过滤功能
   - 多格式导出支持

5. **BacktestManager** - 回测管理器
   - 整合所有组件的主控制器
   - 流程控制和状态管理
   - 用户交互和通知系统

### 技术特性

- **模块化设计**：各组件独立开发，易于维护和扩展
- **响应式界面**：适配不同屏幕尺寸的设备
- **实时计算**：高效的数据处理和计算引擎
- **可扩展性**：支持新策略和指标的集成

## 使用指南

### 快速开始

1. **打开系统**
   ```
   访问 advanced-backtest.html 页面
   ```

2. **配置参数**
   - 输入股票代码（如：000001.SZ）
   - 设置回测时间范围
   - 配置资金和费用参数
   - 选择策略参数

3. **运行回测**
   - 点击"开始回测"按钮
   - 等待系统分析完成
   - 查看结果和报告

4. **分析结果**
   - 查看K线图上的信号标注
   - 分析资金曲线和回撤
   - 查看详细的统计报告
   - 导出分析结果

### 参数说明

#### 基础设置
- **股票代码**：要回测的股票代码
- **开始日期**：回测开始时间
- **结束日期**：回测结束时间

#### 资金设置
- **初始资金**：回测开始时的可用资金
- **仓位管理**：固定股数或按资金比例
- **最大持仓比例**：单次交易最大使用资金比例

#### 费用设置
- **手续费率**：交易手续费（双向收取）
- **印花税率**：印花税（仅卖出时收取）
- **滑点**：模拟实际交易中的价格偏差

#### 策略设置
- **起飞模式仓位**：起飞模式信号的仓位比例
- **强势模式仓位**：强势模式信号的仓位比例
- **目标止盈**：达到此收益率时自动止盈
- **时间止损**：最小和最大持仓天数设置

### 预设配置

系统提供四种预设配置：

1. **保守型**
   - 较小的仓位比例
   - 较低的止盈目标
   - 较短的持仓时间
   - 启用价格止损和移动止损

2. **平衡型**（默认）
   - 中等的仓位比例
   - 适中的止盈目标
   - 标准的持仓时间
   - 启用价格止损

3. **激进型**
   - 较大的仓位比例
   - 较高的止盈目标
   - 较长的持仓时间
   - 较宽松的止损设置

4. **短线型**
   - 中等的仓位比例
   - 较低的止盈目标
   - 很短的持仓时间
   - 严格的止损设置

## 文件结构

```
demo/
├── advanced-backtest.html              # 主页面
├── css/
│   └── backtest.css                   # 回测系统样式
├── js/strategy/
│   ├── advanced-backtest-engine.js    # 高级回测引擎
│   ├── backtest-visualizer.js         # 可视化组件
│   ├── backtest-config.js             # 参数配置组件
│   ├── backtest-report.js             # 统计报告组件
│   ├── backtest-manager.js            # 回测管理器
│   └── strategy-engine.js             # 策略引擎（依赖）
└── ADVANCED_BACKTEST_README.md        # 本文档
```

## 快捷键

- `Ctrl + Enter` - 开始回测
- `Ctrl + Esc` - 停止回测
- `Ctrl + Shift + ?` - 显示帮助

## 注意事项

1. **数据来源**：当前使用模拟数据，实际使用时需要接入真实的股票数据源
2. **性能优化**：大量数据回测时建议分批处理
3. **风险提示**：历史回测结果不代表未来表现，投资有风险
4. **浏览器兼容**：建议使用现代浏览器（Chrome、Firefox、Safari、Edge）

## 开发计划

### 已完成功能 ✅
- [x] 基础回测引擎
- [x] 信号标注和可视化
- [x] 参数配置界面
- [x] 统计报告生成
- [x] 交易明细展示
- [x] 结果导出功能

### 计划中功能 🚧
- [ ] 实时数据接入
- [ ] 多股票批量回测
- [ ] 参数优化算法
- [ ] 基准指数对比
- [ ] 更多技术指标
- [ ] 策略组合回测
- [ ] 风险管理模块
- [ ] 机器学习集成

## 技术支持

如有问题或建议，请查看：
1. 系统内置帮助（Ctrl + Shift + ?）
2. 控制台错误信息
3. 浏览器开发者工具

## 更新日志

### v2.0.0 (2024-08-13)
- 🎉 首次发布高级回测系统
- ✨ 完整的信号标注和可视化功能
- ✨ 专业级统计分析和报告
- ✨ 用户友好的参数配置界面
- ✨ 多维度数据可视化
- ✨ 结果导出和分享功能
