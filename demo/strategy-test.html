<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>策略测试页面</title>
    <link href="https://unpkg.com/tailwindcss@2.2.9/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #0a0e1a;
            color: #ffffff;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .card {
            background: #1a1f2e;
            border: 1px solid #374151;
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }
        .input-field {
            background: #2a2f3e;
            border: 1px solid #374151;
            border-radius: 12px;
            padding: 12px 16px;
            color: #ffffff;
            width: 100%;
        }
        .signal-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .signal-buy {
            background-color: rgba(16, 185, 129, 0.2);
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.3);
        }
        .signal-sell {
            background-color: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.3);
        }
        .signal-hold {
            background-color: rgba(156, 163, 175, 0.2);
            color: #9ca3af;
            border: 1px solid rgba(156, 163, 175, 0.3);
        }
        .mono-font {
            font-family: 'JetBrains Mono', 'Monaco', 'Consolas', monospace;
        }
        .price-up { color: #10b981; }
        .price-down { color: #ef4444; }
        .price-flat { color: #6b7280; }
    </style>
</head>
<body class="p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">
            <i class="fas fa-rocket mr-3 text-blue-400"></i>
            量化策略测试页面
        </h1>

        <!-- 策略引擎测试 -->
        <div class="card p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">策略引擎测试</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block text-sm text-gray-400 mb-2">测试股票代码</label>
                    <input type="text" class="input-field" id="testStockCode" value="000001" placeholder="输入股票代码">
                </div>
                <div>
                    <label class="block text-sm text-gray-400 mb-2">测试数据长度</label>
                    <input type="number" class="input-field" id="testDataLength" value="120" min="50" max="500">
                </div>
            </div>
            <div class="flex space-x-4">
                <button class="btn-primary" onclick="testStrategyEngine()">
                    <i class="fas fa-play mr-2"></i>
                    测试策略引擎
                </button>
                <button class="btn-primary" onclick="generateTestData()">
                    <i class="fas fa-database mr-2"></i>
                    生成测试数据
                </button>
                <button class="btn-primary" onclick="generateDemoData()">
                    <i class="fas fa-magic mr-2"></i>
                    生成演示数据
                </button>
                <button class="btn-primary" onclick="runBacktest()">
                    <i class="fas fa-chart-bar mr-2"></i>
                    运行回测
                </button>
            </div>
        </div>

        <!-- 测试结果显示 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 策略信号 -->
            <div class="card p-6">
                <h3 class="text-lg font-semibold mb-4">策略信号</h3>
                <div id="signalResults" class="space-y-3">
                    <p class="text-gray-400">点击"测试策略引擎"查看信号结果</p>
                </div>
            </div>

            <!-- 技术指标 -->
            <div class="card p-6">
                <h3 class="text-lg font-semibold mb-4">技术指标</h3>
                <div id="indicatorResults" class="space-y-3">
                    <p class="text-gray-400">点击"测试策略引擎"查看指标结果</p>
                </div>
            </div>
        </div>

        <!-- 回测结果 -->
        <div class="card p-6 mt-6">
            <h3 class="text-lg font-semibold mb-4">回测结果</h3>
            <div id="backtestResults">
                <p class="text-gray-400">点击"运行回测"查看回测结果</p>
            </div>
        </div>

        <!-- 日志输出 -->
        <div class="card p-6 mt-6">
            <h3 class="text-lg font-semibold mb-4">日志输出</h3>
            <div id="logOutput" class="bg-gray-900 p-4 rounded-lg font-mono text-sm h-64 overflow-y-auto">
                <p class="text-green-400">[INFO] 策略测试页面已加载</p>
            </div>
        </div>
    </div>

    <!-- 引入策略相关JavaScript文件 -->
    <script src="js/strategy/strategy-engine.js"></script>
    <script src="js/strategy/strategy-panel.js"></script>
    <script src="js/strategy/strategy-results.js"></script>
    <script src="js/strategy/strategy-demo.js"></script>

    <script>
        // 全局变量
        let strategyEngine = null;
        let testData = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始初始化策略引擎...');
            initializeStrategy();
        });

        // 初始化策略引擎
        function initializeStrategy() {
            try {
                if (typeof StrategyEngine !== 'undefined') {
                    strategyEngine = new StrategyEngine();
                    log('策略引擎初始化成功');
                } else {
                    log('错误：StrategyEngine 类未找到', 'error');
                }
            } catch (error) {
                log('策略引擎初始化失败: ' + error.message, 'error');
            }
        }

        // 生成测试数据
        function generateTestData() {
            const stockCode = document.getElementById('testStockCode').value;
            const dataLength = parseInt(document.getElementById('testDataLength').value);

            log(`开始生成 ${stockCode} 的测试数据，长度: ${dataLength}`);

            testData = {
                dates: [],
                opens: [],
                highs: [],
                lows: [],
                closes: [],
                volumes: []
            };

            let basePrice = 10 + Math.random() * 40; // 基础价格 10-50

            for (let i = 0; i < dataLength; i++) {
                const date = new Date();
                date.setDate(date.getDate() - (dataLength - i));
                testData.dates.push(date.toISOString().split('T')[0]);

                // 生成价格数据（随机游走）
                const change = (Math.random() - 0.5) * 0.1; // ±5% 变化
                basePrice *= (1 + change);

                const open = basePrice * (0.98 + Math.random() * 0.04);
                const close = basePrice * (0.98 + Math.random() * 0.04);
                const high = Math.max(open, close) * (1 + Math.random() * 0.03);
                const low = Math.min(open, close) * (1 - Math.random() * 0.03);

                testData.opens.push(open);
                testData.highs.push(high);
                testData.lows.push(low);
                testData.closes.push(close);
                testData.volumes.push(Math.floor(Math.random() * 1000000 + 100000));
            }

            log(`测试数据生成完成，共 ${dataLength} 个数据点`);
            displayTestData();
        }

        // 生成演示数据（包含信号）
        function generateDemoData() {
            const stockCode = document.getElementById('testStockCode').value;
            const dataLength = parseInt(document.getElementById('testDataLength').value);

            if (!window.strategyDemo) {
                log('演示数据生成器未找到', 'error');
                return;
            }

            log(`开始生成 ${stockCode} 的演示数据（包含信号），长度: ${dataLength}`);

            try {
                testData = window.strategyDemo.generateSignalDemoData(stockCode, 'mixed');

                // 如果需要调整数据长度
                if (testData.closes.length !== dataLength) {
                    log(`调整数据长度从 ${testData.closes.length} 到 ${dataLength}`);
                    if (dataLength < testData.closes.length) {
                        // 截取最后的数据
                        const startIndex = testData.closes.length - dataLength;
                        testData.dates = testData.dates.slice(startIndex);
                        testData.opens = testData.opens.slice(startIndex);
                        testData.highs = testData.highs.slice(startIndex);
                        testData.lows = testData.lows.slice(startIndex);
                        testData.closes = testData.closes.slice(startIndex);
                        testData.volumes = testData.volumes.slice(startIndex);
                    }
                }

                log(`演示数据生成完成，共 ${testData.closes.length} 个数据点`);
                log('演示数据包含预设的交易信号模式', 'info');
                displayTestData();

            } catch (error) {
                log('演示数据生成失败: ' + error.message, 'error');
            }
        }

        // 显示测试数据
        function displayTestData() {
            if (!testData) return;
            
            const latest = testData.closes.length - 1;
            const price = testData.closes[latest].toFixed(2);
            const volume = testData.volumes[latest];
            
            log(`最新数据 - 价格: ${price}, 成交量: ${volume}`);
        }

        // 测试策略引擎
        function testStrategyEngine() {
            if (!strategyEngine) {
                log('策略引擎未初始化', 'error');
                return;
            }
            
            if (!testData) {
                log('请先生成测试数据', 'warning');
                generateTestData();
                return;
            }
            
            log('开始运行策略分析...');
            
            try {
                // 设置当前数据
                strategyEngine.setCurrentData(testData);
                
                // 运行策略分析
                const results = strategyEngine.analyze(testData);
                
                log(`策略分析完成，发现 ${results.signals.length} 个信号`);
                
                // 显示信号结果
                displaySignalResults(results.signals);
                
                // 显示技术指标
                displayIndicatorResults(results.indicators);
                
            } catch (error) {
                log('策略分析失败: ' + error.message, 'error');
                console.error(error);
            }
        }

        // 显示信号结果
        function displaySignalResults(signals) {
            const container = document.getElementById('signalResults');
            
            if (signals.length === 0) {
                container.innerHTML = '<p class="text-gray-400">未发现交易信号</p>';
                return;
            }
            
            container.innerHTML = signals.map(signal => `
                <div class="bg-gray-800 p-3 rounded-lg">
                    <div class="flex items-center justify-between mb-2">
                        <span class="signal-indicator ${signal.type === 'takeoff' ? 'signal-buy' : 'signal-sell'}">
                            <i class="fas fa-${signal.type === 'takeoff' ? 'rocket' : 'fire'}"></i>
                            ${signal.mode}
                        </span>
                        <span class="text-sm text-gray-400">${signal.date}</span>
                    </div>
                    <div class="text-sm">
                        <p><strong>价格:</strong> <span class="mono-font">¥${signal.price.toFixed(2)}</span></p>
                        <p><strong>仓位:</strong> ${signal.position}</p>
                        <p><strong>强度:</strong> ${signal.strength}/5</p>
                        <p><strong>条件:</strong> ${signal.conditions.join(', ')}</p>
                    </div>
                </div>
            `).join('');
        }

        // 显示技术指标结果
        function displayIndicatorResults(indicators) {
            const container = document.getElementById('indicatorResults');
            const latest = indicators.ema20.length - 1;
            
            container.innerHTML = `
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span>EMA20:</span>
                        <span class="mono-font">${indicators.ema20[latest]?.toFixed(2) || '--'}</span>
                    </div>
                    <div class="flex justify-between">
                        <span>MA60:</span>
                        <span class="mono-font">${indicators.ma60[latest]?.toFixed(2) || '--'}</span>
                    </div>
                    <div class="flex justify-between">
                        <span>KDJ-K:</span>
                        <span class="mono-font">${indicators.kdj.k[latest]?.toFixed(2) || '--'}</span>
                    </div>
                    <div class="flex justify-between">
                        <span>KDJ-D:</span>
                        <span class="mono-font">${indicators.kdj.d[latest]?.toFixed(2) || '--'}</span>
                    </div>
                    <div class="flex justify-between">
                        <span>布林上轨:</span>
                        <span class="mono-font">${indicators.bb.upper[latest]?.toFixed(2) || '--'}</span>
                    </div>
                    <div class="flex justify-between">
                        <span>成交量比率:</span>
                        <span class="mono-font">${indicators.volumeRatio[latest]?.toFixed(2) || '--'}</span>
                    </div>
                    <div class="flex justify-between">
                        <span>MACD柱线:</span>
                        <span class="mono-font ${(indicators.macd.histogram[latest] || 0) > 0 ? 'price-up' : 'price-down'}">
                            ${indicators.macd.histogram[latest]?.toFixed(4) || '--'}
                        </span>
                    </div>
                </div>
            `;
        }

        // 运行回测
        function runBacktest() {
            if (!testData) {
                log('请先生成测试数据', 'warning');
                return;
            }
            
            log('开始运行回测...');
            
            // 模拟回测结果
            setTimeout(() => {
                const results = {
                    totalReturn: (Math.random() * 40 - 10).toFixed(2),
                    totalTrades: Math.floor(Math.random() * 20 + 5),
                    winRate: (50 + Math.random() * 30).toFixed(1),
                    maxDrawdown: -(Math.random() * 15 + 5).toFixed(2)
                };
                
                displayBacktestResults(results);
                log('回测完成');
            }, 1000);
        }

        // 显示回测结果
        function displayBacktestResults(results) {
            const container = document.getElementById('backtestResults');
            
            container.innerHTML = `
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold mono-font ${parseFloat(results.totalReturn) > 0 ? 'price-up' : 'price-down'}">
                            ${results.totalReturn}%
                        </div>
                        <div class="text-sm text-gray-400">总收益率</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold mono-font">${results.totalTrades}</div>
                        <div class="text-sm text-gray-400">总交易次数</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold mono-font ${parseFloat(results.winRate) > 60 ? 'price-up' : 'price-flat'}">
                            ${results.winRate}%
                        </div>
                        <div class="text-sm text-gray-400">胜率</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold mono-font price-down">${results.maxDrawdown}%</div>
                        <div class="text-sm text-gray-400">最大回撤</div>
                    </div>
                </div>
            `;
        }

        // 日志输出函数
        function log(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = {
                'info': 'text-green-400',
                'warning': 'text-yellow-400',
                'error': 'text-red-400'
            }[type] || 'text-gray-300';
            
            const logEntry = document.createElement('p');
            logEntry.className = colorClass;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logOutput.appendChild(logEntry);
            logOutput.scrollTop = logOutput.scrollHeight;
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    </script>
</body>
</html>
