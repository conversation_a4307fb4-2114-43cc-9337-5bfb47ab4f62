# 量化交易策略系统

## 概述

这是一个基于Web的量化交易策略系统，实现了"起飞模式"和"强势模式"两种交易策略。系统专为A股市场设计，使用日K线数据进行分析。

## 策略说明

### 起飞模式（提前试仓）

**触发条件（4选3）：**
1. K上穿D，且K在20-50之间（刚抬头）
2. 收盘价上穿EMA20（站回中轨，抗低波动噪声）
3. 成交量≥近20日的1.2倍，或OBV连涨3天
4. MACD柱线连续2根上升（动能拐头）

**趋势条件：**
- 日线趋势可以稍弱，但周线趋势不能明显向下
- 优化：周线趋势判断为MA_week20向上或收盘在MA_week20之上

**操作：**
- 满足条件→先上预备仓（1/3仓）
- 若3-5根K内未达+0.5R，则时间止损退出

### 强势模式（确认加仓）

**触发条件（全部满足）：**
1. 大方向向上：收盘在MA60上方、MA60向上，且周线趋势向上
2. 成交量≥近20日的1.5倍，且log_vol在量的布林带上轨之上
3. K上穿D，且K>50（放宽D的限制，容纳回调二启）
4. 收盘价>Highest(close,20)或收盘价>bb_upper（突破结构）
5. MACD柱线>0且连续2根上升（动能延续）

**操作：**
- 核心仓位，同样3-5根K内未达+0.5R则时间止损

### 公共风控机制

- **信号去抖：** 同一方向信号7根K线内只取一次
- **时间止损：** 3-5根K内未达+0.5R，时间止损退出，等待下一次信号
- **目标收益：** +0.5R（可配置）

## 文件结构

```
demo/
├── index.html                    # 主界面
├── strategy-test.html            # 策略测试页面
├── js/strategy/
│   ├── strategy-engine.js        # 策略引擎核心
│   ├── strategy-panel.js         # 策略配置和监控面板
│   └── strategy-results.js       # 策略回测结果显示
└── STRATEGY_README.md            # 本文档
```

## 核心组件

### 1. StrategyEngine（策略引擎）

**主要功能：**
- 技术指标计算（EMA、SMA、KDJ、布林带、MACD、OBV等）
- 信号检测（起飞模式、强势模式）
- 信号去抖和时间管理
- 策略分析和报告生成

**主要方法：**
```javascript
// 分析股票数据
const results = strategyEngine.analyze(stockData);

// 设置当前数据
strategyEngine.setCurrentData(stockData);

// 检测起飞模式信号
const takeoffSignal = strategyEngine.checkTakeoffMode(indicators, index);

// 检测强势模式信号
const strongSignal = strategyEngine.checkStrongMode(indicators, index);
```

### 2. StrategyPanel（策略面板）

**主要功能：**
- 策略参数配置
- 实时监控和信号显示
- 信号历史记录
- 配置保存和导出

**配置参数：**
- 起飞模式：K值范围、成交量比率、OBV连涨天数、满足条件数
- 强势模式：K值阈值、成交量比率、MACD连涨天数
- 公共配置：信号冷却期、止损时间范围

### 3. StrategyResults（策略回测）

**主要功能：**
- 策略回测执行
- 收益统计和分析
- 交易明细记录
- 结果导出功能

**统计指标：**
- 总收益率、年化收益率
- 最大回撤、夏普比率
- 交易次数、胜率
- 策略信号统计

## 使用方法

### 1. 基本使用

1. 打开 `index.html`
2. 点击左侧导航的"量化策略"
3. 配置策略参数
4. 选择监控股票
5. 开始实时监控

### 2. 策略回测

1. 点击左侧导航的"策略回测"
2. 选择回测股票和时间周期
3. 设置初始资金和手续费率
4. 点击"开始回测"查看结果

### 3. 策略测试

1. 打开 `strategy-test.html`
2. 输入测试股票代码
3. 生成测试数据
4. 运行策略分析
5. 查看信号和指标结果

## 技术指标说明

### 基础指标
- **EMA20：** 20日指数移动平均线
- **MA60：** 60日简单移动平均线
- **MA_week20：** 周线20日移动平均线

### 动量指标
- **KDJ：** 随机指标，K、D、J三线
- **MACD：** 指数平滑移动平均线，包含柱线

### 趋势指标
- **布林带：** 价格通道指标
- **成交量布林带：** 基于对数成交量的布林带

### 成交量指标
- **OBV：** 能量潮指标
- **成交量比率：** 当前成交量与20日均量的比值

## 配置说明

### 默认配置

```javascript
{
    takeoff: {
        kRange: [20, 50],           // K值范围
        volumeRatio: 1.2,           // 成交量比率
        obvRisingPeriods: 3,        // OBV连涨天数
        requiredConditions: 3       // 4选3
    },
    strong: {
        kThreshold: 50,             // K值阈值
        volumeRatio: 1.5,           // 成交量比率
        macdRisingPeriods: 2        // MACD柱线连涨
    },
    common: {
        signalCooldown: 7,          // 信号冷却期
        stopLossTimeout: [3, 5],    // 止损时间范围
        targetReturn: 0.5           // 目标收益率
    }
}
```

### 自定义配置

可以通过策略面板修改配置参数，支持：
- 实时配置更新
- 配置保存到本地存储
- 配置导入导出
- 重置为默认配置

## 数据格式

### 输入数据格式

```javascript
const stockData = {
    dates: ['2024-01-01', '2024-01-02', ...],    // 日期数组
    opens: [10.5, 10.6, ...],                    // 开盘价数组
    highs: [10.8, 10.9, ...],                    // 最高价数组
    lows: [10.3, 10.4, ...],                     // 最低价数组
    closes: [10.7, 10.8, ...],                   // 收盘价数组
    volumes: [1000000, 1200000, ...]             // 成交量数组
};
```

### 信号输出格式

```javascript
const signal = {
    date: '2024-01-15',                          // 信号日期
    index: 15,                                   // 数据索引
    price: 10.75,                                // 信号价格
    type: 'takeoff',                             // 信号类型
    mode: '起飞模式',                             // 模式名称
    position: '预备仓(1/3)',                      // 仓位建议
    conditions: ['K上穿D且K值适中', '成交量放大'],   // 满足条件
    strength: 3                                  // 信号强度(1-5)
};
```

## 注意事项

1. **数据质量：** 确保输入数据的完整性和准确性
2. **参数调优：** 根据不同市场环境调整策略参数
3. **风险控制：** 严格执行止损规则，控制单次损失
4. **回测验证：** 新参数应先进行充分回测验证
5. **实盘谨慎：** 策略仅供参考，实盘交易需谨慎

## 扩展功能

### 计划中的功能
- [ ] 多股票组合策略
- [ ] 实时数据接入
- [ ] 更多技术指标
- [ ] 机器学习优化
- [ ] 风险管理模块

### 自定义扩展
系统采用模块化设计，支持：
- 自定义技术指标
- 自定义信号检测逻辑
- 自定义风控规则
- 自定义回测指标

## 技术支持

如有问题或建议，请查看：
1. 浏览器控制台的错误信息
2. `strategy-test.html` 的测试结果
3. 策略配置是否正确
4. 数据格式是否符合要求

## 版本信息

- **当前版本：** v1.0.0
- **更新日期：** 2024-08-13
- **兼容性：** 现代浏览器（Chrome 80+, Firefox 75+, Safari 13+）
