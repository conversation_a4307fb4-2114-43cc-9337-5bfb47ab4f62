/**
 * 优化版股票扫描器
 * 基于统一20日周期和移动突破信号检测的高效扫描引擎
 */

class StockScanner {
    constructor(dataSimulator, config = null) {
        this.dataSimulator = dataSimulator;
        this.config = config || window.INDICATOR_CONFIG;
        this.breakthroughDetector = new BreakthroughDetector(this.config);

        this.isScanning = false;
        this.scanResults = [];
        this.scanProgress = { current: 0, total: 0, percentage: 0 };
        this.scanStartTime = null;

        // 使用统一配置的技术指标计算器
        const indicatorConfig = this.config.indicators;
        this.indicators = {
            bollinger: new BollingerBands({
                window: indicatorConfig.bollinger.window,
                stdDev: indicatorConfig.bollinger.stdDev
            }),
            kdj: new KDJIndicator({
                kPeriod: indicatorConfig.kdj.kPeriod,
                dPeriod: indicatorConfig.kdj.dPeriod,
                jPeriod: indicatorConfig.kdj.jPeriod
            }),
            volume: new VolumeAnalysis({
                triangleWindow: indicatorConfig.volume.triangleWindow,
                emaAlpha: indicatorConfig.volume.emaAlpha
            })
        };

        // 扫描条件配置 - 基于突破信号检测
        this.scanConditions = {
            priceBreakthrough: true,        // 价格突破20日均线
            kdjBreakthrough: true,          // KDJ突破20日均线
            bollingerBreakthrough: true,    // 布林带突破
            volumeBreakthrough: true,       // 成交量突破20日均量
            upBreakthroughOnly: false,      // 仅向上突破
            downBreakthroughOnly: false,    // 仅向下突破
            minStrength: 1                  // 最小信号强度
        };

        // 性能优化配置
        this.scanningConfig = this.config.getScanningConfig();
        this.workerPool = [];
        this.initializeWorkerPool();
    }

    /**
     * 初始化Worker池（用于并行计算）
     */
    initializeWorkerPool() {
        // 简化版本，主要在主线程处理
        // 可以后续扩展为真正的Worker池
        this.workerPool = [];
    }

    /**
     * 开始优化扫描
     * @param {Object} options - 扫描选项
     * @param {Function} progressCallback - 进度回调
     * @param {Function} resultCallback - 结果回调
     * @returns {Promise} 扫描结果
     */
    async startScan(options = {}, progressCallback = null, resultCallback = null) {
        if (this.isScanning) {
            throw new Error('扫描正在进行中，请等待完成');
        }

        this.isScanning = true;
        this.scanResults = [];
        this.scanStartTime = performance.now();

        try {
            // 获取股票列表
            const stockList = this.dataSimulator.getStockList(options.filters || {});
            this.scanProgress.total = stockList.length;
            this.scanProgress.current = 0;

            console.log(`开始优化扫描 ${stockList.length} 只股票，使用20日统一周期...`);

            // 使用配置的批处理大小
            const batchSize = options.batchSize || this.scanningConfig.batchSize;
            const maxConcurrent = this.scanningConfig.maxConcurrent;

            // 分批并行处理
            for (let i = 0; i < stockList.length; i += batchSize * maxConcurrent) {
                if (!this.isScanning) break;

                const batches = [];
                for (let j = 0; j < maxConcurrent && i + j * batchSize < stockList.length; j++) {
                    const start = i + j * batchSize;
                    const end = Math.min(start + batchSize, stockList.length);
                    batches.push(stockList.slice(start, end));
                }

                // 并行处理多个批次
                await Promise.all(batches.map(batch =>
                    this.processBatchOptimized(batch, progressCallback, resultCallback)
                ));

                // 控制处理频率
                await new Promise(resolve =>
                    setTimeout(resolve, this.scanningConfig.progressUpdateInterval)
                );
            }

            const scanDuration = performance.now() - this.scanStartTime;
            console.log(`扫描完成，用时 ${scanDuration.toFixed(0)}ms，找到 ${this.scanResults.length} 只符合条件的股票`);

            // 按突破强度排序结果
            this.sortResultsByStrength();

            return this.scanResults;

        } catch (error) {
            console.error('扫描过程中发生错误:', error);
            throw error;
        } finally {
            this.isScanning = false;
        }
    }

    /**
     * 优化的批处理方法
     * @param {Array} batch - 股票批次
     * @param {Function} progressCallback - 进度回调
     * @param {Function} resultCallback - 结果回调
     */
    async processBatchOptimized(batch, progressCallback, resultCallback) {
        for (const stock of batch) {
            try {
                // 使用统一的20日周期生成数据
                const requiredDays = this.config.getUnifiedPeriod() + 10; // 多留一些数据确保计算准确
                const stockData = this.dataSimulator.generateStockData(stock, requiredDays);

                // 验证数据质量
                const validation = this.config.validateData(stockData.close);
                if (!validation.isValid) {
                    console.warn(`股票 ${stock.code} 数据质量不符合要求:`, validation.errors);
                    continue;
                }

                // 使用统一配置分析股票
                const analysis = await this.analyzeStockOptimized(stockData);

                // 使用突破检测器检查信号
                const breakthroughResults = this.breakthroughDetector.detectAllBreakthroughs(stockData, analysis);

                // 过滤符合条件的突破信号
                const filteredBreakthroughs = this.filterBreakthroughsByConditions(breakthroughResults.breakthroughs);

                if (filteredBreakthroughs.length > 0) {
                    const result = {
                        code: stock.code,
                        name: stock.name,
                        market: stock.market,
                        industry: stock.industry,
                        sector: stock.sector,
                        currentPrice: stockData.close[stockData.close.length - 1],
                        breakthroughs: filteredBreakthroughs,
                        summary: {
                            totalSignals: filteredBreakthroughs.length,
                            upBreakthroughs: filteredBreakthroughs.filter(b => b.direction === 'up').length,
                            downBreakthroughs: filteredBreakthroughs.filter(b => b.direction === 'down').length,
                            maxStrength: Math.max(...filteredBreakthroughs.map(b => b.strength)),
                            avgBreakthroughPercent: filteredBreakthroughs.reduce((sum, b) => sum + Math.abs(b.breakthroughPercent), 0) / filteredBreakthroughs.length
                        },
                        analysis: analysis,
                        scanTime: new Date().toISOString()
                    };

                    this.scanResults.push(result);

                    // 实时返回结果
                    if (resultCallback) {
                        resultCallback(result);
                    }
                }

            } catch (error) {
                console.warn(`分析股票 ${stock.code} 失败:`, error);
            }

            // 更新进度
            this.scanProgress.current++;
            this.scanProgress.percentage = Math.round(
                (this.scanProgress.current / this.scanProgress.total) * 100
            );

            if (progressCallback) {
                const elapsedTime = performance.now() - this.scanStartTime;
                const estimatedTotal = this.scanProgress.total > 0 ?
                    (elapsedTime / this.scanProgress.current) * this.scanProgress.total : 0;
                const remainingTime = estimatedTotal - elapsedTime;

                progressCallback({
                    ...this.scanProgress,
                    currentStock: stock.name,
                    foundCount: this.scanResults.length,
                    elapsedTime: Math.round(elapsedTime),
                    remainingTime: Math.round(Math.max(0, remainingTime)),
                    scanSpeed: Math.round(this.scanProgress.current / (elapsedTime / 1000))
                });
            }
        }
    }

    /**
     * 优化的股票分析方法（使用统一20日周期）
     * @param {Object} stockData - 股票数据
     * @returns {Object} 分析结果
     */
    async analyzeStockOptimized(stockData) {
        try {
            const config = this.config.indicators;

            // 使用统一配置计算布林带（20日窗口）
            const bollinger = this.indicators.bollinger.calculate(
                stockData.close,
                config.bollinger.window,
                config.bollinger.stdDev
            );

            // 使用统一配置计算KDJ（K周期=20）
            const kdj = this.indicators.kdj.calculate({
                high: stockData.high,
                low: stockData.low,
                close: stockData.close
            }, config.kdj.kPeriod, config.kdj.dPeriod, config.kdj.jPeriod);

            // 计算成交量分析（使用20日窗口）
            const volumeAnalysis = this.indicators.volume.calculateInOutDifference({
                open: stockData.open,
                high: stockData.high,
                low: stockData.low,
                close: stockData.close,
                volume: stockData.volume
            });

            // 计算成交量布林带（20日窗口）
            const volumeBollinger = this.indicators.volume.calculateVolumeBollinger(
                volumeAnalysis.positiveDiff,
                config.volume.bollingerWindow,
                config.volume.lag
            );

            return {
                bollinger,
                kdj,
                volumeAnalysis,
                volumeBollinger,
                dataLength: stockData.close.length,
                calculationConfig: {
                    unifiedPeriod: this.config.getUnifiedPeriod(),
                    bollingerWindow: config.bollinger.window,
                    kdjKPeriod: config.kdj.kPeriod,
                    volumeWindow: config.volume.bollingerWindow
                }
            };

        } catch (error) {
            console.error('优化股票分析失败:', error);
            return null;
        }
    }

    /**
     * 根据扫描条件过滤突破信号
     * @param {Array} breakthroughs - 突破信号数组
     * @returns {Array} 过滤后的突破信号
     */
    filterBreakthroughsByConditions(breakthroughs) {
        let filtered = [...breakthroughs];

        // 按信号类型过滤
        const enabledTypes = [];
        if (this.scanConditions.priceBreakthrough) enabledTypes.push('price');
        if (this.scanConditions.kdjBreakthrough) enabledTypes.push('kdj');
        if (this.scanConditions.bollingerBreakthrough) enabledTypes.push('bollinger');
        if (this.scanConditions.volumeBreakthrough) enabledTypes.push('volume');

        if (enabledTypes.length > 0) {
            filtered = filtered.filter(b => enabledTypes.includes(b.type));
        }

        // 按方向过滤
        if (this.scanConditions.upBreakthroughOnly) {
            filtered = filtered.filter(b => b.direction === 'up');
        } else if (this.scanConditions.downBreakthroughOnly) {
            filtered = filtered.filter(b => b.direction === 'down');
        }

        // 按最小强度过滤
        if (this.scanConditions.minStrength > 1) {
            filtered = filtered.filter(b => b.strength >= this.scanConditions.minStrength);
        }

        return filtered;
    }

    /**
     * 按突破强度排序扫描结果
     */
    sortResultsByStrength() {
        this.scanResults.sort((a, b) => {
            // 首先按最大信号强度排序
            if (a.summary.maxStrength !== b.summary.maxStrength) {
                return b.summary.maxStrength - a.summary.maxStrength;
            }

            // 然后按信号数量排序
            if (a.summary.totalSignals !== b.summary.totalSignals) {
                return b.summary.totalSignals - a.summary.totalSignals;
            }

            // 最后按平均突破幅度排序
            return b.summary.avgBreakthroughPercent - a.summary.avgBreakthroughPercent;
        });
    }

    /**
     * 检查扫描条件（保留兼容性）
     * @param {Object} analysis - 分析结果
     * @returns {Array} 符合的信号列表
     */
    checkConditions(analysis) {
        if (!analysis) return [];

        const signals = [];
        const { bollinger, kdj, signals: detectedSignals } = analysis;

        // 检查KDJ金叉
        if (this.scanConditions.kdjGoldenCross && detectedSignals.kdjCross.goldenCross.length > 0) {
            const recentCross = detectedSignals.kdjCross.goldenCross
                .filter(cross => cross.index > analysis.dataLength - 5);
            
            if (recentCross.length > 0) {
                signals.push({
                    type: 'KDJ金叉',
                    description: 'K线上穿D线，买入信号',
                    strength: recentCross[0].strength,
                    index: recentCross[0].index,
                    kValue: recentCross[0].kValue,
                    dValue: recentCross[0].dValue
                });
            }
        }

        // 检查KDJ死叉
        if (this.scanConditions.kdjDeathCross && detectedSignals.kdjCross.deathCross.length > 0) {
            const recentCross = detectedSignals.kdjCross.deathCross
                .filter(cross => cross.index > analysis.dataLength - 5);
            
            if (recentCross.length > 0) {
                signals.push({
                    type: 'KDJ死叉',
                    description: 'K线下穿D线，卖出信号',
                    strength: recentCross[0].strength,
                    index: recentCross[0].index,
                    kValue: recentCross[0].kValue,
                    dValue: recentCross[0].dValue
                });
            }
        }

        // 检查布林带突破
        if (this.scanConditions.bollingerBreakout) {
            const recentUpperBreakouts = detectedSignals.bollingerBreakouts.upperBreakouts
                .filter(breakout => breakout.index > analysis.dataLength - 3);
            
            if (recentUpperBreakouts.length > 0) {
                signals.push({
                    type: '布林带上轨突破',
                    description: '价格突破布林带上轨，强势信号',
                    index: recentUpperBreakouts[0].index,
                    price: recentUpperBreakouts[0].price,
                    upperBand: recentUpperBreakouts[0].upperBand
                });
            }

            const recentLowerBreakouts = detectedSignals.bollingerBreakouts.lowerBreakouts
                .filter(breakout => breakout.index > analysis.dataLength - 3);
            
            if (recentLowerBreakouts.length > 0) {
                signals.push({
                    type: '布林带下轨突破',
                    description: '价格跌破布林带下轨，超卖信号',
                    index: recentLowerBreakouts[0].index,
                    price: recentLowerBreakouts[0].price,
                    lowerBand: recentLowerBreakouts[0].lowerBand
                });
            }
        }

        // 检查成交量异常
        if (this.scanConditions.volumeAnomaly && detectedSignals.volumeBreakouts) {
            const recentVolumeBreakouts = [
                ...detectedSignals.volumeBreakouts.buySignals,
                ...detectedSignals.volumeBreakouts.sellSignals
            ].filter(signal => signal.index > analysis.dataLength - 5);

            if (recentVolumeBreakouts.length > 0) {
                signals.push({
                    type: '成交量异常',
                    description: '成交量出现异常放大，关注资金流向',
                    index: recentVolumeBreakouts[0].index,
                    value: recentVolumeBreakouts[0].value,
                    signalType: recentVolumeBreakouts[0].type
                });
            }
        }

        // 检查KDJ超卖
        if (this.scanConditions.kdjOversold && detectedSignals.kdjOverboughtOversold.oversold.length > 0) {
            const recentOversold = detectedSignals.kdjOverboughtOversold.oversold
                .filter(signal => signal.index > analysis.dataLength - 5);
            
            if (recentOversold.length > 0) {
                signals.push({
                    type: 'KDJ超卖',
                    description: 'KDJ指标显示超卖，可能反弹',
                    index: recentOversold[0].index,
                    kValue: recentOversold[0].kValue,
                    dValue: recentOversold[0].dValue
                });
            }
        }

        // 检查KDJ超买
        if (this.scanConditions.kdjOverbought && detectedSignals.kdjOverboughtOversold.overbought.length > 0) {
            const recentOverbought = detectedSignals.kdjOverboughtOversold.overbought
                .filter(signal => signal.index > analysis.dataLength - 5);
            
            if (recentOverbought.length > 0) {
                signals.push({
                    type: 'KDJ超买',
                    description: 'KDJ指标显示超买，注意回调风险',
                    index: recentOverbought[0].index,
                    kValue: recentOverbought[0].kValue,
                    dValue: recentOverbought[0].dValue
                });
            }
        }

        // 检查价格接近布林带
        if (this.scanConditions.priceNearBollinger && bollinger) {
            const lastIndex = analysis.dataLength - 1;
            const lastPrice = analysis.dataLength > 0 ? analysis.bollinger.middle[lastIndex] : 0;
            const upperBand = bollinger.upper[lastIndex];
            const lowerBand = bollinger.lower[lastIndex];
            const middle = bollinger.middle[lastIndex];

            if (lastPrice && upperBand && lowerBand && middle) {
                const upperDistance = Math.abs(lastPrice - upperBand) / upperBand;
                const lowerDistance = Math.abs(lastPrice - lowerBand) / lowerBand;

                if (upperDistance < 0.02) { // 距离上轨2%以内
                    signals.push({
                        type: '接近布林带上轨',
                        description: '价格接近布林带上轨，关注突破或回调',
                        price: lastPrice,
                        upperBand: upperBand,
                        distance: upperDistance
                    });
                }

                if (lowerDistance < 0.02) { // 距离下轨2%以内
                    signals.push({
                        type: '接近布林带下轨',
                        description: '价格接近布林带下轨，关注支撑或破位',
                        price: lastPrice,
                        lowerBand: lowerBand,
                        distance: lowerDistance
                    });
                }
            }
        }

        return signals;
    }

    /**
     * 停止扫描
     */
    stopScan() {
        this.isScanning = false;
    }

    /**
     * 设置扫描条件
     * @param {Object} conditions - 扫描条件
     */
    setScanConditions(conditions) {
        this.scanConditions = { ...this.scanConditions, ...conditions };
    }

    /**
     * 获取扫描条件
     * @returns {Object} 当前扫描条件
     */
    getScanConditions() {
        return { ...this.scanConditions };
    }

    /**
     * 获取扫描结果
     * @returns {Array} 扫描结果
     */
    getScanResults() {
        return [...this.scanResults];
    }

    /**
     * 获取扫描进度
     * @returns {Object} 扫描进度
     */
    getScanProgress() {
        return { ...this.scanProgress };
    }

    /**
     * 清除扫描结果
     */
    clearResults() {
        this.scanResults = [];
    }

    /**
     * 导出扫描结果
     * @param {string} format - 导出格式 ('json' | 'csv')
     * @returns {string} 导出数据
     */
    exportResults(format = 'json') {
        if (format === 'csv') {
            const headers = ['股票代码', '股票名称', '市场', '行业', '当前价格', '信号类型', '信号描述', '扫描时间'];
            const rows = [headers.join(',')];
            
            this.scanResults.forEach(result => {
                result.signals.forEach(signal => {
                    const row = [
                        result.code,
                        result.name,
                        result.market,
                        result.industry,
                        result.currentPrice,
                        signal.type,
                        `"${signal.description}"`,
                        result.scanTime
                    ];
                    rows.push(row.join(','));
                });
            });
            
            return rows.join('\n');
        } else {
            return JSON.stringify(this.scanResults, null, 2);
        }
    }
}

// 导出股票扫描器
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StockScanner;
} else if (typeof window !== 'undefined') {
    window.StockScanner = StockScanner;
}
