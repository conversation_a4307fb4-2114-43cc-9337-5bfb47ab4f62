/**
 * 移动突破信号检测器
 * 基于20日移动平均值检测各种技术指标的突破信号
 */

class BreakthroughDetector {
    constructor(config = null) {
        this.config = config || window.INDICATOR_CONFIG;
        this.breakthroughConfig = this.config.getBreakthroughConfig();
        this.cache = new Map();
    }

    /**
     * 检测所有指标的突破信号
     * @param {Object} stockData - 股票数据
     * @param {Object} indicators - 计算好的技术指标
     * @returns {Object} 突破信号检测结果
     */
    detectAllBreakthroughs(stockData, indicators) {
        const results = {
            stock: {
                code: stockData.code,
                name: stockData.name,
                currentPrice: stockData.close[stockData.close.length - 1]
            },
            breakthroughs: [],
            summary: {
                totalSignals: 0,
                upBreakthroughs: 0,
                downBreakthroughs: 0,
                strongSignals: 0
            }
        };

        try {
            // 检测价格突破
            const priceBreakthroughs = this.detectPriceBreakthroughs(stockData.close);
            results.breakthroughs.push(...priceBreakthroughs);

            // 检测KDJ突破
            if (indicators.kdj) {
                const kdjBreakthroughs = this.detectKDJBreakthroughs(indicators.kdj);
                results.breakthroughs.push(...kdjBreakthroughs);
            }

            // 检测布林带突破
            if (indicators.bollinger) {
                const bollingerBreakthroughs = this.detectBollingerBreakthroughs(
                    stockData.close, indicators.bollinger
                );
                results.breakthroughs.push(...bollingerBreakthroughs);
            }

            // 检测成交量突破
            if (stockData.volume) {
                const volumeBreakthroughs = this.detectVolumeBreakthroughs(stockData.volume);
                results.breakthroughs.push(...volumeBreakthroughs);
            }

            // 计算汇总信息
            this.calculateSummary(results);

            return results;

        } catch (error) {
            console.error('突破信号检测失败:', error);
            return results;
        }
    }

    /**
     * 检测价格突破信号
     * @param {Array} prices - 价格序列
     * @returns {Array} 价格突破信号
     */
    detectPriceBreakthroughs(prices) {
        const breakthroughs = [];
        const maWindow = this.breakthroughConfig.maWindow;
        const threshold = this.breakthroughConfig.thresholds.price;

        // 计算20日移动平均
        const ma20 = IndicatorUtils.calculateSMA(prices, maWindow);
        
        // 检测最近几个交易日的突破
        const checkPeriod = 3; // 检查最近3个交易日
        const startIndex = Math.max(0, prices.length - checkPeriod);

        for (let i = startIndex; i < prices.length; i++) {
            if (ma20[i] === null || ma20[i] === undefined) continue;

            const currentPrice = prices[i];
            const ma = ma20[i];
            const prevMA = i > 0 ? ma20[i - 1] : ma;
            
            // 计算突破幅度
            const breakthroughRatio = (currentPrice - ma) / ma;
            const absRatio = Math.abs(breakthroughRatio);

            // 检查是否满足突破阈值
            if (absRatio >= threshold.percentage && Math.abs(currentPrice - ma) >= threshold.minValue) {
                const isUpBreakthrough = breakthroughRatio > 0;
                const strength = this.config.calculateSignalStrength(breakthroughRatio, 'price');

                breakthroughs.push({
                    type: 'price',
                    direction: isUpBreakthrough ? 'up' : 'down',
                    description: isUpBreakthrough ? '价格向上突破20日均线' : '价格向下突破20日均线',
                    currentValue: currentPrice,
                    baselineValue: ma,
                    breakthroughRatio: breakthroughRatio,
                    breakthroughPercent: breakthroughRatio * 100,
                    strength: strength,
                    strengthDesc: this.config.getStrengthDescription(strength),
                    index: i,
                    timestamp: new Date().toISOString()
                });
            }
        }

        return breakthroughs;
    }

    /**
     * 检测KDJ突破信号
     * @param {Object} kdjData - KDJ指标数据
     * @returns {Array} KDJ突破信号
     */
    detectKDJBreakthroughs(kdjData) {
        const breakthroughs = [];
        const maWindow = this.breakthroughConfig.maWindow;
        const threshold = this.breakthroughConfig.thresholds.kdj;

        // 计算KDJ各线的20日移动平均
        const kMA = IndicatorUtils.calculateSMA(kdjData.K.filter(v => v !== null), maWindow);
        const dMA = IndicatorUtils.calculateSMA(kdjData.D.filter(v => v !== null), maWindow);
        const jMA = IndicatorUtils.calculateSMA(kdjData.J.filter(v => v !== null), maWindow);

        // 检测K线突破
        const kBreakthroughs = this.detectSingleLineBreakthrough(
            kdjData.K, kMA, 'KDJ-K', threshold.points, threshold.minValue
        );
        breakthroughs.push(...kBreakthroughs);

        // 检测D线突破
        const dBreakthroughs = this.detectSingleLineBreakthrough(
            kdjData.D, dMA, 'KDJ-D', threshold.points, threshold.minValue
        );
        breakthroughs.push(...dBreakthroughs);

        // 检测J线突破
        const jBreakthroughs = this.detectSingleLineBreakthrough(
            kdjData.J, jMA, 'KDJ-J', threshold.points, threshold.minValue
        );
        breakthroughs.push(...jBreakthroughs);

        return breakthroughs;
    }

    /**
     * 检测单条线的突破信号
     * @param {Array} values - 数值序列
     * @param {Array} ma - 移动平均序列
     * @param {string} lineName - 线名称
     * @param {number} pointThreshold - 点位阈值
     * @param {number} minThreshold - 最小阈值
     * @returns {Array} 突破信号
     */
    detectSingleLineBreakthrough(values, ma, lineName, pointThreshold, minThreshold) {
        const breakthroughs = [];
        const checkPeriod = 3;
        const startIndex = Math.max(0, values.length - checkPeriod);

        for (let i = startIndex; i < values.length; i++) {
            if (values[i] === null || ma[i] === null) continue;

            const currentValue = values[i];
            const maValue = ma[i];
            const difference = currentValue - maValue;
            const absDifference = Math.abs(difference);

            // 检查是否满足突破阈值
            if (absDifference >= pointThreshold && absDifference >= minThreshold) {
                const isUpBreakthrough = difference > 0;
                const breakthroughRatio = difference / (maValue || 1);
                const strength = this.config.calculateSignalStrength(breakthroughRatio, 'kdj');

                breakthroughs.push({
                    type: 'kdj',
                    subType: lineName,
                    direction: isUpBreakthrough ? 'up' : 'down',
                    description: `${lineName}${isUpBreakthrough ? '向上' : '向下'}突破20日均线`,
                    currentValue: currentValue,
                    baselineValue: maValue,
                    breakthroughRatio: breakthroughRatio,
                    breakthroughPercent: breakthroughRatio * 100,
                    pointDifference: difference,
                    strength: strength,
                    strengthDesc: this.config.getStrengthDescription(strength),
                    index: i,
                    timestamp: new Date().toISOString()
                });
            }
        }

        return breakthroughs;
    }

    /**
     * 检测布林带突破信号
     * @param {Array} prices - 价格序列
     * @param {Object} bollingerData - 布林带数据
     * @returns {Array} 布林带突破信号
     */
    detectBollingerBreakthroughs(prices, bollingerData) {
        const breakthroughs = [];
        const threshold = this.breakthroughConfig.thresholds.bollinger;
        const checkPeriod = 3;
        const startIndex = Math.max(0, prices.length - checkPeriod);

        for (let i = startIndex; i < prices.length; i++) {
            const price = prices[i];
            const upper = bollingerData.upper[i];
            const lower = bollingerData.lower[i];
            const middle = bollingerData.middle[i];

            if (price === null || upper === null || lower === null || middle === null) continue;

            // 检测上轨突破
            if (price > upper) {
                const breakthroughRatio = (price - upper) / upper;
                if (breakthroughRatio >= threshold.percentage) {
                    const strength = this.config.calculateSignalStrength(breakthroughRatio, 'bollinger');
                    
                    breakthroughs.push({
                        type: 'bollinger',
                        subType: 'upper',
                        direction: 'up',
                        description: '价格突破布林带上轨',
                        currentValue: price,
                        baselineValue: upper,
                        breakthroughRatio: breakthroughRatio,
                        breakthroughPercent: breakthroughRatio * 100,
                        strength: strength,
                        strengthDesc: this.config.getStrengthDescription(strength),
                        index: i,
                        timestamp: new Date().toISOString()
                    });
                }
            }

            // 检测下轨突破
            if (price < lower) {
                const breakthroughRatio = (lower - price) / lower;
                if (breakthroughRatio >= threshold.percentage) {
                    const strength = this.config.calculateSignalStrength(breakthroughRatio, 'bollinger');
                    
                    breakthroughs.push({
                        type: 'bollinger',
                        subType: 'lower',
                        direction: 'down',
                        description: '价格跌破布林带下轨',
                        currentValue: price,
                        baselineValue: lower,
                        breakthroughRatio: breakthroughRatio,
                        breakthroughPercent: breakthroughRatio * 100,
                        strength: strength,
                        strengthDesc: this.config.getStrengthDescription(strength),
                        index: i,
                        timestamp: new Date().toISOString()
                    });
                }
            }
        }

        return breakthroughs;
    }

    /**
     * 检测成交量突破信号
     * @param {Array} volumes - 成交量序列
     * @returns {Array} 成交量突破信号
     */
    detectVolumeBreakthroughs(volumes) {
        const breakthroughs = [];
        const maWindow = this.breakthroughConfig.maWindow;
        const threshold = this.breakthroughConfig.thresholds.volume;

        // 计算20日成交量移动平均
        const volumeMA = IndicatorUtils.calculateSMA(volumes, maWindow);
        
        const checkPeriod = 3;
        const startIndex = Math.max(0, volumes.length - checkPeriod);

        for (let i = startIndex; i < volumes.length; i++) {
            if (volumes[i] === null || volumeMA[i] === null) continue;

            const currentVolume = volumes[i];
            const ma = volumeMA[i];
            const breakthroughRatio = (currentVolume - ma) / ma;
            const absRatio = Math.abs(breakthroughRatio);

            // 检查是否满足突破阈值
            if (absRatio >= threshold.percentage && absRatio >= threshold.minValue) {
                const isUpBreakthrough = breakthroughRatio > 0;
                const strength = this.config.calculateSignalStrength(breakthroughRatio, 'volume');

                breakthroughs.push({
                    type: 'volume',
                    direction: isUpBreakthrough ? 'up' : 'down',
                    description: isUpBreakthrough ? '成交量放大突破20日均量' : '成交量萎缩跌破20日均量',
                    currentValue: currentVolume,
                    baselineValue: ma,
                    breakthroughRatio: breakthroughRatio,
                    breakthroughPercent: breakthroughRatio * 100,
                    strength: strength,
                    strengthDesc: this.config.getStrengthDescription(strength),
                    index: i,
                    timestamp: new Date().toISOString()
                });
            }
        }

        return breakthroughs;
    }

    /**
     * 计算突破信号汇总信息
     * @param {Object} results - 检测结果
     */
    calculateSummary(results) {
        const summary = results.summary;
        const breakthroughs = results.breakthroughs;

        summary.totalSignals = breakthroughs.length;
        summary.upBreakthroughs = breakthroughs.filter(b => b.direction === 'up').length;
        summary.downBreakthroughs = breakthroughs.filter(b => b.direction === 'down').length;
        summary.strongSignals = breakthroughs.filter(b => b.strength === this.breakthroughConfig.strengthLevels.strong).length;

        // 按强度排序
        breakthroughs.sort((a, b) => {
            if (a.strength !== b.strength) {
                return b.strength - a.strength; // 强度高的在前
            }
            return Math.abs(b.breakthroughPercent) - Math.abs(a.breakthroughPercent); // 突破幅度大的在前
        });
    }

    /**
     * 过滤突破信号
     * @param {Array} breakthroughs - 突破信号数组
     * @param {Object} filters - 过滤条件
     * @returns {Array} 过滤后的突破信号
     */
    filterBreakthroughs(breakthroughs, filters = {}) {
        let filtered = [...breakthroughs];

        // 按类型过滤
        if (filters.types && filters.types.length > 0) {
            filtered = filtered.filter(b => filters.types.includes(b.type));
        }

        // 按方向过滤
        if (filters.direction) {
            filtered = filtered.filter(b => b.direction === filters.direction);
        }

        // 按强度过滤
        if (filters.minStrength) {
            filtered = filtered.filter(b => b.strength >= filters.minStrength);
        }

        // 按突破幅度过滤
        if (filters.minBreakthroughPercent) {
            filtered = filtered.filter(b => Math.abs(b.breakthroughPercent) >= filters.minBreakthroughPercent);
        }

        return filtered;
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.cache.clear();
    }
}

// 导出突破检测器
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BreakthroughDetector;
} else if (typeof window !== 'undefined') {
    window.BreakthroughDetector = BreakthroughDetector;
}
