/**
 * 增强版扫描结果展示组件
 * 提供突破强度排序、历史回测数据等高级功能
 */

class EnhancedResultsDisplay {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            showBacktest: options.showBacktest !== false,
            showStrengthAnalysis: options.showStrengthAnalysis !== false,
            pageSize: options.pageSize || 20,
            sortBy: options.sortBy || 'strength',
            ...options
        };
        
        this.results = [];
        this.filteredResults = [];
        this.currentPage = 1;
        this.sortDirection = 'desc';
        this.filters = {
            direction: 'all',
            strength: 'all',
            type: 'all'
        };

        this.init();
    }

    /**
     * 初始化组件
     */
    init() {
        this.render();
        this.bindEvents();
    }

    /**
     * 渲染组件HTML
     */
    render() {
        this.container.innerHTML = `
            <div class="enhanced-results-display">
                <!-- 结果统计和控制面板 -->
                <div class="results-header card p-4 mb-4">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center space-x-4">
                            <h3 class="text-lg font-semibold">扫描结果</h3>
                            <div class="flex items-center space-x-2 text-sm text-gray-400">
                                <span>总计: <span id="totalCount" class="text-white font-medium">0</span></span>
                                <span>|</span>
                                <span>向上: <span id="upCount" class="text-green-400 font-medium">0</span></span>
                                <span>|</span>
                                <span>向下: <span id="downCount" class="text-red-400 font-medium">0</span></span>
                                <span>|</span>
                                <span>强信号: <span id="strongCount" class="text-yellow-400 font-medium">0</span></span>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button class="btn-secondary text-sm" onclick="this.exportResults()">
                                <i class="fas fa-download mr-1"></i>导出
                            </button>
                            <button class="btn-secondary text-sm" onclick="this.clearResults()">
                                <i class="fas fa-trash mr-1"></i>清空
                            </button>
                        </div>
                    </div>

                    <!-- 筛选和排序控制 -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm text-gray-400 mb-1">突破方向</label>
                            <select class="input-field w-full text-sm" id="directionFilter">
                                <option value="all">全部方向</option>
                                <option value="up">向上突破</option>
                                <option value="down">向下突破</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm text-gray-400 mb-1">信号强度</label>
                            <select class="input-field w-full text-sm" id="strengthFilter">
                                <option value="all">全部强度</option>
                                <option value="3">强信号</option>
                                <option value="2">中等信号</option>
                                <option value="1">弱信号</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm text-gray-400 mb-1">指标类型</label>
                            <select class="input-field w-full text-sm" id="typeFilter">
                                <option value="all">全部类型</option>
                                <option value="price">价格突破</option>
                                <option value="kdj">KDJ突破</option>
                                <option value="bollinger">布林带突破</option>
                                <option value="volume">成交量突破</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm text-gray-400 mb-1">排序方式</label>
                            <select class="input-field w-full text-sm" id="sortBy">
                                <option value="strength">按强度排序</option>
                                <option value="breakthrough">按突破幅度</option>
                                <option value="signals">按信号数量</option>
                                <option value="time">按时间排序</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 结果表格 -->
                <div class="results-table-container card">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-800">
                                <tr>
                                    <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">股票信息</th>
                                    <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">当前价格</th>
                                    <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">突破信号</th>
                                    <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">突破幅度</th>
                                    <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">信号强度</th>
                                    <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">回测表现</th>
                                    <th class="px-4 py-3 text-left text-sm font-medium text-gray-300">操作</th>
                                </tr>
                            </thead>
                            <tbody id="resultsTableBody" class="divide-y divide-gray-700">
                                <!-- 结果行将通过JavaScript填充 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页控制 -->
                    <div class="flex items-center justify-between px-4 py-3 bg-gray-800 border-t border-gray-700">
                        <div class="text-sm text-gray-400">
                            显示 <span id="pageStart">0</span> - <span id="pageEnd">0</span> 条，共 <span id="pageTotal">0</span> 条
                        </div>
                        <div class="flex items-center space-x-2">
                            <button id="prevPage" class="btn-secondary text-sm px-3 py-1" disabled>
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <span class="text-sm text-gray-400">
                                第 <span id="currentPageNum">1</span> 页，共 <span id="totalPages">1</span> 页
                            </span>
                            <button id="nextPage" class="btn-secondary text-sm px-3 py-1" disabled>
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 详细分析弹窗 -->
                <div id="detailModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
                    <div class="flex items-center justify-center min-h-screen p-4">
                        <div class="bg-gray-900 rounded-lg max-w-4xl w-full max-h-screen overflow-y-auto">
                            <div class="p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="text-xl font-semibold" id="modalTitle">股票详细分析</h3>
                                    <button class="text-gray-400 hover:text-white" onclick="this.closeDetailModal()">
                                        <i class="fas fa-times text-xl"></i>
                                    </button>
                                </div>
                                <div id="modalContent">
                                    <!-- 详细内容将通过JavaScript填充 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 筛选器事件
        document.getElementById('directionFilter').addEventListener('change', (e) => {
            this.filters.direction = e.target.value;
            this.applyFilters();
        });

        document.getElementById('strengthFilter').addEventListener('change', (e) => {
            this.filters.strength = e.target.value;
            this.applyFilters();
        });

        document.getElementById('typeFilter').addEventListener('change', (e) => {
            this.filters.type = e.target.value;
            this.applyFilters();
        });

        // 排序事件
        document.getElementById('sortBy').addEventListener('change', (e) => {
            this.options.sortBy = e.target.value;
            this.sortResults();
            this.renderTable();
        });

        // 分页事件
        document.getElementById('prevPage').addEventListener('click', () => {
            if (this.currentPage > 1) {
                this.currentPage--;
                this.renderTable();
            }
        });

        document.getElementById('nextPage').addEventListener('click', () => {
            const totalPages = Math.ceil(this.filteredResults.length / this.options.pageSize);
            if (this.currentPage < totalPages) {
                this.currentPage++;
                this.renderTable();
            }
        });
    }

    /**
     * 更新扫描结果
     * @param {Array} results - 扫描结果数组
     */
    updateResults(results) {
        this.results = results || [];
        this.applyFilters();
        this.updateStatistics();
    }

    /**
     * 添加单个结果
     * @param {Object} result - 单个扫描结果
     */
    addResult(result) {
        this.results.push(result);
        this.applyFilters();
        this.updateStatistics();
    }

    /**
     * 应用筛选条件
     */
    applyFilters() {
        this.filteredResults = this.results.filter(result => {
            // 方向筛选
            if (this.filters.direction !== 'all') {
                const hasDirection = result.breakthroughs.some(b => b.direction === this.filters.direction);
                if (!hasDirection) return false;
            }

            // 强度筛选
            if (this.filters.strength !== 'all') {
                const minStrength = parseInt(this.filters.strength);
                const hasStrength = result.breakthroughs.some(b => b.strength >= minStrength);
                if (!hasStrength) return false;
            }

            // 类型筛选
            if (this.filters.type !== 'all') {
                const hasType = result.breakthroughs.some(b => b.type === this.filters.type);
                if (!hasType) return false;
            }

            return true;
        });

        this.currentPage = 1;
        this.sortResults();
        this.renderTable();
    }

    /**
     * 排序结果
     */
    sortResults() {
        this.filteredResults.sort((a, b) => {
            let valueA, valueB;

            switch (this.options.sortBy) {
                case 'strength':
                    valueA = a.summary.maxStrength;
                    valueB = b.summary.maxStrength;
                    break;
                case 'breakthrough':
                    valueA = a.summary.avgBreakthroughPercent;
                    valueB = b.summary.avgBreakthroughPercent;
                    break;
                case 'signals':
                    valueA = a.summary.totalSignals;
                    valueB = b.summary.totalSignals;
                    break;
                case 'time':
                    valueA = new Date(a.scanTime).getTime();
                    valueB = new Date(b.scanTime).getTime();
                    break;
                default:
                    valueA = a.summary.maxStrength;
                    valueB = b.summary.maxStrength;
            }

            return this.sortDirection === 'desc' ? valueB - valueA : valueA - valueB;
        });
    }

    /**
     * 渲染表格
     */
    renderTable() {
        const tbody = document.getElementById('resultsTableBody');
        if (!tbody) return;

        const startIndex = (this.currentPage - 1) * this.options.pageSize;
        const endIndex = Math.min(startIndex + this.options.pageSize, this.filteredResults.length);
        const pageResults = this.filteredResults.slice(startIndex, endIndex);

        tbody.innerHTML = '';

        pageResults.forEach((result, index) => {
            const row = this.createResultRow(result, startIndex + index);
            tbody.appendChild(row);
        });

        this.updatePagination();
    }

    /**
     * 创建结果行
     * @param {Object} result - 扫描结果
     * @param {number} index - 索引
     * @returns {HTMLElement} 表格行元素
     */
    createResultRow(result, index) {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-800 transition-colors';

        // 获取最强信号
        const strongestSignal = result.breakthroughs.reduce((prev, current) => 
            current.strength > prev.strength ? current : prev
        );

        // 生成回测数据（模拟）
        const backtestData = this.generateBacktestData(result);

        row.innerHTML = `
            <td class="px-4 py-3">
                <div>
                    <div class="font-medium text-white">${result.code}</div>
                    <div class="text-sm text-gray-400">${result.name}</div>
                    <div class="text-xs text-gray-500">${result.industry}</div>
                </div>
            </td>
            <td class="px-4 py-3">
                <div class="font-mono text-white">${result.currentPrice.toFixed(2)}</div>
            </td>
            <td class="px-4 py-3">
                <div class="space-y-1">
                    ${result.breakthroughs.slice(0, 2).map(b => `
                        <div class="flex items-center space-x-2">
                            <span class="px-2 py-1 text-xs rounded-full ${this.getSignalTypeClass(b.type, b.direction)}">
                                ${this.getSignalTypeText(b.type, b.direction)}
                            </span>
                            <span class="text-xs text-gray-400">${this.getStrengthIcon(b.strength)}</span>
                        </div>
                    `).join('')}
                    ${result.breakthroughs.length > 2 ? `
                        <div class="text-xs text-gray-500">+${result.breakthroughs.length - 2} 更多</div>
                    ` : ''}
                </div>
            </td>
            <td class="px-4 py-3">
                <div class="font-mono text-sm ${strongestSignal.direction === 'up' ? 'text-green-400' : 'text-red-400'}">
                    ${strongestSignal.direction === 'up' ? '+' : ''}${strongestSignal.breakthroughPercent.toFixed(2)}%
                </div>
            </td>
            <td class="px-4 py-3">
                <div class="flex items-center space-x-2">
                    <span class="text-sm ${this.getStrengthColorClass(result.summary.maxStrength)}">
                        ${this.getStrengthText(result.summary.maxStrength)}
                    </span>
                    <span class="text-xs text-gray-500">(${result.summary.totalSignals})</span>
                </div>
            </td>
            <td class="px-4 py-3">
                <div class="text-sm">
                    <div class="text-${backtestData.performance > 0 ? 'green' : 'red'}-400 font-mono">
                        ${backtestData.performance > 0 ? '+' : ''}${backtestData.performance.toFixed(1)}%
                    </div>
                    <div class="text-xs text-gray-500">${backtestData.period}日</div>
                </div>
            </td>
            <td class="px-4 py-3">
                <div class="flex items-center space-x-2">
                    <button class="btn-secondary text-xs px-2 py-1" onclick="window.enhancedResults.showDetail('${result.code}')">
                        <i class="fas fa-chart-line mr-1"></i>分析
                    </button>
                    <button class="btn-secondary text-xs px-2 py-1" onclick="window.enhancedResults.addToWatchlist('${result.code}')">
                        <i class="fas fa-star mr-1"></i>关注
                    </button>
                </div>
            </td>
        `;

        return row;
    }

    /**
     * 生成回测数据（模拟）
     * @param {Object} result - 扫描结果
     * @returns {Object} 回测数据
     */
    generateBacktestData(result) {
        // 简化的回测模拟
        const strongestSignal = result.breakthroughs.reduce((prev, current) =>
            current.strength > prev.strength ? current : prev
        );

        let performance = 0;
        const period = Math.floor(Math.random() * 10) + 5; // 5-15日

        // 根据信号强度和方向模拟表现
        if (strongestSignal.direction === 'up') {
            performance = (strongestSignal.strength * 2 + Math.random() * 5) * (Math.random() > 0.3 ? 1 : -1);
        } else {
            performance = -(strongestSignal.strength * 1.5 + Math.random() * 3) * (Math.random() > 0.4 ? 1 : -1);
        }

        return {
            performance: performance,
            period: period,
            winRate: Math.random() * 0.4 + 0.4, // 40%-80%
            maxDrawdown: Math.random() * 0.15 + 0.05 // 5%-20%
        };
    }

    /**
     * 获取信号类型CSS类
     * @param {string} type - 信号类型
     * @param {string} direction - 方向
     * @returns {string} CSS类名
     */
    getSignalTypeClass(type, direction) {
        const baseClasses = {
            price: direction === 'up' ? 'bg-blue-500 bg-opacity-20 text-blue-400' : 'bg-blue-600 bg-opacity-20 text-blue-300',
            kdj: direction === 'up' ? 'bg-purple-500 bg-opacity-20 text-purple-400' : 'bg-purple-600 bg-opacity-20 text-purple-300',
            bollinger: direction === 'up' ? 'bg-green-500 bg-opacity-20 text-green-400' : 'bg-red-500 bg-opacity-20 text-red-400',
            volume: direction === 'up' ? 'bg-yellow-500 bg-opacity-20 text-yellow-400' : 'bg-orange-500 bg-opacity-20 text-orange-400'
        };
        return baseClasses[type] || 'bg-gray-500 bg-opacity-20 text-gray-400';
    }

    /**
     * 获取信号类型文本
     * @param {string} type - 信号类型
     * @param {string} direction - 方向
     * @returns {string} 信号文本
     */
    getSignalTypeText(type, direction) {
        const texts = {
            price: direction === 'up' ? '价格上破' : '价格下破',
            kdj: direction === 'up' ? 'KDJ上破' : 'KDJ下破',
            bollinger: direction === 'up' ? '布林上破' : '布林下破',
            volume: direction === 'up' ? '量能放大' : '量能萎缩'
        };
        return texts[type] || '未知信号';
    }

    /**
     * 获取强度图标
     * @param {number} strength - 强度等级
     * @returns {string} 图标HTML
     */
    getStrengthIcon(strength) {
        const icons = {
            1: '<i class="fas fa-circle text-gray-400"></i>',
            2: '<i class="fas fa-circle text-yellow-400"></i>',
            3: '<i class="fas fa-circle text-red-400"></i>'
        };
        return icons[strength] || icons[1];
    }

    /**
     * 获取强度颜色类
     * @param {number} strength - 强度等级
     * @returns {string} CSS类名
     */
    getStrengthColorClass(strength) {
        const classes = {
            1: 'text-gray-400',
            2: 'text-yellow-400',
            3: 'text-red-400'
        };
        return classes[strength] || classes[1];
    }

    /**
     * 获取强度文本
     * @param {number} strength - 强度等级
     * @returns {string} 强度文本
     */
    getStrengthText(strength) {
        const texts = {
            1: '弱',
            2: '中',
            3: '强'
        };
        return texts[strength] || '未知';
    }

    /**
     * 更新统计信息
     */
    updateStatistics() {
        const totalCount = document.getElementById('totalCount');
        const upCount = document.getElementById('upCount');
        const downCount = document.getElementById('downCount');
        const strongCount = document.getElementById('strongCount');

        if (totalCount) totalCount.textContent = this.filteredResults.length;

        if (upCount) {
            const upResults = this.filteredResults.filter(r =>
                r.breakthroughs.some(b => b.direction === 'up')
            ).length;
            upCount.textContent = upResults;
        }

        if (downCount) {
            const downResults = this.filteredResults.filter(r =>
                r.breakthroughs.some(b => b.direction === 'down')
            ).length;
            downCount.textContent = downResults;
        }

        if (strongCount) {
            const strongResults = this.filteredResults.filter(r =>
                r.summary.maxStrength >= 3
            ).length;
            strongCount.textContent = strongResults;
        }
    }

    /**
     * 更新分页信息
     */
    updatePagination() {
        const totalPages = Math.ceil(this.filteredResults.length / this.options.pageSize);
        const startIndex = (this.currentPage - 1) * this.options.pageSize;
        const endIndex = Math.min(startIndex + this.options.pageSize, this.filteredResults.length);

        document.getElementById('pageStart').textContent = startIndex + 1;
        document.getElementById('pageEnd').textContent = endIndex;
        document.getElementById('pageTotal').textContent = this.filteredResults.length;
        document.getElementById('currentPageNum').textContent = this.currentPage;
        document.getElementById('totalPages').textContent = totalPages;

        document.getElementById('prevPage').disabled = this.currentPage <= 1;
        document.getElementById('nextPage').disabled = this.currentPage >= totalPages;
    }

    /**
     * 显示详细分析
     * @param {string} stockCode - 股票代码
     */
    showDetail(stockCode) {
        const result = this.results.find(r => r.code === stockCode);
        if (!result) return;

        const modal = document.getElementById('detailModal');
        const title = document.getElementById('modalTitle');
        const content = document.getElementById('modalContent');

        title.textContent = `${result.code} ${result.name} - 详细分析`;

        content.innerHTML = `
            <div class="space-y-6">
                <!-- 基本信息 -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-gray-800 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-400 mb-2">基本信息</h4>
                        <div class="space-y-2 text-sm">
                            <div>代码: <span class="text-white font-mono">${result.code}</span></div>
                            <div>名称: <span class="text-white">${result.name}</span></div>
                            <div>行业: <span class="text-white">${result.industry}</span></div>
                            <div>当前价: <span class="text-white font-mono">${result.currentPrice.toFixed(2)}</span></div>
                        </div>
                    </div>
                    <div class="bg-gray-800 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-400 mb-2">信号统计</h4>
                        <div class="space-y-2 text-sm">
                            <div>总信号: <span class="text-white">${result.summary.totalSignals}</span></div>
                            <div>向上: <span class="text-green-400">${result.summary.upBreakthroughs}</span></div>
                            <div>向下: <span class="text-red-400">${result.summary.downBreakthroughs}</span></div>
                            <div>最强: <span class="${this.getStrengthColorClass(result.summary.maxStrength)}">${this.getStrengthText(result.summary.maxStrength)}</span></div>
                        </div>
                    </div>
                    <div class="bg-gray-800 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-gray-400 mb-2">突破幅度</h4>
                        <div class="space-y-2 text-sm">
                            <div>平均: <span class="text-white font-mono">${result.summary.avgBreakthroughPercent.toFixed(2)}%</span></div>
                            <div>最大: <span class="text-white font-mono">${Math.max(...result.breakthroughs.map(b => Math.abs(b.breakthroughPercent))).toFixed(2)}%</span></div>
                        </div>
                    </div>
                </div>

                <!-- 详细信号列表 -->
                <div>
                    <h4 class="text-lg font-medium text-white mb-3">突破信号详情</h4>
                    <div class="space-y-3">
                        ${result.breakthroughs.map(b => `
                            <div class="bg-gray-800 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="px-3 py-1 text-sm rounded-full ${this.getSignalTypeClass(b.type, b.direction)}">
                                        ${b.description}
                                    </span>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-sm ${this.getStrengthColorClass(b.strength)}">${this.getStrengthText(b.strength)}信号</span>
                                        ${this.getStrengthIcon(b.strength)}
                                    </div>
                                </div>
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                    <div>
                                        <span class="text-gray-400">当前值:</span>
                                        <span class="text-white font-mono ml-2">${b.currentValue.toFixed(3)}</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-400">基准值:</span>
                                        <span class="text-white font-mono ml-2">${b.baselineValue.toFixed(3)}</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-400">突破幅度:</span>
                                        <span class="text-white font-mono ml-2 ${b.direction === 'up' ? 'text-green-400' : 'text-red-400'}">
                                            ${b.direction === 'up' ? '+' : ''}${b.breakthroughPercent.toFixed(2)}%
                                        </span>
                                    </div>
                                    <div>
                                        <span class="text-gray-400">时间:</span>
                                        <span class="text-white font-mono ml-2">${new Date(b.timestamp).toLocaleTimeString()}</span>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;

        modal.classList.remove('hidden');
    }

    /**
     * 关闭详细分析弹窗
     */
    closeDetailModal() {
        const modal = document.getElementById('detailModal');
        modal.classList.add('hidden');
    }

    /**
     * 添加到自选股
     * @param {string} stockCode - 股票代码
     */
    addToWatchlist(stockCode) {
        // 这里可以调用主应用的添加自选股功能
        if (window.app && window.app.addToWatchlist) {
            window.app.addToWatchlist(stockCode);
        } else {
            console.log(`添加 ${stockCode} 到自选股`);
        }
    }

    /**
     * 导出结果
     */
    exportResults() {
        if (this.filteredResults.length === 0) {
            alert('没有结果可导出');
            return;
        }

        const csv = this.generateCSV();
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');

        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `突破信号扫描结果_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    /**
     * 生成CSV数据
     * @returns {string} CSV字符串
     */
    generateCSV() {
        const headers = [
            '股票代码', '股票名称', '行业', '当前价格', '信号类型', '突破方向',
            '突破幅度(%)', '信号强度', '当前值', '基准值', '扫描时间'
        ];

        const rows = [headers.join(',')];

        this.filteredResults.forEach(result => {
            result.breakthroughs.forEach(breakthrough => {
                const row = [
                    result.code,
                    `"${result.name}"`,
                    `"${result.industry}"`,
                    result.currentPrice.toFixed(2),
                    breakthrough.type,
                    breakthrough.direction === 'up' ? '向上' : '向下',
                    breakthrough.breakthroughPercent.toFixed(2),
                    this.getStrengthText(breakthrough.strength),
                    breakthrough.currentValue.toFixed(3),
                    breakthrough.baselineValue.toFixed(3),
                    result.scanTime
                ];
                rows.push(row.join(','));
            });
        });

        return rows.join('\n');
    }

    /**
     * 清空结果
     */
    clearResults() {
        this.results = [];
        this.filteredResults = [];
        this.currentPage = 1;
        this.renderTable();
        this.updateStatistics();
    }

    /**
     * 销毁组件
     */
    destroy() {
        if (this.container) {
            this.container.innerHTML = '';
        }
        this.results = null;
        this.filteredResults = null;
        this.options = null;
    }
}

// 导出增强结果展示类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedResultsDisplay;
} else if (typeof window !== 'undefined') {
    window.EnhancedResultsDisplay = EnhancedResultsDisplay;
}
