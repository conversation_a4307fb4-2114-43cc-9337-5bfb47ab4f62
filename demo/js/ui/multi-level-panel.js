/**
 * 多层级筛选面板
 * 提供多层级筛选的用户界面和交互功能
 */

class MultiLevelPanel {
    constructor(containerId) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.scanner = null;
        this.currentResults = null;

        // 预设相关属性
        this.currentPreset = null;
        this.originalPresetLevels = null;

        // 编辑相关属性
        this.currentEditingLevelIndex = null;

        // 事件回调
        this.onScanStart = null;
        this.onScanComplete = null;
        this.onLevelComplete = null;

        this.init();
    }

    /**
     * 初始化面板
     */
    init() {
        if (!this.container) {
            console.error(`找不到容器: ${this.containerId}`);
            return;
        }

        this.createUI();
        this.bindEvents();
    }

    /**
     * 创建用户界面
     */
    createUI() {
        this.container.innerHTML = `
            <div class="multi-level-panel">
                <!-- 筛选配置区域 -->
                <div class="config-section mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-200">
                            <i class="fas fa-filter mr-2"></i>多层级筛选配置
                        </h3>
                        <div class="flex space-x-2">
                            <select id="presetSelector" class="bg-gray-700 text-white px-3 py-1 rounded text-sm">
                                <option value="">选择预设方案</option>
                                <option value="conservative">保守型</option>
                                <option value="balanced">平衡型</option>
                                <option value="aggressive">激进型</option>
                            </select>
                            <button id="advancedConfigBtn" class="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded text-sm">
                                <i class="fas fa-cogs mr-1"></i>高级配置
                            </button>
                        </div>
                    </div>

                    <!-- 预设方案概览 -->
                    <div id="presetOverview" class="bg-gray-800 rounded-lg p-4 mb-4" style="display: none;">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-md font-medium text-gray-200">
                                <span id="presetName">当前方案</span>
                                <span id="presetDescription" class="text-sm text-gray-400 ml-2"></span>
                            </h4>
                            <button id="resetPresetBtn" class="text-gray-400 hover:text-white text-sm">
                                <i class="fas fa-undo mr-1"></i>重置
                            </button>
                        </div>
                        <div id="presetLevelsOverview" class="space-y-2">
                            <!-- 动态生成预设层级概览 -->
                        </div>
                    </div>

                    <!-- 筛选层级管理区域 -->
                    <div id="levelsManagementSection" class="bg-gray-800 rounded-lg p-4 mb-4" style="display: none;">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-md font-medium text-gray-200">
                                <i class="fas fa-layer-group mr-2"></i>筛选层级
                            </h4>
                            <button id="addLevelBtn" class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm">
                                <i class="fas fa-plus mr-1"></i>添加层级
                            </button>
                        </div>
                        <div id="levelsManagementList" class="space-y-2">
                            <!-- 动态生成层级管理列表 -->
                        </div>
                    </div>
                </div>

                <!-- 控制按钮区域 -->
                <div class="control-section mb-6">
                    <div class="flex items-center justify-between">
                        <div class="flex space-x-3">
                            <button id="multiLevelStartBtn" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded">
                                <i class="fas fa-play mr-2"></i>开始筛选
                            </button>
                            <button id="multiLevelStopBtn" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded" disabled>
                                <i class="fas fa-stop mr-2"></i>停止筛选
                            </button>
                            <button id="multiLevelClearBtn" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded">
                                <i class="fas fa-trash mr-2"></i>清除结果
                            </button>
                        </div>
                        <div class="text-sm text-gray-400">
                            <span id="scanStatus">就绪</span>
                        </div>
                    </div>
                </div>

                <!-- 筛选进度区域 -->
                <div id="progressSection" class="progress-section mb-6" style="display: none;">
                    <div class="bg-gray-800 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-300">筛选进度</span>
                            <span id="currentLevelName" class="text-sm text-blue-400"></span>
                        </div>
                        <div class="w-full bg-gray-700 rounded-full h-2 mb-2">
                            <div id="progressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                        </div>
                        <div class="flex justify-between text-xs text-gray-400">
                            <span id="progressText">0/0</span>
                            <span id="progressPercentage">0%</span>
                        </div>
                    </div>
                </div>

                <!-- 筛选漏斗图 -->
                <div id="funnelSection" class="funnel-section mb-6" style="display: none;">
                    <h4 class="text-md font-semibold text-gray-200 mb-3">
                        <i class="fas fa-chart-bar mr-2"></i>筛选漏斗图
                    </h4>
                    <div id="funnelChart" class="bg-gray-800 rounded-lg p-4">
                        <!-- 动态生成漏斗图 -->
                    </div>
                </div>

                <!-- 筛选结果区域 -->
                <div id="resultsSection" class="results-section" style="display: none;">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="text-md font-semibold text-gray-200">
                            <i class="fas fa-list mr-2"></i>筛选结果
                        </h4>
                        <div class="flex space-x-2">
                            <button id="exportResultsBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                                <i class="fas fa-download mr-1"></i>导出
                            </button>
                            <button id="addToWatchlistBtn" class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm">
                                <i class="fas fa-plus mr-1"></i>加入自选
                            </button>
                        </div>
                    </div>
                    
                    <!-- 结果统计 -->
                    <div id="resultsSummary" class="bg-gray-800 rounded-lg p-3 mb-4">
                        <!-- 动态生成统计信息 -->
                    </div>
                    
                    <!-- 结果表格 -->
                    <div class="bg-gray-800 rounded-lg overflow-hidden">
                        <div class="overflow-x-auto">
                            <table id="resultsTable" class="w-full text-sm">
                                <thead class="bg-gray-700">
                                    <tr>
                                        <th class="px-4 py-2 text-left">股票代码</th>
                                        <th class="px-4 py-2 text-left">股票名称</th>
                                        <th class="px-4 py-2 text-left">市场</th>
                                        <th class="px-4 py-2 text-left">通过层级</th>
                                        <th class="px-4 py-2 text-left">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="resultsTableBody">
                                    <!-- 动态生成结果行 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 层级编辑弹窗 -->
            <div id="levelEditModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
                <div class="bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-200">编辑筛选层级</h3>
                        <button id="closeLevelEditModal" class="text-gray-400 hover:text-white">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div class="space-y-4">
                        <!-- 层级名称 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">层级名称</label>
                            <input type="text" id="levelNameInput" class="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-blue-500 focus:outline-none">
                        </div>

                        <!-- 时间周期 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">时间周期</label>
                            <select id="levelPeriodSelect" class="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-blue-500 focus:outline-none">
                                <option value="daily">日线</option>
                                <option value="weekly">周线</option>
                                <option value="monthly">月线</option>
                            </select>
                        </div>

                        <!-- 描述 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">描述</label>
                            <input type="text" id="levelDescriptionInput" class="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-blue-500 focus:outline-none">
                        </div>

                        <!-- 技术指标 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">技术指标</label>
                            <select id="levelIndicatorSelect" class="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-blue-500 focus:outline-none">
                                <option value="kdj">KDJ</option>
                                <option value="macd">MACD</option>
                                <option value="bollinger">布林带</option>
                                <option value="volume">成交量</option>
                            </select>
                        </div>

                        <!-- 筛选条件 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">筛选条件</label>
                            <select id="levelConditionSelect" class="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-blue-500 focus:outline-none">
                                <!-- 动态生成选项 -->
                            </select>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3 mt-6">
                        <button id="cancelLevelEdit" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded">
                            取消
                        </button>
                        <button id="saveLevelEdit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
                            保存
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 预设方案选择
        const presetSelector = document.getElementById('presetSelector');
        presetSelector?.addEventListener('change', (e) => {
            this.applyPreset(e.target.value);
        });

        // 高级配置按钮
        const advancedConfigBtn = document.getElementById('advancedConfigBtn');
        advancedConfigBtn?.addEventListener('click', () => {
            this.showAdvancedConfig();
        });

        // 重置预设按钮
        const resetPresetBtn = document.getElementById('resetPresetBtn');
        resetPresetBtn?.addEventListener('click', () => {
            this.resetCurrentPreset();
        });

        // 添加层级按钮
        const addLevelBtn = document.getElementById('addLevelBtn');
        addLevelBtn?.addEventListener('click', () => {
            this.showLevelEditModal();
        });

        // 层级编辑弹窗相关事件
        const closeLevelEditModal = document.getElementById('closeLevelEditModal');
        closeLevelEditModal?.addEventListener('click', () => {
            this.hideLevelEditModal();
        });

        const cancelLevelEdit = document.getElementById('cancelLevelEdit');
        cancelLevelEdit?.addEventListener('click', () => {
            this.hideLevelEditModal();
        });

        const saveLevelEdit = document.getElementById('saveLevelEdit');
        saveLevelEdit?.addEventListener('click', () => {
            this.saveLevelEdit();
        });

        // 指标选择变化时更新条件选项
        const levelIndicatorSelect = document.getElementById('levelIndicatorSelect');
        levelIndicatorSelect?.addEventListener('change', (e) => {
            this.updateConditionOptions(e.target.value);
        });

        // 开始筛选按钮
        const startScanBtn = document.getElementById('multiLevelStartBtn');
        startScanBtn?.addEventListener('click', () => {
            this.startScan();
        });

        // 停止筛选按钮
        const stopScanBtn = document.getElementById('multiLevelStopBtn');
        stopScanBtn?.addEventListener('click', () => {
            this.stopScan();
        });

        // 清除结果按钮
        const clearResultsBtn = document.getElementById('multiLevelClearBtn');
        clearResultsBtn?.addEventListener('click', () => {
            this.clearResults();
        });

        // 导出结果按钮
        const exportResultsBtn = document.getElementById('exportResultsBtn');
        exportResultsBtn?.addEventListener('click', () => {
            this.exportResults();
        });

        // 加入自选按钮
        const addToWatchlistBtn = document.getElementById('addToWatchlistBtn');
        addToWatchlistBtn?.addEventListener('click', () => {
            this.addToWatchlist();
        });
    }

    /**
     * 设置扫描器
     * @param {MultiLevelScanner} scanner - 多层级扫描器实例
     */
    setScanner(scanner) {
        this.scanner = scanner;
        
        // 绑定扫描器事件
        this.scanner.onLevelProgress = (levelIndex, progress) => {
            this.updateProgress(levelIndex, progress);
        };
        
        this.scanner.onLevelComplete = (levelIndex, result) => {
            this.onLevelCompleted(levelIndex, result);
        };
        
        // 初始化默认配置
        this.loadDefaultConfig();
    }

    /**
     * 加载默认配置
     */
    loadDefaultConfig() {
        if (!this.scanner) return;
        
        const presets = MultiLevelScanner.getPresets();
        this.applyPreset('balanced'); // 默认使用平衡型
    }

    /**
     * 应用预设方案
     * @param {string} presetName - 预设方案名称
     */
    applyPreset(presetName) {
        if (!presetName || !this.scanner) {
            this.hidePresetOverview();
            return;
        }

        const presets = MultiLevelScanner.getPresets();
        const preset = presets[presetName];

        if (!preset) return;

        // 保存当前预设信息
        this.currentPreset = presetName;
        this.originalPresetLevels = JSON.parse(JSON.stringify(preset.levels));

        this.scanner.setLevels(preset.levels);
        this.showPresetOverview(preset);
        this.showLevelsManagement(preset.levels);

        console.log(`应用预设方案: ${preset.name}`);
    }



    /**
     * 开始筛选
     */
    async startScan() {
        if (!this.scanner) {
            alert('扫描器未初始化');
            return;
        }

        try {
            this.updateScanStatus('筛选中...', true);
            this.showProgress();
            
            this.onScanStart && this.onScanStart();
            
            const results = await this.scanner.startMultiLevelScan({
                days: 120
            });
            
            this.currentResults = results;
            this.displayResults(results);
            this.updateScanStatus('筛选完成', false);
            
            this.onScanComplete && this.onScanComplete(results);
            
        } catch (error) {
            console.error('筛选失败:', error);
            alert('筛选失败: ' + error.message);
            this.updateScanStatus('筛选失败', false);
        }
    }

    /**
     * 停止筛选
     */
    stopScan() {
        if (this.scanner) {
            this.scanner.stopScan();
            this.updateScanStatus('已停止', false);
        }
    }

    /**
     * 更新扫描状态
     * @param {string} status - 状态文本
     * @param {boolean} isScanning - 是否正在扫描
     */
    updateScanStatus(status, isScanning) {
        const statusElement = document.getElementById('scanStatus');
        const startBtn = document.getElementById('multiLevelStartBtn');
        const stopBtn = document.getElementById('multiLevelStopBtn');

        if (statusElement) statusElement.textContent = status;
        if (startBtn) startBtn.disabled = isScanning;
        if (stopBtn) stopBtn.disabled = !isScanning;
    }

    /**
     * 显示进度区域
     */
    showProgress() {
        const progressSection = document.getElementById('progressSection');
        if (progressSection) {
            progressSection.style.display = 'block';
        }
    }

    /**
     * 隐藏进度区域
     */
    hideProgress() {
        const progressSection = document.getElementById('progressSection');
        if (progressSection) {
            progressSection.style.display = 'none';
        }
    }

    /**
     * 更新进度
     * @param {number} levelIndex - 层级索引
     * @param {Object} progress - 进度信息
     */
    updateProgress(levelIndex, progress) {
        const currentLevelName = document.getElementById('currentLevelName');
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const progressPercentage = document.getElementById('progressPercentage');
        
        if (this.scanner && this.scanner.levels[levelIndex]) {
            const levelName = this.scanner.levels[levelIndex].name;
            if (currentLevelName) currentLevelName.textContent = levelName;
        }
        
        if (progressBar) progressBar.style.width = progress.percentage + '%';
        if (progressText) progressText.textContent = `${progress.current}/${progress.total}`;
        if (progressPercentage) progressPercentage.textContent = progress.percentage + '%';
    }

    /**
     * 层级完成回调
     * @param {number} levelIndex - 层级索引
     * @param {Object} result - 层级结果
     */
    onLevelCompleted(levelIndex, result) {
        console.log(`层级 ${result.levelName} 完成:`, result);
        this.onLevelComplete && this.onLevelComplete(levelIndex, result);
    }

    /**
     * 显示筛选结果
     * @param {Object} results - 筛选结果
     */
    displayResults(results) {
        this.hideProgress();
        this.showFunnelChart(results.levels);
        this.showResultsTable(results.finalResults);
        this.showResultsSummary(results.summary);
        
        const resultsSection = document.getElementById('resultsSection');
        const funnelSection = document.getElementById('funnelSection');
        
        if (resultsSection) resultsSection.style.display = 'block';
        if (funnelSection) funnelSection.style.display = 'block';
    }

    /**
     * 显示漏斗图
     * @param {Array} levels - 层级结果
     */
    showFunnelChart(levels) {
        const container = document.getElementById('funnelChart');
        if (!container) return;
        
        const maxWidth = 300;
        
        container.innerHTML = levels.map((level, index) => {
            const width = level.inputCount > 0 ? Math.max(20, (level.passedCount / levels[0].inputCount) * maxWidth) : 20;
            
            return `
                <div class="funnel-level mb-3">
                    <div class="flex items-center justify-between mb-1">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full mr-2" style="background-color: ${level.color}"></div>
                            <span class="text-sm font-medium text-gray-200">${level.levelName}</span>
                        </div>
                        <span class="text-sm text-gray-400">${level.filterRate}</span>
                    </div>
                    <div class="flex items-center">
                        <div class="bg-gray-700 rounded-full h-6 mr-3" style="width: ${maxWidth}px;">
                            <div class="h-6 rounded-full flex items-center justify-center text-xs text-white font-medium" 
                                 style="width: ${width}px; background-color: ${level.color};">
                                ${level.passedCount}
                            </div>
                        </div>
                        <span class="text-xs text-gray-400">${level.inputCount} → ${level.passedCount}</span>
                    </div>
                </div>
            `;
        }).join('');
    }

    /**
     * 显示结果表格
     * @param {Array} finalResults - 最终结果
     */
    showResultsTable(finalResults) {
        const tbody = document.getElementById('resultsTableBody');
        if (!tbody) return;
        
        tbody.innerHTML = finalResults.map(stock => `
            <tr class="border-b border-gray-700 hover:bg-gray-750">
                <td class="px-4 py-2 font-mono text-blue-400">${stock.code}</td>
                <td class="px-4 py-2">${stock.name}</td>
                <td class="px-4 py-2">
                    <span class="px-2 py-1 bg-gray-700 rounded text-xs">${stock.market}</span>
                </td>
                <td class="px-4 py-2">
                    <span class="px-2 py-1 bg-green-600 rounded text-xs">全部通过</span>
                </td>
                <td class="px-4 py-2">
                    <div class="flex space-x-1">
                        <button class="text-blue-400 hover:text-blue-300 text-xs" onclick="this.viewStock('${stock.code}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="text-green-400 hover:text-green-300 text-xs" onclick="this.addToWatchlist('${stock.code}')">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 显示结果摘要
     * @param {Object} summary - 结果摘要
     */
    showResultsSummary(summary) {
        const container = document.getElementById('resultsSummary');
        if (!container) return;
        
        container.innerHTML = `
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                    <div class="text-2xl font-bold text-blue-400">${summary.initialCount}</div>
                    <div class="text-xs text-gray-400">初始股票</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-green-400">${summary.finalCount}</div>
                    <div class="text-xs text-gray-400">最终结果</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-yellow-400">${summary.overallFilterRate}</div>
                    <div class="text-xs text-gray-400">总筛选率</div>
                </div>
                <div>
                    <div class="text-2xl font-bold text-purple-400">${summary.scanDuration}ms</div>
                    <div class="text-xs text-gray-400">耗时</div>
                </div>
            </div>
        `;
    }

    /**
     * 清除结果
     */
    clearResults() {
        this.currentResults = null;
        this.hideProgress();
        
        const resultsSection = document.getElementById('resultsSection');
        const funnelSection = document.getElementById('funnelSection');
        
        if (resultsSection) resultsSection.style.display = 'none';
        if (funnelSection) funnelSection.style.display = 'none';
        
        this.updateScanStatus('就绪', false);
    }

    /**
     * 导出结果
     */
    exportResults() {
        if (!this.currentResults || !this.currentResults.finalResults) {
            alert('没有可导出的结果');
            return;
        }
        
        const data = this.currentResults.finalResults.map(stock => ({
            股票代码: stock.code,
            股票名称: stock.name,
            市场: stock.market
        }));
        
        const csv = this.arrayToCSV(data);
        this.downloadCSV(csv, '多层级筛选结果.csv');
    }

    /**
     * 数组转CSV
     * @param {Array} data - 数据数组
     * @returns {string} CSV字符串
     */
    arrayToCSV(data) {
        if (!data.length) return '';
        
        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => row[header]).join(','))
        ].join('\n');
        
        return csvContent;
    }

    /**
     * 下载CSV文件
     * @param {string} csv - CSV内容
     * @param {string} filename - 文件名
     */
    downloadCSV(csv, filename) {
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    /**
     * 加入自选股
     */
    addToWatchlist() {
        if (!this.currentResults || !this.currentResults.finalResults) {
            alert('没有可添加的股票');
            return;
        }
        
        // 这里可以调用自选股管理功能
        console.log('添加到自选股:', this.currentResults.finalResults);
        alert(`已添加 ${this.currentResults.finalResults.length} 只股票到自选股`);
    }

    /**
     * 绑定层级编辑事件
     */
    bindLevelEditEvents() {
        const editButtons = document.querySelectorAll('.edit-level-btn');
        editButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const levelIndex = parseInt(btn.dataset.level);
                this.editLevel(levelIndex);
            });
        });
    }

    /**
     * 编辑层级配置
     * @param {number} levelIndex - 层级索引
     */
    editLevel(levelIndex) {
        if (!this.scanner || !this.scanner.levels[levelIndex]) return;

        const level = this.scanner.levels[levelIndex];
        this.showLevelEditDialog(levelIndex, level);
    }

    /**
     * 显示层级编辑对话框
     * @param {number} levelIndex - 层级索引
     * @param {Object} level - 层级配置
     */
    showLevelEditDialog(levelIndex, level) {
        // 创建模态对话框
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-gray-800 rounded-lg p-6 w-96 max-w-90vw">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">编辑筛选层级</h3>
                    <button class="close-modal text-gray-400 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form id="levelEditForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">层级名称</label>
                        <input type="text" id="levelName" value="${level.name}"
                               class="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">时间周期</label>
                        <select id="levelPeriod" class="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600">
                            <option value="daily" ${level.period === 'daily' ? 'selected' : ''}>日线</option>
                            <option value="weekly" ${level.period === 'weekly' ? 'selected' : ''}>周线</option>
                            <option value="monthly" ${level.period === 'monthly' ? 'selected' : ''}>月线</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">描述</label>
                        <input type="text" id="levelDescription" value="${level.description}"
                               class="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">技术指标</label>
                        <select id="levelIndicator" class="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600">
                            <option value="kdj" ${level.indicators.includes('kdj') ? 'selected' : ''}>KDJ</option>
                            <option value="macd" ${level.indicators.includes('macd') ? 'selected' : ''}>MACD</option>
                            <option value="bollinger" ${level.indicators.includes('bollinger') ? 'selected' : ''}>布林带</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">筛选条件</label>
                        <select id="levelCondition" class="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600">
                            ${this.getConditionOptions(level)}
                        </select>
                    </div>

                    <div id="thresholdSection" class="hidden">
                        <label class="block text-sm font-medium text-gray-300 mb-2">阈值</label>
                        <input type="number" id="levelThreshold" step="0.1"
                               class="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-blue-500">
                    </div>

                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" class="cancel-edit bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded">
                            取消
                        </button>
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        `;

        document.body.appendChild(modal);

        // 绑定事件
        this.bindModalEvents(modal, levelIndex);

        // 初始化条件选择
        this.updateConditionOptions();
    }

    /**
     * 获取条件选项
     * @param {Object} level - 层级配置
     * @returns {string} HTML选项字符串
     */
    getConditionOptions(level) {
        const currentCondition = Object.keys(level.conditions)[0];
        const currentType = level.conditions[currentCondition]?.type;

        return `
            <option value="j_above_50" ${currentType === 'j_above_50' ? 'selected' : ''}>J值大于50</option>
            <option value="j_below_20" ${currentType === 'j_below_20' ? 'selected' : ''}>J值小于20</option>
            <option value="j_above_80" ${currentType === 'j_above_80' ? 'selected' : ''}>J值大于80</option>
            <option value="golden_cross" ${currentType === 'golden_cross' ? 'selected' : ''}>金叉</option>
            <option value="death_cross" ${currentType === 'death_cross' ? 'selected' : ''}>死叉</option>
            <option value="above_zero" ${currentType === 'above_zero' ? 'selected' : ''}>零轴上方</option>
            <option value="below_zero" ${currentType === 'below_zero' ? 'selected' : ''}>零轴下方</option>
            <option value="custom_threshold" ${currentType === 'custom_threshold' ? 'selected' : ''}>自定义阈值</option>
        `;
    }

    /**
     * 更新条件选项
     */
    updateConditionOptions() {
        const conditionSelect = document.getElementById('levelCondition');
        const thresholdSection = document.getElementById('thresholdSection');

        if (conditionSelect && thresholdSection) {
            const showThreshold = conditionSelect.value === 'custom_threshold';
            thresholdSection.style.display = showThreshold ? 'block' : 'none';

            conditionSelect.addEventListener('change', () => {
                const showThreshold = conditionSelect.value === 'custom_threshold';
                thresholdSection.style.display = showThreshold ? 'block' : 'none';
            });
        }
    }

    /**
     * 绑定模态对话框事件
     * @param {HTMLElement} modal - 模态对话框元素
     * @param {number} levelIndex - 层级索引
     */
    bindModalEvents(modal, levelIndex) {
        // 关闭按钮
        const closeBtn = modal.querySelector('.close-modal');
        const cancelBtn = modal.querySelector('.cancel-edit');

        const closeModal = () => {
            document.body.removeChild(modal);
        };

        closeBtn.addEventListener('click', closeModal);
        cancelBtn.addEventListener('click', closeModal);

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) closeModal();
        });

        // 表单提交
        const form = modal.querySelector('#levelEditForm');
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveLevelEdit(levelIndex, modal);
        });
    }

    /**
     * 保存层级编辑
     * @param {number} levelIndex - 层级索引
     * @param {HTMLElement} modal - 模态对话框元素
     */
    saveLevelEdit(levelIndex, modal) {
        const formData = new FormData(modal.querySelector('#levelEditForm'));

        const name = document.getElementById('levelName').value;
        const period = document.getElementById('levelPeriod').value;
        const description = document.getElementById('levelDescription').value;
        const indicator = document.getElementById('levelIndicator').value;
        const condition = document.getElementById('levelCondition').value;
        const threshold = document.getElementById('levelThreshold').value;

        // 构建新的层级配置
        const newLevel = {
            name: name,
            period: period,
            description: description,
            indicators: [indicator],
            conditions: {},
            color: this.scanner.levels[levelIndex].color // 保持原有颜色
        };

        // 设置条件
        if (condition === 'custom_threshold' && threshold) {
            newLevel.conditions[indicator] = {
                type: 'custom_threshold',
                threshold: parseFloat(threshold)
            };
        } else {
            newLevel.conditions[indicator] = {
                type: condition
            };
        }

        // 更新扫描器配置
        this.scanner.levels[levelIndex] = newLevel;

        // 重新渲染配置
        this.showLevelsManagement(this.scanner.levels);

        // 关闭对话框
        document.body.removeChild(modal);

        console.log(`层级 ${levelIndex} 配置已更新:`, newLevel);
    }

    /**
     * 显示自定义配置对话框
     */
    showCustomConfig() {
        // 显示添加新层级的对话框
        this.showAddLevelDialog();
    }

    /**
     * 显示添加层级对话框
     */
    showAddLevelDialog() {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-gray-800 rounded-lg p-6 w-96 max-w-90vw">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">添加筛选层级</h3>
                    <button class="close-modal text-gray-400 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form id="addLevelForm" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">层级名称</label>
                        <input type="text" id="newLevelName" placeholder="例如：复筛3"
                               class="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">时间周期</label>
                        <select id="newLevelPeriod" class="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600">
                            <option value="daily">日线</option>
                            <option value="weekly">周线</option>
                            <option value="monthly">月线</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">描述</label>
                        <input type="text" id="newLevelDescription" placeholder="层级描述"
                               class="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-blue-500">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">技术指标</label>
                        <select id="newLevelIndicator" class="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600">
                            <option value="kdj">KDJ</option>
                            <option value="macd">MACD</option>
                            <option value="bollinger">布林带</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">筛选条件</label>
                        <select id="newLevelCondition" class="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600">
                            <option value="j_above_50">J值大于50</option>
                            <option value="j_below_20">J值小于20</option>
                            <option value="j_above_80">J值大于80</option>
                            <option value="golden_cross">金叉</option>
                            <option value="death_cross">死叉</option>
                            <option value="above_zero">零轴上方</option>
                            <option value="below_zero">零轴下方</option>
                            <option value="custom_threshold">自定义阈值</option>
                        </select>
                    </div>

                    <div id="newThresholdSection" class="hidden">
                        <label class="block text-sm font-medium text-gray-300 mb-2">阈值</label>
                        <input type="number" id="newLevelThreshold" step="0.1"
                               class="w-full bg-gray-700 text-white px-3 py-2 rounded border border-gray-600 focus:border-blue-500">
                    </div>

                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" class="cancel-add bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded">
                            取消
                        </button>
                        <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded">
                            添加
                        </button>
                    </div>
                </form>
            </div>
        `;

        document.body.appendChild(modal);

        // 绑定事件
        this.bindAddModalEvents(modal);

        // 初始化条件选择
        this.updateNewConditionOptions();
    }

    /**
     * 更新新层级条件选项
     */
    updateNewConditionOptions() {
        const conditionSelect = document.getElementById('newLevelCondition');
        const thresholdSection = document.getElementById('newThresholdSection');

        if (conditionSelect && thresholdSection) {
            const showThreshold = conditionSelect.value === 'custom_threshold';
            thresholdSection.style.display = showThreshold ? 'block' : 'none';

            conditionSelect.addEventListener('change', () => {
                const showThreshold = conditionSelect.value === 'custom_threshold';
                thresholdSection.style.display = showThreshold ? 'block' : 'none';
            });
        }
    }

    /**
     * 绑定添加模态对话框事件
     * @param {HTMLElement} modal - 模态对话框元素
     */
    bindAddModalEvents(modal) {
        // 关闭按钮
        const closeBtn = modal.querySelector('.close-modal');
        const cancelBtn = modal.querySelector('.cancel-add');

        const closeModal = () => {
            document.body.removeChild(modal);
        };

        closeBtn.addEventListener('click', closeModal);
        cancelBtn.addEventListener('click', closeModal);

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) closeModal();
        });

        // 表单提交
        const form = modal.querySelector('#addLevelForm');
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.addNewLevel(modal);
        });
    }

    /**
     * 添加新层级
     * @param {HTMLElement} modal - 模态对话框元素
     */
    addNewLevel(modal) {
        const name = document.getElementById('newLevelName').value;
        const period = document.getElementById('newLevelPeriod').value;
        const description = document.getElementById('newLevelDescription').value;
        const indicator = document.getElementById('newLevelIndicator').value;
        const condition = document.getElementById('newLevelCondition').value;
        const threshold = document.getElementById('newLevelThreshold').value;

        if (!name || !description) {
            alert('请填写层级名称和描述');
            return;
        }

        // 构建新的层级配置
        const newLevel = {
            name: name,
            period: period,
            description: description,
            indicators: [indicator],
            conditions: {},
            color: this.getRandomColor()
        };

        // 设置条件
        if (condition === 'custom_threshold' && threshold) {
            newLevel.conditions[indicator] = {
                type: 'custom_threshold',
                threshold: parseFloat(threshold)
            };
        } else {
            newLevel.conditions[indicator] = {
                type: condition
            };
        }

        // 添加到扫描器配置
        this.scanner.levels.push(newLevel);

        // 重新渲染配置
        this.showLevelsManagement(this.scanner.levels);

        // 关闭对话框
        document.body.removeChild(modal);

        console.log('新层级已添加:', newLevel);
    }

    /**
     * 获取随机颜色
     * @returns {string} 颜色值
     */
    getRandomColor() {
        const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16'];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    /**
     * 显示预设方案概览
     * @param {Object} preset - 预设方案
     */
    showPresetOverview(preset) {
        const overviewSection = document.getElementById('presetOverview');
        const presetNameEl = document.getElementById('presetName');
        const presetDescEl = document.getElementById('presetDescription');
        const levelsOverviewEl = document.getElementById('presetLevelsOverview');

        if (!overviewSection || !presetNameEl || !presetDescEl || !levelsOverviewEl) return;

        presetNameEl.textContent = preset.name;
        presetDescEl.textContent = `- ${preset.description}`;

        // 生成层级概览
        levelsOverviewEl.innerHTML = preset.levels.map((level, index) => `
            <div class="flex items-center justify-between py-2 px-3 bg-gray-700 rounded">
                <div class="flex items-center">
                    <div class="w-2 h-2 rounded-full mr-2" style="background-color: ${level.color}"></div>
                    <span class="text-sm font-medium text-gray-200">${level.name}</span>
                    <span class="text-xs text-gray-400 ml-2">(${this.getPeriodDisplayName(level.period)})</span>
                </div>
                <div class="text-xs text-gray-400">
                    ${this.getConditionDisplayText(level.conditions)}
                </div>
            </div>
        `).join('');

        overviewSection.style.display = 'block';
    }

    /**
     * 隐藏预设方案概览
     */
    hidePresetOverview() {
        const overviewSection = document.getElementById('presetOverview');
        const levelsManagementSection = document.getElementById('levelsManagementSection');

        if (overviewSection) overviewSection.style.display = 'none';
        if (levelsManagementSection) levelsManagementSection.style.display = 'none';
    }

    /**
     * 显示层级管理区域
     * @param {Array} levels - 层级配置
     */
    showLevelsManagement(levels) {
        const levelsManagementSection = document.getElementById('levelsManagementSection');
        const levelsManagementList = document.getElementById('levelsManagementList');

        if (!levelsManagementSection || !levelsManagementList) return;

        // 生成层级管理列表
        levelsManagementList.innerHTML = levels.map((level, levelIndex) => {
            const conditions = level.conditions;
            const indicatorName = Object.keys(conditions)[0];
            const condition = conditions[indicatorName];

            return `
                <div class="level-management-item bg-gray-700 rounded p-3 border border-gray-600">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full mr-3" style="background-color: ${level.color}"></div>
                            <div>
                                <div class="text-sm font-medium text-gray-200">${level.name}</div>
                                <div class="text-xs text-gray-400">
                                    ${this.getPeriodDisplayName(level.period)} |
                                    ${indicatorName.toUpperCase()}: ${this.getConditionDisplayName(condition.type)}
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button class="edit-level-btn text-blue-400 hover:text-blue-300 text-sm"
                                    data-level="${levelIndex}" title="编辑层级">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="delete-level-btn text-red-400 hover:text-red-300 text-sm"
                                    data-level="${levelIndex}" title="删除层级">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        // 绑定层级管理事件
        this.bindLevelsManagementEvents();

        levelsManagementSection.style.display = 'block';
    }

    /**
     * 绑定层级管理事件
     */
    bindLevelsManagementEvents() {
        // 编辑层级按钮
        document.querySelectorAll('.edit-level-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const levelIndex = parseInt(e.target.closest('.edit-level-btn').dataset.level);
                this.editLevel(levelIndex);
            });
        });

        // 删除层级按钮
        document.querySelectorAll('.delete-level-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const levelIndex = parseInt(e.target.closest('.delete-level-btn').dataset.level);
                this.deleteLevel(levelIndex);
            });
        });
    }

    /**
     * 编辑层级
     * @param {number} levelIndex - 层级索引
     */
    editLevel(levelIndex) {
        if (!this.scanner || !this.scanner.levels[levelIndex]) return;

        const level = this.scanner.levels[levelIndex];
        this.currentEditingLevelIndex = levelIndex;

        // 填充编辑表单
        document.getElementById('levelNameInput').value = level.name;
        document.getElementById('levelPeriodSelect').value = level.period;
        document.getElementById('levelDescriptionInput').value = level.description || '';

        const indicatorName = level.indicators[0];
        document.getElementById('levelIndicatorSelect').value = indicatorName;

        // 更新条件选项并设置当前值
        this.updateConditionOptions(indicatorName);
        const condition = level.conditions[indicatorName];
        document.getElementById('levelConditionSelect').value = condition.type;

        this.showLevelEditModal();
    }

    /**
     * 删除层级
     * @param {number} levelIndex - 层级索引
     */
    deleteLevel(levelIndex) {
        if (!this.scanner || !this.scanner.levels[levelIndex]) return;

        if (this.scanner.levels.length <= 1) {
            alert('至少需要保留一个筛选层级');
            return;
        }

        if (confirm('确定要删除这个筛选层级吗？')) {
            this.scanner.levels.splice(levelIndex, 1);
            this.showLevelsManagement(this.scanner.levels);
            console.log(`已删除层级 ${levelIndex}`);
        }
    }

    /**
     * 显示层级编辑弹窗
     */
    showLevelEditModal() {
        const modal = document.getElementById('levelEditModal');
        if (modal) {
            modal.style.display = 'flex';
            // 初始化条件选项
            const indicatorSelect = document.getElementById('levelIndicatorSelect');
            if (indicatorSelect) {
                this.updateConditionOptions(indicatorSelect.value);
            }
        }
    }

    /**
     * 隐藏层级编辑弹窗
     */
    hideLevelEditModal() {
        const modal = document.getElementById('levelEditModal');
        if (modal) {
            modal.style.display = 'none';
            this.currentEditingLevelIndex = null;
        }
    }

    /**
     * 保存层级编辑
     */
    saveLevelEdit() {
        const name = document.getElementById('levelNameInput').value.trim();
        const period = document.getElementById('levelPeriodSelect').value;
        const description = document.getElementById('levelDescriptionInput').value.trim();
        const indicator = document.getElementById('levelIndicatorSelect').value;
        const conditionType = document.getElementById('levelConditionSelect').value;

        if (!name) {
            alert('请输入层级名称');
            return;
        }

        const levelData = {
            name: name,
            period: period,
            description: description,
            indicators: [indicator],
            conditions: {
                [indicator]: {
                    type: conditionType,
                    threshold: this.getDefaultThreshold(conditionType)
                }
            },
            color: this.getRandomColor()
        };

        if (this.currentEditingLevelIndex !== null) {
            // 编辑现有层级
            this.scanner.levels[this.currentEditingLevelIndex] = levelData;
            console.log(`已更新层级 ${this.currentEditingLevelIndex}: ${name}`);
        } else {
            // 添加新层级
            this.scanner.levels.push(levelData);
            console.log(`已添加新层级: ${name}`);
        }

        // 更新UI
        this.showLevelsManagement(this.scanner.levels);
        this.hideLevelEditModal();
    }

    /**
     * 更新条件选项
     * @param {string} indicator - 指标名称
     */
    updateConditionOptions(indicator) {
        const conditionSelect = document.getElementById('levelConditionSelect');
        if (!conditionSelect) return;

        conditionSelect.innerHTML = this.getConditionOptions(indicator, '');
    }

    /**
     * 重置当前预设
     */
    resetCurrentPreset() {
        if (!this.currentPreset || !this.originalPresetLevels) return;

        this.scanner.setLevels(JSON.parse(JSON.stringify(this.originalPresetLevels)));

        const presets = MultiLevelScanner.getPresets();
        const preset = presets[this.currentPreset];

        this.showPresetOverview(preset);
        this.showLevelsManagement(this.originalPresetLevels);

        console.log(`预设方案已重置: ${preset.name}`);
    }

    /**
     * 显示高级配置（原自定义配置功能）
     */
    showAdvancedConfig() {
        this.showCustomConfig();
    }

    /**
     * 获取周期显示名称
     * @param {string} period - 周期
     * @returns {string} 显示名称
     */
    getPeriodDisplayName(period) {
        const periodNames = {
            'daily': '日线',
            'weekly': '周线',
            'monthly': '月线'
        };
        return periodNames[period] || period;
    }

    /**
     * 获取条件显示文本
     * @param {Object} conditions - 条件配置
     * @returns {string} 显示文本
     */
    getConditionDisplayText(conditions) {
        const conditionTexts = [];

        Object.entries(conditions).forEach(([indicator, condition]) => {
            const indicatorName = indicator.toUpperCase();
            const conditionName = this.getConditionDisplayName(condition.type);
            conditionTexts.push(`${indicatorName}: ${conditionName}`);
        });

        return conditionTexts.join(', ');
    }

    /**
     * 获取条件显示名称
     * @param {string} conditionType - 条件类型
     * @returns {string} 显示名称
     */
    getConditionDisplayName(conditionType) {
        const conditionNames = {
            'j_above_50': 'J值>50',
            'j_above_80': 'J值>80',
            'j_below_20': 'J值<20',
            'golden_cross': '金叉',
            'death_cross': '死叉',
            'above_zero': '零轴上方',
            'below_zero': '零轴下方'
        };
        return conditionNames[conditionType] || conditionType;
    }

    /**
     * 获取条件选项HTML
     * @param {string} indicator - 指标名称
     * @param {string} selectedType - 选中的类型
     * @returns {string} HTML字符串
     */
    getConditionOptions(indicator, selectedType) {
        const options = {
            'kdj': [
                { value: 'j_above_50', text: 'J值>50' },
                { value: 'j_above_80', text: 'J值>80' },
                { value: 'j_below_20', text: 'J值<20' },
                { value: 'golden_cross', text: '金叉' },
                { value: 'death_cross', text: '死叉' }
            ],
            'macd': [
                { value: 'golden_cross', text: '金叉' },
                { value: 'death_cross', text: '死叉' },
                { value: 'above_zero', text: '零轴上方' },
                { value: 'below_zero', text: '零轴下方' }
            ]
        };

        const indicatorOptions = options[indicator] || options['kdj'];

        return indicatorOptions.map(option =>
            `<option value="${option.value}" ${option.value === selectedType ? 'selected' : ''}>${option.text}</option>`
        ).join('');
    }

    /**
     * 获取默认条件
     * @param {string} indicator - 指标名称
     * @returns {Object} 默认条件
     */
    getDefaultCondition(indicator) {
        const defaults = {
            'kdj': { type: 'j_above_50', threshold: 50 },
            'macd': { type: 'golden_cross', threshold: 0 }
        };
        return defaults[indicator] || defaults['kdj'];
    }

    /**
     * 获取默认阈值
     * @param {string} conditionType - 条件类型
     * @returns {number} 默认阈值
     */
    getDefaultThreshold(conditionType) {
        const thresholds = {
            'j_above_50': 50,
            'j_above_80': 80,
            'j_below_20': 20,
            'golden_cross': 0,
            'death_cross': 0,
            'above_zero': 0,
            'below_zero': 0
        };
        return thresholds[conditionType] || 0;
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MultiLevelPanel;
}
