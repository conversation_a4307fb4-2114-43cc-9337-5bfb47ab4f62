/**
 * 量化交易策略引擎
 * 实现起飞模式和强势模式的交易策略
 */

class TechnicalIndicators {
    /**
     * 计算指数移动平均线 (EMA)
     */
    static calculateEMA(data, period, alpha = null) {
        if (!alpha) alpha = 2 / (period + 1);
        const ema = [];
        ema[0] = data[0];
        
        for (let i = 1; i < data.length; i++) {
            ema[i] = alpha * data[i] + (1 - alpha) * ema[i - 1];
        }
        return ema;
    }

    /**
     * 计算简单移动平均线 (SMA)
     */
    static calculateSMA(data, period) {
        const sma = [];
        for (let i = 0; i < data.length; i++) {
            if (i < period - 1) {
                sma[i] = null;
            } else {
                const sum = data.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
                sma[i] = sum / period;
            }
        }
        return sma;
    }

    /**
     * 计算KDJ指标
     */
    static calculateKDJ(highs, lows, closes, kPeriod = 9, dPeriod = 3, jPeriod = 3) {
        const rsv = [];
        const k = [];
        const d = [];
        const j = [];

        for (let i = 0; i < closes.length; i++) {
            if (i < kPeriod - 1) {
                rsv[i] = null;
                k[i] = null;
                d[i] = null;
                j[i] = null;
            } else {
                const periodHigh = Math.max(...highs.slice(i - kPeriod + 1, i + 1));
                const periodLow = Math.min(...lows.slice(i - kPeriod + 1, i + 1));
                
                if (periodHigh === periodLow) {
                    rsv[i] = 50;
                } else {
                    rsv[i] = ((closes[i] - periodLow) / (periodHigh - periodLow)) * 100;
                }

                if (i === kPeriod - 1) {
                    k[i] = rsv[i];
                    d[i] = rsv[i];
                } else {
                    k[i] = (2 * k[i - 1] + rsv[i]) / 3;
                    d[i] = (2 * d[i - 1] + k[i]) / 3;
                }
                
                j[i] = 3 * k[i] - 2 * d[i];
            }
        }

        return { k, d, j };
    }

    /**
     * 计算布林带
     */
    static calculateBollingerBands(data, period = 20, stdDev = 2) {
        const sma = this.calculateSMA(data, period);
        const upper = [];
        const lower = [];

        for (let i = 0; i < data.length; i++) {
            if (i < period - 1) {
                upper[i] = null;
                lower[i] = null;
            } else {
                const slice = data.slice(i - period + 1, i + 1);
                const mean = sma[i];
                const variance = slice.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / period;
                const std = Math.sqrt(variance);
                
                upper[i] = mean + stdDev * std;
                lower[i] = mean - stdDev * std;
            }
        }

        return { upper, middle: sma, lower };
    }

    /**
     * 计算MACD
     */
    static calculateMACD(data, fastPeriod = 12, slowPeriod = 26, signalPeriod = 9) {
        const fastEMA = this.calculateEMA(data, fastPeriod);
        const slowEMA = this.calculateEMA(data, slowPeriod);
        
        const macdLine = fastEMA.map((fast, i) => fast - slowEMA[i]);
        const signalLine = this.calculateEMA(macdLine.filter(val => val !== null), signalPeriod);
        
        // 补齐信号线长度
        const fullSignalLine = new Array(slowPeriod - 1).fill(null).concat(signalLine);
        
        const histogram = macdLine.map((macd, i) => {
            if (macd === null || fullSignalLine[i] === null) return null;
            return macd - fullSignalLine[i];
        });

        return { macdLine, signalLine: fullSignalLine, histogram };
    }

    /**
     * 计算OBV (On Balance Volume)
     */
    static calculateOBV(closes, volumes) {
        const obv = [volumes[0]];
        
        for (let i = 1; i < closes.length; i++) {
            if (closes[i] > closes[i - 1]) {
                obv[i] = obv[i - 1] + volumes[i];
            } else if (closes[i] < closes[i - 1]) {
                obv[i] = obv[i - 1] - volumes[i];
            } else {
                obv[i] = obv[i - 1];
            }
        }
        
        return obv;
    }

    /**
     * 计算最高价序列
     */
    static calculateHighest(data, period) {
        const highest = [];
        for (let i = 0; i < data.length; i++) {
            if (i < period - 1) {
                highest[i] = null;
            } else {
                highest[i] = Math.max(...data.slice(i - period + 1, i + 1));
            }
        }
        return highest;
    }
}

class SignalDetector {
    /**
     * 检测上穿信号
     */
    static crossUp(series1, series2, index) {
        if (index < 1) return false;
        return series1[index - 1] <= series2[index - 1] && series1[index] > series2[index];
    }

    /**
     * 检测连续上升
     */
    static rising(series, periods, index) {
        if (index < periods) return false;
        
        for (let i = 1; i <= periods; i++) {
            if (series[index - i + 1] <= series[index - i]) {
                return false;
            }
        }
        return true;
    }

    /**
     * 计算斜率
     */
    static slope(series, periods, index) {
        if (index < periods) return 0;
        
        const y1 = series[index - periods + 1];
        const y2 = series[index];
        return (y2 - y1) / periods;
    }
}

class StrategyEngine {
    constructor(config = {}) {
        this.config = {
            // 起飞模式参数
            takeoff: {
                kRange: [20, 50],           // K值范围
                volumeRatio: 1.2,           // 成交量比率
                obvRisingPeriods: 3,        // OBV连涨天数
                requiredConditions: 3,      // 4选3
                ...config.takeoff
            },
            
            // 强势模式参数
            strong: {
                kThreshold: 50,             // K值阈值
                volumeRatio: 1.5,           // 成交量比率
                macdRisingPeriods: 2,       // MACD柱线连涨
                ...config.strong
            },
            
            // 公共参数
            common: {
                signalCooldown: 7,          // 信号冷却期
                stopLossTimeout: [3, 5],    // 止损时间范围
                targetReturn: 0.5,          // 目标收益率
                ...config.common
            }
        };

        this.signals = [];
        this.positions = [];
        this.lastSignalIndex = -1;
    }

    /**
     * 分析股票数据并生成交易信号
     */
    analyze(stockData) {
        const { dates, opens, highs, lows, closes, volumes } = stockData;
        
        // 计算技术指标
        const indicators = this.calculateIndicators(stockData);
        
        // 检测信号
        const signals = [];
        
        for (let i = 20; i < closes.length; i++) { // 从第20个数据点开始分析
            const takeoffSignal = this.checkTakeoffMode(indicators, i);
            const strongSignal = this.checkStrongMode(indicators, i);
            
            if (takeoffSignal || strongSignal) {
                // 检查信号冷却期
                if (this.isSignalCooledDown(i)) {
                    signals.push({
                        date: dates[i],
                        index: i,
                        price: closes[i],
                        type: takeoffSignal ? 'takeoff' : 'strong',
                        mode: takeoffSignal ? '起飞模式' : '强势模式',
                        position: takeoffSignal ? '预备仓(1/3)' : '核心仓',
                        conditions: takeoffSignal ? takeoffSignal.conditions : strongSignal.conditions,
                        strength: this.calculateSignalStrength(indicators, i)
                    });
                    
                    this.lastSignalIndex = i;
                }
            }
        }
        
        this.signals = signals;
        return {
            signals,
            indicators,
            analysis: this.generateAnalysisReport(signals, indicators)
        };
    }

    /**
     * 计算所有技术指标
     */
    calculateIndicators(stockData) {
        const { opens, highs, lows, closes, volumes } = stockData;

        // 基础移动平均线
        const ema20 = TechnicalIndicators.calculateEMA(closes, 20);
        const ma60 = TechnicalIndicators.calculateSMA(closes, 60);

        // 周线数据处理 (简化为5日均线模拟)
        const weeklyCloses = this.convertToWeekly(closes);
        const maWeek20 = TechnicalIndicators.calculateSMA(weeklyCloses, 20);

        // KDJ指标
        const kdj = TechnicalIndicators.calculateKDJ(highs, lows, closes);

        // 布林带
        const bb = TechnicalIndicators.calculateBollingerBands(closes, 20, 2);

        // MACD
        const macd = TechnicalIndicators.calculateMACD(closes);

        // OBV
        const obv = TechnicalIndicators.calculateOBV(closes, volumes);

        // 成交量指标
        const volumeEMA20 = TechnicalIndicators.calculateEMA(volumes, 20);
        const volumeRatio = volumes.map((vol, i) => volumeEMA20[i] ? vol / volumeEMA20[i] : 1);

        // 成交量布林带
        const logVolumes = volumes.map(v => Math.log(v + 1));
        const volBB = TechnicalIndicators.calculateBollingerBands(logVolumes, 20, 2);

        // 最高价
        const highest20 = TechnicalIndicators.calculateHighest(closes, 20);

        return {
            ema20, ma60, maWeek20, kdj, bb, macd, obv,
            volumeRatio, volBB, highest20, logVolumes
        };
    }

    /**
     * 检测起飞模式信号
     */
    checkTakeoffMode(indicators, index) {
        const { ema20, kdj, volumeRatio, obv, macd, ma60, maWeek20 } = indicators;
        const { closes } = this.currentData;

        const conditions = [];
        let conditionsMet = 0;

        // 条件1: K上穿D，且K在20-50之间
        if (SignalDetector.crossUp(kdj.k, kdj.d, index) &&
            kdj.k[index] >= this.config.takeoff.kRange[0] &&
            kdj.k[index] <= this.config.takeoff.kRange[1]) {
            conditions.push('K上穿D且K值适中');
            conditionsMet++;
        }

        // 条件2: 收盘价上穿EMA20
        if (SignalDetector.crossUp(closes, ema20, index)) {
            conditions.push('收盘价上穿EMA20');
            conditionsMet++;
        }

        // 条件3: 成交量≥近20日1.2倍 或 OBV连涨3天
        if (volumeRatio[index] >= this.config.takeoff.volumeRatio ||
            SignalDetector.rising(obv, this.config.takeoff.obvRisingPeriods, index)) {
            conditions.push(volumeRatio[index] >= this.config.takeoff.volumeRatio ?
                '成交量放大' : 'OBV连续上涨');
            conditionsMet++;
        }

        // 条件4: MACD柱线连续2根上升
        if (SignalDetector.rising(macd.histogram, 2, index)) {
            conditions.push('MACD动能拐头');
            conditionsMet++;
        }

        // 趋势条件: 周线趋势向上或收盘在周线MA20之上
        const weekTrendOk = this.checkWeeklyTrend(indicators, index);

        if (conditionsMet >= this.config.takeoff.requiredConditions && weekTrendOk) {
            return {
                type: 'takeoff',
                conditions,
                conditionsMet,
                weekTrendOk
            };
        }

        return null;
    }

    /**
     * 检测强势模式信号
     */
    checkStrongMode(indicators, index) {
        const { ema20, ma60, kdj, volumeRatio, volBB, bb, highest20, macd, logVolumes } = indicators;
        const { closes } = this.currentData;

        const conditions = [];

        // 条件1: 大方向向上 - 收盘在MA60上方、MA60向上、周线趋势向上
        const trendUp = closes[index] > ma60[index] &&
                       SignalDetector.slope(ma60, 10, index) > 0 &&
                       this.checkWeeklyTrend(indicators, index);

        if (!trendUp) return null;
        conditions.push('大方向向上');

        // 条件2: 成交量≥近20日1.5倍 且 log_vol在量的布林带上轨之上
        if (volumeRatio[index] >= this.config.strong.volumeRatio &&
            logVolumes[index] > volBB.upper[index]) {
            conditions.push('明确放量突破');
        } else {
            return null;
        }

        // 条件3: K上穿D，且K>50
        if (SignalDetector.crossUp(kdj.k, kdj.d, index) && kdj.k[index] > this.config.strong.kThreshold) {
            conditions.push('KDJ强势突破');
        } else {
            return null;
        }

        // 条件4: 收盘价>20日最高价 或 收盘价>布林带上轨
        if (closes[index] > highest20[index] || closes[index] > bb.upper[index]) {
            conditions.push('价格结构突破');
        } else {
            return null;
        }

        // 条件5: MACD柱线>0且连续2根上升
        if (macd.histogram[index] > 0 &&
            SignalDetector.rising(macd.histogram, this.config.strong.macdRisingPeriods, index)) {
            conditions.push('MACD动能延续');
        } else {
            return null;
        }

        return {
            type: 'strong',
            conditions
        };
    }

    /**
     * 检查周线趋势
     */
    checkWeeklyTrend(indicators, index) {
        const { maWeek20 } = indicators;
        const { closes } = this.currentData;

        // 简化处理：MA_week20向上 或 收盘在MA_week20之上
        const weekIndex = Math.floor(index / 5); // 简化的周线索引
        if (weekIndex < maWeek20.length && maWeek20[weekIndex]) {
            return SignalDetector.slope(maWeek20, 5, weekIndex) > 0 ||
                   closes[index] > maWeek20[weekIndex];
        }
        return true; // 数据不足时默认通过
    }

    /**
     * 检查信号冷却期
     */
    isSignalCooledDown(index) {
        return index - this.lastSignalIndex >= this.config.common.signalCooldown;
    }

    /**
     * 计算信号强度
     */
    calculateSignalStrength(indicators, index) {
        // 基于多个指标的综合强度评分
        let strength = 0;

        // KDJ强度
        if (indicators.kdj.k[index] > indicators.kdj.d[index]) strength += 1;

        // 成交量强度
        if (indicators.volumeRatio[index] > 1.5) strength += 2;
        else if (indicators.volumeRatio[index] > 1.2) strength += 1;

        // MACD强度
        if (indicators.macd.histogram[index] > 0) strength += 1;

        return Math.min(strength, 5); // 最大5分
    }

    /**
     * 转换为周线数据 (简化处理)
     */
    convertToWeekly(dailyData) {
        const weeklyData = [];
        for (let i = 0; i < dailyData.length; i += 5) {
            const weekData = dailyData.slice(i, i + 5);
            if (weekData.length > 0) {
                weeklyData.push(weekData[weekData.length - 1]); // 取周五收盘价
            }
        }
        return weeklyData;
    }

    /**
     * 生成分析报告
     */
    generateAnalysisReport(signals, indicators) {
        const totalSignals = signals.length;
        const takeoffSignals = signals.filter(s => s.type === 'takeoff').length;
        const strongSignals = signals.filter(s => s.type === 'strong').length;

        return {
            totalSignals,
            takeoffSignals,
            strongSignals,
            signalFrequency: totalSignals > 0 ? (signals[signals.length - 1].index - signals[0].index) / totalSignals : 0,
            averageStrength: signals.reduce((sum, s) => sum + s.strength, 0) / totalSignals || 0
        };
    }

    /**
     * 设置当前分析的数据
     */
    setCurrentData(stockData) {
        this.currentData = stockData;
    }
}
