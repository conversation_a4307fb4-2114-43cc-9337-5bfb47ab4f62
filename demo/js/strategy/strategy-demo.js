/**
 * 策略演示脚本
 * 提供策略系统的演示和示例数据
 */

class StrategyDemo {
    constructor() {
        this.demoStocks = [
            { code: '000001', name: '平安银行' },
            { code: '000002', name: '万科A' },
            { code: '600036', name: '招商银行' },
            { code: '600519', name: '贵州茅台' },
            { code: '000858', name: '五粮液' }
        ];
    }

    /**
     * 生成演示用的股票数据
     */
    generateDemoData(stockCode, days = 120) {
        const stockInfo = this.demoStocks.find(s => s.code === stockCode) || 
                         { code: stockCode, name: '演示股票' };
        
        const data = {
            code: stockCode,
            name: stockInfo.name,
            dates: [],
            opens: [],
            highs: [],
            lows: [],
            closes: [],
            volumes: []
        };

        // 基础价格（根据股票代码设定不同的价格范围）
        let basePrice = this.getBasePriceForStock(stockCode);
        
        // 生成历史数据
        for (let i = 0; i < days; i++) {
            const date = new Date();
            date.setDate(date.getDate() - (days - i));
            data.dates.push(date.toISOString().split('T')[0]);
            
            // 价格随机游走，加入一些趋势性
            const trendFactor = this.getTrendFactor(i, days);
            const volatility = this.getVolatilityForStock(stockCode);
            const change = (Math.random() - 0.5) * volatility + trendFactor;
            
            basePrice *= (1 + change);
            basePrice = Math.max(basePrice, 1); // 确保价格不为负
            
            // 生成OHLC数据
            const open = basePrice * (0.995 + Math.random() * 0.01);
            const close = basePrice * (0.995 + Math.random() * 0.01);
            const high = Math.max(open, close) * (1 + Math.random() * 0.02);
            const low = Math.min(open, close) * (1 - Math.random() * 0.02);
            
            data.opens.push(parseFloat(open.toFixed(2)));
            data.highs.push(parseFloat(high.toFixed(2)));
            data.lows.push(parseFloat(low.toFixed(2)));
            data.closes.push(parseFloat(close.toFixed(2)));
            
            // 生成成交量数据
            const baseVolume = this.getBaseVolumeForStock(stockCode);
            const volumeMultiplier = 0.5 + Math.random() * 1.5; // 0.5-2倍的变化
            const volume = Math.floor(baseVolume * volumeMultiplier);
            data.volumes.push(volume);
        }

        return data;
    }

    /**
     * 根据股票代码获取基础价格
     */
    getBasePriceForStock(stockCode) {
        const priceMap = {
            '000001': 12,   // 平安银行
            '000002': 18,   // 万科A
            '600036': 35,   // 招商银行
            '600519': 1800, // 贵州茅台
            '000858': 150   // 五粮液
        };
        return priceMap[stockCode] || (10 + Math.random() * 40);
    }

    /**
     * 根据股票代码获取波动率
     */
    getVolatilityForStock(stockCode) {
        const volatilityMap = {
            '000001': 0.03,  // 银行股波动较小
            '000002': 0.04,  // 地产股
            '600036': 0.03,  // 银行股
            '600519': 0.05,  // 白酒股波动较大
            '000858': 0.05   // 白酒股
        };
        return volatilityMap[stockCode] || 0.04;
    }

    /**
     * 根据股票代码获取基础成交量
     */
    getBaseVolumeForStock(stockCode) {
        const volumeMap = {
            '000001': 50000000,  // 平安银行成交量大
            '000002': 30000000,  // 万科A
            '600036': 25000000,  // 招商银行
            '600519': 5000000,   // 贵州茅台成交量相对较小
            '000858': 8000000    // 五粮液
        };
        return volumeMap[stockCode] || 20000000;
    }

    /**
     * 获取趋势因子（模拟不同阶段的市场趋势）
     */
    getTrendFactor(dayIndex, totalDays) {
        const progress = dayIndex / totalDays;
        
        // 创建一个波浪形的趋势
        const wave1 = Math.sin(progress * Math.PI * 2) * 0.001;
        const wave2 = Math.sin(progress * Math.PI * 4) * 0.0005;
        const longTrend = (progress - 0.5) * 0.0002; // 长期轻微上升趋势
        
        return wave1 + wave2 + longTrend;
    }

    /**
     * 生成包含特定信号的演示数据
     */
    generateSignalDemoData(stockCode, signalType = 'mixed') {
        const data = this.generateDemoData(stockCode, 120);
        
        // 在特定位置插入信号模式
        this.insertSignalPatterns(data, signalType);
        
        return data;
    }

    /**
     * 在数据中插入信号模式
     */
    insertSignalPatterns(data, signalType) {
        const length = data.closes.length;
        
        if (signalType === 'takeoff' || signalType === 'mixed') {
            // 在70%位置插入起飞模式信号
            this.insertTakeoffPattern(data, Math.floor(length * 0.7));
        }
        
        if (signalType === 'strong' || signalType === 'mixed') {
            // 在85%位置插入强势模式信号
            this.insertStrongPattern(data, Math.floor(length * 0.85));
        }
    }

    /**
     * 插入起飞模式信号模式
     */
    insertTakeoffPattern(data, startIndex) {
        if (startIndex < 20 || startIndex >= data.closes.length - 10) return;
        
        // 创建一个小幅下跌后反弹的模式
        for (let i = 0; i < 8; i++) {
            const index = startIndex + i;
            if (index >= data.closes.length) break;
            
            let multiplier;
            if (i < 4) {
                // 前4天小幅下跌
                multiplier = 1 - (i * 0.005);
            } else {
                // 后4天开始反弹
                multiplier = 1 + ((i - 3) * 0.008);
            }
            
            data.closes[index] *= multiplier;
            data.opens[index] *= multiplier;
            data.highs[index] *= multiplier;
            data.lows[index] *= multiplier;
            
            // 在反弹时增加成交量
            if (i >= 4) {
                data.volumes[index] *= (1.2 + Math.random() * 0.3);
            }
        }
    }

    /**
     * 插入强势模式信号模式
     */
    insertStrongPattern(data, startIndex) {
        if (startIndex < 20 || startIndex >= data.closes.length - 5) return;
        
        // 创建一个强势突破模式
        for (let i = 0; i < 5; i++) {
            const index = startIndex + i;
            if (index >= data.closes.length) break;
            
            // 连续上涨
            const multiplier = 1 + (i * 0.015 + Math.random() * 0.01);
            
            data.closes[index] *= multiplier;
            data.opens[index] *= multiplier;
            data.highs[index] *= multiplier;
            data.lows[index] *= multiplier;
            
            // 大幅放量
            data.volumes[index] *= (1.5 + Math.random() * 0.5);
        }
    }

    /**
     * 运行策略演示
     */
    async runStrategyDemo(strategyEngine, stockCode = '000001') {
        console.log(`开始运行策略演示 - 股票: ${stockCode}`);
        
        // 生成演示数据
        const demoData = this.generateSignalDemoData(stockCode, 'mixed');
        console.log(`生成演示数据完成，共 ${demoData.closes.length} 个数据点`);
        
        // 设置数据并运行分析
        strategyEngine.setCurrentData(demoData);
        const results = strategyEngine.analyze(demoData);
        
        console.log(`策略分析完成:`);
        console.log(`- 发现信号: ${results.signals.length} 个`);
        console.log(`- 起飞信号: ${results.analysis.takeoffSignals} 个`);
        console.log(`- 强势信号: ${results.analysis.strongSignals} 个`);
        console.log(`- 平均信号强度: ${results.analysis.averageStrength.toFixed(2)}`);
        
        // 显示信号详情
        results.signals.forEach((signal, index) => {
            console.log(`信号 ${index + 1}: ${signal.mode} - ${signal.date} - ¥${signal.price.toFixed(2)}`);
            console.log(`  条件: ${signal.conditions.join(', ')}`);
            console.log(`  强度: ${signal.strength}/5`);
        });
        
        return {
            demoData,
            results
        };
    }

    /**
     * 生成回测演示数据
     */
    generateBacktestDemo(strategyEngine, stockCode = '000001', days = 250) {
        console.log(`开始生成回测演示数据 - ${days} 天`);
        
        const demoData = this.generateDemoData(stockCode, days);
        
        // 在数据中随机插入多个信号
        const signalPositions = [];
        for (let i = 0; i < 8; i++) {
            const position = Math.floor(30 + Math.random() * (days - 60));
            signalPositions.push(position);
        }
        
        // 排序并插入信号
        signalPositions.sort((a, b) => a - b);
        signalPositions.forEach((pos, index) => {
            const signalType = index % 2 === 0 ? 'takeoff' : 'strong';
            if (signalType === 'takeoff') {
                this.insertTakeoffPattern(demoData, pos);
            } else {
                this.insertStrongPattern(demoData, pos);
            }
        });
        
        return demoData;
    }

    /**
     * 获取演示股票列表
     */
    getDemoStocks() {
        return [...this.demoStocks];
    }

    /**
     * 获取随机演示股票
     */
    getRandomDemoStock() {
        const randomIndex = Math.floor(Math.random() * this.demoStocks.length);
        return this.demoStocks[randomIndex];
    }
}

// 导出演示类
if (typeof window !== 'undefined') {
    window.StrategyDemo = StrategyDemo;
}

// 全局演示实例
window.strategyDemo = new StrategyDemo();
