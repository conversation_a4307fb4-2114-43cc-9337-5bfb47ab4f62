/**
 * 回测管理器
 * 整合所有回测组件，提供完整的回测功能
 */

class BacktestManager {
    constructor() {
        this.strategyEngine = null;
        this.backtestEngine = null;
        this.visualizer = null;
        this.config = null;
        this.report = null;
        
        this.currentResults = null;
        this.isRunning = false;
        
        this.init();
    }

    /**
     * 初始化管理器
     */
    init() {
        this.setupUI();
        this.initializeComponents();
        this.bindEvents();
    }

    /**
     * 设置UI界面
     */
    setupUI() {
        // 检查是否存在策略结果容器
        let container = document.getElementById('strategyResults');
        if (!container) {
            // 如果不存在，创建一个
            container = document.createElement('div');
            container.id = 'strategyResults';
            document.body.appendChild(container);
        }

        container.innerHTML = `
            <div class="backtest-manager">
                <!-- 标题栏 -->
                <div class="card p-6 mb-6">
                    <div class="flex items-center justify-between">
                        <h2 class="text-2xl font-bold flex items-center">
                            <i class="fas fa-rocket mr-3 text-purple-400"></i>
                            高级策略回测系统
                        </h2>
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 rounded-full bg-green-400" id="statusIndicator"></div>
                                <span class="text-sm text-gray-400" id="statusText">就绪</span>
                            </div>
                            <button class="btn-secondary" onclick="backtestManager.showHelp()">
                                <i class="fas fa-question-circle mr-2"></i>
                                帮助
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 主要内容区域 -->
                <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
                    <!-- 左侧配置面板 -->
                    <div class="xl:col-span-1">
                        <div id="backtestConfig"></div>
                    </div>

                    <!-- 右侧结果展示 -->
                    <div class="xl:col-span-3 space-y-6">
                        <!-- 控制面板 -->
                        <div class="card p-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <button class="btn-primary" id="startBacktestBtn" onclick="backtestManager.startBacktest()">
                                        <i class="fas fa-play mr-2"></i>
                                        开始回测
                                    </button>
                                    <button class="btn-secondary" id="stopBacktestBtn" onclick="backtestManager.stopBacktest()" disabled>
                                        <i class="fas fa-stop mr-2"></i>
                                        停止回测
                                    </button>
                                    <button class="btn-secondary" onclick="backtestManager.clearResults()">
                                        <i class="fas fa-trash mr-2"></i>
                                        清除结果
                                    </button>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="progress-container" id="progressContainer" style="display: none;">
                                        <div class="progress-bar">
                                            <div class="progress-fill" id="progressFill"></div>
                                        </div>
                                        <span class="text-sm text-gray-400 ml-2" id="progressText">0%</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 结果展示区域 -->
                        <div id="resultsContainer" style="display: none;">
                            <!-- 可视化图表 -->
                            <div id="backtestVisualizer"></div>
                            
                            <!-- 统计报告 -->
                            <div id="backtestReport"></div>
                        </div>

                        <!-- 空状态 -->
                        <div id="emptyState" class="card p-12 text-center">
                            <div class="w-24 h-24 mx-auto mb-6 opacity-50">
                                <i class="fas fa-chart-line text-6xl text-gray-500"></i>
                            </div>
                            <h3 class="text-xl font-semibold mb-2 text-gray-400">开始您的策略回测</h3>
                            <p class="text-gray-500 mb-6">
                                配置回测参数，选择股票代码，然后点击"开始回测"来分析您的策略表现
                            </p>
                            <div class="flex justify-center space-x-4">
                                <button class="btn-secondary" onclick="backtestManager.loadSampleData()">
                                    <i class="fas fa-download mr-2"></i>
                                    加载示例数据
                                </button>
                                <button class="btn-secondary" onclick="backtestManager.showTutorial()">
                                    <i class="fas fa-graduation-cap mr-2"></i>
                                    查看教程
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 初始化组件
     */
    initializeComponents() {
        // 初始化策略引擎
        this.strategyEngine = new StrategyEngine();
        
        // 初始化配置组件
        this.config = new BacktestConfig('backtestConfig');
        this.config.setCallback('onStartBacktest', (config) => {
            this.runBacktestWithConfig(config);
        });

        // 初始化可视化组件
        this.visualizer = new BacktestVisualizer('backtestVisualizer');

        // 初始化报告组件
        this.report = new BacktestReport('backtestReport');

        // 初始化回测引擎
        this.backtestEngine = new AdvancedBacktestEngine(this.strategyEngine);
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 窗口大小变化时重新调整图表
        window.addEventListener('resize', () => {
            if (this.visualizer && this.currentResults) {
                setTimeout(() => {
                    Object.values(this.visualizer.charts).forEach(chart => {
                        if (chart && chart.resize) {
                            chart.resize();
                        }
                    });
                }, 100);
            }
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'Enter':
                        e.preventDefault();
                        if (!this.isRunning) {
                            this.startBacktest();
                        }
                        break;
                    case 'Escape':
                        e.preventDefault();
                        if (this.isRunning) {
                            this.stopBacktest();
                        }
                        break;
                }
            }
        });
    }

    /**
     * 开始回测
     */
    async startBacktest() {
        const config = this.config.getConfig();
        await this.runBacktestWithConfig(config);
    }

    /**
     * 使用配置运行回测
     */
    async runBacktestWithConfig(config) {
        if (this.isRunning) {
            this.showNotification('回测正在进行中，请稍候...', 'warning');
            return;
        }

        // 验证配置
        if (!this.validateConfig(config)) {
            return;
        }

        this.isRunning = true;
        this.updateStatus('running', '正在运行回测...');
        this.showProgress(true);

        try {
            // 更新回测引擎配置
            this.backtestEngine.config = { ...this.backtestEngine.config, ...config };

            // 获取股票数据
            this.updateProgress(10, '获取股票数据...');
            const stockData = await this.getStockData(config.stockCode, config.startDate, config.endDate);

            if (!stockData || stockData.closes.length === 0) {
                throw new Error('无法获取股票数据或数据为空');
            }

            // 运行回测
            this.updateProgress(30, '分析策略信号...');
            const results = this.backtestEngine.runBacktest(stockData, config.startDate, config.endDate);

            this.updateProgress(70, '生成可视化图表...');
            await this.displayResults(results);

            this.updateProgress(100, '回测完成');
            this.showNotification('回测完成！', 'success');

        } catch (error) {
            console.error('回测失败:', error);
            this.showNotification(`回测失败: ${error.message}`, 'error');
        } finally {
            this.isRunning = false;
            this.updateStatus('ready', '就绪');
            this.showProgress(false);
        }
    }

    /**
     * 验证配置
     */
    validateConfig(config) {
        if (!config.stockCode) {
            this.showNotification('请输入股票代码', 'error');
            return false;
        }

        if (config.initialCapital < 1000) {
            this.showNotification('初始资金不能少于1000元', 'error');
            return false;
        }

        return true;
    }

    /**
     * 获取股票数据
     */
    async getStockData(stockCode, startDate, endDate) {
        // 优先使用演示数据生成器
        if (window.demoDataGenerator) {
            return window.demoDataGenerator.generateStockData(stockCode, startDate, endDate);
        }

        // 回退到简单模拟数据
        return this.generateMockStockData(stockCode, startDate, endDate);
    }

    /**
     * 生成模拟股票数据
     */
    generateMockStockData(stockCode, startDate, endDate) {
        const start = startDate ? new Date(startDate) : new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
        const end = endDate ? new Date(endDate) : new Date();
        
        const days = Math.ceil((end - start) / (24 * 60 * 60 * 1000));
        const data = {
            dates: [],
            opens: [],
            highs: [],
            lows: [],
            closes: [],
            volumes: []
        };

        let price = 10 + Math.random() * 20; // 起始价格 10-30
        
        for (let i = 0; i < days; i++) {
            const date = new Date(start.getTime() + i * 24 * 60 * 60 * 1000);
            
            // 跳过周末
            if (date.getDay() === 0 || date.getDay() === 6) {
                continue;
            }

            const open = price;
            const change = (Math.random() - 0.5) * 0.1; // ±5% 变化
            const close = Math.max(0.1, open * (1 + change));
            const high = Math.max(open, close) * (1 + Math.random() * 0.05);
            const low = Math.min(open, close) * (1 - Math.random() * 0.05);
            const volume = Math.floor(Math.random() * 1000000 + 100000);

            data.dates.push(date.toISOString().split('T')[0]);
            data.opens.push(parseFloat(open.toFixed(2)));
            data.highs.push(parseFloat(high.toFixed(2)));
            data.lows.push(parseFloat(low.toFixed(2)));
            data.closes.push(parseFloat(close.toFixed(2)));
            data.volumes.push(volume);

            price = close;
        }

        return data;
    }

    /**
     * 显示回测结果
     */
    async displayResults(results) {
        this.currentResults = results;

        // 显示结果容器，隐藏空状态
        document.getElementById('resultsContainer').style.display = 'block';
        document.getElementById('emptyState').style.display = 'none';

        // 渲染可视化图表
        this.visualizer.renderCharts(results);

        // 生成统计报告
        this.report.generateReport(results);

        // 滚动到结果区域
        document.getElementById('resultsContainer').scrollIntoView({ 
            behavior: 'smooth', 
            block: 'start' 
        });
    }

    /**
     * 停止回测
     */
    stopBacktest() {
        if (this.isRunning) {
            this.isRunning = false;
            this.updateStatus('ready', '已停止');
            this.showProgress(false);
            this.showNotification('回测已停止', 'info');
        }
    }

    /**
     * 清除结果
     */
    clearResults() {
        this.currentResults = null;
        
        // 隐藏结果容器，显示空状态
        document.getElementById('resultsContainer').style.display = 'none';
        document.getElementById('emptyState').style.display = 'block';

        // 清理图表
        if (this.visualizer) {
            this.visualizer.dispose();
        }

        this.showNotification('结果已清除', 'info');
    }

    /**
     * 更新状态
     */
    updateStatus(status, text) {
        const indicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        
        const colors = {
            ready: 'bg-green-400',
            running: 'bg-yellow-400',
            error: 'bg-red-400'
        };

        indicator.className = `w-3 h-3 rounded-full ${colors[status] || 'bg-gray-400'}`;
        statusText.textContent = text;

        // 更新按钮状态
        const startBtn = document.getElementById('startBacktestBtn');
        const stopBtn = document.getElementById('stopBacktestBtn');
        
        if (status === 'running') {
            startBtn.disabled = true;
            stopBtn.disabled = false;
        } else {
            startBtn.disabled = false;
            stopBtn.disabled = true;
        }
    }

    /**
     * 显示/隐藏进度条
     */
    showProgress(show) {
        const container = document.getElementById('progressContainer');
        container.style.display = show ? 'flex' : 'none';
        
        if (!show) {
            this.updateProgress(0, '');
        }
    }

    /**
     * 更新进度
     */
    updateProgress(percent, text) {
        const fill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        
        fill.style.width = `${percent}%`;
        progressText.textContent = text || `${percent}%`;
    }

    /**
     * 加载示例数据
     */
    loadSampleData() {
        // 随机选择一个演示场景
        const scenarios = ['bull', 'bear', 'balanced', 'volatile'];
        const randomScenario = scenarios[Math.floor(Math.random() * scenarios.length)];

        // 设置示例配置
        const sampleConfig = {
            stockCode: this.getRandomStockCode(),
            startDate: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            endDate: new Date().toISOString().split('T')[0],
            initialCapital: 100000,
            shareSize: 100
        };

        // 更新配置组件
        Object.keys(sampleConfig).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                element.value = sampleConfig[key];
            }
        });

        this.config.updateConfigFromUI();
        this.showNotification(`示例数据已加载 (${randomScenario}场景)`, 'success');
    }

    /**
     * 获取随机股票代码
     */
    getRandomStockCode() {
        const stockCodes = ['000001.SZ', '000002.SZ', '600036.SH', '002415.SZ', '300059.SZ'];
        return stockCodes[Math.floor(Math.random() * stockCodes.length)];
    }

    /**
     * 显示教程
     */
    showTutorial() {
        this.showNotification('教程功能开发中...', 'info');
    }

    /**
     * 显示帮助
     */
    showHelp() {
        const helpContent = `
            <div class="help-modal">
                <h3>回测系统使用帮助</h3>
                <div class="help-content">
                    <h4>基本流程：</h4>
                    <ol>
                        <li>在左侧配置面板设置回测参数</li>
                        <li>输入股票代码和时间范围</li>
                        <li>调整资金和费用设置</li>
                        <li>点击"开始回测"运行分析</li>
                        <li>查看图表和统计报告</li>
                    </ol>
                    
                    <h4>快捷键：</h4>
                    <ul>
                        <li>Ctrl+Enter: 开始回测</li>
                        <li>Ctrl+Esc: 停止回测</li>
                    </ul>
                </div>
            </div>
        `;
        
        // 这里可以显示模态框
        this.showNotification('帮助信息请查看控制台', 'info');
        console.log(helpContent);
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            z-index: 10000;
            max-width: 300px;
            word-wrap: break-word;
        `;
        
        const colors = {
            success: '#10B981',
            error: '#EF4444',
            warning: '#F59E0B',
            info: '#3B82F6'
        };
        
        notification.style.backgroundColor = colors[type] || colors.info;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// 全局初始化
document.addEventListener('DOMContentLoaded', () => {
    if (typeof window !== 'undefined') {
        window.BacktestManager = BacktestManager;
        window.backtestManager = new BacktestManager();
    }
});
