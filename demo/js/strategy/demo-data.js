/**
 * 演示数据生成器
 * 为回测系统提供真实感的模拟数据
 */

class DemoDataGenerator {
    constructor() {
        this.stockCodes = {
            '000001.SZ': { name: '平安银行', sector: '金融', basePrice: 12.5 },
            '000002.SZ': { name: '万科A', sector: '房地产', basePrice: 18.2 },
            '600036.SH': { name: '招商银行', sector: '金融', basePrice: 35.8 },
            '600519.SH': { name: '贵州茅台', sector: '消费', basePrice: 1680.0 },
            '000858.SZ': { name: '五粮液', sector: '消费', basePrice: 128.5 },
            '002415.SZ': { name: '海康威视', sector: '科技', basePrice: 32.1 },
            '300059.SZ': { name: '东方财富', sector: '金融科技', basePrice: 15.8 },
            '002594.SZ': { name: 'BYD', sector: '新能源', basePrice: 245.6 }
        };
    }

    /**
     * 生成股票数据
     */
    generateStockData(stockCode, startDate, endDate, options = {}) {
        const stockInfo = this.stockCodes[stockCode] || { 
            name: '未知股票', 
            sector: '其他', 
            basePrice: 10 + Math.random() * 20 
        };

        const start = new Date(startDate);
        const end = new Date(endDate);
        const days = Math.ceil((end - start) / (24 * 60 * 60 * 1000));
        
        const config = {
            volatility: 0.02,           // 日波动率
            trend: 0.0001,              // 趋势系数
            volumeBase: 1000000,        // 基础成交量
            weekendSkip: true,          // 跳过周末
            addSignals: true,           // 添加策略信号
            ...options
        };

        return this.generatePriceData(stockInfo, start, days, config);
    }

    /**
     * 生成价格数据
     */
    generatePriceData(stockInfo, startDate, days, config) {
        const data = {
            dates: [],
            opens: [],
            highs: [],
            lows: [],
            closes: [],
            volumes: [],
            stockInfo
        };

        let currentPrice = stockInfo.basePrice;
        let currentDate = new Date(startDate);
        let trendDirection = Math.random() > 0.5 ? 1 : -1;
        let trendDuration = 0;
        let maxTrendDuration = 10 + Math.random() * 20;

        for (let i = 0; i < days; i++) {
            // 跳过周末
            if (config.weekendSkip && (currentDate.getDay() === 0 || currentDate.getDay() === 6)) {
                currentDate.setDate(currentDate.getDate() + 1);
                continue;
            }

            // 趋势变化
            trendDuration++;
            if (trendDuration > maxTrendDuration) {
                trendDirection *= -1;
                trendDuration = 0;
                maxTrendDuration = 10 + Math.random() * 20;
            }

            // 计算当日价格
            const trendFactor = config.trend * trendDirection;
            const randomFactor = (Math.random() - 0.5) * config.volatility;
            const priceChange = currentPrice * (trendFactor + randomFactor);
            
            const open = currentPrice;
            const close = Math.max(0.1, currentPrice + priceChange);
            
            // 计算最高最低价
            const dayRange = Math.abs(close - open);
            const extraRange = dayRange * (0.2 + Math.random() * 0.3);
            const high = Math.max(open, close) + extraRange;
            const low = Math.min(open, close) - extraRange * 0.5;

            // 成交量（价格变化大时成交量增加）
            const priceChangeRatio = Math.abs(priceChange / currentPrice);
            const volumeMultiplier = 1 + priceChangeRatio * 3;
            const volume = Math.floor(config.volumeBase * (0.5 + Math.random()) * volumeMultiplier);

            data.dates.push(currentDate.toISOString().split('T')[0]);
            data.opens.push(parseFloat(open.toFixed(2)));
            data.highs.push(parseFloat(high.toFixed(2)));
            data.lows.push(parseFloat(low.toFixed(2)));
            data.closes.push(parseFloat(close.toFixed(2)));
            data.volumes.push(volume);

            currentPrice = close;
            currentDate.setDate(currentDate.getDate() + 1);
        }

        // 添加一些特殊的市场事件
        this.addMarketEvents(data, config);

        return data;
    }

    /**
     * 添加市场事件（如突破、回调等）
     */
    addMarketEvents(data, config) {
        const eventCount = Math.floor(data.dates.length / 30); // 平均每30天一个事件
        
        for (let i = 0; i < eventCount; i++) {
            const eventIndex = Math.floor(Math.random() * (data.dates.length - 10)) + 5;
            const eventType = Math.random();
            
            if (eventType < 0.3) {
                // 突破事件
                this.addBreakoutEvent(data, eventIndex);
            } else if (eventType < 0.6) {
                // 回调事件
                this.addPullbackEvent(data, eventIndex);
            } else {
                // 震荡事件
                this.addConsolidationEvent(data, eventIndex);
            }
        }
    }

    /**
     * 添加突破事件
     */
    addBreakoutEvent(data, startIndex) {
        const duration = 3 + Math.floor(Math.random() * 5);
        const breakoutStrength = 0.05 + Math.random() * 0.1; // 5-15%的突破
        
        for (let i = 0; i < duration && startIndex + i < data.closes.length; i++) {
            const index = startIndex + i;
            const multiplier = 1 + (breakoutStrength * i / duration);
            
            data.closes[index] *= multiplier;
            data.highs[index] = Math.max(data.highs[index], data.closes[index] * 1.02);
            data.volumes[index] *= (1.5 + Math.random());
        }
    }

    /**
     * 添加回调事件
     */
    addPullbackEvent(data, startIndex) {
        const duration = 2 + Math.floor(Math.random() * 4);
        const pullbackStrength = 0.03 + Math.random() * 0.07; // 3-10%的回调
        
        for (let i = 0; i < duration && startIndex + i < data.closes.length; i++) {
            const index = startIndex + i;
            const multiplier = 1 - (pullbackStrength * (i + 1) / duration);
            
            data.closes[index] *= multiplier;
            data.lows[index] = Math.min(data.lows[index], data.closes[index] * 0.98);
            data.volumes[index] *= (1.2 + Math.random() * 0.5);
        }
    }

    /**
     * 添加震荡事件
     */
    addConsolidationEvent(data, startIndex) {
        const duration = 5 + Math.floor(Math.random() * 10);
        const basePrice = data.closes[startIndex];
        const range = basePrice * (0.02 + Math.random() * 0.03); // 2-5%的震荡范围
        
        for (let i = 0; i < duration && startIndex + i < data.closes.length; i++) {
            const index = startIndex + i;
            const oscillation = (Math.random() - 0.5) * range;
            
            data.closes[index] = basePrice + oscillation;
            data.highs[index] = data.closes[index] + range * 0.3;
            data.lows[index] = data.closes[index] - range * 0.3;
            data.volumes[index] *= (0.8 + Math.random() * 0.4); // 震荡时成交量较小
        }
    }

    /**
     * 生成多只股票的数据
     */
    generateMultipleStocks(stockCodes, startDate, endDate, options = {}) {
        const results = {};
        
        stockCodes.forEach(code => {
            results[code] = this.generateStockData(code, startDate, endDate, options);
        });
        
        return results;
    }

    /**
     * 获取可用的股票代码列表
     */
    getAvailableStocks() {
        return Object.keys(this.stockCodes).map(code => ({
            code,
            name: this.stockCodes[code].name,
            sector: this.stockCodes[code].sector
        }));
    }

    /**
     * 生成基准指数数据（如沪深300）
     */
    generateBenchmarkData(startDate, endDate, options = {}) {
        const config = {
            volatility: 0.015,          // 指数波动率较小
            trend: 0.0002,              // 长期上涨趋势
            volumeBase: 50000000,       // 较大的成交量
            weekendSkip: true,
            ...options
        };

        const stockInfo = {
            name: '沪深300',
            sector: '指数',
            basePrice: 3800
        };

        const start = new Date(startDate);
        const end = new Date(endDate);
        const days = Math.ceil((end - start) / (24 * 60 * 60 * 1000));

        return this.generatePriceData(stockInfo, start, days, config);
    }

    /**
     * 为回测系统生成完整的演示场景
     */
    generateBacktestScenario(scenarioType = 'balanced') {
        const scenarios = {
            bull: {
                stockCode: '002415.SZ',
                startDate: '2024-01-01',
                endDate: '2024-08-13',
                options: { volatility: 0.025, trend: 0.0005 }
            },
            bear: {
                stockCode: '000002.SZ',
                startDate: '2024-01-01',
                endDate: '2024-08-13',
                options: { volatility: 0.03, trend: -0.0003 }
            },
            balanced: {
                stockCode: '000001.SZ',
                startDate: '2024-01-01',
                endDate: '2024-08-13',
                options: { volatility: 0.02, trend: 0.0001 }
            },
            volatile: {
                stockCode: '300059.SZ',
                startDate: '2024-01-01',
                endDate: '2024-08-13',
                options: { volatility: 0.04, trend: 0.0002 }
            }
        };

        const scenario = scenarios[scenarioType] || scenarios.balanced;
        return this.generateStockData(scenario.stockCode, scenario.startDate, scenario.endDate, scenario.options);
    }
}

// 创建全局实例
if (typeof window !== 'undefined') {
    window.DemoDataGenerator = DemoDataGenerator;
    window.demoDataGenerator = new DemoDataGenerator();
}

// 导出（如果在Node.js环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DemoDataGenerator;
}
