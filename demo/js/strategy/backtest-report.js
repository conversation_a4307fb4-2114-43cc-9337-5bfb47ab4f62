/**
 * 回测报告组件
 * 提供详细的统计分析和交易记录展示
 */

class BacktestReport {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.currentResults = null;
        
        this.init();
    }

    init() {
        this.setupReportContainer();
    }

    /**
     * 设置报告容器
     */
    setupReportContainer() {
        this.container.innerHTML = `
            <div class="backtest-report">
                <!-- 报告头部 -->
                <div class="card p-6 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-xl font-bold flex items-center">
                            <i class="fas fa-chart-line mr-3 text-green-400"></i>
                            回测报告
                        </h3>
                        <div class="flex space-x-3">
                            <button class="btn-secondary" onclick="backtestReport.exportReport('excel')">
                                <i class="fas fa-file-excel mr-2"></i>
                                导出Excel
                            </button>
                            <button class="btn-secondary" onclick="backtestReport.exportReport('pdf')">
                                <i class="fas fa-file-pdf mr-2"></i>
                                导出PDF
                            </button>
                            <button class="btn-primary" onclick="backtestReport.shareReport()">
                                <i class="fas fa-share-alt mr-2"></i>
                                分享报告
                            </button>
                        </div>
                    </div>
                    <div id="reportSummary" class="text-gray-400">
                        请先运行回测以生成报告
                    </div>
                </div>

                <!-- 核心指标概览 -->
                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-6" id="coreMetrics">
                    <!-- 动态生成 -->
                </div>

                <!-- 详细统计 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <!-- 收益分析 -->
                    <div class="card p-6">
                        <h4 class="text-lg font-semibold mb-4 flex items-center">
                            <i class="fas fa-chart-bar mr-2 text-green-400"></i>
                            收益分析
                        </h4>
                        <div id="profitAnalysis" class="space-y-3">
                            <!-- 动态生成 -->
                        </div>
                    </div>

                    <!-- 风险分析 -->
                    <div class="card p-6">
                        <h4 class="text-lg font-semibold mb-4 flex items-center">
                            <i class="fas fa-shield-alt mr-2 text-red-400"></i>
                            风险分析
                        </h4>
                        <div id="riskAnalysis" class="space-y-3">
                            <!-- 动态生成 -->
                        </div>
                    </div>
                </div>

                <!-- 策略效果对比 -->
                <div class="card p-6 mb-6">
                    <h4 class="text-lg font-semibold mb-4 flex items-center">
                        <i class="fas fa-balance-scale mr-2 text-blue-400"></i>
                        策略效果对比
                    </h4>
                    <div id="strategyComparison" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 动态生成 -->
                    </div>
                </div>

                <!-- 交易明细 -->
                <div class="card p-6 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="text-lg font-semibold flex items-center">
                            <i class="fas fa-list mr-2 text-purple-400"></i>
                            交易明细
                        </h4>
                        <div class="flex space-x-2">
                            <select id="tradeFilter" class="form-input-sm">
                                <option value="all">全部交易</option>
                                <option value="profitable">盈利交易</option>
                                <option value="losing">亏损交易</option>
                                <option value="takeoff">起飞模式</option>
                                <option value="strong">强势模式</option>
                            </select>
                            <button class="btn-secondary btn-sm" onclick="backtestReport.exportTrades()">
                                <i class="fas fa-download mr-1"></i>
                                导出明细
                            </button>
                        </div>
                    </div>
                    <div id="tradesTable" class="overflow-x-auto">
                        <!-- 动态生成 -->
                    </div>
                </div>

                <!-- 月度分析 -->
                <div class="card p-6 mb-6">
                    <h4 class="text-lg font-semibold mb-4 flex items-center">
                        <i class="fas fa-calendar-alt mr-2 text-indigo-400"></i>
                        月度分析
                    </h4>
                    <div id="monthlyAnalysis">
                        <!-- 动态生成 -->
                    </div>
                </div>

                <!-- 基准对比 -->
                <div class="card p-6">
                    <h4 class="text-lg font-semibold mb-4 flex items-center">
                        <i class="fas fa-chart-line mr-2 text-yellow-400"></i>
                        基准对比
                    </h4>
                    <div id="benchmarkComparison">
                        <div class="text-gray-400 text-center py-8">
                            基准对比功能开发中...
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 生成报告
     */
    generateReport(backtestResults) {
        this.currentResults = backtestResults;
        
        this.updateReportSummary(backtestResults);
        this.updateCoreMetrics(backtestResults.statistics);
        this.updateProfitAnalysis(backtestResults.statistics);
        this.updateRiskAnalysis(backtestResults.statistics);
        this.updateStrategyComparison(backtestResults.statistics);
        this.updateTradesTable(backtestResults.trades);
        this.updateMonthlyAnalysis(backtestResults);
        
        this.bindEvents();
    }

    /**
     * 更新报告摘要
     */
    updateReportSummary(results) {
        const { statistics, config } = results;
        const summary = `
            回测期间: ${results.trades.length > 0 ? results.trades[0].openDate : '无交易'} - 
            ${results.trades.length > 0 ? results.trades[results.trades.length - 1].closeDate : '无交易'} | 
            总交易次数: ${statistics.totalTrades} | 
            胜率: ${statistics.winRate}% | 
            总收益率: ${statistics.totalReturn}% | 
            最大回撤: ${statistics.maxDrawdown}%
        `;
        
        document.getElementById('reportSummary').innerHTML = summary;
    }

    /**
     * 更新核心指标
     */
    updateCoreMetrics(statistics) {
        const metrics = [
            {
                label: '总收益率',
                value: statistics.totalReturn + '%',
                icon: 'fas fa-percentage',
                color: parseFloat(statistics.totalReturn) >= 0 ? 'text-green-400' : 'text-red-400'
            },
            {
                label: '年化收益率',
                value: statistics.annualizedReturn + '%',
                icon: 'fas fa-chart-line',
                color: parseFloat(statistics.annualizedReturn) >= 0 ? 'text-green-400' : 'text-red-400'
            },
            {
                label: '胜率',
                value: statistics.winRate + '%',
                icon: 'fas fa-trophy',
                color: parseFloat(statistics.winRate) >= 50 ? 'text-green-400' : 'text-yellow-400'
            },
            {
                label: '最大回撤',
                value: statistics.maxDrawdown + '%',
                icon: 'fas fa-arrow-down',
                color: 'text-red-400'
            },
            {
                label: '夏普比率',
                value: statistics.sharpeRatio,
                icon: 'fas fa-balance-scale',
                color: parseFloat(statistics.sharpeRatio) >= 1 ? 'text-green-400' : 'text-yellow-400'
            },
            {
                label: '交易次数',
                value: statistics.totalTrades,
                icon: 'fas fa-exchange-alt',
                color: 'text-blue-400'
            }
        ];

        const metricsHtml = metrics.map(metric => `
            <div class="card p-4 text-center">
                <div class="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center mx-auto mb-2">
                    <i class="${metric.icon} ${metric.color}"></i>
                </div>
                <div class="text-lg font-bold mono-font ${metric.color}">${metric.value}</div>
                <div class="text-xs text-gray-400">${metric.label}</div>
            </div>
        `).join('');

        document.getElementById('coreMetrics').innerHTML = metricsHtml;
    }

    /**
     * 更新收益分析
     */
    updateProfitAnalysis(statistics) {
        const profitItems = [
            { label: '总盈利', value: `¥${statistics.totalProfit}`, positive: parseFloat(statistics.totalProfit) >= 0 },
            { label: '盈利交易', value: `${statistics.profitableTrades}次`, positive: true },
            { label: '亏损交易', value: `${statistics.losingTrades}次`, positive: false },
            { label: '平均盈利', value: `¥${statistics.avgProfit}`, positive: true },
            { label: '平均亏损', value: `¥${statistics.avgLoss}`, positive: false },
            { label: '盈亏比', value: statistics.profitLossRatio, positive: parseFloat(statistics.profitLossRatio) >= 1 },
            { label: '平均持仓', value: `${statistics.avgHoldingDays}天`, positive: null }
        ];

        const profitHtml = profitItems.map(item => `
            <div class="flex justify-between items-center">
                <span class="text-gray-300">${item.label}</span>
                <span class="font-mono ${
                    item.positive === true ? 'text-green-400' : 
                    item.positive === false ? 'text-red-400' : 'text-gray-300'
                }">${item.value}</span>
            </div>
        `).join('');

        document.getElementById('profitAnalysis').innerHTML = profitHtml;
    }

    /**
     * 更新风险分析
     */
    updateRiskAnalysis(statistics) {
        const riskItems = [
            { label: '最大回撤', value: `${statistics.maxDrawdown}%` },
            { label: '波动率', value: `${statistics.volatility}%` },
            { label: '夏普比率', value: statistics.sharpeRatio },
            { label: '胜率', value: `${statistics.winRate}%` },
            { label: '最大连续亏损', value: '计算中...' }, // 需要额外计算
            { label: '风险调整收益', value: '计算中...' }, // 需要额外计算
            { label: '卡玛比率', value: '计算中...' } // 需要额外计算
        ];

        const riskHtml = riskItems.map(item => `
            <div class="flex justify-between items-center">
                <span class="text-gray-300">${item.label}</span>
                <span class="font-mono text-gray-300">${item.value}</span>
            </div>
        `).join('');

        document.getElementById('riskAnalysis').innerHTML = riskHtml;
    }

    /**
     * 更新策略对比
     */
    updateStrategyComparison(statistics) {
        const comparisonHtml = `
            <div class="space-y-4">
                <h5 class="font-semibold text-green-400">起飞模式</h5>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span>信号次数</span>
                        <span class="font-mono">${statistics.takeoffStats.count}次</span>
                    </div>
                    <div class="flex justify-between">
                        <span>胜率</span>
                        <span class="font-mono">${statistics.takeoffStats.winRate}%</span>
                    </div>
                    <div class="flex justify-between">
                        <span>平均收益</span>
                        <span class="font-mono">${statistics.takeoffStats.avgReturn}%</span>
                    </div>
                </div>
            </div>
            
            <div class="space-y-4">
                <h5 class="font-semibold text-blue-400">强势模式</h5>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span>信号次数</span>
                        <span class="font-mono">${statistics.strongStats.count}次</span>
                    </div>
                    <div class="flex justify-between">
                        <span>胜率</span>
                        <span class="font-mono">${statistics.strongStats.winRate}%</span>
                    </div>
                    <div class="flex justify-between">
                        <span>平均收益</span>
                        <span class="font-mono">${statistics.strongStats.avgReturn}%</span>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('strategyComparison').innerHTML = comparisonHtml;
    }

    /**
     * 更新交易明细表
     */
    updateTradesTable(trades) {
        if (trades.length === 0) {
            document.getElementById('tradesTable').innerHTML = `
                <div class="text-center py-8 text-gray-400">
                    暂无交易记录
                </div>
            `;
            return;
        }

        const tableHtml = `
            <table class="w-full text-sm">
                <thead>
                    <tr class="border-b border-gray-700">
                        <th class="text-left py-2">开仓日期</th>
                        <th class="text-left py-2">平仓日期</th>
                        <th class="text-left py-2">策略类型</th>
                        <th class="text-right py-2">开仓价</th>
                        <th class="text-right py-2">平仓价</th>
                        <th class="text-right py-2">股数</th>
                        <th class="text-right py-2">持仓天数</th>
                        <th class="text-right py-2">盈亏</th>
                        <th class="text-right py-2">收益率</th>
                        <th class="text-left py-2">退出原因</th>
                    </tr>
                </thead>
                <tbody>
                    ${trades.map(trade => `
                        <tr class="border-b border-gray-800 hover:bg-gray-800">
                            <td class="py-2">${trade.openDate}</td>
                            <td class="py-2">${trade.closeDate}</td>
                            <td class="py-2">
                                <span class="px-2 py-1 rounded text-xs ${
                                    trade.positionType === 'takeoff' ? 'bg-green-600' : 'bg-blue-600'
                                }">
                                    ${trade.positionType === 'takeoff' ? '起飞' : '强势'}
                                </span>
                            </td>
                            <td class="text-right py-2 font-mono">¥${trade.openPrice.toFixed(2)}</td>
                            <td class="text-right py-2 font-mono">¥${trade.closePrice.toFixed(2)}</td>
                            <td class="text-right py-2 font-mono">${trade.shareCount}</td>
                            <td class="text-right py-2 font-mono">${trade.holdingDays}</td>
                            <td class="text-right py-2 font-mono ${trade.profit >= 0 ? 'text-green-400' : 'text-red-400'}">
                                ${trade.profit >= 0 ? '+' : ''}¥${trade.profit.toFixed(2)}
                            </td>
                            <td class="text-right py-2 font-mono ${trade.returnRate >= 0 ? 'text-green-400' : 'text-red-400'}">
                                ${trade.returnRate >= 0 ? '+' : ''}${(trade.returnRate * 100).toFixed(2)}%
                            </td>
                            <td class="py-2">
                                <span class="px-2 py-1 rounded text-xs bg-gray-600">
                                    ${trade.exitReason}
                                </span>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;

        document.getElementById('tradesTable').innerHTML = tableHtml;
    }

    /**
     * 更新月度分析
     */
    updateMonthlyAnalysis(results) {
        // 这里可以添加月度收益统计
        const monthlyHtml = `
            <div class="text-center py-8 text-gray-400">
                月度分析功能开发中...
            </div>
        `;

        document.getElementById('monthlyAnalysis').innerHTML = monthlyHtml;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 交易过滤
        document.getElementById('tradeFilter').addEventListener('change', (e) => {
            this.filterTrades(e.target.value);
        });
    }

    /**
     * 过滤交易
     */
    filterTrades(filterType) {
        if (!this.currentResults) return;

        let filteredTrades = this.currentResults.trades;

        switch (filterType) {
            case 'profitable':
                filteredTrades = this.currentResults.trades.filter(t => t.profit > 0);
                break;
            case 'losing':
                filteredTrades = this.currentResults.trades.filter(t => t.profit < 0);
                break;
            case 'takeoff':
                filteredTrades = this.currentResults.trades.filter(t => t.positionType === 'takeoff');
                break;
            case 'strong':
                filteredTrades = this.currentResults.trades.filter(t => t.positionType === 'strong');
                break;
            default:
                filteredTrades = this.currentResults.trades;
        }

        this.updateTradesTable(filteredTrades);
    }

    /**
     * 导出报告
     */
    exportReport(format) {
        if (!this.currentResults) {
            this.showNotification('暂无报告可导出', 'warning');
            return;
        }

        if (format === 'excel') {
            this.exportToExcel();
        } else if (format === 'pdf') {
            this.exportToPDF();
        }
    }

    /**
     * 导出Excel
     */
    exportToExcel() {
        // 这里可以集成 SheetJS 或其他 Excel 导出库
        const data = {
            statistics: this.currentResults.statistics,
            trades: this.currentResults.trades,
            config: this.currentResults.config
        };

        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `backtest_report_${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);

        this.showNotification('报告已导出为JSON格式', 'success');
    }

    /**
     * 导出PDF
     */
    exportToPDF() {
        // 这里可以集成 jsPDF 或其他 PDF 导出库
        this.showNotification('PDF导出功能开发中...', 'info');
    }

    /**
     * 导出交易明细
     */
    exportTrades() {
        if (!this.currentResults || this.currentResults.trades.length === 0) {
            this.showNotification('暂无交易明细可导出', 'warning');
            return;
        }

        const csvContent = this.tradesToCSV(this.currentResults.trades);
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `trades_${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        URL.revokeObjectURL(url);

        this.showNotification('交易明细已导出', 'success');
    }

    /**
     * 将交易数据转换为CSV
     */
    tradesToCSV(trades) {
        const headers = [
            '开仓日期', '平仓日期', '策略类型', '开仓价', '平仓价', 
            '股数', '持仓天数', '盈亏', '收益率', '退出原因'
        ];

        const rows = trades.map(trade => [
            trade.openDate,
            trade.closeDate,
            trade.positionType === 'takeoff' ? '起飞模式' : '强势模式',
            trade.openPrice.toFixed(2),
            trade.closePrice.toFixed(2),
            trade.shareCount,
            trade.holdingDays,
            trade.profit.toFixed(2),
            (trade.returnRate * 100).toFixed(2) + '%',
            trade.exitReason
        ]);

        return [headers, ...rows].map(row => row.join(',')).join('\n');
    }

    /**
     * 分享报告
     */
    shareReport() {
        if (!this.currentResults) {
            this.showNotification('暂无报告可分享', 'warning');
            return;
        }

        // 生成分享链接或二维码
        this.showNotification('分享功能开发中...', 'info');
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            z-index: 10000;
            max-width: 300px;
            word-wrap: break-word;
        `;
        
        const colors = {
            success: '#10B981',
            error: '#EF4444',
            warning: '#F59E0B',
            info: '#3B82F6'
        };
        
        notification.style.backgroundColor = colors[type] || colors.info;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// 导出类
if (typeof window !== 'undefined') {
    window.BacktestReport = BacktestReport;
}
