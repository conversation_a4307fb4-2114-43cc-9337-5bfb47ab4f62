/**
 * 回测可视化组件
 * 提供K线图信号标注、交易标记和统计图表功能
 */

class BacktestVisualizer {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.charts = {};
        this.currentData = null;
        
        this.init();
    }

    init() {
        this.setupChartContainers();
    }

    /**
     * 设置图表容器
     */
    setupChartContainers() {
        this.container.innerHTML = `
            <div class="backtest-visualizer">
                <!-- K线图和信号标注 -->
                <div class="card p-4 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold">K线图与信号标注</h3>
                        <div class="flex space-x-2">
                            <button class="btn-secondary btn-sm" onclick="this.toggleSignals()">
                                <i class="fas fa-eye mr-1"></i>
                                显示/隐藏信号
                            </button>
                            <button class="btn-secondary btn-sm" onclick="this.toggleTrades()">
                                <i class="fas fa-exchange-alt mr-1"></i>
                                显示/隐藏交易
                            </button>
                        </div>
                    </div>
                    <div id="candlestickChart" style="height: 500px;"></div>
                </div>

                <!-- 资金曲线和回撤 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <div class="card p-4">
                        <h3 class="text-lg font-semibold mb-4">资金曲线</h3>
                        <div id="equityCurveChart" style="height: 300px;"></div>
                    </div>
                    <div class="card p-4">
                        <h3 class="text-lg font-semibold mb-4">回撤曲线</h3>
                        <div id="drawdownChart" style="height: 300px;"></div>
                    </div>
                </div>

                <!-- 收益分布和交易分析 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <div class="card p-4">
                        <h3 class="text-lg font-semibold mb-4">收益分布</h3>
                        <div id="returnDistributionChart" style="height: 300px;"></div>
                    </div>
                    <div class="card p-4">
                        <h3 class="text-lg font-semibold mb-4">持仓天数分布</h3>
                        <div id="holdingPeriodChart" style="height: 300px;"></div>
                    </div>
                </div>

                <!-- 月度收益热力图 -->
                <div class="card p-4 mb-6">
                    <h3 class="text-lg font-semibold mb-4">月度收益热力图</h3>
                    <div id="monthlyReturnsChart" style="height: 400px;"></div>
                </div>
            </div>
        `;
    }

    /**
     * 渲染所有图表
     */
    renderCharts(backtestResults) {
        this.currentData = backtestResults;
        
        this.renderCandlestickChart(backtestResults);
        this.renderEquityCurveChart(backtestResults);
        this.renderDrawdownChart(backtestResults);
        this.renderReturnDistributionChart(backtestResults);
        this.renderHoldingPeriodChart(backtestResults);
        this.renderMonthlyReturnsChart(backtestResults);
    }

    /**
     * 渲染K线图和信号标注
     */
    renderCandlestickChart(backtestResults) {
        const { visualData, trades, signals } = backtestResults;
        const candlestickData = visualData.candlestickData;
        
        // 准备K线数据
        const klineData = candlestickData.map(item => [
            item.date,
            item.open,
            item.close,
            item.low,
            item.high,
            item.volume
        ]);

        // 准备信号标记
        const buySignals = signals.map(signal => ({
            name: signal.date,
            coord: [signal.date, signal.price],
            value: signal.price,
            itemStyle: {
                color: signal.type === 'takeoff' ? '#10B981' : '#3B82F6'
            },
            label: {
                formatter: `{c}\n${signal.mode}`,
                position: 'top',
                color: '#fff',
                backgroundColor: signal.type === 'takeoff' ? '#10B981' : '#3B82F6',
                borderRadius: 4,
                padding: [4, 8]
            }
        }));

        // 准备交易标记
        const sellSignals = trades.map(trade => ({
            name: trade.closeDate,
            coord: [trade.closeDate, trade.closePrice],
            value: trade.closePrice,
            itemStyle: {
                color: trade.profit > 0 ? '#EF4444' : '#6B7280'
            },
            label: {
                formatter: `{c}\n${trade.exitReason}\n${trade.profit > 0 ? '+' : ''}${(trade.returnRate * 100).toFixed(2)}%`,
                position: 'bottom',
                color: '#fff',
                backgroundColor: trade.profit > 0 ? '#EF4444' : '#6B7280',
                borderRadius: 4,
                padding: [4, 8]
            }
        }));

        const option = {
            animation: false,
            legend: {
                data: ['K线', '买入信号', '卖出信号'],
                textStyle: { color: '#9CA3AF' }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                },
                backgroundColor: 'rgba(17, 24, 39, 0.95)',
                borderColor: '#374151',
                textStyle: { color: '#F9FAFB' }
            },
            axisPointer: {
                link: { xAxisIndex: 'all' },
                label: {
                    backgroundColor: '#777'
                }
            },
            grid: [
                {
                    left: '10%',
                    right: '8%',
                    height: '60%'
                },
                {
                    left: '10%',
                    right: '8%',
                    top: '75%',
                    height: '16%'
                }
            ],
            xAxis: [
                {
                    type: 'category',
                    data: candlestickData.map(item => item.date),
                    scale: true,
                    boundaryGap: false,
                    axisLine: { onZero: false },
                    splitLine: { show: false },
                    min: 'dataMin',
                    max: 'dataMax',
                    axisLabel: { color: '#9CA3AF' }
                },
                {
                    type: 'category',
                    gridIndex: 1,
                    data: candlestickData.map(item => item.date),
                    scale: true,
                    boundaryGap: false,
                    axisLine: { onZero: false },
                    axisTick: { show: false },
                    splitLine: { show: false },
                    axisLabel: { show: false },
                    min: 'dataMin',
                    max: 'dataMax'
                }
            ],
            yAxis: [
                {
                    scale: true,
                    splitArea: { show: true },
                    axisLabel: { color: '#9CA3AF' }
                },
                {
                    scale: true,
                    gridIndex: 1,
                    splitNumber: 2,
                    axisLabel: { show: false },
                    axisLine: { show: false },
                    axisTick: { show: false },
                    splitLine: { show: false }
                }
            ],
            dataZoom: [
                {
                    type: 'inside',
                    xAxisIndex: [0, 1],
                    start: 80,
                    end: 100
                },
                {
                    show: true,
                    xAxisIndex: [0, 1],
                    type: 'slider',
                    top: '85%',
                    start: 80,
                    end: 100
                }
            ],
            series: [
                {
                    name: 'K线',
                    type: 'candlestick',
                    data: klineData,
                    itemStyle: {
                        color: '#26A69A',
                        color0: '#EF5350',
                        borderColor: '#26A69A',
                        borderColor0: '#EF5350'
                    },
                    markPoint: {
                        data: [
                            ...buySignals.map(signal => ({
                                ...signal,
                                symbol: 'triangle',
                                symbolSize: 15
                            })),
                            ...sellSignals.map(signal => ({
                                ...signal,
                                symbol: 'triangleDown',
                                symbolSize: 15
                            }))
                        ]
                    }
                },
                {
                    name: '成交量',
                    type: 'bar',
                    xAxisIndex: 1,
                    yAxisIndex: 1,
                    data: candlestickData.map(item => item.volume),
                    itemStyle: {
                        color: function(params) {
                            const dataIndex = params.dataIndex;
                            const kline = klineData[dataIndex];
                            return kline[1] > kline[2] ? '#EF5350' : '#26A69A';
                        }
                    }
                }
            ]
        };

        if (this.charts.candlestick) {
            this.charts.candlestick.dispose();
        }
        this.charts.candlestick = echarts.init(document.getElementById('candlestickChart'), 'dark');
        this.charts.candlestick.setOption(option);
    }

    /**
     * 渲染资金曲线图
     */
    renderEquityCurveChart(backtestResults) {
        const { visualData } = backtestResults;
        const equityData = visualData.equityCurveData;

        const option = {
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(17, 24, 39, 0.95)',
                borderColor: '#374151',
                textStyle: { color: '#F9FAFB' },
                formatter: function(params) {
                    const point = params[0];
                    return `日期: ${point.axisValue}<br/>
                            资金: ¥${Number(point.value).toLocaleString()}<br/>
                            收益率: ${equityData[point.dataIndex].return}%`;
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: equityData.map(item => item.date),
                axisLabel: { color: '#9CA3AF' }
            },
            yAxis: {
                type: 'value',
                axisLabel: { 
                    color: '#9CA3AF',
                    formatter: function(value) {
                        return '¥' + (value / 1000).toFixed(0) + 'K';
                    }
                }
            },
            series: [{
                name: '资金曲线',
                type: 'line',
                data: equityData.map(item => item.value),
                smooth: true,
                lineStyle: {
                    color: '#10B981',
                    width: 2
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(16, 185, 129, 0.3)' },
                        { offset: 1, color: 'rgba(16, 185, 129, 0.05)' }
                    ])
                }
            }]
        };

        if (this.charts.equity) {
            this.charts.equity.dispose();
        }
        this.charts.equity = echarts.init(document.getElementById('equityCurveChart'), 'dark');
        this.charts.equity.setOption(option);
    }

    /**
     * 渲染回撤图
     */
    renderDrawdownChart(backtestResults) {
        const { visualData } = backtestResults;
        const drawdownData = visualData.drawdownData;

        const option = {
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(17, 24, 39, 0.95)',
                borderColor: '#374151',
                textStyle: { color: '#F9FAFB' },
                formatter: function(params) {
                    const point = params[0];
                    return `日期: ${point.axisValue}<br/>
                            回撤: ${point.value.toFixed(2)}%`;
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: drawdownData.map(item => item.date),
                axisLabel: { color: '#9CA3AF' }
            },
            yAxis: {
                type: 'value',
                axisLabel: { 
                    color: '#9CA3AF',
                    formatter: '{value}%'
                }
            },
            series: [{
                name: '回撤',
                type: 'line',
                data: drawdownData.map(item => item.drawdown),
                smooth: true,
                lineStyle: {
                    color: '#EF4444',
                    width: 2
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(239, 68, 68, 0.3)' },
                        { offset: 1, color: 'rgba(239, 68, 68, 0.05)' }
                    ])
                }
            }]
        };

        if (this.charts.drawdown) {
            this.charts.drawdown.dispose();
        }
        this.charts.drawdown = echarts.init(document.getElementById('drawdownChart'), 'dark');
        this.charts.drawdown.setOption(option);
    }

    /**
     * 渲染收益分布图
     */
    renderReturnDistributionChart(backtestResults) {
        const { trades } = backtestResults;
        
        // 计算收益分布
        const returns = trades.map(trade => (trade.returnRate * 100).toFixed(1));
        const returnCounts = {};
        
        // 按区间统计
        const bins = [-10, -5, -2, 0, 2, 5, 10, 20];
        const binLabels = ['<-10%', '-10%~-5%', '-5%~-2%', '-2%~0%', '0%~2%', '2%~5%', '5%~10%', '>10%'];
        const binCounts = new Array(binLabels.length).fill(0);
        
        returns.forEach(ret => {
            const value = parseFloat(ret);
            for (let i = 0; i < bins.length - 1; i++) {
                if (value >= bins[i] && value < bins[i + 1]) {
                    binCounts[i]++;
                    break;
                }
            }
            if (value >= bins[bins.length - 1]) {
                binCounts[binCounts.length - 1]++;
            }
        });

        const option = {
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(17, 24, 39, 0.95)',
                borderColor: '#374151',
                textStyle: { color: '#F9FAFB' }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: binLabels,
                axisLabel: { color: '#9CA3AF' }
            },
            yAxis: {
                type: 'value',
                axisLabel: { color: '#9CA3AF' }
            },
            series: [{
                name: '交易次数',
                type: 'bar',
                data: binCounts.map((count, index) => ({
                    value: count,
                    itemStyle: {
                        color: index < 3 ? '#EF4444' : index === 3 ? '#6B7280' : '#10B981'
                    }
                }))
            }]
        };

        if (this.charts.returnDist) {
            this.charts.returnDist.dispose();
        }
        this.charts.returnDist = echarts.init(document.getElementById('returnDistributionChart'), 'dark');
        this.charts.returnDist.setOption(option);
    }

    /**
     * 渲染持仓天数分布图
     */
    renderHoldingPeriodChart(backtestResults) {
        const { trades } = backtestResults;
        
        // 统计持仓天数
        const holdingDays = {};
        trades.forEach(trade => {
            const days = trade.holdingDays;
            holdingDays[days] = (holdingDays[days] || 0) + 1;
        });

        const sortedDays = Object.keys(holdingDays).sort((a, b) => parseInt(a) - parseInt(b));
        const counts = sortedDays.map(day => holdingDays[day]);

        const option = {
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(17, 24, 39, 0.95)',
                borderColor: '#374151',
                textStyle: { color: '#F9FAFB' }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: sortedDays.map(day => day + '天'),
                axisLabel: { color: '#9CA3AF' }
            },
            yAxis: {
                type: 'value',
                axisLabel: { color: '#9CA3AF' }
            },
            series: [{
                name: '交易次数',
                type: 'bar',
                data: counts,
                itemStyle: {
                    color: '#3B82F6'
                }
            }]
        };

        if (this.charts.holdingPeriod) {
            this.charts.holdingPeriod.dispose();
        }
        this.charts.holdingPeriod = echarts.init(document.getElementById('holdingPeriodChart'), 'dark');
        this.charts.holdingPeriod.setOption(option);
    }

    /**
     * 渲染月度收益热力图
     */
    renderMonthlyReturnsChart(backtestResults) {
        const { visualData } = backtestResults;
        const equityData = visualData.equityCurveData;
        
        // 计算月度收益
        const monthlyReturns = this.calculateMonthlyReturns(equityData);
        
        const option = {
            tooltip: {
                position: 'top',
                backgroundColor: 'rgba(17, 24, 39, 0.95)',
                borderColor: '#374151',
                textStyle: { color: '#F9FAFB' },
                formatter: function(params) {
                    return `${params.data[1]}年${params.data[0]}月<br/>收益率: ${params.data[2]}%`;
                }
            },
            grid: {
                height: '50%',
                top: '10%'
            },
            xAxis: {
                type: 'category',
                data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                splitArea: { show: true },
                axisLabel: { color: '#9CA3AF' }
            },
            yAxis: {
                type: 'category',
                data: monthlyReturns.years,
                splitArea: { show: true },
                axisLabel: { color: '#9CA3AF' }
            },
            visualMap: {
                min: -10,
                max: 10,
                calculable: true,
                orient: 'horizontal',
                left: 'center',
                bottom: '15%',
                inRange: {
                    color: ['#EF4444', '#FFFFFF', '#10B981']
                },
                textStyle: { color: '#9CA3AF' }
            },
            series: [{
                name: '月度收益',
                type: 'heatmap',
                data: monthlyReturns.data,
                label: {
                    show: true,
                    formatter: '{c}%',
                    color: '#000'
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }]
        };

        if (this.charts.monthlyReturns) {
            this.charts.monthlyReturns.dispose();
        }
        this.charts.monthlyReturns = echarts.init(document.getElementById('monthlyReturnsChart'), 'dark');
        this.charts.monthlyReturns.setOption(option);
    }

    /**
     * 计算月度收益
     */
    calculateMonthlyReturns(equityData) {
        const monthlyData = {};
        const years = new Set();
        
        for (let i = 1; i < equityData.length; i++) {
            const currentDate = new Date(equityData[i].date);
            const prevDate = new Date(equityData[i-1].date);
            
            if (currentDate.getMonth() !== prevDate.getMonth() || 
                currentDate.getFullYear() !== prevDate.getFullYear()) {
                
                const year = prevDate.getFullYear();
                const month = prevDate.getMonth();
                const key = `${year}-${month}`;
                
                if (!monthlyData[key]) {
                    const monthStart = equityData.find(item => {
                        const date = new Date(item.date);
                        return date.getFullYear() === year && date.getMonth() === month;
                    });
                    
                    if (monthStart) {
                        const monthReturn = ((equityData[i-1].value - monthStart.value) / monthStart.value * 100).toFixed(2);
                        monthlyData[key] = parseFloat(monthReturn);
                        years.add(year);
                    }
                }
            }
        }
        
        const sortedYears = Array.from(years).sort();
        const data = [];
        
        sortedYears.forEach(year => {
            for (let month = 0; month < 12; month++) {
                const key = `${year}-${month}`;
                const value = monthlyData[key] || 0;
                data.push([month, year, value]);
            }
        });
        
        return {
            years: sortedYears,
            data
        };
    }

    /**
     * 切换信号显示
     */
    toggleSignals() {
        // 实现信号显示/隐藏逻辑
        console.log('Toggle signals');
    }

    /**
     * 切换交易标记显示
     */
    toggleTrades() {
        // 实现交易标记显示/隐藏逻辑
        console.log('Toggle trades');
    }

    /**
     * 销毁所有图表
     */
    dispose() {
        Object.values(this.charts).forEach(chart => {
            if (chart && chart.dispose) {
                chart.dispose();
            }
        });
        this.charts = {};
    }
}

// 导出类
if (typeof window !== 'undefined') {
    window.BacktestVisualizer = BacktestVisualizer;
}
