/**
 * 策略结果显示组件
 * 提供策略回测结果、收益统计和图表展示功能
 */

class StrategyResults {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.charts = {};
        this.currentResults = null;
        
        this.init();
    }

    init() {
        this.render();
        this.bindEvents();
    }

    render() {
        this.container.innerHTML = `
            <div class="strategy-results">
                <!-- 回测控制面板 -->
                <div class="card p-6 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold flex items-center">
                            <i class="fas fa-chart-bar mr-2 text-purple-400"></i>
                            策略回测
                        </h3>
                        <div class="flex space-x-3">
                            <button class="btn-secondary" onclick="this.exportResults()">
                                <i class="fas fa-download mr-2"></i>
                                导出结果
                            </button>
                            <button class="btn-primary" onclick="this.runBacktest()">
                                <i class="fas fa-play mr-2"></i>
                                开始回测
                            </button>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm text-gray-400 mb-2">回测股票</label>
                            <select class="input-field w-full" id="backtestStock">
                                <option value="">选择股票</option>
                                <option value="000001">000001 平安银行</option>
                                <option value="000002">000002 万科A</option>
                                <option value="600036">600036 招商银行</option>
                                <option value="600519">600519 贵州茅台</option>
                                <option value="000858">000858 五粮液</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm text-gray-400 mb-2">回测周期</label>
                            <select class="input-field w-full" id="backtestPeriod">
                                <option value="30">最近30天</option>
                                <option value="60">最近60天</option>
                                <option value="120" selected>最近120天</option>
                                <option value="250">最近250天</option>
                                <option value="500">最近500天</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm text-gray-400 mb-2">初始资金</label>
                            <input type="number" class="input-field w-full" 
                                   id="initialCapital" value="100000" min="10000" step="10000">
                        </div>
                        <div>
                            <label class="block text-sm text-gray-400 mb-2">手续费率 (%)</label>
                            <input type="number" class="input-field w-full" 
                                   id="commissionRate" value="0.1" min="0" max="1" step="0.01">
                        </div>
                    </div>
                </div>

                <!-- 回测结果概览 -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6" id="resultsOverview">
                    <div class="card p-6 text-center">
                        <div class="w-12 h-12 gradient-success rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-percentage text-white"></i>
                        </div>
                        <div class="text-2xl font-bold mono-font mb-1" id="totalReturn">--</div>
                        <div class="text-sm text-gray-400">总收益率</div>
                    </div>
                    
                    <div class="card p-6 text-center">
                        <div class="w-12 h-12 gradient-info rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-chart-line text-white"></i>
                        </div>
                        <div class="text-2xl font-bold mono-font mb-1" id="annualizedReturn">--</div>
                        <div class="text-sm text-gray-400">年化收益率</div>
                    </div>
                    
                    <div class="card p-6 text-center">
                        <div class="w-12 h-12 gradient-warning rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-exclamation-triangle text-white"></i>
                        </div>
                        <div class="text-2xl font-bold mono-font mb-1" id="maxDrawdown">--</div>
                        <div class="text-sm text-gray-400">最大回撤</div>
                    </div>
                    
                    <div class="card p-6 text-center">
                        <div class="w-12 h-12 gradient-primary rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-trophy text-white"></i>
                        </div>
                        <div class="text-2xl font-bold mono-font mb-1" id="sharpeRatio">--</div>
                        <div class="text-sm text-gray-400">夏普比率</div>
                    </div>
                </div>

                <!-- 详细统计 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <!-- 交易统计 -->
                    <div class="card p-6">
                        <h4 class="text-lg font-semibold mb-4 flex items-center">
                            <i class="fas fa-calculator mr-2 text-blue-400"></i>
                            交易统计
                        </h4>
                        <div class="space-y-3" id="tradeStats">
                            <div class="flex justify-between">
                                <span class="text-gray-400">总交易次数</span>
                                <span class="mono-font" id="totalTrades">--</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">盈利交易</span>
                                <span class="mono-font price-up" id="profitableTrades">--</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">亏损交易</span>
                                <span class="mono-font price-down" id="losingTrades">--</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">胜率</span>
                                <span class="mono-font" id="winRate">--</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">平均盈利</span>
                                <span class="mono-font price-up" id="avgProfit">--</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">平均亏损</span>
                                <span class="mono-font price-down" id="avgLoss">--</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">盈亏比</span>
                                <span class="mono-font" id="profitLossRatio">--</span>
                            </div>
                        </div>
                    </div>

                    <!-- 策略统计 -->
                    <div class="card p-6">
                        <h4 class="text-lg font-semibold mb-4 flex items-center">
                            <i class="fas fa-cogs mr-2 text-green-400"></i>
                            策略统计
                        </h4>
                        <div class="space-y-3" id="strategyStats">
                            <div class="flex justify-between">
                                <span class="text-gray-400">起飞信号次数</span>
                                <span class="mono-font" id="takeoffSignals">--</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">强势信号次数</span>
                                <span class="mono-font" id="strongSignals">--</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">起飞模式胜率</span>
                                <span class="mono-font" id="takeoffWinRate">--</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">强势模式胜率</span>
                                <span class="mono-font" id="strongWinRate">--</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">平均持仓时间</span>
                                <span class="mono-font" id="avgHoldingPeriod">--</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">时间止损次数</span>
                                <span class="mono-font" id="timeStopLoss">--</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-400">信号频率</span>
                                <span class="mono-font" id="signalFrequency">--</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 收益曲线图表 -->
                <div class="card p-6 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="text-lg font-semibold flex items-center">
                            <i class="fas fa-chart-area mr-2 text-purple-400"></i>
                            收益曲线
                        </h4>
                        <div class="flex space-x-2">
                            <button class="btn-secondary text-sm" onclick="this.toggleBenchmark()">
                                <i class="fas fa-layer-group mr-1"></i>
                                对比基准
                            </button>
                            <button class="btn-secondary text-sm" onclick="this.toggleDrawdown()">
                                <i class="fas fa-chart-line mr-1"></i>
                                显示回撤
                            </button>
                        </div>
                    </div>
                    <div class="chart-container" id="equityCurveChart"></div>
                </div>

                <!-- 信号分布图表 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <div class="card p-6">
                        <h4 class="text-lg font-semibold mb-4 flex items-center">
                            <i class="fas fa-signal mr-2 text-yellow-400"></i>
                            信号分布
                        </h4>
                        <div class="chart-container" id="signalDistributionChart"></div>
                    </div>

                    <div class="card p-6">
                        <h4 class="text-lg font-semibold mb-4 flex items-center">
                            <i class="fas fa-clock mr-2 text-cyan-400"></i>
                            持仓时间分布
                        </h4>
                        <div class="chart-container" id="holdingPeriodChart"></div>
                    </div>
                </div>

                <!-- 交易明细表 -->
                <div class="card p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h4 class="text-lg font-semibold flex items-center">
                            <i class="fas fa-list mr-2 text-orange-400"></i>
                            交易明细
                        </h4>
                        <div class="flex space-x-2">
                            <select class="input-field text-sm" id="tradeFilter">
                                <option value="all">全部交易</option>
                                <option value="profitable">盈利交易</option>
                                <option value="losing">亏损交易</option>
                                <option value="takeoff">起飞模式</option>
                                <option value="strong">强势模式</option>
                            </select>
                            <button class="btn-secondary text-sm" onclick="this.exportTrades()">
                                <i class="fas fa-download mr-1"></i>
                                导出明细
                            </button>
                        </div>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="data-table w-full">
                            <thead>
                                <tr>
                                    <th>开仓时间</th>
                                    <th>平仓时间</th>
                                    <th>模式</th>
                                    <th>开仓价</th>
                                    <th>平仓价</th>
                                    <th>持仓天数</th>
                                    <th>收益率</th>
                                    <th>收益金额</th>
                                    <th>平仓原因</th>
                                </tr>
                            </thead>
                            <tbody id="tradeDetailsTable">
                                <tr>
                                    <td colspan="9" class="text-center text-gray-400 py-8">
                                        请先运行回测
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // 交易过滤器
        const tradeFilter = document.getElementById('tradeFilter');
        if (tradeFilter) {
            tradeFilter.addEventListener('change', () => {
                this.filterTrades(tradeFilter.value);
            });
        }
    }

    runBacktest() {
        const stockCode = document.getElementById('backtestStock').value;
        const period = parseInt(document.getElementById('backtestPeriod').value);
        const initialCapital = parseFloat(document.getElementById('initialCapital').value);
        const commissionRate = parseFloat(document.getElementById('commissionRate').value) / 100;

        if (!stockCode) {
            this.showNotification('请选择回测股票', 'warning');
            return;
        }

        // 显示加载状态
        this.showLoadingState();

        // 模拟回测过程
        setTimeout(() => {
            const results = this.simulateBacktest(stockCode, period, initialCapital, commissionRate);
            this.displayResults(results);
            this.showNotification('回测完成', 'success');
        }, 2000);
    }

    simulateBacktest(stockCode, period, initialCapital, commissionRate) {
        // 模拟回测结果
        const totalReturn = (Math.random() * 40 - 10); // -10% 到 30%
        const annualizedReturn = totalReturn * (365 / period);
        const maxDrawdown = -(Math.random() * 15 + 5); // -5% 到 -20%
        const sharpeRatio = Math.random() * 2 + 0.5; // 0.5 到 2.5

        const totalTrades = Math.floor(Math.random() * 20 + 10);
        const profitableTrades = Math.floor(totalTrades * (0.4 + Math.random() * 0.4));
        const losingTrades = totalTrades - profitableTrades;
        const winRate = (profitableTrades / totalTrades * 100);

        const takeoffSignals = Math.floor(totalTrades * 0.6);
        const strongSignals = totalTrades - takeoffSignals;

        return {
            overview: {
                totalReturn: totalReturn.toFixed(2),
                annualizedReturn: annualizedReturn.toFixed(2),
                maxDrawdown: maxDrawdown.toFixed(2),
                sharpeRatio: sharpeRatio.toFixed(2)
            },
            tradeStats: {
                totalTrades,
                profitableTrades,
                losingTrades,
                winRate: winRate.toFixed(1),
                avgProfit: (Math.random() * 5 + 2).toFixed(2),
                avgLoss: -(Math.random() * 3 + 1).toFixed(2),
                profitLossRatio: (Math.random() * 2 + 1).toFixed(2)
            },
            strategyStats: {
                takeoffSignals,
                strongSignals,
                takeoffWinRate: (Math.random() * 30 + 50).toFixed(1),
                strongWinRate: (Math.random() * 20 + 60).toFixed(1),
                avgHoldingPeriod: (Math.random() * 5 + 3).toFixed(1),
                timeStopLoss: Math.floor(totalTrades * 0.3),
                signalFrequency: (period / totalTrades).toFixed(1)
            },
            trades: this.generateTradeDetails(totalTrades, stockCode),
            equityCurve: this.generateEquityCurve(period, initialCapital, totalReturn)
        };
    }

    generateTradeDetails(count, stockCode) {
        const trades = [];
        const basePrice = Math.random() * 50 + 10;
        
        for (let i = 0; i < count; i++) {
            const openDate = new Date(Date.now() - (count - i) * 24 * 60 * 60 * 1000 * Math.random() * 5);
            const holdingDays = Math.floor(Math.random() * 10 + 1);
            const closeDate = new Date(openDate.getTime() + holdingDays * 24 * 60 * 60 * 1000);
            
            const openPrice = basePrice * (0.9 + Math.random() * 0.2);
            const returnRate = (Math.random() * 20 - 5); // -5% 到 15%
            const closePrice = openPrice * (1 + returnRate / 100);
            
            const mode = Math.random() > 0.6 ? 'takeoff' : 'strong';
            const exitReason = returnRate > 0 ? '目标止盈' : 
                             Math.random() > 0.5 ? '时间止损' : '价格止损';
            
            trades.push({
                openDate: openDate.toLocaleDateString(),
                closeDate: closeDate.toLocaleDateString(),
                mode: mode === 'takeoff' ? '起飞模式' : '强势模式',
                openPrice: openPrice.toFixed(2),
                closePrice: closePrice.toFixed(2),
                holdingDays,
                returnRate: returnRate.toFixed(2),
                profit: (returnRate * 1000).toFixed(0), // 假设每次投入1000元
                exitReason
            });
        }
        
        return trades;
    }

    generateEquityCurve(period, initialCapital, totalReturn) {
        const points = [];
        const dailyReturn = totalReturn / period / 100;
        let equity = initialCapital;
        
        for (let i = 0; i <= period; i++) {
            const volatility = (Math.random() - 0.5) * 0.02; // 日波动率
            const dayReturn = dailyReturn + volatility;
            equity *= (1 + dayReturn);
            
            points.push({
                date: new Date(Date.now() - (period - i) * 24 * 60 * 60 * 1000).toLocaleDateString(),
                equity: equity.toFixed(0),
                benchmark: (initialCapital * (1 + i * 0.0001)).toFixed(0) // 简单基准
            });
        }
        
        return points;
    }

    displayResults(results) {
        this.currentResults = results;
        
        // 更新概览数据
        document.getElementById('totalReturn').textContent = results.overview.totalReturn + '%';
        document.getElementById('annualizedReturn').textContent = results.overview.annualizedReturn + '%';
        document.getElementById('maxDrawdown').textContent = results.overview.maxDrawdown + '%';
        document.getElementById('sharpeRatio').textContent = results.overview.sharpeRatio;
        
        // 更新交易统计
        const tradeStats = results.tradeStats;
        document.getElementById('totalTrades').textContent = tradeStats.totalTrades;
        document.getElementById('profitableTrades').textContent = tradeStats.profitableTrades;
        document.getElementById('losingTrades').textContent = tradeStats.losingTrades;
        document.getElementById('winRate').textContent = tradeStats.winRate + '%';
        document.getElementById('avgProfit').textContent = tradeStats.avgProfit + '%';
        document.getElementById('avgLoss').textContent = tradeStats.avgLoss + '%';
        document.getElementById('profitLossRatio').textContent = tradeStats.profitLossRatio;
        
        // 更新策略统计
        const strategyStats = results.strategyStats;
        document.getElementById('takeoffSignals').textContent = strategyStats.takeoffSignals;
        document.getElementById('strongSignals').textContent = strategyStats.strongSignals;
        document.getElementById('takeoffWinRate').textContent = strategyStats.takeoffWinRate + '%';
        document.getElementById('strongWinRate').textContent = strategyStats.strongWinRate + '%';
        document.getElementById('avgHoldingPeriod').textContent = strategyStats.avgHoldingPeriod + '天';
        document.getElementById('timeStopLoss').textContent = strategyStats.timeStopLoss;
        document.getElementById('signalFrequency').textContent = strategyStats.signalFrequency + '天/次';
        
        // 更新交易明细表
        this.updateTradeDetailsTable(results.trades);
        
        // 绘制图表
        this.drawEquityCurve(results.equityCurve);
        this.drawSignalDistribution(results.strategyStats);
        this.drawHoldingPeriodChart(results.trades);
        
        // 应用颜色样式
        this.applyResultColors(results.overview);
    }

    updateTradeDetailsTable(trades) {
        const table = document.getElementById('tradeDetailsTable');
        table.innerHTML = '';
        
        trades.forEach(trade => {
            const row = document.createElement('tr');
            const profitClass = parseFloat(trade.returnRate) > 0 ? 'price-up' : 'price-down';
            
            row.innerHTML = `
                <td class="mono-font">${trade.openDate}</td>
                <td class="mono-font">${trade.closeDate}</td>
                <td>
                    <span class="signal-indicator ${trade.mode === '起飞模式' ? 'signal-buy' : 'signal-sell'}">
                        <i class="fas fa-${trade.mode === '起飞模式' ? 'rocket' : 'fire'}"></i>
                        ${trade.mode}
                    </span>
                </td>
                <td class="mono-font">¥${trade.openPrice}</td>
                <td class="mono-font">¥${trade.closePrice}</td>
                <td class="mono-font">${trade.holdingDays}天</td>
                <td class="mono-font ${profitClass}">${trade.returnRate}%</td>
                <td class="mono-font ${profitClass}">¥${trade.profit}</td>
                <td class="text-sm">${trade.exitReason}</td>
            `;
            
            table.appendChild(row);
        });
    }

    drawEquityCurve(equityCurve) {
        // 这里应该使用ECharts绘制收益曲线
        // 暂时显示占位符
        const container = document.getElementById('equityCurveChart');
        container.innerHTML = `
            <div class="flex items-center justify-center h-full text-gray-400">
                <div class="text-center">
                    <i class="fas fa-chart-area text-4xl mb-4"></i>
                    <p>收益曲线图表</p>
                    <p class="text-sm">数据点: ${equityCurve.length}</p>
                </div>
            </div>
        `;
    }

    drawSignalDistribution(strategyStats) {
        const container = document.getElementById('signalDistributionChart');
        container.innerHTML = `
            <div class="flex items-center justify-center h-full text-gray-400">
                <div class="text-center">
                    <i class="fas fa-chart-pie text-4xl mb-4"></i>
                    <p>信号分布图表</p>
                    <p class="text-sm">起飞: ${strategyStats.takeoffSignals}, 强势: ${strategyStats.strongSignals}</p>
                </div>
            </div>
        `;
    }

    drawHoldingPeriodChart(trades) {
        const container = document.getElementById('holdingPeriodChart');
        container.innerHTML = `
            <div class="flex items-center justify-center h-full text-gray-400">
                <div class="text-center">
                    <i class="fas fa-chart-bar text-4xl mb-4"></i>
                    <p>持仓时间分布</p>
                    <p class="text-sm">交易数: ${trades.length}</p>
                </div>
            </div>
        `;
    }

    applyResultColors(overview) {
        const totalReturnEl = document.getElementById('totalReturn');
        const annualizedReturnEl = document.getElementById('annualizedReturn');
        
        const totalReturn = parseFloat(overview.totalReturn);
        const colorClass = totalReturn > 0 ? 'price-up' : totalReturn < 0 ? 'price-down' : 'price-flat';
        
        totalReturnEl.className = `text-2xl font-bold mono-font mb-1 ${colorClass}`;
        annualizedReturnEl.className = `text-2xl font-bold mono-font mb-1 ${colorClass}`;
    }

    showLoadingState() {
        const overview = document.getElementById('resultsOverview');
        overview.innerHTML = `
            <div class="col-span-4 flex items-center justify-center py-12">
                <div class="text-center">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                    <p class="text-gray-400">正在运行回测...</p>
                </div>
            </div>
        `;
    }

    filterTrades(filter) {
        if (!this.currentResults) return;
        
        let filteredTrades = this.currentResults.trades;
        
        switch (filter) {
            case 'profitable':
                filteredTrades = filteredTrades.filter(t => parseFloat(t.returnRate) > 0);
                break;
            case 'losing':
                filteredTrades = filteredTrades.filter(t => parseFloat(t.returnRate) < 0);
                break;
            case 'takeoff':
                filteredTrades = filteredTrades.filter(t => t.mode === '起飞模式');
                break;
            case 'strong':
                filteredTrades = filteredTrades.filter(t => t.mode === '强势模式');
                break;
        }
        
        this.updateTradeDetailsTable(filteredTrades);
    }

    exportResults() {
        if (!this.currentResults) {
            this.showNotification('暂无结果可导出', 'warning');
            return;
        }
        
        // 导出回测结果
        const data = {
            overview: this.currentResults.overview,
            tradeStats: this.currentResults.tradeStats,
            strategyStats: this.currentResults.strategyStats,
            trades: this.currentResults.trades
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `backtest_results_${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
        
        this.showNotification('回测结果已导出', 'success');
    }

    exportTrades() {
        if (!this.currentResults) {
            this.showNotification('暂无交易明细可导出', 'warning');
            return;
        }
        
        let csv = '开仓时间,平仓时间,模式,开仓价,平仓价,持仓天数,收益率,收益金额,平仓原因\n';
        this.currentResults.trades.forEach(trade => {
            csv += `${trade.openDate},${trade.closeDate},${trade.mode},${trade.openPrice},${trade.closePrice},${trade.holdingDays},${trade.returnRate}%,¥${trade.profit},${trade.exitReason}\n`;
        });
        
        const blob = new Blob([csv], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `trade_details_${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        URL.revokeObjectURL(url);
        
        this.showNotification('交易明细已导出', 'success');
    }

    toggleBenchmark() {
        this.showNotification('基准对比功能开发中', 'info');
    }

    toggleDrawdown() {
        this.showNotification('回撤显示功能开发中', 'info');
    }

    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
            type === 'success' ? 'bg-green-600' : 
            type === 'warning' ? 'bg-yellow-600' : 
            type === 'error' ? 'bg-red-600' : 'bg-blue-600'
        } text-white`;
        notification.innerHTML = `
            <div class="flex items-center space-x-2">
                <i class="fas fa-${
                    type === 'success' ? 'check-circle' : 
                    type === 'warning' ? 'exclamation-triangle' : 
                    type === 'error' ? 'times-circle' : 'info-circle'
                }"></i>
                <span>${message}</span>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // 自动移除
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// 全局策略结果实例
window.strategyResults = null;
