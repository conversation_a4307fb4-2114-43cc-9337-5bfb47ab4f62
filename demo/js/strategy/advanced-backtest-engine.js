/**
 * 高级策略回测引擎
 * 提供完整的交易模拟、信号标注和统计分析功能
 */

class AdvancedBacktestEngine {
    constructor(strategyEngine, config = {}) {
        this.strategyEngine = strategyEngine;
        this.config = {
            // 交易参数
            initialCapital: 100000,         // 初始资金
            shareSize: 100,                 // 每次交易股数
            positionSizeType: 'fixed',      // 'fixed' | 'percentage'
            maxPositionRatio: 0.3,          // 最大持仓比例
            
            // 费用设置
            commissionRate: 0.001,          // 手续费率 (0.1%)
            stampTaxRate: 0.001,            // 印花税率 (0.1%, 仅卖出)
            slippage: 0.001,                // 滑点 (0.1%)
            
            // 策略参数
            takeoffPositionRatio: 1/3,      // 起飞模式仓位比例
            strongPositionRatio: 1,         // 强势模式仓位比例
            targetReturn: 0.005,            // 目标收益率 (0.5%)
            stopLossTimeout: [3, 5],        // 时间止损范围
            
            ...config
        };
        
        this.trades = [];
        this.positions = [];
        this.equityCurve = [];
        this.signals = [];
        this.currentCapital = this.config.initialCapital;
        this.maxCapital = this.config.initialCapital;
        this.maxDrawdown = 0;
    }

    /**
     * 运行完整回测
     */
    runBacktest(stockData, startDate = null, endDate = null) {
        this.reset();
        
        // 获取策略信号
        this.strategyEngine.setCurrentData(stockData);
        const strategyResults = this.strategyEngine.analyze(stockData);
        this.signals = strategyResults.signals;
        
        // 过滤时间范围
        const filteredData = this.filterDataByDateRange(stockData, startDate, endDate);
        const filteredSignals = this.filterSignalsByDateRange(this.signals, startDate, endDate);
        
        // 模拟交易执行
        this.simulateTrading(filteredData, filteredSignals);
        
        // 计算统计指标
        const statistics = this.calculateStatistics();
        
        // 生成可视化数据
        const visualData = this.generateVisualizationData(filteredData, filteredSignals);
        
        return {
            trades: this.trades,
            positions: this.positions,
            equityCurve: this.equityCurve,
            signals: this.signals,
            statistics,
            visualData,
            config: this.config
        };
    }

    /**
     * 重置回测状态
     */
    reset() {
        this.trades = [];
        this.positions = [];
        this.equityCurve = [];
        this.signals = [];
        this.currentCapital = this.config.initialCapital;
        this.maxCapital = this.config.initialCapital;
        this.maxDrawdown = 0;
    }

    /**
     * 模拟交易执行
     */
    simulateTrading(stockData, signals) {
        const { dates, closes } = stockData;
        let currentPosition = null;
        
        // 初始化资金曲线
        this.equityCurve.push({
            date: dates[0],
            capital: this.currentCapital,
            drawdown: 0
        });
        
        for (let i = 0; i < dates.length; i++) {
            const currentDate = dates[i];
            const currentPrice = closes[i];
            
            // 检查是否有新信号
            const signal = signals.find(s => s.date === currentDate);
            if (signal && !currentPosition) {
                currentPosition = this.openPosition(signal, currentPrice, currentDate, i);
            }
            
            // 检查现有持仓的退出条件
            if (currentPosition) {
                const exitResult = this.checkExitConditions(currentPosition, currentPrice, currentDate, i, stockData);
                if (exitResult.shouldExit) {
                    this.closePosition(currentPosition, currentPrice, currentDate, exitResult.reason);
                    currentPosition = null;
                }
            }
            
            // 更新资金曲线
            this.updateEquityCurve(currentDate, currentPosition, currentPrice);
        }
        
        // 如果最后还有持仓，强制平仓
        if (currentPosition) {
            const lastPrice = closes[closes.length - 1];
            const lastDate = dates[dates.length - 1];
            this.closePosition(currentPosition, lastPrice, lastDate, '回测结束');
        }
    }

    /**
     * 开仓
     */
    openPosition(signal, price, date, index) {
        const positionRatio = signal.type === 'takeoff' ? 
            this.config.takeoffPositionRatio : this.config.strongPositionRatio;
        
        let shareCount;
        if (this.config.positionSizeType === 'fixed') {
            shareCount = this.config.shareSize;
        } else {
            const availableCapital = this.currentCapital * this.config.maxPositionRatio * positionRatio;
            shareCount = Math.floor(availableCapital / price / 100) * 100; // 按手取整
        }
        
        if (shareCount <= 0) return null;
        
        // 计算交易成本
        const tradeValue = shareCount * price;
        const commission = tradeValue * this.config.commissionRate;
        const slippageCost = tradeValue * this.config.slippage;
        const totalCost = tradeValue + commission + slippageCost;
        
        if (totalCost > this.currentCapital) {
            shareCount = Math.floor((this.currentCapital * 0.95) / price / 100) * 100;
            if (shareCount <= 0) return null;
        }
        
        const position = {
            id: `pos_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            signal,
            openDate: date,
            openIndex: index,
            openPrice: price,
            shareCount,
            positionType: signal.type,
            openValue: shareCount * price,
            openCost: commission + slippageCost,
            holdingDays: 0
        };
        
        this.currentCapital -= totalCost;
        this.positions.push(position);
        
        return position;
    }

    /**
     * 检查退出条件
     */
    checkExitConditions(position, currentPrice, currentDate, currentIndex, stockData) {
        position.holdingDays = currentIndex - position.openIndex;
        
        // 计算当前收益率
        const currentReturn = (currentPrice - position.openPrice) / position.openPrice;
        
        // 目标止盈
        if (currentReturn >= this.config.targetReturn) {
            return { shouldExit: true, reason: '目标止盈' };
        }
        
        // 时间止损
        const [minDays, maxDays] = this.config.stopLossTimeout;
        if (position.holdingDays >= minDays && currentReturn < 0) {
            return { shouldExit: true, reason: '时间止损' };
        }
        if (position.holdingDays >= maxDays) {
            return { shouldExit: true, reason: '时间止损' };
        }
        
        // 止损（可以根据需要添加更多止损条件）
        if (currentReturn <= -0.05) { // 5%止损
            return { shouldExit: true, reason: '价格止损' };
        }
        
        return { shouldExit: false };
    }

    /**
     * 平仓
     */
    closePosition(position, price, date, reason) {
        const tradeValue = position.shareCount * price;
        const commission = tradeValue * this.config.commissionRate;
        const stampTax = tradeValue * this.config.stampTaxRate; // 卖出时收印花税
        const slippageCost = tradeValue * this.config.slippage;
        const totalCost = commission + stampTax + slippageCost;
        
        const netValue = tradeValue - totalCost;
        const profit = netValue - position.openValue - position.openCost;
        const returnRate = profit / (position.openValue + position.openCost);
        
        const trade = {
            id: position.id,
            openDate: position.openDate,
            closeDate: date,
            openPrice: position.openPrice,
            closePrice: price,
            shareCount: position.shareCount,
            holdingDays: position.holdingDays,
            positionType: position.positionType,
            openValue: position.openValue,
            closeValue: tradeValue,
            openCost: position.openCost,
            closeCost: totalCost,
            profit,
            returnRate,
            exitReason: reason,
            signal: position.signal
        };
        
        this.currentCapital += netValue;
        this.trades.push(trade);
        
        // 从持仓中移除
        const index = this.positions.findIndex(p => p.id === position.id);
        if (index !== -1) {
            this.positions.splice(index, 1);
        }
    }

    /**
     * 更新资金曲线
     */
    updateEquityCurve(date, position, currentPrice) {
        let totalValue = this.currentCapital;
        
        // 加上持仓市值
        if (position) {
            totalValue += position.shareCount * currentPrice;
        }
        
        // 更新最大资金
        if (totalValue > this.maxCapital) {
            this.maxCapital = totalValue;
        }
        
        // 计算回撤
        const drawdown = (this.maxCapital - totalValue) / this.maxCapital;
        if (drawdown > this.maxDrawdown) {
            this.maxDrawdown = drawdown;
        }
        
        this.equityCurve.push({
            date,
            capital: totalValue,
            drawdown: drawdown * 100 // 转换为百分比
        });
    }

    /**
     * 计算统计指标
     */
    calculateStatistics() {
        if (this.trades.length === 0) {
            return this.getEmptyStatistics();
        }
        
        const profitableTrades = this.trades.filter(t => t.profit > 0);
        const losingTrades = this.trades.filter(t => t.profit < 0);
        
        const totalReturn = (this.currentCapital - this.config.initialCapital) / this.config.initialCapital;
        const totalDays = this.equityCurve.length;
        const annualizedReturn = totalReturn * (365 / totalDays);
        
        // 计算夏普比率
        const returns = this.equityCurve.slice(1).map((point, i) => 
            (point.capital - this.equityCurve[i].capital) / this.equityCurve[i].capital
        );
        const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
        const returnStd = Math.sqrt(returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length);
        const sharpeRatio = returnStd > 0 ? (avgReturn / returnStd) * Math.sqrt(252) : 0;
        
        // 按策略类型分组统计
        const takeoffTrades = this.trades.filter(t => t.positionType === 'takeoff');
        const strongTrades = this.trades.filter(t => t.positionType === 'strong');
        
        return {
            // 基础统计
            totalTrades: this.trades.length,
            profitableTrades: profitableTrades.length,
            losingTrades: losingTrades.length,
            winRate: (profitableTrades.length / this.trades.length * 100).toFixed(2),
            
            // 收益统计
            totalReturn: (totalReturn * 100).toFixed(2),
            annualizedReturn: (annualizedReturn * 100).toFixed(2),
            totalProfit: this.trades.reduce((sum, t) => sum + t.profit, 0).toFixed(2),
            
            // 风险指标
            maxDrawdown: (this.maxDrawdown * 100).toFixed(2),
            sharpeRatio: sharpeRatio.toFixed(2),
            volatility: (returnStd * Math.sqrt(252) * 100).toFixed(2),
            
            // 交易分析
            avgProfit: profitableTrades.length > 0 ? 
                (profitableTrades.reduce((sum, t) => sum + t.profit, 0) / profitableTrades.length).toFixed(2) : '0',
            avgLoss: losingTrades.length > 0 ? 
                (losingTrades.reduce((sum, t) => sum + t.profit, 0) / losingTrades.length).toFixed(2) : '0',
            profitLossRatio: losingTrades.length > 0 ? 
                (Math.abs(profitableTrades.reduce((sum, t) => sum + t.profit, 0) / profitableTrades.length) / 
                 Math.abs(losingTrades.reduce((sum, t) => sum + t.profit, 0) / losingTrades.length)).toFixed(2) : '0',
            avgHoldingDays: (this.trades.reduce((sum, t) => sum + t.holdingDays, 0) / this.trades.length).toFixed(1),
            
            // 策略效果对比
            takeoffStats: {
                count: takeoffTrades.length,
                winRate: takeoffTrades.length > 0 ? 
                    (takeoffTrades.filter(t => t.profit > 0).length / takeoffTrades.length * 100).toFixed(2) : '0',
                avgReturn: takeoffTrades.length > 0 ? 
                    (takeoffTrades.reduce((sum, t) => sum + t.returnRate, 0) / takeoffTrades.length * 100).toFixed(2) : '0'
            },
            strongStats: {
                count: strongTrades.length,
                winRate: strongTrades.length > 0 ? 
                    (strongTrades.filter(t => t.profit > 0).length / strongTrades.length * 100).toFixed(2) : '0',
                avgReturn: strongTrades.length > 0 ? 
                    (strongTrades.reduce((sum, t) => sum + t.returnRate, 0) / strongTrades.length * 100).toFixed(2) : '0'
            },
            
            // 退出原因统计
            exitReasons: this.getExitReasonStats()
        };
    }

    /**
     * 获取退出原因统计
     */
    getExitReasonStats() {
        const reasons = {};
        this.trades.forEach(trade => {
            reasons[trade.exitReason] = (reasons[trade.exitReason] || 0) + 1;
        });
        return reasons;
    }

    /**
     * 获取空统计数据
     */
    getEmptyStatistics() {
        return {
            totalTrades: 0,
            profitableTrades: 0,
            losingTrades: 0,
            winRate: '0',
            totalReturn: '0',
            annualizedReturn: '0',
            totalProfit: '0',
            maxDrawdown: '0',
            sharpeRatio: '0',
            volatility: '0',
            avgProfit: '0',
            avgLoss: '0',
            profitLossRatio: '0',
            avgHoldingDays: '0',
            takeoffStats: { count: 0, winRate: '0', avgReturn: '0' },
            strongStats: { count: 0, winRate: '0', avgReturn: '0' },
            exitReasons: {}
        };
    }

    /**
     * 按日期范围过滤数据
     */
    filterDataByDateRange(stockData, startDate, endDate) {
        if (!startDate && !endDate) return stockData;
        
        const { dates } = stockData;
        const startIndex = startDate ? dates.findIndex(d => d >= startDate) : 0;
        const endIndex = endDate ? dates.findIndex(d => d > endDate) : dates.length;
        
        const actualEndIndex = endIndex === -1 ? dates.length : endIndex;
        
        return {
            dates: dates.slice(startIndex, actualEndIndex),
            opens: stockData.opens.slice(startIndex, actualEndIndex),
            highs: stockData.highs.slice(startIndex, actualEndIndex),
            lows: stockData.lows.slice(startIndex, actualEndIndex),
            closes: stockData.closes.slice(startIndex, actualEndIndex),
            volumes: stockData.volumes.slice(startIndex, actualEndIndex)
        };
    }

    /**
     * 按日期范围过滤信号
     */
    filterSignalsByDateRange(signals, startDate, endDate) {
        if (!startDate && !endDate) return signals;
        
        return signals.filter(signal => {
            const signalDate = signal.date;
            if (startDate && signalDate < startDate) return false;
            if (endDate && signalDate > endDate) return false;
            return true;
        });
    }

    /**
     * 生成可视化数据
     */
    generateVisualizationData(stockData, signals) {
        return {
            candlestickData: this.generateCandlestickData(stockData),
            signalMarkers: this.generateSignalMarkers(signals),
            tradeMarkers: this.generateTradeMarkers(),
            equityCurveData: this.generateEquityCurveData(),
            drawdownData: this.generateDrawdownData()
        };
    }

    /**
     * 生成K线数据
     */
    generateCandlestickData(stockData) {
        const { dates, opens, highs, lows, closes, volumes } = stockData;
        return dates.map((date, i) => ({
            date,
            open: opens[i],
            high: highs[i],
            low: lows[i],
            close: closes[i],
            volume: volumes[i]
        }));
    }

    /**
     * 生成信号标记
     */
    generateSignalMarkers(signals) {
        return signals.map(signal => ({
            date: signal.date,
            price: signal.price,
            type: signal.type,
            mode: signal.mode,
            conditions: signal.conditions,
            strength: signal.strength
        }));
    }

    /**
     * 生成交易标记
     */
    generateTradeMarkers() {
        const markers = [];
        
        this.trades.forEach(trade => {
            // 买入标记
            markers.push({
                date: trade.openDate,
                price: trade.openPrice,
                type: 'buy',
                positionType: trade.positionType,
                shareCount: trade.shareCount
            });
            
            // 卖出标记
            markers.push({
                date: trade.closeDate,
                price: trade.closePrice,
                type: 'sell',
                exitReason: trade.exitReason,
                profit: trade.profit,
                returnRate: trade.returnRate
            });
        });
        
        return markers;
    }

    /**
     * 生成资金曲线数据
     */
    generateEquityCurveData() {
        return this.equityCurve.map(point => ({
            date: point.date,
            value: point.capital,
            return: ((point.capital - this.config.initialCapital) / this.config.initialCapital * 100).toFixed(2)
        }));
    }

    /**
     * 生成回撤数据
     */
    generateDrawdownData() {
        return this.equityCurve.map(point => ({
            date: point.date,
            drawdown: -point.drawdown // 负值表示回撤
        }));
    }
}

// 导出类
if (typeof window !== 'undefined') {
    window.AdvancedBacktestEngine = AdvancedBacktestEngine;
}
