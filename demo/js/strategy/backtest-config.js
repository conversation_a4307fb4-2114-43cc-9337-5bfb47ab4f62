/**
 * 回测参数配置组件
 * 提供用户友好的参数设置界面
 */

class BacktestConfig {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.config = this.getDefaultConfig();
        this.callbacks = {};
        
        this.init();
    }

    init() {
        this.render();
        this.bindEvents();
    }

    /**
     * 获取股票选项
     */
    getStockOptions() {
        if (window.demoDataGenerator) {
            const stocks = window.demoDataGenerator.getAvailableStocks();
            return stocks.map(stock =>
                `<option value="${stock.code}">${stock.code} - ${stock.name} (${stock.sector})</option>`
            ).join('');
        }

        // 默认选项
        return `
            <option value="000001.SZ">000001.SZ - 平安银行 (金融)</option>
            <option value="000002.SZ">000002.SZ - 万科A (房地产)</option>
            <option value="600036.SH">600036.SH - 招商银行 (金融)</option>
            <option value="600519.SH">600519.SH - 贵州茅台 (消费)</option>
            <option value="000858.SZ">000858.SZ - 五粮液 (消费)</option>
            <option value="002415.SZ">002415.SZ - 海康威视 (科技)</option>
            <option value="300059.SZ">300059.SZ - 东方财富 (金融科技)</option>
            <option value="002594.SZ">002594.SZ - BYD (新能源)</option>
        `;
    }

    /**
     * 获取默认配置
     */
    getDefaultConfig() {
        return {
            // 基础设置
            stockCode: '',
            startDate: '',
            endDate: '',
            
            // 资金设置
            initialCapital: 100000,
            positionSizeType: 'fixed', // 'fixed' | 'percentage'
            shareSize: 100,
            maxPositionRatio: 0.3,
            
            // 费用设置
            commissionRate: 0.1, // 0.1%
            stampTaxRate: 0.1,   // 0.1%
            slippage: 0.1,       // 0.1%
            
            // 策略设置
            takeoffPositionRatio: 33.33, // 1/3 仓位
            strongPositionRatio: 100,    // 满仓
            targetReturn: 0.5,           // 0.5%
            stopLossTimeoutMin: 3,       // 最小止损天数
            stopLossTimeoutMax: 5,       // 最大止损天数
            
            // 高级设置
            enablePriceStopLoss: true,
            priceStopLossRatio: 5,       // 5%
            enableTrailingStop: false,
            trailingStopRatio: 2,        // 2%
        };
    }

    /**
     * 渲染配置界面
     */
    render() {
        this.container.innerHTML = `
            <div class="backtest-config">
                <div class="card p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold flex items-center">
                            <i class="fas fa-cog mr-2 text-blue-400"></i>
                            回测参数配置
                        </h3>
                        <div class="flex space-x-2">
                            <button class="btn-secondary btn-sm" onclick="backtestConfig.resetToDefaults()">
                                <i class="fas fa-undo mr-1"></i>
                                重置默认
                            </button>
                            <button class="btn-secondary btn-sm" onclick="backtestConfig.saveConfig()">
                                <i class="fas fa-save mr-1"></i>
                                保存配置
                            </button>
                            <button class="btn-secondary btn-sm" onclick="backtestConfig.loadConfig()">
                                <i class="fas fa-upload mr-1"></i>
                                加载配置
                            </button>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- 基础设置 -->
                        <div class="space-y-6">
                            <div class="config-section">
                                <h4 class="text-md font-semibold mb-4 text-purple-400">基础设置</h4>
                                
                                <div class="form-group">
                                    <label class="form-label">股票代码</label>
                                    <select id="stockCode" class="form-input">
                                        <option value="">请选择股票</option>
                                        ${this.getStockOptions()}
                                    </select>
                                    <div class="form-help">选择要回测的股票代码</div>
                                </div>

                                <div class="grid grid-cols-2 gap-4">
                                    <div class="form-group">
                                        <label class="form-label">开始日期</label>
                                        <input type="date" id="startDate" class="form-input" 
                                               value="${this.config.startDate}">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">结束日期</label>
                                        <input type="date" id="endDate" class="form-input" 
                                               value="${this.config.endDate}">
                                    </div>
                                </div>
                            </div>

                            <div class="config-section">
                                <h4 class="text-md font-semibold mb-4 text-green-400">资金设置</h4>
                                
                                <div class="form-group">
                                    <label class="form-label">初始资金 (元)</label>
                                    <input type="number" id="initialCapital" class="form-input" 
                                           value="${this.config.initialCapital}" min="10000" step="1000">
                                    <div class="form-help">回测开始时的可用资金</div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">仓位管理方式</label>
                                    <select id="positionSizeType" class="form-input">
                                        <option value="fixed" ${this.config.positionSizeType === 'fixed' ? 'selected' : ''}>
                                            固定股数
                                        </option>
                                        <option value="percentage" ${this.config.positionSizeType === 'percentage' ? 'selected' : ''}>
                                            按资金比例
                                        </option>
                                    </select>
                                </div>

                                <div class="form-group" id="shareSizeGroup">
                                    <label class="form-label">每次交易股数</label>
                                    <input type="number" id="shareSize" class="form-input" 
                                           value="${this.config.shareSize}" min="100" step="100">
                                    <div class="form-help">每次买入的股票数量（手）</div>
                                </div>

                                <div class="form-group" id="maxPositionGroup" style="display: none;">
                                    <label class="form-label">最大持仓比例 (%)</label>
                                    <input type="number" id="maxPositionRatio" class="form-input" 
                                           value="${this.config.maxPositionRatio * 100}" min="10" max="100" step="5">
                                    <div class="form-help">单次交易最大使用资金比例</div>
                                </div>
                            </div>

                            <div class="config-section">
                                <h4 class="text-md font-semibold mb-4 text-yellow-400">费用设置</h4>
                                
                                <div class="grid grid-cols-3 gap-4">
                                    <div class="form-group">
                                        <label class="form-label">手续费率 (‰)</label>
                                        <input type="number" id="commissionRate" class="form-input" 
                                               value="${this.config.commissionRate}" min="0.1" max="5" step="0.1">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">印花税率 (‰)</label>
                                        <input type="number" id="stampTaxRate" class="form-input" 
                                               value="${this.config.stampTaxRate}" min="0" max="5" step="0.1">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">滑点 (‰)</label>
                                        <input type="number" id="slippage" class="form-input" 
                                               value="${this.config.slippage}" min="0" max="5" step="0.1">
                                    </div>
                                </div>
                                <div class="form-help">手续费双向收取，印花税仅卖出时收取</div>
                            </div>
                        </div>

                        <!-- 策略设置 -->
                        <div class="space-y-6">
                            <div class="config-section">
                                <h4 class="text-md font-semibold mb-4 text-blue-400">策略设置</h4>
                                
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="form-group">
                                        <label class="form-label">起飞模式仓位 (%)</label>
                                        <input type="number" id="takeoffPositionRatio" class="form-input" 
                                               value="${this.config.takeoffPositionRatio}" min="10" max="100" step="5">
                                        <div class="form-help">起飞模式信号的仓位比例</div>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">强势模式仓位 (%)</label>
                                        <input type="number" id="strongPositionRatio" class="form-input" 
                                               value="${this.config.strongPositionRatio}" min="10" max="100" step="5">
                                        <div class="form-help">强势模式信号的仓位比例</div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">目标止盈 (%)</label>
                                    <input type="number" id="targetReturn" class="form-input" 
                                           value="${this.config.targetReturn}" min="0.1" max="10" step="0.1">
                                    <div class="form-help">达到此收益率时自动止盈</div>
                                </div>

                                <div class="grid grid-cols-2 gap-4">
                                    <div class="form-group">
                                        <label class="form-label">最小持仓天数</label>
                                        <input type="number" id="stopLossTimeoutMin" class="form-input" 
                                               value="${this.config.stopLossTimeoutMin}" min="1" max="10" step="1">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">最大持仓天数</label>
                                        <input type="number" id="stopLossTimeoutMax" class="form-input" 
                                               value="${this.config.stopLossTimeoutMax}" min="1" max="20" step="1">
                                    </div>
                                </div>
                                <div class="form-help">超过最大天数或最小天数且亏损时强制平仓</div>
                            </div>

                            <div class="config-section">
                                <h4 class="text-md font-semibold mb-4 text-red-400">高级止损设置</h4>
                                
                                <div class="form-group">
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" id="enablePriceStopLoss" class="form-checkbox" 
                                               ${this.config.enablePriceStopLoss ? 'checked' : ''}>
                                        <span>启用价格止损</span>
                                    </label>
                                </div>

                                <div class="form-group" id="priceStopLossGroup">
                                    <label class="form-label">价格止损比例 (%)</label>
                                    <input type="number" id="priceStopLossRatio" class="form-input" 
                                           value="${this.config.priceStopLossRatio}" min="1" max="20" step="0.5">
                                    <div class="form-help">跌幅超过此比例时强制止损</div>
                                </div>

                                <div class="form-group">
                                    <label class="flex items-center space-x-2">
                                        <input type="checkbox" id="enableTrailingStop" class="form-checkbox" 
                                               ${this.config.enableTrailingStop ? 'checked' : ''}>
                                        <span>启用移动止损</span>
                                    </label>
                                </div>

                                <div class="form-group" id="trailingStopGroup" style="display: none;">
                                    <label class="form-label">移动止损比例 (%)</label>
                                    <input type="number" id="trailingStopRatio" class="form-input" 
                                           value="${this.config.trailingStopRatio}" min="0.5" max="10" step="0.5">
                                    <div class="form-help">从最高点回撤超过此比例时止损</div>
                                </div>
                            </div>

                            <div class="config-section">
                                <h4 class="text-md font-semibold mb-4 text-indigo-400">快速预设</h4>
                                
                                <div class="grid grid-cols-2 gap-3">
                                    <button class="btn-secondary btn-sm" onclick="backtestConfig.applyPreset('conservative')">
                                        <i class="fas fa-shield-alt mr-1"></i>
                                        保守型
                                    </button>
                                    <button class="btn-secondary btn-sm" onclick="backtestConfig.applyPreset('balanced')">
                                        <i class="fas fa-balance-scale mr-1"></i>
                                        平衡型
                                    </button>
                                    <button class="btn-secondary btn-sm" onclick="backtestConfig.applyPreset('aggressive')">
                                        <i class="fas fa-rocket mr-1"></i>
                                        激进型
                                    </button>
                                    <button class="btn-secondary btn-sm" onclick="backtestConfig.applyPreset('dayTrading')">
                                        <i class="fas fa-clock mr-1"></i>
                                        短线型
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-4 mt-8 pt-6 border-t border-gray-700">
                        <button class="btn-secondary" onclick="backtestConfig.validateConfig()">
                            <i class="fas fa-check-circle mr-2"></i>
                            验证配置
                        </button>
                        <button class="btn-primary" onclick="backtestConfig.startBacktest()">
                            <i class="fas fa-play mr-2"></i>
                            开始回测
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 仓位管理方式切换
        document.getElementById('positionSizeType').addEventListener('change', (e) => {
            this.togglePositionSizeType(e.target.value);
        });

        // 价格止损开关
        document.getElementById('enablePriceStopLoss').addEventListener('change', (e) => {
            this.togglePriceStopLoss(e.target.checked);
        });

        // 移动止损开关
        document.getElementById('enableTrailingStop').addEventListener('change', (e) => {
            this.toggleTrailingStop(e.target.checked);
        });

        // 实时验证
        this.container.querySelectorAll('input, select').forEach(input => {
            input.addEventListener('change', () => {
                this.updateConfigFromUI();
            });
        });

        // 初始化显示状态
        this.togglePositionSizeType(this.config.positionSizeType);
        this.togglePriceStopLoss(this.config.enablePriceStopLoss);
        this.toggleTrailingStop(this.config.enableTrailingStop);
    }

    /**
     * 切换仓位管理方式显示
     */
    togglePositionSizeType(type) {
        const shareSizeGroup = document.getElementById('shareSizeGroup');
        const maxPositionGroup = document.getElementById('maxPositionGroup');
        
        if (type === 'fixed') {
            shareSizeGroup.style.display = 'block';
            maxPositionGroup.style.display = 'none';
        } else {
            shareSizeGroup.style.display = 'none';
            maxPositionGroup.style.display = 'block';
        }
    }

    /**
     * 切换价格止损显示
     */
    togglePriceStopLoss(enabled) {
        const group = document.getElementById('priceStopLossGroup');
        group.style.display = enabled ? 'block' : 'none';
    }

    /**
     * 切换移动止损显示
     */
    toggleTrailingStop(enabled) {
        const group = document.getElementById('trailingStopGroup');
        group.style.display = enabled ? 'block' : 'none';
    }

    /**
     * 从UI更新配置
     */
    updateConfigFromUI() {
        this.config = {
            stockCode: document.getElementById('stockCode').value,
            startDate: document.getElementById('startDate').value,
            endDate: document.getElementById('endDate').value,
            
            initialCapital: parseFloat(document.getElementById('initialCapital').value),
            positionSizeType: document.getElementById('positionSizeType').value,
            shareSize: parseInt(document.getElementById('shareSize').value),
            maxPositionRatio: parseFloat(document.getElementById('maxPositionRatio').value) / 100,
            
            commissionRate: parseFloat(document.getElementById('commissionRate').value) / 1000,
            stampTaxRate: parseFloat(document.getElementById('stampTaxRate').value) / 1000,
            slippage: parseFloat(document.getElementById('slippage').value) / 1000,
            
            takeoffPositionRatio: parseFloat(document.getElementById('takeoffPositionRatio').value) / 100,
            strongPositionRatio: parseFloat(document.getElementById('strongPositionRatio').value) / 100,
            targetReturn: parseFloat(document.getElementById('targetReturn').value) / 100,
            stopLossTimeoutMin: parseInt(document.getElementById('stopLossTimeoutMin').value),
            stopLossTimeoutMax: parseInt(document.getElementById('stopLossTimeoutMax').value),
            
            enablePriceStopLoss: document.getElementById('enablePriceStopLoss').checked,
            priceStopLossRatio: parseFloat(document.getElementById('priceStopLossRatio').value) / 100,
            enableTrailingStop: document.getElementById('enableTrailingStop').checked,
            trailingStopRatio: parseFloat(document.getElementById('trailingStopRatio').value) / 100,
        };
    }

    /**
     * 应用预设配置
     */
    applyPreset(presetName) {
        const presets = {
            conservative: {
                takeoffPositionRatio: 20,
                strongPositionRatio: 50,
                targetReturn: 0.3,
                stopLossTimeoutMin: 2,
                stopLossTimeoutMax: 3,
                enablePriceStopLoss: true,
                priceStopLossRatio: 3,
                enableTrailingStop: true,
                trailingStopRatio: 1.5
            },
            balanced: {
                takeoffPositionRatio: 33.33,
                strongPositionRatio: 80,
                targetReturn: 0.5,
                stopLossTimeoutMin: 3,
                stopLossTimeoutMax: 5,
                enablePriceStopLoss: true,
                priceStopLossRatio: 5,
                enableTrailingStop: false,
                trailingStopRatio: 2
            },
            aggressive: {
                takeoffPositionRatio: 50,
                strongPositionRatio: 100,
                targetReturn: 1.0,
                stopLossTimeoutMin: 5,
                stopLossTimeoutMax: 8,
                enablePriceStopLoss: true,
                priceStopLossRatio: 8,
                enableTrailingStop: false,
                trailingStopRatio: 3
            },
            dayTrading: {
                takeoffPositionRatio: 30,
                strongPositionRatio: 60,
                targetReturn: 0.2,
                stopLossTimeoutMin: 1,
                stopLossTimeoutMax: 2,
                enablePriceStopLoss: true,
                priceStopLossRatio: 2,
                enableTrailingStop: true,
                trailingStopRatio: 1
            }
        };

        const preset = presets[presetName];
        if (preset) {
            Object.assign(this.config, preset);
            this.updateUIFromConfig();
            this.showNotification(`已应用${this.getPresetName(presetName)}预设`, 'success');
        }
    }

    /**
     * 获取预设名称
     */
    getPresetName(presetName) {
        const names = {
            conservative: '保守型',
            balanced: '平衡型',
            aggressive: '激进型',
            dayTrading: '短线型'
        };
        return names[presetName] || presetName;
    }

    /**
     * 从配置更新UI
     */
    updateUIFromConfig() {
        document.getElementById('takeoffPositionRatio').value = this.config.takeoffPositionRatio;
        document.getElementById('strongPositionRatio').value = this.config.strongPositionRatio;
        document.getElementById('targetReturn').value = this.config.targetReturn;
        document.getElementById('stopLossTimeoutMin').value = this.config.stopLossTimeoutMin;
        document.getElementById('stopLossTimeoutMax').value = this.config.stopLossTimeoutMax;
        document.getElementById('enablePriceStopLoss').checked = this.config.enablePriceStopLoss;
        document.getElementById('priceStopLossRatio').value = this.config.priceStopLossRatio;
        document.getElementById('enableTrailingStop').checked = this.config.enableTrailingStop;
        document.getElementById('trailingStopRatio').value = this.config.trailingStopRatio;

        this.togglePriceStopLoss(this.config.enablePriceStopLoss);
        this.toggleTrailingStop(this.config.enableTrailingStop);
    }

    /**
     * 重置为默认配置
     */
    resetToDefaults() {
        this.config = this.getDefaultConfig();
        this.render();
        this.bindEvents();
        this.showNotification('已重置为默认配置', 'info');
    }

    /**
     * 验证配置
     */
    validateConfig() {
        this.updateConfigFromUI();
        
        const errors = [];
        
        if (!this.config.stockCode) {
            errors.push('请输入股票代码');
        }
        
        if (this.config.initialCapital < 10000) {
            errors.push('初始资金不能少于10,000元');
        }
        
        if (this.config.stopLossTimeoutMin > this.config.stopLossTimeoutMax) {
            errors.push('最小持仓天数不能大于最大持仓天数');
        }
        
        if (this.config.targetReturn <= 0) {
            errors.push('目标止盈必须大于0');
        }

        if (errors.length > 0) {
            this.showNotification('配置验证失败：\n' + errors.join('\n'), 'error');
            return false;
        }
        
        this.showNotification('配置验证通过', 'success');
        return true;
    }

    /**
     * 保存配置
     */
    saveConfig() {
        this.updateConfigFromUI();
        const configJson = JSON.stringify(this.config, null, 2);
        const blob = new Blob([configJson], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `backtest_config_${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);
        this.showNotification('配置已保存', 'success');
    }

    /**
     * 加载配置
     */
    loadConfig() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const config = JSON.parse(e.target.result);
                        this.config = { ...this.getDefaultConfig(), ...config };
                        this.render();
                        this.bindEvents();
                        this.showNotification('配置已加载', 'success');
                    } catch (error) {
                        this.showNotification('配置文件格式错误', 'error');
                    }
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }

    /**
     * 开始回测
     */
    startBacktest() {
        if (this.validateConfig()) {
            if (this.callbacks.onStartBacktest) {
                this.callbacks.onStartBacktest(this.config);
            }
        }
    }

    /**
     * 获取当前配置
     */
    getConfig() {
        this.updateConfigFromUI();
        return this.config;
    }

    /**
     * 设置回调函数
     */
    setCallback(name, callback) {
        this.callbacks[name] = callback;
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        // 这里可以集成现有的通知系统
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        // 简单的通知实现
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            z-index: 10000;
            max-width: 300px;
            word-wrap: break-word;
        `;
        
        const colors = {
            success: '#10B981',
            error: '#EF4444',
            warning: '#F59E0B',
            info: '#3B82F6'
        };
        
        notification.style.backgroundColor = colors[type] || colors.info;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// 导出类
if (typeof window !== 'undefined') {
    window.BacktestConfig = BacktestConfig;
}
