/**
 * 策略面板组件
 * 提供策略配置、实时监控和信号显示功能
 */

class StrategyPanel {
    constructor(containerId, strategyEngine) {
        this.container = document.getElementById(containerId);
        this.strategyEngine = strategyEngine;
        this.isMonitoring = false;
        this.currentStock = null;
        this.monitoringInterval = null;
        
        this.init();
    }

    init() {
        this.render();
        this.bindEvents();
    }

    render() {
        this.container.innerHTML = `
            <div class="strategy-panel">
                <!-- 策略配置区域 -->
                <div class="card p-6 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold flex items-center">
                            <i class="fas fa-cogs mr-2 text-blue-400"></i>
                            策略配置
                        </h3>
                        <button class="btn-secondary text-sm" onclick="this.resetToDefaults()">
                            <i class="fas fa-undo mr-1"></i>
                            重置默认
                        </button>
                    </div>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 起飞模式配置 -->
                        <div class="strategy-config-section">
                            <h4 class="text-md font-medium mb-3 text-green-400">
                                <i class="fas fa-rocket mr-2"></i>
                                起飞模式 (预备仓)
                            </h4>
                            <div class="space-y-3">
                                <div class="grid grid-cols-2 gap-3">
                                    <div>
                                        <label class="block text-sm text-gray-400 mb-1">K值范围下限</label>
                                        <input type="number" class="input-field w-full" 
                                               id="takeoffKMin" value="20" min="0" max="100">
                                    </div>
                                    <div>
                                        <label class="block text-sm text-gray-400 mb-1">K值范围上限</label>
                                        <input type="number" class="input-field w-full" 
                                               id="takeoffKMax" value="50" min="0" max="100">
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm text-gray-400 mb-1">成交量比率阈值</label>
                                    <input type="number" class="input-field w-full" 
                                           id="takeoffVolumeRatio" value="1.2" min="1" max="5" step="0.1">
                                </div>
                                <div>
                                    <label class="block text-sm text-gray-400 mb-1">OBV连涨天数</label>
                                    <input type="number" class="input-field w-full" 
                                           id="takeoffObvDays" value="3" min="1" max="10">
                                </div>
                                <div>
                                    <label class="block text-sm text-gray-400 mb-1">满足条件数 (4选N)</label>
                                    <select class="input-field w-full" id="takeoffRequiredConditions">
                                        <option value="2">2条</option>
                                        <option value="3" selected>3条 (推荐)</option>
                                        <option value="4">4条 (严格)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 强势模式配置 -->
                        <div class="strategy-config-section">
                            <h4 class="text-md font-medium mb-3 text-red-400">
                                <i class="fas fa-fire mr-2"></i>
                                强势模式 (核心仓)
                            </h4>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm text-gray-400 mb-1">K值阈值</label>
                                    <input type="number" class="input-field w-full" 
                                           id="strongKThreshold" value="50" min="0" max="100">
                                </div>
                                <div>
                                    <label class="block text-sm text-gray-400 mb-1">成交量比率阈值</label>
                                    <input type="number" class="input-field w-full" 
                                           id="strongVolumeRatio" value="1.5" min="1" max="5" step="0.1">
                                </div>
                                <div>
                                    <label class="block text-sm text-gray-400 mb-1">MACD连涨天数</label>
                                    <input type="number" class="input-field w-full" 
                                           id="strongMacdDays" value="2" min="1" max="5">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 公共配置 -->
                    <div class="mt-6 pt-4 border-t border-gray-700">
                        <h4 class="text-md font-medium mb-3 text-blue-400">
                            <i class="fas fa-sliders-h mr-2"></i>
                            公共配置
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm text-gray-400 mb-1">信号冷却期 (K线)</label>
                                <input type="number" class="input-field w-full" 
                                       id="signalCooldown" value="7" min="1" max="20">
                            </div>
                            <div>
                                <label class="block text-sm text-gray-400 mb-1">止损时间下限 (K线)</label>
                                <input type="number" class="input-field w-full" 
                                       id="stopLossMin" value="3" min="1" max="10">
                            </div>
                            <div>
                                <label class="block text-sm text-gray-400 mb-1">止损时间上限 (K线)</label>
                                <input type="number" class="input-field w-full" 
                                       id="stopLossMax" value="5" min="1" max="10">
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 flex justify-end space-x-3">
                        <button class="btn-secondary" onclick="this.saveConfig()">
                            <i class="fas fa-save mr-2"></i>
                            保存配置
                        </button>
                        <button class="btn-primary" onclick="this.applyConfig()">
                            <i class="fas fa-check mr-2"></i>
                            应用配置
                        </button>
                    </div>
                </div>

                <!-- 实时监控区域 -->
                <div class="card p-6 mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold flex items-center">
                            <i class="fas fa-radar mr-2 text-green-400"></i>
                            实时监控
                        </h3>
                        <div class="flex items-center space-x-3">
                            <div class="flex items-center space-x-2">
                                <span class="status-indicator" id="monitoringStatus"></span>
                                <span class="text-sm text-gray-400" id="monitoringStatusText">未启动</span>
                            </div>
                            <button class="btn-primary" id="toggleMonitoring" onclick="this.toggleMonitoring()">
                                <i class="fas fa-play mr-2"></i>
                                开始监控
                            </button>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                        <div class="bg-gray-800 rounded-lg p-4">
                            <div class="text-sm text-gray-400 mb-1">监控股票</div>
                            <div class="text-lg font-semibold mono-font" id="monitoredStock">--</div>
                        </div>
                        <div class="bg-gray-800 rounded-lg p-4">
                            <div class="text-sm text-gray-400 mb-1">当前价格</div>
                            <div class="text-lg font-semibold mono-font" id="currentPrice">--</div>
                        </div>
                        <div class="bg-gray-800 rounded-lg p-4">
                            <div class="text-sm text-gray-400 mb-1">信号状态</div>
                            <div class="text-lg font-semibold" id="signalStatus">
                                <span class="signal-indicator signal-hold">
                                    <i class="fas fa-circle"></i>
                                    等待中
                                </span>
                            </div>
                        </div>
                        <div class="bg-gray-800 rounded-lg p-4">
                            <div class="text-sm text-gray-400 mb-1">最后更新</div>
                            <div class="text-sm mono-font" id="lastUpdate">--</div>
                        </div>
                    </div>

                    <!-- 股票选择 -->
                    <div class="mb-4">
                        <label class="block text-sm text-gray-400 mb-2">选择监控股票</label>
                        <div class="flex space-x-3">
                            <select class="input-field flex-1" id="monitorStockSelector">
                                <option value="">选择股票</option>
                                <option value="000001">000001 平安银行</option>
                                <option value="000002">000002 万科A</option>
                                <option value="600036">600036 招商银行</option>
                                <option value="600519">600519 贵州茅台</option>
                                <option value="000858">000858 五粮液</option>
                            </select>
                            <button class="btn-secondary" onclick="this.setMonitorStock()">
                                <i class="fas fa-crosshairs mr-2"></i>
                                设置监控
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 信号历史区域 -->
                <div class="card p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold flex items-center">
                            <i class="fas fa-history mr-2 text-yellow-400"></i>
                            信号历史
                        </h3>
                        <div class="flex space-x-2">
                            <button class="btn-secondary text-sm" onclick="this.exportSignals()">
                                <i class="fas fa-download mr-1"></i>
                                导出
                            </button>
                            <button class="btn-secondary text-sm" onclick="this.clearSignals()">
                                <i class="fas fa-trash mr-1"></i>
                                清空
                            </button>
                        </div>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="data-table w-full">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>股票</th>
                                    <th>价格</th>
                                    <th>模式</th>
                                    <th>仓位</th>
                                    <th>强度</th>
                                    <th>条件</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="signalHistoryTable">
                                <tr>
                                    <td colspan="8" class="text-center text-gray-400 py-8">
                                        暂无信号记录
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // 配置变更事件
        const configInputs = this.container.querySelectorAll('input, select');
        configInputs.forEach(input => {
            input.addEventListener('change', () => {
                this.markConfigChanged();
            });
        });
    }

    markConfigChanged() {
        const applyBtn = this.container.querySelector('button[onclick="this.applyConfig()"]');
        if (applyBtn) {
            applyBtn.classList.add('glow');
            applyBtn.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>应用更改';
        }
    }

    applyConfig() {
        const config = this.getConfigFromUI();
        this.strategyEngine.config = { ...this.strategyEngine.config, ...config };
        
        const applyBtn = this.container.querySelector('button[onclick="this.applyConfig()"]');
        if (applyBtn) {
            applyBtn.classList.remove('glow');
            applyBtn.innerHTML = '<i class="fas fa-check mr-2"></i>应用配置';
        }
        
        this.showNotification('配置已应用', 'success');
    }

    getConfigFromUI() {
        return {
            takeoff: {
                kRange: [
                    parseInt(document.getElementById('takeoffKMin').value),
                    parseInt(document.getElementById('takeoffKMax').value)
                ],
                volumeRatio: parseFloat(document.getElementById('takeoffVolumeRatio').value),
                obvRisingPeriods: parseInt(document.getElementById('takeoffObvDays').value),
                requiredConditions: parseInt(document.getElementById('takeoffRequiredConditions').value)
            },
            strong: {
                kThreshold: parseInt(document.getElementById('strongKThreshold').value),
                volumeRatio: parseFloat(document.getElementById('strongVolumeRatio').value),
                macdRisingPeriods: parseInt(document.getElementById('strongMacdDays').value)
            },
            common: {
                signalCooldown: parseInt(document.getElementById('signalCooldown').value),
                stopLossTimeout: [
                    parseInt(document.getElementById('stopLossMin').value),
                    parseInt(document.getElementById('stopLossMax').value)
                ]
            }
        };
    }

    toggleMonitoring() {
        if (this.isMonitoring) {
            this.stopMonitoring();
        } else {
            this.startMonitoring();
        }
    }

    startMonitoring() {
        if (!this.currentStock) {
            this.showNotification('请先选择要监控的股票', 'warning');
            return;
        }

        this.isMonitoring = true;
        this.updateMonitoringUI();
        
        // 开始定时监控
        this.monitoringInterval = setInterval(() => {
            this.checkSignals();
        }, 5000); // 每5秒检查一次
        
        this.showNotification('开始监控 ' + this.currentStock, 'success');
    }

    stopMonitoring() {
        this.isMonitoring = false;
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        this.updateMonitoringUI();
        this.showNotification('监控已停止', 'info');
    }

    updateMonitoringUI() {
        const statusIndicator = document.getElementById('monitoringStatus');
        const statusText = document.getElementById('monitoringStatusText');
        const toggleBtn = document.getElementById('toggleMonitoring');
        
        if (this.isMonitoring) {
            statusIndicator.className = 'status-indicator status-online';
            statusText.textContent = '监控中';
            toggleBtn.innerHTML = '<i class="fas fa-stop mr-2"></i>停止监控';
            toggleBtn.className = 'btn-secondary';
        } else {
            statusIndicator.className = 'status-indicator status-offline';
            statusText.textContent = '未启动';
            toggleBtn.innerHTML = '<i class="fas fa-play mr-2"></i>开始监控';
            toggleBtn.className = 'btn-primary';
        }
    }

    setMonitorStock() {
        const selector = document.getElementById('monitorStockSelector');
        const stockCode = selector.value;
        
        if (!stockCode) {
            this.showNotification('请选择股票', 'warning');
            return;
        }
        
        this.currentStock = stockCode;
        document.getElementById('monitoredStock').textContent = selector.options[selector.selectedIndex].text;
        this.showNotification('监控股票已设置为 ' + stockCode, 'success');
    }

    checkSignals() {
        // 模拟信号检测
        const now = new Date();
        document.getElementById('lastUpdate').textContent = now.toLocaleTimeString();
        
        // 这里应该调用实际的数据获取和信号检测逻辑
        // 暂时使用模拟数据
        this.simulateSignalCheck();
    }

    simulateSignalCheck() {
        // 模拟信号检测结果
        const random = Math.random();
        const signalStatus = document.getElementById('signalStatus');
        
        if (random < 0.05) { // 5% 概率出现起飞信号
            signalStatus.innerHTML = `
                <span class="signal-indicator signal-buy">
                    <i class="fas fa-rocket"></i>
                    起飞信号
                </span>
            `;
            this.addSignalToHistory('takeoff');
        } else if (random < 0.08) { // 3% 概率出现强势信号
            signalStatus.innerHTML = `
                <span class="signal-indicator signal-buy">
                    <i class="fas fa-fire"></i>
                    强势信号
                </span>
            `;
            this.addSignalToHistory('strong');
        } else {
            signalStatus.innerHTML = `
                <span class="signal-indicator signal-hold">
                    <i class="fas fa-circle"></i>
                    等待中
                </span>
            `;
        }
    }

    addSignalToHistory(type) {
        const table = document.getElementById('signalHistoryTable');
        const now = new Date();
        const price = (Math.random() * 50 + 10).toFixed(2);
        const strength = Math.floor(Math.random() * 5) + 1;
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="mono-font">${now.toLocaleString()}</td>
            <td>${this.currentStock}</td>
            <td class="mono-font price-up">¥${price}</td>
            <td>
                <span class="signal-indicator ${type === 'takeoff' ? 'signal-buy' : 'signal-sell'}">
                    <i class="fas fa-${type === 'takeoff' ? 'rocket' : 'fire'}"></i>
                    ${type === 'takeoff' ? '起飞模式' : '强势模式'}
                </span>
            </td>
            <td>${type === 'takeoff' ? '预备仓(1/3)' : '核心仓'}</td>
            <td>
                <div class="flex items-center">
                    ${Array(strength).fill('<i class="fas fa-star text-yellow-400"></i>').join('')}
                    ${Array(5-strength).fill('<i class="far fa-star text-gray-600"></i>').join('')}
                </div>
            </td>
            <td class="text-sm">
                ${type === 'takeoff' ? 'K上穿D, 成交量放大' : '全部条件满足'}
            </td>
            <td>
                <button class="text-blue-400 hover:text-blue-300 text-sm">
                    <i class="fas fa-eye"></i>
                </button>
            </td>
        `;
        
        // 移除"暂无记录"行
        const emptyRow = table.querySelector('td[colspan="8"]');
        if (emptyRow) {
            emptyRow.parentElement.remove();
        }
        
        table.insertBefore(row, table.firstChild);
        
        // 限制历史记录数量
        const rows = table.querySelectorAll('tr');
        if (rows.length > 50) {
            rows[rows.length - 1].remove();
        }
        
        // 显示通知
        this.showNotification(`检测到${type === 'takeoff' ? '起飞' : '强势'}信号！`, 'success');
    }

    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
            type === 'success' ? 'bg-green-600' : 
            type === 'warning' ? 'bg-yellow-600' : 
            type === 'error' ? 'bg-red-600' : 'bg-blue-600'
        } text-white`;
        notification.innerHTML = `
            <div class="flex items-center space-x-2">
                <i class="fas fa-${
                    type === 'success' ? 'check-circle' : 
                    type === 'warning' ? 'exclamation-triangle' : 
                    type === 'error' ? 'times-circle' : 'info-circle'
                }"></i>
                <span>${message}</span>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // 自动移除
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    saveConfig() {
        const config = this.getConfigFromUI();
        localStorage.setItem('strategyConfig', JSON.stringify(config));
        this.showNotification('配置已保存', 'success');
    }

    resetToDefaults() {
        // 重置为默认值
        document.getElementById('takeoffKMin').value = 20;
        document.getElementById('takeoffKMax').value = 50;
        document.getElementById('takeoffVolumeRatio').value = 1.2;
        document.getElementById('takeoffObvDays').value = 3;
        document.getElementById('takeoffRequiredConditions').value = 3;
        
        document.getElementById('strongKThreshold').value = 50;
        document.getElementById('strongVolumeRatio').value = 1.5;
        document.getElementById('strongMacdDays').value = 2;
        
        document.getElementById('signalCooldown').value = 7;
        document.getElementById('stopLossMin').value = 3;
        document.getElementById('stopLossMax').value = 5;
        
        this.markConfigChanged();
        this.showNotification('已重置为默认配置', 'info');
    }

    exportSignals() {
        // 导出信号历史
        const table = document.getElementById('signalHistoryTable');
        const rows = table.querySelectorAll('tr');
        
        if (rows.length === 0 || rows[0].querySelector('td[colspan="8"]')) {
            this.showNotification('暂无数据可导出', 'warning');
            return;
        }
        
        let csv = '时间,股票,价格,模式,仓位,强度,条件\n';
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            if (cells.length > 1) {
                const rowData = Array.from(cells).slice(0, -1).map(cell => 
                    cell.textContent.trim().replace(/,/g, ';')
                ).join(',');
                csv += rowData + '\n';
            }
        });
        
        const blob = new Blob([csv], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `strategy_signals_${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        URL.revokeObjectURL(url);
        
        this.showNotification('信号历史已导出', 'success');
    }

    clearSignals() {
        if (confirm('确定要清空所有信号历史吗？')) {
            const table = document.getElementById('signalHistoryTable');
            table.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center text-gray-400 py-8">
                        暂无信号记录
                    </td>
                </tr>
            `;
            this.showNotification('信号历史已清空', 'info');
        }
    }
}

// 全局策略面板实例
window.strategyPanel = null;
