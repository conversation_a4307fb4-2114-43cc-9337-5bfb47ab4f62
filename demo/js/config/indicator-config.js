/**
 * 技术指标统一配置
 * 所有指标统一使用20日周期设置
 */

class IndicatorConfig {
    constructor() {
        // 统一周期设置：所有指标都基于20日K线数据
        this.UNIFIED_PERIOD = 20;
        
        // 技术指标参数配置
        this.indicators = {
            // KDJ指标配置
            kdj: {
                kPeriod: this.UNIFIED_PERIOD,    // K周期=20
                dPeriod: 3,                      // D周期=3
                jPeriod: 3,                      // J周期=3
                overboughtLevel: 80,             // 超买线
                oversoldLevel: 20                // 超卖线
            },
            
            // 布林带指标配置
            bollinger: {
                window: this.UNIFIED_PERIOD,     // 移动平均窗口=20日
                stdDev: 2.0,                     // 标准差倍数=2
                bandwidthThreshold: 0.1          // 带宽挤压阈值
            },
            
            // 成交量分析配置
            volume: {
                window: this.UNIFIED_PERIOD,     // 移动平均窗口=20日
                triangleWindow: 6,               // 三角移动平均窗口
                emaAlpha: 0.3,                   // EMA平滑系数
                bollingerWindow: this.UNIFIED_PERIOD, // 成交量布林带窗口=20日
                lag: 6                           // 滞后期
            }
        };

        // 突破信号检测配置
        this.breakthrough = {
            // 移动平均计算周期
            maWindow: this.UNIFIED_PERIOD,
            
            // 突破阈值设置
            thresholds: {
                // 价格突破阈值：移动平均值的2%
                price: {
                    percentage: 0.02,            // 2%
                    minValue: 0.01               // 最小绝对值
                },
                
                // KDJ突破阈值：5个点位
                kdj: {
                    points: 5,                   // 5个点位
                    minValue: 2                  // 最小值
                },
                
                // 成交量突破阈值：移动平均值的50%
                volume: {
                    percentage: 0.5,             // 50%
                    minValue: 0.2                // 最小值20%
                },
                
                // 布林带突破阈值
                bollinger: {
                    percentage: 0.01,            // 1%
                    minValue: 0.005              // 最小值0.5%
                }
            },
            
            // 信号强度分级
            strengthLevels: {
                weak: 1,                         // 弱信号
                medium: 2,                       // 中等信号
                strong: 3                        // 强信号
            }
        };

        // 扫描配置
        this.scanning = {
            batchSize: 10,                       // 批处理大小
            maxConcurrent: 3,                    // 最大并发数
            progressUpdateInterval: 100,         // 进度更新间隔(ms)
            cacheExpiry: 300000,                 // 缓存过期时间(5分钟)
            minDataPoints: this.UNIFIED_PERIOD + 5 // 最少数据点数
        };

        // 数据质量配置
        this.dataQuality = {
            maxMissingRatio: 0.1,               // 最大缺失数据比例10%
            outlierThreshold: 3,                // 异常值检测阈值(3σ)
            minVolume: 1000,                    // 最小成交量
            minPrice: 0.01                      // 最小价格
        };
    }

    /**
     * 获取指标配置
     * @param {string} indicatorName - 指标名称
     * @returns {Object} 指标配置
     */
    getIndicatorConfig(indicatorName) {
        return this.indicators[indicatorName] || {};
    }

    /**
     * 获取突破检测配置
     * @returns {Object} 突破检测配置
     */
    getBreakthroughConfig() {
        return this.breakthrough;
    }

    /**
     * 获取扫描配置
     * @returns {Object} 扫描配置
     */
    getScanningConfig() {
        return this.scanning;
    }

    /**
     * 获取数据质量配置
     * @returns {Object} 数据质量配置
     */
    getDataQualityConfig() {
        return this.dataQuality;
    }

    /**
     * 更新阈值配置
     * @param {Object} newThresholds - 新的阈值配置
     */
    updateThresholds(newThresholds) {
        this.breakthrough.thresholds = {
            ...this.breakthrough.thresholds,
            ...newThresholds
        };
    }

    /**
     * 获取统一周期
     * @returns {number} 统一周期
     */
    getUnifiedPeriod() {
        return this.UNIFIED_PERIOD;
    }

    /**
     * 验证数据是否满足计算要求
     * @param {Array} data - 数据数组
     * @returns {Object} 验证结果
     */
    validateData(data) {
        const result = {
            isValid: true,
            errors: [],
            warnings: []
        };

        // 检查数据长度
        if (!data || data.length < this.scanning.minDataPoints) {
            result.isValid = false;
            result.errors.push(`数据长度不足，需要至少${this.scanning.minDataPoints}个数据点`);
        }

        // 检查缺失数据比例
        if (data) {
            const missingCount = data.filter(val => val === null || val === undefined || isNaN(val)).length;
            const missingRatio = missingCount / data.length;
            
            if (missingRatio > this.dataQuality.maxMissingRatio) {
                result.isValid = false;
                result.errors.push(`缺失数据比例过高：${(missingRatio * 100).toFixed(1)}%`);
            } else if (missingRatio > 0.05) {
                result.warnings.push(`存在${(missingRatio * 100).toFixed(1)}%的缺失数据`);
            }
        }

        return result;
    }

    /**
     * 计算信号强度
     * @param {number} breakthroughRatio - 突破比例
     * @param {string} indicatorType - 指标类型
     * @returns {number} 信号强度等级
     */
    calculateSignalStrength(breakthroughRatio, indicatorType) {
        const absRatio = Math.abs(breakthroughRatio);
        const threshold = this.breakthrough.thresholds[indicatorType];
        
        if (!threshold) return this.breakthrough.strengthLevels.weak;

        const baseThreshold = threshold.percentage || threshold.points || 0.05;
        
        if (absRatio >= baseThreshold * 3) {
            return this.breakthrough.strengthLevels.strong;
        } else if (absRatio >= baseThreshold * 1.5) {
            return this.breakthrough.strengthLevels.medium;
        } else {
            return this.breakthrough.strengthLevels.weak;
        }
    }

    /**
     * 获取信号强度描述
     * @param {number} strength - 信号强度等级
     * @returns {string} 强度描述
     */
    getStrengthDescription(strength) {
        const descriptions = {
            [this.breakthrough.strengthLevels.weak]: '弱',
            [this.breakthrough.strengthLevels.medium]: '中',
            [this.breakthrough.strengthLevels.strong]: '强'
        };
        return descriptions[strength] || '未知';
    }

    /**
     * 获取信号强度颜色类
     * @param {number} strength - 信号强度等级
     * @returns {string} CSS类名
     */
    getStrengthColorClass(strength) {
        const colorClasses = {
            [this.breakthrough.strengthLevels.weak]: 'text-gray-400',
            [this.breakthrough.strengthLevels.medium]: 'text-yellow-400',
            [this.breakthrough.strengthLevels.strong]: 'text-red-400'
        };
        return colorClasses[strength] || 'text-gray-400';
    }

    /**
     * 导出配置为JSON
     * @returns {string} JSON字符串
     */
    exportConfig() {
        return JSON.stringify({
            unifiedPeriod: this.UNIFIED_PERIOD,
            indicators: this.indicators,
            breakthrough: this.breakthrough,
            scanning: this.scanning,
            dataQuality: this.dataQuality
        }, null, 2);
    }

    /**
     * 从JSON导入配置
     * @param {string} configJson - JSON配置字符串
     * @returns {boolean} 是否成功导入
     */
    importConfig(configJson) {
        try {
            const config = JSON.parse(configJson);
            
            if (config.unifiedPeriod) this.UNIFIED_PERIOD = config.unifiedPeriod;
            if (config.indicators) this.indicators = { ...this.indicators, ...config.indicators };
            if (config.breakthrough) this.breakthrough = { ...this.breakthrough, ...config.breakthrough };
            if (config.scanning) this.scanning = { ...this.scanning, ...config.scanning };
            if (config.dataQuality) this.dataQuality = { ...this.dataQuality, ...config.dataQuality };
            
            return true;
        } catch (error) {
            console.error('配置导入失败:', error);
            return false;
        }
    }

    /**
     * 重置为默认配置
     */
    resetToDefaults() {
        const defaultConfig = new IndicatorConfig();
        this.UNIFIED_PERIOD = defaultConfig.UNIFIED_PERIOD;
        this.indicators = defaultConfig.indicators;
        this.breakthrough = defaultConfig.breakthrough;
        this.scanning = defaultConfig.scanning;
        this.dataQuality = defaultConfig.dataQuality;
    }
}

// 创建全局配置实例
const INDICATOR_CONFIG = new IndicatorConfig();

// 导出配置类和实例
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { IndicatorConfig, INDICATOR_CONFIG };
} else if (typeof window !== 'undefined') {
    window.IndicatorConfig = IndicatorConfig;
    window.INDICATOR_CONFIG = INDICATOR_CONFIG;
}
