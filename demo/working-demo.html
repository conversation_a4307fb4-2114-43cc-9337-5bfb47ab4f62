<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>策略回测系统 - 工作演示</title>
    
    <!-- 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- ECharts 图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            color: #F9FAFB;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #3B82F6, #8B5CF6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .card {
            background: rgba(31, 41, 55, 0.9);
            border: 1px solid #374151;
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
            backdrop-filter: blur(10px);
        }
        
        .grid {
            display: grid;
            gap: 1.5rem;
        }
        
        .grid-2 { grid-template-columns: repeat(2, 1fr); }
        .grid-3 { grid-template-columns: repeat(3, 1fr); }
        .grid-4 { grid-template-columns: repeat(4, 1fr); }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #D1D5DB;
        }
        
        .form-input, .form-select {
            width: 100%;
            padding: 0.75rem;
            background: #374151;
            border: 1px solid #4B5563;
            border-radius: 0.5rem;
            color: #F9FAFB;
            font-size: 0.875rem;
        }
        
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #3B82F6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin: 0.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3B82F6, #1D4ED8);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        
        .btn-secondary {
            background: #374151;
            color: #D1D5DB;
            border: 1px solid #4B5563;
        }
        
        .btn-secondary:hover {
            background: #4B5563;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .status {
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 1rem 0;
        }
        
        .status.success {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.2);
            color: #10B981;
        }
        
        .status.error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.2);
            color: #EF4444;
        }
        
        .status.warning {
            background: rgba(245, 158, 11, 0.1);
            border: 1px solid rgba(245, 158, 11, 0.2);
            color: #F59E0B;
        }
        
        .metric-card {
            text-align: center;
            padding: 1.5rem;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(16, 185, 129, 0.1));
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 0.75rem;
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin: 0.5rem 0;
            font-family: 'Monaco', monospace;
        }
        
        .metric-label {
            font-size: 0.875rem;
            color: #9CA3AF;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .chart-container {
            height: 400px;
            margin: 1rem 0;
            background: rgba(17, 24, 39, 0.5);
            border-radius: 0.75rem;
            padding: 1rem;
        }
        
        .hidden { display: none; }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #374151;
            border-radius: 50%;
            border-top-color: #3B82F6;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #374151;
            border-radius: 4px;
            overflow: hidden;
            margin: 1rem 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #10B981, #059669);
            transition: width 0.3s ease;
            border-radius: 4px;
        }
        
        @media (max-width: 768px) {
            .grid-2, .grid-3, .grid-4 {
                grid-template-columns: 1fr;
            }
            
            body {
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        th, td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #374151;
        }
        
        th {
            background: #374151;
            font-weight: 600;
        }
        
        tr:nth-child(even) {
            background: rgba(55, 65, 81, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1><i class="fas fa-rocket"></i> 策略回测系统</h1>
            <p>专业级量化交易策略回测平台</p>
        </div>

        <!-- 系统状态 -->
        <div class="card">
            <h2><i class="fas fa-cogs"></i> 系统状态</h2>
            <div id="systemStatus">
                <div class="status warning">
                    <i class="fas fa-spinner fa-spin"></i> 正在初始化系统组件...
                </div>
            </div>
        </div>

        <!-- 回测配置 -->
        <div class="card" id="configSection" style="display: none;">
            <h2><i class="fas fa-sliders-h"></i> 回测配置</h2>
            
            <div class="grid grid-3">
                <div class="form-group">
                    <label class="form-label">股票代码</label>
                    <select id="stockCode" class="form-select">
                        <option value="">请选择股票</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">开始日期</label>
                    <input type="date" id="startDate" class="form-input" value="2024-01-01">
                </div>
                <div class="form-group">
                    <label class="form-label">结束日期</label>
                    <input type="date" id="endDate" class="form-input" value="2024-08-13">
                </div>
            </div>
            
            <div class="grid grid-3">
                <div class="form-group">
                    <label class="form-label">初始资金 (元)</label>
                    <input type="number" id="initialCapital" class="form-input" value="100000" min="10000" step="1000">
                </div>
                <div class="form-group">
                    <label class="form-label">每次交易股数</label>
                    <input type="number" id="shareSize" class="form-input" value="100" min="100" step="100">
                </div>
                <div class="form-group">
                    <label class="form-label">手续费率 (‰)</label>
                    <input type="number" id="commissionRate" class="form-input" value="1" min="0.1" max="5" step="0.1">
                </div>
            </div>
            
            <div style="margin-top: 2rem;">
                <button class="btn btn-primary" id="startBacktest" onclick="startBacktest()">
                    <i class="fas fa-play"></i> 开始回测
                </button>
                <button class="btn btn-secondary" onclick="loadSampleData()">
                    <i class="fas fa-download"></i> 加载示例
                </button>
                <button class="btn btn-secondary" onclick="clearResults()">
                    <i class="fas fa-trash"></i> 清除结果
                </button>
            </div>
            
            <div id="progress" class="hidden">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="status warning">
                    <i class="loading"></i> <span id="progressText">回测进行中...</span>
                </div>
            </div>
        </div>

        <!-- 回测结果 -->
        <div class="card" id="resultsSection" style="display: none;">
            <h2><i class="fas fa-chart-line"></i> 回测结果</h2>
            
            <!-- 核心指标 -->
            <div id="coreMetrics" class="grid grid-4">
                <!-- 动态生成 -->
            </div>
            
            <!-- 资金曲线图 -->
            <div class="chart-container">
                <div id="equityChart"></div>
            </div>
            
            <!-- 交易明细 -->
            <div style="margin-top: 2rem;">
                <h3><i class="fas fa-list"></i> 交易明细</h3>
                <div id="tradesTable"></div>
            </div>
        </div>
    </div>

    <!-- 脚本加载 -->
    <script src="js/strategy/strategy-engine.js"></script>
    <script src="js/strategy/demo-data.js"></script>
    <script src="js/strategy/advanced-backtest-engine.js"></script>

    <script>
        let backtestEngine = null;
        let currentResults = null;
        
        // 系统初始化
        window.addEventListener('load', function() {
            setTimeout(initializeSystem, 1000);
        });
        
        function initializeSystem() {
            const statusDiv = document.getElementById('systemStatus');
            
            try {
                // 检查组件
                const components = ['StrategyEngine', 'DemoDataGenerator', 'AdvancedBacktestEngine'];
                const missing = components.filter(comp => !window[comp]);
                
                if (missing.length > 0) {
                    statusDiv.innerHTML = `
                        <div class="status error">
                            <i class="fas fa-exclamation-triangle"></i> 
                            系统初始化失败，缺少组件: ${missing.join(', ')}
                        </div>
                    `;
                    return;
                }
                
                // 初始化引擎
                const strategyEngine = new StrategyEngine();
                backtestEngine = new AdvancedBacktestEngine(strategyEngine);
                
                // 加载股票选项
                loadStockOptions();
                
                statusDiv.innerHTML = `
                    <div class="status success">
                        <i class="fas fa-check-circle"></i> 
                        系统初始化成功，所有组件加载完毕！
                    </div>
                `;
                
                // 显示配置区域
                document.getElementById('configSection').style.display = 'block';
                
            } catch (error) {
                statusDiv.innerHTML = `
                    <div class="status error">
                        <i class="fas fa-exclamation-triangle"></i> 
                        初始化失败: ${error.message}
                    </div>
                `;
                console.error('初始化错误:', error);
            }
        }
        
        function loadStockOptions() {
            const select = document.getElementById('stockCode');
            const stocks = window.demoDataGenerator.getAvailableStocks();
            
            stocks.forEach(stock => {
                const option = document.createElement('option');
                option.value = stock.code;
                option.textContent = `${stock.code} - ${stock.name}`;
                select.appendChild(option);
            });
        }
        
        function loadSampleData() {
            document.getElementById('stockCode').value = '000001.SZ';
            document.getElementById('startDate').value = '2024-01-01';
            document.getElementById('endDate').value = '2024-08-13';
            showNotification('示例数据已加载', 'success');
        }
        
        async function startBacktest() {
            if (!backtestEngine) {
                showNotification('系统未初始化', 'error');
                return;
            }
            
            const config = getConfig();
            if (!validateConfig(config)) return;
            
            // 显示进度
            document.getElementById('progress').classList.remove('hidden');
            document.getElementById('startBacktest').disabled = true;
            
            try {
                updateProgress(10, '获取股票数据...');
                const stockData = window.demoDataGenerator.generateStockData(
                    config.stockCode, config.startDate, config.endDate
                );
                
                updateProgress(30, '分析策略信号...');
                backtestEngine.config = { ...backtestEngine.config, ...config };
                
                updateProgress(60, '模拟交易执行...');
                const results = backtestEngine.runBacktest(stockData);
                
                updateProgress(90, '生成报告...');
                displayResults(results);
                
                updateProgress(100, '回测完成');
                showNotification('回测完成！', 'success');
                
            } catch (error) {
                showNotification(`回测失败: ${error.message}`, 'error');
                console.error('回测错误:', error);
            } finally {
                document.getElementById('progress').classList.add('hidden');
                document.getElementById('startBacktest').disabled = false;
            }
        }
        
        function getConfig() {
            return {
                stockCode: document.getElementById('stockCode').value,
                startDate: document.getElementById('startDate').value,
                endDate: document.getElementById('endDate').value,
                initialCapital: parseFloat(document.getElementById('initialCapital').value),
                shareSize: parseInt(document.getElementById('shareSize').value),
                commissionRate: parseFloat(document.getElementById('commissionRate').value) / 1000,
                stampTaxRate: 0.001,
                slippage: 0.001,
                takeoffPositionRatio: 1/3,
                strongPositionRatio: 1,
                targetReturn: 0.005,
                stopLossTimeout: [3, 5]
            };
        }
        
        function validateConfig(config) {
            if (!config.stockCode) {
                showNotification('请选择股票代码', 'error');
                return false;
            }
            if (config.initialCapital < 10000) {
                showNotification('初始资金不能少于10,000元', 'error');
                return false;
            }
            return true;
        }
        
        function updateProgress(percent, text) {
            document.getElementById('progressFill').style.width = `${percent}%`;
            document.getElementById('progressText').textContent = text;
        }
        
        function displayResults(results) {
            currentResults = results;
            
            // 显示结果区域
            document.getElementById('resultsSection').style.display = 'block';
            
            // 核心指标
            displayCoreMetrics(results.statistics);
            
            // 资金曲线
            drawEquityCurve(results.visualData.equityCurveData);
            
            // 交易明细
            displayTradesTable(results.trades);
            
            // 滚动到结果
            document.getElementById('resultsSection').scrollIntoView({ behavior: 'smooth' });
        }
        
        function displayCoreMetrics(stats) {
            const metricsDiv = document.getElementById('coreMetrics');
            metricsDiv.innerHTML = `
                <div class="metric-card">
                    <div class="metric-value" style="color: ${parseFloat(stats.totalReturn) >= 0 ? '#10B981' : '#EF4444'}">
                        ${stats.totalReturn}%
                    </div>
                    <div class="metric-label">总收益率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" style="color: #3B82F6">${stats.winRate}%</div>
                    <div class="metric-label">胜率</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" style="color: #8B5CF6">${stats.totalTrades}</div>
                    <div class="metric-label">交易次数</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" style="color: #EF4444">${stats.maxDrawdown}%</div>
                    <div class="metric-label">最大回撤</div>
                </div>
            `;
        }
        
        function drawEquityCurve(equityData) {
            const chart = echarts.init(document.getElementById('equityChart'), 'dark');
            
            const option = {
                title: { text: '资金曲线', textStyle: { color: '#F9FAFB' } },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(17, 24, 39, 0.95)',
                    borderColor: '#374151',
                    textStyle: { color: '#F9FAFB' }
                },
                xAxis: {
                    type: 'category',
                    data: equityData.map(item => item.date),
                    axisLabel: { color: '#9CA3AF' }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: { 
                        color: '#9CA3AF',
                        formatter: value => '¥' + (value / 1000).toFixed(0) + 'K'
                    }
                },
                series: [{
                    name: '资金',
                    type: 'line',
                    data: equityData.map(item => item.value),
                    smooth: true,
                    lineStyle: { color: '#10B981', width: 3 },
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: 'rgba(16, 185, 129, 0.3)' },
                            { offset: 1, color: 'rgba(16, 185, 129, 0.05)' }
                        ])
                    }
                }]
            };
            
            chart.setOption(option);
        }
        
        function displayTradesTable(trades) {
            if (trades.length === 0) {
                document.getElementById('tradesTable').innerHTML = '<div class="status warning">暂无交易记录</div>';
                return;
            }
            
            let html = '<table>';
            html += `
                <tr>
                    <th>开仓日期</th>
                    <th>平仓日期</th>
                    <th>策略类型</th>
                    <th>收益率</th>
                    <th>退出原因</th>
                </tr>
            `;
            
            trades.forEach(trade => {
                const returnColor = trade.returnRate >= 0 ? '#10B981' : '#EF4444';
                html += `
                    <tr>
                        <td>${trade.openDate}</td>
                        <td>${trade.closeDate}</td>
                        <td>
                            <span style="padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem; background: ${trade.positionType === 'takeoff' ? '#10B981' : '#3B82F6'}; color: white;">
                                ${trade.positionType === 'takeoff' ? '起飞模式' : '强势模式'}
                            </span>
                        </td>
                        <td style="color: ${returnColor}; font-weight: bold;">
                            ${(trade.returnRate * 100).toFixed(2)}%
                        </td>
                        <td>${trade.exitReason}</td>
                    </tr>
                `;
            });
            
            html += '</table>';
            document.getElementById('tradesTable').innerHTML = html;
        }
        
        function clearResults() {
            document.getElementById('resultsSection').style.display = 'none';
            currentResults = null;
            showNotification('结果已清除', 'success');
        }
        
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `status ${type}`;
            notification.innerHTML = `<i class="fas fa-${getIconForType(type)}"></i> ${message}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                min-width: 300px;
                animation: slideIn 0.3s ease-out;
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }
        
        function getIconForType(type) {
            const icons = {
                success: 'check-circle',
                error: 'exclamation-circle',
                warning: 'exclamation-triangle'
            };
            return icons[type] || 'info-circle';
        }
    </script>
    
    <style>
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    </style>
</body>
</html>
