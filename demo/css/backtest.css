/**
 * 回测系统专用样式
 */

/* 回测管理器样式 */
.backtest-manager {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

/* 配置区域样式 */
.config-section {
    border-bottom: 1px solid #374151;
    padding-bottom: 1.5rem;
    margin-bottom: 1.5rem;
}

.config-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

/* 表单样式增强 */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #D1D5DB;
    margin-bottom: 0.5rem;
}

.form-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    background-color: #374151;
    border: 1px solid #4B5563;
    border-radius: 0.375rem;
    color: #F9FAFB;
    font-size: 0.875rem;
    transition: all 0.2s;
}

.form-input:focus {
    outline: none;
    border-color: #3B82F6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.form-help {
    font-size: 0.75rem;
    color: #9CA3AF;
    margin-top: 0.25rem;
}

.form-checkbox {
    width: 1rem;
    height: 1rem;
    background-color: #374151;
    border: 1px solid #4B5563;
    border-radius: 0.25rem;
    color: #3B82F6;
}

/* 按钮样式增强 */
.btn-primary {
    background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    border: none;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2563EB 0%, #1E40AF 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-primary:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-secondary {
    background-color: #374151;
    color: #D1D5DB;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    border: 1px solid #4B5563;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
}

.btn-secondary:hover {
    background-color: #4B5563;
    border-color: #6B7280;
    transform: translateY(-1px);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* 进度条样式 */
.progress-container {
    display: flex;
    align-items: center;
}

.progress-bar {
    width: 200px;
    height: 8px;
    background-color: #374151;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #10B981 0%, #059669 100%);
    transition: width 0.3s ease;
    border-radius: 4px;
}

/* 卡片样式增强 */
.card {
    background: rgba(31, 41, 55, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid #374151;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.2s;
}

.card:hover {
    border-color: #4B5563;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* 指标卡片样式 */
.metric-card {
    text-align: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 0.75rem;
    transition: all 0.3s;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
}

.metric-value {
    font-size: 1.5rem;
    font-weight: bold;
    font-family: 'Monaco', 'Menlo', monospace;
    margin: 0.5rem 0;
}

.metric-label {
    font-size: 0.75rem;
    color: #9CA3AF;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* 表格样式 */
.trades-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.trades-table th {
    background-color: #374151;
    color: #D1D5DB;
    font-weight: 600;
    padding: 0.75rem;
    text-align: left;
    border-bottom: 2px solid #4B5563;
}

.trades-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #374151;
    color: #F9FAFB;
}

.trades-table tr:hover {
    background-color: rgba(59, 130, 246, 0.05);
}

/* 状态指示器 */
.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10B981;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-error {
    background-color: rgba(239, 68, 68, 0.1);
    color: #EF4444;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: #F59E0B;
    border: 1px solid rgba(245, 158, 11, 0.2);
}

/* 信号标记样式 */
.signal-marker {
    position: relative;
    display: inline-block;
}

.signal-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(17, 24, 39, 0.95);
    color: #F9FAFB;
    padding: 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 1000;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s;
}

.signal-marker:hover .signal-tooltip {
    opacity: 1;
}

/* 图表容器样式 */
.chart-container {
    background: rgba(31, 41, 55, 0.5);
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: #6B7280;
}

.empty-state-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 1rem;
    right: 1rem;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    color: white;
    font-weight: 500;
    z-index: 10000;
    max-width: 300px;
    word-wrap: break-word;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .backtest-manager {
        padding: 1rem;
    }
    
    .grid {
        grid-template-columns: 1fr;
    }
    
    .btn-primary,
    .btn-secondary {
        width: 100%;
        justify-content: center;
        margin-bottom: 0.5rem;
    }
    
    .trades-table {
        font-size: 0.75rem;
    }
    
    .trades-table th,
    .trades-table td {
        padding: 0.5rem;
    }
}

/* 深色主题优化 */
@media (prefers-color-scheme: dark) {
    .backtest-manager {
        background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
    }
    
    .card {
        background: rgba(17, 24, 39, 0.9);
        border-color: #374151;
    }
    
    .form-input {
        background-color: #1F2937;
        border-color: #374151;
    }
    
    .form-input:focus {
        border-color: #3B82F6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* 加载状态 */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 高亮效果 */
.glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { box-shadow: 0 0 20px rgba(59, 130, 246, 0.5); }
    to { box-shadow: 0 0 30px rgba(59, 130, 246, 0.8); }
}
