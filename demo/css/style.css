/**
 * 量化交易平台主样式文件
 * 提供基础样式和通用组件样式
 */

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 基础样式 */
html {
    font-size: 16px;
    line-height: 1.6;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    color: #F9FAFB;
    min-height: 100vh;
    overflow-x: hidden;
}

/* 容器样式 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.container-fluid {
    width: 100%;
    padding: 0 1rem;
}

/* 网格系统 */
.grid {
    display: grid;
    gap: 1rem;
}

.grid-1 { grid-template-columns: 1fr; }
.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }
.grid-5 { grid-template-columns: repeat(5, 1fr); }
.grid-6 { grid-template-columns: repeat(6, 1fr); }

/* Flexbox 工具类 */
.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.flex-wrap {
    flex-wrap: wrap;
}

.items-center {
    align-items: center;
}

.items-start {
    align-items: flex-start;
}

.items-end {
    align-items: flex-end;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.justify-around {
    justify-content: space-around;
}

.justify-end {
    justify-content: flex-end;
}

/* 间距工具类 */
.space-x-1 > * + * { margin-left: 0.25rem; }
.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-3 > * + * { margin-left: 0.75rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.space-x-8 > * + * { margin-left: 2rem; }

.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-y-8 > * + * { margin-top: 2rem; }

/* 内边距 */
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-5 { padding: 1.25rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }

.px-1 { padding-left: 0.25rem; padding-right: 0.25rem; }
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }

.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }

/* 外边距 */
.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.m-3 { margin: 0.75rem; }
.m-4 { margin: 1rem; }
.m-6 { margin: 1.5rem; }
.m-8 { margin: 2rem; }

.mx-auto { margin-left: auto; margin-right: auto; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }

.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 0.75rem; }
.mr-4 { margin-right: 1rem; }

.ml-1 { margin-left: 0.25rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 0.75rem; }
.ml-4 { margin-left: 1rem; }
.ml-auto { margin-left: auto; }

/* 文本样式 */
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.text-white { color: #ffffff; }
.text-gray-100 { color: #F3F4F6; }
.text-gray-200 { color: #E5E7EB; }
.text-gray-300 { color: #D1D5DB; }
.text-gray-400 { color: #9CA3AF; }
.text-gray-500 { color: #6B7280; }
.text-gray-600 { color: #4B5563; }
.text-gray-700 { color: #374151; }
.text-gray-800 { color: #1F2937; }
.text-gray-900 { color: #111827; }

.text-blue-400 { color: #60A5FA; }
.text-blue-500 { color: #3B82F6; }
.text-green-400 { color: #34D399; }
.text-green-500 { color: #10B981; }
.text-red-400 { color: #F87171; }
.text-red-500 { color: #EF4444; }
.text-yellow-400 { color: #FBBF24; }
.text-yellow-500 { color: #F59E0B; }
.text-purple-400 { color: #A78BFA; }
.text-purple-500 { color: #8B5CF6; }
.text-indigo-400 { color: #818CF8; }
.text-indigo-500 { color: #6366F1; }

/* 背景颜色 */
.bg-transparent { background-color: transparent; }
.bg-white { background-color: #ffffff; }
.bg-gray-50 { background-color: #F9FAFB; }
.bg-gray-100 { background-color: #F3F4F6; }
.bg-gray-200 { background-color: #E5E7EB; }
.bg-gray-300 { background-color: #D1D5DB; }
.bg-gray-400 { background-color: #9CA3AF; }
.bg-gray-500 { background-color: #6B7280; }
.bg-gray-600 { background-color: #4B5563; }
.bg-gray-700 { background-color: #374151; }
.bg-gray-800 { background-color: #1F2937; }
.bg-gray-900 { background-color: #111827; }

.bg-blue-500 { background-color: #3B82F6; }
.bg-blue-600 { background-color: #2563EB; }
.bg-green-500 { background-color: #10B981; }
.bg-green-600 { background-color: #059669; }
.bg-red-500 { background-color: #EF4444; }
.bg-red-600 { background-color: #DC2626; }
.bg-yellow-500 { background-color: #F59E0B; }
.bg-purple-500 { background-color: #8B5CF6; }
.bg-purple-600 { background-color: #7C3AED; }

/* 边框 */
.border { border-width: 1px; }
.border-0 { border-width: 0; }
.border-2 { border-width: 2px; }

.border-gray-200 { border-color: #E5E7EB; }
.border-gray-300 { border-color: #D1D5DB; }
.border-gray-400 { border-color: #9CA3AF; }
.border-gray-500 { border-color: #6B7280; }
.border-gray-600 { border-color: #4B5563; }
.border-gray-700 { border-color: #374151; }

.border-blue-500 { border-color: #3B82F6; }
.border-green-500 { border-color: #10B981; }
.border-red-500 { border-color: #EF4444; }

.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }
.rounded-full { border-radius: 9999px; }

/* 阴影 */
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }

/* 宽度和高度 */
.w-full { width: 100%; }
.w-auto { width: auto; }
.w-1\/2 { width: 50%; }
.w-1\/3 { width: 33.333333%; }
.w-2\/3 { width: 66.666667%; }
.w-1\/4 { width: 25%; }
.w-3\/4 { width: 75%; }

.h-full { height: 100%; }
.h-auto { height: auto; }
.h-screen { height: 100vh; }

.min-h-screen { min-height: 100vh; }

/* 显示和隐藏 */
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.hidden { display: none; }

/* 位置 */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

.top-0 { top: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }

/* z-index */
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

/* 透明度 */
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* 过渡动画 */
.transition { transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform; }
.transition-all { transition-property: all; }
.transition-colors { transition-property: background-color, border-color, color, fill, stroke; }
.transition-opacity { transition-property: opacity; }
.transition-transform { transition-property: transform; }

.duration-75 { transition-duration: 75ms; }
.duration-100 { transition-duration: 100ms; }
.duration-150 { transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }
.duration-500 { transition-duration: 500ms; }

.ease-in { transition-timing-function: cubic-bezier(0.4, 0, 1, 1); }
.ease-out { transition-timing-function: cubic-bezier(0, 0, 0.2, 1); }
.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }

/* 变换 */
.transform { transform: translateVar(--tw-translate-x, --tw-translate-y) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)); }
.scale-95 { transform: scale(0.95); }
.scale-100 { transform: scale(1); }
.scale-105 { transform: scale(1.05); }

/* 光标 */
.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }
.cursor-default { cursor: default; }

/* 选择 */
.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }

/* 溢出 */
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-scroll { overflow: scroll; }
.overflow-auto { overflow: auto; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }

/* 响应式设计 */
@media (max-width: 640px) {
    .grid-2, .grid-3, .grid-4, .grid-5, .grid-6 {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .md\:grid-2 { grid-template-columns: repeat(2, 1fr); }
    .md\:grid-3 { grid-template-columns: repeat(3, 1fr); }
    .md\:hidden { display: none; }
    .md\:block { display: block; }
}

@media (max-width: 1024px) {
    .lg\:grid-2 { grid-template-columns: repeat(2, 1fr); }
    .lg\:grid-3 { grid-template-columns: repeat(3, 1fr); }
    .lg\:grid-4 { grid-template-columns: repeat(4, 1fr); }
}

@media (min-width: 1280px) {
    .xl\:grid-4 { grid-template-columns: repeat(4, 1fr); }
    .xl\:col-span-1 { grid-column: span 1 / span 1; }
    .xl\:col-span-3 { grid-column: span 3 / span 3; }
}
