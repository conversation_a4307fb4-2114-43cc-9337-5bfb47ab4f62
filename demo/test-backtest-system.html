<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>回测系统测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a2e;
            color: #fff;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #2a2a3e;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #10b981;
            color: white;
        }
        .error {
            background: #ef4444;
            color: white;
        }
        .warning {
            background: #f59e0b;
            color: white;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2563eb;
        }
    </style>
</head>
<body>
    <h1>高级回测系统测试</h1>
    
    <div class="test-section">
        <h2>组件加载测试</h2>
        <button onclick="testComponentLoading()">测试组件加载</button>
        <div id="componentTest"></div>
    </div>
    
    <div class="test-section">
        <h2>策略引擎测试</h2>
        <button onclick="testStrategyEngine()">测试策略引擎</button>
        <div id="strategyTest"></div>
    </div>
    
    <div class="test-section">
        <h2>回测引擎测试</h2>
        <button onclick="testBacktestEngine()">测试回测引擎</button>
        <div id="backtestTest"></div>
    </div>
    
    <div class="test-section">
        <h2>数据生成测试</h2>
        <button onclick="testDataGeneration()">测试数据生成</button>
        <div id="dataTest"></div>
    </div>
    
    <div class="test-section">
        <h2>完整流程测试</h2>
        <button onclick="testFullWorkflow()">测试完整流程</button>
        <div id="workflowTest"></div>
    </div>

    <!-- 加载所有必要的脚本 -->
    <script src="js/strategy/strategy-engine.js"></script>
    <script src="js/strategy/advanced-backtest-engine.js"></script>
    <script src="js/strategy/backtest-visualizer.js"></script>
    <script src="js/strategy/backtest-config.js"></script>
    <script src="js/strategy/backtest-report.js"></script>
    <script src="js/strategy/backtest-manager.js"></script>

    <script>
        function showResult(containerId, message, type = 'success') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        function testComponentLoading() {
            clearResults('componentTest');
            
            const components = [
                'StrategyEngine',
                'AdvancedBacktestEngine',
                'BacktestVisualizer',
                'BacktestConfig',
                'BacktestReport',
                'BacktestManager'
            ];
            
            components.forEach(component => {
                if (window[component]) {
                    showResult('componentTest', `✓ ${component} 加载成功`, 'success');
                } else {
                    showResult('componentTest', `✗ ${component} 加载失败`, 'error');
                }
            });
        }

        function testStrategyEngine() {
            clearResults('strategyTest');
            
            try {
                const engine = new StrategyEngine();
                showResult('strategyTest', '✓ 策略引擎创建成功', 'success');
                
                // 测试配置
                if (engine.config && engine.config.takeoff && engine.config.strong) {
                    showResult('strategyTest', '✓ 策略配置正确', 'success');
                } else {
                    showResult('strategyTest', '✗ 策略配置错误', 'error');
                }
                
            } catch (error) {
                showResult('strategyTest', `✗ 策略引擎测试失败: ${error.message}`, 'error');
            }
        }

        function testBacktestEngine() {
            clearResults('backtestTest');
            
            try {
                const strategyEngine = new StrategyEngine();
                const backtestEngine = new AdvancedBacktestEngine(strategyEngine);
                showResult('backtestTest', '✓ 回测引擎创建成功', 'success');
                
                // 测试配置
                if (backtestEngine.config && backtestEngine.config.initialCapital) {
                    showResult('backtestTest', '✓ 回测配置正确', 'success');
                } else {
                    showResult('backtestTest', '✗ 回测配置错误', 'error');
                }
                
                // 测试方法
                if (typeof backtestEngine.runBacktest === 'function') {
                    showResult('backtestTest', '✓ runBacktest 方法存在', 'success');
                } else {
                    showResult('backtestTest', '✗ runBacktest 方法不存在', 'error');
                }
                
            } catch (error) {
                showResult('backtestTest', `✗ 回测引擎测试失败: ${error.message}`, 'error');
            }
        }

        function testDataGeneration() {
            clearResults('dataTest');
            
            try {
                const strategyEngine = new StrategyEngine();
                const backtestEngine = new AdvancedBacktestEngine(strategyEngine);
                
                // 生成测试数据
                const stockData = backtestEngine.generateMockStockData('000001.SZ', '2024-01-01', '2024-08-13');
                
                if (stockData && stockData.dates && stockData.closes) {
                    showResult('dataTest', `✓ 数据生成成功，共 ${stockData.dates.length} 条记录`, 'success');
                    
                    if (stockData.dates.length > 0) {
                        showResult('dataTest', `✓ 日期范围: ${stockData.dates[0]} 到 ${stockData.dates[stockData.dates.length - 1]}`, 'success');
                    }
                    
                    if (stockData.closes.every(price => price > 0)) {
                        showResult('dataTest', '✓ 价格数据有效', 'success');
                    } else {
                        showResult('dataTest', '✗ 价格数据无效', 'error');
                    }
                } else {
                    showResult('dataTest', '✗ 数据生成失败', 'error');
                }
                
            } catch (error) {
                showResult('dataTest', `✗ 数据生成测试失败: ${error.message}`, 'error');
            }
        }

        function testFullWorkflow() {
            clearResults('workflowTest');
            
            try {
                showResult('workflowTest', '开始完整流程测试...', 'warning');
                
                // 1. 创建引擎
                const strategyEngine = new StrategyEngine();
                const backtestEngine = new AdvancedBacktestEngine(strategyEngine);
                showResult('workflowTest', '✓ 步骤1: 引擎创建成功', 'success');
                
                // 2. 生成数据
                const stockData = backtestEngine.generateMockStockData('000001.SZ', '2024-01-01', '2024-08-13');
                showResult('workflowTest', `✓ 步骤2: 数据生成成功 (${stockData.dates.length} 条)`, 'success');
                
                // 3. 运行回测
                const results = backtestEngine.runBacktest(stockData);
                showResult('workflowTest', '✓ 步骤3: 回测执行成功', 'success');
                
                // 4. 验证结果
                if (results.statistics && results.trades && results.signals) {
                    showResult('workflowTest', '✓ 步骤4: 结果结构正确', 'success');
                    showResult('workflowTest', `✓ 信号数量: ${results.signals.length}`, 'success');
                    showResult('workflowTest', `✓ 交易数量: ${results.trades.length}`, 'success');
                    showResult('workflowTest', `✓ 总收益率: ${results.statistics.totalReturn}%`, 'success');
                    showResult('workflowTest', `✓ 胜率: ${results.statistics.winRate}%`, 'success');
                } else {
                    showResult('workflowTest', '✗ 步骤4: 结果结构错误', 'error');
                }
                
                showResult('workflowTest', '🎉 完整流程测试成功！', 'success');
                
            } catch (error) {
                showResult('workflowTest', `✗ 完整流程测试失败: ${error.message}`, 'error');
                console.error('完整流程测试错误:', error);
            }
        }

        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', function() {
            setTimeout(() => {
                testComponentLoading();
            }, 1000);
        });
    </script>
</body>
</html>
